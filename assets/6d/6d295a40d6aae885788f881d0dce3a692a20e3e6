{"accessibility.onboarding.accessibility.button": "協助工具設定...", "accessibility.onboarding.screen.narrator": "按下 Enter 以啟用朗讀功能", "accessibility.onboarding.screen.title": "歡迎遊玩 Minecraft！\n\n需要啟用朗讀功能或是調整協助工具設定嗎？", "addServer.add": "完成", "addServer.enterIp": "伺服器位址", "addServer.enterName": "伺服器名稱", "addServer.resourcePack": "伺服器資源包", "addServer.resourcePack.disabled": "已停用", "addServer.resourcePack.enabled": "已啟用", "addServer.resourcePack.prompt": "提示", "addServer.title": "編輯伺服器資訊", "advMode.command": "控制台指令", "advMode.mode": "模式", "advMode.mode.auto": "重複", "advMode.mode.autoexec.bat": "永遠啟動", "advMode.mode.conditional": "有條件", "advMode.mode.redstone": "脈衝", "advMode.mode.redstoneTriggered": "需要紅石", "advMode.mode.sequence": "連鎖", "advMode.mode.unconditional": "無條件", "advMode.notAllowed": "必須是創造模式中的管理員", "advMode.notEnabled": "這個伺服器不允許使用指令方塊", "advMode.previousOutput": "先前輸出", "advMode.setCommand": "設定指令方塊的指令", "advMode.setCommand.success": "指令設為：%s", "advMode.trackOutput": "追蹤資料輸出", "advMode.triggering": "觸發", "advMode.type": "類型", "advancement.advancementNotFound": "未知的進度：%s", "advancements.adventure.adventuring_time.description": "發現每個生態域", "advancements.adventure.adventuring_time.title": "探險時光", "advancements.adventure.arbalistic.description": "用弩一擊射殺五種生物", "advancements.adventure.arbalistic.title": "重弩手", "advancements.adventure.avoid_vibration.description": "在伏聆振測器或伏守者附近潛行以避免被偵測", "advancements.adventure.avoid_vibration.title": "潛行力 100", "advancements.adventure.blowback.description": "反彈旋風使者的風彈來擊殺它", "advancements.adventure.blowback.title": "逆風翻盤", "advancements.adventure.brush_armadillo.description": "使用刷子從犰狳身上取得犰狳鱗甲", "advancements.adventure.brush_armadillo.title": "莫非這是鱗甲？", "advancements.adventure.bullseye.description": "從至少30公尺外擊中標靶的靶心", "advancements.adventure.bullseye.title": "正中紅心", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "將四塊陶器碎片組成飾紋陶罐", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "精修細補", "advancements.adventure.crafters_crafting_crafters.description": "靠近一台正在合成合成器的合成器", "advancements.adventure.crafters_crafting_crafters.title": "合成器合成合成器", "advancements.adventure.fall_from_world_height.description": "從世界的最高處（建築高度限制）落至底部並存活", "advancements.adventure.fall_from_world_height.title": "洞穴與山崖", "advancements.adventure.heart_transplanter.description": "將嘎枝之心以正確方向放置在兩個蒼白橡木原木之間", "advancements.adventure.heart_transplanter.title": "移心接木", "advancements.adventure.hero_of_the_village.description": "成功在突襲中守住村莊", "advancements.adventure.hero_of_the_village.title": "村莊英雄", "advancements.adventure.honey_block_slide.description": "跳向蜂蜜塊安全滑落地面", "advancements.adventure.honey_block_slide.title": "陷入膠著", "advancements.adventure.kill_a_mob.description": "殺死任何敵對怪物", "advancements.adventure.kill_a_mob.title": "魔物獵人", "advancements.adventure.kill_all_mobs.description": "殺死每種敵對怪物各一隻", "advancements.adventure.kill_all_mobs.title": "獵取怪物", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "在伏聆觸媒旁殺死生物", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "它蔓延了", "advancements.adventure.lighten_up.description": "用斧頭為銅燈除鏽使其更加明亮", "advancements.adventure.lighten_up.title": "銅光煥發", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "保護村民不受雷擊的飛來橫禍，以免發生火災", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "突波保護器", "advancements.adventure.minecraft_trials_edition.description": "踏入試煉密室", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: 試煉版", "advancements.adventure.ol_betsy.description": "用弩發射箭矢", "advancements.adventure.ol_betsy.title": "扣下扳機", "advancements.adventure.overoverkill.description": "使用重錘一擊造成50顆心的傷害", "advancements.adventure.overoverkill.title": "天賜良擊", "advancements.adventure.play_jukebox_in_meadows.description": "用唱片機的音樂為草甸注入生命力", "advancements.adventure.play_jukebox_in_meadows.title": "真善美", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "使用紅石比較器讀取浮雕書櫃的紅石訊號", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "知書就是力量", "advancements.adventure.revaulting.description": "用不祥試煉鑰匙解鎖不祥寶庫", "advancements.adventure.revaulting.title": "逢凶化吉", "advancements.adventure.root.description": "冒險、探索和戰鬥", "advancements.adventure.root.title": "冒險", "advancements.adventure.salvage_sherd.description": "刷拭可疑的方塊以取得陶器碎片", "advancements.adventure.salvage_sherd.title": "探古尋源", "advancements.adventure.shoot_arrow.description": "使用箭矢射擊任何東西", "advancements.adventure.shoot_arrow.title": "瞄準", "advancements.adventure.sleep_in_bed.description": "在床上睡覺以變更您的重生點", "advancements.adventure.sleep_in_bed.title": "甜美的夢", "advancements.adventure.sniper_duel.description": "在距50公尺遠外的地方射殺一隻骷髏", "advancements.adventure.sniper_duel.title": "狙擊手對決", "advancements.adventure.spyglass_at_dragon.description": "使用望遠鏡觀察終界龍", "advancements.adventure.spyglass_at_dragon.title": "那是飛機嗎？", "advancements.adventure.spyglass_at_ghast.description": "使用望遠鏡觀察地獄幽靈", "advancements.adventure.spyglass_at_ghast.title": "那是氣球嗎？", "advancements.adventure.spyglass_at_parrot.description": "使用望遠鏡觀察鸚鵡", "advancements.adventure.spyglass_at_parrot.title": "那是鳥嗎？", "advancements.adventure.summon_iron_golem.description": "生成一隻鐵魔像以協助保衛村莊", "advancements.adventure.summon_iron_golem.title": "招兵買馬", "advancements.adventure.throw_trident.description": "將三叉戟擲向任何物品。\n注意：拋棄你僅有的武器並不是個好主意。", "advancements.adventure.throw_trident.title": "免洗笑話", "advancements.adventure.totem_of_undying.description": "使用不死圖騰來逃避死亡", "advancements.adventure.totem_of_undying.title": "超越生死", "advancements.adventure.trade.description": "成功與村民進行交易", "advancements.adventure.trade.title": "成交！", "advancements.adventure.trade_at_world_height.description": "在建築高度上限與村民交易", "advancements.adventure.trade_at_world_height.title": "星際貿易", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "將下列鍛造模板都至少使用一次：旋塔、豬鼻、肋骨、伏守、寂靜、惱鬼、潮汐、嚮導", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "匠心獨具", "advancements.adventure.trim_with_any_armor_pattern.description": "使用鍛造台合成帶有紋樣的盔甲", "advancements.adventure.trim_with_any_armor_pattern.title": "鍛然一新", "advancements.adventure.two_birds_one_arrow.description": "使用貫穿箭矢一次殺死兩隻夜魅", "advancements.adventure.two_birds_one_arrow.title": "一箭雙鵰", "advancements.adventure.under_lock_and_key.description": "用試煉鑰匙解鎖寶庫", "advancements.adventure.under_lock_and_key.title": "妥善保管", "advancements.adventure.use_lodestone.description": "對磁石使用羅盤", "advancements.adventure.use_lodestone.title": "天涯共此石", "advancements.adventure.very_very_frightening.description": "以閃電制裁村民", "advancements.adventure.very_very_frightening.title": "非常驚世駭俗", "advancements.adventure.voluntary_exile.description": "殺死突襲隊長。\n或許該考慮暫時離村莊遠一點...", "advancements.adventure.voluntary_exile.title": "自我放逐", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "在粉雪上行走... 並且不陷下去", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "輕功雪上飄", "advancements.adventure.who_needs_rockets.description": "使用風彈將自己向上彈射 8 格", "advancements.adventure.who_needs_rockets.title": "誰還需要火箭？", "advancements.adventure.whos_the_pillager_now.description": "讓掠奪者自食其果", "advancements.adventure.whos_the_pillager_now.title": "現在誰才是掠奪者？", "advancements.empty": "這裡似乎什麼都沒有...", "advancements.end.dragon_breath.description": "使用玻璃瓶取得龍之吐息", "advancements.end.dragon_breath.title": "你需要降火氣", "advancements.end.dragon_egg.description": "取得龍蛋", "advancements.end.dragon_egg.title": "銀河飛龍", "advancements.end.elytra.description": "找到鞘翅", "advancements.end.elytra.title": "天下無難事", "advancements.end.enter_end_gateway.description": "逃離這座島", "advancements.end.enter_end_gateway.title": "逃向遠方", "advancements.end.find_end_city.description": "進去吧，還能發生什麼事？", "advancements.end.find_end_city.title": "終末都市", "advancements.end.kill_dragon.description": "祝你好運", "advancements.end.kill_dragon.title": "解放終界", "advancements.end.levitate.description": "利用界伏蚌的攻擊向上飄浮50格", "advancements.end.levitate.title": "上面的風景真好", "advancements.end.respawn_dragon.description": "重生終界龍", "advancements.end.respawn_dragon.title": "終界... 再臨...", "advancements.end.root.description": "或是新的開始？", "advancements.end.root.title": "終界", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "讓悅靈朝音階盒投出一個蛋糕", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "生日快樂歌", "advancements.husbandry.allay_deliver_item_to_player.description": "讓悅靈遞送物品給你", "advancements.husbandry.allay_deliver_item_to_player.title": "我是你好朋友", "advancements.husbandry.axolotl_in_a_bucket.description": "用鐵桶捕捉一隻六角恐龍", "advancements.husbandry.axolotl_in_a_bucket.title": "最可愛的捕食者", "advancements.husbandry.balanced_diet.description": "吃遍所有可以食用的東西，即使它們對你的身體有害", "advancements.husbandry.balanced_diet.title": "均衡飲食", "advancements.husbandry.breed_all_animals.description": "繁殖所有種類的動物！", "advancements.husbandry.breed_all_animals.title": "成雙成對", "advancements.husbandry.breed_an_animal.description": "促使動物繁殖", "advancements.husbandry.breed_an_animal.title": "送子鳥的禮物", "advancements.husbandry.complete_catalogue.description": "馴服所有種類的貓！", "advancements.husbandry.complete_catalogue.title": "貓科全書", "advancements.husbandry.feed_snifflet.description": "餵食一隻幼年嗅探獸", "advancements.husbandry.feed_snifflet.title": "小小嗅探獸", "advancements.husbandry.fishy_business.description": "捕獲一條魚", "advancements.husbandry.fishy_business.title": "關漁生意", "advancements.husbandry.froglights.description": "在你的物品欄中集齊所有種類的蛙光體", "advancements.husbandry.froglights.title": "同心協力！", "advancements.husbandry.kill_axolotl_target.description": "和六角恐龍結盟並贏得一場戰鬥", "advancements.husbandry.kill_axolotl_target.title": "療癒力滿點的友情！", "advancements.husbandry.leash_all_frog_variants.description": "用拴繩牽著所有種類的青蛙", "advancements.husbandry.leash_all_frog_variants.title": "蛙軍壓境", "advancements.husbandry.make_a_sign_glow.description": "使任意種類告示牌上的文字發光", "advancements.husbandry.make_a_sign_glow.title": "光輝奪目！", "advancements.husbandry.netherite_hoe.description": "使用獄髓錠升級一把鋤頭，然後重新衡量你的人生抉擇", "advancements.husbandry.netherite_hoe.title": "敬業樂業", "advancements.husbandry.obtain_sniffer_egg.description": "取得嗅探獸蛋", "advancements.husbandry.obtain_sniffer_egg.title": "逸聞趣事", "advancements.husbandry.place_dried_ghast_in_water.description": "將乾癟幽靈放入水中", "advancements.husbandry.place_dried_ghast_in_water.title": "補水保濕！", "advancements.husbandry.plant_any_sniffer_seed.description": "種植任意嗅探獸種子", "advancements.husbandry.plant_any_sniffer_seed.title": "種種往事", "advancements.husbandry.plant_seed.description": "種下一個種子並見證它的成長", "advancements.husbandry.plant_seed.title": "汗滴禾下土", "advancements.husbandry.remove_wolf_armor.description": "使用剪刀移除狼身上的狼鎧", "advancements.husbandry.remove_wolf_armor.title": "剪潔俐落", "advancements.husbandry.repair_wolf_armor.description": "使用犰狳鱗甲完全修復耗損的狼鎧", "advancements.husbandry.repair_wolf_armor.title": "復舊如新", "advancements.husbandry.ride_a_boat_with_a_goat.description": "與山羊一起乘船航行", "advancements.husbandry.ride_a_boat_with_a_goat.title": "飄羊過海！", "advancements.husbandry.root.description": "這個世界充滿朋友與食物", "advancements.husbandry.root.title": "農牧", "advancements.husbandry.safely_harvest_honey.description": "使用營火以在不激怒蜜蜂的情況下用玻璃瓶從蜂窩中取得蜂蜜", "advancements.husbandry.safely_harvest_honey.title": "待客蜂範", "advancements.husbandry.silk_touch_nest.description": "使用絲綢之觸來移動裡面有3隻蜜蜂的蜂窩或蜂箱", "advancements.husbandry.silk_touch_nest.title": "蜂裝物流", "advancements.husbandry.tactical_fishing.description": "釣魚... 不用釣竿！", "advancements.husbandry.tactical_fishing.title": "戰術性捕魚", "advancements.husbandry.tadpole_in_a_bucket.description": "用鐵桶捕捉一隻蝌蚪", "advancements.husbandry.tadpole_in_a_bucket.title": "通通進桶", "advancements.husbandry.tame_an_animal.description": "馴服一隻動物", "advancements.husbandry.tame_an_animal.title": "永遠的好搭檔", "advancements.husbandry.wax_off.description": "刮除銅方塊上的蠟！", "advancements.husbandry.wax_off.title": "除蠟", "advancements.husbandry.wax_on.description": "為銅方塊塗上蜂蠟！", "advancements.husbandry.wax_on.title": "上蠟", "advancements.husbandry.whole_pack.description": "馴服所有種類的狼", "advancements.husbandry.whole_pack.title": "琳狼滿目", "advancements.nether.all_effects.description": "同時擁有所有狀態效果", "advancements.nether.all_effects.title": "我們是如何走到這地步的？", "advancements.nether.all_potions.description": "同時擁有所有藥水的效果", "advancements.nether.all_potions.title": "猛烈的雞尾酒", "advancements.nether.brew_potion.description": "釀造一瓶藥水", "advancements.nether.brew_potion.title": "道地的釀造坊", "advancements.nether.charge_respawn_anchor.description": "將重生錨充滿能量", "advancements.nether.charge_respawn_anchor.title": "非言「九」命", "advancements.nether.create_beacon.description": "建造及放置烽火台", "advancements.nether.create_beacon.title": "為家庭帶來光明", "advancements.nether.create_full_beacon.description": "令烽火台全力運作", "advancements.nether.create_full_beacon.title": "引導者", "advancements.nether.distract_piglin.description": "用黃金使豬布林分心", "advancements.nether.distract_piglin.title": "金光閃閃", "advancements.nether.explore_nether.description": "探索地獄所有的生態域", "advancements.nether.explore_nether.title": "熱門景點", "advancements.nether.fast_travel.description": "利用地獄在主世界旅行至七公里外", "advancements.nether.fast_travel.title": "子空間氣泡", "advancements.nether.find_bastion.description": "進入堡壘遺蹟", "advancements.nether.find_bastion.title": "今非昔比", "advancements.nether.find_fortress.description": "用自己的方式進入地獄要塞", "advancements.nether.find_fortress.title": "可怕的要塞", "advancements.nether.get_wither_skull.description": "取得凋零骷髏的頭顱", "advancements.nether.get_wither_skull.title": "詭異又恐怖的骷髏", "advancements.nether.loot_bastion.description": "掠奪一個堡壘遺蹟裡的儲物箱", "advancements.nether.loot_bastion.title": "戰豬", "advancements.nether.netherite_armor.description": "取得全套獄髓盔甲", "advancements.nether.netherite_armor.title": "以瓦礫為壁壘", "advancements.nether.obtain_ancient_debris.description": "取得遠古遺骸", "advancements.nether.obtain_ancient_debris.title": "深藏不露", "advancements.nether.obtain_blaze_rod.description": "讓烈焰使者從烈焰桿中解脫", "advancements.nether.obtain_blaze_rod.title": "與火共舞", "advancements.nether.obtain_crying_obsidian.description": "取得哭泣的黑曜石", "advancements.nether.obtain_crying_obsidian.title": "是誰在切洋蔥？", "advancements.nether.return_to_sender.description": "使用火球殺死地獄幽靈", "advancements.nether.return_to_sender.title": "以牙還牙", "advancements.nether.ride_strider.description": "利用扭曲蕈菇釣竿騎乘熾足獸", "advancements.nether.ride_strider.title": "行舟", "advancements.nether.ride_strider_in_overworld_lava.description": "在主世界的熔岩湖上和熾足獸來一段長～～途旅行", "advancements.nether.ride_strider_in_overworld_lava.title": "溫暖如家", "advancements.nether.root.description": "攜帶夏季服飾", "advancements.nether.root.title": "地獄", "advancements.nether.summon_wither.description": "召喚凋零怪", "advancements.nether.summon_wither.title": "凋零山莊", "advancements.nether.uneasy_alliance.description": "將地獄幽靈從地獄安全的救回主世界 ... 然後讓它解脫", "advancements.nether.uneasy_alliance.title": "不安的同盟", "advancements.nether.use_lodestone.description": "對磁石使用羅盤", "advancements.nether.use_lodestone.title": "天涯共此石", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "弱化並治好一位殭屍村民", "advancements.story.cure_zombie_villager.title": "殭屍醫生", "advancements.story.deflect_arrow.description": "使用盾牌反彈投射物", "advancements.story.deflect_arrow.title": "抱歉，今天不行", "advancements.story.enchant_item.description": "使用附魔台附魔一件物品", "advancements.story.enchant_item.title": "附魔師", "advancements.story.enter_the_end.description": "進入終界傳送門", "advancements.story.enter_the_end.title": "結束了？", "advancements.story.enter_the_nether.description": "建造、點燃並進入地獄傳送門", "advancements.story.enter_the_nether.title": "我們必須更深入一點", "advancements.story.follow_ender_eye.description": "跟隨終界之眼", "advancements.story.follow_ender_eye.title": "隔牆有眼", "advancements.story.form_obsidian.description": "取得一塊黑曜石", "advancements.story.form_obsidian.title": "冰桶挑戰", "advancements.story.iron_tools.description": "升級你的鎬", "advancements.story.iron_tools.title": "莫非這是鐵鎬", "advancements.story.lava_bucket.description": "把熔岩裝進鐵桶", "advancements.story.lava_bucket.title": "火熱的東西", "advancements.story.mine_diamond.description": "獲得鑽石", "advancements.story.mine_diamond.title": "鑽石！", "advancements.story.mine_stone.description": "用你的新鎬子挖掘石頭", "advancements.story.mine_stone.title": "石器時代", "advancements.story.obtain_armor.description": "使用鐵製盔甲保護自己", "advancements.story.obtain_armor.title": "整裝待發", "advancements.story.root.description": "遊戲的核心與故事", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "鑽石盔甲救人一命", "advancements.story.shiny_gear.title": "用鑽石包覆我", "advancements.story.smelt_iron.description": "冶煉出鐵錠", "advancements.story.smelt_iron.title": "來硬的", "advancements.story.upgrade_tools.description": "製作一把更好的鎬", "advancements.story.upgrade_tools.title": "獲取升級", "advancements.toast.challenge": "挑戰達成！", "advancements.toast.goal": "目標達成！", "advancements.toast.task": "進度達成！", "argument.anchor.invalid": "無效的實體錨點 %s", "argument.angle.incomplete": "不完整（應有 1 個角度）", "argument.angle.invalid": "無效的角度", "argument.block.id.invalid": "未知的方塊類型 '%s'", "argument.block.property.duplicate": "%2$s 方塊的屬性 '%1$s' 只能設定一次", "argument.block.property.invalid": "%s 方塊不接受以 '%s' 作為 %s 的屬性", "argument.block.property.novalue": "%2$s 方塊的屬性 '%1$s' 應為值", "argument.block.property.unclosed": "方塊狀態屬性後應以 ] 結尾", "argument.block.property.unknown": "%s 方塊沒有屬性 '%s'", "argument.block.tag.disallowed": "這裡無法使用標籤，只能用於實際方塊", "argument.color.invalid": "未知的顏色 '%s'", "argument.component.invalid": "無效的聊天元件：%s", "argument.criteria.invalid": "未知的條件 '%s'", "argument.dimension.invalid": "未知的維度 '%s'", "argument.double.big": "倍精度浮點數不能多於 %s，但找到的是 %s", "argument.double.low": "倍精度浮點數不能少於 %s，但找到的是 %s", "argument.entity.invalid": "無效的名稱或 UUID", "argument.entity.notfound.entity": "未找到任何實體", "argument.entity.notfound.player": "未找到任何玩家", "argument.entity.options.advancements.description": "玩家擁有進度", "argument.entity.options.distance.description": "與實體間的距離", "argument.entity.options.distance.negative": "距離不能為負數", "argument.entity.options.dx.description": "位於 x 和 x + dx 之間的實體", "argument.entity.options.dy.description": "位於 y 和 y + dy 之間的實體", "argument.entity.options.dz.description": "位於 z 和 z + dz 之間的實體", "argument.entity.options.gamemode.description": "玩家於遊戲模式", "argument.entity.options.inapplicable": "此處不適用 '%s' 選項", "argument.entity.options.level.description": "經驗等級", "argument.entity.options.level.negative": "等級不應為負數", "argument.entity.options.limit.description": "最大回傳實體數", "argument.entity.options.limit.toosmall": "限制必須至少為 1", "argument.entity.options.mode.invalid": "無效或未知的遊戲模式 '%s'", "argument.entity.options.name.description": "實體名稱", "argument.entity.options.nbt.description": "實體擁有 NBT", "argument.entity.options.predicate.description": "自訂述詞", "argument.entity.options.scores.description": "實體擁有分數", "argument.entity.options.sort.description": "對實體進行排序", "argument.entity.options.sort.irreversible": "無效或未知的排序類型 '%s'", "argument.entity.options.tag.description": "實體擁有標籤", "argument.entity.options.team.description": "實體於隊伍", "argument.entity.options.type.description": "實體類型", "argument.entity.options.type.invalid": "無效或未知的實體類型 '%s'", "argument.entity.options.unknown": "未知的選項 '%s'", "argument.entity.options.unterminated": "選項應有結尾", "argument.entity.options.valueless": "選項 '%s' 應為數值", "argument.entity.options.x.description": "x 座標", "argument.entity.options.x_rotation.description": "實體的 x 軸方向", "argument.entity.options.y.description": "y 座標", "argument.entity.options.y_rotation.description": "實體的 y 軸方向", "argument.entity.options.z.description": "z 座標", "argument.entity.selector.allEntities": "所有實體", "argument.entity.selector.allPlayers": "所有玩家", "argument.entity.selector.missing": "缺少選擇器類型", "argument.entity.selector.nearestEntity": "最接近的實體", "argument.entity.selector.nearestPlayer": "最接近的玩家", "argument.entity.selector.not_allowed": "選擇器無法使用", "argument.entity.selector.randomPlayer": "隨機玩家", "argument.entity.selector.self": "目前實體", "argument.entity.selector.unknown": "未知的選擇器類型 '%s'", "argument.entity.toomany": "僅接受單一實體，但提供的選擇器允許不只一個實體", "argument.enum.invalid": "無效數值「%s」", "argument.float.big": "單精度浮點數不能多於 %s，但找到的是 %s", "argument.float.low": "單精度浮點數不能少於 %s，但找到的是 %s", "argument.gamemode.invalid": "未知的遊戲模式：%s", "argument.hexcolor.invalid": "無效的十六進位顏色碼 '%s'", "argument.id.invalid": "無效的 ID", "argument.id.unknown": "未知的 ID: %s", "argument.integer.big": "整數不能多於 %s，但找到的是 %s", "argument.integer.low": "整數不能少於 %s，但找到的是 %s", "argument.item.id.invalid": "未知的物品 '%s'", "argument.item.tag.disallowed": "這裡無法使用標籤，只能用於實際物品", "argument.literal.incorrect": "應為字面值 %s", "argument.long.big": "長整數不能多於 %s，但找到的是 %s", "argument.long.low": "長整數不能少於 %s，但找到的是 %s", "argument.message.too_long": "聊天訊息過長（%s 大於最大值 %s 個字元）", "argument.nbt.array.invalid": "無效的陣列類型 '%s'", "argument.nbt.array.mixed": "無法將 %s 插入至 %s", "argument.nbt.expected.compound": "應為複合標籤", "argument.nbt.expected.key": "應為鍵值", "argument.nbt.expected.value": "應為數值", "argument.nbt.list.mixed": "無法將 %s 插入至清單 %s", "argument.nbt.trailing": "多餘的尾隨資料", "argument.player.entities": "只有玩家能受這條指令影響，但提供的選擇器包含了其他實體", "argument.player.toomany": "僅接受單一玩家，但提供的選擇器允許不只一個玩家", "argument.player.unknown": "該玩家不存在", "argument.pos.missing.double": "應為座標", "argument.pos.missing.int": "應為方塊座標", "argument.pos.mixed": "不能混用世界與局部座標（必須全部都用 ^ 或是完全不用）", "argument.pos.outofbounds": "該位置位於允許的範圍外。", "argument.pos.outofworld": "該座標位於世界外！", "argument.pos.unloaded": "該座標尚未載入", "argument.pos2d.incomplete": "不完整（應有 2 個座標）", "argument.pos3d.incomplete": "不完整（應有 3 個座標）", "argument.range.empty": "應為數值或數值的範圍", "argument.range.ints": "只允許整數，不能為小數", "argument.range.swapped": "最小值不能大於最大值", "argument.resource.invalid_type": "元素 '%s' 擁有錯誤類型 '%s' （應為 '%s' ）", "argument.resource.not_found": "無法找到類型為 '%2$s' 的元素 '%1$s'", "argument.resource_or_id.failed_to_parse": "無法剖析結構：%s", "argument.resource_or_id.invalid": "無效的 ID 或標籤", "argument.resource_or_id.no_such_element": "無法在登錄 '%2$s' 中找到元素 '%1$s'", "argument.resource_selector.not_found": "選擇器 '%s' 與類型 '%s' 沒有相符的項目", "argument.resource_tag.invalid_type": "標籤 '%s' 擁有錯誤類型 '%s' （應為 '%s' ）", "argument.resource_tag.not_found": "無法找到類型為 '%2$s' 的標籤 '%1$s'", "argument.rotation.incomplete": "不完整（應有 2 個座標）", "argument.scoreHolder.empty": "無法找到該分數相對應的持有者", "argument.scoreboardDisplaySlot.invalid": "未知的顯示區 '%s'", "argument.style.invalid": "無效的樣式：%s", "argument.time.invalid_tick_count": "刻數不可為負數", "argument.time.invalid_unit": "無效的單位", "argument.time.tick_count_too_low": "刻數不能少於 %s，但找到的是 %s", "argument.uuid.invalid": "無效的 UUID", "argument.waypoint.invalid": "選擇的實體不是路徑點", "arguments.block.tag.unknown": "未知的方塊標籤 '%s'", "arguments.function.tag.unknown": "未知的函數標籤 '%s'", "arguments.function.unknown": "未知的函數 %s", "arguments.item.component.expected": "應為物品元件", "arguments.item.component.malformed": "錯誤的 '%s' 元件：'%s'", "arguments.item.component.repeated": "物品元件 '%s' 重複，只能指定一個值", "arguments.item.component.unknown": "未知的物品元件 '%s'", "arguments.item.malformed": "錯誤的物品：'%s'", "arguments.item.overstacked": "%s 只能堆疊至 %s", "arguments.item.predicate.malformed": "錯誤的 '%s' 述詞：'%s'", "arguments.item.predicate.unknown": "未知的物品述詞 '%s'", "arguments.item.tag.unknown": "未知的物品標籤 '%s'", "arguments.nbtpath.node.invalid": "無效的 NBT 路徑元素", "arguments.nbtpath.nothing_found": "找不到與 %s 相符的元素", "arguments.nbtpath.too_deep": "產生的 NBT 巢套過深", "arguments.nbtpath.too_large": "產生的 NBT 過大", "arguments.objective.notFound": "未知的計分板目標 '%s'", "arguments.objective.readonly": "計分板目標 '%s' 是唯讀的", "arguments.operation.div0": "無法除以零", "arguments.operation.invalid": "無效的操作", "arguments.swizzle.invalid": "無效的搭配，應為 'x'、'y' 與 'z' 的組合", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "盔甲", "attribute.name.armor_toughness": "盔甲強度", "attribute.name.attack_damage": "攻擊傷害", "attribute.name.attack_knockback": "擊退", "attribute.name.attack_speed": "攻擊速度", "attribute.name.block_break_speed": "方塊破壞速度", "attribute.name.block_interaction_range": "方塊互動距離", "attribute.name.burning_time": "燃燒時間", "attribute.name.camera_distance": "鏡頭距離", "attribute.name.entity_interaction_range": "實體互動距離", "attribute.name.explosion_knockback_resistance": "抗爆炸擊退", "attribute.name.fall_damage_multiplier": "摔落傷害倍率", "attribute.name.flying_speed": "飛行速度", "attribute.name.follow_range": "生物追蹤範圍", "attribute.name.generic.armor": "盔甲", "attribute.name.generic.armor_toughness": "盔甲強度", "attribute.name.generic.attack_damage": "攻擊傷害", "attribute.name.generic.attack_knockback": "擊退", "attribute.name.generic.attack_speed": "攻擊速度", "attribute.name.generic.block_interaction_range": "方塊互動距離", "attribute.name.generic.burning_time": "燃燒時間", "attribute.name.generic.entity_interaction_range": "實體互動距離", "attribute.name.generic.explosion_knockback_resistance": "抗爆炸擊退", "attribute.name.generic.fall_damage_multiplier": "摔落傷害倍率", "attribute.name.generic.flying_speed": "飛行速度", "attribute.name.generic.follow_range": "生物追蹤範圍", "attribute.name.generic.gravity": "重力", "attribute.name.generic.jump_strength": "跳躍力", "attribute.name.generic.knockback_resistance": "抗擊退", "attribute.name.generic.luck": "幸運", "attribute.name.generic.max_absorption": "最大傷害吸收值", "attribute.name.generic.max_health": "最高生命值", "attribute.name.generic.movement_efficiency": "移動效率", "attribute.name.generic.movement_speed": "速度", "attribute.name.generic.oxygen_bonus": "額外氧氣", "attribute.name.generic.safe_fall_distance": "安全摔落高度", "attribute.name.generic.scale": "大小", "attribute.name.generic.step_height": "最大行走高度", "attribute.name.generic.water_movement_efficiency": "水中移動效率", "attribute.name.gravity": "重力", "attribute.name.horse.jump_strength": "馬跳躍力", "attribute.name.jump_strength": "跳躍力", "attribute.name.knockback_resistance": "抗擊退", "attribute.name.luck": "幸運", "attribute.name.max_absorption": "最大傷害吸收值", "attribute.name.max_health": "最高生命值", "attribute.name.mining_efficiency": "挖掘效率", "attribute.name.movement_efficiency": "移動效率", "attribute.name.movement_speed": "速度", "attribute.name.oxygen_bonus": "額外氧氣", "attribute.name.player.block_break_speed": "方塊破壞速度", "attribute.name.player.block_interaction_range": "方塊互動距離", "attribute.name.player.entity_interaction_range": "實體互動距離", "attribute.name.player.mining_efficiency": "挖掘效率", "attribute.name.player.sneaking_speed": "潛行速度", "attribute.name.player.submerged_mining_speed": "水中挖掘速度", "attribute.name.player.sweeping_damage_ratio": "橫掃傷害率", "attribute.name.safe_fall_distance": "安全摔落高度", "attribute.name.scale": "大小", "attribute.name.sneaking_speed": "潛行速度", "attribute.name.spawn_reinforcements": "殭屍增援隊", "attribute.name.step_height": "最大行走高度", "attribute.name.submerged_mining_speed": "水中挖掘速度", "attribute.name.sweeping_damage_ratio": "橫掃傷害率", "attribute.name.tempt_range": "生物引誘範圍", "attribute.name.water_movement_efficiency": "水中移動效率", "attribute.name.waypoint_receive_range": "路徑點接收距離", "attribute.name.waypoint_transmit_range": "路徑點傳輸距離", "attribute.name.zombie.spawn_reinforcements": "殭屍增援隊", "biome.minecraft.badlands": "惡地", "biome.minecraft.bamboo_jungle": "竹林", "biome.minecraft.basalt_deltas": "玄武岩三角洲", "biome.minecraft.beach": "沙灘", "biome.minecraft.birch_forest": "樺木森林", "biome.minecraft.cherry_grove": "櫻花樹林", "biome.minecraft.cold_ocean": "寒冷海洋", "biome.minecraft.crimson_forest": "緋紅森林", "biome.minecraft.dark_forest": "黑森林", "biome.minecraft.deep_cold_ocean": "寒冷深海", "biome.minecraft.deep_dark": "深淵", "biome.minecraft.deep_frozen_ocean": "寒凍深海", "biome.minecraft.deep_lukewarm_ocean": "溫和深海", "biome.minecraft.deep_ocean": "深海", "biome.minecraft.desert": "沙漠", "biome.minecraft.dripstone_caves": "鐘乳石洞窟", "biome.minecraft.end_barrens": "終界荒地", "biome.minecraft.end_highlands": "終界高地", "biome.minecraft.end_midlands": "終界平地", "biome.minecraft.eroded_badlands": "侵蝕惡地", "biome.minecraft.flower_forest": "繁花森林", "biome.minecraft.forest": "森林", "biome.minecraft.frozen_ocean": "寒凍海洋", "biome.minecraft.frozen_peaks": "霜凍山峰", "biome.minecraft.frozen_river": "寒凍河流", "biome.minecraft.grove": "雪林", "biome.minecraft.ice_spikes": "冰刺", "biome.minecraft.jagged_peaks": "尖峭山峰", "biome.minecraft.jungle": "叢林", "biome.minecraft.lukewarm_ocean": "溫和海洋", "biome.minecraft.lush_caves": "蒼鬱洞窟", "biome.minecraft.mangrove_swamp": "紅樹林沼澤", "biome.minecraft.meadow": "草甸", "biome.minecraft.mushroom_fields": "蘑菇地", "biome.minecraft.nether_wastes": "地獄荒原", "biome.minecraft.ocean": "海洋", "biome.minecraft.old_growth_birch_forest": "原生樺木森林", "biome.minecraft.old_growth_pine_taiga": "原生松木針葉林", "biome.minecraft.old_growth_spruce_taiga": "原生杉木針葉林", "biome.minecraft.pale_garden": "蒼白之園", "biome.minecraft.plains": "平原", "biome.minecraft.river": "河流", "biome.minecraft.savanna": "莽原", "biome.minecraft.savanna_plateau": "莽原高地", "biome.minecraft.small_end_islands": "終界小島", "biome.minecraft.snowy_beach": "冰雪沙灘", "biome.minecraft.snowy_plains": "雪原", "biome.minecraft.snowy_slopes": "雪坡", "biome.minecraft.snowy_taiga": "冰雪針葉林", "biome.minecraft.soul_sand_valley": "靈魂砂谷", "biome.minecraft.sparse_jungle": "稀疏叢林", "biome.minecraft.stony_peaks": "裸岩山峰", "biome.minecraft.stony_shore": "石岸", "biome.minecraft.sunflower_plains": "向日葵平原", "biome.minecraft.swamp": "沼澤", "biome.minecraft.taiga": "針葉林", "biome.minecraft.the_end": "終界", "biome.minecraft.the_void": "虛空", "biome.minecraft.warm_ocean": "溫暖海洋", "biome.minecraft.warped_forest": "扭曲森林", "biome.minecraft.windswept_forest": "風蝕森林", "biome.minecraft.windswept_gravelly_hills": "風蝕礫質丘陵", "biome.minecraft.windswept_hills": "風蝕丘陵", "biome.minecraft.windswept_savanna": "風蝕莽原", "biome.minecraft.wooded_badlands": "疏林惡地", "block.minecraft.acacia_button": "相思木按鈕", "block.minecraft.acacia_door": "相思木門", "block.minecraft.acacia_fence": "相思木柵欄", "block.minecraft.acacia_fence_gate": "相思木柵欄門", "block.minecraft.acacia_hanging_sign": "懸掛式相思木告示牌", "block.minecraft.acacia_leaves": "相思木樹葉", "block.minecraft.acacia_log": "相思木原木", "block.minecraft.acacia_planks": "相思木材", "block.minecraft.acacia_pressure_plate": "相思木壓力板", "block.minecraft.acacia_sapling": "相思木樹苗", "block.minecraft.acacia_sign": "相思木告示牌", "block.minecraft.acacia_slab": "相思木半磚", "block.minecraft.acacia_stairs": "相思木階梯", "block.minecraft.acacia_trapdoor": "相思木地板門", "block.minecraft.acacia_wall_hanging_sign": "牆上的懸掛式相思木告示牌", "block.minecraft.acacia_wall_sign": "牆上的相思木告示牌", "block.minecraft.acacia_wood": "相思木塊", "block.minecraft.activator_rail": "觸發鐵軌", "block.minecraft.air": "空氣", "block.minecraft.allium": "紫紅球花", "block.minecraft.amethyst_block": "紫水晶方塊", "block.minecraft.amethyst_cluster": "紫水晶晶簇", "block.minecraft.ancient_debris": "遠古遺骸", "block.minecraft.andesite": "安山岩", "block.minecraft.andesite_slab": "安山岩半磚", "block.minecraft.andesite_stairs": "安山岩階梯", "block.minecraft.andesite_wall": "安山岩牆", "block.minecraft.anvil": "鐵砧", "block.minecraft.attached_melon_stem": "連接的西瓜梗", "block.minecraft.attached_pumpkin_stem": "連接的南瓜梗", "block.minecraft.azalea": "杜鵑叢", "block.minecraft.azalea_leaves": "杜鵑葉", "block.minecraft.azure_bluet": "雛草", "block.minecraft.bamboo": "竹子", "block.minecraft.bamboo_block": "竹方塊", "block.minecraft.bamboo_button": "竹按鈕", "block.minecraft.bamboo_door": "竹門", "block.minecraft.bamboo_fence": "竹柵欄", "block.minecraft.bamboo_fence_gate": "竹柵欄門", "block.minecraft.bamboo_hanging_sign": "懸掛式竹告示牌", "block.minecraft.bamboo_mosaic": "竹拼塊", "block.minecraft.bamboo_mosaic_slab": "竹拼半磚", "block.minecraft.bamboo_mosaic_stairs": "竹拼階梯", "block.minecraft.bamboo_planks": "竹材", "block.minecraft.bamboo_pressure_plate": "竹壓力板", "block.minecraft.bamboo_sapling": "竹筍", "block.minecraft.bamboo_sign": "竹告示牌", "block.minecraft.bamboo_slab": "竹半磚", "block.minecraft.bamboo_stairs": "竹階梯", "block.minecraft.bamboo_trapdoor": "竹地板門", "block.minecraft.bamboo_wall_hanging_sign": "牆上的懸掛式竹告示牌", "block.minecraft.bamboo_wall_sign": "牆上的竹告示牌", "block.minecraft.banner.base.black": "黑色基底", "block.minecraft.banner.base.blue": "藍色基底", "block.minecraft.banner.base.brown": "棕色基底", "block.minecraft.banner.base.cyan": "青色基底", "block.minecraft.banner.base.gray": "灰色基底", "block.minecraft.banner.base.green": "綠色基底", "block.minecraft.banner.base.light_blue": "淺藍色基底", "block.minecraft.banner.base.light_gray": "淺灰色基底", "block.minecraft.banner.base.lime": "淺綠色基底", "block.minecraft.banner.base.magenta": "洋紅色基底", "block.minecraft.banner.base.orange": "橙色基底", "block.minecraft.banner.base.pink": "粉紅色基底", "block.minecraft.banner.base.purple": "紫色基底", "block.minecraft.banner.base.red": "紅色基底", "block.minecraft.banner.base.white": "白色基底", "block.minecraft.banner.base.yellow": "黃色基底", "block.minecraft.banner.border.black": "黑色框邊", "block.minecraft.banner.border.blue": "藍色框邊", "block.minecraft.banner.border.brown": "棕色框邊", "block.minecraft.banner.border.cyan": "青色框邊", "block.minecraft.banner.border.gray": "灰色框邊", "block.minecraft.banner.border.green": "綠色框邊", "block.minecraft.banner.border.light_blue": "淺藍色框邊", "block.minecraft.banner.border.light_gray": "淺灰色框邊", "block.minecraft.banner.border.lime": "淺綠色框邊", "block.minecraft.banner.border.magenta": "洋紅色框邊", "block.minecraft.banner.border.orange": "橙色框邊", "block.minecraft.banner.border.pink": "粉紅色框邊", "block.minecraft.banner.border.purple": "紫色框邊", "block.minecraft.banner.border.red": "紅色框邊", "block.minecraft.banner.border.white": "白色框邊", "block.minecraft.banner.border.yellow": "黃色框邊", "block.minecraft.banner.bricks.black": "黑色磚牆花紋", "block.minecraft.banner.bricks.blue": "藍色磚牆花紋", "block.minecraft.banner.bricks.brown": "棕色磚牆花紋", "block.minecraft.banner.bricks.cyan": "青色磚牆花紋", "block.minecraft.banner.bricks.gray": "灰色磚牆花紋", "block.minecraft.banner.bricks.green": "綠色磚牆花紋", "block.minecraft.banner.bricks.light_blue": "淺藍色磚牆花紋", "block.minecraft.banner.bricks.light_gray": "淺灰色磚牆花紋", "block.minecraft.banner.bricks.lime": "淺綠色磚牆花紋", "block.minecraft.banner.bricks.magenta": "洋紅色磚牆花紋", "block.minecraft.banner.bricks.orange": "橙色磚牆花紋", "block.minecraft.banner.bricks.pink": "粉紅色磚牆花紋", "block.minecraft.banner.bricks.purple": "紫色磚牆花紋", "block.minecraft.banner.bricks.red": "紅色磚牆花紋", "block.minecraft.banner.bricks.white": "白色磚牆花紋", "block.minecraft.banner.bricks.yellow": "黃色磚牆花紋", "block.minecraft.banner.circle.black": "黑色圓形", "block.minecraft.banner.circle.blue": "藍色圓形", "block.minecraft.banner.circle.brown": "棕色圓形", "block.minecraft.banner.circle.cyan": "青色圓形", "block.minecraft.banner.circle.gray": "灰色圓形", "block.minecraft.banner.circle.green": "綠色圓形", "block.minecraft.banner.circle.light_blue": "淺藍色圓形", "block.minecraft.banner.circle.light_gray": "淺灰色圓形", "block.minecraft.banner.circle.lime": "淺綠色圓形", "block.minecraft.banner.circle.magenta": "洋紅色圓形", "block.minecraft.banner.circle.orange": "橙色圓形", "block.minecraft.banner.circle.pink": "粉紅色圓形", "block.minecraft.banner.circle.purple": "紫色圓形", "block.minecraft.banner.circle.red": "紅色圓形", "block.minecraft.banner.circle.white": "白色圓形", "block.minecraft.banner.circle.yellow": "黃色圓形", "block.minecraft.banner.creeper.black": "黑色苦力怕圖紋", "block.minecraft.banner.creeper.blue": "藍色苦力怕圖紋", "block.minecraft.banner.creeper.brown": "棕色苦力怕圖紋", "block.minecraft.banner.creeper.cyan": "青色苦力怕圖紋", "block.minecraft.banner.creeper.gray": "灰色苦力怕圖紋", "block.minecraft.banner.creeper.green": "綠色苦力怕圖紋", "block.minecraft.banner.creeper.light_blue": "淺藍色苦力怕圖紋", "block.minecraft.banner.creeper.light_gray": "淺灰色苦力怕圖紋", "block.minecraft.banner.creeper.lime": "淺綠色苦力怕圖紋", "block.minecraft.banner.creeper.magenta": "洋紅色苦力怕圖紋", "block.minecraft.banner.creeper.orange": "橙色苦力怕圖紋", "block.minecraft.banner.creeper.pink": "粉紅色苦力怕圖紋", "block.minecraft.banner.creeper.purple": "紫色苦力怕圖紋", "block.minecraft.banner.creeper.red": "紅色苦力怕圖紋", "block.minecraft.banner.creeper.white": "白色苦力怕圖紋", "block.minecraft.banner.creeper.yellow": "黃色苦力怕圖紋", "block.minecraft.banner.cross.black": "黑色對角交叉", "block.minecraft.banner.cross.blue": "藍色對角交叉", "block.minecraft.banner.cross.brown": "棕色對角交叉", "block.minecraft.banner.cross.cyan": "青色對角交叉", "block.minecraft.banner.cross.gray": "灰色對角交叉", "block.minecraft.banner.cross.green": "綠色對角交叉", "block.minecraft.banner.cross.light_blue": "淺藍色對角交叉", "block.minecraft.banner.cross.light_gray": "淺灰色對角交叉", "block.minecraft.banner.cross.lime": "淺綠色對角交叉", "block.minecraft.banner.cross.magenta": "洋紅色對角交叉", "block.minecraft.banner.cross.orange": "橙色對角交叉", "block.minecraft.banner.cross.pink": "粉紅色對角交叉", "block.minecraft.banner.cross.purple": "紫色對角交叉", "block.minecraft.banner.cross.red": "紅色對角交叉", "block.minecraft.banner.cross.white": "白色對角交叉", "block.minecraft.banner.cross.yellow": "黃色對角交叉", "block.minecraft.banner.curly_border.black": "黑色鋸齒框邊", "block.minecraft.banner.curly_border.blue": "藍色鋸齒框邊", "block.minecraft.banner.curly_border.brown": "棕色鋸齒框邊", "block.minecraft.banner.curly_border.cyan": "青色鋸齒框邊", "block.minecraft.banner.curly_border.gray": "灰色鋸齒框邊", "block.minecraft.banner.curly_border.green": "綠色鋸齒框邊", "block.minecraft.banner.curly_border.light_blue": "淺藍色鋸齒框邊", "block.minecraft.banner.curly_border.light_gray": "淺灰色鋸齒框邊", "block.minecraft.banner.curly_border.lime": "淺綠色鋸齒框邊", "block.minecraft.banner.curly_border.magenta": "洋紅色鋸齒框邊", "block.minecraft.banner.curly_border.orange": "橙色鋸齒框邊", "block.minecraft.banner.curly_border.pink": "粉紅色鋸齒框邊", "block.minecraft.banner.curly_border.purple": "紫色鋸齒框邊", "block.minecraft.banner.curly_border.red": "紅色鋸齒框邊", "block.minecraft.banner.curly_border.white": "白色鋸齒框邊", "block.minecraft.banner.curly_border.yellow": "黃色鋸齒框邊", "block.minecraft.banner.diagonal_left.black": "左上方黑色填滿", "block.minecraft.banner.diagonal_left.blue": "左上方藍色填滿", "block.minecraft.banner.diagonal_left.brown": "左上方棕色填滿", "block.minecraft.banner.diagonal_left.cyan": "左上方青色填滿", "block.minecraft.banner.diagonal_left.gray": "左上方灰色填滿", "block.minecraft.banner.diagonal_left.green": "左上方綠色填滿", "block.minecraft.banner.diagonal_left.light_blue": "左上方淺藍色填滿", "block.minecraft.banner.diagonal_left.light_gray": "左上方淺灰色填滿", "block.minecraft.banner.diagonal_left.lime": "左上方淺綠色填滿", "block.minecraft.banner.diagonal_left.magenta": "左上方洋紅色填滿", "block.minecraft.banner.diagonal_left.orange": "左上方橙色填滿", "block.minecraft.banner.diagonal_left.pink": "左上方粉紅色填滿", "block.minecraft.banner.diagonal_left.purple": "左上方紫色填滿", "block.minecraft.banner.diagonal_left.red": "左上方紅色填滿", "block.minecraft.banner.diagonal_left.white": "左上方白色填滿", "block.minecraft.banner.diagonal_left.yellow": "左上方黃色填滿", "block.minecraft.banner.diagonal_right.black": "右上方黑色填滿", "block.minecraft.banner.diagonal_right.blue": "右上方藍色填滿", "block.minecraft.banner.diagonal_right.brown": "右上方棕色填滿", "block.minecraft.banner.diagonal_right.cyan": "右上方青色填滿", "block.minecraft.banner.diagonal_right.gray": "右上方灰色填滿", "block.minecraft.banner.diagonal_right.green": "右上方綠色填滿", "block.minecraft.banner.diagonal_right.light_blue": "右上方淺藍色填滿", "block.minecraft.banner.diagonal_right.light_gray": "右上方淺灰色填滿", "block.minecraft.banner.diagonal_right.lime": "右上方淺綠色填滿", "block.minecraft.banner.diagonal_right.magenta": "右上方洋紅色填滿", "block.minecraft.banner.diagonal_right.orange": "右上方橙色填滿", "block.minecraft.banner.diagonal_right.pink": "右上方粉紅色填滿", "block.minecraft.banner.diagonal_right.purple": "右上方紫色填滿", "block.minecraft.banner.diagonal_right.red": "右上方紅色填滿", "block.minecraft.banner.diagonal_right.white": "右上方白色填滿", "block.minecraft.banner.diagonal_right.yellow": "右上方黃色填滿", "block.minecraft.banner.diagonal_up_left.black": "左下方黑色填滿", "block.minecraft.banner.diagonal_up_left.blue": "左下方藍色填滿", "block.minecraft.banner.diagonal_up_left.brown": "左下方棕色填滿", "block.minecraft.banner.diagonal_up_left.cyan": "左下方青色填滿", "block.minecraft.banner.diagonal_up_left.gray": "左下方灰色填滿", "block.minecraft.banner.diagonal_up_left.green": "左下方綠色填滿", "block.minecraft.banner.diagonal_up_left.light_blue": "左下方淺藍色填滿", "block.minecraft.banner.diagonal_up_left.light_gray": "左下方淺灰色填滿", "block.minecraft.banner.diagonal_up_left.lime": "左下方淺綠色填滿", "block.minecraft.banner.diagonal_up_left.magenta": "左下方洋紅色填滿", "block.minecraft.banner.diagonal_up_left.orange": "左下方橙色填滿", "block.minecraft.banner.diagonal_up_left.pink": "左下方粉紅色填滿", "block.minecraft.banner.diagonal_up_left.purple": "左下方紫色填滿", "block.minecraft.banner.diagonal_up_left.red": "左下方紅色填滿", "block.minecraft.banner.diagonal_up_left.white": "左下方白色填滿", "block.minecraft.banner.diagonal_up_left.yellow": "左下方黃色填滿", "block.minecraft.banner.diagonal_up_right.black": "右下方黑色填滿", "block.minecraft.banner.diagonal_up_right.blue": "右下方藍色填滿", "block.minecraft.banner.diagonal_up_right.brown": "右下方棕色填滿", "block.minecraft.banner.diagonal_up_right.cyan": "右下方青色填滿", "block.minecraft.banner.diagonal_up_right.gray": "右下半部分灰色填滿", "block.minecraft.banner.diagonal_up_right.green": "右下方綠色填滿", "block.minecraft.banner.diagonal_up_right.light_blue": "右下半部分淺藍色填滿", "block.minecraft.banner.diagonal_up_right.light_gray": "右下方淺灰色填滿", "block.minecraft.banner.diagonal_up_right.lime": "右下半部分淺綠色填滿", "block.minecraft.banner.diagonal_up_right.magenta": "右下方洋紅色填滿", "block.minecraft.banner.diagonal_up_right.orange": "右下方橙色填滿", "block.minecraft.banner.diagonal_up_right.pink": "右下方粉紅色填滿", "block.minecraft.banner.diagonal_up_right.purple": "右下方紫色填滿", "block.minecraft.banner.diagonal_up_right.red": "右下方紅色填滿", "block.minecraft.banner.diagonal_up_right.white": "右下方白色填滿", "block.minecraft.banner.diagonal_up_right.yellow": "右下方黃色填滿", "block.minecraft.banner.flow.black": "黑色渦流", "block.minecraft.banner.flow.blue": "藍色渦流", "block.minecraft.banner.flow.brown": "棕色渦流", "block.minecraft.banner.flow.cyan": "青色渦流", "block.minecraft.banner.flow.gray": "灰色渦流", "block.minecraft.banner.flow.green": "綠色渦流", "block.minecraft.banner.flow.light_blue": "淺藍色渦流", "block.minecraft.banner.flow.light_gray": "淺灰色渦流", "block.minecraft.banner.flow.lime": "淺綠色渦流", "block.minecraft.banner.flow.magenta": "洋紅色渦流", "block.minecraft.banner.flow.orange": "橙色渦流", "block.minecraft.banner.flow.pink": "粉紅色渦流", "block.minecraft.banner.flow.purple": "紫色渦流", "block.minecraft.banner.flow.red": "紅色渦流", "block.minecraft.banner.flow.white": "白色渦流", "block.minecraft.banner.flow.yellow": "黃色渦流", "block.minecraft.banner.flower.black": "黑色花朵圖紋", "block.minecraft.banner.flower.blue": "藍色花朵圖紋", "block.minecraft.banner.flower.brown": "棕色花朵圖紋", "block.minecraft.banner.flower.cyan": "青色花朵圖紋", "block.minecraft.banner.flower.gray": "灰色花朵圖紋", "block.minecraft.banner.flower.green": "綠色花朵圖紋", "block.minecraft.banner.flower.light_blue": "淺藍色花朵圖紋", "block.minecraft.banner.flower.light_gray": "淺灰色花朵圖紋", "block.minecraft.banner.flower.lime": "淺綠色花朵圖紋", "block.minecraft.banner.flower.magenta": "洋紅色花朵圖紋", "block.minecraft.banner.flower.orange": "橙色花朵圖紋", "block.minecraft.banner.flower.pink": "粉紅色花朵圖紋", "block.minecraft.banner.flower.purple": "紫色花朵圖紋", "block.minecraft.banner.flower.red": "紅色花朵圖紋", "block.minecraft.banner.flower.white": "白色花朵圖紋", "block.minecraft.banner.flower.yellow": "黃色花朵圖紋", "block.minecraft.banner.globe.black": "黑色地球", "block.minecraft.banner.globe.blue": "藍色地球", "block.minecraft.banner.globe.brown": "棕色地球", "block.minecraft.banner.globe.cyan": "青色地球", "block.minecraft.banner.globe.gray": "灰色地球", "block.minecraft.banner.globe.green": "綠色地球", "block.minecraft.banner.globe.light_blue": "淺藍色地球", "block.minecraft.banner.globe.light_gray": "淺灰色地球", "block.minecraft.banner.globe.lime": "淺綠色地球", "block.minecraft.banner.globe.magenta": "洋紅色地球", "block.minecraft.banner.globe.orange": "橙色地球", "block.minecraft.banner.globe.pink": "粉紅色地球", "block.minecraft.banner.globe.purple": "紫色地球", "block.minecraft.banner.globe.red": "紅色地球", "block.minecraft.banner.globe.white": "白色地球", "block.minecraft.banner.globe.yellow": "黃色地球", "block.minecraft.banner.gradient.black": "黑色往下漸淡", "block.minecraft.banner.gradient.blue": "藍色往下漸淡", "block.minecraft.banner.gradient.brown": "棕色往下漸淡", "block.minecraft.banner.gradient.cyan": "青色往下漸淡", "block.minecraft.banner.gradient.gray": "灰色往下漸淡", "block.minecraft.banner.gradient.green": "綠色往下漸淡", "block.minecraft.banner.gradient.light_blue": "淺藍色往下漸淡", "block.minecraft.banner.gradient.light_gray": "淺灰色往下漸淡", "block.minecraft.banner.gradient.lime": "淺綠色往下漸淡", "block.minecraft.banner.gradient.magenta": "洋紅色往下漸淡", "block.minecraft.banner.gradient.orange": "橙色往下漸淡", "block.minecraft.banner.gradient.pink": "粉紅色往下漸淡", "block.minecraft.banner.gradient.purple": "紫色往下漸淡", "block.minecraft.banner.gradient.red": "紅色往下漸淡", "block.minecraft.banner.gradient.white": "白色往下漸淡", "block.minecraft.banner.gradient.yellow": "黃色往下漸淡", "block.minecraft.banner.gradient_up.black": "黑色往上漸淡", "block.minecraft.banner.gradient_up.blue": "藍色往上漸淡", "block.minecraft.banner.gradient_up.brown": "棕色往上漸淡", "block.minecraft.banner.gradient_up.cyan": "青色往上漸淡", "block.minecraft.banner.gradient_up.gray": "灰色往上漸淡", "block.minecraft.banner.gradient_up.green": "綠色往上漸淡", "block.minecraft.banner.gradient_up.light_blue": "淺藍色往上漸淡", "block.minecraft.banner.gradient_up.light_gray": "淺灰色往上漸淡", "block.minecraft.banner.gradient_up.lime": "淺綠色往上漸淡", "block.minecraft.banner.gradient_up.magenta": "洋紅色往上漸淡", "block.minecraft.banner.gradient_up.orange": "橙色往上漸淡", "block.minecraft.banner.gradient_up.pink": "粉紅色往上漸淡", "block.minecraft.banner.gradient_up.purple": "紫色往上漸淡", "block.minecraft.banner.gradient_up.red": "紅色往上漸淡", "block.minecraft.banner.gradient_up.white": "白色往上漸淡", "block.minecraft.banner.gradient_up.yellow": "黃色往上漸淡", "block.minecraft.banner.guster.black": "黑色狂風", "block.minecraft.banner.guster.blue": "藍色狂風", "block.minecraft.banner.guster.brown": "棕色狂風", "block.minecraft.banner.guster.cyan": "青色狂風", "block.minecraft.banner.guster.gray": "灰色狂風", "block.minecraft.banner.guster.green": "綠色狂風", "block.minecraft.banner.guster.light_blue": "淺藍色狂風", "block.minecraft.banner.guster.light_gray": "淺灰色狂風", "block.minecraft.banner.guster.lime": "淺綠色狂風", "block.minecraft.banner.guster.magenta": "洋紅色狂風", "block.minecraft.banner.guster.orange": "橙色狂風", "block.minecraft.banner.guster.pink": "粉紅色狂風", "block.minecraft.banner.guster.purple": "紫色狂風", "block.minecraft.banner.guster.red": "紅色狂風", "block.minecraft.banner.guster.white": "白色狂風", "block.minecraft.banner.guster.yellow": "黃色狂風", "block.minecraft.banner.half_horizontal.black": "上半部分黑色填滿", "block.minecraft.banner.half_horizontal.blue": "上半部分藍色填滿", "block.minecraft.banner.half_horizontal.brown": "上半部分棕色填滿", "block.minecraft.banner.half_horizontal.cyan": "上半部分青色填滿", "block.minecraft.banner.half_horizontal.gray": "上半部分灰色填滿", "block.minecraft.banner.half_horizontal.green": "上半部分綠色填滿", "block.minecraft.banner.half_horizontal.light_blue": "上半部分淺藍色填滿", "block.minecraft.banner.half_horizontal.light_gray": "上半部分淺灰色填滿", "block.minecraft.banner.half_horizontal.lime": "上半部分淺綠色填滿", "block.minecraft.banner.half_horizontal.magenta": "上半部分洋紅色填滿", "block.minecraft.banner.half_horizontal.orange": "上半部分橙色填滿", "block.minecraft.banner.half_horizontal.pink": "上半部分粉紅色填滿", "block.minecraft.banner.half_horizontal.purple": "上半部分紫色填滿", "block.minecraft.banner.half_horizontal.red": "上半部分紅色填滿", "block.minecraft.banner.half_horizontal.white": "上半部分白色填滿", "block.minecraft.banner.half_horizontal.yellow": "上半部分黃色填滿", "block.minecraft.banner.half_horizontal_bottom.black": "下半部分黑色填滿", "block.minecraft.banner.half_horizontal_bottom.blue": "下半部分藍色填滿", "block.minecraft.banner.half_horizontal_bottom.brown": "下半部分棕色填滿", "block.minecraft.banner.half_horizontal_bottom.cyan": "下半部分青色填滿", "block.minecraft.banner.half_horizontal_bottom.gray": "下半部分灰色填滿", "block.minecraft.banner.half_horizontal_bottom.green": "下半部分綠色填滿", "block.minecraft.banner.half_horizontal_bottom.light_blue": "下半部分淺藍色填滿", "block.minecraft.banner.half_horizontal_bottom.light_gray": "下半部分淺灰色填滿", "block.minecraft.banner.half_horizontal_bottom.lime": "下半部分淺綠色填滿", "block.minecraft.banner.half_horizontal_bottom.magenta": "下半部分洋紅色填滿", "block.minecraft.banner.half_horizontal_bottom.orange": "下半部分橙色填滿", "block.minecraft.banner.half_horizontal_bottom.pink": "下半部分粉紅色填滿", "block.minecraft.banner.half_horizontal_bottom.purple": "下半部分紫色填滿", "block.minecraft.banner.half_horizontal_bottom.red": "下半部分紅色填滿", "block.minecraft.banner.half_horizontal_bottom.white": "下半部分白色填滿", "block.minecraft.banner.half_horizontal_bottom.yellow": "下半部分黃色填滿", "block.minecraft.banner.half_vertical.black": "左半部分黑色填滿", "block.minecraft.banner.half_vertical.blue": "左半部分藍色填滿", "block.minecraft.banner.half_vertical.brown": "左半部分棕色填滿", "block.minecraft.banner.half_vertical.cyan": "左半部分青色填滿", "block.minecraft.banner.half_vertical.gray": "左半部分灰色填滿", "block.minecraft.banner.half_vertical.green": "左半部分綠色填滿", "block.minecraft.banner.half_vertical.light_blue": "左半部分淺藍色填滿", "block.minecraft.banner.half_vertical.light_gray": "左半部分淺灰色填滿", "block.minecraft.banner.half_vertical.lime": "左半部分淺綠色填滿", "block.minecraft.banner.half_vertical.magenta": "左半部分洋紅色填滿", "block.minecraft.banner.half_vertical.orange": "左半部分橙色填滿", "block.minecraft.banner.half_vertical.pink": "左半部分粉紅色填滿", "block.minecraft.banner.half_vertical.purple": "左半部分紫色填滿", "block.minecraft.banner.half_vertical.red": "左半部分紅色填滿", "block.minecraft.banner.half_vertical.white": "左半部分白色填滿", "block.minecraft.banner.half_vertical.yellow": "左半部分黃色填滿", "block.minecraft.banner.half_vertical_right.black": "右半部分黑色填滿", "block.minecraft.banner.half_vertical_right.blue": "右半部分藍色填滿", "block.minecraft.banner.half_vertical_right.brown": "右半部分棕色填滿", "block.minecraft.banner.half_vertical_right.cyan": "右半部分青色填滿", "block.minecraft.banner.half_vertical_right.gray": "右半部分灰色填滿", "block.minecraft.banner.half_vertical_right.green": "右半部分綠色填滿", "block.minecraft.banner.half_vertical_right.light_blue": "右半部分淺藍色填滿", "block.minecraft.banner.half_vertical_right.light_gray": "右半部分淺灰色填滿", "block.minecraft.banner.half_vertical_right.lime": "右半部分淺綠色填滿", "block.minecraft.banner.half_vertical_right.magenta": "右半部分洋紅色填滿", "block.minecraft.banner.half_vertical_right.orange": "右半部分橙色填滿", "block.minecraft.banner.half_vertical_right.pink": "右半部分粉紅色填滿", "block.minecraft.banner.half_vertical_right.purple": "右半部分紫色填滿", "block.minecraft.banner.half_vertical_right.red": "右半部分紅色填滿", "block.minecraft.banner.half_vertical_right.white": "右半部分白色填滿", "block.minecraft.banner.half_vertical_right.yellow": "右半部分黃色填滿", "block.minecraft.banner.mojang.black": "黑色 Mojang 標誌", "block.minecraft.banner.mojang.blue": "藍色 Mojang 標誌", "block.minecraft.banner.mojang.brown": "棕色 Mojang 標誌", "block.minecraft.banner.mojang.cyan": "青色 Mojang 標誌", "block.minecraft.banner.mojang.gray": "灰色 Mojang 標誌", "block.minecraft.banner.mojang.green": "綠色 Mojang 標誌", "block.minecraft.banner.mojang.light_blue": "淺藍色 Mojang 標誌", "block.minecraft.banner.mojang.light_gray": "淺灰色 Mojang 標誌", "block.minecraft.banner.mojang.lime": "淺綠色 Mojang 標誌", "block.minecraft.banner.mojang.magenta": "洋紅色 Mojang 標誌", "block.minecraft.banner.mojang.orange": "橙色 Mojang 標誌", "block.minecraft.banner.mojang.pink": "粉紅色 Mojang 標誌", "block.minecraft.banner.mojang.purple": "紫色 Mojang 標誌", "block.minecraft.banner.mojang.red": "紅色 Mojang 標誌", "block.minecraft.banner.mojang.white": "白色 Mojang 標誌", "block.minecraft.banner.mojang.yellow": "黃色 Mojang 標誌", "block.minecraft.banner.piglin.black": "黑色豬鼻", "block.minecraft.banner.piglin.blue": "藍色豬鼻", "block.minecraft.banner.piglin.brown": "棕色豬鼻", "block.minecraft.banner.piglin.cyan": "青色豬鼻", "block.minecraft.banner.piglin.gray": "灰色豬鼻", "block.minecraft.banner.piglin.green": "綠色豬鼻", "block.minecraft.banner.piglin.light_blue": "淺藍色豬鼻", "block.minecraft.banner.piglin.light_gray": "淺灰色豬鼻", "block.minecraft.banner.piglin.lime": "淺綠色豬鼻", "block.minecraft.banner.piglin.magenta": "洋紅色豬鼻", "block.minecraft.banner.piglin.orange": "橙色豬鼻", "block.minecraft.banner.piglin.pink": "粉紅色豬鼻", "block.minecraft.banner.piglin.purple": "紫色豬鼻", "block.minecraft.banner.piglin.red": "紅色豬鼻", "block.minecraft.banner.piglin.white": "白色豬鼻", "block.minecraft.banner.piglin.yellow": "黃色豬鼻", "block.minecraft.banner.rhombus.black": "黑色菱形", "block.minecraft.banner.rhombus.blue": "藍色菱形", "block.minecraft.banner.rhombus.brown": "棕色菱形", "block.minecraft.banner.rhombus.cyan": "青色菱形", "block.minecraft.banner.rhombus.gray": "灰色菱形", "block.minecraft.banner.rhombus.green": "綠色菱形", "block.minecraft.banner.rhombus.light_blue": "淺藍色菱形", "block.minecraft.banner.rhombus.light_gray": "淺灰色菱形", "block.minecraft.banner.rhombus.lime": "淺綠色菱形", "block.minecraft.banner.rhombus.magenta": "洋紅色菱形", "block.minecraft.banner.rhombus.orange": "橙色菱形", "block.minecraft.banner.rhombus.pink": "粉紅色菱形", "block.minecraft.banner.rhombus.purple": "紫色菱形", "block.minecraft.banner.rhombus.red": "紅色菱形", "block.minecraft.banner.rhombus.white": "白色菱形", "block.minecraft.banner.rhombus.yellow": "黃色菱形", "block.minecraft.banner.skull.black": "黑色骷髏圖紋", "block.minecraft.banner.skull.blue": "藍色骷髏圖紋", "block.minecraft.banner.skull.brown": "棕色骷髏圖紋", "block.minecraft.banner.skull.cyan": "青色骷髏圖紋", "block.minecraft.banner.skull.gray": "灰色骷髏圖紋", "block.minecraft.banner.skull.green": "綠色骷髏圖紋", "block.minecraft.banner.skull.light_blue": "淺藍色骷髏圖紋", "block.minecraft.banner.skull.light_gray": "淺灰色骷髏圖紋", "block.minecraft.banner.skull.lime": "淺綠色骷髏圖紋", "block.minecraft.banner.skull.magenta": "洋紅色骷髏圖紋", "block.minecraft.banner.skull.orange": "橙色骷髏圖紋", "block.minecraft.banner.skull.pink": "粉紅色骷髏圖紋", "block.minecraft.banner.skull.purple": "紫色骷髏圖紋", "block.minecraft.banner.skull.red": "紅色骷髏圖紋", "block.minecraft.banner.skull.white": "白色骷髏圖紋", "block.minecraft.banner.skull.yellow": "黃色骷髏圖紋", "block.minecraft.banner.small_stripes.black": "黑色垂直條紋", "block.minecraft.banner.small_stripes.blue": "藍色垂直條紋", "block.minecraft.banner.small_stripes.brown": "棕色垂直條紋", "block.minecraft.banner.small_stripes.cyan": "青色垂直條紋", "block.minecraft.banner.small_stripes.gray": "灰色垂直條紋", "block.minecraft.banner.small_stripes.green": "綠色垂直條紋", "block.minecraft.banner.small_stripes.light_blue": "淺藍色垂直條紋", "block.minecraft.banner.small_stripes.light_gray": "淺灰色垂直條紋", "block.minecraft.banner.small_stripes.lime": "淺綠色垂直條紋", "block.minecraft.banner.small_stripes.magenta": "洋紅色垂直條紋", "block.minecraft.banner.small_stripes.orange": "橙色垂直條紋", "block.minecraft.banner.small_stripes.pink": "粉紅色垂直條紋", "block.minecraft.banner.small_stripes.purple": "紫色垂直條紋", "block.minecraft.banner.small_stripes.red": "紅色垂直條紋", "block.minecraft.banner.small_stripes.white": "白色垂直條紋", "block.minecraft.banner.small_stripes.yellow": "黃色垂直條紋", "block.minecraft.banner.square_bottom_left.black": "左下黑色矩形", "block.minecraft.banner.square_bottom_left.blue": "左下藍色矩形", "block.minecraft.banner.square_bottom_left.brown": "左下棕色矩形", "block.minecraft.banner.square_bottom_left.cyan": "左下青色矩形", "block.minecraft.banner.square_bottom_left.gray": "左下灰色矩形", "block.minecraft.banner.square_bottom_left.green": "左下綠色矩形", "block.minecraft.banner.square_bottom_left.light_blue": "左下淺藍色矩形", "block.minecraft.banner.square_bottom_left.light_gray": "左下淺灰色矩形", "block.minecraft.banner.square_bottom_left.lime": "左下淺綠色矩形", "block.minecraft.banner.square_bottom_left.magenta": "左下洋紅色矩形", "block.minecraft.banner.square_bottom_left.orange": "左下橙色矩形", "block.minecraft.banner.square_bottom_left.pink": "左下粉紅色矩形", "block.minecraft.banner.square_bottom_left.purple": "左下紫色矩形", "block.minecraft.banner.square_bottom_left.red": "左下紅色矩形", "block.minecraft.banner.square_bottom_left.white": "左下白色矩形", "block.minecraft.banner.square_bottom_left.yellow": "左下黃色矩形", "block.minecraft.banner.square_bottom_right.black": "右下黑色矩形", "block.minecraft.banner.square_bottom_right.blue": "右下藍色矩形", "block.minecraft.banner.square_bottom_right.brown": "右下棕色矩形", "block.minecraft.banner.square_bottom_right.cyan": "右下青色矩形", "block.minecraft.banner.square_bottom_right.gray": "右下灰色矩形", "block.minecraft.banner.square_bottom_right.green": "右下綠色矩形", "block.minecraft.banner.square_bottom_right.light_blue": "右下淺藍色矩形", "block.minecraft.banner.square_bottom_right.light_gray": "右下淺灰色矩形", "block.minecraft.banner.square_bottom_right.lime": "右下淺綠色矩形", "block.minecraft.banner.square_bottom_right.magenta": "右下洋紅色矩形", "block.minecraft.banner.square_bottom_right.orange": "右下橙色矩形", "block.minecraft.banner.square_bottom_right.pink": "右下粉紅色矩形", "block.minecraft.banner.square_bottom_right.purple": "右下紫色矩形", "block.minecraft.banner.square_bottom_right.red": "右下紅色矩形", "block.minecraft.banner.square_bottom_right.white": "右下白色矩形", "block.minecraft.banner.square_bottom_right.yellow": "右下黃色矩形", "block.minecraft.banner.square_top_left.black": "左上黑色矩形", "block.minecraft.banner.square_top_left.blue": "左上藍色矩形", "block.minecraft.banner.square_top_left.brown": "左上棕色矩形", "block.minecraft.banner.square_top_left.cyan": "左上青色矩形", "block.minecraft.banner.square_top_left.gray": "左上灰色矩形", "block.minecraft.banner.square_top_left.green": "左上綠色矩形", "block.minecraft.banner.square_top_left.light_blue": "左上淺藍色矩形", "block.minecraft.banner.square_top_left.light_gray": "左上淺灰色矩形", "block.minecraft.banner.square_top_left.lime": "左上淺綠色矩形", "block.minecraft.banner.square_top_left.magenta": "左上洋紅色矩形", "block.minecraft.banner.square_top_left.orange": "左上橙色矩形", "block.minecraft.banner.square_top_left.pink": "左上粉紅色矩形", "block.minecraft.banner.square_top_left.purple": "左上紫色矩形", "block.minecraft.banner.square_top_left.red": "左上紅色矩形", "block.minecraft.banner.square_top_left.white": "左上白色矩形", "block.minecraft.banner.square_top_left.yellow": "左上黃色矩形", "block.minecraft.banner.square_top_right.black": "右上黑色矩形", "block.minecraft.banner.square_top_right.blue": "右上藍色矩形", "block.minecraft.banner.square_top_right.brown": "右上棕色矩形", "block.minecraft.banner.square_top_right.cyan": "右上青色矩形", "block.minecraft.banner.square_top_right.gray": "右上灰色矩形", "block.minecraft.banner.square_top_right.green": "右上綠色矩形", "block.minecraft.banner.square_top_right.light_blue": "右上淺藍色矩形", "block.minecraft.banner.square_top_right.light_gray": "右上淺灰色矩形", "block.minecraft.banner.square_top_right.lime": "右上淺綠色矩形", "block.minecraft.banner.square_top_right.magenta": "右上洋紅色矩形", "block.minecraft.banner.square_top_right.orange": "右上橙色矩形", "block.minecraft.banner.square_top_right.pink": "右上粉紅色矩形", "block.minecraft.banner.square_top_right.purple": "右上紫色矩形", "block.minecraft.banner.square_top_right.red": "右上紅色矩形", "block.minecraft.banner.square_top_right.white": "右上白色矩形", "block.minecraft.banner.square_top_right.yellow": "右上黃色矩形", "block.minecraft.banner.straight_cross.black": "黑色十字", "block.minecraft.banner.straight_cross.blue": "藍色十字", "block.minecraft.banner.straight_cross.brown": "棕色十字", "block.minecraft.banner.straight_cross.cyan": "青色十字", "block.minecraft.banner.straight_cross.gray": "灰色十字", "block.minecraft.banner.straight_cross.green": "綠色十字", "block.minecraft.banner.straight_cross.light_blue": "淺藍色十字", "block.minecraft.banner.straight_cross.light_gray": "淺灰色十字", "block.minecraft.banner.straight_cross.lime": "淺綠色十字", "block.minecraft.banner.straight_cross.magenta": "洋紅色十字", "block.minecraft.banner.straight_cross.orange": "橙色十字", "block.minecraft.banner.straight_cross.pink": "粉紅色十字", "block.minecraft.banner.straight_cross.purple": "紫色十字", "block.minecraft.banner.straight_cross.red": "紅色十字", "block.minecraft.banner.straight_cross.white": "白色十字", "block.minecraft.banner.straight_cross.yellow": "黃色十字", "block.minecraft.banner.stripe_bottom.black": "黑色底部", "block.minecraft.banner.stripe_bottom.blue": "藍色底部", "block.minecraft.banner.stripe_bottom.brown": "棕色底部", "block.minecraft.banner.stripe_bottom.cyan": "青色底部", "block.minecraft.banner.stripe_bottom.gray": "灰色底部", "block.minecraft.banner.stripe_bottom.green": "綠色底部", "block.minecraft.banner.stripe_bottom.light_blue": "淺藍色底部", "block.minecraft.banner.stripe_bottom.light_gray": "淺灰色底部", "block.minecraft.banner.stripe_bottom.lime": "淺綠色底部", "block.minecraft.banner.stripe_bottom.magenta": "洋紅色底部", "block.minecraft.banner.stripe_bottom.orange": "橙色底部", "block.minecraft.banner.stripe_bottom.pink": "粉紅色底部", "block.minecraft.banner.stripe_bottom.purple": "紫色底部", "block.minecraft.banner.stripe_bottom.red": "紅色底部", "block.minecraft.banner.stripe_bottom.white": "白色底部", "block.minecraft.banner.stripe_bottom.yellow": "黃色底部", "block.minecraft.banner.stripe_center.black": "中間黑色縱帶", "block.minecraft.banner.stripe_center.blue": "中間藍色縱帶", "block.minecraft.banner.stripe_center.brown": "中間棕色縱帶", "block.minecraft.banner.stripe_center.cyan": "中間青色縱帶", "block.minecraft.banner.stripe_center.gray": "中間灰色縱帶", "block.minecraft.banner.stripe_center.green": "中間綠色縱帶", "block.minecraft.banner.stripe_center.light_blue": "中間淺藍色縱帶", "block.minecraft.banner.stripe_center.light_gray": "中間淺灰色縱帶", "block.minecraft.banner.stripe_center.lime": "中間淺綠色縱帶", "block.minecraft.banner.stripe_center.magenta": "中間洋紅色縱帶", "block.minecraft.banner.stripe_center.orange": "中間橙色縱帶", "block.minecraft.banner.stripe_center.pink": "中間粉紅色縱帶", "block.minecraft.banner.stripe_center.purple": "中間紫色縱帶", "block.minecraft.banner.stripe_center.red": "中間紅色縱帶", "block.minecraft.banner.stripe_center.white": "中間白色縱帶", "block.minecraft.banner.stripe_center.yellow": "中間黃色縱帶", "block.minecraft.banner.stripe_downleft.black": "左斜黑色對角帶", "block.minecraft.banner.stripe_downleft.blue": "左斜藍色對角帶", "block.minecraft.banner.stripe_downleft.brown": "左斜棕色對角帶", "block.minecraft.banner.stripe_downleft.cyan": "左斜青色對角帶", "block.minecraft.banner.stripe_downleft.gray": "左斜灰色對角帶", "block.minecraft.banner.stripe_downleft.green": "左斜綠色對角帶", "block.minecraft.banner.stripe_downleft.light_blue": "左斜淺藍色對角帶", "block.minecraft.banner.stripe_downleft.light_gray": "左斜淺灰色對角帶", "block.minecraft.banner.stripe_downleft.lime": "左斜淺綠色對角帶", "block.minecraft.banner.stripe_downleft.magenta": "左斜洋紅色對角帶", "block.minecraft.banner.stripe_downleft.orange": "左斜橙色對角帶", "block.minecraft.banner.stripe_downleft.pink": "左斜粉紅色對角帶", "block.minecraft.banner.stripe_downleft.purple": "左斜紫色對角帶", "block.minecraft.banner.stripe_downleft.red": "左斜紅色對角帶", "block.minecraft.banner.stripe_downleft.white": "左斜白色對角帶", "block.minecraft.banner.stripe_downleft.yellow": "左斜黃色對角帶", "block.minecraft.banner.stripe_downright.black": "右斜黑色對角帶", "block.minecraft.banner.stripe_downright.blue": "右斜藍色對角帶", "block.minecraft.banner.stripe_downright.brown": "右斜棕色對角帶", "block.minecraft.banner.stripe_downright.cyan": "右斜青色對角帶", "block.minecraft.banner.stripe_downright.gray": "右斜灰色對角帶", "block.minecraft.banner.stripe_downright.green": "右斜綠色對角帶", "block.minecraft.banner.stripe_downright.light_blue": "右斜淺藍色對角帶", "block.minecraft.banner.stripe_downright.light_gray": "右斜淺灰色對角帶", "block.minecraft.banner.stripe_downright.lime": "右斜淺綠色對角帶", "block.minecraft.banner.stripe_downright.magenta": "右斜洋紅色對角帶", "block.minecraft.banner.stripe_downright.orange": "右斜橙色對角帶", "block.minecraft.banner.stripe_downright.pink": "右斜粉紅色對角帶", "block.minecraft.banner.stripe_downright.purple": "右斜紫色對角帶", "block.minecraft.banner.stripe_downright.red": "右斜紅色對角帶", "block.minecraft.banner.stripe_downright.white": "右斜白色對角帶", "block.minecraft.banner.stripe_downright.yellow": "右斜黃色對角帶", "block.minecraft.banner.stripe_left.black": "左方黑色縱帶", "block.minecraft.banner.stripe_left.blue": "左方藍色縱帶", "block.minecraft.banner.stripe_left.brown": "左方棕色縱帶", "block.minecraft.banner.stripe_left.cyan": "左方青色縱帶", "block.minecraft.banner.stripe_left.gray": "左方灰色縱帶", "block.minecraft.banner.stripe_left.green": "左方綠色縱帶", "block.minecraft.banner.stripe_left.light_blue": "左方淺藍色縱帶", "block.minecraft.banner.stripe_left.light_gray": "左方淺灰色縱帶", "block.minecraft.banner.stripe_left.lime": "左方淺綠色縱帶", "block.minecraft.banner.stripe_left.magenta": "左方洋紅色縱帶", "block.minecraft.banner.stripe_left.orange": "左方橙色縱帶", "block.minecraft.banner.stripe_left.pink": "左方粉紅色縱帶", "block.minecraft.banner.stripe_left.purple": "左方紫色縱帶", "block.minecraft.banner.stripe_left.red": "左方紅色縱帶", "block.minecraft.banner.stripe_left.white": "左方白色縱帶", "block.minecraft.banner.stripe_left.yellow": "左方黃色縱帶", "block.minecraft.banner.stripe_middle.black": "中間黑色橫帶", "block.minecraft.banner.stripe_middle.blue": "中間藍色橫帶", "block.minecraft.banner.stripe_middle.brown": "中間棕色橫帶", "block.minecraft.banner.stripe_middle.cyan": "中間青色橫帶", "block.minecraft.banner.stripe_middle.gray": "中間灰色橫帶", "block.minecraft.banner.stripe_middle.green": "中間綠色橫帶", "block.minecraft.banner.stripe_middle.light_blue": "中間淺藍色橫帶", "block.minecraft.banner.stripe_middle.light_gray": "中間淺灰色橫帶", "block.minecraft.banner.stripe_middle.lime": "中間淺綠色橫帶", "block.minecraft.banner.stripe_middle.magenta": "中間洋紅色橫帶", "block.minecraft.banner.stripe_middle.orange": "中間橙色橫帶", "block.minecraft.banner.stripe_middle.pink": "中間粉紅色橫帶", "block.minecraft.banner.stripe_middle.purple": "中間紫色橫帶", "block.minecraft.banner.stripe_middle.red": "中間紅色橫帶", "block.minecraft.banner.stripe_middle.white": "中間白色橫帶", "block.minecraft.banner.stripe_middle.yellow": "中間黃色橫帶", "block.minecraft.banner.stripe_right.black": "右方黑色縱帶", "block.minecraft.banner.stripe_right.blue": "右方藍色縱帶", "block.minecraft.banner.stripe_right.brown": "右方棕色縱帶", "block.minecraft.banner.stripe_right.cyan": "右方青色縱帶", "block.minecraft.banner.stripe_right.gray": "右方灰色縱帶", "block.minecraft.banner.stripe_right.green": "右方綠色縱帶", "block.minecraft.banner.stripe_right.light_blue": "右方淺藍色縱帶", "block.minecraft.banner.stripe_right.light_gray": "右方淺灰色縱帶", "block.minecraft.banner.stripe_right.lime": "右方淺綠色縱帶", "block.minecraft.banner.stripe_right.magenta": "右方洋紅色縱帶", "block.minecraft.banner.stripe_right.orange": "右方橙色縱帶", "block.minecraft.banner.stripe_right.pink": "右方粉紅色縱帶", "block.minecraft.banner.stripe_right.purple": "右方紫色縱帶", "block.minecraft.banner.stripe_right.red": "右方紅色縱帶", "block.minecraft.banner.stripe_right.white": "右方白色縱帶", "block.minecraft.banner.stripe_right.yellow": "右方黃色縱帶", "block.minecraft.banner.stripe_top.black": "黑色頂部", "block.minecraft.banner.stripe_top.blue": "藍色頂部", "block.minecraft.banner.stripe_top.brown": "棕色頂部", "block.minecraft.banner.stripe_top.cyan": "青色頂部", "block.minecraft.banner.stripe_top.gray": "灰色頂部", "block.minecraft.banner.stripe_top.green": "綠色頂部", "block.minecraft.banner.stripe_top.light_blue": "淺藍色頂部", "block.minecraft.banner.stripe_top.light_gray": "淺灰色頂部", "block.minecraft.banner.stripe_top.lime": "淺綠色頂部", "block.minecraft.banner.stripe_top.magenta": "洋紅色頂部", "block.minecraft.banner.stripe_top.orange": "橙色頂部", "block.minecraft.banner.stripe_top.pink": "粉紅色頂部", "block.minecraft.banner.stripe_top.purple": "紫色頂部", "block.minecraft.banner.stripe_top.red": "紅色頂部", "block.minecraft.banner.stripe_top.white": "白色頂部", "block.minecraft.banner.stripe_top.yellow": "黃色頂部", "block.minecraft.banner.triangle_bottom.black": "下方黑色三角形", "block.minecraft.banner.triangle_bottom.blue": "下方藍色三角形", "block.minecraft.banner.triangle_bottom.brown": "下方棕色三角形", "block.minecraft.banner.triangle_bottom.cyan": "下方青色三角形", "block.minecraft.banner.triangle_bottom.gray": "下方灰色三角形", "block.minecraft.banner.triangle_bottom.green": "下方綠色三角形", "block.minecraft.banner.triangle_bottom.light_blue": "下方淺藍色三角形", "block.minecraft.banner.triangle_bottom.light_gray": "下方淺灰色三角形", "block.minecraft.banner.triangle_bottom.lime": "下方淺綠色三角形", "block.minecraft.banner.triangle_bottom.magenta": "下方洋紅色三角形", "block.minecraft.banner.triangle_bottom.orange": "下方橙色三角形", "block.minecraft.banner.triangle_bottom.pink": "下方粉紅色三角形", "block.minecraft.banner.triangle_bottom.purple": "下方紫色三角形", "block.minecraft.banner.triangle_bottom.red": "下方紅色三角形", "block.minecraft.banner.triangle_bottom.white": "下方白色三角形", "block.minecraft.banner.triangle_bottom.yellow": "下方黃色三角形", "block.minecraft.banner.triangle_top.black": "上方黑色倒三角形", "block.minecraft.banner.triangle_top.blue": "上方藍色倒三角形", "block.minecraft.banner.triangle_top.brown": "上方棕色倒三角形", "block.minecraft.banner.triangle_top.cyan": "上方青色倒三角形", "block.minecraft.banner.triangle_top.gray": "上方灰色倒三角形", "block.minecraft.banner.triangle_top.green": "上方綠色倒三角形", "block.minecraft.banner.triangle_top.light_blue": "上方淺藍色倒三角形", "block.minecraft.banner.triangle_top.light_gray": "上方淺灰色倒三角形", "block.minecraft.banner.triangle_top.lime": "上方淺綠色倒三角形", "block.minecraft.banner.triangle_top.magenta": "上方洋紅色倒三角形", "block.minecraft.banner.triangle_top.orange": "上方橙色倒三角形", "block.minecraft.banner.triangle_top.pink": "上方粉紅色倒三角形", "block.minecraft.banner.triangle_top.purple": "上方紫色倒三角形", "block.minecraft.banner.triangle_top.red": "上方紅色倒三角形", "block.minecraft.banner.triangle_top.white": "上方白色倒三角形", "block.minecraft.banner.triangle_top.yellow": "上方黃色倒三角形", "block.minecraft.banner.triangles_bottom.black": "下側黑色鋸齒", "block.minecraft.banner.triangles_bottom.blue": "下側藍色鋸齒", "block.minecraft.banner.triangles_bottom.brown": "下側棕色鋸齒", "block.minecraft.banner.triangles_bottom.cyan": "下側青色鋸齒", "block.minecraft.banner.triangles_bottom.gray": "下側灰色鋸齒", "block.minecraft.banner.triangles_bottom.green": "下側綠色鋸齒", "block.minecraft.banner.triangles_bottom.light_blue": "下側淺藍色鋸齒", "block.minecraft.banner.triangles_bottom.light_gray": "下側淺灰色鋸齒", "block.minecraft.banner.triangles_bottom.lime": "下側淺綠色鋸齒", "block.minecraft.banner.triangles_bottom.magenta": "下側洋紅色鋸齒", "block.minecraft.banner.triangles_bottom.orange": "下側橙色鋸齒", "block.minecraft.banner.triangles_bottom.pink": "下側粉紅色鋸齒", "block.minecraft.banner.triangles_bottom.purple": "下側紫色鋸齒", "block.minecraft.banner.triangles_bottom.red": "下側紅色鋸齒", "block.minecraft.banner.triangles_bottom.white": "下側白色鋸齒", "block.minecraft.banner.triangles_bottom.yellow": "下側黃色鋸齒", "block.minecraft.banner.triangles_top.black": "上側黑色鋸齒", "block.minecraft.banner.triangles_top.blue": "上側藍色鋸齒", "block.minecraft.banner.triangles_top.brown": "上側棕色鋸齒", "block.minecraft.banner.triangles_top.cyan": "上側青色鋸齒", "block.minecraft.banner.triangles_top.gray": "上側灰色鋸齒", "block.minecraft.banner.triangles_top.green": "上側綠色鋸齒", "block.minecraft.banner.triangles_top.light_blue": "上側淺藍色鋸齒", "block.minecraft.banner.triangles_top.light_gray": "上側淺灰色鋸齒", "block.minecraft.banner.triangles_top.lime": "上側淺綠色鋸齒", "block.minecraft.banner.triangles_top.magenta": "上側洋紅色鋸齒", "block.minecraft.banner.triangles_top.orange": "上側橙色鋸齒", "block.minecraft.banner.triangles_top.pink": "上側粉紅色鋸齒", "block.minecraft.banner.triangles_top.purple": "上側紫色鋸齒", "block.minecraft.banner.triangles_top.red": "上側紅色鋸齒", "block.minecraft.banner.triangles_top.white": "上側白色鋸齒", "block.minecraft.banner.triangles_top.yellow": "上側黃色鋸齒", "block.minecraft.barrel": "木桶", "block.minecraft.barrier": "屏障", "block.minecraft.basalt": "玄武岩", "block.minecraft.beacon": "烽火台", "block.minecraft.beacon.primary": "主要效果", "block.minecraft.beacon.secondary": "次要效果", "block.minecraft.bed.no_sleep": "你只能在晚上或暴風雨中入睡", "block.minecraft.bed.not_safe": "你現在無法休息，附近有怪物在遊蕩", "block.minecraft.bed.obstructed": "這張床受到阻擋", "block.minecraft.bed.occupied": "這張床已被占用", "block.minecraft.bed.too_far_away": "現在無法休息，你離床太遠了", "block.minecraft.bedrock": "基岩", "block.minecraft.bee_nest": "蜂窩", "block.minecraft.beehive": "蜂箱", "block.minecraft.beetroots": "甜菜根", "block.minecraft.bell": "鐘", "block.minecraft.big_dripleaf": "大懸葉草", "block.minecraft.big_dripleaf_stem": "大懸葉草葉柄", "block.minecraft.birch_button": "樺木按鈕", "block.minecraft.birch_door": "樺木門", "block.minecraft.birch_fence": "樺木柵欄", "block.minecraft.birch_fence_gate": "樺木柵欄門", "block.minecraft.birch_hanging_sign": "懸掛式樺木告示牌", "block.minecraft.birch_leaves": "樺木樹葉", "block.minecraft.birch_log": "樺木原木", "block.minecraft.birch_planks": "樺木材", "block.minecraft.birch_pressure_plate": "樺木壓力板", "block.minecraft.birch_sapling": "樺木樹苗", "block.minecraft.birch_sign": "樺木告示牌", "block.minecraft.birch_slab": "樺木半磚", "block.minecraft.birch_stairs": "樺木階梯", "block.minecraft.birch_trapdoor": "樺木地板門", "block.minecraft.birch_wall_hanging_sign": "牆上的懸掛式樺木告示牌", "block.minecraft.birch_wall_sign": "牆上的樺木告示牌", "block.minecraft.birch_wood": "樺木塊", "block.minecraft.black_banner": "黑色旗幟", "block.minecraft.black_bed": "黑色床", "block.minecraft.black_candle": "黑色蠟燭", "block.minecraft.black_candle_cake": "插上黑色蠟燭的蛋糕", "block.minecraft.black_carpet": "黑色地毯", "block.minecraft.black_concrete": "黑色混凝土", "block.minecraft.black_concrete_powder": "黑色混凝土粉末", "block.minecraft.black_glazed_terracotta": "黑色釉陶", "block.minecraft.black_shulker_box": "黑色界伏盒", "block.minecraft.black_stained_glass": "黑色玻璃", "block.minecraft.black_stained_glass_pane": "黑色玻璃片", "block.minecraft.black_terracotta": "黑色陶土", "block.minecraft.black_wool": "黑色羊毛", "block.minecraft.blackstone": "黑石", "block.minecraft.blackstone_slab": "黑石半磚", "block.minecraft.blackstone_stairs": "黑石階梯", "block.minecraft.blackstone_wall": "黑石牆", "block.minecraft.blast_furnace": "高爐", "block.minecraft.blue_banner": "藍色旗幟", "block.minecraft.blue_bed": "藍色床", "block.minecraft.blue_candle": "藍色蠟燭", "block.minecraft.blue_candle_cake": "插上藍色蠟燭的蛋糕", "block.minecraft.blue_carpet": "藍色地毯", "block.minecraft.blue_concrete": "藍色混凝土", "block.minecraft.blue_concrete_powder": "藍色混凝土粉末", "block.minecraft.blue_glazed_terracotta": "藍色釉陶", "block.minecraft.blue_ice": "藍冰", "block.minecraft.blue_orchid": "藍色蝴蝶蘭", "block.minecraft.blue_shulker_box": "藍色界伏盒", "block.minecraft.blue_stained_glass": "藍色玻璃", "block.minecraft.blue_stained_glass_pane": "藍色玻璃片", "block.minecraft.blue_terracotta": "藍色陶土", "block.minecraft.blue_wool": "藍色羊毛", "block.minecraft.bone_block": "骨塊", "block.minecraft.bookshelf": "書櫃", "block.minecraft.brain_coral": "腦珊瑚", "block.minecraft.brain_coral_block": "腦珊瑚方塊", "block.minecraft.brain_coral_fan": "扇狀腦珊瑚", "block.minecraft.brain_coral_wall_fan": "牆上的扇狀腦珊瑚", "block.minecraft.brewing_stand": "釀造台", "block.minecraft.brick_slab": "紅磚半磚", "block.minecraft.brick_stairs": "紅磚階梯", "block.minecraft.brick_wall": "紅磚牆", "block.minecraft.bricks": "紅磚", "block.minecraft.brown_banner": "棕色旗幟", "block.minecraft.brown_bed": "棕色床", "block.minecraft.brown_candle": "棕色蠟燭", "block.minecraft.brown_candle_cake": "插上棕色蠟燭的蛋糕", "block.minecraft.brown_carpet": "棕色地毯", "block.minecraft.brown_concrete": "棕色混凝土", "block.minecraft.brown_concrete_powder": "棕色混凝土粉末", "block.minecraft.brown_glazed_terracotta": "棕色釉陶", "block.minecraft.brown_mushroom": "棕色蘑菇", "block.minecraft.brown_mushroom_block": "棕色蘑菇方塊", "block.minecraft.brown_shulker_box": "棕色界伏盒", "block.minecraft.brown_stained_glass": "棕色玻璃", "block.minecraft.brown_stained_glass_pane": "棕色玻璃片", "block.minecraft.brown_terracotta": "棕色陶土", "block.minecraft.brown_wool": "棕色羊毛", "block.minecraft.bubble_column": "氣泡柱", "block.minecraft.bubble_coral": "氣泡珊瑚", "block.minecraft.bubble_coral_block": "氣泡珊瑚方塊", "block.minecraft.bubble_coral_fan": "扇狀氣泡珊瑚", "block.minecraft.bubble_coral_wall_fan": "牆上的扇狀氣泡珊瑚", "block.minecraft.budding_amethyst": "紫水晶芽床", "block.minecraft.bush": "灌木叢", "block.minecraft.cactus": "仙人掌", "block.minecraft.cactus_flower": "仙人掌花", "block.minecraft.cake": "蛋糕", "block.minecraft.calcite": "方解石", "block.minecraft.calibrated_sculk_sensor": "校準伏聆振測器", "block.minecraft.campfire": "營火", "block.minecraft.candle": "蠟燭", "block.minecraft.candle_cake": "插上蠟燭的蛋糕", "block.minecraft.carrots": "胡蘿蔔", "block.minecraft.cartography_table": "製圖台", "block.minecraft.carved_pumpkin": "雕刻過的南瓜", "block.minecraft.cauldron": "鍋釜", "block.minecraft.cave_air": "洞穴空氣", "block.minecraft.cave_vines": "洞穴藤蔓", "block.minecraft.cave_vines_plant": "洞穴藤蔓植株", "block.minecraft.chain": "鎖鏈", "block.minecraft.chain_command_block": "連鎖式指令方塊", "block.minecraft.cherry_button": "櫻花木按鈕", "block.minecraft.cherry_door": "櫻花木門", "block.minecraft.cherry_fence": "櫻花木柵欄", "block.minecraft.cherry_fence_gate": "櫻花木柵欄門", "block.minecraft.cherry_hanging_sign": "懸掛式櫻花木告示牌", "block.minecraft.cherry_leaves": "櫻花木樹葉", "block.minecraft.cherry_log": "櫻花木原木", "block.minecraft.cherry_planks": "櫻花木材", "block.minecraft.cherry_pressure_plate": "櫻花木壓力板", "block.minecraft.cherry_sapling": "櫻花木樹苗", "block.minecraft.cherry_sign": "櫻花木告示牌", "block.minecraft.cherry_slab": "櫻花木半磚", "block.minecraft.cherry_stairs": "櫻花木階梯", "block.minecraft.cherry_trapdoor": "櫻花木地板門", "block.minecraft.cherry_wall_hanging_sign": "牆上的懸掛式櫻花木告示牌", "block.minecraft.cherry_wall_sign": "牆上的櫻花木告示牌", "block.minecraft.cherry_wood": "櫻花木塊", "block.minecraft.chest": "儲物箱", "block.minecraft.chipped_anvil": "微損的鐵砧", "block.minecraft.chiseled_bookshelf": "浮雕書櫃", "block.minecraft.chiseled_copper": "浮雕銅方塊", "block.minecraft.chiseled_deepslate": "浮雕深板岩", "block.minecraft.chiseled_nether_bricks": "浮雕地獄磚", "block.minecraft.chiseled_polished_blackstone": "浮雕拋光黑石", "block.minecraft.chiseled_quartz_block": "浮雕石英方塊", "block.minecraft.chiseled_red_sandstone": "浮雕紅砂岩", "block.minecraft.chiseled_resin_bricks": "浮雕樹脂磚", "block.minecraft.chiseled_sandstone": "浮雕砂岩", "block.minecraft.chiseled_stone_bricks": "浮雕石磚", "block.minecraft.chiseled_tuff": "浮雕凝灰岩", "block.minecraft.chiseled_tuff_bricks": "浮雕凝灰岩磚", "block.minecraft.chorus_flower": "歌萊花", "block.minecraft.chorus_plant": "歌萊枝", "block.minecraft.clay": "黏土", "block.minecraft.closed_eyeblossom": "閉合的擬目花", "block.minecraft.coal_block": "煤炭方塊", "block.minecraft.coal_ore": "煤礦", "block.minecraft.coarse_dirt": "粗泥", "block.minecraft.cobbled_deepslate": "深板岩碎石", "block.minecraft.cobbled_deepslate_slab": "碎深板岩半磚", "block.minecraft.cobbled_deepslate_stairs": "碎深板岩階梯", "block.minecraft.cobbled_deepslate_wall": "碎深板岩牆", "block.minecraft.cobblestone": "鵝卵石", "block.minecraft.cobblestone_slab": "鵝卵石半磚", "block.minecraft.cobblestone_stairs": "鵝卵石階梯", "block.minecraft.cobblestone_wall": "鵝卵石牆", "block.minecraft.cobweb": "蜘蛛網", "block.minecraft.cocoa": "可可豆", "block.minecraft.command_block": "指令方塊", "block.minecraft.comparator": "紅石比較器", "block.minecraft.composter": "堆肥箱", "block.minecraft.conduit": "海靈核心", "block.minecraft.copper_block": "銅方塊", "block.minecraft.copper_bulb": "銅燈", "block.minecraft.copper_door": "銅門", "block.minecraft.copper_grate": "銅格柵", "block.minecraft.copper_ore": "銅礦", "block.minecraft.copper_trapdoor": "銅地板門", "block.minecraft.cornflower": "矢車菊", "block.minecraft.cracked_deepslate_bricks": "裂紋深板岩磚", "block.minecraft.cracked_deepslate_tiles": "裂紋深板岩磚瓦", "block.minecraft.cracked_nether_bricks": "裂紋地獄磚", "block.minecraft.cracked_polished_blackstone_bricks": "裂紋拋光黑石磚", "block.minecraft.cracked_stone_bricks": "裂紋石磚", "block.minecraft.crafter": "合成器", "block.minecraft.crafting_table": "工作台", "block.minecraft.creaking_heart": "嘎枝之心", "block.minecraft.creeper_head": "苦力怕頭顱", "block.minecraft.creeper_wall_head": "牆上的苦力怕頭顱", "block.minecraft.crimson_button": "緋紅蕈木按鈕", "block.minecraft.crimson_door": "緋紅蕈木門", "block.minecraft.crimson_fence": "緋紅蕈木柵欄", "block.minecraft.crimson_fence_gate": "緋紅蕈木柵欄門", "block.minecraft.crimson_fungus": "緋紅蕈菇", "block.minecraft.crimson_hanging_sign": "懸掛式緋紅蕈木告示牌", "block.minecraft.crimson_hyphae": "緋紅菌絲體", "block.minecraft.crimson_nylium": "緋紅菌絲石", "block.minecraft.crimson_planks": "緋紅蕈木材", "block.minecraft.crimson_pressure_plate": "緋紅蕈木壓力板", "block.minecraft.crimson_roots": "緋紅蕈根", "block.minecraft.crimson_sign": "緋紅蕈木告示牌", "block.minecraft.crimson_slab": "緋紅蕈木半磚", "block.minecraft.crimson_stairs": "緋紅蕈木階梯", "block.minecraft.crimson_stem": "緋紅蕈柄", "block.minecraft.crimson_trapdoor": "緋紅蕈木地板門", "block.minecraft.crimson_wall_hanging_sign": "牆上的懸掛式緋紅蕈木告示牌", "block.minecraft.crimson_wall_sign": "牆上的緋紅蕈木告示牌", "block.minecraft.crying_obsidian": "哭泣的黑曜石", "block.minecraft.cut_copper": "切製銅方塊", "block.minecraft.cut_copper_slab": "切製銅半磚", "block.minecraft.cut_copper_stairs": "切製銅階梯", "block.minecraft.cut_red_sandstone": "切製紅砂岩", "block.minecraft.cut_red_sandstone_slab": "切製紅砂岩半磚", "block.minecraft.cut_sandstone": "切製砂岩", "block.minecraft.cut_sandstone_slab": "切製砂岩半磚", "block.minecraft.cyan_banner": "青色旗幟", "block.minecraft.cyan_bed": "青色床", "block.minecraft.cyan_candle": "青色蠟燭", "block.minecraft.cyan_candle_cake": "插上青色蠟燭的蛋糕", "block.minecraft.cyan_carpet": "青色地毯", "block.minecraft.cyan_concrete": "青色混凝土", "block.minecraft.cyan_concrete_powder": "青色混凝土粉末", "block.minecraft.cyan_glazed_terracotta": "青色釉陶", "block.minecraft.cyan_shulker_box": "青色界伏盒", "block.minecraft.cyan_stained_glass": "青色玻璃", "block.minecraft.cyan_stained_glass_pane": "青色玻璃片", "block.minecraft.cyan_terracotta": "青色陶土", "block.minecraft.cyan_wool": "青色羊毛", "block.minecraft.damaged_anvil": "耗損的鐵砧", "block.minecraft.dandelion": "蒲公英", "block.minecraft.dark_oak_button": "黑橡木按鈕", "block.minecraft.dark_oak_door": "黑橡木門", "block.minecraft.dark_oak_fence": "黑橡木柵欄", "block.minecraft.dark_oak_fence_gate": "黑橡木柵欄門", "block.minecraft.dark_oak_hanging_sign": "懸掛式黑橡木告示牌", "block.minecraft.dark_oak_leaves": "黑橡木樹葉", "block.minecraft.dark_oak_log": "黑橡木原木", "block.minecraft.dark_oak_planks": "黑橡木材", "block.minecraft.dark_oak_pressure_plate": "黑橡木壓力板", "block.minecraft.dark_oak_sapling": "黑橡木樹苗", "block.minecraft.dark_oak_sign": "黑橡木告示牌", "block.minecraft.dark_oak_slab": "黑橡木半磚", "block.minecraft.dark_oak_stairs": "黑橡木階梯", "block.minecraft.dark_oak_trapdoor": "黑橡木地板門", "block.minecraft.dark_oak_wall_hanging_sign": "牆上的懸掛式黑橡木告示牌", "block.minecraft.dark_oak_wall_sign": "牆上的黑橡木告示牌", "block.minecraft.dark_oak_wood": "黑橡木塊", "block.minecraft.dark_prismarine": "暗海磷石", "block.minecraft.dark_prismarine_slab": "暗海磷石半磚", "block.minecraft.dark_prismarine_stairs": "暗海磷石階梯", "block.minecraft.daylight_detector": "日光感測器", "block.minecraft.dead_brain_coral": "死亡的腦珊瑚", "block.minecraft.dead_brain_coral_block": "死亡的腦珊瑚方塊", "block.minecraft.dead_brain_coral_fan": "死亡的扇狀腦珊瑚", "block.minecraft.dead_brain_coral_wall_fan": "牆上死亡的扇狀腦珊瑚", "block.minecraft.dead_bubble_coral": "死亡的氣泡珊瑚", "block.minecraft.dead_bubble_coral_block": "死亡的氣泡珊瑚方塊", "block.minecraft.dead_bubble_coral_fan": "死亡的扇狀氣泡珊瑚", "block.minecraft.dead_bubble_coral_wall_fan": "牆上死亡的扇狀氣泡珊瑚", "block.minecraft.dead_bush": "枯灌木", "block.minecraft.dead_fire_coral": "死亡的火珊瑚", "block.minecraft.dead_fire_coral_block": "死亡的火珊瑚方塊", "block.minecraft.dead_fire_coral_fan": "死亡的扇狀火珊瑚", "block.minecraft.dead_fire_coral_wall_fan": "牆上死亡的扇狀火珊瑚", "block.minecraft.dead_horn_coral": "死亡的角珊瑚", "block.minecraft.dead_horn_coral_block": "死亡的角珊瑚方塊", "block.minecraft.dead_horn_coral_fan": "死亡的扇狀角珊瑚", "block.minecraft.dead_horn_coral_wall_fan": "牆上死亡的扇狀角珊瑚", "block.minecraft.dead_tube_coral": "死亡的管珊瑚", "block.minecraft.dead_tube_coral_block": "死亡的管珊瑚方塊", "block.minecraft.dead_tube_coral_fan": "死亡的扇狀管珊瑚", "block.minecraft.dead_tube_coral_wall_fan": "牆上死亡的扇狀管珊瑚", "block.minecraft.decorated_pot": "飾紋陶罐", "block.minecraft.deepslate": "深板岩", "block.minecraft.deepslate_brick_slab": "深板岩磚半磚", "block.minecraft.deepslate_brick_stairs": "深板岩磚階梯", "block.minecraft.deepslate_brick_wall": "深板岩磚牆", "block.minecraft.deepslate_bricks": "深板岩磚", "block.minecraft.deepslate_coal_ore": "深板岩煤礦", "block.minecraft.deepslate_copper_ore": "深板岩銅礦", "block.minecraft.deepslate_diamond_ore": "深板岩鑽石礦", "block.minecraft.deepslate_emerald_ore": "深板岩綠寶石礦", "block.minecraft.deepslate_gold_ore": "深板岩金礦", "block.minecraft.deepslate_iron_ore": "深板岩鐵礦", "block.minecraft.deepslate_lapis_ore": "深板岩青金石礦", "block.minecraft.deepslate_redstone_ore": "深板岩紅石礦", "block.minecraft.deepslate_tile_slab": "深板岩磚瓦半磚", "block.minecraft.deepslate_tile_stairs": "深板岩磚瓦階梯", "block.minecraft.deepslate_tile_wall": "深板岩磚瓦牆", "block.minecraft.deepslate_tiles": "深板岩磚瓦", "block.minecraft.detector_rail": "感測鐵軌", "block.minecraft.diamond_block": "鑽石方塊", "block.minecraft.diamond_ore": "鑽石礦", "block.minecraft.diorite": "閃長岩", "block.minecraft.diorite_slab": "閃長岩半磚", "block.minecraft.diorite_stairs": "閃長岩階梯", "block.minecraft.diorite_wall": "閃長岩牆", "block.minecraft.dirt": "泥土", "block.minecraft.dirt_path": "土徑", "block.minecraft.dispenser": "發射器", "block.minecraft.dragon_egg": "龍蛋", "block.minecraft.dragon_head": "龍首", "block.minecraft.dragon_wall_head": "牆上的龍首", "block.minecraft.dried_ghast": "乾癟幽靈", "block.minecraft.dried_kelp_block": "海帶乾塊", "block.minecraft.dripstone_block": "鐘乳石方塊", "block.minecraft.dropper": "投擲器", "block.minecraft.emerald_block": "綠寶石方塊", "block.minecraft.emerald_ore": "綠寶石礦", "block.minecraft.enchanting_table": "附魔台", "block.minecraft.end_gateway": "終界折躍門", "block.minecraft.end_portal": "終界傳送門", "block.minecraft.end_portal_frame": "終界傳送門框架", "block.minecraft.end_rod": "終界燭", "block.minecraft.end_stone": "終界石", "block.minecraft.end_stone_brick_slab": "終界石磚半磚", "block.minecraft.end_stone_brick_stairs": "終界石磚階梯", "block.minecraft.end_stone_brick_wall": "終界石磚牆", "block.minecraft.end_stone_bricks": "終界石磚", "block.minecraft.ender_chest": "終界箱", "block.minecraft.exposed_chiseled_copper": "斑駁的浮雕銅方塊", "block.minecraft.exposed_copper": "斑駁的銅方塊", "block.minecraft.exposed_copper_bulb": "斑駁的銅燈", "block.minecraft.exposed_copper_door": "斑駁的銅門", "block.minecraft.exposed_copper_grate": "斑駁的銅格柵", "block.minecraft.exposed_copper_trapdoor": "斑駁的銅地板門", "block.minecraft.exposed_cut_copper": "斑駁的切製銅方塊", "block.minecraft.exposed_cut_copper_slab": "斑駁的切製銅半磚", "block.minecraft.exposed_cut_copper_stairs": "斑駁的切製銅階梯", "block.minecraft.farmland": "耕地", "block.minecraft.fern": "蕨", "block.minecraft.fire": "火", "block.minecraft.fire_coral": "火珊瑚", "block.minecraft.fire_coral_block": "火珊瑚方塊", "block.minecraft.fire_coral_fan": "扇狀火珊瑚", "block.minecraft.fire_coral_wall_fan": "牆上的扇狀火珊瑚", "block.minecraft.firefly_bush": "螢火蟲灌木叢", "block.minecraft.fletching_table": "製箭台", "block.minecraft.flower_pot": "花盆", "block.minecraft.flowering_azalea": "開花的杜鵑叢", "block.minecraft.flowering_azalea_leaves": "開花的杜鵑葉", "block.minecraft.frogspawn": "青蛙卵", "block.minecraft.frosted_ice": "霜冰", "block.minecraft.furnace": "熔爐", "block.minecraft.gilded_blackstone": "鑲金黑石", "block.minecraft.glass": "玻璃", "block.minecraft.glass_pane": "玻璃片", "block.minecraft.glow_lichen": "發光地衣", "block.minecraft.glowstone": "螢光石", "block.minecraft.gold_block": "黃金方塊", "block.minecraft.gold_ore": "金礦", "block.minecraft.granite": "花崗岩", "block.minecraft.granite_slab": "花崗岩半磚", "block.minecraft.granite_stairs": "花崗岩階梯", "block.minecraft.granite_wall": "花崗岩牆", "block.minecraft.grass": "草", "block.minecraft.grass_block": "草地", "block.minecraft.gravel": "礫石", "block.minecraft.gray_banner": "灰色旗幟", "block.minecraft.gray_bed": "灰色床", "block.minecraft.gray_candle": "灰色蠟燭", "block.minecraft.gray_candle_cake": "插上灰色蠟燭的蛋糕", "block.minecraft.gray_carpet": "灰色地毯", "block.minecraft.gray_concrete": "灰色混凝土", "block.minecraft.gray_concrete_powder": "灰色混凝土粉末", "block.minecraft.gray_glazed_terracotta": "灰色釉陶", "block.minecraft.gray_shulker_box": "灰色界伏盒", "block.minecraft.gray_stained_glass": "灰色玻璃", "block.minecraft.gray_stained_glass_pane": "灰色玻璃片", "block.minecraft.gray_terracotta": "灰色陶土", "block.minecraft.gray_wool": "灰色羊毛", "block.minecraft.green_banner": "綠色旗幟", "block.minecraft.green_bed": "綠色床", "block.minecraft.green_candle": "綠色蠟燭", "block.minecraft.green_candle_cake": "插上綠色蠟燭的蛋糕", "block.minecraft.green_carpet": "綠色地毯", "block.minecraft.green_concrete": "綠色混凝土", "block.minecraft.green_concrete_powder": "綠色混凝土粉末", "block.minecraft.green_glazed_terracotta": "綠色釉陶", "block.minecraft.green_shulker_box": "綠色界伏盒", "block.minecraft.green_stained_glass": "綠色玻璃", "block.minecraft.green_stained_glass_pane": "綠色玻璃片", "block.minecraft.green_terracotta": "綠色陶土", "block.minecraft.green_wool": "綠色羊毛", "block.minecraft.grindstone": "砂輪", "block.minecraft.hanging_roots": "懸根", "block.minecraft.hay_block": "乾草捆", "block.minecraft.heavy_core": "沉重核心", "block.minecraft.heavy_weighted_pressure_plate": "重質測重壓力板", "block.minecraft.honey_block": "蜂蜜塊", "block.minecraft.honeycomb_block": "蜂巢塊", "block.minecraft.hopper": "漏斗", "block.minecraft.horn_coral": "角珊瑚", "block.minecraft.horn_coral_block": "角珊瑚方塊", "block.minecraft.horn_coral_fan": "扇狀角珊瑚", "block.minecraft.horn_coral_wall_fan": "牆上的扇狀角珊瑚", "block.minecraft.ice": "冰", "block.minecraft.infested_chiseled_stone_bricks": "蛀蝕的浮雕石磚", "block.minecraft.infested_cobblestone": "蛀蝕的鵝卵石", "block.minecraft.infested_cracked_stone_bricks": "蛀蝕的裂紋石磚", "block.minecraft.infested_deepslate": "蛀蝕的深板岩", "block.minecraft.infested_mossy_stone_bricks": "蛀蝕的青苔石磚", "block.minecraft.infested_stone": "蛀蝕的石頭", "block.minecraft.infested_stone_bricks": "蛀蝕的石磚", "block.minecraft.iron_bars": "鐵柵欄", "block.minecraft.iron_block": "鐵方塊", "block.minecraft.iron_door": "鐵門", "block.minecraft.iron_ore": "鐵礦", "block.minecraft.iron_trapdoor": "鐵地板門", "block.minecraft.jack_o_lantern": "南瓜燈", "block.minecraft.jigsaw": "拼圖方塊", "block.minecraft.jukebox": "唱片機", "block.minecraft.jungle_button": "叢林木按鈕", "block.minecraft.jungle_door": "叢林木門", "block.minecraft.jungle_fence": "叢林木柵欄", "block.minecraft.jungle_fence_gate": "叢林木柵欄門", "block.minecraft.jungle_hanging_sign": "懸掛式叢林木告示牌", "block.minecraft.jungle_leaves": "叢林木樹葉", "block.minecraft.jungle_log": "叢林木原木", "block.minecraft.jungle_planks": "叢林木材", "block.minecraft.jungle_pressure_plate": "叢林木壓力板", "block.minecraft.jungle_sapling": "叢林木樹苗", "block.minecraft.jungle_sign": "叢林木告示牌", "block.minecraft.jungle_slab": "叢林木半磚", "block.minecraft.jungle_stairs": "叢林木階梯", "block.minecraft.jungle_trapdoor": "叢林木地板門", "block.minecraft.jungle_wall_hanging_sign": "牆上的懸掛式叢林木告示牌", "block.minecraft.jungle_wall_sign": "牆上的叢林木告示牌", "block.minecraft.jungle_wood": "叢林木塊", "block.minecraft.kelp": "海帶", "block.minecraft.kelp_plant": "海帶植株", "block.minecraft.ladder": "梯子", "block.minecraft.lantern": "燈籠", "block.minecraft.lapis_block": "青金石方塊", "block.minecraft.lapis_ore": "青金石礦", "block.minecraft.large_amethyst_bud": "大型紫水晶芽", "block.minecraft.large_fern": "大型蕨類", "block.minecraft.lava": "熔岩", "block.minecraft.lava_cauldron": "裝熔岩的鍋釜", "block.minecraft.leaf_litter": "枯葉", "block.minecraft.lectern": "講台", "block.minecraft.lever": "控制桿", "block.minecraft.light": "光源", "block.minecraft.light_blue_banner": "淺藍色旗幟", "block.minecraft.light_blue_bed": "淺藍色床", "block.minecraft.light_blue_candle": "淺藍色蠟燭", "block.minecraft.light_blue_candle_cake": "插上淺藍色蠟燭的蛋糕", "block.minecraft.light_blue_carpet": "淺藍色地毯", "block.minecraft.light_blue_concrete": "淺藍色混凝土", "block.minecraft.light_blue_concrete_powder": "淺藍色混凝土粉末", "block.minecraft.light_blue_glazed_terracotta": "淺藍色釉陶", "block.minecraft.light_blue_shulker_box": "淺藍色界伏盒", "block.minecraft.light_blue_stained_glass": "淺藍色玻璃", "block.minecraft.light_blue_stained_glass_pane": "淺藍色玻璃片", "block.minecraft.light_blue_terracotta": "淺藍色陶土", "block.minecraft.light_blue_wool": "淺藍色羊毛", "block.minecraft.light_gray_banner": "淺灰色旗幟", "block.minecraft.light_gray_bed": "淺灰色床", "block.minecraft.light_gray_candle": "淺灰色蠟燭", "block.minecraft.light_gray_candle_cake": "插上淺灰色蠟燭的蛋糕", "block.minecraft.light_gray_carpet": "淺灰色地毯", "block.minecraft.light_gray_concrete": "淺灰色混凝土", "block.minecraft.light_gray_concrete_powder": "淺灰色混凝土粉末", "block.minecraft.light_gray_glazed_terracotta": "淺灰色釉陶", "block.minecraft.light_gray_shulker_box": "淺灰色界伏盒", "block.minecraft.light_gray_stained_glass": "淺灰色玻璃", "block.minecraft.light_gray_stained_glass_pane": "淺灰色玻璃片", "block.minecraft.light_gray_terracotta": "淺灰色陶土", "block.minecraft.light_gray_wool": "淺灰色羊毛", "block.minecraft.light_weighted_pressure_plate": "輕質測重壓力板", "block.minecraft.lightning_rod": "避雷針", "block.minecraft.lilac": "紫丁香", "block.minecraft.lily_of_the_valley": "鈴蘭", "block.minecraft.lily_pad": "荷葉", "block.minecraft.lime_banner": "淺綠色旗幟", "block.minecraft.lime_bed": "淺綠色床", "block.minecraft.lime_candle": "淺綠色蠟燭", "block.minecraft.lime_candle_cake": "插上淺綠色蠟燭的蛋糕", "block.minecraft.lime_carpet": "淺綠色地毯", "block.minecraft.lime_concrete": "淺綠色混凝土", "block.minecraft.lime_concrete_powder": "淺綠色混凝土粉末", "block.minecraft.lime_glazed_terracotta": "淺綠色釉陶", "block.minecraft.lime_shulker_box": "淺綠色界伏盒", "block.minecraft.lime_stained_glass": "淺綠色玻璃", "block.minecraft.lime_stained_glass_pane": "淺綠色玻璃片", "block.minecraft.lime_terracotta": "淺綠色陶土", "block.minecraft.lime_wool": "淺綠色羊毛", "block.minecraft.lodestone": "磁石", "block.minecraft.loom": "紡織機", "block.minecraft.magenta_banner": "洋紅色旗幟", "block.minecraft.magenta_bed": "洋紅色床", "block.minecraft.magenta_candle": "洋紅色蠟燭", "block.minecraft.magenta_candle_cake": "插上洋紅色蠟燭的蛋糕", "block.minecraft.magenta_carpet": "洋紅色地毯", "block.minecraft.magenta_concrete": "洋紅色混凝土", "block.minecraft.magenta_concrete_powder": "洋紅色混凝土粉末", "block.minecraft.magenta_glazed_terracotta": "洋紅色釉陶", "block.minecraft.magenta_shulker_box": "洋紅色界伏盒", "block.minecraft.magenta_stained_glass": "洋紅色玻璃", "block.minecraft.magenta_stained_glass_pane": "洋紅色玻璃片", "block.minecraft.magenta_terracotta": "洋紅色陶土", "block.minecraft.magenta_wool": "洋紅色羊毛", "block.minecraft.magma_block": "岩漿塊", "block.minecraft.mangrove_button": "紅樹林木按鈕", "block.minecraft.mangrove_door": "紅樹林木門", "block.minecraft.mangrove_fence": "紅樹林木柵欄", "block.minecraft.mangrove_fence_gate": "紅樹林木柵欄門", "block.minecraft.mangrove_hanging_sign": "懸掛式紅樹林木告示牌", "block.minecraft.mangrove_leaves": "紅樹林木樹葉", "block.minecraft.mangrove_log": "紅樹林木原木", "block.minecraft.mangrove_planks": "紅樹林木材", "block.minecraft.mangrove_pressure_plate": "紅樹林木壓力板", "block.minecraft.mangrove_propagule": "紅樹林木胎生苗", "block.minecraft.mangrove_roots": "紅樹林木根", "block.minecraft.mangrove_sign": "紅樹林木告示牌", "block.minecraft.mangrove_slab": "紅樹林木半磚", "block.minecraft.mangrove_stairs": "紅樹林木階梯", "block.minecraft.mangrove_trapdoor": "紅樹林木地板門", "block.minecraft.mangrove_wall_hanging_sign": "牆上的懸掛式紅樹林木告示牌", "block.minecraft.mangrove_wall_sign": "牆上的紅樹林木告示牌", "block.minecraft.mangrove_wood": "紅樹林木塊", "block.minecraft.medium_amethyst_bud": "中型紫水晶芽", "block.minecraft.melon": "西瓜", "block.minecraft.melon_stem": "西瓜梗", "block.minecraft.moss_block": "苔蘚方塊", "block.minecraft.moss_carpet": "覆地苔蘚", "block.minecraft.mossy_cobblestone": "青苔鵝卵石", "block.minecraft.mossy_cobblestone_slab": "青苔鵝卵石半磚", "block.minecraft.mossy_cobblestone_stairs": "青苔鵝卵石階梯", "block.minecraft.mossy_cobblestone_wall": "青苔鵝卵石牆", "block.minecraft.mossy_stone_brick_slab": "青苔石磚半磚", "block.minecraft.mossy_stone_brick_stairs": "青苔石磚階梯", "block.minecraft.mossy_stone_brick_wall": "青苔石磚牆", "block.minecraft.mossy_stone_bricks": "青苔石磚", "block.minecraft.moving_piston": "移動中的活塞", "block.minecraft.mud": "泥巴", "block.minecraft.mud_brick_slab": "泥磚半磚", "block.minecraft.mud_brick_stairs": "泥磚階梯", "block.minecraft.mud_brick_wall": "泥磚牆", "block.minecraft.mud_bricks": "泥磚", "block.minecraft.muddy_mangrove_roots": "淤泥紅樹林木根", "block.minecraft.mushroom_stem": "蘑菇柄", "block.minecraft.mycelium": "菌絲土", "block.minecraft.nether_brick_fence": "地獄磚柵欄", "block.minecraft.nether_brick_slab": "地獄磚半磚", "block.minecraft.nether_brick_stairs": "地獄磚階梯", "block.minecraft.nether_brick_wall": "地獄磚牆", "block.minecraft.nether_bricks": "地獄磚", "block.minecraft.nether_gold_ore": "地獄金礦", "block.minecraft.nether_portal": "地獄傳送門", "block.minecraft.nether_quartz_ore": "地獄石英礦", "block.minecraft.nether_sprouts": "地獄芽", "block.minecraft.nether_wart": "地獄疙瘩", "block.minecraft.nether_wart_block": "地獄疙瘩塊", "block.minecraft.netherite_block": "獄髓方塊", "block.minecraft.netherrack": "地獄石", "block.minecraft.note_block": "音階盒", "block.minecraft.oak_button": "橡木按鈕", "block.minecraft.oak_door": "橡木門", "block.minecraft.oak_fence": "橡木柵欄", "block.minecraft.oak_fence_gate": "橡木柵欄門", "block.minecraft.oak_hanging_sign": "懸掛式橡木告示牌", "block.minecraft.oak_leaves": "橡木樹葉", "block.minecraft.oak_log": "橡木原木", "block.minecraft.oak_planks": "橡木材", "block.minecraft.oak_pressure_plate": "橡木壓力板", "block.minecraft.oak_sapling": "橡木樹苗", "block.minecraft.oak_sign": "橡木告示牌", "block.minecraft.oak_slab": "橡木半磚", "block.minecraft.oak_stairs": "橡木階梯", "block.minecraft.oak_trapdoor": "橡木地板門", "block.minecraft.oak_wall_hanging_sign": "牆上的懸掛式橡木告示牌", "block.minecraft.oak_wall_sign": "牆上的橡木告示牌", "block.minecraft.oak_wood": "橡木塊", "block.minecraft.observer": "偵測器", "block.minecraft.obsidian": "黑曜石", "block.minecraft.ochre_froglight": "赭黃蛙光體", "block.minecraft.ominous_banner": "不祥旗幟", "block.minecraft.open_eyeblossom": "張開的擬目花", "block.minecraft.orange_banner": "橙色旗幟", "block.minecraft.orange_bed": "橙色床", "block.minecraft.orange_candle": "橙色蠟燭", "block.minecraft.orange_candle_cake": "插上橙色蠟燭的蛋糕", "block.minecraft.orange_carpet": "橙色地毯", "block.minecraft.orange_concrete": "橙色混凝土", "block.minecraft.orange_concrete_powder": "橙色混凝土粉末", "block.minecraft.orange_glazed_terracotta": "橙色釉陶", "block.minecraft.orange_shulker_box": "橙色界伏盒", "block.minecraft.orange_stained_glass": "橙色玻璃", "block.minecraft.orange_stained_glass_pane": "橙色玻璃片", "block.minecraft.orange_terracotta": "橙色陶土", "block.minecraft.orange_tulip": "橙色鬱金香", "block.minecraft.orange_wool": "橙色羊毛", "block.minecraft.oxeye_daisy": "雛菊", "block.minecraft.oxidized_chiseled_copper": "氧化的浮雕銅方塊", "block.minecraft.oxidized_copper": "氧化的銅方塊", "block.minecraft.oxidized_copper_bulb": "氧化的銅燈", "block.minecraft.oxidized_copper_door": "氧化的銅門", "block.minecraft.oxidized_copper_grate": "氧化的銅格柵", "block.minecraft.oxidized_copper_trapdoor": "氧化的銅地板門", "block.minecraft.oxidized_cut_copper": "氧化的切製銅方塊", "block.minecraft.oxidized_cut_copper_slab": "氧化的切製銅半磚", "block.minecraft.oxidized_cut_copper_stairs": "氧化的切製銅階梯", "block.minecraft.packed_ice": "冰磚", "block.minecraft.packed_mud": "泥坯", "block.minecraft.pale_hanging_moss": "蒼白垂絲", "block.minecraft.pale_moss_block": "蒼白苔蘚方塊", "block.minecraft.pale_moss_carpet": "蒼白覆地苔蘚", "block.minecraft.pale_oak_button": "蒼白橡木按鈕", "block.minecraft.pale_oak_door": "蒼白橡木門", "block.minecraft.pale_oak_fence": "蒼白橡木柵欄", "block.minecraft.pale_oak_fence_gate": "蒼白橡木柵欄門", "block.minecraft.pale_oak_hanging_sign": "懸掛式蒼白橡木告示牌", "block.minecraft.pale_oak_leaves": "蒼白橡木樹葉", "block.minecraft.pale_oak_log": "蒼白橡木原木", "block.minecraft.pale_oak_planks": "蒼白橡木材", "block.minecraft.pale_oak_pressure_plate": "蒼白橡木壓力板", "block.minecraft.pale_oak_sapling": "蒼白橡木樹苗", "block.minecraft.pale_oak_sign": "蒼白橡木告示牌", "block.minecraft.pale_oak_slab": "蒼白橡木半磚", "block.minecraft.pale_oak_stairs": "蒼白橡木階梯", "block.minecraft.pale_oak_trapdoor": "蒼白橡木地板門", "block.minecraft.pale_oak_wall_hanging_sign": "牆上的懸掛式蒼白橡木告示牌", "block.minecraft.pale_oak_wall_sign": "牆上的蒼白橡木告示牌", "block.minecraft.pale_oak_wood": "蒼白橡木塊", "block.minecraft.pearlescent_froglight": "珠紫蛙光體", "block.minecraft.peony": "牡丹花", "block.minecraft.petrified_oak_slab": "石化橡木半磚", "block.minecraft.piglin_head": "豬布林頭顱", "block.minecraft.piglin_wall_head": "牆上的豬布林頭顱", "block.minecraft.pink_banner": "粉紅色旗幟", "block.minecraft.pink_bed": "粉紅色床", "block.minecraft.pink_candle": "粉紅色蠟燭", "block.minecraft.pink_candle_cake": "插上粉紅色蠟燭的蛋糕", "block.minecraft.pink_carpet": "粉紅色地毯", "block.minecraft.pink_concrete": "粉紅色混凝土", "block.minecraft.pink_concrete_powder": "粉紅色混凝土粉末", "block.minecraft.pink_glazed_terracotta": "粉紅色釉陶", "block.minecraft.pink_petals": "粉瓣花", "block.minecraft.pink_shulker_box": "粉紅色界伏盒", "block.minecraft.pink_stained_glass": "粉紅色玻璃", "block.minecraft.pink_stained_glass_pane": "粉紅色玻璃片", "block.minecraft.pink_terracotta": "粉紅色陶土", "block.minecraft.pink_tulip": "粉紅色鬱金香", "block.minecraft.pink_wool": "粉紅色羊毛", "block.minecraft.piston": "活塞", "block.minecraft.piston_head": "活塞頭", "block.minecraft.pitcher_crop": "瓶子草植株", "block.minecraft.pitcher_plant": "瓶子草", "block.minecraft.player_head": "玩家頭顱", "block.minecraft.player_head.named": "%s 的頭顱", "block.minecraft.player_wall_head": "牆上的玩家頭顱", "block.minecraft.podzol": "灰壤", "block.minecraft.pointed_dripstone": "鐘乳石", "block.minecraft.polished_andesite": "拋光安山岩", "block.minecraft.polished_andesite_slab": "拋光安山岩半磚", "block.minecraft.polished_andesite_stairs": "拋光安山岩階梯", "block.minecraft.polished_basalt": "拋光玄武岩", "block.minecraft.polished_blackstone": "拋光黑石", "block.minecraft.polished_blackstone_brick_slab": "拋光黑石磚半磚", "block.minecraft.polished_blackstone_brick_stairs": "拋光黑石磚階梯", "block.minecraft.polished_blackstone_brick_wall": "拋光黑石磚牆", "block.minecraft.polished_blackstone_bricks": "拋光黑石磚", "block.minecraft.polished_blackstone_button": "拋光黑石按鈕", "block.minecraft.polished_blackstone_pressure_plate": "拋光黑石壓力板", "block.minecraft.polished_blackstone_slab": "拋光黑石半磚", "block.minecraft.polished_blackstone_stairs": "拋光黑石階梯", "block.minecraft.polished_blackstone_wall": "拋光黑石牆", "block.minecraft.polished_deepslate": "拋光深板岩", "block.minecraft.polished_deepslate_slab": "拋光深板岩半磚", "block.minecraft.polished_deepslate_stairs": "拋光深板岩階梯", "block.minecraft.polished_deepslate_wall": "拋光深板岩牆", "block.minecraft.polished_diorite": "拋光閃長岩", "block.minecraft.polished_diorite_slab": "拋光閃長岩半磚", "block.minecraft.polished_diorite_stairs": "拋光閃長岩階梯", "block.minecraft.polished_granite": "拋光花崗岩", "block.minecraft.polished_granite_slab": "拋光花崗岩半磚", "block.minecraft.polished_granite_stairs": "拋光花崗岩階梯", "block.minecraft.polished_tuff": "拋光凝灰岩", "block.minecraft.polished_tuff_slab": "拋光凝灰岩半磚", "block.minecraft.polished_tuff_stairs": "拋光凝灰岩階梯", "block.minecraft.polished_tuff_wall": "拋光凝灰岩牆", "block.minecraft.poppy": "罌粟", "block.minecraft.potatoes": "馬鈴薯", "block.minecraft.potted_acacia_sapling": "相思木樹苗盆栽", "block.minecraft.potted_allium": "紫紅球花盆栽", "block.minecraft.potted_azalea_bush": "杜鵑叢盆栽", "block.minecraft.potted_azure_bluet": "雛草盆栽", "block.minecraft.potted_bamboo": "竹子盆栽", "block.minecraft.potted_birch_sapling": "樺木樹苗盆栽", "block.minecraft.potted_blue_orchid": "藍色蝴蝶蘭盆栽", "block.minecraft.potted_brown_mushroom": "棕色蘑菇盆栽", "block.minecraft.potted_cactus": "仙人掌盆栽", "block.minecraft.potted_cherry_sapling": "櫻花木樹苗盆栽", "block.minecraft.potted_closed_eyeblossom": "閉合的擬目花盆栽", "block.minecraft.potted_cornflower": "矢車菊盆栽", "block.minecraft.potted_crimson_fungus": "緋紅蕈菇盆栽", "block.minecraft.potted_crimson_roots": "緋紅蕈根盆栽", "block.minecraft.potted_dandelion": "蒲公英盆栽", "block.minecraft.potted_dark_oak_sapling": "黑橡木樹苗盆栽", "block.minecraft.potted_dead_bush": "枯灌木盆栽", "block.minecraft.potted_fern": "蕨類盆栽", "block.minecraft.potted_flowering_azalea_bush": "開花的杜鵑叢盆栽", "block.minecraft.potted_jungle_sapling": "叢林木樹苗盆栽", "block.minecraft.potted_lily_of_the_valley": "鈴蘭盆栽", "block.minecraft.potted_mangrove_propagule": "紅樹林木胎生苗盆栽", "block.minecraft.potted_oak_sapling": "橡木樹苗盆栽", "block.minecraft.potted_open_eyeblossom": "張開的擬目花盆栽", "block.minecraft.potted_orange_tulip": "橙色鬱金香盆栽", "block.minecraft.potted_oxeye_daisy": "雛菊盆栽", "block.minecraft.potted_pale_oak_sapling": "蒼白橡木樹苗盆栽", "block.minecraft.potted_pink_tulip": "粉紅色鬱金香盆栽", "block.minecraft.potted_poppy": "罌粟盆栽", "block.minecraft.potted_red_mushroom": "紅色蘑菇盆栽", "block.minecraft.potted_red_tulip": "紅色鬱金香盆栽", "block.minecraft.potted_spruce_sapling": "杉木樹苗盆栽", "block.minecraft.potted_torchflower": "火把花盆栽", "block.minecraft.potted_warped_fungus": "扭曲蕈菇盆栽", "block.minecraft.potted_warped_roots": "扭曲蕈根盆栽", "block.minecraft.potted_white_tulip": "白色鬱金香盆栽", "block.minecraft.potted_wither_rose": "凋零玫瑰盆栽", "block.minecraft.powder_snow": "粉雪", "block.minecraft.powder_snow_cauldron": "裝粉雪的鍋釜", "block.minecraft.powered_rail": "動力鐵軌", "block.minecraft.prismarine": "海磷石", "block.minecraft.prismarine_brick_slab": "海磷石磚半磚", "block.minecraft.prismarine_brick_stairs": "海磷石磚階梯", "block.minecraft.prismarine_bricks": "海磷石磚", "block.minecraft.prismarine_slab": "海磷石半磚", "block.minecraft.prismarine_stairs": "海磷石階梯", "block.minecraft.prismarine_wall": "海磷石牆", "block.minecraft.pumpkin": "南瓜", "block.minecraft.pumpkin_stem": "南瓜梗", "block.minecraft.purple_banner": "紫色旗幟", "block.minecraft.purple_bed": "紫色床", "block.minecraft.purple_candle": "紫色蠟燭", "block.minecraft.purple_candle_cake": "插上紫色蠟燭的蛋糕", "block.minecraft.purple_carpet": "紫色地毯", "block.minecraft.purple_concrete": "紫色混凝土", "block.minecraft.purple_concrete_powder": "紫色混凝土粉末", "block.minecraft.purple_glazed_terracotta": "紫色釉陶", "block.minecraft.purple_shulker_box": "紫色界伏盒", "block.minecraft.purple_stained_glass": "紫色玻璃", "block.minecraft.purple_stained_glass_pane": "紫色玻璃片", "block.minecraft.purple_terracotta": "紫色陶土", "block.minecraft.purple_wool": "紫色羊毛", "block.minecraft.purpur_block": "紫珀方塊", "block.minecraft.purpur_pillar": "紫珀柱", "block.minecraft.purpur_slab": "紫珀半磚", "block.minecraft.purpur_stairs": "紫珀階梯", "block.minecraft.quartz_block": "石英方塊", "block.minecraft.quartz_bricks": "石英磚", "block.minecraft.quartz_pillar": "石英柱", "block.minecraft.quartz_slab": "石英半磚", "block.minecraft.quartz_stairs": "石英階梯", "block.minecraft.rail": "鐵軌", "block.minecraft.raw_copper_block": "銅原礦方塊", "block.minecraft.raw_gold_block": "金原礦方塊", "block.minecraft.raw_iron_block": "鐵原礦方塊", "block.minecraft.red_banner": "紅色旗幟", "block.minecraft.red_bed": "紅色床", "block.minecraft.red_candle": "紅色蠟燭", "block.minecraft.red_candle_cake": "插上紅色蠟燭的蛋糕", "block.minecraft.red_carpet": "紅色地毯", "block.minecraft.red_concrete": "紅色混凝土", "block.minecraft.red_concrete_powder": "紅色混凝土粉末", "block.minecraft.red_glazed_terracotta": "紅色釉陶", "block.minecraft.red_mushroom": "紅色蘑菇", "block.minecraft.red_mushroom_block": "紅色蘑菇方塊", "block.minecraft.red_nether_brick_slab": "紅地獄磚半磚", "block.minecraft.red_nether_brick_stairs": "紅地獄磚階梯", "block.minecraft.red_nether_brick_wall": "紅地獄磚牆", "block.minecraft.red_nether_bricks": "紅地獄磚", "block.minecraft.red_sand": "紅沙", "block.minecraft.red_sandstone": "紅砂岩", "block.minecraft.red_sandstone_slab": "紅砂岩半磚", "block.minecraft.red_sandstone_stairs": "紅砂岩階梯", "block.minecraft.red_sandstone_wall": "紅砂岩牆", "block.minecraft.red_shulker_box": "紅色界伏盒", "block.minecraft.red_stained_glass": "紅色玻璃", "block.minecraft.red_stained_glass_pane": "紅色玻璃片", "block.minecraft.red_terracotta": "紅色陶土", "block.minecraft.red_tulip": "紅色鬱金香", "block.minecraft.red_wool": "紅色羊毛", "block.minecraft.redstone_block": "紅石方塊", "block.minecraft.redstone_lamp": "紅石燈", "block.minecraft.redstone_ore": "紅石礦", "block.minecraft.redstone_torch": "紅石火把", "block.minecraft.redstone_wall_torch": "牆上的紅石火把", "block.minecraft.redstone_wire": "紅石線", "block.minecraft.reinforced_deepslate": "強化深板岩", "block.minecraft.repeater": "紅石中繼器", "block.minecraft.repeating_command_block": "重複型指令方塊", "block.minecraft.resin_block": "樹脂方塊", "block.minecraft.resin_brick_slab": "樹脂磚半磚", "block.minecraft.resin_brick_stairs": "樹脂磚階梯", "block.minecraft.resin_brick_wall": "樹脂磚牆", "block.minecraft.resin_bricks": "樹脂磚", "block.minecraft.resin_clump": "樹脂團", "block.minecraft.respawn_anchor": "重生錨", "block.minecraft.rooted_dirt": "扎根土", "block.minecraft.rose_bush": "玫瑰叢", "block.minecraft.sand": "沙", "block.minecraft.sandstone": "砂岩", "block.minecraft.sandstone_slab": "砂岩半磚", "block.minecraft.sandstone_stairs": "砂岩階梯", "block.minecraft.sandstone_wall": "砂岩牆", "block.minecraft.scaffolding": "鷹架", "block.minecraft.sculk": "伏聆", "block.minecraft.sculk_catalyst": "伏聆觸媒", "block.minecraft.sculk_sensor": "伏聆振測器", "block.minecraft.sculk_shrieker": "伏聆嘯口", "block.minecraft.sculk_vein": "伏聆脈絡", "block.minecraft.sea_lantern": "海燈籠", "block.minecraft.sea_pickle": "海鞘", "block.minecraft.seagrass": "海草", "block.minecraft.set_spawn": "已設定重生點", "block.minecraft.short_dry_grass": "矮枯草", "block.minecraft.short_grass": "矮草", "block.minecraft.shroomlight": "蕈光體", "block.minecraft.shulker_box": "界伏盒", "block.minecraft.skeleton_skull": "骷髏頭顱", "block.minecraft.skeleton_wall_skull": "牆上的骷髏頭顱", "block.minecraft.slime_block": "史萊姆方塊", "block.minecraft.small_amethyst_bud": "小型紫水晶芽", "block.minecraft.small_dripleaf": "小懸葉草", "block.minecraft.smithing_table": "鍛造台", "block.minecraft.smoker": "煙燻爐", "block.minecraft.smooth_basalt": "平滑玄武岩", "block.minecraft.smooth_quartz": "平滑石英方塊", "block.minecraft.smooth_quartz_slab": "平滑石英半磚", "block.minecraft.smooth_quartz_stairs": "平滑石英階梯", "block.minecraft.smooth_red_sandstone": "平滑紅砂岩", "block.minecraft.smooth_red_sandstone_slab": "平滑紅砂岩半磚", "block.minecraft.smooth_red_sandstone_stairs": "平滑紅砂岩階梯", "block.minecraft.smooth_sandstone": "平滑砂岩", "block.minecraft.smooth_sandstone_slab": "平滑砂岩半磚", "block.minecraft.smooth_sandstone_stairs": "平滑砂岩階梯", "block.minecraft.smooth_stone": "平滑石頭", "block.minecraft.smooth_stone_slab": "平滑石半磚", "block.minecraft.sniffer_egg": "嗅探獸蛋", "block.minecraft.snow": "雪", "block.minecraft.snow_block": "雪塊", "block.minecraft.soul_campfire": "靈魂營火", "block.minecraft.soul_fire": "靈魂火", "block.minecraft.soul_lantern": "靈魂燈籠", "block.minecraft.soul_sand": "靈魂砂", "block.minecraft.soul_soil": "靈魂土", "block.minecraft.soul_torch": "靈魂火把", "block.minecraft.soul_wall_torch": "牆上的靈魂火把", "block.minecraft.spawn.not_valid": "你沒有床或已充能的重生錨，或是它受到阻擋", "block.minecraft.spawner": "生怪磚", "block.minecraft.spawner.desc1": "用生怪蛋互動時：", "block.minecraft.spawner.desc2": "設定生物種類", "block.minecraft.sponge": "海綿", "block.minecraft.spore_blossom": "孢子花", "block.minecraft.spruce_button": "杉木按鈕", "block.minecraft.spruce_door": "杉木門", "block.minecraft.spruce_fence": "杉木柵欄", "block.minecraft.spruce_fence_gate": "杉木柵欄門", "block.minecraft.spruce_hanging_sign": "懸掛式杉木告示牌", "block.minecraft.spruce_leaves": "杉木樹葉", "block.minecraft.spruce_log": "杉木原木", "block.minecraft.spruce_planks": "杉木材", "block.minecraft.spruce_pressure_plate": "杉木壓力板", "block.minecraft.spruce_sapling": "杉木樹苗", "block.minecraft.spruce_sign": "杉木告示牌", "block.minecraft.spruce_slab": "杉木半磚", "block.minecraft.spruce_stairs": "杉木階梯", "block.minecraft.spruce_trapdoor": "杉木地板門", "block.minecraft.spruce_wall_hanging_sign": "牆上的懸掛式杉木告示牌", "block.minecraft.spruce_wall_sign": "牆上的杉木告示牌", "block.minecraft.spruce_wood": "杉木塊", "block.minecraft.sticky_piston": "黏性活塞", "block.minecraft.stone": "石頭", "block.minecraft.stone_brick_slab": "石磚半磚", "block.minecraft.stone_brick_stairs": "石磚階梯", "block.minecraft.stone_brick_wall": "石磚牆", "block.minecraft.stone_bricks": "石磚", "block.minecraft.stone_button": "石製按鈕", "block.minecraft.stone_pressure_plate": "石製壓力板", "block.minecraft.stone_slab": "石半磚", "block.minecraft.stone_stairs": "石頭階梯", "block.minecraft.stonecutter": "切石機", "block.minecraft.stripped_acacia_log": "剝皮相思木原木", "block.minecraft.stripped_acacia_wood": "剝皮相思木塊", "block.minecraft.stripped_bamboo_block": "剝皮竹方塊", "block.minecraft.stripped_birch_log": "剝皮樺木原木", "block.minecraft.stripped_birch_wood": "剝皮樺木塊", "block.minecraft.stripped_cherry_log": "剝皮櫻花木原木", "block.minecraft.stripped_cherry_wood": "剝皮櫻花木塊", "block.minecraft.stripped_crimson_hyphae": "剝皮緋紅菌絲體", "block.minecraft.stripped_crimson_stem": "剝皮緋紅蕈柄", "block.minecraft.stripped_dark_oak_log": "剝皮黑橡木原木", "block.minecraft.stripped_dark_oak_wood": "剝皮黑橡木塊", "block.minecraft.stripped_jungle_log": "剝皮叢林木原木", "block.minecraft.stripped_jungle_wood": "剝皮叢林木塊", "block.minecraft.stripped_mangrove_log": "剝皮紅樹林木原木", "block.minecraft.stripped_mangrove_wood": "剝皮紅樹林木塊", "block.minecraft.stripped_oak_log": "剝皮橡木原木", "block.minecraft.stripped_oak_wood": "剝皮橡木塊", "block.minecraft.stripped_pale_oak_log": "剝皮蒼白橡木原木", "block.minecraft.stripped_pale_oak_wood": "剝皮蒼白橡木塊", "block.minecraft.stripped_spruce_log": "剝皮杉木原木", "block.minecraft.stripped_spruce_wood": "剝皮杉木塊", "block.minecraft.stripped_warped_hyphae": "剝皮扭曲菌絲體", "block.minecraft.stripped_warped_stem": "剝皮扭曲蕈柄", "block.minecraft.structure_block": "結構方塊", "block.minecraft.structure_void": "結構空位", "block.minecraft.sugar_cane": "甘蔗", "block.minecraft.sunflower": "向日葵", "block.minecraft.suspicious_gravel": "可疑的礫石", "block.minecraft.suspicious_sand": "可疑的沙", "block.minecraft.sweet_berry_bush": "甜莓灌木叢", "block.minecraft.tall_dry_grass": "高枯草", "block.minecraft.tall_grass": "芒草", "block.minecraft.tall_seagrass": "高海草", "block.minecraft.target": "標靶", "block.minecraft.terracotta": "陶土", "block.minecraft.test_block": "測試方塊", "block.minecraft.test_instance_block": "測試實例方塊", "block.minecraft.tinted_glass": "遮光玻璃", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT 爆炸已停用", "block.minecraft.torch": "火把", "block.minecraft.torchflower": "火把花", "block.minecraft.torchflower_crop": "火把花植株", "block.minecraft.trapped_chest": "陷阱儲物箱", "block.minecraft.trial_spawner": "試煉生怪磚", "block.minecraft.tripwire": "絆線", "block.minecraft.tripwire_hook": "絆線鉤", "block.minecraft.tube_coral": "管珊瑚", "block.minecraft.tube_coral_block": "管珊瑚方塊", "block.minecraft.tube_coral_fan": "扇狀管珊瑚", "block.minecraft.tube_coral_wall_fan": "牆上的扇狀管珊瑚", "block.minecraft.tuff": "凝灰岩", "block.minecraft.tuff_brick_slab": "凝灰岩磚半磚", "block.minecraft.tuff_brick_stairs": "凝灰岩磚階梯", "block.minecraft.tuff_brick_wall": "凝灰岩磚牆", "block.minecraft.tuff_bricks": "凝灰岩磚", "block.minecraft.tuff_slab": "凝灰岩半磚", "block.minecraft.tuff_stairs": "凝灰岩階梯", "block.minecraft.tuff_wall": "凝灰岩牆", "block.minecraft.turtle_egg": "海龜蛋", "block.minecraft.twisting_vines": "扭曲藤", "block.minecraft.twisting_vines_plant": "扭曲藤植株", "block.minecraft.vault": "寶庫", "block.minecraft.verdant_froglight": "蒼翠蛙光體", "block.minecraft.vine": "藤蔓", "block.minecraft.void_air": "虛空空氣", "block.minecraft.wall_torch": "牆上的火把", "block.minecraft.warped_button": "扭曲蕈木按鈕", "block.minecraft.warped_door": "扭曲蕈木門", "block.minecraft.warped_fence": "扭曲蕈木柵欄", "block.minecraft.warped_fence_gate": "扭曲蕈木柵欄門", "block.minecraft.warped_fungus": "扭曲蕈菇", "block.minecraft.warped_hanging_sign": "懸掛式扭曲蕈木告示牌", "block.minecraft.warped_hyphae": "扭曲菌絲體", "block.minecraft.warped_nylium": "扭曲菌絲石", "block.minecraft.warped_planks": "扭曲蕈木材", "block.minecraft.warped_pressure_plate": "扭曲蕈木壓力板", "block.minecraft.warped_roots": "扭曲蕈根", "block.minecraft.warped_sign": "扭曲蕈木告示牌", "block.minecraft.warped_slab": "扭曲蕈木半磚", "block.minecraft.warped_stairs": "扭曲蕈木階梯", "block.minecraft.warped_stem": "扭曲蕈柄", "block.minecraft.warped_trapdoor": "扭曲蕈木地板門", "block.minecraft.warped_wall_hanging_sign": "牆上的懸掛式扭曲蕈木告示牌", "block.minecraft.warped_wall_sign": "牆上的扭曲蕈木告示牌", "block.minecraft.warped_wart_block": "扭曲疙瘩塊", "block.minecraft.water": "水", "block.minecraft.water_cauldron": "裝水的鍋釜", "block.minecraft.waxed_chiseled_copper": "上蠟的浮雕銅方塊", "block.minecraft.waxed_copper_block": "上蠟的銅方塊", "block.minecraft.waxed_copper_bulb": "上蠟的銅燈", "block.minecraft.waxed_copper_door": "上蠟的銅門", "block.minecraft.waxed_copper_grate": "上蠟的銅格柵", "block.minecraft.waxed_copper_trapdoor": "上蠟的銅地板門", "block.minecraft.waxed_cut_copper": "上蠟的切製銅方塊", "block.minecraft.waxed_cut_copper_slab": "上蠟的切製銅半磚", "block.minecraft.waxed_cut_copper_stairs": "上蠟的切製銅階梯", "block.minecraft.waxed_exposed_chiseled_copper": "上蠟的斑駁浮雕銅方塊", "block.minecraft.waxed_exposed_copper": "上蠟的斑駁銅方塊", "block.minecraft.waxed_exposed_copper_bulb": "上蠟的斑駁銅燈", "block.minecraft.waxed_exposed_copper_door": "上蠟的斑駁銅門", "block.minecraft.waxed_exposed_copper_grate": "上蠟的斑駁銅格柵", "block.minecraft.waxed_exposed_copper_trapdoor": "上蠟的斑駁銅地板門", "block.minecraft.waxed_exposed_cut_copper": "上蠟的斑駁切製銅方塊", "block.minecraft.waxed_exposed_cut_copper_slab": "上蠟的斑駁切製銅半磚", "block.minecraft.waxed_exposed_cut_copper_stairs": "上蠟的斑駁切製銅階梯", "block.minecraft.waxed_oxidized_chiseled_copper": "上蠟的氧化浮雕銅方塊", "block.minecraft.waxed_oxidized_copper": "上蠟的氧化銅方塊", "block.minecraft.waxed_oxidized_copper_bulb": "上蠟的氧化銅燈", "block.minecraft.waxed_oxidized_copper_door": "上蠟的氧化銅門", "block.minecraft.waxed_oxidized_copper_grate": "上蠟的氧化銅格柵", "block.minecraft.waxed_oxidized_copper_trapdoor": "上蠟的氧化銅地板門", "block.minecraft.waxed_oxidized_cut_copper": "上蠟的氧化切製銅方塊", "block.minecraft.waxed_oxidized_cut_copper_slab": "上蠟的氧化切製銅半磚", "block.minecraft.waxed_oxidized_cut_copper_stairs": "上蠟的氧化切製銅階梯", "block.minecraft.waxed_weathered_chiseled_copper": "上蠟的風化浮雕銅方塊", "block.minecraft.waxed_weathered_copper": "上蠟的風化銅方塊", "block.minecraft.waxed_weathered_copper_bulb": "上蠟的風化銅燈", "block.minecraft.waxed_weathered_copper_door": "上蠟的風化銅門", "block.minecraft.waxed_weathered_copper_grate": "上蠟的風化銅格柵", "block.minecraft.waxed_weathered_copper_trapdoor": "上蠟的風化銅地板門", "block.minecraft.waxed_weathered_cut_copper": "上蠟的風化切製銅方塊", "block.minecraft.waxed_weathered_cut_copper_slab": "上蠟的風化切製銅半磚", "block.minecraft.waxed_weathered_cut_copper_stairs": "上蠟的風化切製銅階梯", "block.minecraft.weathered_chiseled_copper": "風化的浮雕銅方塊", "block.minecraft.weathered_copper": "風化的銅方塊", "block.minecraft.weathered_copper_bulb": "風化的銅燈", "block.minecraft.weathered_copper_door": "風化的銅門", "block.minecraft.weathered_copper_grate": "風化的銅格柵", "block.minecraft.weathered_copper_trapdoor": "風化的銅地板門", "block.minecraft.weathered_cut_copper": "風化的切製銅方塊", "block.minecraft.weathered_cut_copper_slab": "風化的切製銅半磚", "block.minecraft.weathered_cut_copper_stairs": "風化的切製銅階梯", "block.minecraft.weeping_vines": "垂泣藤", "block.minecraft.weeping_vines_plant": "垂泣藤植株", "block.minecraft.wet_sponge": "濕海綿", "block.minecraft.wheat": "小麥植株", "block.minecraft.white_banner": "白色旗幟", "block.minecraft.white_bed": "白色床", "block.minecraft.white_candle": "白色蠟燭", "block.minecraft.white_candle_cake": "插上白色蠟燭的蛋糕", "block.minecraft.white_carpet": "白色地毯", "block.minecraft.white_concrete": "白色混凝土", "block.minecraft.white_concrete_powder": "白色混凝土粉末", "block.minecraft.white_glazed_terracotta": "白色釉陶", "block.minecraft.white_shulker_box": "白色界伏盒", "block.minecraft.white_stained_glass": "白色玻璃", "block.minecraft.white_stained_glass_pane": "白色玻璃片", "block.minecraft.white_terracotta": "白色陶土", "block.minecraft.white_tulip": "白色鬱金香", "block.minecraft.white_wool": "白色羊毛", "block.minecraft.wildflowers": "野花簇", "block.minecraft.wither_rose": "凋零玫瑰", "block.minecraft.wither_skeleton_skull": "凋零骷髏頭顱", "block.minecraft.wither_skeleton_wall_skull": "牆上的凋零骷髏頭顱", "block.minecraft.yellow_banner": "黃色旗幟", "block.minecraft.yellow_bed": "黃色床", "block.minecraft.yellow_candle": "黃色蠟燭", "block.minecraft.yellow_candle_cake": "插上黃色蠟燭的蛋糕", "block.minecraft.yellow_carpet": "黃色地毯", "block.minecraft.yellow_concrete": "黃色混凝土", "block.minecraft.yellow_concrete_powder": "黃色混凝土粉末", "block.minecraft.yellow_glazed_terracotta": "黃色釉陶", "block.minecraft.yellow_shulker_box": "黃色界伏盒", "block.minecraft.yellow_stained_glass": "黃色玻璃", "block.minecraft.yellow_stained_glass_pane": "黃色玻璃片", "block.minecraft.yellow_terracotta": "黃色陶土", "block.minecraft.yellow_wool": "黃色羊毛", "block.minecraft.zombie_head": "殭屍頭顱", "block.minecraft.zombie_wall_head": "牆上的殭屍頭顱", "book.byAuthor": "作者 %1$s", "book.edit.title": "書本編輯畫面", "book.editTitle": "輸入書名：", "book.finalizeButton": "署名並完成", "book.finalizeWarning": "注意！當您在這本書署名後，將無法再修改書中內容。", "book.generation.0": "原始版本", "book.generation.1": "複本", "book.generation.2": "複本的複本", "book.generation.3": "破舊的", "book.invalid.tag": "* 書本標籤無效 *", "book.pageIndicator": "第 %1$s 頁/共 %2$s 頁", "book.page_button.next": "下一頁", "book.page_button.previous": "上一頁", "book.sign.title": "書本署名畫面", "book.sign.titlebox": "標題", "book.signButton": "署名", "book.view.title": "書本檢視畫面", "build.tooHigh": "最大建築高度限制為 %s", "chat.cannotSend": "無法傳送聊天訊息", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "按此傳送", "chat.copy": "複製到剪貼簿", "chat.copy.click": "按此複製到剪貼簿", "chat.deleted_marker": "這則訊息已被伺服器刪除。", "chat.disabled.chain_broken": "由於訊息鏈中斷，聊天已被停用。請嘗試重新連線。", "chat.disabled.expiredProfileKey": "由於個人資訊公鑰過期，已停用聊天功能。請嘗試重新連線。", "chat.disabled.invalid_command_signature": "指令包含非預期的或是遺漏的指令引數簽章。", "chat.disabled.invalid_signature": "聊天簽章無效。請嘗試重新連線。", "chat.disabled.launcher": "啟動器選項已停用聊天功能。無法傳送訊息。", "chat.disabled.missingProfileKey": "由於個人資訊公鑰遺失，已停用聊天功能。請嘗試重新連線。", "chat.disabled.options": "用戶端選項已停用聊天功能。", "chat.disabled.out_of_order_chat": "收到的聊天訊息順序不正確。您的系統時間是否已變更？", "chat.disabled.profile": "聊天功能受到帳號設定限制。請重新按下「%s」以取得更多資訊。", "chat.disabled.profile.moreInfo": "聊天功能受到帳號設定限制。無法傳送或查看訊息。", "chat.editBox": "聊天欄", "chat.filtered": "被伺服器過濾。", "chat.filtered_full": "伺服器已為部分玩家隱藏您的訊息。", "chat.link.confirm": "你確定要開啟以下網頁嗎？", "chat.link.confirmTrusted": "確定要開啟此連結或將它複製到剪貼簿嗎？", "chat.link.open": "在瀏覽器中開啟", "chat.link.warning": "千萬不要開啟任何你不信任的人所提供的連結！", "chat.queue": "[+%s 則待傳送訊息]", "chat.square_brackets": "[%s]", "chat.tag.error": "伺服器傳送了無效訊息。", "chat.tag.modified": "訊息被伺服器修改過。原文：", "chat.tag.not_secure": "無法檢舉未認證的訊息。", "chat.tag.system": "無法檢舉伺服器訊息。", "chat.tag.system_single_player": "伺服器訊息。", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s 已完成挑戰 %s", "chat.type.advancement.goal": "%s 已達成目標 %s", "chat.type.advancement.task": "%s 已完成進度 %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "傳送隊伍訊息", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s 說︰%s", "chat.validation_error": "聊天驗證錯誤", "chat_screen.message": "傳送訊息：%s", "chat_screen.title": "聊天畫面", "chat_screen.usage": "輸入訊息後按下 Enter 鍵傳送", "chunk.toast.checkLog": "查看記錄檔以取得詳細資訊", "chunk.toast.loadFailure": "無法載入 %s 處的區塊", "chunk.toast.lowDiskSpace": "磁碟空間不足！", "chunk.toast.lowDiskSpace.description": "可能無法儲存世界。", "chunk.toast.saveFailure": "無法儲存 %s 處的區塊", "clear.failed.multiple": "在 %s 個玩家身上找不到任何物品", "clear.failed.single": "在玩家 %s 身上找不到任何物品", "color.minecraft.black": "黑色", "color.minecraft.blue": "藍色", "color.minecraft.brown": "棕色", "color.minecraft.cyan": "青色", "color.minecraft.gray": "灰色", "color.minecraft.green": "綠色", "color.minecraft.light_blue": "淺藍色", "color.minecraft.light_gray": "淺灰色", "color.minecraft.lime": "淺綠色", "color.minecraft.magenta": "洋紅色", "color.minecraft.orange": "橙色", "color.minecraft.pink": "粉紅色", "color.minecraft.purple": "紫色", "color.minecraft.red": "紅色", "color.minecraft.white": "白色", "color.minecraft.yellow": "黃色", "command.context.here": "<--［這裡］", "command.context.parse_error": "第 %2$s 個字元 %1$s：%3$s", "command.exception": "無法解析指令：%s", "command.expected.separator": "引數結尾應為空白字元，但發現有尾隨資料", "command.failed": "試圖執行該指令時發生了非預期性錯誤", "command.forkLimit": "已達上下文數量上限 (%s)", "command.unknown.argument": "指令的引數不正確", "command.unknown.command": "未知或不完整的指令，錯誤如下", "commands.advancement.criterionNotFound": "進度 %1$s 不包含 '%2$s' 標準條件", "commands.advancement.grant.criterion.to.many.failure": "無法將進度 %2$s 的條件 '%1$s' 給予 %3$s 個玩家，因為他們已經擁有該條件", "commands.advancement.grant.criterion.to.many.success": "已將進度 %2$s 的條件 '%1$s' 給予 %3$s 個玩家", "commands.advancement.grant.criterion.to.one.failure": "無法將進度 %2$s 的條件 '%1$s' 給予 %3$s，因為他已經擁有該條件", "commands.advancement.grant.criterion.to.one.success": "已將進度 %2$s 的條件 '%1$s' 給予 %3$s", "commands.advancement.grant.many.to.many.failure": "無法將 %s 個進度給予 %s 個玩家，因為他們已達成那些進度", "commands.advancement.grant.many.to.many.success": "已將 %s 個進度給予 %s 個玩家", "commands.advancement.grant.many.to.one.failure": "無法將 %s 個進度給予 %s，因為他已達成此進度", "commands.advancement.grant.many.to.one.success": "已將 %s 個進度給予 %s", "commands.advancement.grant.one.to.many.failure": "無法將進度 %s 給予 %s 個玩家，因為他們已達成此進度", "commands.advancement.grant.one.to.many.success": "已將進度 %s 給予 %s 個玩家", "commands.advancement.grant.one.to.one.failure": "無法將進度 %s 給予 %s，因為他已達成此進度", "commands.advancement.grant.one.to.one.success": "已將進度 %s 給予 %s", "commands.advancement.revoke.criterion.to.many.failure": "無法從 %3$s 個玩家身上撤銷進度 %2$s 尚未取得的的條件 '%1$s'", "commands.advancement.revoke.criterion.to.many.success": "已從 %3$s 個玩家身上撤銷進度 %2$s 的條件 '%1$s'", "commands.advancement.revoke.criterion.to.one.failure": "無法從 %3$s 身上撤銷進度 %2$s 尚未取得的條件 '%1$s'", "commands.advancement.revoke.criterion.to.one.success": "已從 %3$s 身上撤銷進度 %2$s 的條件 '%1$s'", "commands.advancement.revoke.many.to.many.failure": "無法從 %2$s 個玩家身上撤銷尚未取得的 %1$s 個進度", "commands.advancement.revoke.many.to.many.success": "已從 %2$s 個玩家身上撤銷 %1$s 個進度", "commands.advancement.revoke.many.to.one.failure": "無法從 %2$s 身上撤銷尚未取得的 %1$s 個進度", "commands.advancement.revoke.many.to.one.success": "已從 %2$s 身上撤銷 %1$s 個進度", "commands.advancement.revoke.one.to.many.failure": "無法從 %2$s 個玩家身上撤銷尚未取得的進度 %1$s", "commands.advancement.revoke.one.to.many.success": "已從 %2$s 個玩家身上撤銷進度 %1$s", "commands.advancement.revoke.one.to.one.failure": "無法從 %2$s 身上撤銷尚未取得的進度 %1$s", "commands.advancement.revoke.one.to.one.success": "已從 %2$s 身上撤銷進度 %1$s", "commands.attribute.base_value.get.success": "實體 %2$s 的屬性 %1$s 基礎值為 %3$s", "commands.attribute.base_value.reset.success": "已將實體 %2$s 的屬性 %1$s 重設為預設值 %3$s", "commands.attribute.base_value.set.success": "已將實體 %2$s 的屬性 %1$s 基礎值設定為 %3$s", "commands.attribute.failed.entity": "%s 不是這條指令的有效實體", "commands.attribute.failed.modifier_already_present": "實體 %3$s 於屬性 %2$s 中已存在修飾符 %1$s", "commands.attribute.failed.no_attribute": "實體 %s 沒有屬性 %s", "commands.attribute.failed.no_modifier": "實體 %2$s 的屬性 %1$s 沒有修飾符 %3$s", "commands.attribute.modifier.add.success": "已為實體 %3$s 的屬性 %2$s 加上修飾符 %1$s", "commands.attribute.modifier.remove.success": "已從實體 %3$s 的屬性 %2$s 移除修飾符 %1$s", "commands.attribute.modifier.value.get.success": "實體 %3$s 於屬性 %2$s 上的修飾符 %1$s 值為 %4$s", "commands.attribute.value.get.success": "實體 %2$s 的屬性 %1$s 值為 %3$s", "commands.ban.failed": "未進行任何變更。該玩家已經被封鎖了", "commands.ban.success": "%s 已被封鎖：%s", "commands.banip.failed": "未進行任何變更。這個 IP 已被封鎖", "commands.banip.info": "這次封鎖作用於 %s 個玩家：%s", "commands.banip.invalid": "無效的 IP 位址或未知的玩家", "commands.banip.success": "已封鎖 IP %s: %s", "commands.banlist.entry": "%s 被 %s 封鎖：%s", "commands.banlist.entry.unknown": "（未知）", "commands.banlist.list": "以下 %s 個玩家被封鎖：", "commands.banlist.none": "沒有被封鎖的玩家", "commands.bossbar.create.failed": "已存在 ID 為 '%s' 的 Boss 狀態條", "commands.bossbar.create.success": "已建立自訂 Boss 狀態條 %s", "commands.bossbar.get.max": "自訂 Boss 狀態條 %s 的最大值為 %s", "commands.bossbar.get.players.none": "自訂 Boss 狀態條 %s 目前沒有在線上的玩家", "commands.bossbar.get.players.some": "自訂 Boss 狀態條 %s 目前有 %s 位玩家在線上：%s", "commands.bossbar.get.value": "自訂 Boss 狀態條 %s 的數值為 %s", "commands.bossbar.get.visible.hidden": "自訂 Boss 狀態條 %s 目前為隱藏", "commands.bossbar.get.visible.visible": "自訂 Boss 狀態條 %s 目前可見", "commands.bossbar.list.bars.none": "沒有已啟用的自訂 Boss 狀態條", "commands.bossbar.list.bars.some": "已啟用的 %s 個自訂 Boss 狀態條為：%s", "commands.bossbar.remove.success": "已移除自訂 Boss 狀態條 %s", "commands.bossbar.set.color.success": "已變更自訂 Boss 狀態條 %s 的顏色", "commands.bossbar.set.color.unchanged": "未進行任何變更。此 Boss 狀態條已經是該顏色", "commands.bossbar.set.max.success": "已將自訂 Boss 狀態條 %s 的最大值變更為 %s", "commands.bossbar.set.max.unchanged": "未進行任何變更。此 Boss 狀態條已經是該最大值", "commands.bossbar.set.name.success": "已重新命名自訂 Boss 狀態條 %s", "commands.bossbar.set.name.unchanged": "未進行任何變更。此 Boss 狀態條已經是該名稱", "commands.bossbar.set.players.success.none": "自訂 Boss 狀態條 %s 上不再有任何玩家", "commands.bossbar.set.players.success.some": "自訂 Boss 狀態條 %s 現在有 %s 位玩家：%s", "commands.bossbar.set.players.unchanged": "未進行任何變更。這些玩家已經在此 Boss 狀態條中，無法加入或移除", "commands.bossbar.set.style.success": "已變更自訂 Boss 狀態條 %s 的樣式", "commands.bossbar.set.style.unchanged": "未進行任何變更。此 Boss 狀態條已經是該樣式", "commands.bossbar.set.value.success": "已將自訂 Boss 狀態條 %s 的數值更改為 %s", "commands.bossbar.set.value.unchanged": "未進行任何變更。此 Boss 狀態條已經是該數值", "commands.bossbar.set.visibility.unchanged.hidden": "未進行任何變更。此 Boss 狀態條已被隱藏", "commands.bossbar.set.visibility.unchanged.visible": "未進行任何變更。此 Boss 狀態條已設為可見", "commands.bossbar.set.visible.success.hidden": "已將自訂 Boss 狀態條 %s 設為隱藏", "commands.bossbar.set.visible.success.visible": "已將自訂 Boss 狀態條 %s 設為可見", "commands.bossbar.unknown": "ID 為 '%s' 的 Boss 狀態條不存在", "commands.clear.success.multiple": "已移除 %2$s 個玩家的 %1$s 個物品", "commands.clear.success.single": "已移除玩家 %2$s 的 %1$s 個物品", "commands.clear.test.multiple": "在 %2$s 個玩家身上找到 %1$s 個相符的物品", "commands.clear.test.single": "在玩家 %2$s 身上找到 %1$s 個相符的物品", "commands.clone.failed": "未複製任何方塊", "commands.clone.overlap": "來源與目的區域不可重疊", "commands.clone.success": "已成功複製 %s 個方塊", "commands.clone.toobig": "指定區域內的方塊太多（指定 %2$s 但最大值為 %1$s）", "commands.damage.invulnerable": "目標對指定的傷害類型免疫", "commands.damage.success": "已將 %s 傷害套用至 %s", "commands.data.block.get": "位於 %2$s, %3$s, %4$s 方塊上的 %1$s 乘上 %5$s 的倍率後為 %6$s", "commands.data.block.invalid": "指定的方塊並非方塊實體", "commands.data.block.modified": "已修改位於 %s, %s, %s 的方塊資料", "commands.data.block.query": "%s, %s, %s 擁有以下方塊資料：%s", "commands.data.entity.get": "%2$s 上的 %1$s 乘上 %3$s 的倍率後為 %4$s", "commands.data.entity.invalid": "無法修改玩家資料", "commands.data.entity.modified": "已修改 %s 的實體資料", "commands.data.entity.query": "%s 擁有以下實體資料：%s", "commands.data.get.invalid": "無法取得 %s，只允許數字類型標籤", "commands.data.get.multiple": "此引數接受單一的 NBT 數值", "commands.data.get.unknown": "無法取得 %s，標籤不存在", "commands.data.merge.failed": "未進行任何變更。指定的屬性已經是該值", "commands.data.modify.expected_list": "應為串列，當前值：%s", "commands.data.modify.expected_object": "應為物件，當前值：%s", "commands.data.modify.expected_value": "應為值，當前值：%s", "commands.data.modify.invalid_index": "無效的串列索引：%s", "commands.data.modify.invalid_substring": "無效的子字串索引：%s 至 %s", "commands.data.storage.get": "儲存於 %2$s 中的 %1$s 乘上 %3$s 的倍率後為 %4$s", "commands.data.storage.modified": "已修改容器 %s", "commands.data.storage.query": "容器 %s 擁有下列內容物：%s", "commands.datapack.create.already_exists": "名為 '%s' 的資料包已經存在", "commands.datapack.create.invalid_full_name": "無效的新資料包名稱 '%s'", "commands.datapack.create.invalid_name": "新資料包名稱 '%s' 中含有無效字元", "commands.datapack.create.io_failure": "無法建立名為 '%s' 的資料包，請查看記錄檔", "commands.datapack.create.metadata_encode_failure": "無法編碼資料包 '%s' 的中繼資料：%s", "commands.datapack.create.success": "已建立名為 '%s' 的空資料包", "commands.datapack.disable.failed": "資料包 '%s' 未啟用！", "commands.datapack.disable.failed.feature": "無法停用資料包 '%s'，因為它屬於已啟用的功能！", "commands.datapack.enable.failed": "資料包 '%s' 已經啟用！", "commands.datapack.enable.failed.no_flags": "無法啟用資料包 '%s'，因為所需的功能並未在這個世界中啟用：%s！", "commands.datapack.list.available.none": "沒有可用的資料包", "commands.datapack.list.available.success": "有 %s 個可用的資料包： %s", "commands.datapack.list.enabled.none": "沒有啟用的資料包", "commands.datapack.list.enabled.success": "有 %s 個啟用的資料包：%s", "commands.datapack.modify.disable": "正在停用資料包 %s", "commands.datapack.modify.enable": "正在啟用資料包 %s", "commands.datapack.unknown": "未知的資料包 '%s'", "commands.debug.alreadyRunning": "刻點分析器已在執行中", "commands.debug.function.noRecursion": "無法從函數內部開始追蹤", "commands.debug.function.noReturnRun": "追蹤無法與 /return run 同時使用", "commands.debug.function.success.multiple": "從 %2$s 個函數追蹤到的 %1$s 個指令輸出至檔案 %3$s", "commands.debug.function.success.single": "已從函數 '%2$s' 追蹤到的 %1$s 個指令輸出至檔案 %3$s", "commands.debug.function.traceFailed": "函數追蹤失敗", "commands.debug.notRunning": "刻點分析器未啟動", "commands.debug.started": "開始刻點分析", "commands.debug.stopped": "已停止刻點分析，共耗時 %s 秒和 %s 刻（每秒 %s 刻）", "commands.defaultgamemode.success": "這個世界的預設遊戲模式目前為 %s", "commands.deop.failed": "未進行任何變更。該玩家不是管理員", "commands.deop.success": "已將 %s 從伺服器管理員中除名", "commands.dialog.clear.multiple": "已清除 %s 個玩家的對話框", "commands.dialog.clear.single": "已清除 %s 的對話框", "commands.dialog.show.multiple": "已將對話框顯示給 %s 個玩家", "commands.dialog.show.single": "已將對話框顯示給 %s", "commands.difficulty.failure": "難度沒有改變，它已經被設為 %s", "commands.difficulty.query": "目前難易度為 %s", "commands.difficulty.success": "已將難易度設為 %s", "commands.drop.no_held_items": "該實體無法持有任何物品", "commands.drop.no_loot_table": "實體 %s 沒有戰利品表", "commands.drop.no_loot_table.block": "方塊 %s 沒有戰利品表", "commands.drop.success.multiple": "掉落 %s 個物品", "commands.drop.success.multiple_with_table": "從戰利品表 %2$s 中掉落 %1$s 個物品", "commands.drop.success.single": "掉落了 %s 個 %s", "commands.drop.success.single_with_table": "從戰利品表 %3$s 中掉落了 %1$s 個 %2$s", "commands.effect.clear.everything.failed": "該對象沒有可以移除的效果", "commands.effect.clear.everything.success.multiple": "已移除 %s 個對象上的所有效果", "commands.effect.clear.everything.success.single": "已移除 %s 身上的所有效果", "commands.effect.clear.specific.failed": "該對象沒有你指定的效果", "commands.effect.clear.specific.success.multiple": "已移除 %2$s 個對象身上的 %1$s 效果", "commands.effect.clear.specific.success.single": "已移除 %2$s 身上的 %1$s 效果", "commands.effect.give.failed": "無法套用這項效果（該對象不受效果影響，或已有其他更強的效果）", "commands.effect.give.success.multiple": "已將 %s 效果作用於 %s 個對象", "commands.effect.give.success.single": "已將 %s 效果作用於 %s", "commands.enchant.failed": "未進行任何變更。對象手中沒有物品或是無法套用這項附魔", "commands.enchant.failed.entity": "%s 不是這條指令的有效實體", "commands.enchant.failed.incompatible": "該附魔不適用於 %s", "commands.enchant.failed.itemless": "%s 手上沒有任何物品", "commands.enchant.failed.level": "%s 高於該附魔所支援的最高等級 %s", "commands.enchant.success.multiple": "已將附魔 %s 套用於 %s 個實體", "commands.enchant.success.single": "已將附魔 %s 套用於 %s 的物品", "commands.execute.blocks.toobig": "指定區域內的方塊太多（指定 %2$s 但最大值為 %1$s）", "commands.execute.conditional.fail": "測試失敗", "commands.execute.conditional.fail_count": "測試失敗，總計：%s", "commands.execute.conditional.pass": "測試通過", "commands.execute.conditional.pass_count": "測試通過，總計：%s", "commands.execute.function.instantiationFailure": "無法具現化函數 %s：%s", "commands.experience.add.levels.success.multiple": "已給予 %2$s 個玩家 %1$s 等經驗等級", "commands.experience.add.levels.success.single": "已給予 %2$s %1$s 等經驗等級", "commands.experience.add.points.success.multiple": "已給予 %2$s 個玩家 %1$s 點經驗值", "commands.experience.add.points.success.single": "已給予 %2$s %1$s 點經驗值", "commands.experience.query.levels": "%s 擁有 %s 經驗等級", "commands.experience.query.points": "%s 擁有 %s 經驗點", "commands.experience.set.levels.success.multiple": "已將 %2$s 個玩家的經驗等級設定為 %1$s 等", "commands.experience.set.levels.success.single": "已將 %2$s 的經驗等級設定為 %1$s 等", "commands.experience.set.points.invalid": "無法將經驗點設為高於玩家目前等級的最高經驗點", "commands.experience.set.points.success.multiple": "已將 %2$s 個玩家的經驗值設定為 %1$s", "commands.experience.set.points.success.single": "已將 %2$s 的經驗值設定為 %1$s", "commands.fill.failed": "未填滿任何方塊", "commands.fill.success": "已成功填充 %s 個方塊", "commands.fill.toobig": "指定區域內的方塊太多（指定 %2$s 但最大值為 %1$s）", "commands.fillbiome.success": "已設定自 %s, %s, %s 至 %s, %s, %s 之間的生態域", "commands.fillbiome.success.count": "已設定自 %2$s, %3$s, %4$s 至 %5$s, %6$s, %7$s 之間的 %1$s 個生態域單元", "commands.fillbiome.toobig": "指定區域內的方塊太多（指定 %2$s 但最大值為 %1$s）", "commands.forceload.added.failure": "沒有區塊被標記為常駐區塊", "commands.forceload.added.multiple": "已將 %2$s 中 %3$s 到 %4$s 之間的 %1$s 個區塊標記為常駐區塊", "commands.forceload.added.none": "從 %s 中未找到常駐區塊", "commands.forceload.added.single": "已將 %2$s 中的區塊 %1$s 標記為常駐區塊", "commands.forceload.list.multiple": "從 %2$s 中找到 %1$s 個常駐區塊：%3$s", "commands.forceload.list.single": "從 %s 中找到一個常駐區塊：%s", "commands.forceload.query.failure": "%2$s 中的區塊 %1$s 未標記為常駐區塊", "commands.forceload.query.success": "%2$s 中的區塊 %1$s 已標記為常駐區塊", "commands.forceload.removed.all": "已取消 %s 中的所有常駐區塊標記", "commands.forceload.removed.failure": "沒有移除任何常駐區塊標記", "commands.forceload.removed.multiple": "已將 %2$s 中 %3$s 到 %4$s 之間的 %1$s 個區塊取消常駐區塊標記", "commands.forceload.removed.single": "已將 %2$s 中的區塊 %1$s 取消常駐區塊標記", "commands.forceload.toobig": "指定區域內的區塊太多（最大值為 %s，指定為 %s）", "commands.function.error.argument_not_compound": "無效的引數類型：%s，應為 Compound", "commands.function.error.missing_argument": "函數 %1$s 缺少引數 %2$s", "commands.function.error.missing_arguments": "函數 %s 缺少引數", "commands.function.error.parse": "在具現化巨集 %s 時：指令 '%s' 導致錯誤：%s", "commands.function.instantiationFailure": "無法具現化函數 %s：%s", "commands.function.result": "函數 %s 已回傳 %s", "commands.function.scheduled.multiple": "正在執行 %s 函數", "commands.function.scheduled.no_functions": "找不到名為 %s 的函數", "commands.function.scheduled.single": "正在執行 %s 函數", "commands.function.success.multiple": "已從 %2$s 個函數執行 %1$s 個指令", "commands.function.success.multiple.result": "已執行 %s 個函數", "commands.function.success.single": "已從函數 '%2$s' 執行 %1$s 個指令", "commands.function.success.single.result": "函數 '%2$s' 已回傳 %1$s", "commands.gamemode.success.other": "已將 %s 的遊戲模式切換為 %s", "commands.gamemode.success.self": "已將自己的遊戲模式切換為 %s", "commands.gamerule.query": "遊戲規則 %s 目前設為：%s", "commands.gamerule.set": "遊戲規則 %s 現在已設為：%s", "commands.give.failed.toomanyitems": "無法給予超過 %s 個 %s", "commands.give.success.multiple": "已將 %s 個 %s 給予 %s 個玩家", "commands.give.success.single": "已將 %s 個 %s 給予 %s", "commands.help.failed": "未知的指令或權限不足", "commands.item.block.set.success": "已用 %4$s 替換位於 %1$s, %2$s, %3$s 的欄位", "commands.item.entity.set.success.multiple": "已用 %2$s 替換 %1$s 個實體上的欄位", "commands.item.entity.set.success.single": "已用 %2$s 替換 %1$s 上的欄位", "commands.item.source.no_such_slot": "該來源並沒有 %s 欄位", "commands.item.source.not_a_container": "來源位置 %s, %s, %s 並非容器", "commands.item.target.no_changed.known_item": "沒有任何對象的 %2$s 欄位接收到物品 %1$s", "commands.item.target.no_changes": "沒有任何對象的 %s 欄位接收到物品", "commands.item.target.no_such_slot": "該對象並無 %s 欄位", "commands.item.target.not_a_container": "指定的位置 %s, %s, %s 並非容器", "commands.jfr.dump.failed": "無法傾出 JFR 紀錄：%s", "commands.jfr.start.failed": "無法開始 JFR 分析", "commands.jfr.started": "開始進行 JFR 分析", "commands.jfr.stopped": "已停止 JFR 分析，分析內容已傾出至 %s", "commands.kick.owner.failed": "無法在區網遊戲中踢出伺服器擁有者", "commands.kick.singleplayer.failed": "無法在離線單人遊戲踢出玩家", "commands.kick.success": "%s 已被踢出：%s", "commands.kill.success.multiple": "已消滅 %s 個實體", "commands.kill.success.single": "已消滅 %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "目前有 %s/%s 個玩家在線上：%s", "commands.locate.biome.not_found": "無法在合理距離內找到 \"%s\" 類型的生態域", "commands.locate.biome.success": "最接近的 %s 位於 %s（距離 %s 個方塊遠）", "commands.locate.poi.not_found": "無法在合理距離內找到 \"%s\" 類型的興趣點", "commands.locate.poi.success": "最接近的 %s 位於 %s（距離 %s 個方塊遠）", "commands.locate.structure.invalid": "沒有類型為 \"%s\" 的結構", "commands.locate.structure.not_found": "無法在附近找到類型為 \"%s\" 的結構", "commands.locate.structure.success": "最接近的 %s 位於 %s（距離 %s 個方塊遠）", "commands.message.display.incoming": "%s 悄悄對您說：%s", "commands.message.display.outgoing": "您悄悄對 %s 說：%s", "commands.op.failed": "未進行任何變更。該玩家已是管理員", "commands.op.success": "已將 %s 設為伺服器管理員", "commands.pardon.failed": "未進行任何變更。該玩家未被封鎖", "commands.pardon.success": "已解除封鎖 %s", "commands.pardonip.failed": "未進行任何變更。該 IP 未被封鎖", "commands.pardonip.invalid": "無效的 IP 位址", "commands.pardonip.success": "已解除封鎖 IP %s", "commands.particle.failed": "此粒子效果無法被任何玩家看見", "commands.particle.success": "顯示粒子 %s", "commands.perf.alreadyRunning": "效能分析器已在執行中", "commands.perf.notRunning": "效能分析器未啟動", "commands.perf.reportFailed": "無法建立除錯報告", "commands.perf.reportSaved": "已將除錯報告建立於 %s", "commands.perf.started": "已啟用持續 10 秒的效能分析測試（使用 '/perf stop' 以提前結束）", "commands.perf.stopped": "效能分析已停止，總共耗時 %s 秒和 %s 刻 (%s 刻/秒)", "commands.place.feature.failed": "無法放置該地物", "commands.place.feature.invalid": "沒有類型為 \"%s\" 的地物", "commands.place.feature.success": "已於 %2$s, %3$s, %4$s 處放置 \"%1$s\"", "commands.place.jigsaw.failed": "無法生成拼圖", "commands.place.jigsaw.invalid": "沒有類型為 \"%s\" 的模板池", "commands.place.jigsaw.success": "已於 %s, %s, %s 處生成拼圖", "commands.place.structure.failed": "無法放置該結構", "commands.place.structure.invalid": "沒有類型為 \"%s\" 的結構", "commands.place.structure.success": "已於 %2$s, %3$s, %4$s 處生成結構 \"%1$s\"", "commands.place.template.failed": "無法放置該模板", "commands.place.template.invalid": "沒有 ID 為 \"%s\" 的模板", "commands.place.template.success": "已於 %2$s, %3$s, %4$s 處載入模板 \"%1$s\"", "commands.playsound.failed": "此音效太遠無法被聽見", "commands.playsound.success.multiple": "已將音效 %s 播放給 %s 個玩家", "commands.playsound.success.single": "已將音效 %s 播放給 %s", "commands.publish.alreadyPublished": "主機連接埠 %s 上已經建立了多人遊戲", "commands.publish.failed": "無法公開遊戲至區域網", "commands.publish.started": "已在 %s 連接埠上發布本機遊戲", "commands.publish.success": "多人遊戲已建立於主機連接埠 %s", "commands.random.error.range_too_large": "隨機值範圍最大為 2147463646", "commands.random.error.range_too_small": "隨機值範圍最小為 2", "commands.random.reset.all.success": "已重設 %s 個隨機序列", "commands.random.reset.success": "已重設隨機序列 %s", "commands.random.roll": "%s 擲出了 %s（在 %s 和 %s 之間）", "commands.random.sample.success": "隨機值：%s", "commands.recipe.give.failed": "沒有學到新的配方", "commands.recipe.give.success.multiple": "已為 %2$s 個玩家解鎖了 %1$s 條合成配方", "commands.recipe.give.success.single": "已為 %2$s 解鎖了 %1$s 條合成配方", "commands.recipe.take.failed": "沒有可以被遺忘的配方", "commands.recipe.take.success.multiple": "已移除 %2$s 個玩家的 %1$s 條合成配方", "commands.recipe.take.success.single": "已移除 %2$s 的 %1$s 條合成配方", "commands.reload.failure": "重新載入失敗，保留原有資料", "commands.reload.success": "重新載入中！", "commands.ride.already_riding": "%s 已在騎乘 %s", "commands.ride.dismount.success": "%s 結束騎乘 %s", "commands.ride.mount.failure.cant_ride_players": "無法騎乘玩家", "commands.ride.mount.failure.generic": "%s 無法騎乘 %s", "commands.ride.mount.failure.loop": "實體不能騎乘自身或是其乘客", "commands.ride.mount.failure.wrong_dimension": "無法騎乘位於不同維度的實體", "commands.ride.mount.success": "%s 開始騎乘 %s", "commands.ride.not_riding": "%s 未騎乘任何載具", "commands.rotate.success": "已旋轉 %s", "commands.save.alreadyOff": "儲存功能已經關閉", "commands.save.alreadyOn": "儲存功能已經開啟", "commands.save.disabled": "自動存檔已停用", "commands.save.enabled": "自動存檔已啟用", "commands.save.failed": "無法儲存遊戲（有足夠的磁碟空間嗎？）", "commands.save.saving": "正在儲存遊戲（這可能需要一點時間！）", "commands.save.success": "遊戲已儲存", "commands.schedule.cleared.failure": "沒有 ID 為 %s 的排程", "commands.schedule.cleared.success": "移除 ID 為 %2$s 的 %1$s 個排程", "commands.schedule.created.function": "已將函數 '%1$s' 排定於 %2$s 刻後，遊戲時間為 %3$s", "commands.schedule.created.tag": "已將標籤 '%s' 排定於 %s 刻後，遊戲時間為 %s", "commands.schedule.macro": "無法排定巨集", "commands.schedule.same_tick": "無法排定於當前刻", "commands.scoreboard.objectives.add.duplicate": "已存在使用該名稱的目標", "commands.scoreboard.objectives.add.success": "建立新的目標 %s", "commands.scoreboard.objectives.display.alreadyEmpty": "未進行任何變更。顯示區已經是空的", "commands.scoreboard.objectives.display.alreadySet": "未進行任何變更。顯示區已顯示該目標", "commands.scoreboard.objectives.display.cleared": "已清除 %s 顯示區的所有目標", "commands.scoreboard.objectives.display.set": "已將目標 %2$s 顯示於 %1$s 的顯示區", "commands.scoreboard.objectives.list.empty": "目前沒有目標", "commands.scoreboard.objectives.list.success": "目前有 %s 個目標： %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "已停用目標 %s 的顯示自動更新", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "已啟用目標 %s 的顯示自動更新", "commands.scoreboard.objectives.modify.displayname": "已將 %s 的顯示名稱更改為 %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "已清除目標 %s 的預設數字格式", "commands.scoreboard.objectives.modify.objectiveFormat.set": "已變更目標 %s 的預設數字格式", "commands.scoreboard.objectives.modify.rendertype": "已變更目標 %s 的顯示類型", "commands.scoreboard.objectives.remove.success": "已移除目標 %s", "commands.scoreboard.players.add.success.multiple": "已為 %3$s 個實體的 %2$s 增加 %1$s", "commands.scoreboard.players.add.success.single": "已為 %3$s 的 %2$s 增加 %1$s（目前為 %4$s）", "commands.scoreboard.players.display.name.clear.success.multiple": "已清除 %s 個實體在 %s 中的顯示名稱", "commands.scoreboard.players.display.name.clear.success.single": "已清除 %s 在 %s 中的顯示名稱", "commands.scoreboard.players.display.name.set.success.multiple": "已將 %2$s 個實體在 %3$s 中的顯示名稱變更為 %1$s", "commands.scoreboard.players.display.name.set.success.single": "已將 %2$s 在 %3$s 中的顯示名稱變更為 %1$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "已清除 %s 個實體在 %s 中的數字格式", "commands.scoreboard.players.display.numberFormat.clear.success.single": "已清除 %s 在 %s 中的數字格式", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "已變更 %s 個實體在 %s 中的數字格式", "commands.scoreboard.players.display.numberFormat.set.success.single": "已變更 %s 在 %s 中的數字格式", "commands.scoreboard.players.enable.failed": "未進行任何變更。該觸發項目已經啟用", "commands.scoreboard.players.enable.invalid": "啟用僅作用於觸發類型的目標", "commands.scoreboard.players.enable.success.multiple": "已在 %2$s 個實體上啟用觸發項目 %1$s", "commands.scoreboard.players.enable.success.single": "已在 %2$s 上啟用觸發項目 %1$s", "commands.scoreboard.players.get.null": "無法取得 %2$s 的 %1$s 數值，數值未設定", "commands.scoreboard.players.get.success": "%s 擁有 %s %s", "commands.scoreboard.players.list.empty": "目前沒有追蹤的實體", "commands.scoreboard.players.list.entity.empty": "%s 沒有分數可以顯示", "commands.scoreboard.players.list.entity.entry": "%s：%s", "commands.scoreboard.players.list.entity.success": "%s 有 %s 個分數：", "commands.scoreboard.players.list.success": "目前追蹤了 %s 個實體： %s", "commands.scoreboard.players.operation.success.multiple": "已更新 %2$s 個實體的 %1$s", "commands.scoreboard.players.operation.success.single": "已將 %2$s 的 %1$s 設為 %3$s", "commands.scoreboard.players.remove.success.multiple": "已從 %3$s 個實體的 %2$s 移除 %1$s", "commands.scoreboard.players.remove.success.single": "已從 %3$s 的 %2$s 移除 %1$s（現在是 %4$s）", "commands.scoreboard.players.reset.all.multiple": "已重設 %s 個實體的所有分數", "commands.scoreboard.players.reset.all.single": "已重設 %s 的所有分數", "commands.scoreboard.players.reset.specific.multiple": "已重設 %2$s 個實體的 %1$s", "commands.scoreboard.players.reset.specific.single": "已重設 %2$s 的 %1$s", "commands.scoreboard.players.set.success.multiple": "已將 %2$s 個實體的 %1$s 設為 %3$s", "commands.scoreboard.players.set.success.single": "已將 %2$s 的 %1$s 設為 %3$s", "commands.seed.success": "世界種子碼：%s", "commands.setblock.failed": "無法放置方塊", "commands.setblock.success": "已變更位於 %s, %s, %s 的方塊", "commands.setidletimeout.success": "已將玩家閒置踢出時間設定為 %s 分鐘", "commands.setidletimeout.success.disabled": "已停用玩家閒置踢出", "commands.setworldspawn.failure.not_overworld": "只能在主世界設定世界重生點", "commands.setworldspawn.success": "已將世界重生點設為 %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "已將 %6$s 個玩家的重生點設為 %5$s 中的 %1$s, %2$s, %3$s [%4$s]", "commands.spawnpoint.success.single": "已將 %6$s 的重生點設為 %5$s 中的 %1$s, %2$s, %3$s [%4$s]", "commands.spectate.not_spectator": "%s 不處於旁觀者模式", "commands.spectate.self": "無法旁觀你自己", "commands.spectate.success.started": "正在旁觀 %s", "commands.spectate.success.stopped": "不再旁觀實體", "commands.spreadplayers.failed.entities": "未能將 %s 個實體分散至 (%s, %s) 周圍（空間中實體過多 – 請嘗試將分散範圍調整為至多 %s 格）", "commands.spreadplayers.failed.invalid.height": "無效的 maxHeight 值：%s，應高過世界最小高度 %s", "commands.spreadplayers.failed.teams": "未能將 %s 個隊伍分散至 (%s, %s) 周圍（空間中實體過多 – 請嘗試將分散範圍調整為至多 %s 格）", "commands.spreadplayers.success.entities": "已將 %s 個實體分散於 (%s, %s) 附近， 每個實體之間的平均距離為 %s 格", "commands.spreadplayers.success.teams": "已將 %s 個隊伍分散於 (%s, %s) 附近， 每個隊伍之間的平均距離為 %s 格", "commands.stop.stopping": "正在停止伺服器", "commands.stopsound.success.source.any": "已停止所有來自 '%s' 的音效", "commands.stopsound.success.source.sound": "已停止來自音源 '%2$s' 的音效 '%1$s'", "commands.stopsound.success.sourceless.any": "已停止所有的音效", "commands.stopsound.success.sourceless.sound": "已停止音效 '%s'", "commands.summon.failed": "無法召喚實體", "commands.summon.failed.uuid": "UUID 重複，無法召喚實體", "commands.summon.invalidPosition": "無效的召喚座標", "commands.summon.success": "已召喚新的 %s", "commands.tag.add.failed": "該對象已擁有此標籤或擁有過多標籤", "commands.tag.add.success.multiple": "已為 %2$s 個實體加上了 '%1$s' 標籤", "commands.tag.add.success.single": "已為 %2$s 加上了 '%1$s' 標籤", "commands.tag.list.multiple.empty": "這 %s 個實體上沒有標籤", "commands.tag.list.multiple.success": "這 %s 個實體總共有 %s 個標籤：%s", "commands.tag.list.single.empty": "%s 沒有標籤", "commands.tag.list.single.success": "%s 有 %s 個標籤：%s", "commands.tag.remove.failed": "該對象沒有這個標籤", "commands.tag.remove.success.multiple": "已從 %2$s 個實體上移除了 '%1$s' 標籤", "commands.tag.remove.success.single": "已從 %2$s 上移除了 '%1$s' 標籤", "commands.team.add.duplicate": "已存在使用該名稱的隊伍", "commands.team.add.success": "已建立隊伍 %s", "commands.team.empty.success": "已從隊伍 %2$s 中移除 %1$s 個成員", "commands.team.empty.unchanged": "未進行任何變更。該隊伍已經是空的", "commands.team.join.success.multiple": "已將 %s 個成員加入隊伍 %s", "commands.team.join.success.single": "已將 %s 加入隊伍 %s", "commands.team.leave.success.multiple": "已將 %s 個成員從所有隊伍中移除", "commands.team.leave.success.single": "已將 %s 從隊伍中移除", "commands.team.list.members.empty": "隊伍 %s 中沒有成員", "commands.team.list.members.success": "隊伍 %s 有 %s 個成員： %s", "commands.team.list.teams.empty": "沒有隊伍存在", "commands.team.list.teams.success": "共有 %s 個隊伍： %s", "commands.team.option.collisionRule.success": "隊伍 %s 現在的碰撞規則為 \"%s\"", "commands.team.option.collisionRule.unchanged": "未進行任何變更。碰撞規則已為該數值", "commands.team.option.color.success": "將隊伍 %s 的顏色更新為 %s", "commands.team.option.color.unchanged": "未進行任何變更。隊伍已擁有該顏色", "commands.team.option.deathMessageVisibility.success": "隊伍 %s 現在的死亡訊息可見度為「%s」", "commands.team.option.deathMessageVisibility.unchanged": "未進行任何變更。死亡訊息的可見度已為該數值", "commands.team.option.friendlyfire.alreadyDisabled": "未進行任何變更。該隊伍已經停用同隊相殘", "commands.team.option.friendlyfire.alreadyEnabled": "未進行任何變更。該隊伍已經啟用同隊相殘", "commands.team.option.friendlyfire.disabled": "已為隊伍 %s 關閉同隊相殘", "commands.team.option.friendlyfire.enabled": "已為隊伍 %s 啟用同隊相殘", "commands.team.option.name.success": "已更新隊伍 %s 的名稱", "commands.team.option.name.unchanged": "未進行任何變更。隊伍已擁有該名稱", "commands.team.option.nametagVisibility.success": "隊伍 %s 現在的名稱可見度為「%s」", "commands.team.option.nametagVisibility.unchanged": "未進行任何變更。名牌可見度已為該數值", "commands.team.option.prefix.success": "隊伍前綴已設為 %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "未進行任何變更。該隊伍已經看不見隱身的隊友", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "未進行任何變更。該隊伍已經可以看到隱身的隊友", "commands.team.option.seeFriendlyInvisibles.disabled": "隊伍 %s 現在看不到隱身的隊友了", "commands.team.option.seeFriendlyInvisibles.enabled": "隊伍 %s 現在可以看到隱身的隊友了", "commands.team.option.suffix.success": "隊伍後綴已設為 %s", "commands.team.remove.success": "已移除隊伍 %s", "commands.teammsg.failed.noteam": "你必須在隊伍中才能傳送隊伍訊息", "commands.teleport.invalidPosition": "無效的傳送座標", "commands.teleport.success.entity.multiple": "已傳送 %s 個實體到 %s", "commands.teleport.success.entity.single": "傳送 %s 到 %s", "commands.teleport.success.location.multiple": "已傳送 %s 個實體到 %s, %s, %s", "commands.teleport.success.location.single": "傳送 %s 到 %s, %s, %s", "commands.test.batch.starting": "正在啟動環境 %s 批次 %s", "commands.test.clear.error.no_tests": "找不到任何需要清除的測試", "commands.test.clear.success": "已清除 %s 個結構", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "按此複製到剪貼簿", "commands.test.create.success": "已為測試 %s 建立測試設定", "commands.test.error.no_test_containing_pos": "找不到包含 %s, %s, %s 的測試實例", "commands.test.error.no_test_instances": "未找到測試實例", "commands.test.error.non_existant_test": "無法找到測試 %s", "commands.test.error.structure_not_found": "無法找到測試結構 %s", "commands.test.error.test_instance_not_found": "無法找到測試實例方塊實體", "commands.test.error.test_instance_not_found.position": "無法在 %s, %s, %s 處找到測試實例方塊實體", "commands.test.error.too_large": "結構在每個軸上的長度皆須小於 %s 個方塊", "commands.test.locate.done": "定位完成，找到了 %s 個結構", "commands.test.locate.found": "在下列位置找到結構：%s（距離：%s）", "commands.test.locate.started": "開始定位測試結構，這可能需要一點時間...", "commands.test.no_tests": "沒有需要執行的測試", "commands.test.relative_position": "相對 %s 的位置：%s", "commands.test.reset.error.no_tests": "找不到任何需要重設的測試", "commands.test.reset.success": "已重設 %s 個結構", "commands.test.run.no_tests": "未找到任何測試", "commands.test.run.running": "正在執行 %s 個測試...", "commands.test.summary": "遊戲測試完成！執行了 %s 個測試", "commands.test.summary.all_required_passed": "必要測試均已通過 :)", "commands.test.summary.failed": "%s 個必要測試失敗 :(", "commands.test.summary.optional_failed": "%s 個可選測試失敗", "commands.tick.query.percentiles": "百分位數：50%%：%s 毫秒，95%%：%s 毫秒，99%%：%s 毫秒；共取樣：%s", "commands.tick.query.rate.running": "目標速率：每秒 %s 刻。\n每刻平均時間：%s 毫秒（目標：%s 毫秒）", "commands.tick.query.rate.sprinting": "目標速率：每秒 %s 刻（已忽略，僅供參考）。\n每刻平均時間：%s 毫秒", "commands.tick.rate.success": "已將目標速率設為每秒 %s 刻", "commands.tick.sprint.report": "遊戲刻加速已完成，速率為每秒 %s 刻，即每刻 %s 毫秒", "commands.tick.sprint.stop.fail": "沒有正在執行的遊戲刻加速", "commands.tick.sprint.stop.success": "已中止目前的遊戲刻加速", "commands.tick.status.frozen": "遊戲已定格", "commands.tick.status.lagging": "遊戲正在執行，但無法維持目標速率", "commands.tick.status.running": "遊戲正在以正常速率執行", "commands.tick.status.sprinting": "遊戲加速執行中", "commands.tick.step.fail": "無法步進遊戲，請先定格遊戲", "commands.tick.step.stop.fail": "沒有正在執行的遊戲刻步進", "commands.tick.step.stop.success": "已中止目前的遊戲刻步進", "commands.tick.step.success": "正在步進 %s 刻", "commands.time.query": "目前時間為 %s", "commands.time.set": "設定時間為 %s", "commands.title.cleared.multiple": "已清除 %s 個玩家的標題", "commands.title.cleared.single": "已清除 %s 的標題", "commands.title.reset.multiple": "已重設 %s 個玩家的標題設定", "commands.title.reset.single": "已重設 %s 的標題設定", "commands.title.show.actionbar.multiple": "已對 %s 個玩家顯示了新的動作欄標題", "commands.title.show.actionbar.single": "已對 %s 顯示了新的動作欄標題", "commands.title.show.subtitle.multiple": "已對 %s 個玩家顯示了新的副標題", "commands.title.show.subtitle.single": "已對 %s 顯示了新的副標題", "commands.title.show.title.multiple": "已對 %s 個玩家顯示了新的標題", "commands.title.show.title.single": "已對 %s 顯示了新的標題", "commands.title.times.multiple": "已變更 %s 個玩家的標題顯示時間", "commands.title.times.single": "已變更 %s 的標題顯示時間", "commands.transfer.error.no_players": "必須至少指定一個待轉移的玩家", "commands.transfer.success.multiple": "正在轉移 %s 個玩家至 %s:%s", "commands.transfer.success.single": "正在轉移 %s 至 %s:%s", "commands.trigger.add.success": "已觸發 %s（將數值增加 %s）", "commands.trigger.failed.invalid": "你只能觸發 'trigger' 類型的目標", "commands.trigger.failed.unprimed": "你還無法觸發這個目標", "commands.trigger.set.success": "已觸發 %s（將數值設為 %s）", "commands.trigger.simple.success": "已觸發 %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "%s 內沒有路徑點", "commands.waypoint.list.success": "在 %2$s 內找到 %1$s 個路徑點：%3$s", "commands.waypoint.modify.color": "已將路徑點顏色設為 %s", "commands.waypoint.modify.color.reset": "已重設路徑點顏色", "commands.waypoint.modify.style": "已變更路徑點樣式設定", "commands.weather.set.clear": "已將天氣設為晴朗", "commands.weather.set.rain": "已將天氣設為降雨", "commands.weather.set.thunder": "已將天氣設為雷雨", "commands.whitelist.add.failed": "玩家已在白名單內", "commands.whitelist.add.success": "已新增 %s 到白名單", "commands.whitelist.alreadyOff": "白名單已經關閉", "commands.whitelist.alreadyOn": "白名單已經啟用", "commands.whitelist.disabled": "已關閉白名單", "commands.whitelist.enabled": "已啟用白名單", "commands.whitelist.list": "以下 %s 個玩家在白名單中：%s", "commands.whitelist.none": "沒有玩家在白名單中", "commands.whitelist.reloaded": "已重新載入白名單", "commands.whitelist.remove.failed": "玩家不在白名單內", "commands.whitelist.remove.success": "已將 %s 從白名單中移除", "commands.worldborder.center.failed": "未進行任何變更。世界邊界中心已經設定於該處", "commands.worldborder.center.success": "已將世界邊界的中心設為 %s, %s", "commands.worldborder.damage.amount.failed": "未進行任何變更。世界邊界傷害已為該值", "commands.worldborder.damage.amount.success": "已將世界邊界每秒的傷害設為每遠離一格增加 %s", "commands.worldborder.damage.buffer.failed": "未進行任何變更。世界邊界傷害緩衝區已為該距離", "commands.worldborder.damage.buffer.success": "已將世界邊界傷害緩衝設為 %s 格", "commands.worldborder.get": "世界邊界目前為 %s 格寬", "commands.worldborder.set.failed.big": "世界邊界不能大於 %s 格寬", "commands.worldborder.set.failed.far": "世界邊界不能大於 %s 格寬", "commands.worldborder.set.failed.nochange": "未進行任何變更。世界邊界已為該尺寸", "commands.worldborder.set.failed.small": "世界邊界不能小於 1 格寬", "commands.worldborder.set.grow": "在 %2$s 秒間將世界邊界擴增為 %1$s 格寬", "commands.worldborder.set.immediate": "已將世界邊界設為 %s 格寬", "commands.worldborder.set.shrink": "在 %2$s 秒間將世界邊界縮減為 %1$s 格寬", "commands.worldborder.warning.distance.failed": "未進行任何變更。世界邊界警告距離已為該距離", "commands.worldborder.warning.distance.success": "已將世界邊界警告距離設為 %s 格", "commands.worldborder.warning.time.failed": "未進行任何變更。世界邊界警告時間已為該時間", "commands.worldborder.warning.time.success": "已將世界邊界警告時間設為 %s 秒", "compliance.playtime.greaterThan24Hours": "您已經遊玩超過 24 小時", "compliance.playtime.hours": "您已經遊玩了 %s 小時", "compliance.playtime.message": "遊戲過度可能會影響日常生活", "connect.aborted": "已中止", "connect.authorizing": "登入中...", "connect.connecting": "正在連線到伺服器...", "connect.encrypting": "加密中...", "connect.failed": "與伺服器連線失敗", "connect.failed.transfer": "轉移至伺服器時連線失敗", "connect.joining": "正在加入世界...", "connect.negotiating": "協議中...", "connect.reconfiging": "重新設定中...", "connect.reconfiguring": "正在重新設定...", "connect.transferring": "正在轉移至新的伺服器...", "container.barrel": "木桶", "container.beacon": "烽火台", "container.beehive.bees": "蜜蜂：%s / %s", "container.beehive.honey": "蜂蜜：%s / %s", "container.blast_furnace": "高爐", "container.brewing": "釀造台", "container.cartography_table": "製圖台", "container.chest": "儲物箱", "container.chestDouble": "大型儲物箱", "container.crafter": "合成器", "container.crafting": "合成", "container.creative": "選擇物品", "container.dispenser": "發射器", "container.dropper": "投擲器", "container.enchant": "附魔", "container.enchant.clue": "%s . . . ？", "container.enchant.lapis.many": "%s 個青金石", "container.enchant.lapis.one": "1 個青金石", "container.enchant.level.many": "%s 個附魔等級", "container.enchant.level.one": "1 個附魔等級", "container.enchant.level.requirement": "等級需求：%s", "container.enderchest": "終界箱", "container.furnace": "熔爐", "container.grindstone_title": "修復與解除附魔", "container.hopper": "漏斗", "container.inventory": "物品欄", "container.isLocked": "%s 已鎖上！", "container.lectern": "講台", "container.loom": "紡織機", "container.repair": "修復與命名", "container.repair.cost": "附魔成本：%1$s", "container.repair.expensive": "太貴了！", "container.shulkerBox": "界伏盒", "container.shulkerBox.itemCount": "%s ×%s", "container.shulkerBox.more": "還有 %s 件物品...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "煙燻爐", "container.spectatorCantOpen": "無法開啟。戰利品尚未生成。", "container.stonecutter": "切石機", "container.upgrade": "升級裝備", "container.upgrade.error_tooltip": "物品無法使用此方式升級", "container.upgrade.missing_template_tooltip": "放置鍛造模板", "controls.keybinds": "按鍵綁定...", "controls.keybinds.duplicateKeybinds": "此按鍵也用於：\n%s", "controls.keybinds.title": "按鍵綁定", "controls.reset": "重設", "controls.resetAll": "重設按鍵", "controls.title": "按鍵設定", "createWorld.customize.buffet.biome": "請選擇一種生態域", "createWorld.customize.buffet.title": "單一自訂生態域", "createWorld.customize.flat.height": "高度", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "底層 - %s", "createWorld.customize.flat.layer.top": "頂層 - %s", "createWorld.customize.flat.removeLayer": "移除地層", "createWorld.customize.flat.tile": "地層組成", "createWorld.customize.flat.title": "自訂超平坦世界", "createWorld.customize.presets": "方案集", "createWorld.customize.presets.list": "您也可以選擇預先設定的地形！", "createWorld.customize.presets.select": "使用此方案", "createWorld.customize.presets.share": "想與人分享您的地形方案嗎？請使用下方的文字框！", "createWorld.customize.presets.title": "選擇一種方案", "createWorld.preparing": "正在準備建立世界...", "createWorld.tab.game.title": "遊戲", "createWorld.tab.more.title": "更多", "createWorld.tab.world.title": "世界", "credits_and_attribution.button.attribution": "著作權", "credits_and_attribution.button.credits": "製作人員名單", "credits_and_attribution.button.licenses": "授權條款", "credits_and_attribution.screen.title": "鳴謝", "dataPack.bundle.description": "啟用實驗性束口袋物品", "dataPack.bundle.name": "束口袋", "dataPack.locator_bar.description": "在多人遊戲中顯示其他玩家的方位", "dataPack.locator_bar.name": "定位條", "dataPack.minecart_improvements.description": "改進礦車的移動", "dataPack.minecart_improvements.name": "礦車改進", "dataPack.redstone_experiments.description": "實驗性紅石變更", "dataPack.redstone_experiments.name": "紅石實驗性內容", "dataPack.title": "選擇資料包", "dataPack.trade_rebalance.description": "新版村民交易系統", "dataPack.trade_rebalance.name": "重新平衡村民交易", "dataPack.update_1_20.description": "Minecraft 1.20 的新功能及內容", "dataPack.update_1_20.name": "1.20 更新", "dataPack.update_1_21.description": "Minecraft 1.21 的新功能與內容", "dataPack.update_1_21.name": "1.21 更新", "dataPack.validation.back": "返回", "dataPack.validation.failed": "資料包驗證失敗！", "dataPack.validation.reset": "重設為預設值", "dataPack.validation.working": "正在驗證所選的資料包...", "dataPack.vanilla.description": "Minecraft 預設的資料包", "dataPack.vanilla.name": "預設", "dataPack.winter_drop.description": "冬季小更新的新功能與內容", "dataPack.winter_drop.name": "冬季小更新", "datapackFailure.safeMode": "安全模式", "datapackFailure.safeMode.failed.description": "此世界含有無效或毀損的儲存資料。", "datapackFailure.safeMode.failed.title": "無法在安全模式下載入世界。", "datapackFailure.title": "目前選擇的資料包中出現錯誤，導致世界無法載入。\n您可以嘗試只載入原版資料包（「安全模式」）或是回到標題畫面手動修復。", "death.attack.anvil": "%1$s 被墜落下來的鐵砧壓扁了", "death.attack.anvil.player": "%1$s 在與 %2$s 戰鬥時被落下的鐵砧壓扁", "death.attack.arrow": "%1$s 被 %2$s 射殺了", "death.attack.arrow.item": "%1$s 被 %2$s 用 %3$s 射殺", "death.attack.badRespawnPoint.link": "刻意的遊戲設計", "death.attack.badRespawnPoint.message": "%1$s 被 %2$s 殺死了", "death.attack.cactus": "%1$s 被仙人掌刺死了", "death.attack.cactus.player": "%1$s 在試圖逃離 %2$s 時被仙人掌刺死了", "death.attack.cramming": "%1$s 遭到擠壓致死", "death.attack.cramming.player": "%1$s 遭到 %2$s 擠壓致死", "death.attack.dragonBreath": "%1$s 被龍之吐息烤熟了", "death.attack.dragonBreath.player": "%1$s 被 %2$s 的龍之吐息烤熟了", "death.attack.drown": "%1$s 溺死了", "death.attack.drown.player": "%1$s 在試圖逃離 %2$s 時在水中溺斃", "death.attack.dryout": "%1$s 脫水而死", "death.attack.dryout.player": "%1$s 在試圖逃離 %2$s 時脫水而死", "death.attack.even_more_magic": "%1$s 被深不可測的力量殺死了", "death.attack.explosion": "%1$s 被炸飛了", "death.attack.explosion.player": "%1$s 被 %2$s 炸死了", "death.attack.explosion.player.item": "%1$s 被 %2$s 用 %3$s 炸死", "death.attack.fall": "%1$s 以為能安然無恙的著地", "death.attack.fall.player": "%1$s 在試圖逃離 %2$s 時失足墜地", "death.attack.fallingBlock": "%1$s 被墜落下來的方塊壓扁了", "death.attack.fallingBlock.player": "%1$s 在與 %2$s 戰鬥時被落下的方塊壓扁", "death.attack.fallingStalactite": "%1$s 被墜落的鐘乳石刺穿了", "death.attack.fallingStalactite.player": "%1$s 在與 %2$s 戰鬥時被落下的鐘乳石刺穿", "death.attack.fireball": "%1$s 被 %2$s 的火球殺死了", "death.attack.fireball.item": "%1$s 被 %2$s 用 %3$s 打出的火球殺死", "death.attack.fireworks": "%1$s 在爆炸中犧牲了", "death.attack.fireworks.item": "%1$s 在 %2$s 用 %3$s 發射的煙火所產生的爆炸中犧牲了", "death.attack.fireworks.player": "%1$s 在與 %2$s 戰鬥時隨著爆炸逝去", "death.attack.flyIntoWall": "%1$s 體驗了動能", "death.attack.flyIntoWall.player": "%1$s 在試圖逃離 %2$s 時體驗了動能", "death.attack.freeze": "%1$s 凍死了", "death.attack.freeze.player": "%1$s 被 %2$s 凍死了", "death.attack.generic": "%1$s 死亡", "death.attack.generic.player": "%1$s 因 %2$s 而死", "death.attack.genericKill": "%1$s 被殺死了", "death.attack.genericKill.player": "%1$s 在與 %2$s 戰鬥時被殺死了", "death.attack.hotFloor": "%1$s 察覺地面是片熔岩", "death.attack.hotFloor.player": "%1$s 因為 %2$s 而走進了危險地帶", "death.attack.inFire": "%1$s 在火焰中昇天", "death.attack.inFire.player": "%1$s 在與 %2$s 戰鬥時踏入了火中", "death.attack.inWall": "%1$s 在牆壁裡窒息", "death.attack.inWall.player": "%1$s 在與 %2$s 戰鬥時卡進牆裡窒息", "death.attack.indirectMagic": "%1$s 被 %2$s 用魔法殺死", "death.attack.indirectMagic.item": "%1$s 被 %2$s 用 %3$s 殺死", "death.attack.lava": "%1$s 試圖在熔岩中游泳", "death.attack.lava.player": "%1$s 跳入熔岩試圖逃離 %2$s 的追殺", "death.attack.lightningBolt": "%1$s 被閃電擊斃", "death.attack.lightningBolt.player": "%1$s 在與 %2$s 戰鬥時被閃電擊斃", "death.attack.mace_smash": "%1$s 被 %2$s 錘爆了", "death.attack.mace_smash.item": "%1$s 被 %2$s 用 %3$s 錘爆了", "death.attack.magic": "%1$s 被魔法殺死了", "death.attack.magic.player": "%1$s 在試圖逃離 %2$s 時被魔法殺死了", "death.attack.message_too_long": "死亡訊息過長而無法完整送出。非常抱歉！以下為截短後的版本：%s", "death.attack.mob": "%1$s 被 %2$s 殺死了", "death.attack.mob.item": "%1$s 被 %2$s 用 %3$s 殺死", "death.attack.onFire": "%1$s 被燒死了", "death.attack.onFire.item": "%1$s 在與手持 %3$s 的 %2$s 戰鬥時被火焰燒成灰燼", "death.attack.onFire.player": "%1$s 在與 %2$s 戰鬥時被火焰燒成灰燼", "death.attack.outOfWorld": "%1$s 掉到世界外面了", "death.attack.outOfWorld.player": "%1$s 不想和 %2$s 活在同一個世界", "death.attack.outsideBorder": "%1$s 脫離了這個世界", "death.attack.outsideBorder.player": "%1$s 在與 %2$s 戰鬥時脫離了這個世界", "death.attack.player": "%1$s 被 %2$s 殺死了", "death.attack.player.item": "%1$s 被 %2$s 用 %3$s 殺死", "death.attack.sonic_boom": "%1$s 被一道聲波尖嘯抹殺了", "death.attack.sonic_boom.item": "%1$s 在試圖逃離手持 %3$s 的 %2$s 時被一道聲波尖嘯抹殺了", "death.attack.sonic_boom.player": "%1$s 在試圖逃離 %2$s 時被一道聲波尖嘯抹殺了", "death.attack.stalagmite": "%1$s 在石筍上被刺穿", "death.attack.stalagmite.player": "%1$s 在與 %2$s 戰鬥時在石筍上被刺穿", "death.attack.starve": "%1$s 餓死了", "death.attack.starve.player": "%1$s 在與 %2$s 戰鬥時餓死了", "death.attack.sting": "%1$s 被螫死了", "death.attack.sting.item": "%1$s 被 %2$s 以 %3$s 螫死了", "death.attack.sting.player": "%1$s 被 %2$s 螫死了", "death.attack.sweetBerryBush": "%1$s 被甜莓灌木叢刺死了", "death.attack.sweetBerryBush.player": "%1$s 在試圖逃離 %2$s 時被甜莓灌木叢刺死了", "death.attack.thorns": "%1$s 試圖襲擊 %2$s 時被反將一軍", "death.attack.thorns.item": "%1$s 試圖攻擊 %2$s 時死於 %3$s", "death.attack.thrown": "%1$s 被 %2$s 活生生揍死了", "death.attack.thrown.item": "%1$s 被 %2$s 用 %3$s 揍死", "death.attack.trident": "%1$s 被 %2$s 刺穿了", "death.attack.trident.item": "%1$s 被 %2$s 用 %3$s 刺穿了", "death.attack.wither": "%1$s 凋零了", "death.attack.wither.player": "%1$s 在與 %2$s 戰鬥時凋零了", "death.attack.witherSkull": "%1$s 被 %2$s 發射的頭顱射死了", "death.attack.witherSkull.item": "%1$s 被 %2$s 以 %3$s 發射的頭顱射死了", "death.fell.accident.generic": "%1$s 從高處跌落", "death.fell.accident.ladder": "%1$s 從梯子上摔落", "death.fell.accident.other_climbable": "%1$s 在攀爬時摔落", "death.fell.accident.scaffolding": "%1$s 從鷹架上摔落", "death.fell.accident.twisting_vines": "%1$s 從扭曲藤上摔落", "death.fell.accident.vines": "%1$s 從藤蔓上摔落", "death.fell.accident.weeping_vines": "%1$s 從垂泣藤上摔落", "death.fell.assist": "%1$s 被 %2$s 擊落", "death.fell.assist.item": "%1$s 被 %2$s 以 %3$s 擊落", "death.fell.finish": "%1$s 摔傷後被 %2$s 殺了", "death.fell.finish.item": "%1$s 摔傷後被 %2$s 以 %3$s 擊殺", "death.fell.killer": "%1$s 從空中摔落地面", "deathScreen.quit.confirm": "你確定要退出了嗎？", "deathScreen.respawn": "重生", "deathScreen.score": "分數", "deathScreen.score.value": "分數：%s", "deathScreen.spectate": "旁觀世界", "deathScreen.title": "你死了！", "deathScreen.title.hardcore": "遊戲結束！", "deathScreen.titleScreen": "標題畫面", "debug.advanced_tooltips.help": "F3 + H = 進階提示", "debug.advanced_tooltips.off": "進階提示：隱藏", "debug.advanced_tooltips.on": "進階提示：顯示", "debug.chunk_boundaries.help": "F3 + G = 顯示區塊邊界", "debug.chunk_boundaries.off": "區塊邊界：隱藏", "debug.chunk_boundaries.on": "區塊邊界：顯示", "debug.clear_chat.help": "F3 + D = 清除聊天欄", "debug.copy_location.help": "F3 + C = 將位置複製為 /tp 指令，按住 F3 + C 使遊戲崩潰", "debug.copy_location.message": "將位置複製到剪貼簿", "debug.crash.message": "已按住 F3 + C。若未放開按鍵將使遊戲崩潰。", "debug.crash.warning": "遊戲崩潰倒數 %s...", "debug.creative_spectator.error": "無法切換遊戲模式，需要權限", "debug.creative_spectator.help": "F3 + N = 循環切換 先前的遊戲模式 <-> 旁觀者模式", "debug.dump_dynamic_textures": "已將動態紋理儲存至 %s", "debug.dump_dynamic_textures.help": "F3 + S = 傾印動態紋理", "debug.gamemodes.error": "您沒有權限開啟遊戲模式切換器", "debug.gamemodes.help": "F3 + F4 = 開啟遊戲模式切換器", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s 下一個", "debug.help.help": "F3 + Q = 顯示此清單", "debug.help.message": "按鍵設定：", "debug.inspect.client.block": "已將用戶端方塊資料複製到剪貼簿", "debug.inspect.client.entity": "已將用戶端實體資料複製到剪貼簿", "debug.inspect.help": "F3 + I = 將實體或方塊的資料複製到剪貼簿", "debug.inspect.server.block": "已將伺服器端方塊資料複製到剪貼簿", "debug.inspect.server.entity": "已將伺服器端實體資料複製到剪貼簿", "debug.pause.help": "F3 + Esc = 暫停遊戲但不顯示遊戲選單（若遊戲允許暫停）", "debug.pause_focus.help": "F3 + P = 在失焦時暫停", "debug.pause_focus.off": "在失去焦點時暫停：停用", "debug.pause_focus.on": "在失去焦點時暫停：啟用", "debug.prefix": "[除錯]:", "debug.profiling.help": "F3 + L = 開始或停止分析", "debug.profiling.start": "已開始分析 %s 秒。使用 F3 + L 提前停止", "debug.profiling.stop": "分析結束。已將結果儲存至 %s", "debug.reload_chunks.help": "F3 + A = 重新載入區塊", "debug.reload_chunks.message": "正在重新載入所有區塊", "debug.reload_resourcepacks.help": "F3 + T = 重新載入資源包", "debug.reload_resourcepacks.message": "已重新載入資源包", "debug.show_hitboxes.help": "F3 + B = 顯示碰撞箱", "debug.show_hitboxes.off": "碰撞箱：隱藏", "debug.show_hitboxes.on": "碰撞箱：顯示", "debug.version.header": "用戶端版本資訊：", "debug.version.help": "F3 + V = 顯示用戶端版本資訊", "demo.day.1": "試玩版只有五天的遊戲內時間。盡你所能吧！", "demo.day.2": "第二天", "demo.day.3": "第三天", "demo.day.4": "第四天", "demo.day.5": "這是你的最後一天！", "demo.day.6": "你度過了你的第五天。按下 %s 來為你的創作保存一張擷圖。", "demo.day.warning": "你的時間即將結束！", "demo.demoExpired": "試玩時間結束！", "demo.help.buy": "立即購買！", "demo.help.fullWrapped": "你可以在遊戲中試玩五天（約為現實世界的 1 小時 40 分鐘）。在選單的「進度」選項中，可以找到一些有幫助的提示。希望你玩得開心！", "demo.help.inventory": "使用 %1$s 鍵來開啟您的物品欄", "demo.help.jump": "按 %1$s 鍵跳躍", "demo.help.later": "繼續進行遊戲！", "demo.help.movement": "使用 %1$s、%2$s、%3$s、%4$s 鍵與滑鼠來移動", "demo.help.movementMouse": "使用滑鼠以查看四周", "demo.help.movementShort": "按下 %1$s、%2$s、%3$s、%4$s 鍵移動", "demo.help.title": "Minecraft 試玩模式", "demo.remainingTime": "剩餘時間：%s", "demo.reminder": "試玩時間已經結束。請購買遊戲來繼續，或重新開始一個新的世界！", "difficulty.lock.question": "你確定要鎖定這個世界的難易度嗎？ 這個世界的難易度將永久設為%1$s，且無法再被更改。", "difficulty.lock.title": "鎖定世界難易度", "disconnect.endOfStream": "數據傳送終止", "disconnect.exceeded_packet_rate": "由於超過封包速率限制而被踢出遊戲", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "忽略狀態請求", "disconnect.loginFailedInfo": "登入失敗：%s", "disconnect.loginFailedInfo.insufficientPrivileges": "多人遊戲已停用。請檢查您的 Microsoft 帳號設定。", "disconnect.loginFailedInfo.invalidSession": "無效的 session（請嘗試重開您的遊戲與啟動器）", "disconnect.loginFailedInfo.serversUnavailable": "目前無法連線到驗證伺服器。請稍後再試。", "disconnect.loginFailedInfo.userBanned": "您已被禁止遊玩多人遊戲", "disconnect.lost": "失去連線", "disconnect.packetError": "網路協定錯誤", "disconnect.spam": "因為濫發訊息被踢出", "disconnect.timeout": "連線逾時", "disconnect.transfer": "已轉移至另一個伺服器", "disconnect.unknownHost": "未知的主機", "download.pack.failed": "%s 個資源包下載失敗，共 %s 個資源包", "download.pack.progress.bytes": "進度：%s（總大小未知）", "download.pack.progress.percent": "進度：%s%%", "download.pack.title": "正在下載資源包 %s/%s", "editGamerule.default": "預設值：%s", "editGamerule.title": "編輯遊戲規則", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "吸收", "effect.minecraft.bad_omen": "不祥之兆", "effect.minecraft.blindness": "失明", "effect.minecraft.conduit_power": "海靈祝福", "effect.minecraft.darkness": "黑暗", "effect.minecraft.dolphins_grace": "海豚悠游", "effect.minecraft.fire_resistance": "抗火", "effect.minecraft.glowing": "發光", "effect.minecraft.haste": "挖掘加速", "effect.minecraft.health_boost": "生命值提升", "effect.minecraft.hero_of_the_village": "村莊英雄", "effect.minecraft.hunger": "飢餓", "effect.minecraft.infested": "蛀蝕", "effect.minecraft.instant_damage": "立即傷害", "effect.minecraft.instant_health": "立即治療", "effect.minecraft.invisibility": "隱形", "effect.minecraft.jump_boost": "跳躍提升", "effect.minecraft.levitation": "懸浮", "effect.minecraft.luck": "幸運", "effect.minecraft.mining_fatigue": "挖掘疲勞", "effect.minecraft.nausea": "噁心", "effect.minecraft.night_vision": "夜視", "effect.minecraft.oozing": "滲漿", "effect.minecraft.poison": "劇毒", "effect.minecraft.raid_omen": "突襲之兆", "effect.minecraft.regeneration": "回復", "effect.minecraft.resistance": "抗性", "effect.minecraft.saturation": "飽食", "effect.minecraft.slow_falling": "緩降", "effect.minecraft.slowness": "緩速", "effect.minecraft.speed": "加速", "effect.minecraft.strength": "力量", "effect.minecraft.trial_omen": "試煉之兆", "effect.minecraft.unluck": "霉運", "effect.minecraft.water_breathing": "水下呼吸", "effect.minecraft.weakness": "虛弱", "effect.minecraft.weaving": "結網", "effect.minecraft.wind_charged": "蘊風", "effect.minecraft.wither": "凋零", "effect.none": "無效果", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "親水性", "enchantment.minecraft.bane_of_arthropods": "節肢剋星", "enchantment.minecraft.binding_curse": "綁定詛咒", "enchantment.minecraft.blast_protection": "爆炸保護", "enchantment.minecraft.breach": "破甲", "enchantment.minecraft.channeling": "喚雷", "enchantment.minecraft.density": "緻密", "enchantment.minecraft.depth_strider": "深海漫遊", "enchantment.minecraft.efficiency": "效率", "enchantment.minecraft.feather_falling": "輕盈", "enchantment.minecraft.fire_aspect": "燃燒", "enchantment.minecraft.fire_protection": "火焰保護", "enchantment.minecraft.flame": "火焰", "enchantment.minecraft.fortune": "幸運", "enchantment.minecraft.frost_walker": "冰霜行者", "enchantment.minecraft.impaling": "魚叉", "enchantment.minecraft.infinity": "無限", "enchantment.minecraft.knockback": "擊退", "enchantment.minecraft.looting": "掠奪", "enchantment.minecraft.loyalty": "忠誠", "enchantment.minecraft.luck_of_the_sea": "海洋的祝福", "enchantment.minecraft.lure": "魚餌", "enchantment.minecraft.mending": "修補", "enchantment.minecraft.multishot": "分裂箭矢", "enchantment.minecraft.piercing": "貫穿", "enchantment.minecraft.power": "強力", "enchantment.minecraft.projectile_protection": "投射物保護", "enchantment.minecraft.protection": "保護", "enchantment.minecraft.punch": "衝擊", "enchantment.minecraft.quick_charge": "快速上弦", "enchantment.minecraft.respiration": "水中呼吸", "enchantment.minecraft.riptide": "波濤", "enchantment.minecraft.sharpness": "鋒利", "enchantment.minecraft.silk_touch": "絲綢之觸", "enchantment.minecraft.smite": "不死剋星", "enchantment.minecraft.soul_speed": "靈魂疾走", "enchantment.minecraft.sweeping": "橫掃之刃", "enchantment.minecraft.sweeping_edge": "橫掃之刃", "enchantment.minecraft.swift_sneak": "迅捷潛行", "enchantment.minecraft.thorns": "尖刺", "enchantment.minecraft.unbreaking": "耐久", "enchantment.minecraft.vanishing_curse": "消失詛咒", "enchantment.minecraft.wind_burst": "風爆", "entity.minecraft.acacia_boat": "相思木船", "entity.minecraft.acacia_chest_boat": "儲物箱相思木船", "entity.minecraft.allay": "悅靈", "entity.minecraft.area_effect_cloud": "藥水效果雲", "entity.minecraft.armadillo": "犰狳", "entity.minecraft.armor_stand": "盔甲座", "entity.minecraft.arrow": "箭矢", "entity.minecraft.axolotl": "六角恐龍", "entity.minecraft.bamboo_chest_raft": "儲物箱竹筏", "entity.minecraft.bamboo_raft": "竹筏", "entity.minecraft.bat": "蝙蝠", "entity.minecraft.bee": "蜜蜂", "entity.minecraft.birch_boat": "樺木船", "entity.minecraft.birch_chest_boat": "儲物箱樺木船", "entity.minecraft.blaze": "烈焰使者", "entity.minecraft.block_display": "方塊展示實體", "entity.minecraft.boat": "船", "entity.minecraft.bogged": "沼骸", "entity.minecraft.breeze": "旋風使者", "entity.minecraft.breeze_wind_charge": "風彈", "entity.minecraft.camel": "駱駝", "entity.minecraft.cat": "貓", "entity.minecraft.cave_spider": "洞穴蜘蛛", "entity.minecraft.cherry_boat": "櫻花木船", "entity.minecraft.cherry_chest_boat": "儲物箱櫻花木船", "entity.minecraft.chest_boat": "儲物箱船", "entity.minecraft.chest_minecart": "儲物箱礦車", "entity.minecraft.chicken": "雞", "entity.minecraft.cod": "鱈魚", "entity.minecraft.command_block_minecart": "指令方塊礦車", "entity.minecraft.cow": "牛", "entity.minecraft.creaking": "嘎枝", "entity.minecraft.creaking_transient": "嘎枝", "entity.minecraft.creeper": "苦力怕", "entity.minecraft.dark_oak_boat": "黑橡木船", "entity.minecraft.dark_oak_chest_boat": "儲物箱黑橡木船", "entity.minecraft.dolphin": "海豚", "entity.minecraft.donkey": "驢子", "entity.minecraft.dragon_fireball": "龍炎彈", "entity.minecraft.drowned": "沉屍", "entity.minecraft.egg": "拋出的雞蛋", "entity.minecraft.elder_guardian": "遠古深海守衛", "entity.minecraft.end_crystal": "終界水晶", "entity.minecraft.ender_dragon": "終界龍", "entity.minecraft.ender_pearl": "拋出的終界珍珠", "entity.minecraft.enderman": "終界使者", "entity.minecraft.endermite": "終界蟎", "entity.minecraft.evoker": "喚魔者", "entity.minecraft.evoker_fangs": "喚魔者尖牙", "entity.minecraft.experience_bottle": "拋出的經驗瓶", "entity.minecraft.experience_orb": "經驗球", "entity.minecraft.eye_of_ender": "終界之眼", "entity.minecraft.falling_block": "掉落的方塊", "entity.minecraft.falling_block_type": "掉落的 %s", "entity.minecraft.fireball": "火球", "entity.minecraft.firework_rocket": "煙火", "entity.minecraft.fishing_bobber": "浮標", "entity.minecraft.fox": "狐狸", "entity.minecraft.frog": "青蛙", "entity.minecraft.furnace_minecart": "熔爐礦車", "entity.minecraft.ghast": "地獄幽靈", "entity.minecraft.giant": "巨人", "entity.minecraft.glow_item_frame": "螢光物品展示框", "entity.minecraft.glow_squid": "螢光魷魚", "entity.minecraft.goat": "山羊", "entity.minecraft.guardian": "深海守衛", "entity.minecraft.happy_ghast": "快樂幽靈", "entity.minecraft.hoglin": "豬布獸", "entity.minecraft.hopper_minecart": "漏斗礦車", "entity.minecraft.horse": "馬", "entity.minecraft.husk": "屍殼", "entity.minecraft.illusioner": "幻術師", "entity.minecraft.interaction": "互動實體", "entity.minecraft.iron_golem": "鐵魔像", "entity.minecraft.item": "物品", "entity.minecraft.item_display": "物品展示實體", "entity.minecraft.item_frame": "物品展示框", "entity.minecraft.jungle_boat": "叢林木船", "entity.minecraft.jungle_chest_boat": "儲物箱叢林木船", "entity.minecraft.killer_bunny": "殺手兔", "entity.minecraft.leash_knot": "拴繩", "entity.minecraft.lightning_bolt": "閃電電流", "entity.minecraft.lingering_potion": "滯留藥水", "entity.minecraft.llama": "駱馬", "entity.minecraft.llama_spit": "駱馬唾液", "entity.minecraft.magma_cube": "岩漿立方怪", "entity.minecraft.mangrove_boat": "紅樹林木船", "entity.minecraft.mangrove_chest_boat": "儲物箱紅樹林木船", "entity.minecraft.marker": "標記", "entity.minecraft.minecart": "礦車", "entity.minecraft.mooshroom": "哞菇", "entity.minecraft.mule": "騾子", "entity.minecraft.oak_boat": "橡木船", "entity.minecraft.oak_chest_boat": "儲物箱橡木船", "entity.minecraft.ocelot": "山貓", "entity.minecraft.ominous_item_spawner": "不祥物品生成器", "entity.minecraft.painting": "繪畫", "entity.minecraft.pale_oak_boat": "蒼白橡木船", "entity.minecraft.pale_oak_chest_boat": "儲物箱蒼白橡木船", "entity.minecraft.panda": "貓熊", "entity.minecraft.parrot": "鸚鵡", "entity.minecraft.phantom": "夜魅", "entity.minecraft.pig": "豬", "entity.minecraft.piglin": "豬布林", "entity.minecraft.piglin_brute": "豬布林蠻兵", "entity.minecraft.pillager": "掠奪者", "entity.minecraft.player": "玩家", "entity.minecraft.polar_bear": "北極熊", "entity.minecraft.potion": "藥水", "entity.minecraft.pufferfish": "河豚", "entity.minecraft.rabbit": "兔子", "entity.minecraft.ravager": "劫毀獸", "entity.minecraft.salmon": "鮭魚", "entity.minecraft.sheep": "綿羊", "entity.minecraft.shulker": "界伏蚌", "entity.minecraft.shulker_bullet": "界伏彈", "entity.minecraft.silverfish": "蠹魚", "entity.minecraft.skeleton": "骷髏", "entity.minecraft.skeleton_horse": "骷髏馬", "entity.minecraft.slime": "史萊姆", "entity.minecraft.small_fireball": "小火球", "entity.minecraft.sniffer": "嗅探獸", "entity.minecraft.snow_golem": "雪人", "entity.minecraft.snowball": "雪球", "entity.minecraft.spawner_minecart": "生怪磚礦車", "entity.minecraft.spectral_arrow": "追跡之箭", "entity.minecraft.spider": "蜘蛛", "entity.minecraft.splash_potion": "飛濺藥水", "entity.minecraft.spruce_boat": "杉木船", "entity.minecraft.spruce_chest_boat": "儲物箱杉木船", "entity.minecraft.squid": "魷魚", "entity.minecraft.stray": "流髑", "entity.minecraft.strider": "熾足獸", "entity.minecraft.tadpole": "蝌蚪", "entity.minecraft.text_display": "文字展示實體", "entity.minecraft.tnt": "點燃的 TNT", "entity.minecraft.tnt_minecart": "TNT 礦車", "entity.minecraft.trader_llama": "商駝", "entity.minecraft.trident": "三叉戟", "entity.minecraft.tropical_fish": "熱帶魚", "entity.minecraft.tropical_fish.predefined.0": "海葵魚", "entity.minecraft.tropical_fish.predefined.1": "長鼻高鰭刺尾魚", "entity.minecraft.tropical_fish.predefined.10": "角蝶魚", "entity.minecraft.tropical_fish.predefined.11": "華麗蝴蝶魚", "entity.minecraft.tropical_fish.predefined.12": "鸚哥魚", "entity.minecraft.tropical_fish.predefined.13": "額斑刺蝶魚", "entity.minecraft.tropical_fish.predefined.14": "赤慈鯛", "entity.minecraft.tropical_fish.predefined.15": "紅唇真蛇䲁", "entity.minecraft.tropical_fish.predefined.16": "西大西洋笛鯛", "entity.minecraft.tropical_fish.predefined.17": "馬鮁", "entity.minecraft.tropical_fish.predefined.18": "白條雙鋸魚", "entity.minecraft.tropical_fish.predefined.19": "鱗魨", "entity.minecraft.tropical_fish.predefined.2": "擬刺尾鯛", "entity.minecraft.tropical_fish.predefined.20": "高鰭鸚哥魚", "entity.minecraft.tropical_fish.predefined.21": "黃高鰭刺尾魚", "entity.minecraft.tropical_fish.predefined.3": "蝴蝶魚", "entity.minecraft.tropical_fish.predefined.4": "慈鯛", "entity.minecraft.tropical_fish.predefined.5": "小丑魚", "entity.minecraft.tropical_fish.predefined.6": "五彩搏魚", "entity.minecraft.tropical_fish.predefined.7": "紅黃繡雀鯛", "entity.minecraft.tropical_fish.predefined.8": "川紋笛鯛", "entity.minecraft.tropical_fish.predefined.9": "鬚鯛", "entity.minecraft.tropical_fish.type.betty": "貝蒂魚", "entity.minecraft.tropical_fish.type.blockfish": "方塊魚", "entity.minecraft.tropical_fish.type.brinely": "鹽水魚", "entity.minecraft.tropical_fish.type.clayfish": "黏土魚", "entity.minecraft.tropical_fish.type.dasher": "迅泳魚", "entity.minecraft.tropical_fish.type.flopper": "翅翼魚", "entity.minecraft.tropical_fish.type.glitter": "閃魚", "entity.minecraft.tropical_fish.type.kob": "赤羚魚", "entity.minecraft.tropical_fish.type.snooper": "藏礁魚", "entity.minecraft.tropical_fish.type.spotty": "斑點魚", "entity.minecraft.tropical_fish.type.stripey": "條紋魚", "entity.minecraft.tropical_fish.type.sunstreak": "日紋魚", "entity.minecraft.turtle": "海龜", "entity.minecraft.vex": "惱鬼", "entity.minecraft.villager": "村民", "entity.minecraft.villager.armorer": "製甲師", "entity.minecraft.villager.butcher": "屠夫", "entity.minecraft.villager.cartographer": "製圖師", "entity.minecraft.villager.cleric": "神職人員", "entity.minecraft.villager.farmer": "農夫", "entity.minecraft.villager.fisherman": "漁夫", "entity.minecraft.villager.fletcher": "製箭師", "entity.minecraft.villager.leatherworker": "皮匠", "entity.minecraft.villager.librarian": "圖書管理員", "entity.minecraft.villager.mason": "石匠", "entity.minecraft.villager.nitwit": "傻子", "entity.minecraft.villager.none": "村民", "entity.minecraft.villager.shepherd": "牧羊人", "entity.minecraft.villager.toolsmith": "工具匠", "entity.minecraft.villager.weaponsmith": "武器匠", "entity.minecraft.vindicator": "衛道士", "entity.minecraft.wandering_trader": "流浪商人", "entity.minecraft.warden": "伏守者", "entity.minecraft.wind_charge": "風彈", "entity.minecraft.witch": "女巫", "entity.minecraft.wither": "凋零怪", "entity.minecraft.wither_skeleton": "凋零骷髏", "entity.minecraft.wither_skull": "凋零頭顱", "entity.minecraft.wolf": "狼", "entity.minecraft.zoglin": "豬屍獸", "entity.minecraft.zombie": "殭屍", "entity.minecraft.zombie_horse": "殭屍馬", "entity.minecraft.zombie_villager": "殭屍村民", "entity.minecraft.zombified_piglin": "殭屍化豬布林", "entity.not_summonable": "無法召喚類型為 %s 的實體", "event.minecraft.raid": "突襲", "event.minecraft.raid.defeat": "失敗", "event.minecraft.raid.defeat.full": "突襲 - 失敗", "event.minecraft.raid.raiders_remaining": "剩餘敵人：%s", "event.minecraft.raid.victory": "勝利", "event.minecraft.raid.victory.full": "突襲 - 勝利", "filled_map.buried_treasure": "藏寶圖", "filled_map.explorer_jungle": "叢林探險家地圖", "filled_map.explorer_swamp": "沼澤探險家地圖", "filled_map.id": "ID #%s", "filled_map.level": "（縮放等級 %s/%s）", "filled_map.locked": "已鎖定", "filled_map.mansion": "林地探險家地圖", "filled_map.monument": "海洋探險家地圖", "filled_map.scale": "比例尺 1:%s", "filled_map.trial_chambers": "試煉探險家地圖", "filled_map.unknown": "未知的地圖", "filled_map.village_desert": "沙漠村莊地圖", "filled_map.village_plains": "平原村莊地圖", "filled_map.village_savanna": "莽原村莊地圖", "filled_map.village_snowy": "雪原村莊地圖", "filled_map.village_taiga": "針葉林村莊地圖", "flat_world_preset.minecraft.bottomless_pit": "無底深淵", "flat_world_preset.minecraft.classic_flat": "經典平坦", "flat_world_preset.minecraft.desert": "沙漠", "flat_world_preset.minecraft.overworld": "主世界", "flat_world_preset.minecraft.redstone_ready": "為紅石準備", "flat_world_preset.minecraft.snowy_kingdom": "雪之王國", "flat_world_preset.minecraft.the_void": "虛空", "flat_world_preset.minecraft.tunnelers_dream": "隧道工人的夢想", "flat_world_preset.minecraft.water_world": "水世界", "flat_world_preset.unknown": "???", "gameMode.adventure": "冒險模式", "gameMode.changed": "你的遊戲模式已被更新為 %s", "gameMode.creative": "創造模式", "gameMode.hardcore": "極限模式", "gameMode.spectator": "旁觀者模式", "gameMode.survival": "生存模式", "gamerule.allowFireTicksAwayFromPlayer": "允許火在遠離玩家處蔓延", "gamerule.allowFireTicksAwayFromPlayer.description": "控制火和熔岩是否能在距離任何玩家 8 個區塊之外繼續蔓延", "gamerule.announceAdvancements": "進度通知", "gamerule.blockExplosionDropDecay": "在與方塊互動的爆炸中，部分方塊不會掉落成戰利品", "gamerule.blockExplosionDropDecay.description": "在與方塊相互作用引發的爆炸中，會遺失部分被摧毀方塊的掉落物。", "gamerule.category.chat": "聊天欄", "gamerule.category.drops": "掉落", "gamerule.category.misc": "雜項", "gamerule.category.mobs": "生物", "gamerule.category.player": "玩家", "gamerule.category.spawning": "生成", "gamerule.category.updates": "世界更新", "gamerule.commandBlockOutput": "記錄指令方塊輸出", "gamerule.commandModificationBlockLimit": "指令修改方塊數量限制", "gamerule.commandModificationBlockLimit.description": "單個指令（例如 fill 或是 clone）最多能修改的方塊數量。", "gamerule.disableElytraMovementCheck": "停用鞘翅移動檢測", "gamerule.disablePlayerMovementCheck": "停用玩家移動檢測", "gamerule.disableRaids": "停用突襲", "gamerule.doDaylightCycle": "日夜交替", "gamerule.doEntityDrops": "掉落實體裝備", "gamerule.doEntityDrops.description": "控制自礦車（包括物品欄）、物品展示框、船等的掉落物。", "gamerule.doFireTick": "更新火焰", "gamerule.doImmediateRespawn": "立即重生", "gamerule.doInsomnia": "生成夜魅", "gamerule.doLimitedCrafting": "需要配方才能合成", "gamerule.doLimitedCrafting.description": "若啟用，玩家將只能合成已解鎖的配方。", "gamerule.doMobLoot": "掉落生物戰利品", "gamerule.doMobLoot.description": "控制生物資源掉落，包含經驗球。", "gamerule.doMobSpawning": "生成生物", "gamerule.doMobSpawning.description": "部分實體可能採用不同的規則。", "gamerule.doPatrolSpawning": "生成掠奪者巡邏隊", "gamerule.doTileDrops": "掉落方塊", "gamerule.doTileDrops.description": "控制方塊資源掉落，包含經驗球。", "gamerule.doTraderSpawning": "生成流浪商人", "gamerule.doVinesSpread": "藤蔓蔓延", "gamerule.doVinesSpread.description": "控制藤蔓方塊是否能隨機向相鄰的方塊蔓延。這不會影響其他類型的藤蔓方塊（例如垂泣藤及扭曲藤等）。", "gamerule.doWardenSpawning": "生成伏守者", "gamerule.doWeatherCycle": "更新天氣", "gamerule.drowningDamage": "造成溺水傷害", "gamerule.enderPearlsVanishOnDeath": "拋出的終界珍珠在死亡時消失", "gamerule.enderPearlsVanishOnDeath.description": "玩家拋出的終界珍珠是否在玩家死亡時消失。", "gamerule.entitiesWithPassengersCanUsePortals": "被騎乘的實體是否能使用傳送門", "gamerule.entitiesWithPassengersCanUsePortals.description": "允許被騎乘的實體透過地獄傳送門、終界傳送門和終界折躍門傳送。", "gamerule.fallDamage": "造成摔落傷害", "gamerule.fireDamage": "造成火焰傷害", "gamerule.forgiveDeadPlayers": "原諒死者", "gamerule.forgiveDeadPlayers.description": "當目標玩家在附近死亡後，被其激怒的中立生物將息怒。", "gamerule.freezeDamage": "造成冰凍傷害", "gamerule.globalSoundEvents": "全域聲音事件", "gamerule.globalSoundEvents.description": "當特定遊戲事件發生時，例如生成 Boss，聲音可以在任何地方被聽見。", "gamerule.keepInventory": "死亡後保留物品欄", "gamerule.lavaSourceConversion": "流動熔岩轉化成熔岩源", "gamerule.lavaSourceConversion.description": "流動熔岩在被兩個熔岩源包圍時，會轉化成熔岩源。", "gamerule.locatorBar": "啟用玩家定位條", "gamerule.locatorBar.description": "啟用後，畫面上會顯示指示玩家方位的定位條。", "gamerule.logAdminCommands": "記錄管理員指令", "gamerule.maxCommandChainLength": "指令連鎖大小限制", "gamerule.maxCommandChainLength.description": "套用於連鎖型指令方塊及函數。", "gamerule.maxCommandForkCount": "指令的上下文上限", "gamerule.maxCommandForkCount.description": "「execute as」等指令使用的上下文最大數量。", "gamerule.maxEntityCramming": "實體擠壓上限", "gamerule.minecartMaxSpeed": "礦車最大速度", "gamerule.minecartMaxSpeed.description": "礦車在地面上移動的預設速度上限。", "gamerule.mobExplosionDropDecay": "在生物的爆炸中，部分方塊不會掉落成戰利品", "gamerule.mobExplosionDropDecay.description": "在生物引發的爆炸中，會遺失部分被摧毀方塊的掉落物。", "gamerule.mobGriefing": "允許生物的破壞行為", "gamerule.naturalRegeneration": "自然回血", "gamerule.playersNetherPortalCreativeDelay": "創造模式玩家使用地獄傳送門的等待時間", "gamerule.playersNetherPortalCreativeDelay.description": "創造模式玩家使用地獄傳送門前往其他維度前，需要站在其中的等待時間（以刻為單位）。", "gamerule.playersNetherPortalDefaultDelay": "非創造模式玩家使用地獄傳送門的等待時間", "gamerule.playersNetherPortalDefaultDelay.description": "非創造模式玩家使用地獄傳送門前往其他維度前，需要站在其中的等待時間（以刻為單位）。", "gamerule.playersSleepingPercentage": "睡眠比例", "gamerule.playersSleepingPercentage.description": "跳過夜晚所需的入睡玩家比例。", "gamerule.projectilesCanBreakBlocks": "投射物是否能破壞方塊", "gamerule.projectilesCanBreakBlocks.description": "控制投射物是否能破壞可被其破壞的方塊。", "gamerule.randomTickSpeed": "隨機刻速率", "gamerule.reducedDebugInfo": "簡化除錯資訊", "gamerule.reducedDebugInfo.description": "限制除錯畫面的內容。", "gamerule.sendCommandFeedback": "回傳指令回饋", "gamerule.showDeathMessages": "顯示死亡訊息", "gamerule.snowAccumulationHeight": "積雪厚度", "gamerule.snowAccumulationHeight.description": "降雪時，地面上的積雪最多累積至此處指定的層數。", "gamerule.spawnChunkRadius": "重生區塊半徑", "gamerule.spawnChunkRadius.description": "主世界重生點周圍維持載入的區塊數量。", "gamerule.spawnRadius": "重生點半徑", "gamerule.spawnRadius.description": "控制玩家能夠生成的重生點範圍區域大小。", "gamerule.spectatorsGenerateChunks": "允許旁觀者生成地形", "gamerule.tntExplodes": "允許 TNT 被點燃並爆炸", "gamerule.tntExplosionDropDecay": "在 TNT 的爆炸中，部分方塊不會掉落成戰利品", "gamerule.tntExplosionDropDecay.description": "在 TNT 引發的爆炸中，會遺失部分被摧毀方塊的掉落物。", "gamerule.universalAnger": "無差別憤怒", "gamerule.universalAnger.description": "被激怒的中立生物將攻擊任何附近的玩家，而不限於激怒他們的那位。停用「forgiveDeadPlayers」可以有更好的效果。", "gamerule.waterSourceConversion": "流動水轉化成水源", "gamerule.waterSourceConversion.description": "流動水在被兩個水源包圍時，會轉化成水源。", "generator.custom": "自訂", "generator.customized": "舊版自訂", "generator.minecraft.amplified": "巨大化世界", "generator.minecraft.amplified.info": "注意：這只是為了好玩，需要效能良好的電腦。", "generator.minecraft.debug_all_block_states": "除錯模式", "generator.minecraft.flat": "超平坦", "generator.minecraft.large_biomes": "大型生態域", "generator.minecraft.normal": "預設", "generator.minecraft.single_biome_surface": "單一生態域", "generator.single_biome_caves": "洞穴", "generator.single_biome_floating_islands": "浮空島嶼", "gui.abuseReport.attestation": "提交這份檢舉，即表示您已確認提供了盡可能準確且完整的資訊。", "gui.abuseReport.comments": "留言", "gui.abuseReport.describe": "分享詳細資料有助我們做出更準確、清楚的判斷。", "gui.abuseReport.discard.content": "如果離開，這份檢舉和留言將會遺失。\n您確定仍要離開嗎？", "gui.abuseReport.discard.discard": "離開並捨棄檢舉", "gui.abuseReport.discard.draft": "儲存為草稿", "gui.abuseReport.discard.return": "繼續編輯", "gui.abuseReport.discard.title": "捨棄檢舉和留言？", "gui.abuseReport.draft.content": "您是要繼續編輯現有的檢舉報告，還是要捨棄並建立新報告？", "gui.abuseReport.draft.discard": "捨棄", "gui.abuseReport.draft.edit": "繼續編輯", "gui.abuseReport.draft.quittotitle.content": "您要繼續編輯或是捨棄？", "gui.abuseReport.draft.quittotitle.title": "您有一份聊天訊息檢舉草稿，如果離開，將遺失您的草稿", "gui.abuseReport.draft.title": "是否編輯聊天訊息檢舉草稿？", "gui.abuseReport.error.title": "提交檢舉時發生問題", "gui.abuseReport.message": "您在哪裡觀察到不當行為？\n這有助於我們調查您所提交的個案。", "gui.abuseReport.more_comments": "請說明事情經過：", "gui.abuseReport.name.comment_box_label": "請說明你檢舉這個名稱的原因：", "gui.abuseReport.name.reporting": "您正在檢舉 \"%s\"。", "gui.abuseReport.name.title": "檢舉不當玩家名稱", "gui.abuseReport.observed_what": "請說明檢舉理由。", "gui.abuseReport.read_info": "深入了解檢舉機制", "gui.abuseReport.reason.alcohol_tobacco_drugs": "毒品或酒精", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "有人鼓勵他人參與非法用藥行為，或唆使未達法規年齡者飲酒。", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "兒童性剝削或虐待", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "有人談論或以其他方式推廣涉及孩童的猥褻行為。", "gui.abuseReport.reason.defamation_impersonation_false_information": "誹謗", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "有人以利用或誤導他人為目的，損害你或他人的名譽，例如散布虛假資訊。", "gui.abuseReport.reason.description": "描述：", "gui.abuseReport.reason.false_reporting": "不實檢舉", "gui.abuseReport.reason.generic": "我想要檢舉此玩家", "gui.abuseReport.reason.generic.description": "此玩家的行為使我惱怒或反感。", "gui.abuseReport.reason.harassment_or_bullying": "騷擾或霸凌", "gui.abuseReport.reason.harassment_or_bullying.description": "有人羞辱、攻擊、霸凌你或其他人。包含違反您或其他人意願，不斷聯絡騷擾或是散布個人資料（即「肉搜」）。", "gui.abuseReport.reason.hate_speech": "仇恨言論", "gui.abuseReport.reason.hate_speech.description": "有人基於身分（如宗教、種族或性相關）攻擊您或其他玩家。", "gui.abuseReport.reason.imminent_harm": "恐嚇加害他人", "gui.abuseReport.reason.imminent_harm.description": "有人恐嚇會在現實生活中加害您或其他人。", "gui.abuseReport.reason.narration": "%s：%s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "未經同意的親密圖像", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "有人談論、分享或以其他方式推廣私密、親密的影像。", "gui.abuseReport.reason.self_harm_or_suicide": "自殘或自殺", "gui.abuseReport.reason.self_harm_or_suicide.description": "有人談論或威脅要在現實生活中自殘。", "gui.abuseReport.reason.sexually_inappropriate": "性相關不當內容", "gui.abuseReport.reason.sexually_inappropriate.description": "外觀圖案與性行為、性器官和性暴力有關。", "gui.abuseReport.reason.terrorism_or_violent_extremism": "恐怖主義或暴力極端主義", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "有人因為政治、宗教、思想等其他原因，談論、推廣或威脅實行恐怖主義或極端暴力。", "gui.abuseReport.reason.title": "選擇檢舉類別", "gui.abuseReport.report_sent_msg": "謝謝，您已成功向我們提出檢舉！\n\n我們的團隊將會儘快進行審核。", "gui.abuseReport.select_reason": "選擇檢舉類別", "gui.abuseReport.send": "提交檢舉", "gui.abuseReport.send.comment_too_long": "請縮短留言", "gui.abuseReport.send.error_message": "提交檢舉時回傳了錯誤：\n「%s」", "gui.abuseReport.send.generic_error": "提交檢舉時發生了非預期的錯誤。", "gui.abuseReport.send.http_error": "提交檢舉時發生了非預期的 HTTP 錯誤。", "gui.abuseReport.send.json_error": "提交檢舉時遭遇格式錯誤的訊息承載。", "gui.abuseReport.send.no_reason": "請選擇檢舉類別", "gui.abuseReport.send.not_attested": "請先閱讀上方文字並勾選核取方塊後，再提交檢舉", "gui.abuseReport.send.service_unavailable": "無法存取檢舉服務。請檢查您是否已連上網路，並重新嘗試。", "gui.abuseReport.sending.title": "正在提交您的檢舉...", "gui.abuseReport.sent.title": "已送出檢舉", "gui.abuseReport.skin.title": "檢舉玩家外觀", "gui.abuseReport.title": "檢舉玩家", "gui.abuseReport.type.chat": "聊天訊息", "gui.abuseReport.type.name": "玩家名稱", "gui.abuseReport.type.skin": "玩家外觀", "gui.acknowledge": "了解", "gui.advancements": "進度", "gui.all": "全部", "gui.back": "返回", "gui.banned.description": "%s\n\n%s\n\n透過以下連結了解更多：%s", "gui.banned.description.permanent": "您的帳號已被永久封鎖，您無法在線上模式遊玩或加入 Realms。", "gui.banned.description.reason": "我們最近收到了對您不當行為的檢舉。我們的仲裁者已審查此案並認定為「%s」，已違反 Minecraft 社群準則。", "gui.banned.description.reason_id": "代碼：%s", "gui.banned.description.reason_id_message": "代碼：%s - %s", "gui.banned.description.temporary": "%s 在此之前，您無法使用線上模式或加入 Realms。", "gui.banned.description.temporary.duration": "您的帳號已被暫時停權，將於 %s 後恢復。", "gui.banned.description.unknownreason": "我們最近收到了對您不當行為的檢舉。我們的仲裁者已審查此案，並認定其違反 Minecraft 社群準則。", "gui.banned.name.description": "你現在使用的名稱（%s）違反社群準則。你可以遊玩單人遊戲，但你需要更改你的名稱以遊玩線上遊戲。\n\n前往以下連結以了解更多或提交個案複查：%s", "gui.banned.name.title": "在多人遊戲中不允許使用該名稱", "gui.banned.reason.defamation_impersonation_false_information": "假冒或分享資訊以利用或誤導他人", "gui.banned.reason.drugs": "推銷非法藥品", "gui.banned.reason.extreme_violence_or_gore": "描述現實世界的暴力或血腥場面", "gui.banned.reason.false_reporting": "過多虛假或不實檢舉", "gui.banned.reason.fraud": "欺騙行為以獲得或使用內容", "gui.banned.reason.generic_violation": "違反社群準則", "gui.banned.reason.harassment_or_bullying": "具針對性、有害地使用侮辱性語言", "gui.banned.reason.hate_speech": "仇恨或歧視言論", "gui.banned.reason.hate_terrorism_notorious_figure": "推廣仇恨團體、恐怖組織或不法分子", "gui.banned.reason.imminent_harm_to_person_or_property": "意圖在現實世界中造成人身或財產傷害", "gui.banned.reason.nudity_or_pornography": "展示淫穢或色情材料", "gui.banned.reason.sexually_inappropriate": "性相關話題或內容", "gui.banned.reason.spam_or_advertising": "濫發訊息或廣告宣傳", "gui.banned.skin.description": "你現在使用的外觀違反社群準則。你可以使用預設外觀，或是使用一個新的外觀遊玩。\n\n前往以下連結以了解更多或提交個案複查：%s", "gui.banned.skin.title": "該外觀不被允許使用", "gui.banned.title.permanent": "帳號已被永久封鎖", "gui.banned.title.temporary": "帳號已暫時停權", "gui.cancel": "取消", "gui.chatReport.comments": "留言", "gui.chatReport.describe": "分享詳細資料有助我們做出更準確、清楚的判斷。", "gui.chatReport.discard.content": "如果離開，這份檢舉和留言將會遺失。\n您確定仍要離開嗎？", "gui.chatReport.discard.discard": "離開並捨棄檢舉", "gui.chatReport.discard.draft": "儲存為草稿", "gui.chatReport.discard.return": "繼續編輯", "gui.chatReport.discard.title": "捨棄檢舉及留言？", "gui.chatReport.draft.content": "您是要繼續編輯現有的檢舉報告，還是要捨棄並建立新報告？", "gui.chatReport.draft.discard": "捨棄", "gui.chatReport.draft.edit": "繼續編輯", "gui.chatReport.draft.quittotitle.content": "您要繼續編輯還是捨棄？", "gui.chatReport.draft.quittotitle.title": "您有一份聊天訊息檢舉草稿，如果離開，將遺失您的草稿", "gui.chatReport.draft.title": "是否編輯聊天訊息檢舉草稿？", "gui.chatReport.more_comments": "請說明事情經過：", "gui.chatReport.observed_what": "請說明檢舉理由。", "gui.chatReport.read_info": "深入了解檢舉機制", "gui.chatReport.report_sent_msg": "謝謝，您已成功向我們提出檢舉！\n\n我們的團隊將會儘快進行審核。", "gui.chatReport.select_chat": "選擇要檢舉的聊天訊息", "gui.chatReport.select_reason": "選擇檢舉類別", "gui.chatReport.selected_chat": "已選取 %s 則待檢舉訊息", "gui.chatReport.send": "傳送檢舉", "gui.chatReport.send.comments_too_long": "請縮短留言", "gui.chatReport.send.no_reason": "請選擇檢舉類別", "gui.chatReport.send.no_reported_messages": "請選擇至少一則要檢舉的聊天訊息", "gui.chatReport.send.too_many_messages": "嘗試在檢舉中選取過多訊息", "gui.chatReport.title": "檢舉玩家聊天", "gui.chatSelection.context": "本訊息前後的其他訊息將會被採用，並作為額外的對話情境資訊使用", "gui.chatSelection.fold": "已隱藏 %s 則訊息", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s 加入了聊天", "gui.chatSelection.message.narrate": "%1$s 在 %3$s 說：%2$s", "gui.chatSelection.selected": "已選取 %2$s 則中的 %1$s 則訊息", "gui.chatSelection.title": "選取要檢舉的聊天訊息", "gui.continue": "繼續", "gui.copy_link_to_clipboard": "複製連結到剪貼簿", "gui.days": "%s 天", "gui.done": "完成", "gui.down": "下", "gui.entity_tooltip.type": "類型：%s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "已拒絕 %s 個檔案", "gui.fileDropFailure.title": "無法新增檔案", "gui.hours": "%s 小時", "gui.loadingMinecraft": "正在載入 Minecraft", "gui.minutes": "%s 分鐘", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s 按鈕", "gui.narrate.editBox": "%s 編輯欄：%s", "gui.narrate.slider": "%s 滑桿", "gui.narrate.tab": "%s 頁籤", "gui.no": "否", "gui.none": "無", "gui.ok": "確定", "gui.open_report_dir": "開啟報告資料夾", "gui.proceed": "繼續", "gui.recipebook.moreRecipes": "按下右鍵以查看更多", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "搜尋...", "gui.recipebook.toggleRecipes.all": "已列出所有項目", "gui.recipebook.toggleRecipes.blastable": "已列出高爐可熔煉項目", "gui.recipebook.toggleRecipes.craftable": "已列出可合成項目", "gui.recipebook.toggleRecipes.smeltable": "已列出可熔煉項目", "gui.recipebook.toggleRecipes.smokable": "已列出可燻製項目", "gui.report_to_server": "向伺服器回報", "gui.socialInteractions.blocking_hint": "以 Microsoft 帳號管理", "gui.socialInteractions.empty_blocked": "聊天室中沒有已封鎖的玩家", "gui.socialInteractions.empty_hidden": "聊天室中沒有被隱藏的玩家", "gui.socialInteractions.hidden_in_chat": "將會隱藏 %s 的聊天訊息", "gui.socialInteractions.hide": "在聊天室中隱藏", "gui.socialInteractions.narration.hide": "隱藏來自 %s 的訊息", "gui.socialInteractions.narration.report": "檢舉玩家 %s", "gui.socialInteractions.narration.show": "顯示來自 %s 的訊息", "gui.socialInteractions.report": "檢舉", "gui.socialInteractions.search_empty": "找不到使用此名稱的玩家", "gui.socialInteractions.search_hint": "搜尋...", "gui.socialInteractions.server_label.multiple": "%s - %s 位玩家", "gui.socialInteractions.server_label.single": "%s - %s 位玩家", "gui.socialInteractions.show": "在聊天室中顯示", "gui.socialInteractions.shown_in_chat": "將會顯示 %s 的聊天訊息", "gui.socialInteractions.status_blocked": "已封鎖", "gui.socialInteractions.status_blocked_offline": "已封鎖 - 離線", "gui.socialInteractions.status_hidden": "隱藏", "gui.socialInteractions.status_hidden_offline": "隱藏 - 離線", "gui.socialInteractions.status_offline": "離線", "gui.socialInteractions.tab_all": "全部", "gui.socialInteractions.tab_blocked": "已封鎖", "gui.socialInteractions.tab_hidden": "隱藏", "gui.socialInteractions.title": "社群交流", "gui.socialInteractions.tooltip.hide": "隱藏訊息", "gui.socialInteractions.tooltip.report": "檢舉玩家", "gui.socialInteractions.tooltip.report.disabled": "檢舉服務目前無法使用", "gui.socialInteractions.tooltip.report.no_messages": "玩家 %s 沒有可以檢舉的訊息", "gui.socialInteractions.tooltip.report.not_reportable": "無法檢舉此玩家，因為此伺服器無法驗證該玩家的聊天訊息", "gui.socialInteractions.tooltip.show": "顯示訊息", "gui.stats": "統計", "gui.toMenu": "返回伺服器清單", "gui.toRealms": "返回 Realms 清單", "gui.toTitle": "返回標題畫面", "gui.toWorld": "返回世界清單", "gui.togglable_slot": "按此停用欄位", "gui.up": "上", "gui.waitingForResponse.button.inactive": "返回（%s 秒）", "gui.waitingForResponse.title": "正在等待伺服器回應", "gui.yes": "是", "hanging_sign.edit": "編輯懸掛式告示牌訊息", "instrument.minecraft.admire_goat_horn": "仰慕", "instrument.minecraft.call_goat_horn": "呼喚", "instrument.minecraft.dream_goat_horn": "夢想", "instrument.minecraft.feel_goat_horn": "感覺", "instrument.minecraft.ponder_goat_horn": "沉思", "instrument.minecraft.seek_goat_horn": "尋覓", "instrument.minecraft.sing_goat_horn": "歌頌", "instrument.minecraft.yearn_goat_horn": "嚮往", "inventory.binSlot": "銷毀物品", "inventory.hotbarInfo": "按下 %1$s + %2$s 儲存快捷欄", "inventory.hotbarSaved": "快捷欄已儲存（按 %1$s + %2$s 復原）", "item.canBreak": "可破壞：", "item.canPlace": "可置於：", "item.canUse.unknown": "未知", "item.color": "顏色：%s", "item.components": "%s 個元件", "item.disabled": "已停用的物品", "item.durability": "耐久度：%s / %s", "item.dyed": "已染色", "item.minecraft.acacia_boat": "相思木船", "item.minecraft.acacia_chest_boat": "儲物箱相思木船", "item.minecraft.allay_spawn_egg": "悅靈 生怪蛋", "item.minecraft.amethyst_shard": "紫水晶碎片", "item.minecraft.angler_pottery_shard": "垂釣陶器碎片", "item.minecraft.angler_pottery_sherd": "垂釣陶器碎片", "item.minecraft.apple": "蘋果", "item.minecraft.archer_pottery_shard": "弓箭陶器碎片", "item.minecraft.archer_pottery_sherd": "弓箭陶器碎片", "item.minecraft.armadillo_scute": "犰狳鱗甲", "item.minecraft.armadillo_spawn_egg": "犰狳 生怪蛋", "item.minecraft.armor_stand": "盔甲座", "item.minecraft.arms_up_pottery_shard": "人形陶器碎片", "item.minecraft.arms_up_pottery_sherd": "人形陶器碎片", "item.minecraft.arrow": "箭矢", "item.minecraft.axolotl_bucket": "六角恐龍桶", "item.minecraft.axolotl_spawn_egg": "六角恐龍 生怪蛋", "item.minecraft.baked_potato": "烤馬鈴薯", "item.minecraft.bamboo_chest_raft": "儲物箱竹筏", "item.minecraft.bamboo_raft": "竹筏", "item.minecraft.bat_spawn_egg": "蝙蝠 生怪蛋", "item.minecraft.bee_spawn_egg": "蜜蜂 生怪蛋", "item.minecraft.beef": "生牛肉", "item.minecraft.beetroot": "甜菜根", "item.minecraft.beetroot_seeds": "甜菜種子", "item.minecraft.beetroot_soup": "甜菜湯", "item.minecraft.birch_boat": "樺木船", "item.minecraft.birch_chest_boat": "儲物箱樺木船", "item.minecraft.black_bundle": "黑色束口袋", "item.minecraft.black_dye": "黑色染料", "item.minecraft.black_harness": "黑色帽鞍", "item.minecraft.blade_pottery_shard": "利刃陶器碎片", "item.minecraft.blade_pottery_sherd": "利刃陶器碎片", "item.minecraft.blaze_powder": "烈焰粉", "item.minecraft.blaze_rod": "烈焰桿", "item.minecraft.blaze_spawn_egg": "烈焰使者 生怪蛋", "item.minecraft.blue_bundle": "藍色束口袋", "item.minecraft.blue_dye": "藍色染料", "item.minecraft.blue_egg": "藍色雞蛋", "item.minecraft.blue_harness": "藍色帽鞍", "item.minecraft.bogged_spawn_egg": "沼骸 生怪蛋", "item.minecraft.bolt_armor_trim_smithing_template": "鍛造模板", "item.minecraft.bolt_armor_trim_smithing_template.new": "鑲鉚盔甲紋樣", "item.minecraft.bone": "骨頭", "item.minecraft.bone_meal": "骨粉", "item.minecraft.book": "書", "item.minecraft.bordure_indented_banner_pattern": "鋸齒框邊旗幟圖案", "item.minecraft.bow": "弓", "item.minecraft.bowl": "碗", "item.minecraft.bread": "麵包", "item.minecraft.breeze_rod": "旋風桿", "item.minecraft.breeze_spawn_egg": "旋風使者 生怪蛋", "item.minecraft.brewer_pottery_shard": "釀造陶器碎片", "item.minecraft.brewer_pottery_sherd": "釀造陶器碎片", "item.minecraft.brewing_stand": "釀造台", "item.minecraft.brick": "紅磚頭", "item.minecraft.brown_bundle": "棕色束口袋", "item.minecraft.brown_dye": "棕色染料", "item.minecraft.brown_egg": "棕色雞蛋", "item.minecraft.brown_harness": "棕色帽鞍", "item.minecraft.brush": "刷子", "item.minecraft.bucket": "鐵桶", "item.minecraft.bundle": "束口袋", "item.minecraft.bundle.empty": "空", "item.minecraft.bundle.empty.description": "可容納一組混合的物品", "item.minecraft.bundle.full": "滿", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "烈焰陶器碎片", "item.minecraft.burn_pottery_sherd": "烈焰陶器碎片", "item.minecraft.camel_spawn_egg": "駱駝 生怪蛋", "item.minecraft.carrot": "胡蘿蔔", "item.minecraft.carrot_on_a_stick": "胡蘿蔔釣竿", "item.minecraft.cat_spawn_egg": "貓 生怪蛋", "item.minecraft.cauldron": "鍋釜", "item.minecraft.cave_spider_spawn_egg": "洞穴蜘蛛 生怪蛋", "item.minecraft.chainmail_boots": "鎖鏈靴子", "item.minecraft.chainmail_chestplate": "鎖鏈胸甲", "item.minecraft.chainmail_helmet": "鎖鏈頭盔", "item.minecraft.chainmail_leggings": "鎖鏈護腿", "item.minecraft.charcoal": "木炭", "item.minecraft.cherry_boat": "櫻花木船", "item.minecraft.cherry_chest_boat": "儲物箱櫻花木船", "item.minecraft.chest_minecart": "儲物箱礦車", "item.minecraft.chicken": "生雞肉", "item.minecraft.chicken_spawn_egg": "雞 生怪蛋", "item.minecraft.chorus_fruit": "歌萊果", "item.minecraft.clay_ball": "黏土球", "item.minecraft.clock": "時鐘", "item.minecraft.coal": "煤炭", "item.minecraft.coast_armor_trim_smithing_template": "鍛造模板", "item.minecraft.coast_armor_trim_smithing_template.new": "海岸盔甲紋樣", "item.minecraft.cocoa_beans": "可可豆", "item.minecraft.cod": "生鱈魚", "item.minecraft.cod_bucket": "鱈魚桶", "item.minecraft.cod_spawn_egg": "鱈魚 生怪蛋", "item.minecraft.command_block_minecart": "指令方塊礦車", "item.minecraft.compass": "羅盤", "item.minecraft.cooked_beef": "牛排", "item.minecraft.cooked_chicken": "烤雞", "item.minecraft.cooked_cod": "烤鱈魚", "item.minecraft.cooked_mutton": "烤羊肉", "item.minecraft.cooked_porkchop": "烤豬肉", "item.minecraft.cooked_rabbit": "烤兔肉", "item.minecraft.cooked_salmon": "烤鮭魚", "item.minecraft.cookie": "餅乾", "item.minecraft.copper_ingot": "銅錠", "item.minecraft.cow_spawn_egg": "牛 生怪蛋", "item.minecraft.creaking_spawn_egg": "嘎枝 生怪蛋", "item.minecraft.creeper_banner_pattern": "旗幟圖案", "item.minecraft.creeper_banner_pattern.desc": "苦力怕圖紋", "item.minecraft.creeper_banner_pattern.new": "苦力怕圖紋旗幟圖案", "item.minecraft.creeper_spawn_egg": "苦力怕 生怪蛋", "item.minecraft.crossbow": "弩", "item.minecraft.crossbow.projectile": "投射物：", "item.minecraft.crossbow.projectile.multiple": "投射物：%s × %s", "item.minecraft.crossbow.projectile.single": "投射物：%s", "item.minecraft.cyan_bundle": "青色束口袋", "item.minecraft.cyan_dye": "青色染料", "item.minecraft.cyan_harness": "青色帽鞍", "item.minecraft.danger_pottery_shard": "危機陶器碎片", "item.minecraft.danger_pottery_sherd": "危機陶器碎片", "item.minecraft.dark_oak_boat": "黑橡木船", "item.minecraft.dark_oak_chest_boat": "儲物箱黑橡木船", "item.minecraft.debug_stick": "除錯棒", "item.minecraft.debug_stick.empty": "%s 沒有屬性", "item.minecraft.debug_stick.select": "選擇 \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" to %s", "item.minecraft.diamond": "鑽石", "item.minecraft.diamond_axe": "鑽石斧", "item.minecraft.diamond_boots": "鑽石靴子", "item.minecraft.diamond_chestplate": "鑽石胸甲", "item.minecraft.diamond_helmet": "鑽石頭盔", "item.minecraft.diamond_hoe": "鑽石鋤", "item.minecraft.diamond_horse_armor": "鑽石製馬鎧", "item.minecraft.diamond_leggings": "鑽石護腿", "item.minecraft.diamond_pickaxe": "鑽石鎬", "item.minecraft.diamond_shovel": "鑽石鏟", "item.minecraft.diamond_sword": "鑽石劍", "item.minecraft.disc_fragment_5": "唱片碎片", "item.minecraft.disc_fragment_5.desc": "唱片 - 5", "item.minecraft.dolphin_spawn_egg": "海豚 生怪蛋", "item.minecraft.donkey_spawn_egg": "驢子 生怪蛋", "item.minecraft.dragon_breath": "龍之吐息", "item.minecraft.dried_kelp": "海帶乾", "item.minecraft.drowned_spawn_egg": "沉屍 生怪蛋", "item.minecraft.dune_armor_trim_smithing_template": "鍛造模板", "item.minecraft.dune_armor_trim_smithing_template.new": "沙丘盔甲紋樣", "item.minecraft.echo_shard": "回聲碎片", "item.minecraft.egg": "雞蛋", "item.minecraft.elder_guardian_spawn_egg": "遠古深海守衛 生怪蛋", "item.minecraft.elytra": "鞘翅", "item.minecraft.emerald": "綠寶石", "item.minecraft.enchanted_book": "附魔書", "item.minecraft.enchanted_golden_apple": "附魔金蘋果", "item.minecraft.end_crystal": "終界水晶", "item.minecraft.ender_dragon_spawn_egg": "終界龍 生怪蛋", "item.minecraft.ender_eye": "終界之眼", "item.minecraft.ender_pearl": "終界珍珠", "item.minecraft.enderman_spawn_egg": "終界使者 生怪蛋", "item.minecraft.endermite_spawn_egg": "終界蟎 生怪蛋", "item.minecraft.evoker_spawn_egg": "喚魔者 生怪蛋", "item.minecraft.experience_bottle": "經驗瓶", "item.minecraft.explorer_pottery_shard": "探險陶器碎片", "item.minecraft.explorer_pottery_sherd": "探險陶器碎片", "item.minecraft.eye_armor_trim_smithing_template": "鍛造模板", "item.minecraft.eye_armor_trim_smithing_template.new": "眼眸盔甲紋樣", "item.minecraft.feather": "羽毛", "item.minecraft.fermented_spider_eye": "發酵蜘蛛眼", "item.minecraft.field_masoned_banner_pattern": "磚牆花紋旗幟圖案", "item.minecraft.filled_map": "地圖", "item.minecraft.fire_charge": "火焰彈", "item.minecraft.firework_rocket": "煙火", "item.minecraft.firework_rocket.flight": "飛行時間：", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "火藥球", "item.minecraft.firework_star.black": "黑色", "item.minecraft.firework_star.blue": "藍色", "item.minecraft.firework_star.brown": "棕色", "item.minecraft.firework_star.custom_color": "自訂", "item.minecraft.firework_star.cyan": "青色", "item.minecraft.firework_star.fade_to": "淡出", "item.minecraft.firework_star.flicker": "閃爍", "item.minecraft.firework_star.gray": "灰色", "item.minecraft.firework_star.green": "綠色", "item.minecraft.firework_star.light_blue": "淺藍色", "item.minecraft.firework_star.light_gray": "淺灰色", "item.minecraft.firework_star.lime": "淺綠色", "item.minecraft.firework_star.magenta": "洋紅色", "item.minecraft.firework_star.orange": "橙色", "item.minecraft.firework_star.pink": "粉紅色", "item.minecraft.firework_star.purple": "紫色", "item.minecraft.firework_star.red": "紅色", "item.minecraft.firework_star.shape": "未知形狀", "item.minecraft.firework_star.shape.burst": "爆裂", "item.minecraft.firework_star.shape.creeper": "苦力怕形", "item.minecraft.firework_star.shape.large_ball": "大型球狀", "item.minecraft.firework_star.shape.small_ball": "小型球狀", "item.minecraft.firework_star.shape.star": "星形", "item.minecraft.firework_star.trail": "軌跡", "item.minecraft.firework_star.white": "白色", "item.minecraft.firework_star.yellow": "黃色", "item.minecraft.fishing_rod": "釣竿", "item.minecraft.flint": "燧石", "item.minecraft.flint_and_steel": "打火石", "item.minecraft.flow_armor_trim_smithing_template": "鍛造模板", "item.minecraft.flow_armor_trim_smithing_template.new": "渦流盔甲紋樣", "item.minecraft.flow_banner_pattern": "旗幟圖案", "item.minecraft.flow_banner_pattern.desc": "渦流", "item.minecraft.flow_banner_pattern.new": "渦流旗幟圖案", "item.minecraft.flow_pottery_sherd": "渦流陶器碎片", "item.minecraft.flower_banner_pattern": "旗幟圖案", "item.minecraft.flower_banner_pattern.desc": "花朵圖紋", "item.minecraft.flower_banner_pattern.new": "花朵圖紋旗幟圖案", "item.minecraft.flower_pot": "花盆", "item.minecraft.fox_spawn_egg": "狐狸 生怪蛋", "item.minecraft.friend_pottery_shard": "摯友陶器碎片", "item.minecraft.friend_pottery_sherd": "摯友陶器碎片", "item.minecraft.frog_spawn_egg": "青蛙 生怪蛋", "item.minecraft.furnace_minecart": "熔爐礦車", "item.minecraft.ghast_spawn_egg": "地獄幽靈 生怪蛋", "item.minecraft.ghast_tear": "幽靈之淚", "item.minecraft.glass_bottle": "玻璃瓶", "item.minecraft.glistering_melon_slice": "鑲金西瓜片", "item.minecraft.globe_banner_pattern": "旗幟圖案", "item.minecraft.globe_banner_pattern.desc": "地球", "item.minecraft.globe_banner_pattern.new": "地球旗幟圖案", "item.minecraft.glow_berries": "螢光莓", "item.minecraft.glow_ink_sac": "螢光墨囊", "item.minecraft.glow_item_frame": "螢光物品展示框", "item.minecraft.glow_squid_spawn_egg": "螢光魷魚 生怪蛋", "item.minecraft.glowstone_dust": "螢石粉", "item.minecraft.goat_horn": "山羊角", "item.minecraft.goat_spawn_egg": "山羊 生怪蛋", "item.minecraft.gold_ingot": "金錠", "item.minecraft.gold_nugget": "金粒", "item.minecraft.golden_apple": "金蘋果", "item.minecraft.golden_axe": "金斧", "item.minecraft.golden_boots": "黃金靴子", "item.minecraft.golden_carrot": "金胡蘿蔔", "item.minecraft.golden_chestplate": "黃金胸甲", "item.minecraft.golden_helmet": "黃金頭盔", "item.minecraft.golden_hoe": "金鋤", "item.minecraft.golden_horse_armor": "黃金製馬鎧", "item.minecraft.golden_leggings": "黃金護腿", "item.minecraft.golden_pickaxe": "金鎬", "item.minecraft.golden_shovel": "金鏟", "item.minecraft.golden_sword": "金劍", "item.minecraft.gray_bundle": "灰色束口袋", "item.minecraft.gray_dye": "灰色染料", "item.minecraft.gray_harness": "灰色帽鞍", "item.minecraft.green_bundle": "綠色束口袋", "item.minecraft.green_dye": "綠色染料", "item.minecraft.green_harness": "綠色帽鞍", "item.minecraft.guardian_spawn_egg": "深海守衛 生怪蛋", "item.minecraft.gunpowder": "火藥", "item.minecraft.guster_banner_pattern": "旗幟圖案", "item.minecraft.guster_banner_pattern.desc": "狂風", "item.minecraft.guster_banner_pattern.new": "狂風旗幟圖案", "item.minecraft.guster_pottery_sherd": "狂風陶器碎片", "item.minecraft.happy_ghast_spawn_egg": "快樂幽靈 生怪蛋", "item.minecraft.harness": "帽鞍", "item.minecraft.heart_of_the_sea": "海洋之心", "item.minecraft.heart_pottery_shard": "愛心陶器碎片", "item.minecraft.heart_pottery_sherd": "愛心陶器碎片", "item.minecraft.heartbreak_pottery_shard": "心碎陶器碎片", "item.minecraft.heartbreak_pottery_sherd": "心碎陶器碎片", "item.minecraft.hoglin_spawn_egg": "豬布獸 生怪蛋", "item.minecraft.honey_bottle": "蜂蜜瓶", "item.minecraft.honeycomb": "蜂巢", "item.minecraft.hopper_minecart": "漏斗礦車", "item.minecraft.horse_spawn_egg": "馬 生怪蛋", "item.minecraft.host_armor_trim_smithing_template": "鍛造模板", "item.minecraft.host_armor_trim_smithing_template.new": "主人盔甲紋樣", "item.minecraft.howl_pottery_shard": "狼嚎陶器碎片", "item.minecraft.howl_pottery_sherd": "狼嚎陶器碎片", "item.minecraft.husk_spawn_egg": "屍殼 生怪蛋", "item.minecraft.ink_sac": "墨囊", "item.minecraft.iron_axe": "鐵斧", "item.minecraft.iron_boots": "鐵製靴子", "item.minecraft.iron_chestplate": "鐵製胸甲", "item.minecraft.iron_golem_spawn_egg": "鐵魔像 生怪蛋", "item.minecraft.iron_helmet": "鐵製頭盔", "item.minecraft.iron_hoe": "鐵鋤", "item.minecraft.iron_horse_armor": "鐵製馬鎧", "item.minecraft.iron_ingot": "鐵錠", "item.minecraft.iron_leggings": "鐵製護腿", "item.minecraft.iron_nugget": "鐵粒", "item.minecraft.iron_pickaxe": "鐵鎬", "item.minecraft.iron_shovel": "鐵鏟", "item.minecraft.iron_sword": "鐵劍", "item.minecraft.item_frame": "物品展示框", "item.minecraft.jungle_boat": "叢林木船", "item.minecraft.jungle_chest_boat": "儲物箱叢林木船", "item.minecraft.knowledge_book": "知識之書", "item.minecraft.lapis_lazuli": "青金石", "item.minecraft.lava_bucket": "熔岩桶", "item.minecraft.lead": "拴繩", "item.minecraft.leather": "皮革", "item.minecraft.leather_boots": "皮革靴子", "item.minecraft.leather_chestplate": "皮革上衣", "item.minecraft.leather_helmet": "皮革帽子", "item.minecraft.leather_horse_armor": "皮革製馬鎧", "item.minecraft.leather_leggings": "皮革褲子", "item.minecraft.light_blue_bundle": "淺藍色束口袋", "item.minecraft.light_blue_dye": "淺藍色染料", "item.minecraft.light_blue_harness": "淺藍色帽鞍", "item.minecraft.light_gray_bundle": "淺灰色束口袋", "item.minecraft.light_gray_dye": "淺灰色染料", "item.minecraft.light_gray_harness": "淺灰色帽鞍", "item.minecraft.lime_bundle": "淺綠色束口袋", "item.minecraft.lime_dye": "淺綠色染料", "item.minecraft.lime_harness": "淺綠色帽鞍", "item.minecraft.lingering_potion": "滯留藥水", "item.minecraft.lingering_potion.effect.awkward": "滯留 基礎藥水", "item.minecraft.lingering_potion.effect.empty": "滯留 不可合成的藥水", "item.minecraft.lingering_potion.effect.fire_resistance": "滯留 抗火藥水", "item.minecraft.lingering_potion.effect.harming": "滯留 傷害藥水", "item.minecraft.lingering_potion.effect.healing": "滯留 治療藥水", "item.minecraft.lingering_potion.effect.infested": "滯留 蛀蝕藥水", "item.minecraft.lingering_potion.effect.invisibility": "滯留 隱形藥水", "item.minecraft.lingering_potion.effect.leaping": "滯留 跳躍藥水", "item.minecraft.lingering_potion.effect.levitation": "滯留 懸浮藥水", "item.minecraft.lingering_potion.effect.luck": "滯留 幸運藥水", "item.minecraft.lingering_potion.effect.mundane": "滯留 平凡藥水", "item.minecraft.lingering_potion.effect.night_vision": "滯留 夜視藥水", "item.minecraft.lingering_potion.effect.oozing": "滯留 滲漿藥水", "item.minecraft.lingering_potion.effect.poison": "滯留 劇毒藥水", "item.minecraft.lingering_potion.effect.regeneration": "滯留 回復藥水", "item.minecraft.lingering_potion.effect.slow_falling": "滯留 緩降藥水", "item.minecraft.lingering_potion.effect.slowness": "滯留 緩速藥水", "item.minecraft.lingering_potion.effect.strength": "滯留 力量藥水", "item.minecraft.lingering_potion.effect.swiftness": "滯留 迅捷藥水", "item.minecraft.lingering_potion.effect.thick": "滯留 黏稠藥水", "item.minecraft.lingering_potion.effect.turtle_master": "滯留 龜仙藥水", "item.minecraft.lingering_potion.effect.water": "滯留 水瓶", "item.minecraft.lingering_potion.effect.water_breathing": "滯留 水下呼吸藥水", "item.minecraft.lingering_potion.effect.weakness": "滯留 虛弱藥水", "item.minecraft.lingering_potion.effect.weaving": "滯留 結網藥水", "item.minecraft.lingering_potion.effect.wind_charged": "滯留 蘊風藥水", "item.minecraft.llama_spawn_egg": "駱馬 生怪蛋", "item.minecraft.lodestone_compass": "磁石羅盤", "item.minecraft.mace": "重錘", "item.minecraft.magenta_bundle": "洋紅色束口袋", "item.minecraft.magenta_dye": "洋紅色染料", "item.minecraft.magenta_harness": "洋紅色帽鞍", "item.minecraft.magma_cream": "岩漿球", "item.minecraft.magma_cube_spawn_egg": "岩漿立方怪 生怪蛋", "item.minecraft.mangrove_boat": "紅樹林木船", "item.minecraft.mangrove_chest_boat": "儲物箱紅樹林木船", "item.minecraft.map": "空白地圖", "item.minecraft.melon_seeds": "西瓜種子", "item.minecraft.melon_slice": "西瓜片", "item.minecraft.milk_bucket": "鮮奶桶", "item.minecraft.minecart": "礦車", "item.minecraft.miner_pottery_shard": "礦工陶器碎片", "item.minecraft.miner_pottery_sherd": "礦工陶器碎片", "item.minecraft.mojang_banner_pattern": "旗幟圖案", "item.minecraft.mojang_banner_pattern.desc": "Mojang 圖紋", "item.minecraft.mojang_banner_pattern.new": "Mojang 標誌旗幟圖案", "item.minecraft.mooshroom_spawn_egg": "哞菇 生怪蛋", "item.minecraft.mourner_pottery_shard": "悼惜陶器碎片", "item.minecraft.mourner_pottery_sherd": "悼惜陶器碎片", "item.minecraft.mule_spawn_egg": "騾子 生怪蛋", "item.minecraft.mushroom_stew": "蘑菇湯", "item.minecraft.music_disc_11": "唱片", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "唱片", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "唱片", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "唱片", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "唱片", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "唱片", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "唱片", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "唱片", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON>（音樂盒）", "item.minecraft.music_disc_far": "唱片", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "唱片", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "唱片", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "唱片", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "唱片", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "唱片", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "唱片", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "唱片", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "唱片", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "唱片", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "唱片", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "唱片", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "唱片", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "生羊肉", "item.minecraft.name_tag": "命名牌", "item.minecraft.nautilus_shell": "鸚鵡螺殼", "item.minecraft.nether_brick": "地獄磚頭", "item.minecraft.nether_star": "地獄之星", "item.minecraft.nether_wart": "地獄疙瘩", "item.minecraft.netherite_axe": "獄髓斧", "item.minecraft.netherite_boots": "獄髓靴子", "item.minecraft.netherite_chestplate": "獄髓胸甲", "item.minecraft.netherite_helmet": "獄髓頭盔", "item.minecraft.netherite_hoe": "獄髓鋤", "item.minecraft.netherite_ingot": "獄髓錠", "item.minecraft.netherite_leggings": "獄髓護腿", "item.minecraft.netherite_pickaxe": "獄髓鎬", "item.minecraft.netherite_scrap": "獄髓碎片", "item.minecraft.netherite_shovel": "獄髓鏟", "item.minecraft.netherite_sword": "獄髓劍", "item.minecraft.netherite_upgrade_smithing_template": "鍛造模板", "item.minecraft.netherite_upgrade_smithing_template.new": "獄髓升級", "item.minecraft.oak_boat": "橡木船", "item.minecraft.oak_chest_boat": "儲物箱橡木船", "item.minecraft.ocelot_spawn_egg": "山貓 生怪蛋", "item.minecraft.ominous_bottle": "不祥之瓶", "item.minecraft.ominous_trial_key": "不祥試煉鑰匙", "item.minecraft.orange_bundle": "橙色束口袋", "item.minecraft.orange_dye": "橙色染料", "item.minecraft.orange_harness": "橙色帽鞍", "item.minecraft.painting": "繪畫", "item.minecraft.pale_oak_boat": "蒼白橡木船", "item.minecraft.pale_oak_chest_boat": "儲物箱蒼白橡木船", "item.minecraft.panda_spawn_egg": "貓熊 生怪蛋", "item.minecraft.paper": "紙", "item.minecraft.parrot_spawn_egg": "鸚鵡 生怪蛋", "item.minecraft.phantom_membrane": "夜魅皮膜", "item.minecraft.phantom_spawn_egg": "夜魅 生怪蛋", "item.minecraft.pig_spawn_egg": "豬 生怪蛋", "item.minecraft.piglin_banner_pattern": "旗幟圖案", "item.minecraft.piglin_banner_pattern.desc": "豬鼻", "item.minecraft.piglin_banner_pattern.new": "豬鼻旗幟圖案", "item.minecraft.piglin_brute_spawn_egg": "豬布林蠻兵 生怪蛋", "item.minecraft.piglin_spawn_egg": "豬布林 生怪蛋", "item.minecraft.pillager_spawn_egg": "掠奪者 生怪蛋", "item.minecraft.pink_bundle": "粉紅色束口袋", "item.minecraft.pink_dye": "粉紅色染料", "item.minecraft.pink_harness": "粉紅色帽鞍", "item.minecraft.pitcher_plant": "瓶子草", "item.minecraft.pitcher_pod": "瓶子草豆莢", "item.minecraft.plenty_pottery_shard": "富饒陶器碎片", "item.minecraft.plenty_pottery_sherd": "富饒陶器碎片", "item.minecraft.poisonous_potato": "毒馬鈴薯", "item.minecraft.polar_bear_spawn_egg": "北極熊 生怪蛋", "item.minecraft.popped_chorus_fruit": "爆開的歌萊果", "item.minecraft.porkchop": "生豬肉", "item.minecraft.potato": "馬鈴薯", "item.minecraft.potion": "藥水", "item.minecraft.potion.effect.awkward": "基礎藥水", "item.minecraft.potion.effect.empty": "不可合成的藥水", "item.minecraft.potion.effect.fire_resistance": "抗火藥水", "item.minecraft.potion.effect.harming": "傷害藥水", "item.minecraft.potion.effect.healing": "治療藥水", "item.minecraft.potion.effect.infested": "蛀蝕藥水", "item.minecraft.potion.effect.invisibility": "隱形藥水", "item.minecraft.potion.effect.leaping": "跳躍藥水", "item.minecraft.potion.effect.levitation": "懸浮藥水", "item.minecraft.potion.effect.luck": "幸運藥水", "item.minecraft.potion.effect.mundane": "平凡藥水", "item.minecraft.potion.effect.night_vision": "夜視藥水", "item.minecraft.potion.effect.oozing": "滲漿藥水", "item.minecraft.potion.effect.poison": "劇毒藥水", "item.minecraft.potion.effect.regeneration": "回復藥水", "item.minecraft.potion.effect.slow_falling": "緩降藥水", "item.minecraft.potion.effect.slowness": "緩速藥水", "item.minecraft.potion.effect.strength": "力量藥水", "item.minecraft.potion.effect.swiftness": "迅捷藥水", "item.minecraft.potion.effect.thick": "黏稠藥水", "item.minecraft.potion.effect.turtle_master": "龜仙藥水", "item.minecraft.potion.effect.water": "水瓶", "item.minecraft.potion.effect.water_breathing": "水下呼吸藥水", "item.minecraft.potion.effect.weakness": "虛弱藥水", "item.minecraft.potion.effect.weaving": "結網藥水", "item.minecraft.potion.effect.wind_charged": "蘊風藥水", "item.minecraft.pottery_shard_archer": "弓箭陶器碎片", "item.minecraft.pottery_shard_arms_up": "人形陶器碎片", "item.minecraft.pottery_shard_prize": "珍寶陶器碎片", "item.minecraft.pottery_shard_skull": "頭顱陶器碎片", "item.minecraft.powder_snow_bucket": "粉雪桶", "item.minecraft.prismarine_crystals": "海磷晶體", "item.minecraft.prismarine_shard": "海磷碎片", "item.minecraft.prize_pottery_shard": "珍寶陶器碎片", "item.minecraft.prize_pottery_sherd": "珍寶陶器碎片", "item.minecraft.pufferfish": "河豚", "item.minecraft.pufferfish_bucket": "河豚桶", "item.minecraft.pufferfish_spawn_egg": "河豚 生怪蛋", "item.minecraft.pumpkin_pie": "南瓜派", "item.minecraft.pumpkin_seeds": "南瓜種子", "item.minecraft.purple_bundle": "紫色束口袋", "item.minecraft.purple_dye": "紫色染料", "item.minecraft.purple_harness": "紫色帽鞍", "item.minecraft.quartz": "地獄石英", "item.minecraft.rabbit": "生兔肉", "item.minecraft.rabbit_foot": "兔子腳", "item.minecraft.rabbit_hide": "兔子皮", "item.minecraft.rabbit_spawn_egg": "兔子 生怪蛋", "item.minecraft.rabbit_stew": "兔肉湯", "item.minecraft.raiser_armor_trim_smithing_template": "鍛造模板", "item.minecraft.raiser_armor_trim_smithing_template.new": "牧者盔甲紋樣", "item.minecraft.ravager_spawn_egg": "劫毀獸 生怪蛋", "item.minecraft.raw_copper": "銅原礦", "item.minecraft.raw_gold": "金原礦", "item.minecraft.raw_iron": "鐵原礦", "item.minecraft.recovery_compass": "回生羅盤", "item.minecraft.red_bundle": "紅色束口袋", "item.minecraft.red_dye": "紅色染料", "item.minecraft.red_harness": "紅色帽鞍", "item.minecraft.redstone": "紅石粉", "item.minecraft.resin_brick": "樹脂磚頭", "item.minecraft.resin_clump": "樹脂團", "item.minecraft.rib_armor_trim_smithing_template": "鍛造模板", "item.minecraft.rib_armor_trim_smithing_template.new": "肋骨盔甲紋樣", "item.minecraft.rotten_flesh": "腐肉", "item.minecraft.saddle": "鞍", "item.minecraft.salmon": "生鮭魚", "item.minecraft.salmon_bucket": "鮭魚桶", "item.minecraft.salmon_spawn_egg": "鮭魚 生怪蛋", "item.minecraft.scrape_pottery_sherd": "刮削陶器碎片", "item.minecraft.scute": "鱗甲", "item.minecraft.sentry_armor_trim_smithing_template": "鍛造模板", "item.minecraft.sentry_armor_trim_smithing_template.new": "哨兵盔甲紋樣", "item.minecraft.shaper_armor_trim_smithing_template": "鍛造模板", "item.minecraft.shaper_armor_trim_smithing_template.new": "工匠盔甲紋樣", "item.minecraft.sheaf_pottery_shard": "麥捆陶器碎片", "item.minecraft.sheaf_pottery_sherd": "麥捆陶器碎片", "item.minecraft.shears": "剪刀", "item.minecraft.sheep_spawn_egg": "綿羊 生怪蛋", "item.minecraft.shelter_pottery_shard": "樹蔭陶器碎片", "item.minecraft.shelter_pottery_sherd": "樹蔭陶器碎片", "item.minecraft.shield": "盾牌", "item.minecraft.shield.black": "黑色盾牌", "item.minecraft.shield.blue": "藍色盾牌", "item.minecraft.shield.brown": "棕色盾牌", "item.minecraft.shield.cyan": "青色盾牌", "item.minecraft.shield.gray": "灰色盾牌", "item.minecraft.shield.green": "綠色盾牌", "item.minecraft.shield.light_blue": "淺藍色盾牌", "item.minecraft.shield.light_gray": "淺灰色盾牌", "item.minecraft.shield.lime": "淺綠色盾牌", "item.minecraft.shield.magenta": "洋紅色盾牌", "item.minecraft.shield.orange": "橙色盾牌", "item.minecraft.shield.pink": "粉紅色盾牌", "item.minecraft.shield.purple": "紫色盾牌", "item.minecraft.shield.red": "紅色盾牌", "item.minecraft.shield.white": "白色盾牌", "item.minecraft.shield.yellow": "黃色盾牌", "item.minecraft.shulker_shell": "界伏殼", "item.minecraft.shulker_spawn_egg": "界伏蚌 生怪蛋", "item.minecraft.sign": "告示牌", "item.minecraft.silence_armor_trim_smithing_template": "鍛造模板", "item.minecraft.silence_armor_trim_smithing_template.new": "寂靜盔甲紋樣", "item.minecraft.silverfish_spawn_egg": "蠹魚 生怪蛋", "item.minecraft.skeleton_horse_spawn_egg": "骷髏馬 生怪蛋", "item.minecraft.skeleton_spawn_egg": "骷髏 生怪蛋", "item.minecraft.skull_banner_pattern": "旗幟圖案", "item.minecraft.skull_banner_pattern.desc": "骷髏圖紋", "item.minecraft.skull_banner_pattern.new": "骷髏圖紋旗幟圖案", "item.minecraft.skull_pottery_shard": "頭顱陶器碎片", "item.minecraft.skull_pottery_sherd": "頭顱陶器碎片", "item.minecraft.slime_ball": "史萊姆球", "item.minecraft.slime_spawn_egg": "史萊姆 生怪蛋", "item.minecraft.smithing_template": "鍛造模板", "item.minecraft.smithing_template.applies_to": "可套用在：", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "放置錠或晶體", "item.minecraft.smithing_template.armor_trim.applies_to": "盔甲", "item.minecraft.smithing_template.armor_trim.base_slot_description": "放置盔甲", "item.minecraft.smithing_template.armor_trim.ingredients": "錠或晶體", "item.minecraft.smithing_template.ingredients": "原材料：", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "放置獄髓錠", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "鑽石裝備", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "放置鑽石盔甲、武器或工具", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "獄髓錠", "item.minecraft.smithing_template.upgrade": "已有升級：", "item.minecraft.sniffer_spawn_egg": "嗅探獸 生怪蛋", "item.minecraft.snort_pottery_shard": "嗅探陶器碎片", "item.minecraft.snort_pottery_sherd": "嗅探陶器碎片", "item.minecraft.snout_armor_trim_smithing_template": "鍛造模板", "item.minecraft.snout_armor_trim_smithing_template.new": "豬鼻盔甲紋樣", "item.minecraft.snow_golem_spawn_egg": "雪人 生怪蛋", "item.minecraft.snowball": "雪球", "item.minecraft.spectral_arrow": "追跡之箭", "item.minecraft.spider_eye": "蜘蛛眼", "item.minecraft.spider_spawn_egg": "蜘蛛 生怪蛋", "item.minecraft.spire_armor_trim_smithing_template": "鍛造模板", "item.minecraft.spire_armor_trim_smithing_template.new": "旋塔盔甲紋樣", "item.minecraft.splash_potion": "飛濺藥水", "item.minecraft.splash_potion.effect.awkward": "飛濺 基礎藥水", "item.minecraft.splash_potion.effect.empty": "飛濺 不可合成的藥水", "item.minecraft.splash_potion.effect.fire_resistance": "飛濺 抗火藥水", "item.minecraft.splash_potion.effect.harming": "飛濺 傷害藥水", "item.minecraft.splash_potion.effect.healing": "飛濺 治療藥水", "item.minecraft.splash_potion.effect.infested": "飛濺 蛀蝕藥水", "item.minecraft.splash_potion.effect.invisibility": "飛濺 隱形藥水", "item.minecraft.splash_potion.effect.leaping": "飛濺 跳躍藥水", "item.minecraft.splash_potion.effect.levitation": "飛濺 懸浮藥水", "item.minecraft.splash_potion.effect.luck": "飛濺 幸運藥水", "item.minecraft.splash_potion.effect.mundane": "飛濺 平凡藥水", "item.minecraft.splash_potion.effect.night_vision": "飛濺 夜視藥水", "item.minecraft.splash_potion.effect.oozing": "飛濺 滲漿藥水", "item.minecraft.splash_potion.effect.poison": "飛濺 劇毒藥水", "item.minecraft.splash_potion.effect.regeneration": "飛濺 回復藥水", "item.minecraft.splash_potion.effect.slow_falling": "飛濺 緩降藥水", "item.minecraft.splash_potion.effect.slowness": "飛濺 緩速藥水", "item.minecraft.splash_potion.effect.strength": "飛濺 力量藥水", "item.minecraft.splash_potion.effect.swiftness": "飛濺 迅捷藥水", "item.minecraft.splash_potion.effect.thick": "飛濺 黏稠藥水", "item.minecraft.splash_potion.effect.turtle_master": "飛濺 龜仙藥水", "item.minecraft.splash_potion.effect.water": "飛濺 水瓶", "item.minecraft.splash_potion.effect.water_breathing": "飛濺 水下呼吸藥水", "item.minecraft.splash_potion.effect.weakness": "飛濺 虛弱藥水", "item.minecraft.splash_potion.effect.weaving": "飛濺 結網藥水", "item.minecraft.splash_potion.effect.wind_charged": "飛濺 蘊風藥水", "item.minecraft.spruce_boat": "杉木船", "item.minecraft.spruce_chest_boat": "儲物箱杉木船", "item.minecraft.spyglass": "望遠鏡", "item.minecraft.squid_spawn_egg": "魷魚 生怪蛋", "item.minecraft.stick": "木棒", "item.minecraft.stone_axe": "石斧", "item.minecraft.stone_hoe": "石鋤", "item.minecraft.stone_pickaxe": "石鎬", "item.minecraft.stone_shovel": "石鏟", "item.minecraft.stone_sword": "石劍", "item.minecraft.stray_spawn_egg": "流髑 生怪蛋", "item.minecraft.strider_spawn_egg": "熾足獸 生怪蛋", "item.minecraft.string": "線", "item.minecraft.sugar": "糖", "item.minecraft.suspicious_stew": "可疑的燉湯", "item.minecraft.sweet_berries": "甜莓", "item.minecraft.tadpole_bucket": "蝌蚪桶", "item.minecraft.tadpole_spawn_egg": "蝌蚪 生怪蛋", "item.minecraft.tide_armor_trim_smithing_template": "鍛造模板", "item.minecraft.tide_armor_trim_smithing_template.new": "潮汐盔甲紋樣", "item.minecraft.tipped_arrow": "藥水箭", "item.minecraft.tipped_arrow.effect.awkward": "藥水箭", "item.minecraft.tipped_arrow.effect.empty": "不可合成的藥水箭", "item.minecraft.tipped_arrow.effect.fire_resistance": "抗火之箭", "item.minecraft.tipped_arrow.effect.harming": "傷害之箭", "item.minecraft.tipped_arrow.effect.healing": "治療之箭", "item.minecraft.tipped_arrow.effect.infested": "蛀蝕之箭", "item.minecraft.tipped_arrow.effect.invisibility": "隱形之箭", "item.minecraft.tipped_arrow.effect.leaping": "跳躍之箭", "item.minecraft.tipped_arrow.effect.levitation": "懸浮之箭", "item.minecraft.tipped_arrow.effect.luck": "幸運之箭", "item.minecraft.tipped_arrow.effect.mundane": "藥水箭", "item.minecraft.tipped_arrow.effect.night_vision": "夜視之箭", "item.minecraft.tipped_arrow.effect.oozing": "滲漿之箭", "item.minecraft.tipped_arrow.effect.poison": "劇毒之箭", "item.minecraft.tipped_arrow.effect.regeneration": "回復之箭", "item.minecraft.tipped_arrow.effect.slow_falling": "緩降之箭", "item.minecraft.tipped_arrow.effect.slowness": "緩速之箭", "item.minecraft.tipped_arrow.effect.strength": "力量之箭", "item.minecraft.tipped_arrow.effect.swiftness": "迅捷之箭", "item.minecraft.tipped_arrow.effect.thick": "藥水箭", "item.minecraft.tipped_arrow.effect.turtle_master": "龜仙之箭", "item.minecraft.tipped_arrow.effect.water": "飛濺之箭", "item.minecraft.tipped_arrow.effect.water_breathing": "水下呼吸之箭", "item.minecraft.tipped_arrow.effect.weakness": "虛弱之箭", "item.minecraft.tipped_arrow.effect.weaving": "結網之箭", "item.minecraft.tipped_arrow.effect.wind_charged": "蘊風之箭", "item.minecraft.tnt_minecart": "TNT 礦車", "item.minecraft.torchflower_seeds": "火把花種子", "item.minecraft.totem_of_undying": "不死圖騰", "item.minecraft.trader_llama_spawn_egg": "商駝 生怪蛋", "item.minecraft.trial_key": "試煉鑰匙", "item.minecraft.trident": "三叉戟", "item.minecraft.tropical_fish": "熱帶魚", "item.minecraft.tropical_fish_bucket": "熱帶魚桶", "item.minecraft.tropical_fish_spawn_egg": "熱帶魚 生怪蛋", "item.minecraft.turtle_helmet": "海龜殼", "item.minecraft.turtle_scute": "海龜鱗甲", "item.minecraft.turtle_spawn_egg": "海龜 生怪蛋", "item.minecraft.vex_armor_trim_smithing_template": "鍛造模板", "item.minecraft.vex_armor_trim_smithing_template.new": "惱鬼盔甲紋樣", "item.minecraft.vex_spawn_egg": "惱鬼 生怪蛋", "item.minecraft.villager_spawn_egg": "村民 生怪蛋", "item.minecraft.vindicator_spawn_egg": "衛道士 生怪蛋", "item.minecraft.wandering_trader_spawn_egg": "流浪商人 生怪蛋", "item.minecraft.ward_armor_trim_smithing_template": "鍛造模板", "item.minecraft.ward_armor_trim_smithing_template.new": "伏守盔甲紋樣", "item.minecraft.warden_spawn_egg": "伏守者 生怪蛋", "item.minecraft.warped_fungus_on_a_stick": "扭曲蕈菇釣竿", "item.minecraft.water_bucket": "水桶", "item.minecraft.wayfinder_armor_trim_smithing_template": "鍛造模板", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "嚮導盔甲紋樣", "item.minecraft.wheat": "小麥", "item.minecraft.wheat_seeds": "小麥種子", "item.minecraft.white_bundle": "白色束口袋", "item.minecraft.white_dye": "白色染料", "item.minecraft.white_harness": "白色帽鞍", "item.minecraft.wild_armor_trim_smithing_template": "鍛造模板", "item.minecraft.wild_armor_trim_smithing_template.new": "荒野盔甲紋樣", "item.minecraft.wind_charge": "風彈", "item.minecraft.witch_spawn_egg": "女巫 生怪蛋", "item.minecraft.wither_skeleton_spawn_egg": "凋零骷髏 生怪蛋", "item.minecraft.wither_spawn_egg": "凋零怪 生怪蛋", "item.minecraft.wolf_armor": "狼鎧", "item.minecraft.wolf_spawn_egg": "狼 生怪蛋", "item.minecraft.wooden_axe": "木斧", "item.minecraft.wooden_hoe": "木鋤", "item.minecraft.wooden_pickaxe": "木鎬", "item.minecraft.wooden_shovel": "木鏟", "item.minecraft.wooden_sword": "木劍", "item.minecraft.writable_book": "書和羽毛筆", "item.minecraft.written_book": "完成的書", "item.minecraft.yellow_bundle": "黃色束口袋", "item.minecraft.yellow_dye": "黃色染料", "item.minecraft.yellow_harness": "黃色帽鞍", "item.minecraft.zoglin_spawn_egg": "豬屍獸 生怪蛋", "item.minecraft.zombie_horse_spawn_egg": "殭屍馬 生怪蛋", "item.minecraft.zombie_spawn_egg": "殭屍 生怪蛋", "item.minecraft.zombie_villager_spawn_egg": "殭屍村民 生怪蛋", "item.minecraft.zombified_piglin_spawn_egg": "殭屍化豬布林 生怪蛋", "item.modifiers.any": "裝備時：", "item.modifiers.armor": "穿戴時：", "item.modifiers.body": "裝備時：", "item.modifiers.chest": "裝備在身上時：", "item.modifiers.feet": "裝備在腳上時：", "item.modifiers.hand": "手持時：", "item.modifiers.head": "裝備於頭部時：", "item.modifiers.legs": "裝備在腿上時：", "item.modifiers.mainhand": "在慣用手時：", "item.modifiers.offhand": "在非慣用手時：", "item.modifiers.saddle": "裝備鞍時：", "item.nbt_tags": "NBT：%s 個標籤", "item.op_block_warning.line1": "警告：", "item.op_block_warning.line2": "使用此物品可能會導致指令執行", "item.op_block_warning.line3": "除非您完全了解其內容，否則請勿使用！", "item.unbreakable": "無法破壞", "itemGroup.buildingBlocks": "建築方塊", "itemGroup.coloredBlocks": "染色方塊", "itemGroup.combat": "戰鬥", "itemGroup.consumables": "消耗品", "itemGroup.crafting": "合成用品", "itemGroup.foodAndDrink": "食物及飲品", "itemGroup.functional": "功能方塊", "itemGroup.hotbar": "已儲存的快捷欄", "itemGroup.ingredients": "原材料", "itemGroup.inventory": "生存模式物品欄", "itemGroup.natural": "自然方塊", "itemGroup.op": "管理員實用物品", "itemGroup.redstone": "紅石方塊", "itemGroup.search": "搜尋物品", "itemGroup.spawnEggs": "生怪蛋", "itemGroup.tools": "工具及實用物品", "item_modifier.unknown": "未知的物品屬性：%s", "jigsaw_block.final_state": "轉變為：", "jigsaw_block.generate": "生成", "jigsaw_block.joint.aligned": "固定", "jigsaw_block.joint.rollable": "可旋轉", "jigsaw_block.joint_label": "拼接類型：", "jigsaw_block.keep_jigsaws": "儲存拼圖", "jigsaw_block.levels": "層數：%s", "jigsaw_block.name": "名稱：", "jigsaw_block.placement_priority": "放置優先順序：", "jigsaw_block.placement_priority.tooltip": "當此拼圖方塊連接到某個部件時，此處即為在更大的結構中各部件的處理連接的順序。\n\n部件依照優先順序由高到低處理，若優先順序相同，則依插入順序處理。", "jigsaw_block.pool": "目標池：", "jigsaw_block.selection_priority": "選擇優先順序：", "jigsaw_block.selection_priority.tooltip": "當父級部件處理連接時，此處即為拼圖方塊嘗試連接到目標部件的順序。\n\n拼圖方塊會依照優先順序由高到低處理，若優先順序相同，則依隨機順序處理。", "jigsaw_block.target": "目標名稱：", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON>（音樂盒）", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "進度", "key.attack": "攻擊／破壞", "key.back": "後退", "key.categories.creative": "創造模式", "key.categories.gameplay": "遊戲控制", "key.categories.inventory": "物品欄", "key.categories.misc": "雜項", "key.categories.movement": "角色移動", "key.categories.multiplayer": "多人遊戲", "key.categories.ui": "遊戲介面", "key.chat": "開啟聊天欄", "key.command": "開啟指令視窗", "key.drop": "丟棄已選擇的物品", "key.forward": "前進", "key.fullscreen": "切換全螢幕", "key.hotbar.1": "快捷欄 1", "key.hotbar.2": "快捷欄 2", "key.hotbar.3": "快捷欄 3", "key.hotbar.4": "快捷欄 4", "key.hotbar.5": "快捷欄 5", "key.hotbar.6": "快捷欄 6", "key.hotbar.7": "快捷欄 7", "key.hotbar.8": "快捷欄 8", "key.hotbar.9": "快捷欄 9", "key.inventory": "開啟／關閉物品欄", "key.jump": "跳躍", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "↓ 鍵", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "數字鍵 0", "key.keyboard.keypad.1": "數字鍵 1", "key.keyboard.keypad.2": "數字鍵 2", "key.keyboard.keypad.3": "數字鍵 3", "key.keyboard.keypad.4": "數字鍵 4", "key.keyboard.keypad.5": "數字鍵 5", "key.keyboard.keypad.6": "數字鍵 6", "key.keyboard.keypad.7": "數字鍵 7", "key.keyboard.keypad.8": "數字鍵 8", "key.keyboard.keypad.9": "數字鍵 9", "key.keyboard.keypad.add": "數字鍵 +", "key.keyboard.keypad.decimal": "數字鍵 .", "key.keyboard.keypad.divide": "數字鍵 /", "key.keyboard.keypad.enter": "數字鍵 Enter", "key.keyboard.keypad.equal": "數字鍵 =", "key.keyboard.keypad.multiply": "數字鍵 *", "key.keyboard.keypad.subtract": "數字鍵 -", "key.keyboard.left": "← 鍵", "key.keyboard.left.alt": "左 Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "左 Ctrl", "key.keyboard.left.shift": "左 Shift", "key.keyboard.left.win": "左 Windows", "key.keyboard.menu": "選單鍵", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "→ 鍵", "key.keyboard.right.alt": "右 Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "右 Ctrl", "key.keyboard.right.shift": "右 Shift", "key.keyboard.right.win": "右 Windows", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "空白鍵", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "未指定", "key.keyboard.up": "↑ 鍵", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "往左", "key.loadToolbarActivator": "載入快捷欄", "key.mouse": "滑鼠鍵 %1$s", "key.mouse.left": "左鍵", "key.mouse.middle": "中鍵", "key.mouse.right": "右鍵", "key.pickItem": "選取方塊", "key.playerlist": "列出玩家", "key.quickActions": "快速動作", "key.right": "往右", "key.saveToolbarActivator": "儲存快捷欄", "key.screenshot": "擷取螢幕畫面", "key.smoothCamera": "切換視角平滑移動", "key.sneak": "潛行", "key.socialInteractions": "社群交流畫面", "key.spectatorOutlines": "標示玩家（旁觀者）", "key.sprint": "跑步", "key.swapOffhand": "互換雙手物品", "key.togglePerspective": "切換視角", "key.use": "使用物品／放置方塊", "known_server_link.announcements": "公告", "known_server_link.community": "社群", "known_server_link.community_guidelines": "社群規範", "known_server_link.feedback": "意見回饋", "known_server_link.forums": "論壇", "known_server_link.news": "新聞", "known_server_link.report_bug": "回報伺服器錯誤", "known_server_link.status": "狀態", "known_server_link.support": "支援", "known_server_link.website": "網站", "lanServer.otherPlayers": "對其他玩家的設定", "lanServer.port": "連接埠號碼", "lanServer.port.invalid": "無效的連接埠。\n請將編輯欄留空，或是輸入一個 1024 到 65535 之間的數值。", "lanServer.port.invalid.new": "無效的連接埠。\n請將編輯欄留空，或是輸入一個 %s 到 %s 之間的數值。", "lanServer.port.unavailable": "無法使用這個連接埠。\n請將編輯欄留空，或是輸入一個 1024 到 65535 之間的新數值。", "lanServer.port.unavailable.new": "無法使用這個連接埠。\n請將編輯欄留空，或是輸入一個 %s 到 %s 之間的新數值。", "lanServer.scanning": "正在您的區域網路內尋找可供加入的遊戲", "lanServer.start": "開始區網世界", "lanServer.title": "區網世界", "language.code": "zho-Hant_TW", "language.name": "繁體中文", "language.region": "台灣", "lectern.take_book": "取下書本", "loading.progress": "%s%%", "mco.account.privacy.info": "閱讀更多關於 Mojang 以及隱私權法的資訊", "mco.account.privacy.info.button": "關於《一般資料保護規則》(GDPR)", "mco.account.privacy.information": "Mojang 為了保護兒童及其隱私權施行了許多政策，包含《兒童線上隱私保護法》(COPPA) 與《一般資料保護規則》(GDPR)。\n\n您在存取 Realms 帳號之前可能需要取得家長的同意。", "mco.account.privacyinfo": "Mojang 為了保護兒童及其隱私權施行了許多政策，包含《兒童線上隱私保護法》(COPPA) 與《一般資料保護規則》(GDPR)。\n\n您在存取 Realms 帳號之前可能需要取得家長的同意。\n\n如果您擁有較舊的 Minecraft 帳號（以使用者名稱登入），您必須將帳號轉移為 Mojang 帳號以使用 Realms。", "mco.account.update": "更新帳號", "mco.activity.noactivity": "在過去的 %s 天沒有活動", "mco.activity.title": "玩家活動", "mco.backup.button.download": "下載最新備份", "mco.backup.button.reset": "重設世界", "mco.backup.button.restore": "還原", "mco.backup.button.upload": "上傳世界", "mco.backup.changes.tooltip": "改變", "mco.backup.entry": "備份 (%s)", "mco.backup.entry.description": "詳細資訊", "mco.backup.entry.enabledPack": "已啟用的資源或資料包", "mco.backup.entry.gameDifficulty": "遊戲難易度", "mco.backup.entry.gameMode": "遊戲模式", "mco.backup.entry.gameServerVersion": "遊戲伺服器版本", "mco.backup.entry.name": "名稱", "mco.backup.entry.seed": "種子碼", "mco.backup.entry.templateName": "模板名稱", "mco.backup.entry.undefined": "未定義的變更", "mco.backup.entry.uploaded": "已上傳", "mco.backup.entry.worldType": "世界類型", "mco.backup.generate.world": "產生世界", "mco.backup.info.title": "自上次備份以來發生的變更", "mco.backup.narration": "自 %s 備份", "mco.backup.nobackups": "這個 Realm 目前沒有任何備份。", "mco.backup.restoring": "正在還原您的 Realm", "mco.backup.unknown": "未知", "mco.brokenworld.download": "下載", "mco.brokenworld.downloaded": "已下載", "mco.brokenworld.message.line1": "請重設或選擇另一個世界。", "mco.brokenworld.message.line2": "你也可以下載此地圖到單人遊戲", "mco.brokenworld.minigame.title": "這個小遊戲不再被支援", "mco.brokenworld.nonowner.error": "請等待 Realm 的擁有者重設世界", "mco.brokenworld.nonowner.title": "世界已過期", "mco.brokenworld.play": "開始遊戲", "mco.brokenworld.reset": "重設", "mco.brokenworld.title": "你當前的世界不再被支援", "mco.client.incompatible.msg.line1": "您的用戶端與 Realms 不相容。", "mco.client.incompatible.msg.line2": "請使用最新版本的 Minecraft。", "mco.client.incompatible.msg.line3": "Realms 與快照版本不相容。", "mco.client.incompatible.title": "用戶端不相容！", "mco.client.outdated.stable.version": "您的用戶端版本 (%s) 與 Realms 不相容。\n\n請使用最新版本的 Minecraft。", "mco.client.unsupported.snapshot.version": "您的用戶端版本 (%s) 與 Realms 不相容。\n\nRealms 無法在這個快照版本使用。", "mco.compatibility.downgrade": "降低版本", "mco.compatibility.downgrade.description": "這個世界最後一次遊玩的版本為 %s，而您使用的版本為 %s。降低世界版本可能導致資料損毀，我們無法保證世界能讀取或正常運作。\n\n您的世界備份會儲存於「世界備份」中。如有需要可復原世界。", "mco.compatibility.incompatible.popup.title": "版本不相容", "mco.compatibility.incompatible.releaseType.popup.message": "您嘗試加入的世界與目前版本不相容。", "mco.compatibility.incompatible.series.popup.message": "這個世界最後一次遊玩的版本為 %s，而您使用的版本為 %s。\n\n這些版本互不相容。需要建立新的世界才能遊玩這個版本。", "mco.compatibility.unverifiable.message": "無法驗證這個世界上一次遊玩的版本。如果升級或降級世界的版本，則會自動建立備份，並儲存於「世界備份」中。", "mco.compatibility.unverifiable.title": "無法驗證相容性", "mco.compatibility.upgrade": "升級", "mco.compatibility.upgrade.description": "這個世界最後一次遊玩的版本為 %s，而您使用的版本為 %s。\n\n您的世界備份會儲存於「世界備份」中。\n\n如有需要可復原世界。", "mco.compatibility.upgrade.friend.description": "這個世界最後一次遊玩的版本為 %s，而您使用的版本為 %s。\n\n您的世界備份會儲存於「世界備份」中。\n\n如有需要，Realm 的擁有者可復原世界。", "mco.compatibility.upgrade.title": "您確定要升級這個世界嗎？", "mco.configure.current.minigame": "目前", "mco.configure.world.activityfeed.disabled": "玩家動態暫時無法使用", "mco.configure.world.backup": "世界備份", "mco.configure.world.buttons.activity": "玩家活動", "mco.configure.world.buttons.close": "暫時關閉 Realm", "mco.configure.world.buttons.delete": "刪除", "mco.configure.world.buttons.done": "完成", "mco.configure.world.buttons.edit": "設定", "mco.configure.world.buttons.invite": "邀請玩家", "mco.configure.world.buttons.moreoptions": "更多選項", "mco.configure.world.buttons.newworld": "新的世界", "mco.configure.world.buttons.open": "重新開啟 Realm", "mco.configure.world.buttons.options": "世界選項", "mco.configure.world.buttons.players": "玩家", "mco.configure.world.buttons.region_preference": "選擇地區...", "mco.configure.world.buttons.resetworld": "重設世界", "mco.configure.world.buttons.save": "儲存", "mco.configure.world.buttons.settings": "設定", "mco.configure.world.buttons.subscription": "訂閱", "mco.configure.world.buttons.switchminigame": "切換小遊戲", "mco.configure.world.close.question.line1": "您可以暫時關閉 Realm，以防有人在調整時進入遊玩。準備就緒後就可以重新開啟。\n\n此操作不會取消您的 Realms 訂閱。", "mco.configure.world.close.question.line2": "您確定要繼續嗎？", "mco.configure.world.close.question.title": "需要在不中斷遊玩的情況下，做出變更嗎？", "mco.configure.world.closing": "正在暫時關閉 Realm...", "mco.configure.world.commandBlocks": "指令方塊", "mco.configure.world.delete.button": "刪除 Realm", "mco.configure.world.delete.question.line1": "你的 Realm 將被永久刪除", "mco.configure.world.delete.question.line2": "您確定要繼續嗎？", "mco.configure.world.description": "Realm 的描述", "mco.configure.world.edit.slot.name": "世界名稱", "mco.configure.world.edit.subscreen.adventuremap": "由於您目前的世界是冒險世界，因此某些設定將被停用", "mco.configure.world.edit.subscreen.experience": "由於您目前的世界是體驗世界，因此某些設定將被停用", "mco.configure.world.edit.subscreen.inspiration": "由於您當前的世界為靈感，因此某些設定將被停用", "mco.configure.world.forceGameMode": "鎖定遊戲模式", "mco.configure.world.invite.narration": "您有 %s 個新邀請", "mco.configure.world.invite.profile.name": "名稱", "mco.configure.world.invited": "已邀請", "mco.configure.world.invited.number": "已邀請 (%s)", "mco.configure.world.invites.normal.tooltip": "普通玩家", "mco.configure.world.invites.ops.tooltip": "管理員", "mco.configure.world.invites.remove.tooltip": "移除", "mco.configure.world.leave.question.line1": "如果你離開了這個 Realm，除非再次受到邀請，否則您將不會再看到它", "mco.configure.world.leave.question.line2": "您確定要繼續嗎？", "mco.configure.world.loading": "正在載入 Realm", "mco.configure.world.location": "位置", "mco.configure.world.minigame": "目前：%s", "mco.configure.world.name": "Realm 名稱", "mco.configure.world.opening": "正在開啟 Realm...", "mco.configure.world.players.error": "提供之玩家名稱不存在", "mco.configure.world.players.inviting": "正在邀請玩家...", "mco.configure.world.players.title": "玩家", "mco.configure.world.pvp": "玩家對戰", "mco.configure.world.region_preference": "地區偏好", "mco.configure.world.region_preference.title": "地區偏好選擇", "mco.configure.world.reset.question.line1": "您的世界將重新生成，您將失去當前的世界", "mco.configure.world.reset.question.line2": "您確定要繼續嗎？", "mco.configure.world.resourcepack.question": "這個 Realm 需使用自訂的資源包\n\n您想要自動下載並安裝它嗎？", "mco.configure.world.resourcepack.question.line1": "這個 Realm 需使用自訂的資源包", "mco.configure.world.resourcepack.question.line2": "您想要自動下載並安裝它嗎？", "mco.configure.world.restore.download.question.line1": "世界將在下載後加入您的單人遊戲世界中。", "mco.configure.world.restore.download.question.line2": "確定要繼續嗎？", "mco.configure.world.restore.question.line1": "你的 Realm 世界將被還原至日期 '%s' (%s)", "mco.configure.world.restore.question.line2": "您確定要繼續嗎？", "mco.configure.world.settings.expired": "您無法編輯已過期 Realm 的設定", "mco.configure.world.settings.title": "設定", "mco.configure.world.slot": "世界 %s", "mco.configure.world.slot.empty": "空白", "mco.configure.world.slot.switch.question.line1": "您的 Realm 將被切換至另一個世界", "mco.configure.world.slot.switch.question.line2": "您確定要繼續嗎？", "mco.configure.world.slot.tooltip": "切換到世界", "mco.configure.world.slot.tooltip.active": "加入", "mco.configure.world.slot.tooltip.minigame": "切換至小遊戲", "mco.configure.world.spawnAnimals": "生成動物", "mco.configure.world.spawnMonsters": "生成怪物", "mco.configure.world.spawnNPCs": "生成 NPC", "mco.configure.world.spawnProtection": "重生點保護", "mco.configure.world.spawn_toggle.message": "關閉此選項將會移除該類型的所有現存實體", "mco.configure.world.spawn_toggle.message.npc": "關閉此選項將會移除該類型的所有現存實體，例如村民", "mco.configure.world.spawn_toggle.title": "警告！", "mco.configure.world.status": "狀態", "mco.configure.world.subscription.day": "天", "mco.configure.world.subscription.days": "天", "mco.configure.world.subscription.expired": "已過期", "mco.configure.world.subscription.extend": "延長訂閱時間", "mco.configure.world.subscription.less_than_a_day": "少於一天", "mco.configure.world.subscription.month": "個月", "mco.configure.world.subscription.months": "個月", "mco.configure.world.subscription.recurring.daysleft": "將自動更新於", "mco.configure.world.subscription.recurring.info": "對您的 Realms 訂閱所做的變更（例如續期或關閉自動續費等）將在您的下一個帳單日期生效。", "mco.configure.world.subscription.remaining.days": "%1$s 天", "mco.configure.world.subscription.remaining.months": "%1$s 個月", "mco.configure.world.subscription.remaining.months.days": "%1$s 個月 %2$s 天", "mco.configure.world.subscription.start": "開始日期", "mco.configure.world.subscription.tab": "訂閱", "mco.configure.world.subscription.timeleft": "剩餘時間", "mco.configure.world.subscription.title": "訂閱內容資訊", "mco.configure.world.subscription.unknown": "未知", "mco.configure.world.switch.slot": "建立世界", "mco.configure.world.switch.slot.subtitle": "這個世界是空的，請選擇您要進行的動作", "mco.configure.world.title": "設定 Realm：", "mco.configure.world.uninvite.player": "您確定要取消邀請「%s」嗎？", "mco.configure.world.uninvite.question": "您是否確定要取消邀請", "mco.configure.worlds.title": "世界", "mco.connect.authorizing": "登入中...", "mco.connect.connecting": "正在連線至 Realm...", "mco.connect.failed": "無法連線至 Realm", "mco.connect.region": "伺服器地區：%s", "mco.connect.success": "完成", "mco.create.world": "建立", "mco.create.world.error": "您必須輸入一個名稱！", "mco.create.world.failed": "無法建立世界！", "mco.create.world.reset.title": "正在建立世界...", "mco.create.world.skip": "略過", "mco.create.world.subtitle": "隨個人喜好，選擇要放進您新 Realm 的世界", "mco.create.world.wait": "正在建立 Realm...", "mco.download.cancelled": "下載已取消", "mco.download.confirmation.line1": "您即將下載的世界檔案大小大於 %s", "mco.download.confirmation.line2": "您將無法再度上傳這個世界至 Realm。", "mco.download.confirmation.oversized": "您即將下載的世界檔案大小大於 %s\n\n您將無法再度上傳這個世界至 Realm", "mco.download.done": "下載完成", "mco.download.downloading": "下載中", "mco.download.extracting": "正在提取中", "mco.download.failed": "下載失敗", "mco.download.percent": "%s %%", "mco.download.preparing": "正在準備下載", "mco.download.resourcePack.fail": "資源包下載失敗！", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "每秒%s", "mco.download.title": "下載最新的世界", "mco.error.invalid.session.message": "請嘗試重新開啟 Minecraft", "mco.error.invalid.session.title": "無效的 session", "mco.errorMessage.6001": "用戶端版本太舊", "mco.errorMessage.6002": "尚未接受使用條款", "mco.errorMessage.6003": "超過下載限制", "mco.errorMessage.6004": "超過上傳限制", "mco.errorMessage.6005": "世界已鎖定", "mco.errorMessage.6006": "世界已過期", "mco.errorMessage.6007": "玩家所在的 Realms 過多", "mco.errorMessage.6008": "無效的 Realm 名稱", "mco.errorMessage.6009": "無效的 Realm 描述", "mco.errorMessage.connectionFailure": "發生錯誤，請稍後再試。", "mco.errorMessage.generic": "發生錯誤：", "mco.errorMessage.initialize.failed": "無法初始化 Realm", "mco.errorMessage.noDetails": "未提供錯誤詳細資訊", "mco.errorMessage.realmsService": "發生錯誤（%s）：", "mco.errorMessage.realmsService.configurationError": "編輯世界選項時發生了非預期的錯誤", "mco.errorMessage.realmsService.connectivity": "無法連線到 Realms：%s", "mco.errorMessage.realmsService.realmsError": "Realms（%s）：", "mco.errorMessage.realmsService.unknownCompatibility": "無法檢查相容版本，回應為：%s", "mco.errorMessage.retry": "請重新操作", "mco.errorMessage.serviceBusy": "Realms 目前忙碌中。\n請稍候數分鐘再重新嘗試連線。", "mco.gui.button": "按鈕", "mco.gui.ok": "確定", "mco.info": "資訊！", "mco.invited.player.narration": "已邀請玩家 %s", "mco.invites.button.accept": "接受", "mco.invites.button.reject": "拒絕", "mco.invites.nopending": "沒有待處理的邀請！", "mco.invites.pending": "新邀請！", "mco.invites.title": "待處理的邀請", "mco.minigame.world.changeButton": "選擇另一個小遊戲", "mco.minigame.world.info.line1": "此操作將把您的世界暫時替換為小遊戲！", "mco.minigame.world.info.line2": "之後您可以毫無損失地返回到原來的世界。", "mco.minigame.world.noSelection": "請選擇", "mco.minigame.world.restore": "正在結束小遊戲...", "mco.minigame.world.restore.question.line1": "小遊戲將結束，你的 Realm 將被還原。", "mco.minigame.world.restore.question.line2": "您確定要繼續嗎？", "mco.minigame.world.selected": "已選擇的小遊戲：", "mco.minigame.world.slot.screen.title": "正在切換世界...", "mco.minigame.world.startButton": "切換", "mco.minigame.world.starting.screen.title": "正在開始小遊戲...", "mco.minigame.world.stopButton": "結束小遊戲", "mco.minigame.world.switch.new": "選擇另一個小遊戲？", "mco.minigame.world.switch.title": "切換小遊戲", "mco.minigame.world.title": "將 Realm 切換至小遊戲", "mco.news": "Realms 新聞", "mco.notification.dismiss": "關閉", "mco.notification.transferSubscription.buttonText": "立即轉移", "mco.notification.transferSubscription.message": "Java Realms 訂閱正在轉移至 Microsoft Store。別讓您的訂閱過期！\n現在轉移即可免費獲得 30 天的 Realms。\n請前往 minecraft.net 的「個人檔案」頁面以轉移您的訂閱。", "mco.notification.visitUrl.buttonText.default": "開啟連結", "mco.notification.visitUrl.message.default": "請前往下方連結", "mco.onlinePlayers": "線上玩家", "mco.play.button.realm.closed": "Realm 已關閉", "mco.question": "問題", "mco.reset.world.adventure": "冒險", "mco.reset.world.experience": "體驗", "mco.reset.world.generate": "新的世界", "mco.reset.world.inspiration": "靈感", "mco.reset.world.resetting.screen.title": "正在重設世界...", "mco.reset.world.seed": "種子碼（可選）", "mco.reset.world.template": "Realm 範本", "mco.reset.world.title": "重設世界", "mco.reset.world.upload": "上傳世界", "mco.reset.world.warning": "這將取代您目前的 Realm 世界", "mco.selectServer.buy": "購買 Realm！", "mco.selectServer.close": "關閉", "mco.selectServer.closed": "已關閉的 Realm", "mco.selectServer.closeserver": "關閉 Realm", "mco.selectServer.configure": "設定 Realm", "mco.selectServer.configureRealm": "設定 Realm", "mco.selectServer.create": "建立 Realm", "mco.selectServer.create.subtitle": "選擇要放進你新 Realm 的世界", "mco.selectServer.expired": "已過期的 Realm", "mco.selectServer.expiredList": "您的 Realm 訂閱已經過期", "mco.selectServer.expiredRenew": "續期", "mco.selectServer.expiredSubscribe": "訂閱", "mco.selectServer.expiredTrial": "您的試用期已結束", "mco.selectServer.expires.day": "一天內將到期", "mco.selectServer.expires.days": "還有 %s 天到期", "mco.selectServer.expires.soon": "即將到期", "mco.selectServer.leave": "離開 Realm", "mco.selectServer.loading": "正在載入 Realm 清單", "mco.selectServer.mapOnlySupportedForVersion": "此地圖在 %s 不被支援", "mco.selectServer.minigame": "小遊戲：", "mco.selectServer.minigameName": "小遊戲：%s", "mco.selectServer.minigameNotSupportedInVersion": "不能在 %s 玩這個小遊戲", "mco.selectServer.noRealms": "你似乎還沒有 Realm。新增 Realm 來與朋友一起遊玩。", "mco.selectServer.note": "注意：", "mco.selectServer.open": "開啟 Realm", "mco.selectServer.openserver": "開啟 Realm", "mco.selectServer.play": "開始遊戲", "mco.selectServer.popup": "Realms可以同時讓最多十位好友安全又簡單地享受線上的 Minecraft 世界。 它支援許多小遊戲與大量的自訂世界！只有 Realm的擁有者需要支付費用。", "mco.selectServer.purchase": "新增 Realm", "mco.selectServer.trial": "取得試用版！", "mco.selectServer.uninitialized": "按此建立您的 Realm！", "mco.snapshot.createSnapshotPopup.text": "你即將建立免費的快照版 Realm，它會與您付費的 Realm 訂閱進行綁定。這份新的快照版 Realm 可以在付費訂閱生效期間隨時存取。原本的付費 Realm 將不會受到影響。", "mco.snapshot.createSnapshotPopup.title": "是否建立快照版 Realm？", "mco.snapshot.creating": "正在建立快照版 Realm…", "mco.snapshot.description": "與 %s 綁定", "mco.snapshot.friendsRealm.downgrade": "您需要使用版本 %s 來加入這個 Realm", "mco.snapshot.friendsRealm.upgrade": "在您使用這個快照進行遊戲前，%s 需升級其 Realm", "mco.snapshot.paired": "這個快照版 Realm 與 %s 綁定", "mco.snapshot.parent.tooltip": "使用最新版的 Minecraft 遊玩這個 Realm", "mco.snapshot.start": "開啟免費的快照版 Realm", "mco.snapshot.subscription.info": "這是與您 Realm 訂閱「%s」綁定的快照版 Realm。與其綁定的 Realm 可以使用時，它也會維持能夠使用。", "mco.snapshot.tooltip": "使用快照版 Realm 搶先體驗即將推出的 Minecraft 版本，其中可能包含了新功能及其他改動。\n\n您可以在遊戲的正式版本中找到一般的 Realm。", "mco.snapshotRealmsPopup.message": "從快照 23w41a 開始，Realms 可於快照中使用。每份 Realms 訂閱中，包含了一份獨立於您的普通 Java 版 Realm 的快照版 Realm！", "mco.snapshotRealmsPopup.title": "Realms 現在可於快照中使用", "mco.snapshotRealmsPopup.urlText": "了解更多", "mco.template.button.publisher": "發布者", "mco.template.button.select": "選擇", "mco.template.button.trailer": "預告片", "mco.template.default.name": "世界樣板", "mco.template.info.tooltip": "發布者網站", "mco.template.name": "樣板", "mco.template.select.failure": "我們無法接收這個類別的內容清單。\n請檢查您的網路連線，或是稍後再嘗試。", "mco.template.select.narrate.authors": "作者：%s", "mco.template.select.narrate.version": "版本 %s", "mco.template.select.none": "噢，這個類別目前似乎是空的。\n請之後再回來尋找新內容，而如果您是一位創作者，\n%s。", "mco.template.select.none.linkTitle": "您可以考慮上傳一些自己的東西", "mco.template.title": "世界範本", "mco.template.title.minigame": "Realm 小遊戲", "mco.template.trailer.tooltip": "地圖預告片", "mco.terms.buttons.agree": "同意", "mco.terms.buttons.disagree": "不同意", "mco.terms.sentence.1": "我同意 Minecraft Realms 所列明的條款", "mco.terms.sentence.2": "使用條款", "mco.terms.title": "Minecraft Realms 使用條款", "mco.time.daysAgo": "%1$s 天前", "mco.time.hoursAgo": "%1$s 小時前", "mco.time.minutesAgo": "%1$s 分鐘前", "mco.time.now": "現在", "mco.time.secondsAgo": "%1$s 秒前", "mco.trial.message.line1": "想獲得屬於您自己的 Realm 嗎？", "mco.trial.message.line2": "按此獲得更多資訊", "mco.upload.button.name": "上傳", "mco.upload.cancelled": "上傳已取消", "mco.upload.close.failure": "無法關閉您的 Realm，請稍後再試", "mco.upload.done": "上傳完成", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "上傳失敗！(%s)", "mco.upload.failed.too_big.description": "選取的世界過大。大小不得超過 %s。", "mco.upload.failed.too_big.title": "世界過大", "mco.upload.hardcore": "不可上傳極限模式的世界！", "mco.upload.percent": "%s %%", "mco.upload.preparing": "正在準備您的世界資料", "mco.upload.select.world.none": "找不到單人遊戲世界！", "mco.upload.select.world.subtitle": "請選擇一個單人遊戲世界來上傳", "mco.upload.select.world.title": "上傳世界", "mco.upload.size.failure.line1": "「%s」太大了！", "mco.upload.size.failure.line2": "大小為 %s。大小上限為 %s。", "mco.upload.uploading": "正在上傳「%s」", "mco.upload.verifying": "正在驗證您的世界", "mco.version": "版本：%s", "mco.warning": "警告！", "mco.worldSlot.minigame": "小遊戲", "menu.custom_options": "自訂選項...", "menu.custom_options.title": "自訂選項", "menu.custom_options.tooltip": "注意：自訂選項是由第三方伺服器與／或內容提供。\n請小心使用！", "menu.custom_screen_info.button_narration": "這是一個自訂畫面。了解更多資訊。", "menu.custom_screen_info.contents": "這個畫面的內容是由第三方伺服器和地圖控制，不由 Mojang Studios 或 Microsoft 擁有、營運或監督。\n\n請小心操作！點擊連結時務必謹慎，切勿洩露你的個人資訊，包括帳號密碼等。\n\n如果這個畫面阻止你繼續，可以點擊下方按鈕從目前伺服器中斷連線。", "menu.custom_screen_info.disconnect": "已拒絕自訂畫面", "menu.custom_screen_info.title": "關於自訂畫面的說明", "menu.custom_screen_info.tooltip": "這是一個自訂畫面。按此了解更多資訊。", "menu.disconnect": "中斷連線", "menu.feedback": "意見回饋...", "menu.feedback.title": "意見回饋", "menu.game": "遊戲目錄", "menu.modded": " (Modded)", "menu.multiplayer": "多人遊戲", "menu.online": "Minecraft Realms", "menu.options": "選項...", "menu.paused": "遊戲暫停", "menu.playdemo": "遊玩試玩世界", "menu.playerReporting": "檢舉玩家", "menu.preparingSpawn": "正在準備重生點區域：%s%%", "menu.quick_actions": "快速動作...", "menu.quick_actions.title": "快速動作", "menu.quit": "離開遊戲", "menu.reportBugs": "回報錯誤", "menu.resetdemo": "重設試玩世界", "menu.returnToGame": "繼續遊戲", "menu.returnToMenu": "儲存並回到標題畫面", "menu.savingChunks": "正在儲存區塊", "menu.savingLevel": "正在儲存世界", "menu.sendFeedback": "提供意見回饋", "menu.server_links": "伺服器連結...", "menu.server_links.title": "伺服器連結", "menu.shareToLan": "在區網上公開", "menu.singleplayer": "單人遊戲", "menu.working": "正在處理...", "merchant.deprecated": "村民每天最多補貨兩次。", "merchant.level.1": "新手", "merchant.level.2": "學徒", "merchant.level.3": "老手", "merchant.level.4": "專家", "merchant.level.5": "大師", "merchant.title": "%s - %s", "merchant.trades": "交易", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "按 %1$s 取消乘坐", "multiplayer.applyingPack": "正在套用資源包", "multiplayer.confirm_command.parse_errors": "你嘗試執行的指令無法辨識或無效。\n確定要執行嗎？\n指令：%s", "multiplayer.confirm_command.permissions_required": "你嘗試執行的指令需要較高的權限。\n這可能會對你的遊戲產生負面影響。\n確定要執行嗎？\n指令：%s", "multiplayer.confirm_command.title": "確認執行指令", "multiplayer.disconnect.authservers_down": "驗證伺服器暫停服務，請稍後再試，非常抱歉！", "multiplayer.disconnect.bad_chat_index": "偵測到來自伺服器已遺失或已重新排序的聊天訊息", "multiplayer.disconnect.banned": "您已經被這個伺服器封鎖", "multiplayer.disconnect.banned.expiration": "\n您的封鎖將於 %s 解除", "multiplayer.disconnect.banned.reason": "您已被這個伺服器封鎖。\n原因：%s", "multiplayer.disconnect.banned_ip.expiration": "\n您的封鎖將於 %s 解除", "multiplayer.disconnect.banned_ip.reason": "您的 IP 位址已被這個伺服器封鎖。\n原因：%s", "multiplayer.disconnect.chat_validation_failed": "聊天訊息驗證失敗", "multiplayer.disconnect.duplicate_login": "您已從其他位置登入", "multiplayer.disconnect.expired_public_key": "個人資訊公鑰已過期。請檢查系統時間已同步處理，並嘗試重新啟動遊戲。", "multiplayer.disconnect.flying": "此伺服器並沒有啟用飛行", "multiplayer.disconnect.generic": "連線中斷", "multiplayer.disconnect.idling": "您的閒置時間過長！", "multiplayer.disconnect.illegal_characters": "聊天欄中出現不允許的字元", "multiplayer.disconnect.incompatible": "用戶端不相容！請使用 %s", "multiplayer.disconnect.invalid_entity_attacked": "試圖攻擊無效的實體", "multiplayer.disconnect.invalid_packet": "伺服器傳送無效的封包", "multiplayer.disconnect.invalid_player_data": "無效的玩家資料", "multiplayer.disconnect.invalid_player_movement": "接收到無效的玩家移動封包", "multiplayer.disconnect.invalid_public_key_signature": "無效的個人資訊公鑰。\n請嘗試重啟遊戲。", "multiplayer.disconnect.invalid_public_key_signature.new": "無效的個人資訊公鑰簽章。\n請嘗試重新啟動遊戲。", "multiplayer.disconnect.invalid_vehicle_movement": "接收到無效的載具移動封包", "multiplayer.disconnect.ip_banned": "您的 IP 位址已被這個伺服器封鎖", "multiplayer.disconnect.kicked": "已被管理員踢出", "multiplayer.disconnect.missing_tags": "從伺服器接收到不完整的標籤集。\n請聯絡伺服器管理員。", "multiplayer.disconnect.name_taken": "該名稱已被使用", "multiplayer.disconnect.not_whitelisted": "你不在這個伺服器的白名單中！", "multiplayer.disconnect.out_of_order_chat": "收到的聊天封包順序不正確。您的系統時間是否已變更？", "multiplayer.disconnect.outdated_client": "用戶端不相容！請使用 %s", "multiplayer.disconnect.outdated_server": "用戶端不相容！請使用 %s", "multiplayer.disconnect.server_full": "伺服器已滿！", "multiplayer.disconnect.server_shutdown": "伺服器已關閉", "multiplayer.disconnect.slow_login": "登入逾時", "multiplayer.disconnect.too_many_pending_chats": "太多未經認可的聊天訊息", "multiplayer.disconnect.transfers_disabled": "伺服器不接受轉移", "multiplayer.disconnect.unexpected_query_response": "從用戶端接收到預期外的自訂資料", "multiplayer.disconnect.unsigned_chat": "收到缺少或含有無效簽章的聊天封包。", "multiplayer.disconnect.unverified_username": "無法驗證使用者名稱！", "multiplayer.downloadingStats": "正在下載統計資訊...", "multiplayer.downloadingTerrain": "正在載入地形...", "multiplayer.lan.server_found": "找到新的伺服器：%s", "multiplayer.message_not_delivered": "無法傳送聊天訊息，請檢查伺服器紀錄：%s", "multiplayer.player.joined": "%s 加入了遊戲", "multiplayer.player.joined.renamed": "%s （之前是 %s）加入了遊戲", "multiplayer.player.left": "%s 離開了遊戲", "multiplayer.player.list.hp": "%s 生命值", "multiplayer.player.list.narration": "在線玩家：%s", "multiplayer.requiredTexturePrompt.disconnect": "伺服器要求使用其自訂的資源包", "multiplayer.requiredTexturePrompt.line1": "此伺服器要求使用其自訂的資源包。", "multiplayer.requiredTexturePrompt.line2": "拒絕此自訂資源包將使你和伺服器的連線中斷。", "multiplayer.socialInteractions.not_available": "社群交流僅適用於多人遊戲世界", "multiplayer.status.and_more": "... 以及另外 %s 人 ...", "multiplayer.status.cancelled": "已取消", "multiplayer.status.cannot_connect": "無法連線到伺服器", "multiplayer.status.cannot_resolve": "無法解析主機名稱", "multiplayer.status.finished": "完成", "multiplayer.status.incompatible": "遊戲版本不相容！", "multiplayer.status.motd.narration": "伺服器訊息：%s", "multiplayer.status.no_connection": "（未連線）", "multiplayer.status.old": "舊版", "multiplayer.status.online": "線上", "multiplayer.status.ping": "%s 毫秒", "multiplayer.status.ping.narration": "延遲為 %s 毫秒", "multiplayer.status.pinging": "Pinging...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "有 %s 位玩家在線上，共 %s 位玩家", "multiplayer.status.quitting": "正在離開", "multiplayer.status.request_handled": "已處理傳送的狀態請求", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "接收到未請求的資訊", "multiplayer.status.version.narration": "伺服器版本：%s", "multiplayer.stopSleeping": "起床", "multiplayer.texturePrompt.failure.line1": "無法套用伺服器資源包", "multiplayer.texturePrompt.failure.line2": "任何需要自訂資源包的功能可能無法依預期執行", "multiplayer.texturePrompt.line1": "此伺服器建議使用其自訂的資源包。", "multiplayer.texturePrompt.line2": "您是否希望它自動下載並安裝？", "multiplayer.texturePrompt.serverPrompt": "%s\n\n訊息來自於以下伺服器：\n%s", "multiplayer.title": "進行多人遊戲", "multiplayer.unsecureserver.toast": "此伺服器中的聊天訊息可能經過修改，無法反映出原始的訊息", "multiplayer.unsecureserver.toast.title": "無法驗證聊天訊息", "multiplayerWarning.check": "不再顯示此畫面", "multiplayerWarning.header": "警告：第三方遊戲伺服器", "multiplayerWarning.message": "警告：第三方伺服器所提供的遊戲不隸屬於 Mojang Studios 或 Microsoft， 不會受到我們的管理或監督。 線上遊玩時，您可能會遭遇到未受管理的聊天訊息、或是其他由使用者製造的內容，而這些內容可能不適合所有人。", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "谷岡久美 - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "谷岡久美 - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "谷岡久美 - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "谷岡久美 - komorebi", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "谷岡久美 - poko<PERSON>ko", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "谷岡久美 - yakusoku", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "按鈕：%s", "narration.button.usage.focused": "按下 Enter 以啟用", "narration.button.usage.hovered": "按下左鍵以啟用", "narration.checkbox": "核取方塊：%s", "narration.checkbox.usage.focused": "按下 Enter 以切換", "narration.checkbox.usage.hovered": "按下左鍵以切換", "narration.component_list.usage": "按 Tab 移動到下個元素", "narration.cycle_button.usage.focused": "按下 Enter 以切換至 %s", "narration.cycle_button.usage.hovered": "按下左鍵以切換至 %s", "narration.edit_box": "編輯欄：%s", "narration.item": "物品：%s", "narration.recipe": "%s 的合成配方", "narration.recipe.usage": "按下左鍵以選取", "narration.recipe.usage.more": "按下右鍵顯示更多合成配方", "narration.selection.usage": "按上下方向鍵移動到其他項目", "narration.slider.usage.focused": "按下左右方向鍵變更數值", "narration.slider.usage.hovered": "拉動滑桿以變更數值", "narration.suggestion": "已從 %2$s 項建議中選擇了第 %1$s 項：%3$s", "narration.suggestion.tooltip": "已從 %2$s 項建議中選擇了第 %1$s 項：%3$s (%4$s)", "narration.suggestion.usage.cycle.fixed": "按下 Tab 以切換至下一個建議", "narration.suggestion.usage.cycle.hidable": "按下 Tab 以切換至下一個建議，或按下 Escape 以忽略建議", "narration.suggestion.usage.fill.fixed": "按下 Tab 以使用建議", "narration.suggestion.usage.fill.hidable": "按下 Tab 以使用建議，或按下 Escape 以忽略建議", "narration.tab_navigation.usage": "使用 Ctrl 及 Tab 鍵以切換頁籤", "narrator.button.accessibility": "協助工具", "narrator.button.difficulty_lock": "難易度鎖定", "narrator.button.difficulty_lock.locked": "已鎖定", "narrator.button.difficulty_lock.unlocked": "未鎖定", "narrator.button.language": "語言", "narrator.controls.bound": "%s 已被綁定為 %s", "narrator.controls.reset": "重設 %s 按鈕", "narrator.controls.unbound": "%s 未被綁定", "narrator.joining": "正在加入", "narrator.loading": "正在載入：%s", "narrator.loading.done": "完成", "narrator.position.list": "已選擇清單 %2$s 列中的第 %1$s 列", "narrator.position.object_list": "已選擇 %2$s 個列元素中的第 %1$s 個", "narrator.position.screen": "%2$s 個畫面元素中的第 %1$s 個", "narrator.position.tab": "已選擇第 %s 個頁籤，共 %s 個", "narrator.ready_to_play": "準備就緒", "narrator.screen.title": "標題畫面", "narrator.screen.usage": "使用滑鼠游標或是 Tab 鍵選擇元素", "narrator.select": "已選擇：%s", "narrator.select.world": "已選擇 %s，最後遊玩時間為 %s，%s，%s，版本為 %s", "narrator.select.world_info": "已選擇 %s，上次遊玩時間：%s，%s", "narrator.toast.disabled": "已關閉朗讀功能", "narrator.toast.enabled": "已啟用朗讀功能", "optimizeWorld.confirm.description": "這項操作將嘗試以最新的遊戲資料格式最佳化您的世界。視情況可能會用上一段很長的時間。完成後，您的世界可能會更順暢，但將不再相容於舊版的遊戲。您確定要繼續嗎？", "optimizeWorld.confirm.proceed": "建立備份並最佳化", "optimizeWorld.confirm.title": "最佳化世界", "optimizeWorld.info.converted": "已升級的區塊數：%s", "optimizeWorld.info.skipped": "已略過的區塊數：%s", "optimizeWorld.info.total": "總區塊數：%s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "正在計算區塊...", "optimizeWorld.stage.failed": "失敗！:(", "optimizeWorld.stage.finished": "正在完成...", "optimizeWorld.stage.finished.chunks": "正在完成區塊升級...", "optimizeWorld.stage.finished.entities": "正在完成實體升級...", "optimizeWorld.stage.finished.poi": "正在完成興趣點升級...", "optimizeWorld.stage.upgrading": "正在升級所有區塊...", "optimizeWorld.stage.upgrading.chunks": "正在升級所有區塊...", "optimizeWorld.stage.upgrading.entities": "正在升級所有實體...", "optimizeWorld.stage.upgrading.poi": "正在升級所有興趣點...", "optimizeWorld.title": "正在最佳化世界「%s」", "options.accessibility": "協助工具設定...", "options.accessibility.high_contrast": "高對比度", "options.accessibility.high_contrast.error.tooltip": "無法使用高對比度資源包。", "options.accessibility.high_contrast.tooltip": "提高 UI 控制項的對比度。", "options.accessibility.high_contrast_block_outline": "高對比度方塊輪廓", "options.accessibility.high_contrast_block_outline.tooltip": "增強目標方塊的輪廓對比度。", "options.accessibility.link": "協助工具指南", "options.accessibility.menu_background_blurriness": "選單背景模糊程度", "options.accessibility.menu_background_blurriness.tooltip": "變更選單背景的模糊程度。", "options.accessibility.narrator_hotkey": "朗讀功能快捷鍵", "options.accessibility.narrator_hotkey.mac.tooltip": "允許使用「Cmd + B」開啟及關閉朗讀功能。", "options.accessibility.narrator_hotkey.tooltip": "允許使用「Ctrl + B」開啟及關閉朗讀功能。", "options.accessibility.panorama_speed": "全景圖捲動速度", "options.accessibility.text_background": "文字背景", "options.accessibility.text_background.chat": "聊天欄", "options.accessibility.text_background.everywhere": "全部", "options.accessibility.text_background_opacity": "文字背景不透明度", "options.accessibility.title": "協助工具設定", "options.allowServerListing": "允許伺服器列出您的名稱", "options.allowServerListing.tooltip": "伺服器可能會公開列出目前上線的玩家。\n若關閉此選項，您的名稱就不會被列出。", "options.ao": "柔和光源", "options.ao.max": "最大值", "options.ao.min": "最小值", "options.ao.off": "關閉", "options.attack.crosshair": "十字準星", "options.attack.hotbar": "快捷欄", "options.attackIndicator": "攻擊顯示計", "options.audioDevice": "裝置", "options.audioDevice.default": "系統預設值", "options.autoJump": "自動跳躍", "options.autoSuggestCommands": "指令提示", "options.autosaveIndicator": "自動儲存指示器", "options.biomeBlendRadius": "生態域混合", "options.biomeBlendRadius.1": "關閉（最快）", "options.biomeBlendRadius.11": "11×11（極高）", "options.biomeBlendRadius.13": "13×13（炫耀）", "options.biomeBlendRadius.15": "15×15（極限）", "options.biomeBlendRadius.3": "3×3（流暢）", "options.biomeBlendRadius.5": "5×5（普通）", "options.biomeBlendRadius.7": "7×7（高）", "options.biomeBlendRadius.9": "9×9（非常高）", "options.chat": "聊天設定...", "options.chat.color": "顏色", "options.chat.delay": "聊天延遲：%s 秒", "options.chat.delay_none": "聊天延遲：無", "options.chat.height.focused": "聚焦高度", "options.chat.height.unfocused": "未聚焦高度", "options.chat.line_spacing": "行距", "options.chat.links": "網址連結", "options.chat.links.prompt": "連結提示", "options.chat.opacity": "聊天欄文字不透明度", "options.chat.scale": "聊天室文字大小", "options.chat.title": "聊天設定", "options.chat.visibility": "聊天欄", "options.chat.visibility.full": "顯示", "options.chat.visibility.hidden": "隱藏", "options.chat.visibility.system": "只顯示指令", "options.chat.width": "寬度", "options.chunks": "%s 區塊", "options.clouds.fancy": "精緻", "options.clouds.fast": "流暢", "options.controls": "按鍵設定...", "options.credits_and_attribution": "鳴謝...", "options.damageTiltStrength": "受傷抖動效果", "options.damageTiltStrength.tooltip": "受到傷害導致遊戲視角晃動的程度。", "options.darkMojangStudiosBackgroundColor": "黑白標誌", "options.darkMojangStudiosBackgroundColor.tooltip": "將 Mojang Studios 載入畫面的背景顏色變更為黑色。", "options.darknessEffectScale": "黑暗波動效果", "options.darknessEffectScale.tooltip": "控制你會受到多少伏守者與伏聆嘯口造成的黑暗效果脈動。", "options.difficulty": "難易度", "options.difficulty.easy": "簡單", "options.difficulty.easy.info": "生成的敵對生物攻擊力較低。生命值會因為飢餓值耗盡而降低至 5 顆心。", "options.difficulty.hard": "困難", "options.difficulty.hard.info": "生成的敵對生物攻擊力較高。生命值會因為飢餓值耗盡而歸零。", "options.difficulty.hardcore": "極限", "options.difficulty.normal": "普通", "options.difficulty.normal.info": "生成的敵對生物攻擊力一般。生命值會因為飢餓值耗盡而降低至半顆心。", "options.difficulty.online": "伺服器難易度", "options.difficulty.peaceful": "和平", "options.difficulty.peaceful.info": "不會生成敵對生物，只有部分中立生物會生成。飢餓值不會降低，生命值會隨著時間回復。", "options.directionalAudio": "定向音效", "options.directionalAudio.off.tooltip": "經典立體音效。", "options.directionalAudio.on.tooltip": "使用基於 HRTF 的定向音效模擬出更生動的 3D 音訊。必須使用相容 HRTF 音訊的硬體，並透過耳機獲得最佳體驗。", "options.discrete_mouse_scroll": "離散式捲動", "options.entityDistanceScaling": "實體顯示距離", "options.entityShadows": "實體陰影", "options.font": "字型設定...", "options.font.title": "字型設定", "options.forceUnicodeFont": "強制使用 Unicode 字型", "options.fov": "視角廣度", "options.fov.max": "超廣角", "options.fov.min": "正常", "options.fovEffectScale": "視角廣度效果", "options.fovEffectScale.tooltip": "控制視角廣度隨遊戲狀態效果改變的程度。", "options.framerate": "%s fps", "options.framerateLimit": "最大 FPS", "options.framerateLimit.max": "無限", "options.fullscreen": "全螢幕", "options.fullscreen.current": "自動", "options.fullscreen.entry": "%s×%s@%s（%s 位元色深）", "options.fullscreen.resolution": "全螢幕解析度", "options.fullscreen.unavailable": "設定無法使用", "options.gamma": "亮度", "options.gamma.default": "預設", "options.gamma.max": "明亮", "options.gamma.min": "昏暗", "options.generic_value": "%s：%s", "options.glintSpeed": "附魔光效閃爍速度", "options.glintSpeed.tooltip": "控制附魔物品的光效閃動速度。", "options.glintStrength": "附魔光效閃爍強度", "options.glintStrength.tooltip": "控制附魔物品的光效透明程度。", "options.graphics": "畫質", "options.graphics.fabulous": "極致！", "options.graphics.fabulous.tooltip": "%s 畫質使用畫面著色器來繪製透明方塊與 水後方的天氣、雲以及粒子。\n此選項可能會嚴重影響使用行動裝置或是 4K 解析度時的效能。", "options.graphics.fancy": "精緻", "options.graphics.fancy.tooltip": "精緻畫質在大部分裝置的效能與畫面品質間取得平衡。\n天氣、雲和粒子可能無法在透明方塊或水的後方出現。", "options.graphics.fast": "流暢", "options.graphics.fast.tooltip": "流暢畫質會顯示較少的雨及雪。\n各種方塊（如樹葉）的透明效果將會被停用。", "options.graphics.warning.accept": "以不受支援的狀態繼續", "options.graphics.warning.cancel": "帶我回上一頁", "options.graphics.warning.message": "您的繪圖裝置被檢測出不受 %s 畫質選項支援。\n\n您可以忽略這則訊息，但是若您選擇 %s 畫質將不會為您的裝置提供支援。", "options.graphics.warning.renderer": "偵測到的渲染器：[%s]", "options.graphics.warning.title": "不受支援的圖形裝置", "options.graphics.warning.vendor": "偵測到的供應商：[%s]", "options.graphics.warning.version": "偵測到的 OpenGL 版本：[%s]", "options.guiScale": "介面大小", "options.guiScale.auto": "自動", "options.hidden": "隱藏", "options.hideLightningFlashes": "隱藏閃電效果", "options.hideLightningFlashes.tooltip": "防止打雷造成天空閃爍。閃電本身依然可見。", "options.hideMatchedNames": "隱藏相符的名稱", "options.hideMatchedNames.tooltip": "第三方伺服器可能會傳送非標準格式的聊天訊息。\n開啟此選項後，將會依據發言者的名稱相符與否來隱藏玩家。", "options.hideSplashTexts": "隱藏閃爍標語", "options.hideSplashTexts.tooltip": "隱藏標題畫面中的黃色閃爍標語。", "options.inactivityFpsLimit": "限制 FPS", "options.inactivityFpsLimit.afk": "無操作時", "options.inactivityFpsLimit.afk.tooltip": "當玩家超過一分鐘沒有進行操作時，將 FPS 限制為 30。再過 9 分鐘後，進一步將其限制為 10。", "options.inactivityFpsLimit.minimized": "最小化時", "options.inactivityFpsLimit.minimized.tooltip": "只在遊戲視窗最小化時限制 FPS。", "options.invertMouse": "滑鼠反轉", "options.japaneseGlyphVariants": "日文字形變體", "options.japaneseGlyphVariants.tooltip": "在預設字型中為 CJK 字元使用日本字形。", "options.key.hold": "按住", "options.key.toggle": "切換", "options.language": "語言...", "options.language.title": "語言", "options.languageAccuracyWarning": "（語言翻譯並不一定是 100%% 精確）", "options.languageWarning": "語言翻譯並不一定是100%%精確", "options.mainHand": "慣用手", "options.mainHand.left": "左", "options.mainHand.right": "右", "options.mipmapLevels": "Mipmap 等級", "options.modelPart.cape": "披風", "options.modelPart.hat": "帽子", "options.modelPart.jacket": "外套", "options.modelPart.left_pants_leg": "左褲管", "options.modelPart.left_sleeve": "左衣袖", "options.modelPart.right_pants_leg": "右褲管", "options.modelPart.right_sleeve": "右衣袖", "options.mouseWheelSensitivity": "滾輪靈敏度", "options.mouse_settings": "滑鼠設定...", "options.mouse_settings.title": "滑鼠設定", "options.multiplayer.title": "多人遊戲設定...", "options.multiplier": "%s×", "options.music_frequency": "音樂頻率", "options.music_frequency.constant": "持續", "options.music_frequency.default": "預設", "options.music_frequency.frequent": "頻繁", "options.music_frequency.tooltip": "調整遊戲世界中音樂播放的頻率。", "options.narrator": "朗讀功能", "options.narrator.all": "朗讀全部", "options.narrator.chat": "朗讀聊天欄", "options.narrator.notavailable": "無法使用", "options.narrator.off": "關閉", "options.narrator.system": "朗讀系統訊息", "options.notifications.display_time": "通知顯示時長", "options.notifications.display_time.tooltip": "影響所有通知在畫面中停留的時間長短。", "options.off": "關閉", "options.off.composed": "%s：關閉", "options.on": "開啟", "options.on.composed": "%s：開啟", "options.online": "線上狀態...", "options.online.title": "線上狀態選項", "options.onlyShowSecureChat": "僅顯示已受保護的聊天內容", "options.onlyShowSecureChat.tooltip": "僅顯示其他玩家經驗證為本人發出、未經修改的訊息。", "options.operatorItemsTab": "管理員物品頁籤", "options.particles": "粒子密度", "options.particles.all": "全部", "options.particles.decreased": "少量", "options.particles.minimal": "最少", "options.percent_add_value": "%s：+%s%%", "options.percent_value": "%s：%s%%", "options.pixel_value": "%s：%spx", "options.prioritizeChunkUpdates": "區塊生成器", "options.prioritizeChunkUpdates.byPlayer": "半阻塞", "options.prioritizeChunkUpdates.byPlayer.tooltip": "區塊內的部分動作會將該區塊即時重新編譯。其中包括放置或破壞方塊。", "options.prioritizeChunkUpdates.nearby": "全阻塞", "options.prioritizeChunkUpdates.nearby.tooltip": "永遠即時編譯附近的區塊。此選項在放置或破壞方塊時可能會嚴重影響遊戲效能。", "options.prioritizeChunkUpdates.none": "依執行緒", "options.prioritizeChunkUpdates.none.tooltip": "附近的區塊依執行緒排程平行編譯。此選項在破壞方塊時可能會產生短暫的畫面空洞。", "options.rawMouseInput": "原始輸入", "options.realmsNotifications": "Realms 新聞及邀請", "options.realmsNotifications.tooltip": "在標題畫面中取得 Realms 新聞及邀請，並在 Realms 按鈕上顯示對應的圖示。", "options.reducedDebugInfo": "簡化除錯資訊", "options.renderClouds": "雲", "options.renderCloudsDistance": "雲層距離", "options.renderDistance": "顯示距離", "options.resourcepack": "資源包...", "options.rotateWithMinecart": "隨著礦車轉動", "options.rotateWithMinecart.tooltip": "玩家的視角是否應隨著礦車的轉向而轉動。僅在啟用「礦車改進」實驗性設定的世界中可用。", "options.screenEffectScale": "畫面扭曲效果", "options.screenEffectScale.tooltip": "噁心狀態效果和地獄傳送門畫面扭曲的強度。\n數值較低時，噁心效果將被一層綠色的視覺效果取代。", "options.sensitivity": "靈敏度", "options.sensitivity.max": "超高速！！！", "options.sensitivity.min": "*哈欠*", "options.showNowPlayingToast": "顯示音樂提示", "options.showNowPlayingToast.tooltip": "當歌曲開始播放時會顯示提示。歌曲播放時，遊戲中的暫停選單也會持續顯示相同的提示。", "options.showSubtitles": "顯示字幕", "options.simulationDistance": "模擬距離", "options.skinCustomisation": "外觀自訂...", "options.skinCustomisation.title": "外觀自訂", "options.sounds": "音樂和音效...", "options.sounds.title": "音樂和音效選項", "options.telemetry": "遙測資料...", "options.telemetry.button": "資料收集", "options.telemetry.button.tooltip": "「%s」只包含必要資料。\n「%s」包含可選資料和必要資料。", "options.telemetry.disabled": "遙測資料已停用。", "options.telemetry.state.all": "全部", "options.telemetry.state.minimal": "最少", "options.telemetry.state.none": "無", "options.title": "選項", "options.touchscreen": "觸控模式", "options.video": "顯示設定...", "options.videoTitle": "顯示設定", "options.viewBobbing": "走路晃動", "options.visible": "顯示", "options.vsync": "垂直同步", "outOfMemory.message": "Minecraft 記憶體不足。\n\n可能是由於遊戲錯誤，或配置給 Java 虛擬機器的記憶體不足。\n\n為防止世界損毀，已結束目前遊戲。我們已嘗試釋放記憶體，讓您返回主選單並繼續遊玩，但這個方法也可能無法解決問題。\n\n如果您再次看到此訊息，請重新啟動遊戲。", "outOfMemory.title": "記憶體不足！", "pack.available.title": "可用", "pack.copyFailure": "檔案複製失敗", "pack.dropConfirm": "您確定要將下列檔案新增至 Minecraft 嗎？", "pack.dropInfo": "直接拖曳至此視窗以新增檔案", "pack.dropRejected.message": "下列項目不是有效的資源或資料包，因此未能執行複製操作：\n %s", "pack.dropRejected.title": "加入的項目不是資源或資料包", "pack.folderInfo": "（請將檔案置於此處）", "pack.incompatible": "不相容", "pack.incompatible.confirm.new": "這是為新版本的 Minecraft 所設計，可能無法正確運作。", "pack.incompatible.confirm.old": "這是為舊版本的 Minecraft 所設計，可能無法正確運作。", "pack.incompatible.confirm.title": "您確定要載入此檔案？", "pack.incompatible.new": "（為 Minecraft 較新版本製作）", "pack.incompatible.old": "（為 Minecraft 較舊版本製作）", "pack.nameAndSource": "%s（%s）", "pack.openFolder": "開啟資料夾", "pack.selected.title": "已選擇", "pack.source.builtin": "內建", "pack.source.feature": "功能", "pack.source.local": "本機", "pack.source.server": "伺服器", "pack.source.world": "世界", "painting.dimensions": "%s×%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "隨機樣式", "parsing.bool.expected": "應為布林值", "parsing.bool.invalid": "無效的布林值，應為 'true' 或 'false' 但出現了 '%s'", "parsing.double.expected": "應為倍精度浮點數", "parsing.double.invalid": "無效的倍精度浮點數 '%s'", "parsing.expected": "應為 '%s'", "parsing.float.expected": "應為單精度浮點數", "parsing.float.invalid": "無效的單精度浮點數 '%s'", "parsing.int.expected": "應為整數", "parsing.int.invalid": "無效的整數 '%s'", "parsing.long.expected": "應為長整數", "parsing.long.invalid": "無效的長整數 '%s'", "parsing.quote.escape": "引號字串內的跳脫序列 '\\%s' 無效", "parsing.quote.expected.end": "字串未以引號作結", "parsing.quote.expected.start": "字串應以引號開首", "particle.invalidOptions": "無法剖析粒子選項：%s", "particle.notFound": "未知的粒子：%s", "permissions.requires.entity": "需要一個實體來執行這條指令", "permissions.requires.player": "需要一個玩家來執行這條指令", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "使用時：", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "未知的述詞：%s", "quickplay.error.invalid_identifier": "無法找到具有指定識別碼的世界", "quickplay.error.realm_connect": "無法連線到 Realm", "quickplay.error.realm_permission": "權限不足，無法連線至此 Realm", "quickplay.error.title": "無法快速進入遊戲", "realms.configuration.region.australia_east": "澳洲新南威爾斯州", "realms.configuration.region.australia_southeast": "澳洲維多利亞州", "realms.configuration.region.brazil_south": "巴西", "realms.configuration.region.central_india": "印度", "realms.configuration.region.central_us": "美國愛荷華州", "realms.configuration.region.east_asia": "香港", "realms.configuration.region.east_us": "美國維吉尼亞州", "realms.configuration.region.east_us_2": "美國北卡羅萊納州", "realms.configuration.region.france_central": "法國", "realms.configuration.region.japan_east": "日本東部", "realms.configuration.region.japan_west": "日本西部", "realms.configuration.region.korea_central": "南韓", "realms.configuration.region.north_central_us": "美國伊利諾州", "realms.configuration.region.north_europe": "愛爾蘭", "realms.configuration.region.south_central_us": "美國德克薩斯州", "realms.configuration.region.southeast_asia": "新加坡", "realms.configuration.region.sweden_central": "瑞典", "realms.configuration.region.uae_north": "阿拉伯聯合大公國（阿聯）", "realms.configuration.region.uk_south": "英國南部", "realms.configuration.region.west_central_us": "美國猶他州", "realms.configuration.region.west_europe": "荷蘭", "realms.configuration.region.west_us": "美國加利福尼亞州", "realms.configuration.region.west_us_2": "美國華盛頓州", "realms.configuration.region_preference.automatic_owner": "自動（取決於 Realms 擁有者的網路延遲）", "realms.configuration.region_preference.automatic_player": "自動（取決於首位加入遊戲的玩家）", "realms.missing.snapshot.error.text": "快照目前不支援 Realms", "recipe.notFound": "未知的配方：%s", "recipe.toast.description": "查看您的配方手冊", "recipe.toast.title": "已解鎖新的合成配方！", "record.nowPlaying": "正在播放：%s", "recover_world.bug_tracker": "回報錯誤", "recover_world.button": "嘗試恢復", "recover_world.done.failed": "無法從之前的狀態恢復。", "recover_world.done.success": "恢復成功！", "recover_world.done.title": "恢復完成", "recover_world.issue.missing_file": "缺少檔案", "recover_world.issue.none": "沒有問題", "recover_world.message": "嘗試讀取世界資料夾「%s」時發生下列問題。\n可以嘗試從較舊的狀態恢復這個世界，也可以在錯誤追蹤器上回報此問題。", "recover_world.no_fallback": "沒有可以恢復的狀態", "recover_world.restore": "嘗試恢復", "recover_world.restoring": "正在嘗試恢復世界...", "recover_world.state_entry": "%s 的狀態：", "recover_world.state_entry.unknown": "未知", "recover_world.title": "無法載入世界", "recover_world.warning": "無法載入世界簡介", "resourcePack.broken_assets": "偵測到損壞的資源檔案", "resourcePack.high_contrast.name": "高對比度", "resourcePack.load_fail": "資源重新載入失敗", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "偵測到資源包錯誤", "resourcePack.server.name": "世界指定資源", "resourcePack.title": "選擇資源包", "resourcePack.vanilla.description": "Minecraft 預設的視覺與環境效果", "resourcePack.vanilla.name": "預設", "resourcepack.downloading": "正在下載資源包", "resourcepack.progress": "正在下載檔案 (%s MB)...", "resourcepack.requesting": "正在傳送請求...", "screenshot.failure": "無法儲存螢幕擷取畫面：%s", "screenshot.success": "已儲存螢幕擷取畫面為 %s", "selectServer.add": "新增伺服器", "selectServer.defaultName": "Minecraft 伺服器", "selectServer.delete": "刪除", "selectServer.deleteButton": "刪除", "selectServer.deleteQuestion": "你確定要移除這個伺服器嗎？", "selectServer.deleteWarning": "「%s」將會永遠消失！（非常久！）", "selectServer.direct": "直接連線", "selectServer.edit": "編輯", "selectServer.hiddenAddress": "（隱藏）", "selectServer.refresh": "重新整理", "selectServer.select": "加入伺服器", "selectWorld.access_failure": "無法進入世界", "selectWorld.allowCommands": "允許作弊", "selectWorld.allowCommands.info": "例如 /gamemode 、 /experience 等指令", "selectWorld.allowCommands.new": "允許指令", "selectWorld.backupEraseCache": "清除快取資料", "selectWorld.backupJoinConfirmButton": "建立備份後載入", "selectWorld.backupJoinSkipButton": "我知道我在做什麼！", "selectWorld.backupQuestion.customized": "自訂世界已不受支援", "selectWorld.backupQuestion.downgrade": "不支援世界降級功能", "selectWorld.backupQuestion.experimental": "使用實驗性設定的世界不受支援", "selectWorld.backupQuestion.snapshot": "您確定要載入這個世界嗎？", "selectWorld.backupWarning.customized": "很遺憾我們不支援這個版本的 Minecraft 使用自訂世界。我們依然能完整載入這個世界，但是任何新生成的地形將不沿用自訂的設定。很抱歉造成您的不便！", "selectWorld.backupWarning.downgrade": "這個世界最後一次遊玩的版本為 %s，您現在的版本為 %s。降低世界版本可能導致資料毀損，我們無法保證世界能讀取運作。若您仍要繼續，請先進行備份。", "selectWorld.backupWarning.experimental": "這個世界使用了隨時可能失效的實驗性設定。我們無法保證它能正常載入或運作。請務必小心謹慎！", "selectWorld.backupWarning.snapshot": "這個世界最後遊玩的版本為 %s，您的當前版本為 %s。請先備份以避免損壞您的世界。", "selectWorld.bonusItems": "獎勵箱", "selectWorld.cheats": "允許作弊", "selectWorld.commands": "指令", "selectWorld.conversion": "必須被轉換！", "selectWorld.conversion.tooltip": "此世界必須先以較舊的版本（如 1.6.4）開啟後才能安全轉換", "selectWorld.create": "建立新的世界", "selectWorld.customizeType": "自訂", "selectWorld.dataPacks": "資料包", "selectWorld.data_read": "正在讀取世界資料...", "selectWorld.delete": "刪除", "selectWorld.deleteButton": "刪除", "selectWorld.deleteQuestion": "你確定要刪除這個世界嗎？", "selectWorld.deleteWarning": "'%s' 將會永遠消失！（非常久！）", "selectWorld.delete_failure": "無法刪除世界", "selectWorld.edit": "編輯", "selectWorld.edit.backup": "進行備份", "selectWorld.edit.backupCreated": "已備份：%s", "selectWorld.edit.backupFailed": "備份失敗", "selectWorld.edit.backupFolder": "開啟備份資料夾", "selectWorld.edit.backupSize": "檔案大小：%s MB", "selectWorld.edit.export_worldgen_settings": "匯出世界生成設定", "selectWorld.edit.export_worldgen_settings.failure": "匯出失敗", "selectWorld.edit.export_worldgen_settings.success": "已匯出", "selectWorld.edit.openFolder": "開啟世界資料夾", "selectWorld.edit.optimize": "最佳化世界", "selectWorld.edit.resetIcon": "重設圖示", "selectWorld.edit.save": "儲存", "selectWorld.edit.title": "編輯世界", "selectWorld.enterName": "世界名稱", "selectWorld.enterSeed": "世界生成種子碼", "selectWorld.experimental": "實驗性", "selectWorld.experimental.details": "詳細資訊", "selectWorld.experimental.details.entry": "需要實驗性功能：%s", "selectWorld.experimental.details.title": "實驗性功能需求", "selectWorld.experimental.message": "請注意！\n此設定需要使用仍在開發中的功能。您的世界可能會崩潰、損壞或在未來的版本中無法運作。", "selectWorld.experimental.title": "實驗性功能警告", "selectWorld.experiments": "實驗性內容", "selectWorld.experiments.info": "實驗性內容為可能在未來加入的新功能。請小心它們可能造成損壞。已開啟的實驗性內容無法在建立世界後關閉。", "selectWorld.futureworld.error.text": "試圖載入較新版本的世界時發生錯誤。這項操作原本就有其風險，很抱歉無法成功載入。", "selectWorld.futureworld.error.title": "發生錯誤！", "selectWorld.gameMode": "遊戲模式", "selectWorld.gameMode.adventure": "冒險", "selectWorld.gameMode.adventure.info": "與生存模式相同，但不能放置或移除方塊。", "selectWorld.gameMode.adventure.line1": "與生存模式相同，但無法", "selectWorld.gameMode.adventure.line2": "增加或移除方塊", "selectWorld.gameMode.creative": "創造", "selectWorld.gameMode.creative.info": "沒有限制地創造、建設與探索。你能夠飛行，使用無窮的材料，也不會被怪物傷害。", "selectWorld.gameMode.creative.line1": "無限資源、自由飛行，", "selectWorld.gameMode.creative.line2": "而且能夠瞬間破壞方塊", "selectWorld.gameMode.hardcore": "極限", "selectWorld.gameMode.hardcore.info": "難易度鎖定在「困難」的生存模式。死亡後無法重生。", "selectWorld.gameMode.hardcore.line1": "與生存模式相同，但是鎖定在", "selectWorld.gameMode.hardcore.line2": "最高難度，而且只有一次生命", "selectWorld.gameMode.spectator": "旁觀者", "selectWorld.gameMode.spectator.info": "你可以觀看但是不能夠觸碰。", "selectWorld.gameMode.spectator.line1": "你可以觀看但是不可觸摸", "selectWorld.gameMode.survival": "生存", "selectWorld.gameMode.survival.info": "探索神秘的世界，盡情建造、收集、合成，並與怪物戰鬥。", "selectWorld.gameMode.survival.line1": "搜索資源、合成、提升", "selectWorld.gameMode.survival.line2": "等級、生命值和飢餓值", "selectWorld.gameRules": "遊戲規則", "selectWorld.import_worldgen_settings": "匯入設定", "selectWorld.import_worldgen_settings.failure": "匯入設定時發生錯誤", "selectWorld.import_worldgen_settings.select_file": "選擇設定檔 (.json)", "selectWorld.incompatible.description": "這個世界無法在此版本中開啟。\n上次於版本 %s 開啟。", "selectWorld.incompatible.info": "不相容的版本：%s", "selectWorld.incompatible.title": "版本不相容", "selectWorld.incompatible.tooltip": "這個世界使用不相容的版本建立，無法開啟。", "selectWorld.incompatible_series": "以不相容的版本建立", "selectWorld.load_folder_access": "無法載入或存取遊戲世界存檔所在的資料夾！", "selectWorld.loading_list": "正在載入世界清單", "selectWorld.locked": "被另一個正在執行的 Minecraft 鎖定", "selectWorld.mapFeatures": "生成結構", "selectWorld.mapFeatures.info": "例如村莊、沉船等等", "selectWorld.mapType": "世界類型", "selectWorld.mapType.normal": "正常", "selectWorld.moreWorldOptions": "更多世界選項...", "selectWorld.newWorld": "新的世界", "selectWorld.recreate": "重新建立", "selectWorld.recreate.customized.text": "這個版本的 Minecraft 已不再支援自訂世界。我們可以試著利用相同的種子碼與屬性重新建立，但任何自訂地形將會遺失。很抱歉為您帶來不便！", "selectWorld.recreate.customized.title": "自訂世界已不受支援", "selectWorld.recreate.error.text": "試圖重新建立世界時出現了一些問題。", "selectWorld.recreate.error.title": "發生錯誤！", "selectWorld.resource_load": "正在準備資源...", "selectWorld.resultFolder": "將被儲存於：", "selectWorld.search": "搜尋世界", "selectWorld.seedInfo": "若不填則隨機生成種子碼", "selectWorld.select": "進入所選的世界", "selectWorld.targetFolder": "存檔資料夾：%s", "selectWorld.title": "選擇世界", "selectWorld.tooltip.fromNewerVersion1": "世界是以較新的版本儲存，", "selectWorld.tooltip.fromNewerVersion2": "載入這個世界可能會導致出現問題！", "selectWorld.tooltip.snapshot1": "以目前版本載入之前", "selectWorld.tooltip.snapshot2": "別忘了先備份這個世界。", "selectWorld.unable_to_load": "無法載入世界", "selectWorld.version": "版本：", "selectWorld.versionJoinButton": "依然載入", "selectWorld.versionQuestion": "你確定要載入這個世界嗎？", "selectWorld.versionUnknown": "未知", "selectWorld.versionWarning": "這個世界最後遊玩的版本為 %s，使用目前版本載入可能會導致損壞！", "selectWorld.warning.deprecated.question": "有些功能已停止支援，將在未來失效。您確定要繼續嗎？", "selectWorld.warning.deprecated.title": "警告！這些設定使用了停止支援的功能", "selectWorld.warning.experimental.question": "這些實驗性設定將來可能會失效。您確定要繼續嗎？", "selectWorld.warning.experimental.title": "警告！這些設定使用了實驗性功能", "selectWorld.warning.lowDiskSpace.description": "裝置儲存空間不足。\n在磁碟空間不足時遊玩，可能會導致世界損毀。", "selectWorld.warning.lowDiskSpace.title": "警告！磁碟空間不足！", "selectWorld.world": "世界", "sign.edit": "編輯告示牌文字", "sleep.not_possible": "入睡的玩家數目不足以跳過這個夜晚", "sleep.players_sleeping": "%s/%s 的玩家正在睡覺", "sleep.skipping_night": "正在睡夢中度過今晚", "slot.only_single_allowed": "僅接受單一欄位，當前值 '%s'", "slot.unknown": "未知的欄位 '%s'", "snbt.parser.empty_key": "鍵值不能為空", "snbt.parser.expected_binary_numeral": "應為二進位數字", "snbt.parser.expected_decimal_numeral": "應為十進位數字", "snbt.parser.expected_float_type": "應為浮點數", "snbt.parser.expected_hex_escape": "字元字面值長度應為 %s", "snbt.parser.expected_hex_numeral": "應為十六進位數字", "snbt.parser.expected_integer_type": "應為整數", "snbt.parser.expected_non_negative_number": "應為非負數", "snbt.parser.expected_number_or_boolean": "應為數字或布林值", "snbt.parser.expected_string_uuid": "應為表示有效 UUID 的字串", "snbt.parser.expected_unquoted_string": "應為有效的無引號字串", "snbt.parser.infinity_not_allowed": "不允許使用非有限數的數值", "snbt.parser.invalid_array_element_type": "無效的陣列元素類型", "snbt.parser.invalid_character_name": "無效的 Unicode 字元名稱", "snbt.parser.invalid_codepoint": "無效的 Unicode 字元編碼：%s", "snbt.parser.invalid_string_contents": "無效的字串內容", "snbt.parser.invalid_unquoted_start": "無引號字串不能以數字 0-9、+ 或 - 開頭", "snbt.parser.leading_zero_not_allowed": "十進位數字不能以 0 開頭", "snbt.parser.no_such_operation": "不存在的操作：%s", "snbt.parser.number_parse_failure": "無法剖析數字：%s", "snbt.parser.undescore_not_allowed": "數字的開頭和結尾不允許使用底線字元", "soundCategory.ambient": "環境音效／環境", "soundCategory.block": "方塊", "soundCategory.hostile": "敵對生物", "soundCategory.master": "主音量", "soundCategory.music": "音樂", "soundCategory.neutral": "友好生物", "soundCategory.player": "玩家", "soundCategory.record": "唱片機／音階盒", "soundCategory.ui": "使用者介面", "soundCategory.voice": "聲音／語音", "soundCategory.weather": "天氣", "spectatorMenu.close": "關閉選單", "spectatorMenu.next_page": "下一頁", "spectatorMenu.previous_page": "上一頁", "spectatorMenu.root.prompt": "按下任意鍵以選擇指令，再按一次該按鍵以使用它", "spectatorMenu.team_teleport": "傳送至隊伍成員", "spectatorMenu.team_teleport.prompt": "選擇一個隊伍作為傳送目標", "spectatorMenu.teleport": "傳送至玩家", "spectatorMenu.teleport.prompt": "選擇一位玩家作為傳送目標", "stat.generalButton": "一般", "stat.itemsButton": "物品", "stat.minecraft.animals_bred": "動物繁殖數", "stat.minecraft.aviate_one_cm": "鞘翅飛行距離", "stat.minecraft.bell_ring": "敲鐘次數", "stat.minecraft.boat_one_cm": "坐船航行距離", "stat.minecraft.clean_armor": "清洗的盔甲數量", "stat.minecraft.clean_banner": "清洗的旗幟數量", "stat.minecraft.clean_shulker_box": "界伏盒清洗次數", "stat.minecraft.climb_one_cm": "攀爬距離", "stat.minecraft.crouch_one_cm": "蹲行距離", "stat.minecraft.damage_absorbed": "吸收的傷害量", "stat.minecraft.damage_blocked_by_shield": "盾牌抵擋的傷害總量", "stat.minecraft.damage_dealt": "造成傷害總量", "stat.minecraft.damage_dealt_absorbed": "造成傷害（被吸收）", "stat.minecraft.damage_dealt_resisted": "造成傷害（被抵銷）", "stat.minecraft.damage_resisted": "抵銷的傷害量", "stat.minecraft.damage_taken": "承受傷害總量", "stat.minecraft.deaths": "死亡次數", "stat.minecraft.drop": "丟棄物品數量", "stat.minecraft.eat_cake_slice": "已吃掉的蛋糕片數量", "stat.minecraft.enchant_item": "物品附魔數量", "stat.minecraft.fall_one_cm": "掉落高度", "stat.minecraft.fill_cauldron": "鍋釜填滿次數", "stat.minecraft.fish_caught": "捕獲魚量", "stat.minecraft.fly_one_cm": "飛行距離", "stat.minecraft.happy_ghast_one_cm": "騎快樂幽靈移動距離", "stat.minecraft.horse_one_cm": "騎馬移動距離", "stat.minecraft.inspect_dispenser": "發射器搜索次數", "stat.minecraft.inspect_dropper": "投擲器搜索次數", "stat.minecraft.inspect_hopper": "漏斗搜索次數", "stat.minecraft.interact_with_anvil": "鐵砧使用次數", "stat.minecraft.interact_with_beacon": "烽火台使用次數", "stat.minecraft.interact_with_blast_furnace": "高爐使用次數", "stat.minecraft.interact_with_brewingstand": "釀造台使用次數", "stat.minecraft.interact_with_campfire": "營火使用次數", "stat.minecraft.interact_with_cartography_table": "製圖台使用次數", "stat.minecraft.interact_with_crafting_table": "工作台使用次數", "stat.minecraft.interact_with_furnace": "熔爐使用次數", "stat.minecraft.interact_with_grindstone": "砂輪使用次數", "stat.minecraft.interact_with_lectern": "講台使用次數", "stat.minecraft.interact_with_loom": "紡織機使用次數", "stat.minecraft.interact_with_smithing_table": "鍛造台使用次數", "stat.minecraft.interact_with_smoker": "煙燻爐使用次數", "stat.minecraft.interact_with_stonecutter": "切石機使用次數", "stat.minecraft.jump": "跳躍次數", "stat.minecraft.leave_game": "離開遊戲次數", "stat.minecraft.minecart_one_cm": "乘坐礦車距離", "stat.minecraft.mob_kills": "生物擊殺數", "stat.minecraft.open_barrel": "木桶開啟次數", "stat.minecraft.open_chest": "儲物箱開啟次數", "stat.minecraft.open_enderchest": "終界箱開啟次數", "stat.minecraft.open_shulker_box": "界伏盒開啟次數", "stat.minecraft.pig_one_cm": "騎豬移動距離", "stat.minecraft.play_noteblock": "音階盒演奏次數", "stat.minecraft.play_record": "唱片播放", "stat.minecraft.play_time": "遊戲時間", "stat.minecraft.player_kills": "玩家擊殺數", "stat.minecraft.pot_flower": "盆栽放置數量", "stat.minecraft.raid_trigger": "突襲觸發次數", "stat.minecraft.raid_win": "擊敗突襲次數", "stat.minecraft.sleep_in_bed": "睡覺次數", "stat.minecraft.sneak_time": "潛行時間", "stat.minecraft.sprint_one_cm": "跑步距離", "stat.minecraft.strider_one_cm": "騎熾足獸移動距離", "stat.minecraft.swim_one_cm": "游泳距離", "stat.minecraft.talked_to_villager": "與村民對話次數", "stat.minecraft.target_hit": "擊中標靶次數", "stat.minecraft.time_since_death": "距離上次死亡", "stat.minecraft.time_since_rest": "距離上次睡覺", "stat.minecraft.total_world_time": "世界開啟時間", "stat.minecraft.traded_with_villager": "與村民交易次數", "stat.minecraft.trigger_trapped_chest": "陷阱儲物箱觸發次數", "stat.minecraft.tune_noteblock": "音階盒調音次數", "stat.minecraft.use_cauldron": "從鍋釜取水次數", "stat.minecraft.walk_on_water_one_cm": "水面上的行走距離", "stat.minecraft.walk_one_cm": "行走距離", "stat.minecraft.walk_under_water_one_cm": "水面下的行走距離", "stat.mobsButton": "生物", "stat_type.minecraft.broken": "損壞次數", "stat_type.minecraft.crafted": "合成次數", "stat_type.minecraft.dropped": "丟棄數量", "stat_type.minecraft.killed": "你殺死過 %s 隻%s", "stat_type.minecraft.killed.none": "你沒有殺死過%s", "stat_type.minecraft.killed_by": "你被%s殺死過 %s 次", "stat_type.minecraft.killed_by.none": "你沒有被%s殺死過", "stat_type.minecraft.mined": "挖掘次數", "stat_type.minecraft.picked_up": "拾起數量", "stat_type.minecraft.used": "使用次數", "stats.none": "-", "structure_block.button.detect_size": "偵測", "structure_block.button.load": "載入", "structure_block.button.save": "儲存", "structure_block.custom_data": "自訂資料標籤名稱", "structure_block.detect_size": "偵測結構的大小與位置：", "structure_block.hover.corner": "角落：%s", "structure_block.hover.data": "資料：%s", "structure_block.hover.load": "載入：%s", "structure_block.hover.save": "儲存：%s", "structure_block.include_entities": "包含實體：", "structure_block.integrity": "結構完整度及種子碼", "structure_block.integrity.integrity": "結構完整性", "structure_block.integrity.seed": "結構種子碼", "structure_block.invalid_structure_name": "無效的結構名稱 '%s'", "structure_block.load_not_found": "沒有名為 '%s' 的結構", "structure_block.load_prepare": "結構 '%s' 的位置已準備就緒", "structure_block.load_success": "已從 '%s' 載入結構", "structure_block.mode.corner": "角落", "structure_block.mode.data": "資料", "structure_block.mode.load": "載入", "structure_block.mode.save": "儲存", "structure_block.mode_info.corner": "角落模式 ─ 位置及大小記號", "structure_block.mode_info.data": "資料模式 ─ 遊戲邏輯記號", "structure_block.mode_info.load": "載入模式 ─ 從檔案載入", "structure_block.mode_info.save": "儲存模式 ─ 寫入檔案", "structure_block.position": "相對位置", "structure_block.position.x": "X 軸相對位置", "structure_block.position.y": "Y 軸相對位置", "structure_block.position.z": "Z 軸相對位置", "structure_block.save_failure": "無法儲存結構 '%s'", "structure_block.save_success": "已將結構儲存為 '%s'", "structure_block.show_air": "顯示隱形方塊：", "structure_block.show_boundingbox": "顯示定界框：", "structure_block.size": "結構大小", "structure_block.size.x": "X 軸結構大小", "structure_block.size.y": "Y 軸結構大小", "structure_block.size.z": "Z 軸結構大小", "structure_block.size_failure": "無法偵測結構大小。請添加與結構名稱對應的角落", "structure_block.size_success": "已成功偵測 '%s' 的大小", "structure_block.strict": "嚴格放置：", "structure_block.structure_name": "結構名稱", "subtitles.ambient.cave": "毛骨悚然的聲響", "subtitles.ambient.sound": "毛骨悚然的聲響", "subtitles.block.amethyst_block.chime": "紫水晶叮噹作響", "subtitles.block.amethyst_block.resonate": "紫水晶共振", "subtitles.block.anvil.destroy": "鐵砧被破壞", "subtitles.block.anvil.land": "鐵砧落地", "subtitles.block.anvil.use": "使用鐵砧", "subtitles.block.barrel.close": "木桶關閉", "subtitles.block.barrel.open": "木桶開啟", "subtitles.block.beacon.activate": "烽火台啟動", "subtitles.block.beacon.ambient": "烽火台嗡嗡作響", "subtitles.block.beacon.deactivate": "烽火台關閉", "subtitles.block.beacon.power_select": "選取烽火台效果", "subtitles.block.beehive.drip": "蜂蜜滴落", "subtitles.block.beehive.enter": "蜜蜂進入蜂箱", "subtitles.block.beehive.exit": "蜜蜂離開蜂箱", "subtitles.block.beehive.shear": "剪刀刮削", "subtitles.block.beehive.work": "蜜蜂工作", "subtitles.block.bell.resonate": "鐘鳴", "subtitles.block.bell.use": "鐘聲", "subtitles.block.big_dripleaf.tilt_down": "懸葉草下垂", "subtitles.block.big_dripleaf.tilt_up": "懸葉草回彈", "subtitles.block.blastfurnace.fire_crackle": "高爐劈啪聲", "subtitles.block.brewing_stand.brew": "釀造台冒泡聲", "subtitles.block.bubble_column.bubble_pop": "氣泡破裂", "subtitles.block.bubble_column.upwards_ambient": "氣泡上浮", "subtitles.block.bubble_column.upwards_inside": "氣泡推升", "subtitles.block.bubble_column.whirlpool_ambient": "氣泡翻滾", "subtitles.block.bubble_column.whirlpool_inside": "氣泡捲入", "subtitles.block.button.click": "按鈕咔嗒聲", "subtitles.block.cake.add_candle": "蛋糕擠壓聲", "subtitles.block.campfire.crackle": "營火劈啪聲", "subtitles.block.candle.crackle": "蠟燭劈啪聲", "subtitles.block.candle.extinguish": "蠟燭熄滅聲", "subtitles.block.chest.close": "儲物箱關閉", "subtitles.block.chest.locked": "儲物箱鎖上", "subtitles.block.chest.open": "儲物箱開啟", "subtitles.block.chorus_flower.death": "歌萊花凋謝", "subtitles.block.chorus_flower.grow": "歌萊花生長", "subtitles.block.comparator.click": "比較器咔嗒聲", "subtitles.block.composter.empty": "清空堆肥箱", "subtitles.block.composter.fill": "填入堆肥箱", "subtitles.block.composter.ready": "堆肥箱堆肥", "subtitles.block.conduit.activate": "海靈核心啟動", "subtitles.block.conduit.ambient": "海靈核心脈動", "subtitles.block.conduit.attack.target": "海靈核心攻擊", "subtitles.block.conduit.deactivate": "海靈核心關閉", "subtitles.block.copper_bulb.turn_off": "銅燈熄滅", "subtitles.block.copper_bulb.turn_on": "銅燈亮起", "subtitles.block.copper_trapdoor.close": "地板門關閉", "subtitles.block.copper_trapdoor.open": "地板門開啟", "subtitles.block.crafter.craft": "合成器合成物品", "subtitles.block.crafter.fail": "合成器合成失敗", "subtitles.block.creaking_heart.hurt": "嘎枝之心咕噥聲", "subtitles.block.creaking_heart.idle": "毛骨悚然的聲響", "subtitles.block.creaking_heart.spawn": "嘎枝之心甦醒", "subtitles.block.deadbush.idle": "乾燥聲", "subtitles.block.decorated_pot.insert": "飾紋陶罐被填充", "subtitles.block.decorated_pot.insert_fail": "飾紋陶罐晃動", "subtitles.block.decorated_pot.shatter": "飾紋陶罐碎裂", "subtitles.block.dispenser.dispense": "物品射出", "subtitles.block.dispenser.fail": "發射器發射失敗", "subtitles.block.door.toggle": "門嘎吱作響", "subtitles.block.dried_ghast.ambient": "乾燥聲", "subtitles.block.dried_ghast.ambient_water": "乾癟幽靈補水聲", "subtitles.block.dried_ghast.place_in_water": "乾癟幽靈浸泡聲", "subtitles.block.dried_ghast.transition": "乾癟幽靈恢復", "subtitles.block.dry_grass.ambient": "風聲", "subtitles.block.enchantment_table.use": "使用附魔台", "subtitles.block.end_portal.spawn": "終界傳送門開啟", "subtitles.block.end_portal_frame.fill": "嵌上終界之眼", "subtitles.block.eyeblossom.close": "擬目花閉合", "subtitles.block.eyeblossom.idle": "擬目花沙沙聲", "subtitles.block.eyeblossom.open": "擬目花張開", "subtitles.block.fence_gate.toggle": "柵欄門嘎吱作響", "subtitles.block.fire.ambient": "火劈啪聲", "subtitles.block.fire.extinguish": "火焰熄滅聲", "subtitles.block.firefly_bush.idle": "螢火蟲嗡嗡聲", "subtitles.block.frogspawn.hatch": "蝌蚪孵化", "subtitles.block.furnace.fire_crackle": "熔爐劈啪聲", "subtitles.block.generic.break": "方塊破壞聲", "subtitles.block.generic.fall": "某物掉落在方塊上", "subtitles.block.generic.footsteps": "腳步聲", "subtitles.block.generic.hit": "正在破壞方塊", "subtitles.block.generic.place": "方塊放置聲", "subtitles.block.grindstone.use": "使用砂輪", "subtitles.block.growing_plant.crop": "修剪植物", "subtitles.block.hanging_sign.waxed_interact_fail": "告示牌晃動", "subtitles.block.honey_block.slide": "從蜂蜜塊上滑下", "subtitles.block.iron_trapdoor.close": "地板門關閉", "subtitles.block.iron_trapdoor.open": "地板門開啟", "subtitles.block.lava.ambient": "熔岩冒泡聲", "subtitles.block.lava.extinguish": "熔岩滋滋聲", "subtitles.block.lever.click": "控制桿咔嗒聲", "subtitles.block.note_block.note": "播放音階盒", "subtitles.block.pale_hanging_moss.idle": "毛骨悚然的聲響", "subtitles.block.piston.move": "活塞被推動", "subtitles.block.pointed_dripstone.drip_lava": "熔岩滴落", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "熔岩滴入鍋釜", "subtitles.block.pointed_dripstone.drip_water": "水滴落", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "水滴入鍋釜", "subtitles.block.pointed_dripstone.land": "鐘乳石砸落", "subtitles.block.portal.ambient": "傳送門颼颼聲", "subtitles.block.portal.travel": "傳送門噪音消退", "subtitles.block.portal.trigger": "傳送門噪音增強", "subtitles.block.pressure_plate.click": "壓力板咔嗒聲", "subtitles.block.pumpkin.carve": "剪刀切割", "subtitles.block.redstone_torch.burnout": "紅石火把嘶嘶聲", "subtitles.block.respawn_anchor.ambient": "重生錨颼颼聲", "subtitles.block.respawn_anchor.charge": "重生錨被充能", "subtitles.block.respawn_anchor.deplete": "重生錨消耗", "subtitles.block.respawn_anchor.set_spawn": "重生錨設定重生點", "subtitles.block.sand.idle": "沙聲", "subtitles.block.sand.wind": "風聲", "subtitles.block.sculk.charge": "伏聆冒出氣泡", "subtitles.block.sculk.spread": "伏聆蔓延", "subtitles.block.sculk_catalyst.bloom": "伏聆觸媒催發", "subtitles.block.sculk_sensor.clicking": "伏聆振測器振動", "subtitles.block.sculk_sensor.clicking_stop": "伏聆振測器停止振動", "subtitles.block.sculk_shrieker.shriek": "伏聆嘯口尖嘯", "subtitles.block.shulker_box.close": "界伏盒關閉", "subtitles.block.shulker_box.open": "界伏盒開啟", "subtitles.block.sign.waxed_interact_fail": "告示牌晃動", "subtitles.block.smithing_table.use": "使用鍛造台", "subtitles.block.smoker.smoke": "煙燻爐冒煙", "subtitles.block.sniffer_egg.crack": "嗅探獸蛋破裂", "subtitles.block.sniffer_egg.hatch": "嗅探獸蛋孵化", "subtitles.block.sniffer_egg.plop": "嗅探獸下蛋", "subtitles.block.sponge.absorb": "海綿吸水", "subtitles.block.sweet_berry_bush.pick_berries": "漿果彈出", "subtitles.block.trapdoor.close": "地板門關閉", "subtitles.block.trapdoor.open": "地板門開啟", "subtitles.block.trapdoor.toggle": "地板門嘎吱作響", "subtitles.block.trial_spawner.about_to_spawn_item": "不祥物品準備聲", "subtitles.block.trial_spawner.ambient": "試煉生怪磚劈啪聲", "subtitles.block.trial_spawner.ambient_charged": "不祥劈啪聲", "subtitles.block.trial_spawner.ambient_ominous": "不祥劈啪聲", "subtitles.block.trial_spawner.charge_activate": "試煉生怪磚被不祥壟罩", "subtitles.block.trial_spawner.close_shutter": "試煉生怪磚關閉", "subtitles.block.trial_spawner.detect_player": "試煉生怪磚充能", "subtitles.block.trial_spawner.eject_item": "試煉生怪磚彈出物品", "subtitles.block.trial_spawner.ominous_activate": "試煉生怪磚被不祥壟罩", "subtitles.block.trial_spawner.open_shutter": "試煉生怪磚開啟", "subtitles.block.trial_spawner.spawn_item": "不祥物品掉落聲", "subtitles.block.trial_spawner.spawn_item_begin": "不祥物品出現聲", "subtitles.block.trial_spawner.spawn_mob": "試煉生怪磚生成生物", "subtitles.block.tripwire.attach": "絆線連接", "subtitles.block.tripwire.click": "絆線咔嗒聲", "subtitles.block.tripwire.detach": "絆線分離", "subtitles.block.vault.activate": "寶庫燃起", "subtitles.block.vault.ambient": "寶庫劈啪聲", "subtitles.block.vault.close_shutter": "寶庫關閉", "subtitles.block.vault.deactivate": "寶庫熄滅聲", "subtitles.block.vault.eject_item": "寶庫彈出物品", "subtitles.block.vault.insert_item": "寶庫解鎖", "subtitles.block.vault.insert_item_fail": "寶庫拒絕物品", "subtitles.block.vault.open_shutter": "寶庫開啟", "subtitles.block.vault.reject_rewarded_player": "寶庫拒絕玩家", "subtitles.block.water.ambient": "流水聲", "subtitles.block.wet_sponge.dries": "海綿烘乾", "subtitles.chiseled_bookshelf.insert": "放置書", "subtitles.chiseled_bookshelf.insert_enchanted": "放置附魔書", "subtitles.chiseled_bookshelf.take": "取走書", "subtitles.chiseled_bookshelf.take_enchanted": "取走附魔書", "subtitles.enchant.thorns.hit": "尖刺刺傷聲", "subtitles.entity.allay.ambient_with_item": "悅靈搜尋", "subtitles.entity.allay.ambient_without_item": "悅靈渴望", "subtitles.entity.allay.death": "悅靈死亡", "subtitles.entity.allay.hurt": "悅靈受傷", "subtitles.entity.allay.item_given": "悅靈咯咯笑", "subtitles.entity.allay.item_taken": "悅靈喜悅", "subtitles.entity.allay.item_thrown": "悅靈投擲", "subtitles.entity.armadillo.ambient": "犰狳呼嚕聲", "subtitles.entity.armadillo.brush": "鱗甲刷落聲", "subtitles.entity.armadillo.death": "犰狳死亡", "subtitles.entity.armadillo.eat": "犰狳進食", "subtitles.entity.armadillo.hurt": "犰狳受傷", "subtitles.entity.armadillo.hurt_reduced": "犰狳保護自己", "subtitles.entity.armadillo.land": "犰狳落地", "subtitles.entity.armadillo.peek": "犰狳窺視", "subtitles.entity.armadillo.roll": "犰狳蜷縮", "subtitles.entity.armadillo.scute_drop": "犰狳掉落鱗甲", "subtitles.entity.armadillo.unroll_finish": "犰狳伸展", "subtitles.entity.armadillo.unroll_start": "犰狳窺視", "subtitles.entity.armor_stand.fall": "物體掉落", "subtitles.entity.arrow.hit": "箭矢擊中聲", "subtitles.entity.arrow.hit_player": "擊中玩家", "subtitles.entity.arrow.shoot": "射出箭矢", "subtitles.entity.axolotl.attack": "六角恐龍攻擊", "subtitles.entity.axolotl.death": "六角恐龍死亡", "subtitles.entity.axolotl.hurt": "六角恐龍受傷", "subtitles.entity.axolotl.idle_air": "六角恐龍鳴叫", "subtitles.entity.axolotl.idle_water": "六角恐龍鳴叫", "subtitles.entity.axolotl.splash": "六角恐龍濺起水花", "subtitles.entity.axolotl.swim": "六角恐龍游泳", "subtitles.entity.bat.ambient": "蝙蝠聲", "subtitles.entity.bat.death": "蝙蝠死亡", "subtitles.entity.bat.hurt": "蝙蝠受傷", "subtitles.entity.bat.takeoff": "蝙蝠飛翔聲", "subtitles.entity.bee.ambient": "蜜蜂嗡嗡聲", "subtitles.entity.bee.death": "蜜蜂死亡", "subtitles.entity.bee.hurt": "蜜蜂受傷", "subtitles.entity.bee.loop": "蜜蜂嗡嗡聲", "subtitles.entity.bee.loop_aggressive": "蜜蜂憤怒地嗡嗡作響", "subtitles.entity.bee.pollinate": "蜜蜂快樂地嗡嗡作響", "subtitles.entity.bee.sting": "蜜蜂螫咬", "subtitles.entity.blaze.ambient": "烈焰使者吐息", "subtitles.entity.blaze.burn": "烈焰使者劈啪聲", "subtitles.entity.blaze.death": "烈焰使者死亡", "subtitles.entity.blaze.hurt": "烈焰使者受傷", "subtitles.entity.blaze.shoot": "烈焰使者發射", "subtitles.entity.boat.paddle_land": "划船", "subtitles.entity.boat.paddle_water": "划船", "subtitles.entity.bogged.ambient": "沼骸喀啦聲", "subtitles.entity.bogged.death": "沼骸死亡", "subtitles.entity.bogged.hurt": "沼骸受傷", "subtitles.entity.breeze.charge": "旋風使者蓄力", "subtitles.entity.breeze.death": "旋風使者死亡", "subtitles.entity.breeze.deflect": "旋風使者反彈", "subtitles.entity.breeze.hurt": "旋風使者受傷", "subtitles.entity.breeze.idle_air": "旋風使者飛行", "subtitles.entity.breeze.idle_ground": "旋風使者呼嘯", "subtitles.entity.breeze.inhale": "旋風使者吸氣", "subtitles.entity.breeze.jump": "旋風使者跳躍", "subtitles.entity.breeze.land": "旋風使者落地", "subtitles.entity.breeze.shoot": "旋風使者發射", "subtitles.entity.breeze.slide": "旋風使者滑行", "subtitles.entity.breeze.whirl": "旋風使者旋轉", "subtitles.entity.breeze.wind_burst": "風彈爆裂", "subtitles.entity.camel.ambient": "駱駝呼嚕聲", "subtitles.entity.camel.dash": "駱駝衝刺", "subtitles.entity.camel.dash_ready": "駱駝體力恢復", "subtitles.entity.camel.death": "駱駝死亡", "subtitles.entity.camel.eat": "駱駝進食", "subtitles.entity.camel.hurt": "駱駝受傷", "subtitles.entity.camel.saddle": "裝上鞍", "subtitles.entity.camel.sit": "駱駝坐下", "subtitles.entity.camel.stand": "駱駝起身", "subtitles.entity.camel.step": "駱駝踏步", "subtitles.entity.camel.step_sand": "駱駝踏沙", "subtitles.entity.cat.ambient": "貓叫聲", "subtitles.entity.cat.beg_for_food": "貓討食", "subtitles.entity.cat.death": "貓死亡", "subtitles.entity.cat.eat": "貓進食", "subtitles.entity.cat.hiss": "貓嘶嘶聲", "subtitles.entity.cat.hurt": "貓受傷", "subtitles.entity.cat.purr": "貓呼嚕叫", "subtitles.entity.chicken.ambient": "雞叫聲", "subtitles.entity.chicken.death": "雞死亡", "subtitles.entity.chicken.egg": "雞下蛋", "subtitles.entity.chicken.hurt": "雞受傷", "subtitles.entity.cod.death": "鱈魚死亡", "subtitles.entity.cod.flop": "鱈魚拍打", "subtitles.entity.cod.hurt": "鱈魚受傷", "subtitles.entity.cow.ambient": "牛哞聲", "subtitles.entity.cow.death": "牛死亡", "subtitles.entity.cow.hurt": "牛受傷", "subtitles.entity.cow.milk": "牛被擠出鮮奶", "subtitles.entity.creaking.activate": "嘎枝凝視", "subtitles.entity.creaking.ambient": "嘎枝嘎吱作響", "subtitles.entity.creaking.attack": "嘎枝攻擊", "subtitles.entity.creaking.deactivate": "嘎枝平復", "subtitles.entity.creaking.death": "嘎枝崩解", "subtitles.entity.creaking.freeze": "嘎枝停止", "subtitles.entity.creaking.spawn": "嘎枝顯現", "subtitles.entity.creaking.sway": "嘎枝被擊中", "subtitles.entity.creaking.twitch": "嘎枝抽搐", "subtitles.entity.creaking.unfreeze": "嘎枝移動", "subtitles.entity.creeper.death": "苦力怕死亡", "subtitles.entity.creeper.hurt": "苦力怕受傷", "subtitles.entity.creeper.primed": "苦力怕嘶嘶聲", "subtitles.entity.dolphin.ambient": "海豚啾啾聲", "subtitles.entity.dolphin.ambient_water": "海豚吹哨", "subtitles.entity.dolphin.attack": "海豚攻擊", "subtitles.entity.dolphin.death": "海豚死亡", "subtitles.entity.dolphin.eat": "海豚進食", "subtitles.entity.dolphin.hurt": "海豚受傷", "subtitles.entity.dolphin.jump": "海豚跳躍", "subtitles.entity.dolphin.play": "海豚嬉鬧", "subtitles.entity.dolphin.splash": "海豚濺水", "subtitles.entity.dolphin.swim": "海豚游泳", "subtitles.entity.donkey.ambient": "驢子叫聲", "subtitles.entity.donkey.angry": "驢子嘶鳴", "subtitles.entity.donkey.chest": "為驢子裝上儲物箱", "subtitles.entity.donkey.death": "驢子死亡", "subtitles.entity.donkey.eat": "驢子進食", "subtitles.entity.donkey.hurt": "驢子受傷", "subtitles.entity.donkey.jump": "驢子跳躍", "subtitles.entity.drowned.ambient": "沉屍咕嚕聲", "subtitles.entity.drowned.ambient_water": "沉屍咕嚕聲", "subtitles.entity.drowned.death": "沉屍死亡", "subtitles.entity.drowned.hurt": "沉屍受傷", "subtitles.entity.drowned.shoot": "沉屍投擲三叉戟", "subtitles.entity.drowned.step": "沉屍腳步聲", "subtitles.entity.drowned.swim": "沉屍游泳", "subtitles.entity.egg.throw": "雞蛋擲出", "subtitles.entity.elder_guardian.ambient": "遠古深海守衛叫聲", "subtitles.entity.elder_guardian.ambient_land": "遠古深海守衛拍打聲", "subtitles.entity.elder_guardian.curse": "遠古深海守衛施咒", "subtitles.entity.elder_guardian.death": "遠古深海守衛死亡", "subtitles.entity.elder_guardian.flop": "遠古深海守衛拍擊聲", "subtitles.entity.elder_guardian.hurt": "遠古深海守衛受傷", "subtitles.entity.ender_dragon.ambient": "終界龍咆哮聲", "subtitles.entity.ender_dragon.death": "終界龍死亡", "subtitles.entity.ender_dragon.flap": "終界龍擊翅聲", "subtitles.entity.ender_dragon.growl": "終界龍吼叫", "subtitles.entity.ender_dragon.hurt": "終界龍受傷", "subtitles.entity.ender_dragon.shoot": "終界龍發射火球", "subtitles.entity.ender_eye.death": "終界之眼掉落", "subtitles.entity.ender_eye.launch": "終界之眼擲出", "subtitles.entity.ender_pearl.throw": "終界珍珠擲出", "subtitles.entity.enderman.ambient": "終界使者低鳴聲", "subtitles.entity.enderman.death": "終界使者死亡", "subtitles.entity.enderman.hurt": "終界使者受傷", "subtitles.entity.enderman.scream": "終界使者尖叫", "subtitles.entity.enderman.stare": "終界使者大聲嘶吼", "subtitles.entity.enderman.teleport": "終界使者瞬移聲", "subtitles.entity.endermite.ambient": "終界蟎竄動", "subtitles.entity.endermite.death": "終界蟎死亡", "subtitles.entity.endermite.hurt": "終界蟎受傷", "subtitles.entity.evoker.ambient": "喚魔者嘀咕", "subtitles.entity.evoker.cast_spell": "喚魔者施咒", "subtitles.entity.evoker.celebrate": "喚魔者歡呼", "subtitles.entity.evoker.death": "喚魔者死亡", "subtitles.entity.evoker.hurt": "喚魔者受傷", "subtitles.entity.evoker.prepare_attack": "喚魔者準備攻擊", "subtitles.entity.evoker.prepare_summon": "喚魔者準備召喚", "subtitles.entity.evoker.prepare_wololo": "喚魔者準備施咒", "subtitles.entity.evoker_fangs.attack": "尖牙咬擊", "subtitles.entity.experience_orb.pickup": "獲得經驗值", "subtitles.entity.firework_rocket.blast": "煙火爆炸", "subtitles.entity.firework_rocket.launch": "煙火發射", "subtitles.entity.firework_rocket.twinkle": "煙火閃爍", "subtitles.entity.fish.swim": "水濺聲", "subtitles.entity.fishing_bobber.retrieve": "浮標收回", "subtitles.entity.fishing_bobber.splash": "浮標濺水聲", "subtitles.entity.fishing_bobber.throw": "浮標投出", "subtitles.entity.fox.aggro": "狐狸發怒", "subtitles.entity.fox.ambient": "狐狸吱叫", "subtitles.entity.fox.bite": "狐狸啃咬", "subtitles.entity.fox.death": "狐狸死亡", "subtitles.entity.fox.eat": "狐狸進食", "subtitles.entity.fox.hurt": "狐狸受傷", "subtitles.entity.fox.screech": "狐狸尖叫聲", "subtitles.entity.fox.sleep": "狐狸打鼾", "subtitles.entity.fox.sniff": "狐狸嗅氣", "subtitles.entity.fox.spit": "狐狸吐出", "subtitles.entity.fox.teleport": "狐狸傳送", "subtitles.entity.frog.ambient": "青蛙呱呱叫", "subtitles.entity.frog.death": "青蛙死亡", "subtitles.entity.frog.eat": "青蛙進食", "subtitles.entity.frog.hurt": "青蛙受傷", "subtitles.entity.frog.lay_spawn": "青蛙產卵", "subtitles.entity.frog.long_jump": "青蛙跳躍", "subtitles.entity.generic.big_fall": "物體掉落", "subtitles.entity.generic.burn": "燃燒", "subtitles.entity.generic.death": "死亡", "subtitles.entity.generic.drink": "飲用聲", "subtitles.entity.generic.eat": "進食", "subtitles.entity.generic.explode": "爆炸聲", "subtitles.entity.generic.extinguish_fire": "火焰熄滅聲", "subtitles.entity.generic.hurt": "物體受傷", "subtitles.entity.generic.small_fall": "物體跌落", "subtitles.entity.generic.splash": "濺水聲", "subtitles.entity.generic.swim": "游泳", "subtitles.entity.generic.wind_burst": "風彈爆裂", "subtitles.entity.ghast.ambient": "地獄幽靈哭泣聲", "subtitles.entity.ghast.death": "地獄幽靈死亡", "subtitles.entity.ghast.hurt": "地獄幽靈受傷", "subtitles.entity.ghast.shoot": "地獄幽靈射擊", "subtitles.entity.ghastling.ambient": "小幽靈咕咕聲", "subtitles.entity.ghastling.death": "小幽靈死亡", "subtitles.entity.ghastling.hurt": "小幽靈受傷", "subtitles.entity.ghastling.spawn": "小幽靈出現", "subtitles.entity.glow_item_frame.add_item": "填充螢光物品展示框", "subtitles.entity.glow_item_frame.break": "取下螢光物品展示框", "subtitles.entity.glow_item_frame.place": "放置螢光物品展示框", "subtitles.entity.glow_item_frame.remove_item": "清空螢光物品展示框", "subtitles.entity.glow_item_frame.rotate_item": "螢光物品展示框咔嗒聲", "subtitles.entity.glow_squid.ambient": "螢光魷魚游泳", "subtitles.entity.glow_squid.death": "螢光魷魚死亡", "subtitles.entity.glow_squid.hurt": "螢光魷魚受傷", "subtitles.entity.glow_squid.squirt": "螢光魷魚噴墨", "subtitles.entity.goat.ambient": "山羊咩叫聲", "subtitles.entity.goat.death": "山羊死亡", "subtitles.entity.goat.eat": "山羊進食", "subtitles.entity.goat.horn_break": "山羊角斷裂", "subtitles.entity.goat.hurt": "山羊受傷", "subtitles.entity.goat.long_jump": "山羊跳躍聲", "subtitles.entity.goat.milk": "山羊被擠出鮮奶", "subtitles.entity.goat.prepare_ram": "山羊重踏聲", "subtitles.entity.goat.ram_impact": "山羊衝撞聲", "subtitles.entity.goat.screaming.ambient": "山羊吼叫聲", "subtitles.entity.goat.step": "山羊腳步聲", "subtitles.entity.guardian.ambient": "深海守衛叫聲", "subtitles.entity.guardian.ambient_land": "深海守衛拍打聲", "subtitles.entity.guardian.attack": "深海守衛發射", "subtitles.entity.guardian.death": "深海守衛死亡", "subtitles.entity.guardian.flop": "深海守衛拍擊聲", "subtitles.entity.guardian.hurt": "深海守衛受傷", "subtitles.entity.happy_ghast.ambient": "快樂幽靈哼唱聲", "subtitles.entity.happy_ghast.death": "快樂幽靈死亡", "subtitles.entity.happy_ghast.equip": "帽鞍裝備聲", "subtitles.entity.happy_ghast.harness_goggles_down": "快樂幽靈就緒", "subtitles.entity.happy_ghast.harness_goggles_up": "快樂幽靈停下", "subtitles.entity.happy_ghast.hurt": "快樂幽靈受傷", "subtitles.entity.happy_ghast.unequip": "帽鞍卸除聲", "subtitles.entity.hoglin.ambient": "豬布獸吼叫", "subtitles.entity.hoglin.angry": "豬布獸憤怒地吼叫", "subtitles.entity.hoglin.attack": "豬布獸攻擊", "subtitles.entity.hoglin.converted_to_zombified": "豬布獸轉化為豬屍獸", "subtitles.entity.hoglin.death": "豬布獸死亡", "subtitles.entity.hoglin.hurt": "豬布獸受傷", "subtitles.entity.hoglin.retreat": "豬布獸撤退", "subtitles.entity.hoglin.step": "豬布獸腳步聲", "subtitles.entity.horse.ambient": "馬嘶鳴", "subtitles.entity.horse.angry": "馬嘶鳴", "subtitles.entity.horse.armor": "裝上馬鎧", "subtitles.entity.horse.breathe": "馬呼吸聲", "subtitles.entity.horse.death": "馬死亡", "subtitles.entity.horse.eat": "馬進食", "subtitles.entity.horse.gallop": "馬跑步聲", "subtitles.entity.horse.hurt": "馬受傷", "subtitles.entity.horse.jump": "馬跳躍", "subtitles.entity.horse.saddle": "裝上鞍", "subtitles.entity.husk.ambient": "屍殼呻吟聲", "subtitles.entity.husk.converted_to_zombie": "屍殼轉變為殭屍", "subtitles.entity.husk.death": "屍殼死亡", "subtitles.entity.husk.hurt": "屍殼受傷", "subtitles.entity.illusioner.ambient": "幻術師嘀咕", "subtitles.entity.illusioner.cast_spell": "幻術師施咒", "subtitles.entity.illusioner.death": "幻術師死亡", "subtitles.entity.illusioner.hurt": "幻術師受傷", "subtitles.entity.illusioner.mirror_move": "幻術師分影", "subtitles.entity.illusioner.prepare_blindness": "幻術師準備盲眼咒", "subtitles.entity.illusioner.prepare_mirror": "幻術師準備分影咒", "subtitles.entity.iron_golem.attack": "鐵魔像攻擊", "subtitles.entity.iron_golem.damage": "鐵魔像破裂", "subtitles.entity.iron_golem.death": "鐵魔像死亡", "subtitles.entity.iron_golem.hurt": "鐵魔像受傷", "subtitles.entity.iron_golem.repair": "鐵魔像修復", "subtitles.entity.item.break": "物品損壞", "subtitles.entity.item.pickup": "撿起物品", "subtitles.entity.item_frame.add_item": "填充物品展示框", "subtitles.entity.item_frame.break": "取下物品展示框", "subtitles.entity.item_frame.place": "放置物品展示框", "subtitles.entity.item_frame.remove_item": "清空物品展示框", "subtitles.entity.item_frame.rotate_item": "物品展示框咔嗒聲", "subtitles.entity.leash_knot.break": "取下拴繩", "subtitles.entity.leash_knot.place": "繫上拴繩", "subtitles.entity.lightning_bolt.impact": "雷擊聲", "subtitles.entity.lightning_bolt.thunder": "雷聲隆隆", "subtitles.entity.llama.ambient": "駱馬叫聲", "subtitles.entity.llama.angry": "駱馬怒吼", "subtitles.entity.llama.chest": "為駱馬裝上儲物箱", "subtitles.entity.llama.death": "駱馬死亡", "subtitles.entity.llama.eat": "駱馬進食", "subtitles.entity.llama.hurt": "駱馬受傷", "subtitles.entity.llama.spit": "駱馬呸", "subtitles.entity.llama.step": "駱馬踏步", "subtitles.entity.llama.swag": "駱馬被裝飾", "subtitles.entity.magma_cube.death": "岩漿立方怪死亡", "subtitles.entity.magma_cube.hurt": "岩漿立方怪受傷", "subtitles.entity.magma_cube.squish": "岩漿立方怪啪搭聲", "subtitles.entity.minecart.inside": "礦車哐啷聲", "subtitles.entity.minecart.inside_underwater": "礦車水下哐啷聲", "subtitles.entity.minecart.riding": "礦車移動", "subtitles.entity.mooshroom.convert": "哞菇轉變", "subtitles.entity.mooshroom.eat": "哞菇進食", "subtitles.entity.mooshroom.milk": "哞菇被擠出鮮奶", "subtitles.entity.mooshroom.suspicious_milk": "哞菇被擠出可疑的奶", "subtitles.entity.mule.ambient": "騾子叫聲", "subtitles.entity.mule.angry": "騾子嘶鳴", "subtitles.entity.mule.chest": "為騾子裝上儲物箱", "subtitles.entity.mule.death": "騾子死亡", "subtitles.entity.mule.eat": "騾子進食", "subtitles.entity.mule.hurt": "騾子受傷", "subtitles.entity.mule.jump": "騾子跳躍", "subtitles.entity.painting.break": "取下繪畫", "subtitles.entity.painting.place": "放置繪畫", "subtitles.entity.panda.aggressive_ambient": "貓熊發怒", "subtitles.entity.panda.ambient": "貓熊喘氣", "subtitles.entity.panda.bite": "貓熊啃咬", "subtitles.entity.panda.cant_breed": "貓熊哀鳴", "subtitles.entity.panda.death": "貓熊死亡", "subtitles.entity.panda.eat": "貓熊進食", "subtitles.entity.panda.hurt": "貓熊受傷", "subtitles.entity.panda.pre_sneeze": "貓熊鼻子發癢", "subtitles.entity.panda.sneeze": "貓熊打噴嚏", "subtitles.entity.panda.step": "貓熊腳步聲", "subtitles.entity.panda.worried_ambient": "貓熊嗚咽", "subtitles.entity.parrot.ambient": "鸚鵡說話", "subtitles.entity.parrot.death": "鸚鵡死亡", "subtitles.entity.parrot.eats": "鸚鵡進食", "subtitles.entity.parrot.fly": "鸚鵡振翅", "subtitles.entity.parrot.hurts": "鸚鵡受傷", "subtitles.entity.parrot.imitate.blaze": "鸚鵡吐息", "subtitles.entity.parrot.imitate.bogged": "鸚鵡喀啦聲", "subtitles.entity.parrot.imitate.breeze": "鸚鵡呼嘯", "subtitles.entity.parrot.imitate.creaking": "鸚鵡嘎吱作響", "subtitles.entity.parrot.imitate.creeper": "鸚鵡嘶嘶聲", "subtitles.entity.parrot.imitate.drowned": "鸚鵡咕嚕聲", "subtitles.entity.parrot.imitate.elder_guardian": "鸚鵡呻吟聲", "subtitles.entity.parrot.imitate.ender_dragon": "鸚鵡咆哮聲", "subtitles.entity.parrot.imitate.endermite": "鸚鵡竄動", "subtitles.entity.parrot.imitate.evoker": "鸚鵡嘀咕", "subtitles.entity.parrot.imitate.ghast": "鸚鵡哭泣聲", "subtitles.entity.parrot.imitate.guardian": "鸚鵡呻吟聲", "subtitles.entity.parrot.imitate.hoglin": "鸚鵡吼叫", "subtitles.entity.parrot.imitate.husk": "鸚鵡呻吟聲", "subtitles.entity.parrot.imitate.illusioner": "鸚鵡嘀咕", "subtitles.entity.parrot.imitate.magma_cube": "鸚鵡啪搭聲", "subtitles.entity.parrot.imitate.phantom": "鸚鵡尖叫聲", "subtitles.entity.parrot.imitate.piglin": "鸚鵡哼氣", "subtitles.entity.parrot.imitate.piglin_brute": "鸚鵡哼氣", "subtitles.entity.parrot.imitate.pillager": "鸚鵡嘀咕", "subtitles.entity.parrot.imitate.ravager": "鸚鵡呼嚕聲", "subtitles.entity.parrot.imitate.shulker": "鸚鵡潛伏", "subtitles.entity.parrot.imitate.silverfish": "鸚鵡嘶嘶聲", "subtitles.entity.parrot.imitate.skeleton": "鸚鵡喀啦聲", "subtitles.entity.parrot.imitate.slime": "鸚鵡啪搭聲", "subtitles.entity.parrot.imitate.spider": "鸚鵡嘶嘶聲", "subtitles.entity.parrot.imitate.stray": "鸚鵡喀啦聲", "subtitles.entity.parrot.imitate.vex": "鸚鵡惱怒", "subtitles.entity.parrot.imitate.vindicator": "鸚鵡嘀咕聲", "subtitles.entity.parrot.imitate.warden": "鸚鵡呻吟", "subtitles.entity.parrot.imitate.witch": "鸚鵡咯咯笑", "subtitles.entity.parrot.imitate.wither": "鸚鵡發怒", "subtitles.entity.parrot.imitate.wither_skeleton": "鸚鵡喀啦聲", "subtitles.entity.parrot.imitate.zoglin": "鸚鵡吼叫", "subtitles.entity.parrot.imitate.zombie": "鸚鵡呻吟聲", "subtitles.entity.parrot.imitate.zombie_villager": "鸚鵡呻吟聲", "subtitles.entity.phantom.ambient": "夜魅尖叫聲", "subtitles.entity.phantom.bite": "夜魅啃咬", "subtitles.entity.phantom.death": "夜魅死亡", "subtitles.entity.phantom.flap": "夜魅振翅", "subtitles.entity.phantom.hurt": "夜魅受傷", "subtitles.entity.phantom.swoop": "夜魅俯衝", "subtitles.entity.pig.ambient": "豬叫聲", "subtitles.entity.pig.death": "豬死亡", "subtitles.entity.pig.hurt": "豬受傷", "subtitles.entity.pig.saddle": "裝上鞍", "subtitles.entity.piglin.admiring_item": "豬布林鑑賞物品", "subtitles.entity.piglin.ambient": "豬布林哼氣", "subtitles.entity.piglin.angry": "豬布林憤怒地哼氣", "subtitles.entity.piglin.celebrate": "豬布林慶祝", "subtitles.entity.piglin.converted_to_zombified": "豬布林轉化為殭屍化豬布林", "subtitles.entity.piglin.death": "豬布林死亡", "subtitles.entity.piglin.hurt": "豬布林受傷", "subtitles.entity.piglin.jealous": "豬布林嫉妒地哼氣", "subtitles.entity.piglin.retreat": "豬布林撤退", "subtitles.entity.piglin.step": "豬布林腳步聲", "subtitles.entity.piglin_brute.ambient": "豬布林蠻兵哼氣", "subtitles.entity.piglin_brute.angry": "豬布林蠻兵憤怒地哼氣", "subtitles.entity.piglin_brute.converted_to_zombified": "豬布林蠻兵轉化為殭屍化豬布林", "subtitles.entity.piglin_brute.death": "豬布林蠻兵死亡", "subtitles.entity.piglin_brute.hurt": "豬布林蠻兵受傷", "subtitles.entity.piglin_brute.step": "豬布林蠻兵腳步聲", "subtitles.entity.pillager.ambient": "掠奪者嘀咕", "subtitles.entity.pillager.celebrate": "掠奪者歡呼", "subtitles.entity.pillager.death": "掠奪者死亡", "subtitles.entity.pillager.hurt": "掠奪者受傷", "subtitles.entity.player.attack.crit": "暴擊", "subtitles.entity.player.attack.knockback": "擊退攻擊", "subtitles.entity.player.attack.strong": "重攻擊", "subtitles.entity.player.attack.sweep": "橫掃攻擊", "subtitles.entity.player.attack.weak": "弱攻擊", "subtitles.entity.player.burp": "打嗝", "subtitles.entity.player.death": "玩家死亡", "subtitles.entity.player.freeze_hurt": "玩家凍傷", "subtitles.entity.player.hurt": "玩家受傷", "subtitles.entity.player.hurt_drown": "玩家溺水", "subtitles.entity.player.hurt_on_fire": "玩家燃燒", "subtitles.entity.player.levelup": "玩家升級聲", "subtitles.entity.player.teleport": "玩家傳送", "subtitles.entity.polar_bear.ambient": "北極熊低吼", "subtitles.entity.polar_bear.ambient_baby": "幼年北極熊低哼", "subtitles.entity.polar_bear.death": "北極熊死亡", "subtitles.entity.polar_bear.hurt": "北極熊受傷", "subtitles.entity.polar_bear.warning": "北極熊吼叫", "subtitles.entity.potion.splash": "瓶子碎裂聲", "subtitles.entity.potion.throw": "擲瓶聲", "subtitles.entity.puffer_fish.blow_out": "河豚消氣", "subtitles.entity.puffer_fish.blow_up": "河豚充氣", "subtitles.entity.puffer_fish.death": "河豚死亡", "subtitles.entity.puffer_fish.flop": "河豚拍打", "subtitles.entity.puffer_fish.hurt": "河豚受傷", "subtitles.entity.puffer_fish.sting": "河豚螫人", "subtitles.entity.rabbit.ambient": "兔子叫聲", "subtitles.entity.rabbit.attack": "兔子攻擊", "subtitles.entity.rabbit.death": "兔子死亡", "subtitles.entity.rabbit.hurt": "兔子受傷", "subtitles.entity.rabbit.jump": "兔子跳躍", "subtitles.entity.ravager.ambient": "劫毀獸咕嚕聲", "subtitles.entity.ravager.attack": "劫毀獸啃咬", "subtitles.entity.ravager.celebrate": "劫毀獸歡呼", "subtitles.entity.ravager.death": "劫毀獸死亡", "subtitles.entity.ravager.hurt": "劫毀獸受傷", "subtitles.entity.ravager.roar": "劫毀獸咆哮聲", "subtitles.entity.ravager.step": "劫毀獸腳步聲", "subtitles.entity.ravager.stunned": "劫毀獸暈眩", "subtitles.entity.salmon.death": "鮭魚死亡", "subtitles.entity.salmon.flop": "鮭魚拍打", "subtitles.entity.salmon.hurt": "鮭魚受傷", "subtitles.entity.sheep.ambient": "綿羊叫聲", "subtitles.entity.sheep.death": "綿羊死亡", "subtitles.entity.sheep.hurt": "綿羊受傷", "subtitles.entity.shulker.ambient": "界伏蚌潛伏", "subtitles.entity.shulker.close": "界伏蚌關閉", "subtitles.entity.shulker.death": "界伏蚌死亡", "subtitles.entity.shulker.hurt": "界伏蚌受傷", "subtitles.entity.shulker.open": "界伏蚌開啟", "subtitles.entity.shulker.shoot": "界伏蚌發射", "subtitles.entity.shulker.teleport": "界伏蚌瞬移", "subtitles.entity.shulker_bullet.hit": "界伏彈爆炸", "subtitles.entity.shulker_bullet.hurt": "界伏彈破裂", "subtitles.entity.silverfish.ambient": "蠹魚嘶嘶聲", "subtitles.entity.silverfish.death": "蠹魚死亡", "subtitles.entity.silverfish.hurt": "蠹魚受傷", "subtitles.entity.skeleton.ambient": "骷髏喀啦聲", "subtitles.entity.skeleton.converted_to_stray": "骷髏轉化為流髑", "subtitles.entity.skeleton.death": "骷髏死亡", "subtitles.entity.skeleton.hurt": "骷髏受傷", "subtitles.entity.skeleton.shoot": "骷髏射箭", "subtitles.entity.skeleton_horse.ambient": "骷髏馬叫聲", "subtitles.entity.skeleton_horse.death": "骷髏馬死亡", "subtitles.entity.skeleton_horse.hurt": "骷髏馬受傷", "subtitles.entity.skeleton_horse.jump_water": "骷髏馬跳躍", "subtitles.entity.skeleton_horse.swim": "骷髏馬游泳", "subtitles.entity.slime.attack": "史萊姆攻擊", "subtitles.entity.slime.death": "史萊姆死亡", "subtitles.entity.slime.hurt": "史萊姆受傷", "subtitles.entity.slime.squish": "史萊姆啪搭聲", "subtitles.entity.sniffer.death": "嗅探獸死亡", "subtitles.entity.sniffer.digging": "嗅探獸挖掘", "subtitles.entity.sniffer.digging_stop": "嗅探獸起身", "subtitles.entity.sniffer.drop_seed": "嗅探獸挖出種子", "subtitles.entity.sniffer.eat": "嗅探獸進食", "subtitles.entity.sniffer.egg_crack": "嗅探獸蛋破裂", "subtitles.entity.sniffer.egg_hatch": "嗅探獸蛋孵化", "subtitles.entity.sniffer.happy": "嗅探獸愉悅", "subtitles.entity.sniffer.hurt": "嗅探獸受傷", "subtitles.entity.sniffer.idle": "嗅探獸呼嚕聲", "subtitles.entity.sniffer.scenting": "嗅探獸嗅聞", "subtitles.entity.sniffer.searching": "嗅探獸尋找", "subtitles.entity.sniffer.sniffing": "嗅探獸嗅探", "subtitles.entity.sniffer.step": "嗅探獸腳步聲", "subtitles.entity.snow_golem.death": "雪人死亡", "subtitles.entity.snow_golem.hurt": "雪人受傷", "subtitles.entity.snowball.throw": "投擲雪球聲", "subtitles.entity.spider.ambient": "蜘蛛嘶嘶聲", "subtitles.entity.spider.death": "蜘蛛死亡", "subtitles.entity.spider.hurt": "蜘蛛受傷", "subtitles.entity.squid.ambient": "魷魚游泳聲", "subtitles.entity.squid.death": "魷魚死亡", "subtitles.entity.squid.hurt": "魷魚受傷", "subtitles.entity.squid.squirt": "魷魚噴墨", "subtitles.entity.stray.ambient": "流髑喀啦聲", "subtitles.entity.stray.death": "流髑死亡", "subtitles.entity.stray.hurt": "流髑受傷", "subtitles.entity.strider.death": "熾足獸死亡", "subtitles.entity.strider.eat": "熾足獸進食", "subtitles.entity.strider.happy": "熾足獸顫抖", "subtitles.entity.strider.hurt": "熾足獸受傷", "subtitles.entity.strider.idle": "熾足獸吵鬧", "subtitles.entity.strider.retreat": "熾足獸退卻", "subtitles.entity.tadpole.death": "蝌蚪死亡", "subtitles.entity.tadpole.flop": "蝌蚪拍打", "subtitles.entity.tadpole.grow_up": "蝌蚪成長", "subtitles.entity.tadpole.hurt": "蝌蚪受傷", "subtitles.entity.tnt.primed": "TNT 嗞嗞作響", "subtitles.entity.tropical_fish.death": "熱帶魚死亡", "subtitles.entity.tropical_fish.flop": "熱帶魚拍打", "subtitles.entity.tropical_fish.hurt": "熱帶魚受傷", "subtitles.entity.turtle.ambient_land": "海龜啾啾聲", "subtitles.entity.turtle.death": "海龜死亡", "subtitles.entity.turtle.death_baby": "幼年海龜死亡", "subtitles.entity.turtle.egg_break": "海龜蛋破碎", "subtitles.entity.turtle.egg_crack": "海龜蛋裂開", "subtitles.entity.turtle.egg_hatch": "海龜蛋孵化", "subtitles.entity.turtle.hurt": "海龜受傷", "subtitles.entity.turtle.hurt_baby": "幼年海龜受傷", "subtitles.entity.turtle.lay_egg": "海龜產卵", "subtitles.entity.turtle.shamble": "海龜爬行", "subtitles.entity.turtle.shamble_baby": "幼年海龜爬行", "subtitles.entity.turtle.swim": "海龜游泳", "subtitles.entity.vex.ambient": "惱鬼惱怒", "subtitles.entity.vex.charge": "惱鬼尖叫", "subtitles.entity.vex.death": "惱鬼死亡", "subtitles.entity.vex.hurt": "惱鬼受傷", "subtitles.entity.villager.ambient": "村民嘀咕聲", "subtitles.entity.villager.celebrate": "村民歡呼", "subtitles.entity.villager.death": "村民死亡", "subtitles.entity.villager.hurt": "村民受傷", "subtitles.entity.villager.no": "村民拒絕", "subtitles.entity.villager.trade": "村民交易", "subtitles.entity.villager.work_armorer": "製甲師工作", "subtitles.entity.villager.work_butcher": "屠夫工作", "subtitles.entity.villager.work_cartographer": "製圖師工作", "subtitles.entity.villager.work_cleric": "神職人員工作", "subtitles.entity.villager.work_farmer": "農夫工作", "subtitles.entity.villager.work_fisherman": "漁夫工作", "subtitles.entity.villager.work_fletcher": "製箭師工作", "subtitles.entity.villager.work_leatherworker": "皮匠工作", "subtitles.entity.villager.work_librarian": "圖書管理員工作", "subtitles.entity.villager.work_mason": "石匠工作", "subtitles.entity.villager.work_shepherd": "牧羊人工作", "subtitles.entity.villager.work_toolsmith": "工具匠工作", "subtitles.entity.villager.work_weaponsmith": "武器匠工作", "subtitles.entity.villager.yes": "村民同意", "subtitles.entity.vindicator.ambient": "衛道士嘀咕聲", "subtitles.entity.vindicator.celebrate": "衛道士歡呼", "subtitles.entity.vindicator.death": "衛道士死亡", "subtitles.entity.vindicator.hurt": "衛道士受傷", "subtitles.entity.wandering_trader.ambient": "流浪商人嘀咕聲", "subtitles.entity.wandering_trader.death": "流浪商人死亡", "subtitles.entity.wandering_trader.disappeared": "流浪商人消失", "subtitles.entity.wandering_trader.drink_milk": "流浪商人飲用鮮奶", "subtitles.entity.wandering_trader.drink_potion": "流浪商人喝藥水", "subtitles.entity.wandering_trader.hurt": "流浪商人受傷", "subtitles.entity.wandering_trader.no": "流浪商人拒絕", "subtitles.entity.wandering_trader.reappeared": "流浪商人出現", "subtitles.entity.wandering_trader.trade": "流浪商人交易", "subtitles.entity.wandering_trader.yes": "流浪商人同意", "subtitles.entity.warden.agitated": "伏守者怒吼", "subtitles.entity.warden.ambient": "伏守者呻吟", "subtitles.entity.warden.angry": "伏守者發怒", "subtitles.entity.warden.attack_impact": "伏守者擊打聲", "subtitles.entity.warden.death": "伏守者死亡", "subtitles.entity.warden.dig": "伏守者掘地", "subtitles.entity.warden.emerge": "伏守者浮現", "subtitles.entity.warden.heartbeat": "伏守者心跳聲", "subtitles.entity.warden.hurt": "伏守者受傷", "subtitles.entity.warden.listening": "伏守者注意到聲響", "subtitles.entity.warden.listening_angry": "伏守者被注意到的聲響激怒", "subtitles.entity.warden.nearby_close": "伏守者接近", "subtitles.entity.warden.nearby_closer": "伏守者靠近", "subtitles.entity.warden.nearby_closest": "伏守者貼近", "subtitles.entity.warden.roar": "伏守者咆哮", "subtitles.entity.warden.sniff": "伏守者嗅聞", "subtitles.entity.warden.sonic_boom": "伏守者音爆", "subtitles.entity.warden.sonic_charge": "伏守者蓄力", "subtitles.entity.warden.step": "伏守者腳步聲", "subtitles.entity.warden.tendril_clicks": "伏守者的觸鬚抖動", "subtitles.entity.wind_charge.throw": "風彈飛行", "subtitles.entity.wind_charge.wind_burst": "風彈爆裂", "subtitles.entity.witch.ambient": "女巫咯咯笑", "subtitles.entity.witch.celebrate": "女巫歡呼", "subtitles.entity.witch.death": "女巫死亡", "subtitles.entity.witch.drink": "女巫喝藥水", "subtitles.entity.witch.hurt": "女巫受傷", "subtitles.entity.witch.throw": "女巫投擲", "subtitles.entity.wither.ambient": "凋零怪發怒", "subtitles.entity.wither.death": "凋零怪死亡", "subtitles.entity.wither.hurt": "凋零怪受傷", "subtitles.entity.wither.shoot": "凋零怪攻擊", "subtitles.entity.wither.spawn": "凋零怪被釋放", "subtitles.entity.wither_skeleton.ambient": "凋零骷髏喀啦聲", "subtitles.entity.wither_skeleton.death": "凋零骷髏死亡", "subtitles.entity.wither_skeleton.hurt": "凋零骷髏受傷", "subtitles.entity.wolf.ambient": "狼喘氣聲", "subtitles.entity.wolf.bark": "狼嗥叫", "subtitles.entity.wolf.death": "狼死亡", "subtitles.entity.wolf.growl": "狼嚎聲", "subtitles.entity.wolf.hurt": "狼受傷", "subtitles.entity.wolf.pant": "狼喘氣聲", "subtitles.entity.wolf.shake": "狼甩動身體", "subtitles.entity.wolf.whine": "狼嗚咽聲", "subtitles.entity.zoglin.ambient": "豬屍獸吼叫", "subtitles.entity.zoglin.angry": "豬屍獸憤怒地吼叫", "subtitles.entity.zoglin.attack": "豬屍獸攻擊", "subtitles.entity.zoglin.death": "豬屍獸死亡", "subtitles.entity.zoglin.hurt": "豬屍獸受傷", "subtitles.entity.zoglin.step": "豬屍獸腳步聲", "subtitles.entity.zombie.ambient": "殭屍呻吟聲", "subtitles.entity.zombie.attack_wooden_door": "門晃動", "subtitles.entity.zombie.break_wooden_door": "門損壞", "subtitles.entity.zombie.converted_to_drowned": "殭屍轉變為沉屍", "subtitles.entity.zombie.death": "殭屍死亡", "subtitles.entity.zombie.destroy_egg": "海龜蛋被踩踏", "subtitles.entity.zombie.hurt": "殭屍受傷", "subtitles.entity.zombie.infect": "殭屍傳染", "subtitles.entity.zombie_horse.ambient": "殭屍馬叫聲", "subtitles.entity.zombie_horse.death": "殭屍馬死亡", "subtitles.entity.zombie_horse.hurt": "殭屍馬受傷", "subtitles.entity.zombie_villager.ambient": "殭屍村民呻吟聲", "subtitles.entity.zombie_villager.converted": "殭屍村民喊叫", "subtitles.entity.zombie_villager.cure": "殭屍村民抽鼻", "subtitles.entity.zombie_villager.death": "殭屍村民死亡", "subtitles.entity.zombie_villager.hurt": "殭屍村民受傷", "subtitles.entity.zombified_piglin.ambient": "殭屍化豬布林呼嚕聲", "subtitles.entity.zombified_piglin.angry": "殭屍化豬布林憤怒的呼嚕聲", "subtitles.entity.zombified_piglin.death": "殭屍化豬布林死亡", "subtitles.entity.zombified_piglin.hurt": "殭屍化豬布林受傷", "subtitles.event.mob_effect.bad_omen": "不祥之兆顯現", "subtitles.event.mob_effect.raid_omen": "突襲迫在眉睫", "subtitles.event.mob_effect.trial_omen": "不祥試煉迫在眉睫", "subtitles.event.raid.horn": "不祥號角響起", "subtitles.item.armor.equip": "穿上裝備", "subtitles.item.armor.equip_chain": "鎖鏈盔甲裝備聲", "subtitles.item.armor.equip_diamond": "鑽石盔甲裝備聲", "subtitles.item.armor.equip_elytra": "鞘翅沙沙聲", "subtitles.item.armor.equip_gold": "黃金盔甲裝備聲", "subtitles.item.armor.equip_iron": "鐵製盔甲裝備聲", "subtitles.item.armor.equip_leather": "皮革盔甲裝備聲", "subtitles.item.armor.equip_netherite": "獄髓盔甲裝備聲", "subtitles.item.armor.equip_turtle": "戴上海龜殼", "subtitles.item.armor.equip_wolf": "狼鎧配戴聲", "subtitles.item.armor.unequip_wolf": "狼鎧滑落", "subtitles.item.axe.scrape": "斧頭刮削", "subtitles.item.axe.strip": "斧頭剝皮", "subtitles.item.axe.wax_off": "除蠟", "subtitles.item.bone_meal.use": "骨粉播撒聲", "subtitles.item.book.page_turn": "書頁沙沙聲", "subtitles.item.book.put": "擺上書本", "subtitles.item.bottle.empty": "瓶子淨空", "subtitles.item.bottle.fill": "裝瓶", "subtitles.item.brush.brushing.generic": "正在刷拭", "subtitles.item.brush.brushing.gravel": "正在刷拭礫石", "subtitles.item.brush.brushing.gravel.complete": "刷拭礫石完成", "subtitles.item.brush.brushing.sand": "正在刷拭沙子", "subtitles.item.brush.brushing.sand.complete": "刷拭沙子完成", "subtitles.item.bucket.empty": "倒空鐵桶", "subtitles.item.bucket.fill": "填充鐵桶", "subtitles.item.bucket.fill_axolotl": "六角恐龍被撈起", "subtitles.item.bucket.fill_fish": "捕獲魚", "subtitles.item.bucket.fill_tadpole": "捕獲蝌蚪", "subtitles.item.bundle.drop_contents": "束口袋清空", "subtitles.item.bundle.insert": "裝入物品", "subtitles.item.bundle.insert_fail": "束口袋已滿", "subtitles.item.bundle.remove_one": "取出物品", "subtitles.item.chorus_fruit.teleport": "玩家傳送", "subtitles.item.crop.plant": "種下作物", "subtitles.item.crossbow.charge": "弩上弦", "subtitles.item.crossbow.hit": "箭矢擊中聲", "subtitles.item.crossbow.load": "弩裝填", "subtitles.item.crossbow.shoot": "弩發射", "subtitles.item.dye.use": "染色", "subtitles.item.elytra.flying": "滑翔聲", "subtitles.item.firecharge.use": "火球颼颼聲", "subtitles.item.flintandsteel.use": "打火石咔嗒聲", "subtitles.item.glow_ink_sac.use": "螢光墨囊噴濺", "subtitles.item.goat_horn.play": "吹奏山羊角", "subtitles.item.hoe.till": "鋤頭耕地", "subtitles.item.honey_bottle.drink": "吞嚥", "subtitles.item.honeycomb.wax_on": "上蠟", "subtitles.item.horse_armor.unequip": "馬鎧滑落", "subtitles.item.ink_sac.use": "墨囊噴濺", "subtitles.item.lead.break": "剪斷拴繩", "subtitles.item.lead.tied": "繫上拴繩", "subtitles.item.lead.untied": "解開拴繩", "subtitles.item.llama_carpet.unequip": "地毯滑落", "subtitles.item.lodestone_compass.lock": "磁石羅盤綁定磁石", "subtitles.item.mace.smash_air": "重錘重擊聲", "subtitles.item.mace.smash_ground": "重錘重擊聲", "subtitles.item.nether_wart.plant": "種下作物", "subtitles.item.ominous_bottle.dispose": "玻璃瓶碎裂聲", "subtitles.item.saddle.unequip": "鞍滑落", "subtitles.item.shears.shear": "剪刀咔嗒聲", "subtitles.item.shears.snip": "剪刀剪斷聲", "subtitles.item.shield.block": "盾牌格擋", "subtitles.item.shovel.flatten": "鏟子夷平", "subtitles.item.spyglass.stop_using": "望遠鏡收合", "subtitles.item.spyglass.use": "望遠鏡伸展", "subtitles.item.totem.use": "圖騰觸發", "subtitles.item.trident.hit": "三叉戟突刺", "subtitles.item.trident.hit_ground": "三叉戟振動", "subtitles.item.trident.return": "三叉戟返回", "subtitles.item.trident.riptide": "三叉戟突進", "subtitles.item.trident.throw": "三叉戟鏗鏘聲", "subtitles.item.trident.thunder": "三叉戟雷聲劈啪", "subtitles.item.wolf_armor.break": "狼鎧損壞", "subtitles.item.wolf_armor.crack": "狼鎧破裂", "subtitles.item.wolf_armor.damage": "狼鎧受損", "subtitles.item.wolf_armor.repair": "狼鎧修復", "subtitles.particle.soul_escape": "靈魂逃脫", "subtitles.ui.cartography_table.take_result": "繪製地圖", "subtitles.ui.hud.bubble_pop": "氧氣值消耗", "subtitles.ui.loom.take_result": "使用紡織機", "subtitles.ui.stonecutter.take_result": "使用切石機", "subtitles.weather.rain": "雨聲", "symlink_warning.message": "繼續操作前請知悉，從包含符號連結的資料夾中載入世界可能會有風險。請前往 %s 了解更多資訊。", "symlink_warning.message.pack": "繼續操作前請知悉，載入包含符號連結的資源或資料包可能會有風險。請前往 %s 了解更多資訊。", "symlink_warning.message.world": "繼續操作前請知悉，從包含符號連結的資料夾中載入世界可能會有風險。請前往 %s 了解更多資訊。", "symlink_warning.more_info": "更多資訊", "symlink_warning.title": "世界資料夾包含符號連結", "symlink_warning.title.pack": "加入的資源或資料包存在符號連結", "symlink_warning.title.world": "世界資料夾包含符號連結", "team.collision.always": "碰撞啟用", "team.collision.never": "碰撞停用", "team.collision.pushOtherTeams": "碰撞其他隊伍", "team.collision.pushOwnTeam": "碰撞我方隊伍", "team.notFound": "未知的隊伍 '%s'", "team.visibility.always": "持續顯示", "team.visibility.hideForOtherTeams": "隱藏其他隊伍", "team.visibility.hideForOwnTeam": "隱藏我方隊伍", "team.visibility.never": "永不顯示", "telemetry.event.advancement_made.description": "了解達成進度後發生的事情，有利於我們更好了解並改進遊戲過程。", "telemetry.event.advancement_made.title": "達成進度", "telemetry.event.game_load_times.description": "此事件透過測量遊戲啟動階段的各段執行時長，可幫助我們找到需要改進啟動效能的地方。", "telemetry.event.game_load_times.title": "遊戲載入時間", "telemetry.event.optional": "%s（可選）", "telemetry.event.optional.disabled": "%s（可選 – 已停用）", "telemetry.event.performance_metrics.description": "了解 Minecraft 整體的效能概況，可以幫助我們針對各類裝置及作業系統調整並改進遊戲。 \n遊戲版本資訊也包含在內，以便我們比較新版本 Minecraft 的效能概況。", "telemetry.event.performance_metrics.title": "效能指標", "telemetry.event.required": "%s（必要）", "telemetry.event.world_load_times.description": "對我們而言，了解加入世界所需的時長，以及此時長隨時間推移的變化情形是十分重要的。舉例來說，當我們加入新功能或是進行較大的技術性改動時，我們會需要這些資訊來了解這些改動對載入時間有何影響。", "telemetry.event.world_load_times.title": "世界載入時間", "telemetry.event.world_loaded.description": "了解玩家如何遊玩 Minecraft （例如遊戲模式、伺服器和用戶端是否為原版、遊戲版本等資訊），可以讓我們專注於遊戲更新，用來改善玩家最關心的地方。\n世界載入事件和世界卸載事件一起用於計算遊戲時間。", "telemetry.event.world_loaded.title": "世界載入", "telemetry.event.world_unloaded.description": "此事件與世界載入事件用於計算遊戲時長。\n持續時間（單位為秒和刻）在遊玩結束時（回到標題畫面，或是從伺服器中斷連線）測量。", "telemetry.event.world_unloaded.title": "世界卸載", "telemetry.property.advancement_game_time.title": "遊戲時間（刻）", "telemetry.property.advancement_id.title": "進度 ID", "telemetry.property.client_id.title": "用戶端 ID", "telemetry.property.client_modded.title": "用戶端是否為原版", "telemetry.property.dedicated_memory_kb.title": "專屬記憶體（kB）", "telemetry.property.event_timestamp_utc.title": "事件時間戳記（UTC）", "telemetry.property.frame_rate_samples.title": "畫面更新率樣本（FPS）", "telemetry.property.game_mode.title": "遊戲模式", "telemetry.property.game_version.title": "遊戲版本", "telemetry.property.launcher_name.title": "啟動器名稱", "telemetry.property.load_time_bootstrap_ms.title": "啟動引導時間（毫秒）", "telemetry.property.load_time_loading_overlay_ms.title": "載入畫面顯示時間（毫秒）", "telemetry.property.load_time_pre_window_ms.title": "視窗開啟前時間（毫秒）", "telemetry.property.load_time_total_time_ms.title": "總載入時間（毫秒）", "telemetry.property.minecraft_session_id.title": "Minecraft Session ID", "telemetry.property.new_world.title": "是否為新的世界", "telemetry.property.number_of_samples.title": "樣本數量", "telemetry.property.operating_system.title": "作業系統", "telemetry.property.opt_in.title": "可選擇加入", "telemetry.property.platform.title": "平台", "telemetry.property.realms_map_content.title": "Realms 地圖內容（小遊戲名稱）", "telemetry.property.render_distance.title": "顯示距離", "telemetry.property.render_time_samples.title": "繪製時間樣本", "telemetry.property.seconds_since_load.title": "自載入後的時間（秒）", "telemetry.property.server_modded.title": "伺服器是否為原版", "telemetry.property.server_type.title": "伺服器類型", "telemetry.property.ticks_since_load.title": "自載入後的時間（刻）", "telemetry.property.used_memory_samples.title": "使用的記憶體", "telemetry.property.user_id.title": "使用者 ID", "telemetry.property.world_load_time_ms.title": "世界載入時間（毫秒）", "telemetry.property.world_session_id.title": "世界 Session ID", "telemetry_info.button.give_feedback": "提供意見回饋", "telemetry_info.button.privacy_statement": "隱私權聲明", "telemetry_info.button.show_data": "開啟我的統計資料", "telemetry_info.opt_in.description": "我同意傳送可選的遙測資料", "telemetry_info.property_title": "包含的資料", "telemetry_info.screen.description": "收集以下資料有助於讓我們了解與玩家相關的實際情形，進而幫助我們改進 Minecraft。\n您也可以傳送額外的意見回饋來幫助我們持續改進 Minecraft。", "telemetry_info.screen.title": "遙測資料收集", "test.error.block_property_mismatch": "屬性 %s 應為 %s，但實際為 %s", "test.error.block_property_missing": "缺少方塊屬性，屬性 %s 應為 %s", "test.error.entity_property": "實體 %s 測試失敗：%s", "test.error.entity_property_details": "實體 %s 測試失敗：%s，應為：%s，但實際為：%s", "test.error.expected_block": "方塊應為 %s，但實際為 %s", "test.error.expected_block_tag": "方塊應在 #%s 中，但實際為 %s", "test.error.expected_container_contents": "容器應含有：%s", "test.error.expected_container_contents_single": "容器應含有單個：%s", "test.error.expected_empty_container": "容器應為空", "test.error.expected_entity": "應為 %s", "test.error.expected_entity_around": "%s 應存在於 %s, %s, %s 周圍", "test.error.expected_entity_count": "類型為 %2$s 的實體應有 %1$s 個，但找到了 %3$s 個", "test.error.expected_entity_data": "實體資料應為：%s，但實際為：%s", "test.error.expected_entity_data_predicate": "%s 的實體資料不相符", "test.error.expected_entity_effect": "%s 應有效果 %s %s", "test.error.expected_entity_having": "實體物品欄中應含有 %s", "test.error.expected_entity_holding": "實體應持有 %s", "test.error.expected_entity_in_test": "%s 應存在於測試中", "test.error.expected_entity_not_touching": "%s 不應與 %s, %s, %s 接觸（相對位置：%s, %s, %s）", "test.error.expected_entity_touching": "%s 應與 %s, %s, %s 接觸（相對位置：%s, %s, %s）", "test.error.expected_item": "應為類型 %s 的物品", "test.error.expected_items_count": "類型為 %2$s 的物品應有 %1$s 個，但找到了 %3$s 個", "test.error.fail": "滿足失敗條件", "test.error.invalid_block_type": "找到非預期的方塊類型：%s", "test.error.missing_block_entity": "缺少方塊實體", "test.error.position": "%s，位於 %s, %s, %s（相對位置：%s, %s, %s），時間為 %s 刻", "test.error.sequence.condition_already_triggered": "條件已在 %s 刻時觸發", "test.error.sequence.condition_not_triggered": "條件未觸發", "test.error.sequence.invalid_tick": "在無效刻時成功：應為 %s", "test.error.sequence.not_completed": "測試在序列完成前已逾時", "test.error.set_biome": "無法設定測試的生態域", "test.error.spawn_failure": "無法建立實體 %s", "test.error.state_not_equal": "狀態錯誤。應為 %s，但實際為 %s", "test.error.structure.failure": "無法為 %s 放置測試結構", "test.error.tick": "%s，時間為 %s 刻", "test.error.ticking_without_structure": "在放置結構前執行了測試", "test.error.timeout.no_result": "未在 %s 刻內成功或失敗", "test.error.timeout.no_sequences_finished": "在 %s 刻內沒有序列完成", "test.error.too_many_entities": "%2$s, %3$s, %4$s 周圍應只有一個 %1$s，但找到了 %5$s 個", "test.error.unexpected_block": "方塊不應為 %s", "test.error.unexpected_entity": "%s 不應存在", "test.error.unexpected_item": "物品不應為類型 %s", "test.error.unknown": "未知的內部錯誤：%s", "test.error.value_not_equal": "%s 應為 %s，但實際為 %s", "test.error.wrong_block_entity": "錯誤的方塊實體類型：%s", "test_block.error.missing": "測試結構缺少 %s 方塊", "test_block.error.too_many": "%s 方塊過多", "test_block.invalid_timeout": "無效的時間限制 (%s) - 刻數須為正數", "test_block.message": "訊息：", "test_block.mode.accept": "接受", "test_block.mode.fail": "失敗", "test_block.mode.log": "記錄", "test_block.mode.start": "啟動", "test_block.mode_info.accept": "接受模式 ─ 接受測試（部分）成功", "test_block.mode_info.fail": "失敗模式 ─ 使測試失敗", "test_block.mode_info.log": "記錄模式 ─ 將紀錄輸出至記錄檔中", "test_block.mode_info.start": "啟動模式 ─ 測試的起點", "test_instance.action.reset": "重設並載入", "test_instance.action.run": "載入並執行", "test_instance.action.save": "儲存結構", "test_instance.description.batch": "批次：%s", "test_instance.description.failed": "失敗：%s", "test_instance.description.function": "函數：%s", "test_instance.description.invalid_id": "無效的測試 ID", "test_instance.description.no_test": "測試不存在", "test_instance.description.structure": "結構：%s", "test_instance.description.type": "類型：%s", "test_instance.type.block_based": "基於方塊的測試", "test_instance.type.function": "內建函數測試", "test_instance_block.entities": "實體：", "test_instance_block.error.no_test": "存在未被定義的測試，無法在 %s, %s, %s 處執行測試實例", "test_instance_block.error.no_test_structure": "沒有測試結構，無法在 %s, %s, %s 處執行測試實例", "test_instance_block.error.unable_to_save": "無法在 %s, %s, %s 處儲存測試實例的測試結構模板", "test_instance_block.invalid": "[無效]", "test_instance_block.reset_success": "測試重設成功：%s", "test_instance_block.rotation": "旋轉角度：", "test_instance_block.size": "測試結構大小", "test_instance_block.starting": "正在啟動測試 %s", "test_instance_block.test_id": "測試實例 ID", "title.32bit.deprecation": "偵測到 32 位元系統：未來需求為 64 位元系統時，您可能無法遊玩！", "title.32bit.deprecation.realms": "Minecraft 在不久後僅支援 64 位元系統，您將無法在此裝置遊玩或使用 Realms。您需要自行取消 Realms 的任何訂閱。", "title.32bit.deprecation.realms.check": "不再顯示此畫面", "title.32bit.deprecation.realms.header": "偵測到 32 位元系統", "title.credits": "Mojang AB 版權所有。請勿二次轉發！", "title.multiplayer.disabled": "多人遊戲已停用。請檢查您的 Microsoft 帳號設定。", "title.multiplayer.disabled.banned.name": "您遊玩線上遊戲前必須更改您的名稱", "title.multiplayer.disabled.banned.permanent": "您帳號的線上遊玩功能已被永久停權", "title.multiplayer.disabled.banned.temporary": "您帳號的線上遊玩功能已被暫時停權", "title.multiplayer.lan": "多人遊戲（區域網路）", "title.multiplayer.other": "多人遊戲（第三方伺服器）", "title.multiplayer.realms": "多人遊戲（Realms）", "title.singleplayer": "單人遊戲", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": " % 你好", "translation.test.invalid2": "你好 %s", "translation.test.none": "你好，世界！", "translation.test.world": "世界", "trim_material.minecraft.amethyst": "紫水晶材質", "trim_material.minecraft.copper": "銅材質", "trim_material.minecraft.diamond": "鑽石材質", "trim_material.minecraft.emerald": "綠寶石材質", "trim_material.minecraft.gold": "金材質", "trim_material.minecraft.iron": "鐵材質", "trim_material.minecraft.lapis": "青金石材質", "trim_material.minecraft.netherite": "獄髓材質", "trim_material.minecraft.quartz": "石英材質", "trim_material.minecraft.redstone": "紅石材質", "trim_material.minecraft.resin": "樹脂材質", "trim_pattern.minecraft.bolt": "鑲鉚盔甲紋樣", "trim_pattern.minecraft.coast": "海岸盔甲紋樣", "trim_pattern.minecraft.dune": "沙丘盔甲紋樣", "trim_pattern.minecraft.eye": "眼眸盔甲紋樣", "trim_pattern.minecraft.flow": "渦流盔甲紋樣", "trim_pattern.minecraft.host": "主人盔甲紋樣", "trim_pattern.minecraft.raiser": "牧者盔甲紋樣", "trim_pattern.minecraft.rib": "肋骨盔甲紋樣", "trim_pattern.minecraft.sentry": "哨兵盔甲紋樣", "trim_pattern.minecraft.shaper": "工匠盔甲紋樣", "trim_pattern.minecraft.silence": "寂靜盔甲紋樣", "trim_pattern.minecraft.snout": "豬鼻盔甲紋樣", "trim_pattern.minecraft.spire": "旋塔盔甲紋樣", "trim_pattern.minecraft.tide": "潮汐盔甲紋樣", "trim_pattern.minecraft.vex": "惱鬼盔甲紋樣", "trim_pattern.minecraft.ward": "伏守盔甲紋樣", "trim_pattern.minecraft.wayfinder": "嚮導盔甲紋樣", "trim_pattern.minecraft.wild": "荒野盔甲紋樣", "tutorial.bundleInsert.description": "按下右鍵以放入物品", "tutorial.bundleInsert.title": "使用束口袋", "tutorial.craft_planks.description": "配方手冊能對您有所幫助", "tutorial.craft_planks.title": "合成木材", "tutorial.find_tree.description": "揍它來採集木頭", "tutorial.find_tree.title": "尋找一棵樹", "tutorial.look.description": "使用您的滑鼠來轉向", "tutorial.look.title": "環顧四周", "tutorial.move.description": "用 %s 來跳躍", "tutorial.move.title": "用 %s、%s、%s 和 %s 來移動", "tutorial.open_inventory.description": "按 %s", "tutorial.open_inventory.title": "開啟您的物品欄", "tutorial.punch_tree.description": "按住 %s", "tutorial.punch_tree.title": "摧毀這棵樹", "tutorial.socialInteractions.description": "按下 %s 以開啟", "tutorial.socialInteractions.title": "社群交流", "upgrade.minecraft.netherite_upgrade": "獄髓升級"}