
#Wires
[wires]
	#Small Connector max output in FE/t (Energy transfer).
	#Range: > 0
	small_connector_max_output = 1000
	#Small Connector max input in FE/t (Energy transfer).
	#Range: > 0
	small_connector_max_input = 1000
	#Small Connector With Light energy consumption in FE/t.
	#Range: > 0
	small_light_connector_consumption = 1
	#Large Connector max wire length in blocks.
	#Range: 0 ~ 256
	large_connector_wire_length = 32
	#Small Connector max wire length in blocks.
	#Range: 0 ~ 256
	small_connector_wire_length = 16
	#Large Connector max input in FE/t (Energy transfer).
	#Range: > 0
	large_connector_max_input = 5000
	#Allows blocks attached to a connector to freely pass energy to and from the connector network.
	connector_allow_passive_io = true
	#Large Connector max output in FE/t (Energy transfer).
	#Range: > 0
	large_connector_max_output = 5000
	#Ignore checking if block face can support connector.
	connector_ignore_face_check = true

#Make sure config changes are duplicated on both Clients and the Server when running a dedicated Server,
# as the config isnt synced between Clients and Server.
#General Settings
[general]
	#Max stress for the Alternator and Electric Motor (in SU at 256 RPM).
	#Range: > 0
	max_stress = 16384
	#Forge Energy conversion rate (in FE/t at 256 RPM, value is the FE/t generated and consumed is at 256rpm).
	#Range: > 0
	fe_at_max_rpm = 480
	#If audio should be enabled or not.
	audio_enabled = true

#Portable Energy Interface
[portable_energy_interface]
	#PEI max output in FE/t (Energy transfer).
	#Range: > 0
	pei_max_output = 5000
	#PEI max input in FE/t (Energy transfer).
	#Range: > 0
	pei_max_input = 5000

#Electric Motor
[electric_motor]
	#Electric Motor internal capacity in FE.
	#Range: > 0
	motor_capacity = 5000
	#Electric Motor minimum required energy consumption in FE/t.
	#Range: > 0
	motor_min_consumption = 8
	#Electric Motor max input in FE (Energy transfer not consumption).
	#Range: > 0
	motor_max_input = 5000
	#Electric Motor min/max RPM.
	#Range: > 1
	motor_rpm_range = 256

#Tesla Coil
[tesla_coil]
	#Tesla Coil charge rate in FE/t.
	#Range: > 0
	tesla_coil_charge_rate = 5000
	#Tesla Coil fire interval (in ticks).
	#Range: > 0
	tesla_coil_fire_cooldown = 20
	#Hurt range (in blocks/meters).
	#Range: > 0
	tesla_coil_hurt_range = 3
	#Tesla Coil internal capacity in FE.
	#Range: > 0
	tesla_coil_capacity = 40000
	#Energy consumed when Tesla Coil is fired (in FE).
	#Range: > 0
	tesla_coil_hurt_energy_required = 1000
	#The duration of the Shocked effect for mobs (in ticks).
	#Range: > 0
	tesla_coil_effect_time_mob = 20
	#The duration of the Shocked effect for players (in ticks).
	#Range: > 0
	tesla_coil_effect_time_player = 20
	#Tesla Coil max input in FE/t (Energy transfer).
	#Range: > 0
	tesla_coil_max_input = 10000
	#Tesla Coil charge rate in FE/t for recipes.
	#Range: > 0
	tesla_coil_recipe_charge_rate = 2000
	#Damaged dealt to mobs when Tesla Coil is fired (in half hearts).
	#Range: > 0
	tesla_coil_hurt_mob = 3
	#Damaged dealt to players when Tesla Coil is fired (in half hearts).
	#Range: > 0
	tesla_coil_hurt_player = 2

#Alternator
[alternator]
	#Alternator efficiency relative to base conversion rate.
	#Range: 0.01 ~ 1.0
	generator_efficiency = 0.75
	#Alternator internal capacity in FE.
	#Range: > 0
	generator_capacity = 5000
	#Alternator max input in FE (Energy transfer, not generation).
	#Range: > 0
	generator_max_output = 5000

#Accumulator
[accumulator]
	#Accumulator internal capacity per block in FE.
	#Range: > 0
	accumulator_capacity = 2000000
	#Accumulator max output in FE/t (Energy transfer).
	#Range: > 0
	accumulator_max_output = 5000
	#Accumulator max input in FE/t (Energy transfer).
	#Range: > 0
	accumulator_max_input = 5000
	#Accumulator max multiblock height.
	#Range: 1 ~ 8
	accumulator_max_height = 5
	#Accumulator max multiblock width.
	#Range: 1 ~ 8
	accumulator_max_width = 3

#Rolling Mill
[rolling_mill]
	#Rolling Mill duration in ticks.
	#Range: > 0
	rolling_mill_processing_duration = 120
	#Rolling Mill base stress impact.
	#Range: 0 ~ 1024
	rolling_mill_stress = 8

#Misc
[misc]
	#Diamond Grit Sandpaper durability (number of uses).
	#Range: > 3
	diamond_grit_sandpaper_uses = 1024
	#Barbed Wire Damage.
	#Range: 0.0 ~ 3.4028234663852886E38
	barbed_wire_damage = 2.0

