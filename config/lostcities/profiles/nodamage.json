{"cities": {"__readonly__": "This profile is read only and cannot be modified! If you want to make a new profile based on this then you can make a copy to a new name", "cityLevel3Height": 99, "cityMinRadius": 50, "cityAvoidVoid": true, "cityLevel1Height": 83, "cityMaxRadius": 128, "cityStyleThreshold": -1.0, "cityMaxHeight": 130, "cityChance": 0.01, "cityThreshold": 0.2, "cityLevel0Height": 75, "cityStyleAlternative": "", "cityPerlinInnerScale": 0.1, "cityMinHeight": 50, "cityPerlinOffset": 0.1, "cityLevel2Height": 91, "oceanCorrectionBorder": 4, "cityPerlinScale": 3.0}, "client": {"__readonly__": "This profile is read only and cannot be modified! If you want to make a new profile based on this then you can make a copy to a new name", "horizon": -1.0, "fogDensity": -1.0, "fogBlue": -1.0, "fogRed": -1.0, "fogGreen": -1.0}, "explosions": {"__readonly__": "This profile is read only and cannot be modified! If you want to make a new profile based on this then you can make a copy to a new name", "explosionMaxRadius": 35, "explosionMinRadius": 15, "explosionMinHeight": 75, "miniExplosionChance": 0.0, "miniExplosionMaxHeight": 100, "miniExplosionMaxRadius": 12, "debrisToNearbyChunkFactor": 200, "explosionChance": 0.0, "explosionMaxHeight": 90, "miniExplosionMinRadius": 5, "explosionsInCitiesOnly": true, "miniExplosionMinHeight": 60}, "lostcity": {"__readonly__": "This profile is read only and cannot be modified! If you want to make a new profile based on this then you can make a copy to a new name", "highwaySupports": true, "ruinChance": 0.0, "groundLevel": 71, "railwayStationsEnabled": true, "buildingChance": 0.3, "railwaysCanEnd": false, "buildingMaxFloorsChance": 6, "bedrockLayer": 1, "liquidBlock": "minecraft:water", "rubbleLayer": false, "buildingMinFloors": 0, "seaLevel": -1, "rubbleDirtScale": 3.0, "fountainChance": 0.05, "dataCenterChance": 0.1, "warning": "", "highwaySecondaryPerlinScale": 10.0, "spawnNotInBuilding": false, "bridgeSupports": true, "worldStyle": "standard", "buildingMaxFloors": 8, "avoidFoliage": false, "buildingMinCellars": 0, "buildingFrontChance": 0.2, "baseBlock": "minecraft:stone", "ruinMaxlevelPercent": 1.0, "spawnBiome": "", "avoidWater": false, "railwaysEnabled": true, "highwayRequiresTwoCities": true, "parkBorder": true, "editMode": false, "spawnCity": "", "randomLeafBlockChance": 0.1, "rubbleLeaveScale": 6.0, "vineChance": 0.009, "highwayLevelFromCities": 0, "highwayPerlinFactor": 2.0, "bridgeChance": 0.7, "ruinMinlevelPercent": 0.8, "buildingDoorwayChance": 0.6, "icon": "textures/gui/icon_nodamage.png", "parkElevation": true, "description": "Like default but no explosion damage", "spawnSphere": "", "forceSpawnInBuilding": false, "terrainFixUpperMinOffset": -1, "highwayDistanceMask": 7, "terrainFixUpperMaxOffset": 1, "generateLoot": true, "terrainFixLowerMaxOffset": -3, "chestWithoutLootChance": 0.2, "terrainFixLowerMinOffset": -4, "extraDescription": "Ruins and rubble are disabled and ravines are disabled in cities", "landscapeType": "default", "generateLighting": false, "libraryChance": 0.1, "generateSpawners": true, "buildingMinFloorsChance": 4, "buildingWithoutLootChance": 0.2, "railwayDungeonChance": 0.01, "generateNether": false, "parkChance": 0.2, "corridorChance": 0.7, "randomLeafBlockThickness": 2, "buildingMaxCellars": 3, "highwayMainPerlinScale": 50.0, "scatteredChanceMultiplier": 1.0}, "cityspheres": {"__readonly__": "This profile is read only and cannot be modified! If you want to make a new profile based on this then you can make a copy to a new name", "citySphereFactor": 1.2, "citySphereChance": 0.7, "outsideProfile": "", "outsideGroundLevel": -1, "onlyPredefined": false, "citySphereClearBelow": 0, "citySphereClearBelowUntilAir": false, "monorailOffset": -2, "outsideSurfaceVariation": 1.0, "monorailChance": 0.8, "citySphereClearAbove": 0, "citySphereClearAboveUntilAir": false, "sphereSurfaceVariation": 1.0}, "public": true}