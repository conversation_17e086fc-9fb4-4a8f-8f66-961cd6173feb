
#Mana
[mana]
	#Base mana regen in seconds
	#Range: > 0
	baseRegen = 5
	#Base max mana
	#Range: > 0
	baseMax = 100
	#How often max and regen will be calculated, in ticks. NOTE: Having the base mana regen AT LEAST this value is recommended.
	#Range: 1 ~ 20
	updateInterval = 5
	#Max mana bonus per glyph
	#Range: > 0
	glyphmax = 15
	#Max mana bonus for tier of book
	#Range: > 0
	tierMax = 50
	#Mana regen bonus for tier of book
	#Range: > 0
	tierRegen = 1
	#Mana Boost value per level
	#Range: > 0
	manaBoost = 25
	#(enchantment) Mana regen per second per level
	#Range: > 0
	manaRegenEnchantment = 2
	#Regen bonus per glyph
	#Range: 0.0 ~ 2.147483647E9
	glyphRegen = 0.33
	#Regen bonus per potion level
	#Range: > 0
	potionRegen = 10

[spell_casting]
	#Enforce augment cap on casting? Turn this off if you are a pack maker and want to create more powerful items than players.
	enforceCapOnCast = true
	#Enforce glyph per spell limit on casting? Turn this off if you are a pack maker and want to create more powerful items than players.
	enforceGlyphLimitOnCast = true

[item]
	#Cost per glyph in a codex
	#Range: > 0
	codexCost = 10

[warp_portals]
	#Enable warp portals?
	enableWarpPortals = true

#Beta Features
[beta]
	#Allow crafting infinite spells. This is a beta feature and may cause crashes.
	infiniteSpells = false
	#Limits the crafting infinite spells beta, set a cap to the number of additional glyphs. This is a beta feature and may cause crashes.
	#Range: 10 ~ 1000
	infiniteSpellLimit = 30

