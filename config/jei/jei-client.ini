[advanced]
	# Description: Display search bar in the center
	# Valid Values: [true, false]
	# Default Value: false
	CenterSearch = false

	# Description: Set low-memory mode (makes search very slow, but uses less RAM)
	# Valid Values: [true, false]
	# Default Value: false
	LowMemorySlowSearchEnabled = false

	# Description: Enable cheating items into the hotbar by using the shift+number keys.
	# Valid Values: [true, false]
	# Default Value: false
	CheatToHotbarUsingHotkeysEnabled = false

	# Description: Enable adding new bookmarks to the front of the bookmark list.
	# Valid Values: [true, false]
	# Default Value: true
	AddBookmarksToFrontEnabled = true

	# Description: When looking up recipes with items that contain fluids, also look up recipes for the fluids.
	# Valid Values: [true, false]
	# Default Value: false
	LookupFluidContents = false

	# Description: How items should be handed to you
	# Valid Values: [INVENTORY, MOUSE_PICKUP]
	# Default Value: MOUSE_PICKUP
	GiveMode = MOUSE_PICKUP

	# Description: Max. recipe gui height
	# Valid Values: Any integer greater than or equal to 175
	# Default Value: 350
	RecipeGuiHeight = 350


[sorting]
	# Description: Sorting order for the ingredient list
	# Valid Values: A comma-separated list containing values of:
	# [MOD_NAME, INGREDIENT_TYPE, ALPHABETICAL, CREATIVE_MENU, TAG, ARMOR, MAX_DURABILITY]
	# Default Value: MOD_NAME, INGREDIENT_TYPE, CREATIVE_MENU
	IngredientSortStages = MOD_NAME, INGREDIENT_TYPE, CREATIVE_MENU


[search]
	# Description: Search mode for Mod Names (prefix: @)
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: REQUIRE_PREFIX
	ModNameSearchMode = REQUIRE_PREFIX

	# Description: Search mode for Tooltips (prefix: #)
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: ENABLED
	TooltipSearchMode = ENABLED

	# Description: Search mode for Tag Names (prefix: $)
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: REQUIRE_PREFIX
	TagSearchMode = REQUIRE_PREFIX

	# Description: Search mode for Colors (prefix: ^)
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: DISABLED
	ColorSearchMode = DISABLED

	# Description: Search mode for resources locations (prefix: &)
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: DISABLED
	ResourceLocationSearchMode = DISABLED

	# Description: Search advanced tooltips (visible with F3+H)
	# Valid Values: [true, false]
	# Default Value: false
	SearchAdvancedTooltips = false


[IngredientList]
	# Description: Max number of rows shown
	# Valid Values: An integer in the range [1, 100] (inclusive)
	# Default Value: 16
	MaxRows = 99

	# Description: Max number of columns shown
	# Valid Values: An integer in the range [4, 100] (inclusive)
	# Default Value: 9
	MaxColumns = 18

	# Description: Horizontal alignment of the ingredient grid inside the available area
	# Valid Values: [LEFT, CENTER, RIGHT]
	# Default Value: RIGHT
	HorizontalAlignment = RIGHT

	# Description: Vertical alignment of the ingredient grid inside the available area
	# Valid Values: [TOP, CENTER, BOTTOM]
	# Default Value: TOP
	VerticalAlignment = TOP

	# Description: Visibility of the top page buttons. Use AUTO_HIDE to only show it when there are multiple pages.
	# Valid Values: [ENABLED, AUTO_HIDE, DISABLED]
	# Default Value: ENABLED
	ButtonNavigationVisibility = ENABLED

	# Description: Set to true to draw a background texture behind the gui.
	# Valid Values: [true, false]
	# Default Value: false
	DrawBackground = false


[BookmarkList]
	# Description: Max number of rows shown
	# Valid Values: An integer in the range [1, 100] (inclusive)
	# Default Value: 16
	MaxRows = 16

	# Description: Max number of columns shown
	# Valid Values: An integer in the range [4, 100] (inclusive)
	# Default Value: 9
	MaxColumns = 9

	# Description: Horizontal alignment of the ingredient grid inside the available area
	# Valid Values: [LEFT, CENTER, RIGHT]
	# Default Value: LEFT
	HorizontalAlignment = LEFT

	# Description: Vertical alignment of the ingredient grid inside the available area
	# Valid Values: [TOP, CENTER, BOTTOM]
	# Default Value: TOP
	VerticalAlignment = TOP

	# Description: Visibility of the top page buttons. Use AUTO_HIDE to only show it when there are multiple pages.
	# Valid Values: [ENABLED, AUTO_HIDE, DISABLED]
	# Default Value: ENABLED
	ButtonNavigationVisibility = ENABLED

	# Description: Set to true to draw a background texture behind the gui.
	# Valid Values: [true, false]
	# Default Value: false
	DrawBackground = false


