# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Apotheosis Enchantment Information

# This file contains configurable data for each enchantment.
# The names of each category correspond to the registry names of every loaded enchantment.


"minecraft:protection" {
    # The max level of this enchantment - originally 4.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"minecraft:fire_protection" {
    # The max level of this enchantment - originally 4.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:feather_falling" {
    # The max level of this enchantment - originally 4.
    # Default: 11; Range: [1 ~ 127]
    I:"Max Level"=11

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:blast_protection" {
    # The max level of this enchantment - originally 4.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:projectile_protection" {
    # The max level of this enchantment - originally 4.
    # Default: 11; Range: [1 ~ 127]
    I:"Max Level"=11

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:respiration" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:aqua_affinity" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:thorns" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"minecraft:depth_strider" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:frost_walker" {
    # The max level of this enchantment - originally 2.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:binding_curse" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"minecraft:soul_speed" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"minecraft:swift_sneak" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"minecraft:sharpness" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"minecraft:smite" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:bane_of_arthropods" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:knockback" {
    # The max level of this enchantment - originally 2.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:fire_aspect" {
    # The max level of this enchantment - originally 2.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:looting" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:sweeping" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:efficiency" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"minecraft:silk_touch" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"minecraft:unbreaking" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:fortune" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:power" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"minecraft:punch" {
    # The max level of this enchantment - originally 2.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:flame" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:infinity" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"minecraft:luck_of_the_sea" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:lure" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:loyalty" {
    # The max level of this enchantment - originally 3.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:impaling" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:riptide" {
    # The max level of this enchantment - originally 3.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:channeling" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"minecraft:multishot" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:quick_charge" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"minecraft:piercing" {
    # The max level of this enchantment - originally 4.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"minecraft:mending" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"minecraft:vanishing_curse" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"evilcraft:unusing" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"evilcraft:breaking" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"evilcraft:life_stealing" {
    # The max level of this enchantment - originally 3.
    # Default: 6; Range: [1 ~ 127]
    I:"Max Level"=6

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"evilcraft:poison_tip" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"evilcraft:vengeance" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"apotheosis:berserkers_fury" {
    # The max level of this enchantment - originally 3.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Level"=3

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:chainsaw" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:chromatic" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"apotheosis:crescendo" {
    # The max level of this enchantment - originally 5.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:earths_boon" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:endless_quiver" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:exploitation" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=VERY_RARE
}


"apotheosis:growth_serum" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:icy_thorns" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:infusion" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:knowledge" {
    # The max level of this enchantment - originally 3.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Level"=3

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:life_mending" {
    # The max level of this enchantment - originally 3.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Level"=3

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:miners_fervor" {
    # The max level of this enchantment - originally 5.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:natures_blessing" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:obliteration" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:rebounding" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:reflective" {
    # The max level of this enchantment - originally 5.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:scavenger" {
    # The max level of this enchantment - originally 3.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Level"=3

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"apotheosis:shield_bash" {
    # The max level of this enchantment - originally 4.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:spearfishing" {
    # The max level of this enchantment - originally 5.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"apotheosis:splitting" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:stable_footing" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"apotheosis:tempting" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"supplementaries:stasis" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"tombstone:soulbound" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"tombstone:magic_siphon" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"tombstone:plague_bringer" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"tombstone:curse_of_bones" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"tombstone:blessing" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"tombstone:frostbite" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"tombstone:spectral_bite" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=RARE
}


"naturesaura:aura_mending" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"railcraft:wrecking" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"railcraft:implosion" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"railcraft:destruction" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=3

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"railcraft:smack" {
    # The max level of this enchantment - originally 4.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ad_astra_giselle_addon:space_fire_proof" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ad_astra_giselle_addon:acid_rain_proof" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ad_astra_giselle_addon:gravity_normalizing" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ad_astra_giselle_addon:space_breathing" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ars_nouveau:mana_regen" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ars_nouveau:mana_boost" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ars_nouveau:reactive" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"utilitix:bell_range" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"apotheosis:bane_of_illagers" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"apotheosis:capturing" {
    # The max level of this enchantment - originally 5.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"twilightforest:fire_react" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"twilightforest:chill_aura" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"twilightforest:destruction" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"farmersdelight:backstabbing" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"deeperdarker:catalysis" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"deeperdarker:sculk_smite" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"undergarden:ricochet" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"undergarden:longevity" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"undergarden:self_sling" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"create:potato_recovery" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"create:capacity" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"mahoutsukai:projector" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"mysticalagriculture:mystical_enlightenment" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"mysticalagriculture:soul_siphoner" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"enderio:auto_smelt" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"enderio:repellent" {
    # The max level of this enchantment - originally 4.
    # Default: 11; Range: [1 ~ 127]
    I:"Max Level"=11

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"enderio:shimmer" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"enderio:soulbound" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"enderio:withering" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"enderio:xp_boost" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"reliquary:severing" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"create_sa:gravity_gun" {
    I:"Max Level"=1
    I:"Max Loot Level"=1
    S:"Max Power Function"=
    S:"Min Power Function"=
    B:Treasure=false
    B:Discoverable=true
    B:Lootable=true
    B:Tradeable=true
    S:Rarity=RARE
}


"create_sa:digging" {
    I:"Max Level"=1
    I:"Max Loot Level"=1
    S:"Max Power Function"=
    S:"Min Power Function"=
    B:Treasure=false
    B:Discoverable=true
    B:Lootable=true
    B:Tradeable=true
    S:Rarity=UNCOMMON
}


"create_sa:impact" {
    I:"Max Level"=7
    I:"Max Loot Level"=3
    S:"Max Power Function"=
    S:"Min Power Function"=
    B:Treasure=false
    B:Discoverable=true
    B:Lootable=true
    B:Tradeable=true
    S:Rarity=UNCOMMON
}


"create_sa:hellfire" {
    I:"Max Level"=1
    I:"Max Loot Level"=1
    S:"Max Power Function"=
    S:"Min Power Function"=
    B:Treasure=false
    B:Discoverable=true
    B:Lootable=true
    B:Tradeable=true
    S:Rarity=COMMON
}


"minecolonies:raider_damage_enchant" {
    # The max level of this enchantment - originally 2.
    # Default: 6; Range: [1 ~ 127]
    I:"Max Level"=6

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"tombstone:spectral_conjurer" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"tombstone:incurable_wounds" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"tombstone:decrepitude" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"tombstone:sanctified" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"tombstone:ruthless_strike" {
    # The max level of this enchantment - originally 10.
    # Default: 13; Range: [1 ~ 127]
    I:"Max Level"=13

    # The max level of this enchantment available from loot sources.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Loot Level"=10

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"mythicbotany:hammer_mobility" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"cofh_core:holding" {
    # The max level of this enchantment - originally 4.
    # Default: 12; Range: [1 ~ 127]
    I:"Max Level"=12

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: COMMON
    S:Rarity=COMMON
}


"ars_elemental:mirror_shield" {
    # The max level of this enchantment - originally 4.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: false
    B:Discoverable=false

    # If enchanted books of this enchantment are available via loot sources.
    # Default: false
    B:Lootable=false

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ars_elemental:soulbound" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: false
    B:Tradeable=false

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"draconicevolution:reaper_enchantment" {
    # The max level of this enchantment - originally 5.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:magic_protection" {
    # The max level of this enchantment - originally 4.
    # Default: 9; Range: [1 ~ 127]
    I:"Max Level"=9

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:displacement" {
    # The max level of this enchantment - originally 3.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:fire_rebuke" {
    # The max level of this enchantment - originally 3.
    # Default: 6; Range: [1 ~ 127]
    I:"Max Level"=6

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ensorcellation:frost_rebuke" {
    # The max level of this enchantment - originally 3.
    # Default: 6; Range: [1 ~ 127]
    I:"Max Level"=6

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ensorcellation:air_affinity" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:xp_boost" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:gourmand" {
    # The max level of this enchantment - originally 2.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:reach" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:vitality" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ensorcellation:damage_ender" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:damage_illager" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:damage_villager" {
    # The max level of this enchantment - originally 5.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Loot Level"=5

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:cavalier" {
    # The max level of this enchantment - originally 4.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:frost_aspect" {
    # The max level of this enchantment - originally 2.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:instigating" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:leech" {
    # The max level of this enchantment - originally 4.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 4; Range: [1 ~ 127]
    I:"Max Loot Level"=4

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:magic_edge" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:vorpal" {
    # The max level of this enchantment - originally 3.
    # Default: 8; Range: [1 ~ 127]
    I:"Max Level"=8

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:excavating" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:hunter" {
    # The max level of this enchantment - originally 2.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ensorcellation:quick_draw" {
    # The max level of this enchantment - originally 3.
    # Default: 5; Range: [1 ~ 127]
    I:"Max Level"=5

    # The max level of this enchantment available from loot sources.
    # Default: 3; Range: [1 ~ 127]
    I:"Max Loot Level"=3

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:trueshot" {
    # The max level of this enchantment - originally 2.
    # Default: 6; Range: [1 ~ 127]
    I:"Max Level"=6

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:volley" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:angler" {
    # The max level of this enchantment - originally 2.
    # Default: 7; Range: [1 ~ 127]
    I:"Max Level"=7

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ensorcellation:pilfering" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: RARE
    S:Rarity=RARE
}


"ensorcellation:bulwark" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:phalanx" {
    # The max level of this enchantment - originally 2.
    # Default: 10; Range: [1 ~ 127]
    I:"Max Level"=10

    # The max level of this enchantment available from loot sources.
    # Default: 2; Range: [1 ~ 127]
    I:"Max Loot Level"=2

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:soulbound" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


"ensorcellation:curse_fool" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"ensorcellation:curse_mercy" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: true
    B:Treasure=true

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: VERY_RARE
    S:Rarity=VERY_RARE
}


"forbidden_arcanus:permafrost" {
    # The max level of this enchantment - originally 1.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Level"=1

    # The max level of this enchantment available from loot sources.
    # Default: 1; Range: [1 ~ 127]
    I:"Max Loot Level"=1

    # A function to determine the max enchanting power.  The variable "x" is level.  See: https://github.com/uklimaschewski/EvalEx#usage-examples
    # Default: 
    S:"Max Power Function"=

    # A function to determine the min enchanting power.
    # Default: 
    S:"Min Power Function"=

    # If this enchantment is only available by loot sources.
    # Default: false
    B:Treasure=false

    # If this enchantment is obtainable via enchanting and enchanted loot items.
    # Default: true
    B:Discoverable=true

    # If enchanted books of this enchantment are available via loot sources.
    # Default: true
    B:Lootable=true

    # If enchanted books of this enchantment are available via villager trades.
    # Default: true
    B:Tradeable=true

    # The rarity of this enchantment.  Valid values are COMMON, UNCOMMON, RARE, and VERY_RARE.
    # Default: UNCOMMON
    S:Rarity=UNCOMMON
}


