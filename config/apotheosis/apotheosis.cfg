# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Apotheosis Module Control

# This file allows individual modules of Apotheosis to be enabled or disabled.
# Changes will have no effect until the next game restart.
# This file must match on client and server.


general {
    # If the enchantment module is enabled.
    # Default: true
    B:"Enable Enchantment Module"=true

    # If the adventure module is loaded.
    # Default: true
    B:"Enable Adventure Module"=true

    # If the spawner module is enabled.
    # Default: true
    B:"Enable Spawner Module"=true

    # If the potion module is loaded.
    # Default: true
    B:"Enable Potion Module"=true

    # If the village module is loaded.
    # Default: true
    B:"Enable Village Module"=true

    # If the garden module is loaded.
    # Default: true
    B:"Enable Garden Module"=true

    # If the Chronicle of Shadows is given to new players.
    # Default: true
    B:"Give Book on First Join"=false
}


