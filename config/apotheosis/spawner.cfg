# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Apotheosis Spawner Module Configuration

general {
    # The level of silk touch needed to harvest a spawner.  Set to -1 to disable, 0 to always drop.  The enchantment module can increase the max level of silk touch.
    # Functionally server-authoritative, but should match on client for information.
    # Default: 1; Range: [-1 ~ 127]
    I:"Spawner Silk Level"=1

    # The durability damage dealt to an item that silk touches a spawner.
    # Server-authoritative.
    # Default: 100; Range: [0 ~ 100000]
    I:"Spawner Silk Damage"=100

    # Whether to show info regarding the capturing enchantment in the JEI information for spawn eggs.
    # Default: true
    B:"Enable Capturing Enchantment JEI Info"=true
}


spawn_eggs {
    # A list of entity registry names that cannot be applied to spawners via egg.
    # Should match between client and server.
    # Default: [
    S:"Banned Mobs" <
        minecraft:ender_dragon
        minecraft:wither
        productivebees:ashy_mining_bee
        productivebees:bee_bomb
        productivebees:blue_banded_bee
        productivebees:bumble_bee
        productivebees:chocolate_mining_bee
        productivebees:collector_bee
        productivebees:configurable_bee
        productivebees:creeper_bee
        productivebees:cupid_bee
        productivebees:digger_bee
        productivebees:dye_bee
        productivebees:farmer_bee
        productivebees:green_carpenter_bee
        productivebees:hoarder_bee
        productivebees:leafcutter_bee
        productivebees:lumber_bee
        productivebees:mason_bee
        productivebees:neon_cuckoo_bee
        productivebees:nomad_bee
        productivebees:quarry_bee
        productivebees:rancher_bee
        productivebees:reed_bee
        productivebees:resin_bee
        productivebees:sweat_bee
        productivebees:yellow_black_carpenter_bee
        artifacts:mimic
        twilightforest:swarm_spider
        twilightforest:hedge_spider
     >
}


