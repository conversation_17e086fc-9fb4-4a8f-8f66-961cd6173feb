# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Apotheosis Adventure Module Config

affixes {
    # A list of type overrides for the affix loot system.  Format is <itemname>|chance|<type>.
    # Valid types are: none, sword, trident, shield, heavy_weapon, pickaxe, shovel, crossbow, bow
    # Default: [minecraft:iron_sword|sword], [minecraft:shulker_shell|none]
    S:"Equipment Type Overrides" <
        minecraft:iron_sword|sword
        minecraft:shulker_shell|none
        allthemodium:alloy_paxel|pickaxe
        industrialforegoing:infinity_hammer|none
        mythicbotany:mjoellnir|none
     >

    # The chance that a naturally spawned mob will be granted an affix item. 0 = 0%, 1 = 100%
    # Default: 0.075; Range: [0.0 ~ 1.0]
    S:"Random Affix Chance"=0.075

    # The chance that a mob will drop a gem. 0 = 0%, 1 = 100%
    # Default: 0.045; Range: [0.0 ~ 1.0]
    S:"Gem Drop Chance"=0.045

    # The flat bonus chance that bosses have to drop a gem, added to Gem Drop Chance. 0 = 0%, 1 = 100%
    # Default: 0.33; Range: [0.0 ~ 1.0]
    S:"Gem Boss Bonus"=0.33

    # If affixes that cleave can hit players (excluding the user).
    # Default: false
    B:"Cleave Players"=false

    # Loot Rules, in the form of Loot Table Matchers, permitting affix items to spawn in loot tables.
    # The format for these is domain:pattern|chance and domain is optional.  Domain is a modid, pattern is a regex string, and chance is a float 0..1 chance for the item to spawn in any matched tables.
    # If you omit the domain, the format is pattern|chance, and the matcher will run for all domains.
    # The pattern MUST be a valid regex string, and should match the paths of desired loot tables under the specified domain.  Note: "Match Any Character" is ".*" (dot star) and not "*" (star).
    # If there is a match, an item has a chance to spawn in that loot table.
    # Default: [minecraft:chests.*|0.35], [.*chests.*|0.3], [twilightforest:structures.*|0.3]
    S:"Affix Item Loot Rules" <
        minecraft:chests.*|0.35
        .*chests.*|0.3
        twilightforest:structures.*|0.3
     >

    # Loot Rules, in the form of Loot Table Matchers, permitting affixes to be added to any valid item. Here, the chance refers to the chance an item receives affixes. See comment on "Affix Item Loot Rules" for description.
    # Default: [.*blocks.*|0], [.*|0.35]
    S:"Affix Convert Loot Rules" <
        .*blocks.*|0
        .*|0.35
        minecraft:entities.witch|0
        minecraft:entities.shulker|0
     >

    # Dimensional rarities for affix conversion (see "Affix Convert Loot Rules"), in the form of dimension|min|max. A dimension not listed uses all rarities.
    # Default: [overworld|common|rare], [the_nether|uncommon|epic], [the_end|rare|mythic], [twilightforest:twilight_forest|uncommon|epic]
    S:"Affix Convert Rarities" <
        overworld|common|rare
        the_nether|uncommon|epic
        the_end|rare|mythic
        allthemodium:the_other|epic|mythic
        twilightforest:twilight_forest|uncommon|epic
     >

    # If Quark's Attribute Tooltip handling is disabled for affix items
    # Default: true
    B:"Disable Quark Tooltips for Affix Items"=true

    # The item that will be used when attempting to place torches with the torch placer affix.  Must be a valid item that places a block on right click.
    # Default: minecraft:torch
    S:"Torch Placement Item"=minecraft:torch
}


gems {
    # Loot Rules, in the form of Loot Table Matchers, permitting gems to spawn in loot tables.  See comment on "Affix Item Loot Rules" for description.
    # Default: [minecraft:chests.*|0.25], [.*chests.*|0.20], [twilightforest:structures.*|0.20]
    S:"Gem Loot Rules" <
        minecraft:chests.*|0.25
        .*chests.*|0.20
        twilightforest:structures.*|0.20
     >

    # Dimensional rarities for gem drops, in the form of dimension|min|max. A dimension not listed uses all rarities.
    # Default: [overworld|common|mythic], [the_nether|uncommon|mythic], [the_end|rare|mythic], [twilightforest:twilight_forest|uncommon|mythic]
    S:"Gem Dimensional Rarities" <
        overworld|common|mythic
        the_nether|uncommon|mythic
        the_end|rare|mythic
        allthemodium:the_other|epic|mythic
        twilightforest:twilight_forest|uncommon|mythic
     >
}


bosses {
    # If boss items are always cursed.  Enable this if you want bosses to be less overpowered by always giving them a negative effect.
    # Default: false
    B:"Curse Boss Items"=false

    # The range at which boss spawns will be announced.  If you are closer than this number of blocks (ignoring y-level), you will receive the announcement.
    # Default: 96.0; Range: [0.0 ~ 1024.0]
    S:"Boss Announce Range"=96.0

    # The volume of the boss announcement sound. 0 to disable. This control is clientside.
    # Default: 0.75; Range: [0.0 ~ 1.0]
    S:"Boss Announce Volume"=0.75

    # If the boss announcement range ignores y-level.
    # Default: false
    B:"Boss Announce Ignore Y"=false

    # The time, in ticks, that must pass between any two natural boss spawns in a single dimension.
    # Default: 3600; Range: [0 ~ 720000]
    I:"Boss Spawn Cooldown"=3600

    # If true, invading bosses will automatically target the closest player.
    # Default: false
    B:"Boss Auto-Aggro"=false

    # If true, bosses will glow when they spawn.
    # Default: true
    B:"Boss Glowing On Spawn"=true

    # Dimensions where bosses can spawn naturally, spawn chance, and spawn rules.
    # Format is dimname|chance|rule, chance is a float from 0..1.
    # Valid rules are visible here https://github.com/Shadows-of-Fire/Apotheosis/blob/1.19/src/main/java/shadows/apotheosis/adventure/boss/BossEvents.java#L174C27-L174C27
    # Default: [minecraft:overworld|0.018|NEEDS_SKY], [minecraft:the_nether|0.025|ANY], [minecraft:the_end|0.018|SURFACE_OUTER_END], [twilightforest:twilight_forest|0.05|NEEDS_SURFACE]
    S:"Boss Spawn Dimensions" <
        minecraft:overworld|0.018|NEEDS_SKY
        minecraft:the_nether|0.025|ANY
        minecraft:the_end|0.018|SURFACE_OUTER_END
        allthemodium:the_other|0.07|NEEDS_SURFACE
        twilightforest:twilight_forest|0.05|NEEDS_SURFACE
     >

    # List of sound effects to play when boss spawns are announced. This control is clientside.
    # Default: [block.end_portal.spawn]
    S:"Boss Announce Sounds" <
        block.end_portal.spawn
     >
}


worldgen {
    # The dimensions that the deadly module will generate in.
    # Default: [overworld]
    S:"Generation Dimension Whitelist" <
        overworld
     >
}


spawners {
    # The chance that a Rogue Spawner has a "valuable" chest instead of a standard one. 0 = 0%, 1 = 100%
    # Default: 0.11; Range: [0.0 ~ 1.0]
    S:"Spawner Value Chance"=0.11
}


