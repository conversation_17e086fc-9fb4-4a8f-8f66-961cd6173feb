# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Apotheosis Potion Module Configuration

general {
    I:"Knowledge XP Multiplier"=4

    # If Potion Charms will only work when in a curios slot, instead of in the inventory.
    # Default: false
    B:"Restrict Charms to Curios"=false

    # A list of effects that, when as charms, will be applied and reapplied at a longer threshold to avoid issues at low durations, like night vision.
    # Server-authoritative.
    # Default: [minecraft:night_vision], [minecraft:health_boost]
    S:"Extended Potion Charms" <
        minecraft:night_vision
        minecraft:health_boost
     >

    # A list of effects that, cannot be crafted into Potion Charms.
    # Server-authoritative.
    # Default: [
    S:"Blacklisted Potion Charm Effects" <
     >
}


