# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Apotheosis Village Module Configuration

wanderer {
    # If the generic trade list will be cleared before datapack loaded trades are added.
    # Server-authoritative.
    # Default: false
    B:"Clear Generic Trades"=false

    # If the rare trade list will be cleared before datapack loaded trades are added.
    # Server-authoritative.
    # Default: false
    B:"Clear Rare Trades"=false

    # If the Wandering Trader can attempt to spawn underground.
    # Server-authoritative.
    # Default: true
    B:"Underground Trader"=true
}


arrows {
    # If explosive arrows can break blocks.
    # Server-authoritative.
    # Default: true
    B:"Explosive Arrow Block Damage"=true
}


