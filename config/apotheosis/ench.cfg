# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Apotheosis Enchantment Module Config

client {
    # If enchanted book metadata (treasure, tradeable, etc) are shown in the tooltip.
    # Default: true
    B:"Show Enchanted Book Metadata"=true

    # The 1/n chance that a sculkshelf plays a sound, per client tick. Set to 0 to disable.
    # Default: 200; Range: [0 ~ 32767]
    I:"Sculkshelf Noise Chance"=200
}


