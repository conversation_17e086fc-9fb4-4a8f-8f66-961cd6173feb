# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# 

entity {
    # A list of full names, which are used in the generation of boss names. May be empty only if name parts is not empty.
    # Default: [<PERSON><PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON>], [Doom<PERSON><PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON>TheDrunk], [<PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON><PERSON><PERSON><PERSON>], [Is<PERSON>hed<PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON><PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [<PERSON>], [Edson], [Elton], [Eddison], [Earl], [Eric], [Ericson], [Eddie], [Ediovany], [Emma], [Elizabeth], [Eliza], [Esperanza], [Esper], [Esmeralda], [Emi], [Emily], [Elaine], [Fernando], [Ferdinand], [Fred], [Feddie], [Fredward], [Frank], [Franklin], [Felix], [Felicia], [Fran], [Greg], [Gregory], [George], [Gerald], [Gina], [Geraldine], [Gabby], [Hendrix], [Henry], [Hobbes], [Herbert], [Heath], [Henderson], [Helga], [Hera], [Helen], [Helena], [Hannah], [Ike], [Issac], [Israel], [Ismael], [Irlanda], [Isabelle], [Irene], [Irenia], [Jimmy], [Jim], [Justin], [Jacob], [Jake], [Jon], [Johnson], [Jonny], [Jonathan], [Josh], [Joshua], [Julian], [Jesus], [Jericho], [Jeb], [Jess], [Joan], [Jill], [Jillian], [Jessica], [Jennifer], [Jenny], [Jen], [Judy], [Kenneth], [Kenny], [Ken], [Keith], [Kevin], [Karen], [Kassandra], [Kassie], [Leonard], [Leo], [Leroy], [Lee], [Lenny], [Luke], [Lucas], [Liam], [Lorraine], [Latasha], [Lauren], [Laquisha], [Livia], [Lydia], [Lila], [Lilly], [Lillian], [Lilith], [Lana], [Mason], [Mike], [Mickey], [Mario], [Manny], [Mark], [Marcus], [Martin], [Marty], [Matthew], [Matt], [Max], [Maximillian], [Marth], [Mia], [Marriah], [Maddison], [Maddie], [Marissa], [Miranda], [Mary], [Martha], [Melonie], [Melody], [Mel], [Minnie], [Nathan], [Nathaniel], [Nate], [Ned], [Nick], [Norman], [Nicholas], [Natasha], [Nicki], [Nora], [Nelly], [Nina], [Orville], [Oliver], [Orlando], [Owen], [Olsen], [Odin], [Olaf], [Ortega], [Olivia], [Patrick], [Pat], [Paul], [Perry], [Pinnochio], [Patrice], [Patricia], [Pennie], [Petunia], [Patti], [Pernelle], [Quade], [Quincy], [Quentin], [Quinn], [Roberto], [Robbie], [Rob], [Robert], [Roy], [Roland], [Ronald], [Richard], [Rick], [Ricky], [Rose], [Rosa], [Rhonda], [Rebecca], [Roberta], [Sparky], [Shiloh], [Stephen], [Steve], [Saul], [Sheen], [Shane], [Sean], [Sampson], [Samuel], [Sammy], [Stefan], [Sasha], [Sam], [Susan], [Suzy], [Shelby], [Samantha], [Sheila], [Sharon], [Sally], [Stephanie], [Sandra], [Sandy], [Sage], [Tim], [Thomas], [Thompson], [Tyson], [Tyler], [Tom], [Tyrone], [Timmothy], [Tamara], [Tabby], [Tabitha], [Tessa], [Tiara], [Tyra], [Uriel], [Ursala], [Uma], [Victor], [Vincent], [Vince], [Vance], [Vinny], [Velma], [Victoria], [Veronica], [Wilson], [Wally], [Wallace], [Will], [Wilard], [William], [Wilhelm], [Xavier], [Xandra], [Young], [Yvonne], [Yolanda], [Zach], [Zachary]
    S:Names <
        Prim
        Tyrael
        Bajorno
        Michael Morbius
        Morbius
        Arun
        Panez
        Doomsday
        Vanamar
        WhatTheDrunk
        Lothrazar
        Chelly
        Chelicia
        Darsh
        Dariush
        Cheese E Piloza
        Bing
        Royal
        NoWayHere
        SwankyStella
        Isosahedron
        Asfalis
        Biz
        Icicle
        Darko
        Shadows
        Katarina
        Faellynna
        Diliviel
        Jank
        Albert
        Andrew
        Anderson
        Andy
        Allan
        Arthur
        Aaron
        Allison
        Arielle
        Amanda
        Anne
        Annie
        Amy
        Alana
        Brandon
        Brady
        Bernard
        Ben
        Benjamin
        Bob
        Bobette
        Brooke
        Brandy
        Beatrice
        Bea
        Bella
        Becky
        Carlton
        Carl
        Calvin
        Cameron
        Carson
        Chase
        Cassandra
        Cassie
        Cas
        Carol
        Carly
        Cherise
        Charlotte
        Cheryl
        Chasity
        Danny
        Drake
        Daniel
        Derrel
        David
        Dave
        Donovan
        Don
        Donald
        Drew
        Derrick
        Darla
        Donna
        Dora
        Danielle
        Edward
        Elliot
        Ed
        Edson
        Elton
        Eddison
        Earl
        Eric
        Ericson
        Eddie
        Ediovany
        Emma
        Elizabeth
        Eliza
        Esperanza
        Esper
        Esmeralda
        Emi
        Emily
        Elaine
        Fernando
        Ferdinand
        Fred
        Feddie
        Fredward
        Frank
        Franklin
        Felix
        Felicia
        Fran
        Greg
        Gregory
        George
        Gerald
        Gina
        Geraldine
        Gabby
        Hendrix
        Henry
        Hobbes
        Herbert
        Heath
        Henderson
        Helga
        Hera
        Helen
        Helena
        Hannah
        Ike
        Issac
        Israel
        Ismael
        Irlanda
        Isabelle
        Irene
        Irenia
        Jimmy
        Jim
        Justin
        Jacob
        Jake
        Jon
        Johnson
        Jonny
        Jonathan
        Josh
        Joshua
        Julian
        Jesus
        Jericho
        Jeb
        Jess
        Joan
        Jill
        Jillian
        Jessica
        Jennifer
        Jenny
        Jen
        Judy
        Kenneth
        Kenny
        Ken
        Keith
        Kevin
        Karen
        Kassandra
        Kassie
        Leonard
        Leo
        Leroy
        Lee
        Lenny
        Luke
        Lucas
        Liam
        Lorraine
        Latasha
        Lauren
        Laquisha
        Livia
        Lydia
        Lila
        Lilly
        Lillian
        Lilith
        Lana
        Mason
        Mike
        Mickey
        Mario
        Manny
        Mark
        Marcus
        Martin
        Marty
        Matthew
        Matt
        Max
        Maximillian
        Marth
        Mia
        Marriah
        Maddison
        Maddie
        Marissa
        Miranda
        Mary
        Martha
        Melonie
        Melody
        Mel
        Minnie
        Nathan
        Nathaniel
        Nate
        Ned
        Nick
        Norman
        Nicholas
        Natasha
        Nicki
        Nora
        Nelly
        Nina
        Orville
        Oliver
        Orlando
        Owen
        Olsen
        Odin
        Olaf
        Ortega
        Olivia
        Patrick
        Pat
        Paul
        Perry
        Pinnochio
        Patrice
        Patricia
        Pennie
        Petunia
        Patti
        Pernelle
        Quade
        Quincy
        Quentin
        Quinn
        Roberto
        Robbie
        Rob
        Robert
        Roy
        Roland
        Ronald
        Richard
        Rick
        Ricky
        Rose
        Rosa
        Rhonda
        Rebecca
        Roberta
        Sparky
        Shiloh
        Stephen
        Steve
        Saul
        Sheen
        Shane
        Sean
        Sampson
        Samuel
        Sammy
        Stefan
        Sasha
        Sam
        Susan
        Suzy
        Shelby
        Samantha
        Sheila
        Sharon
        Sally
        Stephanie
        Sandra
        Sandy
        Sage
        Tim
        Thomas
        Thompson
        Tyson
        Tyler
        Tom
        Tyrone
        Timmothy
        Tamara
        Tabby
        Tabitha
        Tessa
        Tiara
        Tyra
        Uriel
        Ursala
        Uma
        Victor
        Vincent
        Vince
        Vance
        Vinny
        Velma
        Victoria
        Veronica
        Wilson
        Wally
        Wallace
        Will
        Wilard
        William
        Wilhelm
        Xavier
        Xandra
        Young
        Yvonne
        Yolanda
        Zach
        Zachary
     >

    # A list of name pieces, which can be spliced together to create full names.  May be empty only if names is not empty.
    # Default: [Prim], [Morb], [Ius], [Kat], [Chel], [Bing], [Darsh], [Jank], [Dark], [Osto], [Grab], [Thar], [Ger], [Ald], [Mas], [On], [O], [Din], [Thor], [Jon], [Ath], [Burb], [En], [A], [E], [I], [U], [Hab], [Bloo], [Ena], [Dit], [Aph], [Ern], [Bor], [Dav], [Id], [Toast], [Son], [For], [Wen], [Lob], [Van], [Zap], [Ear], [Ben], [Don], [Bran], [Gro], [Jen], [Bob], [Ette], [Ere], [Man], [Qua], [Bro], [Cree], [Per], [Skel], [Ton], [Zom], [Bie], [Wolf], [End], [Er], [Pig], [Sil], [Ver], [Fish], [Cow], [Chic], [Ken], [Sheep], [Squid], [Hell]
    S:"Name Parts" <
        Prim
        Morb
        Ius
        Kat
        Chel
        Bing
        Darsh
        Jank
        Dark
        Osto
        Grab
        Thar
        Ger
        Ald
        Mas
        On
        O
        Din
        Thor
        Jon
        Ath
        Burb
        En
        A
        E
        I
        U
        Hab
        Bloo
        Ena
        Dit
        Aph
        Ern
        Bor
        Dav
        Id
        Toast
        Son
        For
        Wen
        Lob
        Van
        Zap
        Ear
        Ben
        Don
        Bran
        Gro
        Jen
        Bob
        Ette
        Ere
        Man
        Qua
        Bro
        Cree
        Per
        Skel
        Ton
        Zom
        Bie
        Wolf
        End
        Er
        Pig
        Sil
        Ver
        Fish
        Cow
        Chic
        Ken
        Sheep
        Squid
        Hell
     >

    # A list of prefixes, which are used in the generation of boss names. May be empty.
    # Default: [Dr. Michael], [Sir], [Mister], [Madam], [Doctor], [Father], [Mother], [Poppa], [Lord], [Lady], [Overseer], [Professor], [Mr.], [Mr. President], [Duke], [Duchess], [Dame], [The Honorable], [Chancellor], [Vice-Chancellor], [His Holiness], [Reverend], [Count], [Viscount], [Earl], [Captain], [Major], [General], [Senpai]
    S:Prefixes <
        Dr. Michael
        Sir
        Mister
        Madam
        Doctor
        Father
        Mother
        Poppa
        Lord
        Lady
        Overseer
        Professor
        Mr.
        Mr. President
        Duke
        Duchess
        Dame
        The Honorable
        Chancellor
        Vice-Chancellor
        His Holiness
        Reverend
        Count
        Viscount
        Earl
        Captain
        Major
        General
        Senpai
     >

    # A list of suffixes, which are used in the generation of boss names. A suffix is always preceeded by "The". May be empty.
    # Default: [Morbius], [Dragonborn], [Rejected], [Mighty], [Supreme], [Superior], [Ultimate], [Lame], [Wimpy], [Curious], [Sneaky], [Pathetic], [Crying], [Eagle], [Errant], [Unholy], [Questionable], [Mean], [Hungry], [Thirsty], [Feeble], [Wise], [Sage], [Magical], [Mythical], [Legendary], [Not Very Nice], [Jerk], [Doctor], [Misunderstood], [Angry], [Knight], [Bishop], [Godly], [Special], [Toasty], [Shiny], [Shimmering], [Light], [Dark], [Odd-Smelling], [Funky], [Rock Smasher], [Son of Herobrine], [Cracked], [Sticky], [§kAlien§r], [Baby], [Manly], [Rough], [Scary], [Undoubtable], [Honest], [Non-Suspicious], [Boring], [Odd], [Lazy], [Super], [Nifty], [Ogre Slayer], [Pig Thief], [Dirt Digger], [Really Cool], [Doominator], [... Something], [Extra-Fishy], [Gorilla Slaughterer], [Marbles Winner], [AC Rizzlord], [President], [Burger Chef], [Professional Animator], [Cheese Sprayer], [Happiness Advocate], [Ghost Hunter], [Head of Potatoes], [Ninja], [Warrior], [Pyromancer]
    S:Suffixes <
        Morbius
        Dragonborn
        Rejected
        Mighty
        Supreme
        Superior
        Ultimate
        Lame
        Wimpy
        Curious
        Sneaky
        Pathetic
        Crying
        Eagle
        Errant
        Unholy
        Questionable
        Mean
        Hungry
        Thirsty
        Feeble
        Wise
        Sage
        Magical
        Mythical
        Legendary
        Not Very Nice
        Jerk
        Doctor
        Misunderstood
        Angry
        Knight
        Bishop
        Godly
        Special
        Toasty
        Shiny
        Shimmering
        Light
        Dark
        Odd-Smelling
        Funky
        Rock Smasher
        Son of Herobrine
        Cracked
        Sticky
        §kAlien§r
        Baby
        Manly
        Rough
        Scary
        Undoubtable
        Honest
        Non-Suspicious
        Boring
        Odd
        Lazy
        Super
        Nifty
        Ogre Slayer
        Pig Thief
        Dirt Digger
        Really Cool
        Doominator
        ... Something
        Extra-Fishy
        Gorilla Slaughterer
        Marbles Winner
        AC Rizzlord
        President
        Burger Chef
        Professional Animator
        Cheese Sprayer
        Happiness Advocate
        Ghost Hunter
        Head of Potatoes
        Ninja
        Warrior
        Pyromancer
     >
}


items {
    # A list of root names for helms, used in the generation of item names. May not be empty.
    # Default: [Helmet], [Cap], [Crown], [Great Helm], [Bassinet], [Sallet], [Close Helm], [Barbute]
    S:Helms <
        Helmet
        Cap
        Crown
        Great Helm
        Bassinet
        Sallet
        Close Helm
        Barbute
     >

    # A list of root names for chestplates, used in the generation of item names. May not be empty.
    # Default: [Chestplate], [Tunic], [Brigandine], [Hauberk], [Cuirass]
    S:chestplates <
        Chestplate
        Tunic
        Brigandine
        Hauberk
        Cuirass
     >

    # A list of root names for leggings, used in the generation of item names. May not be empty.
    # Default: [Leggings], [Pants], [Tassets], [Cuisses], [Schynbalds]
    S:leggings <
        Leggings
        Pants
        Tassets
        Cuisses
        Schynbalds
     >

    # A list of root names for boots, used in the generation of item names. May not be empty.
    # Default: [Boots], [Shoes], [Greaves], [Sabatons], [Sollerets]
    S:boots <
        Boots
        Shoes
        Greaves
        Sabatons
        Sollerets
     >

    # A list of root names for swords, used in the generation of item names. May not be empty.
    # Default: [Sword], [Cutter], [Slicer], [Dicer], [Knife], [Blade], [Machete], [Brand], [Claymore], [Cutlass], [Foil], [Dagger], [Glaive], [Rapier], [Saber], [Scimitar], [Shortsword], [Longsword], [Broadsword], [Calibur]
    S:swords <
        Sword
        Cutter
        Slicer
        Dicer
        Knife
        Blade
        Machete
        Brand
        Claymore
        Cutlass
        Foil
        Dagger
        Glaive
        Rapier
        Saber
        Scimitar
        Shortsword
        Longsword
        Broadsword
        Calibur
     >

    # A list of root names for axes, used in the generation of item names. May not be empty.
    # Default: [Axe], [Chopper], [Hatchet], [Tomahawk], [Cleaver], [Hacker], [Tree-Cutter], [Truncator]
    S:axes <
        Axe
        Chopper
        Hatchet
        Tomahawk
        Cleaver
        Hacker
        Tree-Cutter
        Truncator
     >

    # A list of root names for pickaxes, used in the generation of item names. May not be empty.
    # Default: [Pickaxe], [Pick], [Mattock], [Rock-Smasher], [Miner]
    S:pickaxes <
        Pickaxe
        Pick
        Mattock
        Rock-Smasher
        Miner
     >

    # A list of root names for shovels, used in the generation of item names. May not be empty.
    # Default: [Shovel], [Spade], [Digger], [Excavator], [Trowel], [Scoop]
    S:shovels <
        Shovel
        Spade
        Digger
        Excavator
        Trowel
        Scoop
     >

    # A list of root names for bows, used in the generation of item names. May not be empty.
    # Default: [Bow], [Shortbow], [Longbow], [Flatbow], [Recurve Bow], [Reflex Bow], [Self Bow], [Composite Bow], [Arrow-Flinger]
    S:bows <
        Bow
        Shortbow
        Longbow
        Flatbow
        Recurve Bow
        Reflex Bow
        Self Bow
        Composite Bow
        Arrow-Flinger
     >

    # A list of root names for shields, used in the generation of item names. May not be empty.
    # Default: [Shield], [Buckler], [Targe], [Greatshield], [Blockade], [Bulwark], [Tower Shield], [Protector], [Aegis]
    S:shields <
        Shield
        Buckler
        Targe
        Greatshield
        Blockade
        Bulwark
        Tower Shield
        Protector
        Aegis
     >
}


tools {
    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ae2:nether_quartz_axe, ae2:nether_quartz_hoe, ae2:nether_quartz_shovel, ae2:nether_quartz_pickaxe, ae2:nether_quartz_sword
    # 
    # Default: [
    S:ae2_nether_quartz_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:soulium_dagger, mysticalagriculture:passive_soulium_dagger, mysticalagriculture:hostile_soulium_dagger, mysticalagriculture:creative_soulium_dagger
    # 
    # Default: [
    S:SOULIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagradditions:tertium_paxel
    # 
    # Default: [
    S:TERTIUM <
     >
    S:gtceu_steel_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: enderio:dark_steel_sword
    # 
    # Default: [
    S:enderio_dark_steel_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:awakened_supremium_sword, mysticalagriculture:awakened_supremium_pickaxe, mysticalagriculture:awakened_supremium_shovel, mysticalagriculture:awakened_supremium_axe, mysticalagriculture:awakened_supremium_hoe, mysticalagriculture:awakened_supremium_sickle, mysticalagriculture:awakened_supremium_scythe
    # 
    # Default: [
    S:AWAKENED_SUPREMIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:horizonite_sword, blue_skies:horizonite_pickaxe, blue_skies:horizonite_axe, blue_skies:horizonite_shovel, blue_skies:horizonite_hoe
    # 
    # Default: [
    S:HORIZONITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:magehunter
    # 
    # Default: [
    S:METAL_MAGEHUNTER <
     >
    S:create_sa_brass_pickaxe <
     >
    S:gtceu_duranium_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:bluebright_sword, blue_skies:bluebright_pickaxe, blue_skies:bluebright_axe, blue_skies:bluebright_shovel, blue_skies:bluebright_hoe, blue_skies:lunar_sword, blue_skies:lunar_pickaxe, blue_skies:lunar_axe, blue_skies:lunar_shovel, blue_skies:lunar_hoe, blue_skies:starlit_sword, blue_skies:starlit_pickaxe, blue_skies:starlit_axe, blue_skies:starlit_shovel, blue_skies:starlit_hoe, blue_skies:dusk_sword, blue_skies:dusk_pickaxe, blue_skies:dusk_axe, blue_skies:dusk_shovel, blue_skies:dusk_hoe, blue_skies:frostbright_sword, blue_skies:frostbright_pickaxe, blue_skies:frostbright_axe, blue_skies:frostbright_shovel, blue_skies:frostbright_hoe, blue_skies:maple_sword, blue_skies:maple_pickaxe, blue_skies:maple_axe, blue_skies:maple_shovel, blue_skies:maple_hoe
    # 
    # Default: [
    S:WOOD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: farmersdelight:flint_knife
    # 
    # Default: [
    S:farmersdelight_flint_knife <
     >
    S:create_sa_experience_sword <
     >
    S:create_sa_zinc_pickaxe <
     >
    S:create_sa_brass_shovel <
     >
    S:create_sa_copper_shovel <
     >
    S:create_sa_blazing_axe <
     >
    S:twilightforest_knightmetal_sword <
     >
    S:gtceu_neutronium_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: occultism:infused_pickaxe
    # 
    # Default: [
    S:SPIRIT_ATTUNED_GEM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: wstweaks:lava_blade, wstweaks:blaze_blade
    # 
    # Default: [
    S:wstweaks_lava_blade <
     >
    S:create_sa_blazing_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:lunar_stone_sword, blue_skies:lunar_stone_pickaxe, blue_skies:lunar_stone_axe, blue_skies:lunar_stone_shovel, blue_skies:lunar_stone_hoe
    # 
    # Default: [
    S:LUNAR_STONE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:iron_sword, minecraft:iron_shovel, minecraft:iron_pickaxe, minecraft:iron_axe, minecraft:iron_hoe, evilcraft:spikey_claws, cfm:spatula, occultism:butcher_knife, platforms:wrench, endermanoverhaul:corrupted_blade, minecolonies:iron_scimitar, railcraft:iron_tunnel_bore_head, railcraft:iron_spike_maul, railcraft:iron_crowbar, aquaculture:iron_fillet_knife, farmersdelight:iron_knife, mekanismtools:iron_paxel, mahoutsukai:caliburn, mahoutsukai:clarent, mahoutsukai:morgan, mahoutsukai:rule_breaker, forbidden_arcanus:iron_blacksmith_gavel, nethersdelight:iron_machete, delightful:veridium_knife, delightful:infused_veridium_knife, delightful:skyjade_knife, delightful:stratus_knife
    # 
    # Default: [
    S:IRON <
     >
    S:create_sa_brass_sword <
     >
    S:create_sa_experience_pickaxe <
     >
    S:gtceu_damascus_steel_sword <
     >
    S:create_sa_experience_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:ice_sword
    # 
    # Default: [
    S:twilightforest_ice_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagradditions:inferium_paxel
    # 
    # Default: [
    S:INFERIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthemodium:vibranium_sword, allthemodium:vibranium_pickaxe, allthemodium:vibranium_axe, allthemodium:vibranium_shovel, allthemodium:vibranium_hoe
    # 
    # Default: [
    S:allthemodium_vibranium_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:diamond_sword, minecraft:diamond_shovel, minecraft:diamond_pickaxe, minecraft:diamond_axe, minecraft:diamond_hoe, evilcraft:vengeance_pickaxe, occultism:iesnium_pickaxe, mysticalagriculture:diamond_sickle, mysticalagriculture:diamond_scythe, minecolonies:chiefsword, irons_spellbooks:spellbreaker, railcraft:diamond_tunnel_bore_head, railcraft:diamond_spike_maul, railcraft:diamond_crowbar, railcraft:seasons_crowbar, aquaculture:diamond_fillet_knife, twilightforest:diamond_minotaur_axe, twilightforest:mazebreaker_pickaxe, mob_grinding_utils:null_sword, farmersdelight:diamond_knife, integratedtunnels:dummy_pickaxe, mekanismtools:diamond_paxel, rftoolsbuilder:superharvestingtool, forbidden_arcanus:diamond_blacksmith_gavel, nethersdelight:diamond_machete
    # 
    # Default: [
    S:DIAMOND <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagradditions:imperium_paxel
    # 
    # Default: [
    S:IMPERIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:polyethylene_mallet, gtceu:polyethylene_plunger
    # 
    # Default: [
    S:gtceu_polyethylene_mallet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:bronze_knife
    # 
    # Default: [
    S:BRONZE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:forgotten_knife
    # 
    # Default: [
    S:FORGOTTEN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:debug_sword
    # 
    # Default: [
    S:DEVELOPER <
     >
    S:create_sa_zinc_sword <
     >
    S:gtceu_invar_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:glass_sword
    # 
    # Default: [
    S:twilightforest_glass_sword <
     >
    S:create_sa_blazing_cleaver <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:pyrope_sword, blue_skies:pyrope_pickaxe, blue_skies:pyrope_axe, blue_skies:pyrope_shovel, blue_skies:pyrope_hoe
    # 
    # Default: [
    S:PYROPE <
     >
    S:rootsclassic_engraved_blade <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:different_sword
    # 
    # Default: [
    S:DIFFERENT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: naturesaura:sky_pickaxe, naturesaura:sky_axe, naturesaura:sky_shovel, naturesaura:sky_hoe, naturesaura:sky_sword
    # 
    # Default: [
    S:SKY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: undergarden:cloggrum_battleaxe, undergarden:cloggrum_sword, undergarden:cloggrum_pickaxe, undergarden:cloggrum_axe, undergarden:cloggrum_shovel, undergarden:cloggrum_hoe
    # 
    # Default: [
    S:CLOGGRUM <
     >
    S:gtceu_stainless_steel_sword <
     >
    S:create_sa_experience_shovel <
     >
    S:twilightforest_ironwood_sword <
     >
    S:create_sa_copper_axe <
     >
    S:gtceu_hsse_sword <
     >
    S:create_sa_copper_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:diopside_sword, blue_skies:diopside_pickaxe, blue_skies:diopside_axe, blue_skies:diopside_shovel, blue_skies:diopside_hoe
    # 
    # Default: [
    S:DIOPSIDE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:polytetrafluoroethylene_mallet, gtceu:polytetrafluoroethylene_plunger
    # 
    # Default: [
    S:gtceu_polytetrafluoroethylene_mallet <
     >
    S:gtceu_rose_gold_sword <
     >
    S:rootsclassic_living_sword <
     >
    S:gtceu_cobalt_brass_sword <
     >
    S:gtceu_wrought_iron_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:netherite_sword, minecraft:netherite_shovel, minecraft:netherite_pickaxe, minecraft:netherite_axe, minecraft:netherite_hoe, bhc:blade_of_vitality, ars_nouveau:enchanters_sword, farmersdelight:netherite_knife, mekanismtools:netherite_paxel, forbidden_arcanus:netherite_blacksmith_gavel, nethersdelight:netherite_machete
    # 
    # Default: [
    S:NETHERITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:polybenzimidazole_mallet, gtceu:polybenzimidazole_plunger
    # 
    # Default: [
    S:gtceu_polybenzimidazole_mallet <
     >
    S:gtceu_tungsten_steel_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:turquoise_stone_sword, blue_skies:turquoise_stone_pickaxe, blue_skies:turquoise_stone_axe, blue_skies:turquoise_stone_shovel, blue_skies:turquoise_stone_hoe
    # 
    # Default: [
    S:TURQUOISE_STONE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ae2:fluix_axe, ae2:fluix_hoe, ae2:fluix_shovel, ae2:fluix_pickaxe, ae2:fluix_sword
    # 
    # Default: [
    S:ae2_fluix_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ends_delight:dragon_egg_shell_knife
    # 
    # Default: [
    S:ends_delight_dragon_egg_shell_knife <
     >
    S:gtceu_blue_steel_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ends_delight:end_stone_knife
    # 
    # Default: [
    S:ends_delight_end_stone_knife <
     >
    S:create_sa_rose_quartz_pickaxe <
     >
    S:gtceu_bronze_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ae2:certus_quartz_axe, ae2:certus_quartz_hoe, ae2:certus_quartz_shovel, ae2:certus_quartz_pickaxe, ae2:certus_quartz_sword
    # 
    # Default: [
    S:ae2_certus_quartz_axe <
     >
    S:create_sa_copper_hoe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:froststeel_knife
    # 
    # Default: [
    S:FROSTSTEEL <
     >
    S:create_sa_brass_axe <
     >
    S:gtceu_ultimet_sword <
     >
    S:create_sa_portable_drill <
     >
    S:create_sa_copper_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:aquite_sword, blue_skies:aquite_pickaxe, blue_skies:aquite_axe, blue_skies:aquite_shovel, blue_skies:aquite_hoe
    # 
    # Default: [
    S:AQUITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthemodium:unobtainium_sword, allthemodium:unobtainium_pickaxe, allthemodium:unobtainium_axe, allthemodium:unobtainium_shovel, allthemodium:unobtainium_hoe
    # 
    # Default: [
    S:allthemodium_unobtainium_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagradditions:supremium_paxel
    # 
    # Default: [
    S:SUPREMIUM <
     >
    S:create_sa_rose_quartz_axe <
     >
    S:create_sa_zinc_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:stone_sword, minecraft:stone_shovel, minecraft:stone_pickaxe, minecraft:stone_axe, minecraft:stone_hoe, cataclysm:athame, aquaculture:stone_fillet_knife, mekanismtools:stone_paxel, forbidden_arcanus:stone_blacksmith_gavel
    # 
    # Default: [
    S:STONE <
     >
    S:gtceu_iron_sword <
     >
    S:gtceu_vanadium_steel_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagradditions:prudentium_paxel
    # 
    # Default: [
    S:PRUDENTIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railcraft:steel_sword, railcraft:steel_shovel, railcraft:steel_pickaxe, railcraft:steel_axe, railcraft:steel_hoe, railcraft:steel_tunnel_bore_head, railcraft:steel_spike_maul, railcraft:steel_crowbar
    # 
    # Default: [
    S:STEEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:final_fractal
    # 
    # Default: [
    S:TOOL_WITHERITE <
     >
    S:twilightforest_fiery_sword <
     >
    S:create_sa_rose_quartz_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:golden_sword, minecraft:golden_shovel, minecraft:golden_pickaxe, minecraft:golden_axe, minecraft:golden_hoe, evilcraft:vein_sword, reliquary:mercy_cross, reliquary:magicbane, aquaculture:gold_fillet_knife, twilightforest:gold_minotaur_axe, farmersdelight:golden_knife, mekanismtools:gold_paxel, forbidden_arcanus:golden_blacksmith_gavel, nethersdelight:golden_machete
    # 
    # Default: [
    S:GOLD <
     >
    S:gtceu_tungsten_carbide_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthemodium:allthemodium_sword, allthemodium:allthemodium_pickaxe, allthemodium:allthemodium_axe, allthemodium:allthemodium_shovel, allthemodium:allthemodium_hoe
    # 
    # Default: [
    S:allthemodium_allthemodium_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: deeperdarker:warden_shovel, deeperdarker:warden_pickaxe, deeperdarker:warden_axe, deeperdarker:warden_hoe, deeperdarker:warden_sword
    # 
    # Default: [
    S:WARDEN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:keeper_flamberge
    # 
    # Default: [
    S:KEEPER_FLAMBERGE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:giant_pickaxe, twilightforest:giant_sword
    # 
    # Default: [
    S:twilightforest_giant_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:claymore
    # 
    # Default: [
    S:CLAYMORE <
     >
    S:create_sa_brass_hoe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:comet_sword, blue_skies:comet_pickaxe, blue_skies:comet_axe, blue_skies:comet_shovel, blue_skies:comet_hoe
    # 
    # Default: [
    S:COMET <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: naturesaura:depth_pickaxe, naturesaura:depth_axe, naturesaura:depth_shovel, naturesaura:depth_hoe, naturesaura:depth_sword
    # 
    # Default: [
    S:DEPTH <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: bloodmagic:soulsword, bloodmagic:soulaxe, bloodmagic:soulpickaxe, bloodmagic:soulshovel, bloodmagic:soulscythe
    # 
    # Default: [
    S:SENTIENT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aquaculture:neptunium_pickaxe, aquaculture:neptunium_shovel, aquaculture:neptunium_axe, aquaculture:neptunium_hoe, aquaculture:neptunium_sword, aquaculture:neptunium_fillet_knife
    # 
    # Default: [
    S:aquaculture_neptunium_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:zweiender
    # 
    # Default: [
    S:TOOL_ENDERITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:wood_mallet
    # 
    # Default: [
    S:gtceu_wood_mallet <
     >
    S:create_sa_blazing_shovel <
     >
    S:gtceu_naquadah_alloy_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:infused_arc_sword
    # 
    # Default: [
    S:INFUSED <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightdelight:teardrop_sword
    # 
    # Default: [
    S:twilightdelight_teardrop_sword <
     >
    S:gtceu_aluminium_sword <
     >
    S:twilightforest_steeleaf_sword <
     >
    S:create_sa_rose_quartz_shovel <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ends_delight:dragon_tooth_knife
    # 
    # Default: [
    S:ends_delight_dragon_tooth_knife <
     >
    S:gtceu_red_steel_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:charoite_sword, blue_skies:charoite_pickaxe, blue_skies:charoite_axe, blue_skies:charoite_shovel, blue_skies:charoite_hoe
    # 
    # Default: [
    S:CHAROITE <
     >
    S:gtceu_sterling_silver_sword <
     >
    S:gtceu_diamond_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:flint_mortar, gtceu:flint_pickaxe, gtceu:flint_shovel, gtceu:flint_axe, gtceu:flint_sword, gtceu:flint_knife, gtceu:flint_hoe
    # 
    # Default: [
    S:gtceu_flint_mortar <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ends_delight:purpur_knife
    # 
    # Default: [
    S:ends_delight_purpur_knife <
     >
    S:create_sa_zinc_hoe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:rubber_mallet, gtceu:rubber_plunger
    # 
    # Default: [
    S:gtceu_rubber_mallet <
     >
    S:gtceu_titanium_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthemodium:alloy_sword, allthemodium:alloy_axe, allthemodium:alloy_pick, allthemodium:alloy_shovel, allthemodium:alloy_paxel
    # 
    # Default: [
    S:allthemodium_alloy_sword <
     >
    S:create_sa_zinc_shovel <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:utherium_knife
    # 
    # Default: [
    S:UTHERIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:rose_gold_knife
    # 
    # Default: [
    S:ROSE_GOLD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: forbidden_arcanus:draco_arcanus_sword, forbidden_arcanus:draco_arcanus_shovel, forbidden_arcanus:draco_arcanus_pickaxe, forbidden_arcanus:draco_arcanus_axe, forbidden_arcanus:draco_arcanus_hoe
    # 
    # Default: [
    S:DRACO_ARCANUS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:osmium_pickaxe, mekanismtools:osmium_axe, mekanismtools:osmium_shovel, mekanismtools:osmium_hoe, mekanismtools:osmium_sword, mekanismtools:osmium_paxel
    # 
    # Default: [
    S:mekanismtools_osmium_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:steel_pickaxe, mekanismtools:steel_axe, mekanismtools:steel_shovel, mekanismtools:steel_hoe, mekanismtools:steel_sword, mekanismtools:steel_paxel
    # 
    # Default: [
    S:mekanismtools_steel_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:reaper_scythe, eidolon:cleaving_axe, eidolon:athame
    # 
    # Default: [
    S:eidolon_reaper_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:bronze_pickaxe, mekanismtools:bronze_axe, mekanismtools:bronze_shovel, mekanismtools:bronze_hoe, mekanismtools:bronze_sword, mekanismtools:bronze_paxel
    # 
    # Default: [
    S:mekanismtools_bronze_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:vampire_blade
    # 
    # Default: [
    S:VAMPIRE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: redstone_arsenal:flux_sword, redstone_arsenal:flux_shovel, redstone_arsenal:flux_pickaxe, redstone_arsenal:flux_axe, redstone_arsenal:flux_sickle, redstone_arsenal:flux_excavator, redstone_arsenal:flux_hammer
    # 
    # Default: [
    S:redstone_arsenal_flux_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:elementium_aiot
    # 
    # Default: [
    S:ELEMENTIUM_AIOT_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: draconicevolution:wyvern_shovel, draconicevolution:wyvern_hoe, draconicevolution:wyvern_pickaxe, draconicevolution:wyvern_axe, draconicevolution:wyvern_sword
    # 
    # Default: [
    S:draconicevolution_wyvern_shovel <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:fiery_knife
    # 
    # Default: [
    S:FIERY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:signalum_axe, thermal_extra:signalum_pickaxe, thermal_extra:signalum_hoe, thermal_extra:signalum_shovel, thermal_extra:signalum_sword, thermal_extra:signalum_excavator, thermal_extra:signalum_hammer, thermal_extra:signalum_knife, thermal_extra:signalum_sickle
    # 
    # Default: [
    S:thermal_extra_signalum_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:leaf_knife
    # 
    # Default: [
    S:LEAF <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:titanium_scythe, gtceu:titanium_hammer, gtceu:iv_titanium_drill, gtceu:ev_titanium_drill, gtceu:lv_titanium_chainsaw, gtceu:titanium_pickaxe, gtceu:hv_titanium_drill, gtceu:titanium_shovel, gtceu:titanium_buzzsaw, gtceu:titanium_axe, gtceu:titanium_file, gtceu:iv_titanium_wire_cutter, gtceu:lv_titanium_drill, gtceu:mv_titanium_drill, gtceu:lv_titanium_wire_cutter, gtceu:hv_titanium_wire_cutter, gtceu:titanium_wire_cutter, gtceu:lv_titanium_wrench, gtceu:titanium_mining_hammer, gtceu:titanium_saw, gtceu:titanium_screwdriver, gtceu:iv_titanium_wrench, gtceu:titanium_spade, gtceu:titanium_wrench, gtceu:lv_titanium_screwdriver, gtceu:titanium_sword, gtceu:titanium_knife, gtceu:hv_titanium_wrench, gtceu:titanium_butchery_knife, gtceu:titanium_crowbar, gtceu:titanium_hoe
    # 
    # Default: [
    S:gtceu_titanium_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:valkyrie_pickaxe, aether:valkyrie_axe, aether:valkyrie_shovel, aether:valkyrie_hoe, aether:valkyrie_lance
    # 
    # Default: [
    S:VALKYRIE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:lapis_lazuli_knife
    # 
    # Default: [
    S:LAPIS_LAZULI <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:black_steel_sword, cataclysm:black_steel_shovel, cataclysm:black_steel_pickaxe, cataclysm:black_steel_axe, cataclysm:black_steel_hoe
    # 
    # Default: [
    S:cataclysm_black_steel_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:styrene_butadiene_rubber_mallet, gtceu:styrene_butadiene_rubber_plunger
    # 
    # Default: [
    S:gtceu_styrene_butadiene_rubber_mallet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:sapping_sword
    # 
    # Default: [
    S:eidolon_sapping_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: forbidden_arcanus:reinforced_deorum_blacksmith_gavel, forbidden_arcanus:reinforced_deorum_sword, forbidden_arcanus:reinforced_deorum_shovel, forbidden_arcanus:reinforced_deorum_pickaxe, forbidden_arcanus:reinforced_deorum_axe, forbidden_arcanus:reinforced_deorum_hoe
    # 
    # Default: [
    S:REINFORCED_DEORUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:dragonsteel_axe, thermal_extra:dragonsteel_pickaxe, thermal_extra:dragonsteel_hoe, thermal_extra:dragonsteel_shovel, thermal_extra:dragonsteel_sword, thermal_extra:dragonsteel_excavator, thermal_extra:dragonsteel_hammer, thermal_extra:dragonsteel_knife, thermal_extra:dragonsteel_sickle
    # 
    # Default: [
    S:thermal_extra_dragonsteel_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:livingwood_aiot
    # 
    # Default: [
    S:LIVINGWOOD_AIOT_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:tin_knife
    # 
    # Default: [
    S:TIN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: immersiveengineering:pickaxe_steel, immersiveengineering:shovel_steel, immersiveengineering:axe_steel, immersiveengineering:hoe_steel, immersiveengineering:sword_steel
    # 
    # Default: [
    S:immersiveengineering_pickaxe_steel <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:red_steel_scythe, gtceu:red_steel_hammer, gtceu:iv_red_steel_drill, gtceu:ev_red_steel_drill, gtceu:lv_red_steel_chainsaw, gtceu:red_steel_pickaxe, gtceu:hv_red_steel_drill, gtceu:red_steel_shovel, gtceu:red_steel_buzzsaw, gtceu:red_steel_axe, gtceu:red_steel_file, gtceu:iv_red_steel_wire_cutter, gtceu:lv_red_steel_drill, gtceu:mv_red_steel_drill, gtceu:lv_red_steel_wire_cutter, gtceu:hv_red_steel_wire_cutter, gtceu:red_steel_wire_cutter, gtceu:lv_red_steel_wrench, gtceu:red_steel_mining_hammer, gtceu:red_steel_saw, gtceu:red_steel_screwdriver, gtceu:iv_red_steel_wrench, gtceu:red_steel_spade, gtceu:red_steel_wrench, gtceu:lv_red_steel_screwdriver, gtceu:red_steel_sword, gtceu:red_steel_knife, gtceu:hv_red_steel_wrench, gtceu:red_steel_butchery_knife, gtceu:red_steel_crowbar, gtceu:red_steel_hoe
    # 
    # Default: [
    S:gtceu_red_steel_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:stainless_steel_scythe, gtceu:stainless_steel_hammer, gtceu:iv_stainless_steel_drill, gtceu:ev_stainless_steel_drill, gtceu:lv_stainless_steel_chainsaw, gtceu:stainless_steel_pickaxe, gtceu:hv_stainless_steel_drill, gtceu:stainless_steel_shovel, gtceu:stainless_steel_buzzsaw, gtceu:stainless_steel_axe, gtceu:stainless_steel_file, gtceu:iv_stainless_steel_wire_cutter, gtceu:lv_stainless_steel_drill, gtceu:mv_stainless_steel_drill, gtceu:lv_stainless_steel_wire_cutter, gtceu:hv_stainless_steel_wire_cutter, gtceu:stainless_steel_wire_cutter, gtceu:lv_stainless_steel_wrench, gtceu:stainless_steel_mining_hammer, gtceu:stainless_steel_saw, gtceu:stainless_steel_screwdriver, gtceu:iv_stainless_steel_wrench, gtceu:stainless_steel_spade, gtceu:stainless_steel_wrench, gtceu:lv_stainless_steel_screwdriver, gtceu:stainless_steel_sword, gtceu:stainless_steel_knife, gtceu:hv_stainless_steel_wrench, gtceu:stainless_steel_butchery_knife, gtceu:stainless_steel_crowbar, gtceu:stainless_steel_hoe
    # 
    # Default: [
    S:gtceu_stainless_steel_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:misery
    # 
    # Default: [
    S:DREADSWORD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:skyroot_pickaxe, aether:skyroot_axe, aether:skyroot_shovel, aether:skyroot_hoe, aether:skyroot_sword
    # 
    # Default: [
    S:SKYROOT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightdelight:steeleaf_knife, twilightforest:steeleaf_sword, twilightforest:steeleaf_shovel, twilightforest:steeleaf_pickaxe, twilightforest:steeleaf_axe, twilightforest:steeleaf_hoe
    # 
    # Default: [
    S:twilightdelight_steeleaf_knife <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:lead_knife
    # 
    # Default: [
    S:LEAD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:reversal_pick
    # 
    # Default: [
    S:eidolon_reversal_pick <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:astral_sword, voidscape:astral_axe, voidscape:astral_pickaxe, voidscape:astral_shovel
    # 
    # Default: [
    S:voidscape_astral_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:candy_cane_sword
    # 
    # Default: [
    S:CANDY_CANE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: lost_aether_content:phoenix_shovel, lost_aether_content:phoenix_pickaxe, lost_aether_content:phoenix_axe, lost_aether_content:phoenix_sword, lost_aether_content:phoenix_hoe
    # 
    # Default: [
    S:PHOENIX <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:osmium_knife
    # 
    # Default: [
    S:OSMIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:aluminum_knife
    # 
    # Default: [
    S:ALUMINUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:silver_sword, eidolon:silver_pickaxe, eidolon:silver_axe, eidolon:silver_shovel, eidolon:silver_hoe
    # 
    # Default: [
    S:eidolon_silver_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:alf_knife
    # 
    # Default: [
    S:delightful_alf_knife <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: draconicevolution:draconic_shovel, draconicevolution:draconic_hoe, draconicevolution:draconic_pickaxe, draconicevolution:draconic_axe, draconicevolution:draconic_sword, draconicevolution:draconic_staff
    # 
    # Default: [
    S:draconicevolution_draconic_shovel <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:gravitite_pickaxe, aether:gravitite_axe, aether:gravitite_shovel, aether:gravitite_hoe, aether:gravitite_sword
    # 
    # Default: [
    S:GRAVITITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:necronium_knife
    # 
    # Default: [
    S:NECRONIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:flaming_sword
    # 
    # Default: [
    S:FLAMING <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:wrought_iron_scythe, gtceu:wrought_iron_hammer, gtceu:wrought_iron_mortar, gtceu:iv_wrought_iron_drill, gtceu:ev_wrought_iron_drill, gtceu:lv_wrought_iron_chainsaw, gtceu:wrought_iron_pickaxe, gtceu:hv_wrought_iron_drill, gtceu:wrought_iron_shovel, gtceu:wrought_iron_buzzsaw, gtceu:wrought_iron_axe, gtceu:wrought_iron_file, gtceu:iv_wrought_iron_wire_cutter, gtceu:lv_wrought_iron_drill, gtceu:mv_wrought_iron_drill, gtceu:lv_wrought_iron_wire_cutter, gtceu:hv_wrought_iron_wire_cutter, gtceu:wrought_iron_wire_cutter, gtceu:lv_wrought_iron_wrench, gtceu:wrought_iron_mining_hammer, gtceu:wrought_iron_saw, gtceu:wrought_iron_screwdriver, gtceu:iv_wrought_iron_wrench, gtceu:wrought_iron_spade, gtceu:wrought_iron_wrench, gtceu:lv_wrought_iron_screwdriver, gtceu:wrought_iron_sword, gtceu:wrought_iron_knife, gtceu:hv_wrought_iron_wrench, gtceu:wrought_iron_butchery_knife, gtceu:wrought_iron_crowbar, gtceu:wrought_iron_hoe
    # 
    # Default: [
    S:gtceu_wrought_iron_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:blazing_knife
    # 
    # Default: [
    S:BLAZING <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:invar_scythe, gtceu:invar_hammer, gtceu:invar_mortar, gtceu:iv_invar_drill, gtceu:ev_invar_drill, gtceu:lv_invar_chainsaw, gtceu:invar_pickaxe, gtceu:hv_invar_drill, gtceu:invar_shovel, gtceu:invar_buzzsaw, gtceu:invar_axe, gtceu:invar_file, gtceu:iv_invar_wire_cutter, gtceu:lv_invar_drill, gtceu:mv_invar_drill, gtceu:lv_invar_wire_cutter, gtceu:hv_invar_wire_cutter, gtceu:invar_wire_cutter, gtceu:lv_invar_wrench, gtceu:invar_mining_hammer, gtceu:invar_saw, gtceu:invar_screwdriver, gtceu:iv_invar_wrench, gtceu:invar_spade, gtceu:invar_wrench, gtceu:lv_invar_screwdriver, gtceu:invar_sword, gtceu:invar_knife, gtceu:hv_invar_wrench, gtceu:invar_butchery_knife, gtceu:invar_crowbar, gtceu:invar_hoe
    # 
    # Default: [
    S:gtceu_invar_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:large_amethyst_knife
    # 
    # Default: [
    S:LARGE_AMETHYST <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:obsidian_infused_enderite_knife
    # 
    # Default: [
    S:OBSIDIAN_INFUSED_ENDERITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:zinc_knife
    # 
    # Default: [
    S:ZINC <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:emerald_knife
    # 
    # Default: [
    S:EMERALD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:titanite_sword, voidscape:titanite_axe, voidscape:titanite_pickaxe, voidscape:titanite_hoe
    # 
    # Default: [
    S:voidscape_titanite_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:gilded_quartz_knife
    # 
    # Default: [
    S:GILDED_QUARTZ <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:crystalline_knife
    # 
    # Default: [
    S:CRYSTALLINE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:nether_quartz_knife
    # 
    # Default: [
    S:NETHER_QUARTZ <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:zanite_pickaxe, aether:zanite_axe, aether:zanite_shovel, aether:zanite_hoe, aether:zanite_sword
    # 
    # Default: [
    S:ZANITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:copper_knife
    # 
    # Default: [
    S:COPPER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:invar_knife
    # 
    # Default: [
    S:INVAR <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:mythril_knife
    # 
    # Default: [
    S:MYTHRIL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:lumium_axe, thermal_extra:lumium_pickaxe, thermal_extra:lumium_hoe, thermal_extra:lumium_shovel, thermal_extra:lumium_sword, thermal_extra:lumium_excavator, thermal_extra:lumium_hammer, thermal_extra:lumium_knife, thermal_extra:lumium_sickle
    # 
    # Default: [
    S:thermal_extra_lumium_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:neutronium_scythe, gtceu:neutronium_hammer, gtceu:iv_neutronium_drill, gtceu:ev_neutronium_drill, gtceu:lv_neutronium_chainsaw, gtceu:neutronium_pickaxe, gtceu:hv_neutronium_drill, gtceu:neutronium_shovel, gtceu:neutronium_buzzsaw, gtceu:neutronium_axe, gtceu:neutronium_file, gtceu:iv_neutronium_wire_cutter, gtceu:lv_neutronium_drill, gtceu:mv_neutronium_drill, gtceu:lv_neutronium_wire_cutter, gtceu:hv_neutronium_wire_cutter, gtceu:neutronium_wire_cutter, gtceu:lv_neutronium_wrench, gtceu:neutronium_mining_hammer, gtceu:neutronium_saw, gtceu:neutronium_screwdriver, gtceu:iv_neutronium_wrench, gtceu:neutronium_spade, gtceu:neutronium_wrench, gtceu:lv_neutronium_screwdriver, gtceu:neutronium_sword, gtceu:neutronium_knife, gtceu:hv_neutronium_wrench, gtceu:neutronium_butchery_knife, gtceu:neutronium_crowbar, gtceu:neutronium_hoe
    # 
    # Default: [
    S:gtceu_neutronium_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:certus_quartz_knife
    # 
    # Default: [
    S:CERTUS_QUARTZ <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:gilded_netherite_knife
    # 
    # Default: [
    S:GILDED_NETHERITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:livingwood_sword, aiotbotania:livingwood_axe, aiotbotania:livingwood_pickaxe, aiotbotania:livingwood_shovel, aiotbotania:livingwood_hoe
    # 
    # Default: [
    S:LIVINGWOOD_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:ichor_sword, voidscape:ichor_axe, voidscape:ichor_pickaxe
    # 
    # Default: [
    S:voidscape_ichor_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:cobalt_brass_scythe, gtceu:cobalt_brass_hammer, gtceu:cobalt_brass_mortar, gtceu:iv_cobalt_brass_drill, gtceu:ev_cobalt_brass_drill, gtceu:lv_cobalt_brass_chainsaw, gtceu:cobalt_brass_pickaxe, gtceu:hv_cobalt_brass_drill, gtceu:cobalt_brass_shovel, gtceu:cobalt_brass_buzzsaw, gtceu:cobalt_brass_axe, gtceu:cobalt_brass_file, gtceu:iv_cobalt_brass_wire_cutter, gtceu:lv_cobalt_brass_drill, gtceu:mv_cobalt_brass_drill, gtceu:lv_cobalt_brass_wire_cutter, gtceu:hv_cobalt_brass_wire_cutter, gtceu:cobalt_brass_wire_cutter, gtceu:lv_cobalt_brass_wrench, gtceu:cobalt_brass_mining_hammer, gtceu:cobalt_brass_saw, gtceu:cobalt_brass_screwdriver, gtceu:iv_cobalt_brass_wrench, gtceu:cobalt_brass_spade, gtceu:cobalt_brass_wrench, gtceu:lv_cobalt_brass_screwdriver, gtceu:cobalt_brass_sword, gtceu:cobalt_brass_knife, gtceu:hv_cobalt_brass_wrench, gtceu:cobalt_brass_butchery_knife, gtceu:cobalt_brass_crowbar, gtceu:cobalt_brass_hoe
    # 
    # Default: [
    S:gtceu_cobalt_brass_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:manasteel_pick, botania:manasteel_shovel, botania:manasteel_axe, botania:manasteel_hoe, botania:manasteel_sword, botania:ender_dagger, delightful:manasteel_knife
    # 
    # Default: [
    S:MANASTEEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:rose_gold_scythe, gtceu:rose_gold_hammer, gtceu:iv_rose_gold_drill, gtceu:ev_rose_gold_drill, gtceu:lv_rose_gold_chainsaw, gtceu:rose_gold_pickaxe, gtceu:hv_rose_gold_drill, gtceu:rose_gold_shovel, gtceu:rose_gold_buzzsaw, gtceu:rose_gold_axe, gtceu:rose_gold_file, gtceu:iv_rose_gold_wire_cutter, gtceu:lv_rose_gold_drill, gtceu:mv_rose_gold_drill, gtceu:lv_rose_gold_wire_cutter, gtceu:hv_rose_gold_wire_cutter, gtceu:rose_gold_wire_cutter, gtceu:lv_rose_gold_wrench, gtceu:rose_gold_mining_hammer, gtceu:rose_gold_saw, gtceu:rose_gold_screwdriver, gtceu:iv_rose_gold_wrench, gtceu:rose_gold_spade, gtceu:rose_gold_wrench, gtceu:lv_rose_gold_screwdriver, gtceu:rose_gold_sword, gtceu:rose_gold_knife, gtceu:hv_rose_gold_wrench, gtceu:rose_gold_butchery_knife, gtceu:rose_gold_crowbar, gtceu:rose_gold_hoe
    # 
    # Default: [
    S:gtceu_rose_gold_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:thyrium_knife
    # 
    # Default: [
    S:THYRIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:blue_steel_scythe, gtceu:blue_steel_hammer, gtceu:iv_blue_steel_drill, gtceu:ev_blue_steel_drill, gtceu:lv_blue_steel_chainsaw, gtceu:blue_steel_pickaxe, gtceu:hv_blue_steel_drill, gtceu:blue_steel_shovel, gtceu:blue_steel_buzzsaw, gtceu:blue_steel_axe, gtceu:blue_steel_file, gtceu:iv_blue_steel_wire_cutter, gtceu:lv_blue_steel_drill, gtceu:mv_blue_steel_drill, gtceu:lv_blue_steel_wire_cutter, gtceu:hv_blue_steel_wire_cutter, gtceu:blue_steel_wire_cutter, gtceu:lv_blue_steel_wrench, gtceu:blue_steel_mining_hammer, gtceu:blue_steel_saw, gtceu:blue_steel_screwdriver, gtceu:iv_blue_steel_wrench, gtceu:blue_steel_spade, gtceu:blue_steel_wrench, gtceu:lv_blue_steel_screwdriver, gtceu:blue_steel_sword, gtceu:blue_steel_knife, gtceu:hv_blue_steel_wrench, gtceu:blue_steel_butchery_knife, gtceu:blue_steel_crowbar, gtceu:blue_steel_hoe
    # 
    # Default: [
    S:gtceu_blue_steel_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:terra_aiot
    # 
    # Default: [
    S:TERRASTEEL_AIOT_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:steel_scythe, gtceu:steel_hammer, gtceu:steel_mortar, gtceu:iv_steel_drill, gtceu:ev_steel_drill, gtceu:lv_steel_chainsaw, gtceu:steel_pickaxe, gtceu:hv_steel_drill, gtceu:steel_shovel, gtceu:steel_buzzsaw, gtceu:steel_axe, gtceu:steel_file, gtceu:iv_steel_wire_cutter, gtceu:lv_steel_drill, gtceu:mv_steel_drill, gtceu:lv_steel_wire_cutter, gtceu:hv_steel_wire_cutter, gtceu:steel_wire_cutter, gtceu:lv_steel_wrench, gtceu:steel_mining_hammer, gtceu:steel_saw, gtceu:steel_screwdriver, gtceu:iv_steel_wrench, gtceu:steel_spade, gtceu:steel_wrench, gtceu:lv_steel_screwdriver, gtceu:steel_sword, gtceu:steel_knife, gtceu:hv_steel_wrench, gtceu:steel_butchery_knife, gtceu:steel_crowbar, gtceu:steel_hoe
    # 
    # Default: [
    S:gtceu_steel_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:abyssal_axe, thermal_extra:abyssal_pickaxe, thermal_extra:abyssal_hoe, thermal_extra:abyssal_shovel, thermal_extra:abyssal_sword, thermal_extra:abyssal_excavator, thermal_extra:abyssal_hammer, thermal_extra:abyssal_knife, thermal_extra:abyssal_sickle
    # 
    # Default: [
    S:thermal_extra_abyssal_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:corrupt_sword, voidscape:corrupt_axe
    # 
    # Default: [
    S:voidscape_corrupt_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightdelight:knightmetal_knife, twilightforest:knightmetal_sword, twilightforest:knightmetal_pickaxe, twilightforest:knightmetal_axe
    # 
    # Default: [
    S:twilightdelight_knightmetal_knife <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:kiwano_knife
    # 
    # Default: [
    S:KIWANO <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:amethyst_rapier
    # 
    # Default: [
    S:AMETHYST <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:ironwood_knife
    # 
    # Default: [
    S:IRONWOOD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:void_forge, cataclysm:infernal_forge
    # 
    # Default: [
    S:cataclysm_void_forge <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:soul_steel_knife
    # 
    # Default: [
    S:SOUL_STEEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:charred_warhammer
    # 
    # Default: [
    S:voidscape_charred_warhammer <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:alfsteel_hoe
    # 
    # Default: [
    S:ALFSTEEL_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:livingrock_sword, aiotbotania:livingrock_axe, aiotbotania:livingrock_pickaxe, aiotbotania:livingrock_shovel, aiotbotania:livingrock_hoe
    # 
    # Default: [
    S:LIVINGROCK_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:khopesh
    # 
    # Default: [
    S:cataclysm_khopesh <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:enderite_knife
    # 
    # Default: [
    S:ENDERITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:duranium_scythe, gtceu:duranium_hammer, gtceu:iv_duranium_drill, gtceu:ev_duranium_drill, gtceu:lv_duranium_chainsaw, gtceu:duranium_pickaxe, gtceu:hv_duranium_drill, gtceu:duranium_shovel, gtceu:duranium_buzzsaw, gtceu:duranium_axe, gtceu:duranium_file, gtceu:iv_duranium_wire_cutter, gtceu:lv_duranium_drill, gtceu:mv_duranium_drill, gtceu:lv_duranium_wire_cutter, gtceu:hv_duranium_wire_cutter, gtceu:duranium_wire_cutter, gtceu:lv_duranium_wrench, gtceu:duranium_mining_hammer, gtceu:duranium_saw, gtceu:duranium_screwdriver, gtceu:iv_duranium_wrench, gtceu:duranium_spade, gtceu:duranium_wrench, gtceu:lv_duranium_screwdriver, gtceu:duranium_sword, gtceu:duranium_knife, gtceu:hv_duranium_wrench, gtceu:duranium_butchery_knife, gtceu:duranium_crowbar, gtceu:duranium_hoe
    # 
    # Default: [
    S:gtceu_duranium_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:pearlescent_knife
    # 
    # Default: [
    S:PEARLESCENT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:allthemodium_knife
    # 
    # Default: [
    S:ALLTHEMODIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:constantan_knife
    # 
    # Default: [
    S:CONSTANTAN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:adamantium_knife
    # 
    # Default: [
    S:ADAMANTIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:knightmetal_knife
    # 
    # Default: [
    S:KNIGHTMETAL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:elementium_pickaxe, botania:elementium_shovel, botania:elementium_axe, botania:elementium_hoe, botania:elementium_sword, delightful:elementium_knife
    # 
    # Default: [
    S:ELEMENTIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:ultimet_scythe, gtceu:ultimet_hammer, gtceu:iv_ultimet_drill, gtceu:ev_ultimet_drill, gtceu:lv_ultimet_chainsaw, gtceu:ultimet_pickaxe, gtceu:hv_ultimet_drill, gtceu:ultimet_shovel, gtceu:ultimet_buzzsaw, gtceu:ultimet_axe, gtceu:ultimet_file, gtceu:iv_ultimet_wire_cutter, gtceu:lv_ultimet_drill, gtceu:mv_ultimet_drill, gtceu:lv_ultimet_wire_cutter, gtceu:hv_ultimet_wire_cutter, gtceu:ultimet_wire_cutter, gtceu:lv_ultimet_wrench, gtceu:ultimet_mining_hammer, gtceu:ultimet_saw, gtceu:ultimet_screwdriver, gtceu:iv_ultimet_wrench, gtceu:ultimet_spade, gtceu:ultimet_wrench, gtceu:lv_ultimet_screwdriver, gtceu:ultimet_sword, gtceu:ultimet_knife, gtceu:hv_ultimet_wrench, gtceu:ultimet_butchery_knife, gtceu:ultimet_crowbar, gtceu:ultimet_hoe
    # 
    # Default: [
    S:gtceu_ultimet_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:terra_pick, botania:terra_axe, botania:terra_sword, botania:star_sword, botania:thunder_sword, mythicbotany:alfsteel_sword, mythicbotany:alfsteel_pick, mythicbotany:alfsteel_axe, aiotbotania:terra_shovel, aiotbotania:terra_hoe, delightful:terra_knife
    # 
    # Default: [
    S:TERRASTEEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:refined_glowstone_knife
    # 
    # Default: [
    S:REFINED_GLOWSTONE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:damascus_steel_scythe, gtceu:damascus_steel_hammer, gtceu:damascus_steel_mortar, gtceu:iv_damascus_steel_drill, gtceu:ev_damascus_steel_drill, gtceu:lv_damascus_steel_chainsaw, gtceu:damascus_steel_pickaxe, gtceu:hv_damascus_steel_drill, gtceu:damascus_steel_shovel, gtceu:damascus_steel_buzzsaw, gtceu:damascus_steel_axe, gtceu:damascus_steel_file, gtceu:iv_damascus_steel_wire_cutter, gtceu:lv_damascus_steel_drill, gtceu:mv_damascus_steel_drill, gtceu:lv_damascus_steel_wire_cutter, gtceu:hv_damascus_steel_wire_cutter, gtceu:damascus_steel_wire_cutter, gtceu:lv_damascus_steel_wrench, gtceu:damascus_steel_mining_hammer, gtceu:damascus_steel_saw, gtceu:damascus_steel_screwdriver, gtceu:iv_damascus_steel_wrench, gtceu:damascus_steel_spade, gtceu:damascus_steel_wrench, gtceu:lv_damascus_steel_screwdriver, gtceu:damascus_steel_sword, gtceu:damascus_steel_knife, gtceu:hv_damascus_steel_wrench, gtceu:damascus_steel_butchery_knife, gtceu:damascus_steel_crowbar, gtceu:damascus_steel_hoe
    # 
    # Default: [
    S:gtceu_damascus_steel_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:experience_knife
    # 
    # Default: [
    S:EXPERIENCE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:valkyrum_knife
    # 
    # Default: [
    S:VALKYRUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:livingrock_aiot
    # 
    # Default: [
    S:LIVINGROCK_AIOT_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:voidic_crystal_sword, voidscape:voidic_crystal_axe, voidscape:voidic_crystal_pickaxe
    # 
    # Default: [
    S:voidscape_voidic_crystal_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:refined_obsidian_pickaxe, mekanismtools:refined_obsidian_axe, mekanismtools:refined_obsidian_shovel, mekanismtools:refined_obsidian_hoe, mekanismtools:refined_obsidian_sword, mekanismtools:refined_obsidian_paxel
    # 
    # Default: [
    S:mekanismtools_refined_obsidian_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:lapis_lazuli_pickaxe, mekanismtools:lapis_lazuli_axe, mekanismtools:lapis_lazuli_shovel, mekanismtools:lapis_lazuli_hoe, mekanismtools:lapis_lazuli_sword, mekanismtools:lapis_lazuli_paxel
    # 
    # Default: [
    S:mekanismtools_lapis_lazuli_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:living_knife
    # 
    # Default: [
    S:LIVING <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:manasteel_aiot
    # 
    # Default: [
    S:MANASTEEL_AIOT_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: everythingcopper:copper_sword, everythingcopper:copper_shovel, everythingcopper:copper_pickaxe, everythingcopper:copper_axe, everythingcopper:copper_hoe
    # 
    # Default: [
    S:everythingcopper_copper_sword <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:hsse_scythe, gtceu:hsse_hammer, gtceu:iv_hsse_drill, gtceu:ev_hsse_drill, gtceu:lv_hsse_chainsaw, gtceu:hsse_pickaxe, gtceu:hv_hsse_drill, gtceu:hsse_shovel, gtceu:hsse_buzzsaw, gtceu:hsse_axe, gtceu:hsse_file, gtceu:iv_hsse_wire_cutter, gtceu:lv_hsse_drill, gtceu:mv_hsse_drill, gtceu:lv_hsse_wire_cutter, gtceu:hv_hsse_wire_cutter, gtceu:hsse_wire_cutter, gtceu:lv_hsse_wrench, gtceu:hsse_mining_hammer, gtceu:hsse_saw, gtceu:hsse_screwdriver, gtceu:iv_hsse_wrench, gtceu:hsse_spade, gtceu:hsse_wrench, gtceu:lv_hsse_screwdriver, gtceu:hsse_sword, gtceu:hsse_knife, gtceu:hv_hsse_wrench, gtceu:hsse_butchery_knife, gtceu:hsse_crowbar, gtceu:hsse_hoe
    # 
    # Default: [
    S:gtceu_hsse_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:lightning_sword
    # 
    # Default: [
    S:LIGHTNING <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:iron_scythe, gtceu:iron_hammer, gtceu:iron_mortar, gtceu:iv_iron_drill, gtceu:ev_iron_drill, gtceu:lv_iron_chainsaw, gtceu:iron_pickaxe, gtceu:hv_iron_drill, gtceu:iron_shovel, gtceu:iron_buzzsaw, gtceu:iron_axe, gtceu:iron_file, gtceu:iv_iron_wire_cutter, gtceu:lv_iron_drill, gtceu:mv_iron_drill, gtceu:lv_iron_wire_cutter, gtceu:hv_iron_wire_cutter, gtceu:iron_wire_cutter, gtceu:lv_iron_wrench, gtceu:iron_mining_hammer, gtceu:iron_saw, gtceu:iron_screwdriver, gtceu:iv_iron_wrench, gtceu:iron_spade, gtceu:iron_wrench, gtceu:lv_iron_screwdriver, gtceu:iron_sword, gtceu:iron_knife, gtceu:hv_iron_wrench, gtceu:iron_butchery_knife, gtceu:iron_crowbar, gtceu:iron_hoe
    # 
    # Default: [
    S:gtceu_iron_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:shellite_axe, thermal_extra:shellite_pickaxe, thermal_extra:shellite_hoe, thermal_extra:shellite_shovel, thermal_extra:shellite_sword, thermal_extra:shellite_excavator, thermal_extra:shellite_hammer, thermal_extra:shellite_knife, thermal_extra:shellite_sickle
    # 
    # Default: [
    S:thermal_extra_shellite_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:netherite_opal_knife
    # 
    # Default: [
    S:NETHERITE_OPAL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:holystone_knife
    # 
    # Default: [
    S:HOLYSTONE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:hammer_of_kingbdogz
    # 
    # Default: [
    S:HAMMER_OF_KINGBDOGZ <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:twinite_axe, thermal_extra:twinite_pickaxe, thermal_extra:twinite_hoe, thermal_extra:twinite_shovel, thermal_extra:twinite_sword, thermal_extra:twinite_excavator, thermal_extra:twinite_hammer, thermal_extra:twinite_knife, thermal_extra:twinite_sickle
    # 
    # Default: [
    S:thermal_extra_twinite_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: sgjourney:naquadah_sword, sgjourney:naquadah_pickaxe, sgjourney:naquadah_axe, sgjourney:naquadah_shovel, sgjourney:naquadah_hoe
    # 
    # Default: [
    S:naquadah <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:tungsten_steel_scythe, gtceu:tungsten_steel_hammer, gtceu:iv_tungsten_steel_drill, gtceu:ev_tungsten_steel_drill, gtceu:lv_tungsten_steel_chainsaw, gtceu:tungsten_steel_pickaxe, gtceu:hv_tungsten_steel_drill, gtceu:tungsten_steel_shovel, gtceu:tungsten_steel_buzzsaw, gtceu:tungsten_steel_axe, gtceu:tungsten_steel_file, gtceu:iv_tungsten_steel_wire_cutter, gtceu:lv_tungsten_steel_drill, gtceu:mv_tungsten_steel_drill, gtceu:lv_tungsten_steel_wire_cutter, gtceu:hv_tungsten_steel_wire_cutter, gtceu:tungsten_steel_wire_cutter, gtceu:lv_tungsten_steel_wrench, gtceu:tungsten_steel_mining_hammer, gtceu:tungsten_steel_saw, gtceu:tungsten_steel_screwdriver, gtceu:iv_tungsten_steel_wrench, gtceu:tungsten_steel_spade, gtceu:tungsten_steel_wrench, gtceu:lv_tungsten_steel_screwdriver, gtceu:tungsten_steel_sword, gtceu:tungsten_steel_knife, gtceu:hv_tungsten_steel_wrench, gtceu:tungsten_steel_butchery_knife, gtceu:tungsten_steel_crowbar, gtceu:tungsten_steel_hoe
    # 
    # Default: [
    S:gtceu_tungsten_steel_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:pig_slayer
    # 
    # Default: [
    S:PIG_SLAYER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:diamond_scythe, gtceu:diamond_hammer, gtceu:iv_diamond_drill, gtceu:ev_diamond_drill, gtceu:lv_diamond_chainsaw, gtceu:diamond_pickaxe, gtceu:hv_diamond_drill, gtceu:diamond_shovel, gtceu:diamond_buzzsaw, gtceu:diamond_axe, gtceu:diamond_file, gtceu:iv_diamond_wire_cutter, gtceu:lv_diamond_drill, gtceu:mv_diamond_drill, gtceu:lv_diamond_wire_cutter, gtceu:hv_diamond_wire_cutter, gtceu:diamond_wire_cutter, gtceu:lv_diamond_wrench, gtceu:diamond_mining_hammer, gtceu:diamond_saw, gtceu:diamond_screwdriver, gtceu:iv_diamond_wrench, gtceu:diamond_spade, gtceu:diamond_wrench, gtceu:lv_diamond_screwdriver, gtceu:diamond_sword, gtceu:diamond_knife, gtceu:hv_diamond_wrench, gtceu:diamond_butchery_knife, gtceu:diamond_crowbar, gtceu:diamond_hoe
    # 
    # Default: [
    S:gtceu_diamond_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:bone_knife
    # 
    # Default: [
    S:BONE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:onyx_knife
    # 
    # Default: [
    S:ONYX <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:electrum_knife
    # 
    # Default: [
    S:ELECTRUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: draconicevolution:chaotic_shovel, draconicevolution:chaotic_hoe, draconicevolution:chaotic_pickaxe, draconicevolution:chaotic_axe, draconicevolution:chaotic_sword, draconicevolution:chaotic_staff
    # 
    # Default: [
    S:draconicevolution_chaotic_shovel <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:vanadium_steel_scythe, gtceu:vanadium_steel_hammer, gtceu:iv_vanadium_steel_drill, gtceu:ev_vanadium_steel_drill, gtceu:lv_vanadium_steel_chainsaw, gtceu:vanadium_steel_pickaxe, gtceu:hv_vanadium_steel_drill, gtceu:vanadium_steel_shovel, gtceu:vanadium_steel_buzzsaw, gtceu:vanadium_steel_axe, gtceu:vanadium_steel_file, gtceu:iv_vanadium_steel_wire_cutter, gtceu:lv_vanadium_steel_drill, gtceu:mv_vanadium_steel_drill, gtceu:lv_vanadium_steel_wire_cutter, gtceu:hv_vanadium_steel_wire_cutter, gtceu:vanadium_steel_wire_cutter, gtceu:lv_vanadium_steel_wrench, gtceu:vanadium_steel_mining_hammer, gtceu:vanadium_steel_saw, gtceu:vanadium_steel_screwdriver, gtceu:iv_vanadium_steel_wrench, gtceu:vanadium_steel_spade, gtceu:vanadium_steel_wrench, gtceu:lv_vanadium_steel_screwdriver, gtceu:vanadium_steel_sword, gtceu:vanadium_steel_knife, gtceu:hv_vanadium_steel_wrench, gtceu:vanadium_steel_butchery_knife, gtceu:vanadium_steel_crowbar, gtceu:vanadium_steel_hoe
    # 
    # Default: [
    S:gtceu_vanadium_steel_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:silicone_rubber_mallet, gtceu:silicone_rubber_plunger
    # 
    # Default: [
    S:gtceu_silicone_rubber_mallet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:tungsten_carbide_scythe, gtceu:tungsten_carbide_hammer, gtceu:iv_tungsten_carbide_drill, gtceu:ev_tungsten_carbide_drill, gtceu:lv_tungsten_carbide_chainsaw, gtceu:tungsten_carbide_pickaxe, gtceu:hv_tungsten_carbide_drill, gtceu:tungsten_carbide_shovel, gtceu:tungsten_carbide_buzzsaw, gtceu:tungsten_carbide_axe, gtceu:tungsten_carbide_file, gtceu:iv_tungsten_carbide_wire_cutter, gtceu:lv_tungsten_carbide_drill, gtceu:mv_tungsten_carbide_drill, gtceu:lv_tungsten_carbide_wire_cutter, gtceu:hv_tungsten_carbide_wire_cutter, gtceu:tungsten_carbide_wire_cutter, gtceu:lv_tungsten_carbide_wrench, gtceu:tungsten_carbide_mining_hammer, gtceu:tungsten_carbide_saw, gtceu:tungsten_carbide_screwdriver, gtceu:iv_tungsten_carbide_wrench, gtceu:tungsten_carbide_spade, gtceu:tungsten_carbide_wrench, gtceu:lv_tungsten_carbide_screwdriver, gtceu:tungsten_carbide_sword, gtceu:tungsten_carbide_knife, gtceu:hv_tungsten_carbide_wrench, gtceu:tungsten_carbide_butchery_knife, gtceu:tungsten_carbide_crowbar, gtceu:tungsten_carbide_hoe
    # 
    # Default: [
    S:gtceu_tungsten_carbide_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:nickel_knife
    # 
    # Default: [
    S:NICKEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:soul_infused_axe, thermal_extra:soul_infused_pickaxe, thermal_extra:soul_infused_hoe, thermal_extra:soul_infused_shovel, thermal_extra:soul_infused_sword, thermal_extra:soul_infused_excavator, thermal_extra:soul_infused_hammer, thermal_extra:soul_infused_knife, thermal_extra:soul_infused_sickle
    # 
    # Default: [
    S:thermal_extra_soul_infused_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:steeleaf_knife
    # 
    # Default: [
    S:STEELEAF <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:sinisite_knife
    # 
    # Default: [
    S:SINISITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: forbidden_arcanus:slimec_pickaxe
    # 
    # Default: [
    S:SLIMEC <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:black_opal_knife
    # 
    # Default: [
    S:BLACK_OPAL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:resonarium_knife
    # 
    # Default: [
    S:RESONARIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:sterling_silver_scythe, gtceu:sterling_silver_hammer, gtceu:iv_sterling_silver_drill, gtceu:ev_sterling_silver_drill, gtceu:lv_sterling_silver_chainsaw, gtceu:sterling_silver_pickaxe, gtceu:hv_sterling_silver_drill, gtceu:sterling_silver_shovel, gtceu:sterling_silver_buzzsaw, gtceu:sterling_silver_axe, gtceu:sterling_silver_file, gtceu:iv_sterling_silver_wire_cutter, gtceu:lv_sterling_silver_drill, gtceu:mv_sterling_silver_drill, gtceu:lv_sterling_silver_wire_cutter, gtceu:hv_sterling_silver_wire_cutter, gtceu:sterling_silver_wire_cutter, gtceu:lv_sterling_silver_wrench, gtceu:sterling_silver_mining_hammer, gtceu:sterling_silver_saw, gtceu:sterling_silver_screwdriver, gtceu:iv_sterling_silver_wrench, gtceu:sterling_silver_spade, gtceu:sterling_silver_wrench, gtceu:lv_sterling_silver_screwdriver, gtceu:sterling_silver_sword, gtceu:sterling_silver_knife, gtceu:hv_sterling_silver_wrench, gtceu:sterling_silver_butchery_knife, gtceu:sterling_silver_crowbar, gtceu:sterling_silver_hoe
    # 
    # Default: [
    S:gtceu_sterling_silver_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:brass_knife
    # 
    # Default: [
    S:BRASS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:aluminium_scythe, gtceu:aluminium_hammer, gtceu:iv_aluminium_drill, gtceu:ev_aluminium_drill, gtceu:lv_aluminium_chainsaw, gtceu:aluminium_pickaxe, gtceu:hv_aluminium_drill, gtceu:aluminium_shovel, gtceu:aluminium_buzzsaw, gtceu:aluminium_axe, gtceu:aluminium_file, gtceu:iv_aluminium_wire_cutter, gtceu:lv_aluminium_drill, gtceu:mv_aluminium_drill, gtceu:lv_aluminium_wire_cutter, gtceu:hv_aluminium_wire_cutter, gtceu:aluminium_wire_cutter, gtceu:lv_aluminium_wrench, gtceu:aluminium_mining_hammer, gtceu:aluminium_saw, gtceu:aluminium_screwdriver, gtceu:iv_aluminium_wrench, gtceu:aluminium_spade, gtceu:aluminium_wrench, gtceu:lv_aluminium_screwdriver, gtceu:aluminium_sword, gtceu:aluminium_knife, gtceu:hv_aluminium_wrench, gtceu:aluminium_butchery_knife, gtceu:aluminium_crowbar, gtceu:aluminium_hoe
    # 
    # Default: [
    S:gtceu_aluminium_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:bronze_scythe, gtceu:bronze_hammer, gtceu:bronze_mortar, gtceu:iv_bronze_drill, gtceu:ev_bronze_drill, gtceu:lv_bronze_chainsaw, gtceu:bronze_pickaxe, gtceu:hv_bronze_drill, gtceu:bronze_shovel, gtceu:bronze_buzzsaw, gtceu:bronze_axe, gtceu:bronze_file, gtceu:iv_bronze_wire_cutter, gtceu:lv_bronze_drill, gtceu:mv_bronze_drill, gtceu:lv_bronze_wire_cutter, gtceu:hv_bronze_wire_cutter, gtceu:bronze_wire_cutter, gtceu:lv_bronze_wrench, gtceu:bronze_mining_hammer, gtceu:bronze_saw, gtceu:bronze_screwdriver, gtceu:iv_bronze_wrench, gtceu:bronze_spade, gtceu:bronze_wrench, gtceu:lv_bronze_screwdriver, gtceu:bronze_sword, gtceu:bronze_knife, gtceu:hv_bronze_wrench, gtceu:bronze_butchery_knife, gtceu:bronze_crowbar, gtceu:bronze_hoe
    # 
    # Default: [
    S:gtceu_bronze_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:holy_sword
    # 
    # Default: [
    S:HOLY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:deathbringer_scythe
    # 
    # Default: [
    S:eidolon_deathbringer_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:fluix_knife
    # 
    # Default: [
    S:FLUIX <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:silver_knife
    # 
    # Default: [
    S:SILVER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightdelight:fiery_knife, twilightforest:fiery_sword, twilightforest:fiery_pickaxe
    # 
    # Default: [
    S:twilightdelight_fiery_knife <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightdelight:ironwood_knife, twilightforest:ironwood_sword, twilightforest:ironwood_shovel, twilightforest:ironwood_pickaxe, twilightforest:ironwood_axe, twilightforest:ironwood_hoe
    # 
    # Default: [
    S:twilightdelight_ironwood_knife <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:netherite_scythe, gtceu:netherite_hammer, gtceu:iv_netherite_drill, gtceu:ev_netherite_drill, gtceu:lv_netherite_chainsaw, gtceu:netherite_pickaxe, gtceu:hv_netherite_drill, gtceu:netherite_shovel, gtceu:netherite_buzzsaw, gtceu:netherite_axe, gtceu:netherite_file, gtceu:iv_netherite_wire_cutter, gtceu:lv_netherite_drill, gtceu:mv_netherite_drill, gtceu:lv_netherite_wire_cutter, gtceu:hv_netherite_wire_cutter, gtceu:netherite_wire_cutter, gtceu:lv_netherite_wrench, gtceu:netherite_mining_hammer, gtceu:netherite_saw, gtceu:netherite_screwdriver, gtceu:iv_netherite_wrench, gtceu:netherite_spade, gtceu:netherite_wrench, gtceu:lv_netherite_screwdriver, gtceu:netherite_sword, gtceu:netherite_knife, gtceu:hv_netherite_wrench, gtceu:netherite_butchery_knife, gtceu:netherite_crowbar, gtceu:netherite_hoe
    # 
    # Default: [
    S:gtceu_netherite_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:naquadah_alloy_scythe, gtceu:naquadah_alloy_hammer, gtceu:iv_naquadah_alloy_drill, gtceu:ev_naquadah_alloy_drill, gtceu:lv_naquadah_alloy_chainsaw, gtceu:naquadah_alloy_pickaxe, gtceu:hv_naquadah_alloy_drill, gtceu:naquadah_alloy_shovel, gtceu:naquadah_alloy_buzzsaw, gtceu:naquadah_alloy_axe, gtceu:naquadah_alloy_file, gtceu:iv_naquadah_alloy_wire_cutter, gtceu:lv_naquadah_alloy_drill, gtceu:mv_naquadah_alloy_drill, gtceu:lv_naquadah_alloy_wire_cutter, gtceu:hv_naquadah_alloy_wire_cutter, gtceu:naquadah_alloy_wire_cutter, gtceu:lv_naquadah_alloy_wrench, gtceu:naquadah_alloy_mining_hammer, gtceu:naquadah_alloy_saw, gtceu:naquadah_alloy_screwdriver, gtceu:iv_naquadah_alloy_wrench, gtceu:naquadah_alloy_spade, gtceu:naquadah_alloy_wrench, gtceu:lv_naquadah_alloy_screwdriver, gtceu:naquadah_alloy_sword, gtceu:naquadah_alloy_knife, gtceu:hv_naquadah_alloy_wrench, gtceu:naquadah_alloy_butchery_knife, gtceu:naquadah_alloy_crowbar, gtceu:naquadah_alloy_hoe
    # 
    # Default: [
    S:gtceu_naquadah_alloy_scythe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: delightful:refined_obsidian_knife
    # 
    # Default: [
    S:REFINED_OBSIDIAN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: forbidden_arcanus:mystical_dagger
    # 
    # Default: [
    S:MYSTICAL_DAGGER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aiotbotania:alfsteel_shovel, aiotbotania:alfsteel_aiot
    # 
    # Default: [
    S:ALFSTEEL_AIOT_ITEM_TIER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:enderium_axe, thermal_extra:enderium_pickaxe, thermal_extra:enderium_hoe, thermal_extra:enderium_shovel, thermal_extra:enderium_sword, thermal_extra:enderium_excavator, thermal_extra:enderium_hammer, thermal_extra:enderium_knife, thermal_extra:enderium_sickle
    # 
    # Default: [
    S:thermal_extra_enderium_axe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:refined_glowstone_pickaxe, mekanismtools:refined_glowstone_axe, mekanismtools:refined_glowstone_shovel, mekanismtools:refined_glowstone_hoe, mekanismtools:refined_glowstone_sword, mekanismtools:refined_glowstone_paxel
    # 
    # Default: [
    S:mekanismtools_refined_glowstone_pickaxe <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:glass_pickaxe
    # 
    # Default: [
    S:botania_glass_pickaxe <
     >
}


armors {
    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:magenta_conductor_cap
    # 
    # Default: [
    S:railways_magenta_conductor_cap <
     >
    S:create_sa_zinc_chestplate <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:ironwood_helmet, twilightforest:ironwood_chestplate, twilightforest:ironwood_leggings, twilightforest:ironwood_boots
    # 
    # Default: [
    S:ARMOR_IRONWOOD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:archevoker_helmet, irons_spellbooks:archevoker_chestplate, irons_spellbooks:archevoker_leggings, irons_spellbooks:archevoker_boots
    # 
    # Default: [
    S:ARCHEVOKER <
     >
    S:create_sa_copper_exoskeleton_chestplate <
     >
    S:create_sa_zinc_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:priest_helmet, irons_spellbooks:priest_chestplate, irons_spellbooks:priest_leggings, irons_spellbooks:priest_boots
    # 
    # Default: [
    S:PRIEST <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:gray_conductor_cap
    # 
    # Default: [
    S:railways_gray_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:cultist_helmet, irons_spellbooks:cultist_chestplate, irons_spellbooks:cultist_leggings, irons_spellbooks:cultist_boots
    # 
    # Default: [
    S:CULTIST <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: pneumaticcraft:pneumatic_helmet, pneumaticcraft:pneumatic_chestplate, pneumaticcraft:pneumatic_leggings, pneumaticcraft:pneumatic_boots
    # 
    # Default: [
    S:pneumaticcraft_pneumatic_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:red_conductor_cap
    # 
    # Default: [
    S:railways_red_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:prudentium_helmet, mysticalagriculture:prudentium_chestplate, mysticalagriculture:prudentium_leggings, mysticalagriculture:prudentium_boots
    # 
    # Default: [
    S:PRUDENTIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:pink_conductor_cap
    # 
    # Default: [
    S:railways_pink_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:netherite_helmet, minecraft:netherite_chestplate, minecraft:netherite_leggings, minecraft:netherite_boots, cataclysm:monstrous_helm, create_jetpack:netherite_jetpack, create:netherite_backtank, create:netherite_diving_helmet, create:netherite_diving_boots
    # 
    # Default: [
    S:NETHERITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecolonies:pirate_hat, minecolonies:pirate_top, minecolonies:pirate_leggins, minecolonies:pirate_boots
    # 
    # Default: [
    S:minecolonies_pirate_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railcraft:overalls
    # 
    # Default: [
    S:OVERALLS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: naturesaura:sky_helmet, naturesaura:sky_chest, naturesaura:sky_pants, naturesaura:sky_shoes
    # 
    # Default: [
    S:SKY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecolonies:build_goggles
    # 
    # Default: [
    S:minecolonies_build_goggles <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecolonies:plate_armor_helmet, minecolonies:plate_armor_chest, minecolonies:plate_armor_legs, minecolonies:plate_armor_boots
    # 
    # Default: [
    S:minecolonies_plate_armor_helmet <
     >
    S:create_sa_brass_chestplate <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:yellow_conductor_cap
    # 
    # Default: [
    S:railways_yellow_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ad_astra:jet_suit_helmet, ad_astra:jet_suit, ad_astra:jet_suit_pants, ad_astra:jet_suit_boots
    # 
    # Default: [
    S:ad_astra_jet_suit_helmet <
     >
    S:create_sa_copper_boots <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:black_conductor_cap
    # 
    # Default: [
    S:railways_black_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:shadowwalker_helmet, irons_spellbooks:shadowwalker_chestplate, irons_spellbooks:shadowwalker_leggings, irons_spellbooks:shadowwalker_boots
    # 
    # Default: [
    S:SHADOWWALKER <
     >
    S:create_sa_slime_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:pyrope_helmet, blue_skies:pyrope_chestplate, blue_skies:pyrope_leggings, blue_skies:pyrope_boots
    # 
    # Default: [
    S:PYROPE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:knightmetal_helmet, twilightforest:knightmetal_chestplate, twilightforest:knightmetal_leggings, twilightforest:knightmetal_boots
    # 
    # Default: [
    S:ARMOR_KNIGHTLY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:steeleaf_helmet, twilightforest:steeleaf_chestplate, twilightforest:steeleaf_leggings, twilightforest:steeleaf_boots
    # 
    # Default: [
    S:ARMOR_STEELEAF <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: deepresonance:radiation_suit_helmet, deepresonance:radiation_suit_chestplate, deepresonance:radiation_suit_leggings, deepresonance:radiation_suit_boots
    # 
    # Default: [
    S:deepresonance_radiation_suit_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:orange_conductor_cap
    # 
    # Default: [
    S:railways_orange_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ironjetpacks:jetpack
    # 
    # Default: [
    S:JETPACK <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ars_nouveau:sorcerer_boots, ars_nouveau:sorcerer_leggings, ars_nouveau:sorcerer_robes, ars_nouveau:sorcerer_hood
    # 
    # Default: [
    S:ars_nouveau_sorcerer_boots <
     >
    S:create_sa_andesite_jetpack_chestplate <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:ignitium_helmet, cataclysm:ignitium_chestplate, cataclysm:ignitium_elytra_chestplate, cataclysm:ignitium_leggings, cataclysm:ignitium_boots
    # 
    # Default: [
    S:IGNITIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ars_nouveau:battlemage_boots, ars_nouveau:battlemage_leggings, ars_nouveau:battlemage_robes, ars_nouveau:battlemage_hood
    # 
    # Default: [
    S:ars_nouveau_battlemage_boots <
     >
    S:create_sa_zinc_boots <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthemodium:vibranium_boots, allthemodium:vibranium_leggings, allthemodium:vibranium_chestplate, allthemodium:vibranium_helmet
    # 
    # Default: [
    S:VIBRANIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:wandering_magician_helmet, irons_spellbooks:wandering_magician_chestplate, irons_spellbooks:wandering_magician_leggings, irons_spellbooks:wandering_magician_boots
    # 
    # Default: [
    S:WANDERING_MAGICIAN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: bloodmagic:livinghelmet, bloodmagic:livingplate, bloodmagic:livingleggings, bloodmagic:livingboots
    # 
    # Default: [
    S:bloodmagic_livinghelmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railcraft:goggles
    # 
    # Default: [
    S:GOGGLES <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:imperium_helmet, mysticalagriculture:imperium_chestplate, mysticalagriculture:imperium_leggings, mysticalagriculture:imperium_boots
    # 
    # Default: [
    S:IMPERIUM <
     >
    S:create_sa_brass_boots <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:tarnished_helmet
    # 
    # Default: [
    S:TARNISHED <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:turtle_helmet
    # 
    # Default: [
    S:TURTLE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:leather_helmet, minecraft:leather_chestplate, minecraft:leather_leggings, minecraft:leather_boots, silentgear:helmet, silentgear:chestplate, silentgear:leggings, silentgear:boots, quark:backpack, quark:forgotten_hat
    # 
    # Default: [
    S:LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:diamond_helmet, minecraft:diamond_chestplate, minecraft:diamond_leggings, minecraft:diamond_boots, draconicevolution:wyvern_chestpiece, draconicevolution:draconic_chestpiece, draconicevolution:chaotic_chestpiece, productivebees:bee_nest_diamond_helmet
    # 
    # Default: [
    S:DIAMOND <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railcraft:steel_boots, railcraft:steel_chestplate, railcraft:steel_helmet, railcraft:steel_leggings
    # 
    # Default: [
    S:STEEL <
     >
    S:create_sa_zinc_leggings <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:charoite_helmet, blue_skies:charoite_chestplate, blue_skies:charoite_leggings, blue_skies:charoite_boots
    # 
    # Default: [
    S:CHAROITE <
     >
    S:create_sa_copper_leggings <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:pumpkin_helmet, irons_spellbooks:pumpkin_chestplate, irons_spellbooks:pumpkin_leggings, irons_spellbooks:pumpkin_boots
    # 
    # Default: [
    S:PUMPKIN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:iron_helmet, minecraft:iron_chestplate, minecraft:iron_leggings, minecraft:iron_boots, occultism:otherworld_goggles
    # 
    # Default: [
    S:IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:brown_conductor_cap
    # 
    # Default: [
    S:railways_brown_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:light_blue_conductor_cap
    # 
    # Default: [
    S:railways_light_blue_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:chainmail_helmet, minecraft:chainmail_chestplate, minecraft:chainmail_leggings, minecraft:chainmail_boots, mob_grinding_utils:monocle
    # 
    # Default: [
    S:CHAIN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:diopside_helmet, blue_skies:diopside_chestplate, blue_skies:diopside_leggings, blue_skies:diopside_boots
    # 
    # Default: [
    S:DIOPSIDE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:awakened_supremium_helmet, mysticalagriculture:awakened_supremium_chestplate, mysticalagriculture:awakened_supremium_leggings, mysticalagriculture:awakened_supremium_boots
    # 
    # Default: [
    S:AWAKENED_SUPREMIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:shadow_helmet, blue_skies:shadow_chestplate, blue_skies:shadow_leggings, blue_skies:shadow_boots
    # 
    # Default: [
    S:SHADOW <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecraft:golden_helmet, minecraft:golden_chestplate, minecraft:golden_leggings, minecraft:golden_boots
    # 
    # Default: [
    S:GOLD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: undergarden:utherium_helmet, undergarden:utherium_chestplate, undergarden:utherium_leggings, undergarden:utherium_boots
    # 
    # Default: [
    S:UTHERIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:tertium_helmet, mysticalagriculture:tertium_chestplate, mysticalagriculture:tertium_leggings, mysticalagriculture:tertium_boots
    # 
    # Default: [
    S:TERTIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecolonies:santa_hat
    # 
    # Default: [
    S:minecolonies_santa_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:plagued_helmet, irons_spellbooks:plagued_chestplate, irons_spellbooks:plagued_leggings, irons_spellbooks:plagued_boots
    # 
    # Default: [
    S:PLAGUED <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:light_gray_conductor_cap
    # 
    # Default: [
    S:railways_light_gray_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:purple_conductor_cap
    # 
    # Default: [
    S:railways_purple_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: naturesaura:infused_iron_helmet, naturesaura:infused_iron_chest, naturesaura:infused_iron_pants, naturesaura:infused_iron_shoes
    # 
    # Default: [
    S:INFUSED <
     >
    S:create_sa_slime_boots <
     >
    S:create_sa_copper_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: reliquary:witch_hat
    # 
    # Default: [
    S:reliquary_witch_hat <
     >
    S:minecraft_shulker_shell <
     >
    S:create_sa_copper_jetpack_chestplate <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ad_astra:space_helmet, ad_astra:space_suit, ad_astra:space_pants, ad_astra:space_boots
    # 
    # Default: [
    S:ad_astra_space_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ars_nouveau:arcanist_boots, ars_nouveau:arcanist_leggings, ars_nouveau:arcanist_robes, ars_nouveau:arcanist_hood
    # 
    # Default: [
    S:ars_nouveau_arcanist_boots <
     >
    S:create_sa_brass_leggings <
     >
    S:create_sa_copper_chestplate <
     >
    S:SYLVAN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: naturesaura:depth_helmet, naturesaura:depth_chest, naturesaura:depth_pants, naturesaura:depth_shoes
    # 
    # Default: [
    S:DEPTH <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:yeti_helmet, twilightforest:yeti_chestplate, twilightforest:yeti_leggings, twilightforest:yeti_boots
    # 
    # Default: [
    S:ARMOR_YETI <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:cyan_conductor_cap
    # 
    # Default: [
    S:railways_cyan_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthewizardgear:unobtainium_mage_helmet, allthewizardgear:unobtainium_mage_chestplate, allthewizardgear:unobtainium_mage_leggings, allthewizardgear:unobtainium_mage_boots
    # 
    # Default: [
    S:UNOBTAINIUM <
     >
    S:create_sa_andesite_exoskeleton_chestplate <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:green_conductor_cap
    # 
    # Default: [
    S:railways_green_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: deeperdarker:warden_helmet, deeperdarker:warden_chestplate, deeperdarker:warden_leggings, deeperdarker:warden_boots
    # 
    # Default: [
    S:WARDEN <
     >
    S:create_sa_brass_exoskeleton_chestplate <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:naga_chestplate, twilightforest:naga_leggings
    # 
    # Default: [
    S:ARMOR_NAGA <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: minecolonies:pirate_cap, minecolonies:pirate_chest, minecolonies:pirate_legs, minecolonies:pirate_shoes
    # 
    # Default: [
    S:minecolonies_pirate_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:phantom_helmet, twilightforest:phantom_chestplate
    # 
    # Default: [
    S:ARMOR_PHANTOM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: undergarden:froststeel_helmet, undergarden:froststeel_chestplate, undergarden:froststeel_leggings, undergarden:froststeel_boots
    # 
    # Default: [
    S:FROSTSTEEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:white_conductor_cap
    # 
    # Default: [
    S:railways_white_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:pyromancer_helmet, irons_spellbooks:pyromancer_chestplate, irons_spellbooks:pyromancer_leggings, irons_spellbooks:pyromancer_boots
    # 
    # Default: [
    S:PYROMANCER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: undergarden:masticated_chestplate
    # 
    # Default: [
    S:MASTICATED <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:aquite_helmet, blue_skies:aquite_chestplate, blue_skies:aquite_leggings, blue_skies:aquite_boots
    # 
    # Default: [
    S:AQUITE <
     >
    S:create_sa_brass_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:cryomancer_helmet, irons_spellbooks:cryomancer_chestplate, irons_spellbooks:cryomancer_leggings, irons_spellbooks:cryomancer_boots
    # 
    # Default: [
    S:CRYOMANCER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:blue_conductor_cap
    # 
    # Default: [
    S:railways_blue_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:inferium_helmet, mysticalagriculture:inferium_chestplate, mysticalagriculture:inferium_leggings, mysticalagriculture:inferium_boots
    # 
    # Default: [
    S:INFERIUM <
     >
    S:WILDWOOD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:arctic_helmet, twilightforest:arctic_chestplate, twilightforest:arctic_leggings, twilightforest:arctic_boots
    # 
    # Default: [
    S:ARMOR_ARCTIC <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: create_jetpack:jetpack, create:copper_backtank, create:copper_diving_helmet, create:copper_diving_boots
    # 
    # Default: [
    S:COPPER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: evilcraft:spectral_glasses
    # 
    # Default: [
    S:evilcraft_spectral_glasses <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: blue_skies:horizonite_helmet, blue_skies:horizonite_chestplate, blue_skies:horizonite_leggings, blue_skies:horizonite_boots
    # 
    # Default: [
    S:HORIZONITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ad_astra:netherite_space_helmet, ad_astra:netherite_space_suit, ad_astra:netherite_space_pants, ad_astra:netherite_space_boots
    # 
    # Default: [
    S:ad_astra_netherite_space_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: pneumaticcraft:compressed_iron_helmet, pneumaticcraft:compressed_iron_chestplate, pneumaticcraft:compressed_iron_leggings, pneumaticcraft:compressed_iron_boots
    # 
    # Default: [
    S:pneumaticcraft_compressed_iron_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: twilightforest:fiery_helmet, twilightforest:fiery_chestplate, twilightforest:fiery_leggings, twilightforest:fiery_boots
    # 
    # Default: [
    S:ARMOR_FIERY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:electromancer_helmet, irons_spellbooks:electromancer_chestplate, irons_spellbooks:electromancer_leggings, irons_spellbooks:electromancer_boots
    # 
    # Default: [
    S:ELECTROMANCER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: undergarden:cloggrum_helmet, undergarden:cloggrum_chestplate, undergarden:cloggrum_leggings, undergarden:cloggrum_boots
    # 
    # Default: [
    S:CLOGGRUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthemodium:allthemodium_boots, allthemodium:allthemodium_leggings, allthemodium:allthemodium_chestplate, allthemodium:allthemodium_helmet
    # 
    # Default: [
    S:ALLTHEMODIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: railways:lime_conductor_cap
    # 
    # Default: [
    S:railways_lime_conductor_cap <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aquaculture:neptunium_helmet, aquaculture:neptunium_chestplate, aquaculture:neptunium_leggings, aquaculture:neptunium_boots
    # 
    # Default: [
    S:NEPTUNIUM <
     >
    S:create_sa_brass_jetpack_chestplate <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mysticalagriculture:supremium_helmet, mysticalagriculture:supremium_chestplate, mysticalagriculture:supremium_leggings, mysticalagriculture:supremium_boots
    # 
    # Default: [
    S:SUPREMIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: lost_aether_content:agility_boots
    # 
    # Default: [
    S:AGILITY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:hazmat_chestpiece, gtceu:hazmat_leggings, gtceu:hazmat_boots, gtceu:hazmat_headpiece
    # 
    # Default: [
    S:GOOD_PPE_EQUIPMENT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:osmium_helmet, mekanismtools:osmium_chestplate, mekanismtools:osmium_leggings, mekanismtools:osmium_boots
    # 
    # Default: [
    S:mekanismtools_osmium_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:steel_helmet, mekanismtools:steel_chestplate, mekanismtools:steel_leggings, mekanismtools:steel_boots
    # 
    # Default: [
    S:mekanismtools_steel_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ars_elemental:aqua_hat, ars_elemental:aqua_robes, ars_elemental:aqua_leggings, ars_elemental:aqua_boots
    # 
    # Default: [
    S:ars_elemental_aqua_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:bronze_helmet, mekanismtools:bronze_chestplate, mekanismtools:bronze_leggings, mekanismtools:bronze_boots
    # 
    # Default: [
    S:mekanismtools_bronze_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reindeer_ears
    # 
    # Default: [
    S:REINDEER_EARS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:elementium_helmet, botania:elementium_chestplate, botania:elementium_leggings, botania:elementium_boots
    # 
    # Default: [
    S:ELEMENTIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:mob_santa_hat
    # 
    # Default: [
    S:MOB_CHRISTMAS_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:zanite_helmet, aether:zanite_chestplate, aether:zanite_leggings, aether:zanite_boots
    # 
    # Default: [
    S:ZANITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:frankenstein_mask, maidensmerrymaking:frankenstein_body, maidensmerrymaking:frankenstein_leggings, maidensmerrymaking:frankenstein_feet
    # 
    # Default: [
    S:FRANKENSTEIN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:bone_reptile_helmet, cataclysm:bone_reptile_chestplate
    # 
    # Default: [
    S:BONE_REPTILE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reindeer_ears_iron
    # 
    # Default: [
    S:REINDEER_EARS_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:green_ugly_christmas_sweater_leather, maidensmerrymaking:striped_pajama_pants_leather, maidensmerrymaking:santa_slippers_leather
    # 
    # Default: [
    S:GREEN_CHRISTMAS_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:bonelord_helm, eidolon:bonelord_chestplate, eidolon:bonelord_greaves
    # 
    # Default: [
    S:eidolon_bonelord_helm <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_frankenstein_mask, maidensmerrymaking:reinforced_frankenstein_body, maidensmerrymaking:reinforced_frankenstein_leggings, maidensmerrymaking:reinforced_frankenstein_feet
    # 
    # Default: [
    S:REINFORCED_FRANKENSTEIN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:red_ugly_christmas_sweater_iron, maidensmerrymaking:plaid_pajama_pants_iron, maidensmerrymaking:reindeer_slippers_iron
    # 
    # Default: [
    S:RED_CHRISTMAS_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:astral_helmet, voidscape:astral_chest, voidscape:astral_legs, voidscape:astral_boots
    # 
    # Default: [
    S:voidscape_astral_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:manaweave_helmet, botania:manaweave_chestplate, botania:manaweave_leggings, botania:manaweave_boots
    # 
    # Default: [
    S:MANAWEAVE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: deeperdarker:resonarium_helmet, deeperdarker:resonarium_chestplate, deeperdarker:resonarium_leggings, deeperdarker:resonarium_boots
    # 
    # Default: [
    S:RESONARIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:frankenstein_mask_iron, maidensmerrymaking:frankenstein_body_iron, maidensmerrymaking:frankenstein_leggings_iron, maidensmerrymaking:frankenstein_feet_iron
    # 
    # Default: [
    S:FRANKENSTEIN_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ars_elemental:air_hat, ars_elemental:air_robes, ars_elemental:air_leggings, ars_elemental:air_boots
    # 
    # Default: [
    S:ars_elemental_air_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal:hazmat_helmet, thermal:hazmat_chestplate, thermal:hazmat_leggings, thermal:hazmat_boots
    # 
    # Default: [
    S:thermal_hazmat_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:free_runners_armored
    # 
    # Default: [
    S:mekanism_free_runners_armored <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:gold_crown
    # 
    # Default: [
    S:DEV <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:titanite_helmet, voidscape:titanite_chest, voidscape:titanite_legs, voidscape:titanite_boots
    # 
    # Default: [
    S:voidscape_titanite_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: sgjourney:personal_shield_emitter
    # 
    # Default: [
    S:PERSONAL_SHIELD <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_pirate_mask, maidensmerrymaking:reinforced_pirate_body, maidensmerrymaking:reinforced_pirate_pants, maidensmerrymaking:reinforced_pirate_plunderers
    # 
    # Default: [
    S:REINFORCED_PIRATE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_red_santa_hat
    # 
    # Default: [
    S:REINFORCED_RED_SANTA_HAT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: irons_spellbooks:netherite_mage_helmet, irons_spellbooks:netherite_mage_chestplate, irons_spellbooks:netherite_mage_leggings, irons_spellbooks:netherite_mage_boots
    # 
    # Default: [
    S:NETHERITE_BATTLEMAGE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:red_santa_hat_leather
    # 
    # Default: [
    S:RED_SANTA_HAT_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_mermaid_mask, maidensmerrymaking:reinforced_mermaid_body, maidensmerrymaking:reinforced_mermaid_tail, maidensmerrymaking:reinforced_mermaid_fins
    # 
    # Default: [
    S:REINFORCED_MERMAID <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:mekasuit_helmet, mekanism:mekasuit_bodyarmor, mekanism:mekasuit_pants, mekanism:mekasuit_boots
    # 
    # Default: [
    S:mekanism_mekasuit_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:red_ugly_christmas_sweater, maidensmerrymaking:plaid_pajama_pants, maidensmerrymaking:reindeer_slippers
    # 
    # Default: [
    S:RED_CHRISTMAS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:merman_mask_iron, maidensmerrymaking:merman_body_iron, maidensmerrymaking:merman_tail_iron, maidensmerrymaking:merman_fins_iron
    # 
    # Default: [
    S:MERMAN_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:silver_helmet, eidolon:silver_chestplate, eidolon:silver_leggings, eidolon:silver_boots
    # 
    # Default: [
    S:eidolon_silver_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:merman_mask, maidensmerrymaking:merman_body, maidensmerrymaking:merman_tail, maidensmerrymaking:merman_fins
    # 
    # Default: [
    S:MERMAN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_green_ugly_christmas_sweater, maidensmerrymaking:reinforced_striped_pajama_pants, maidensmerrymaking:reinforced_santa_slippers
    # 
    # Default: [
    S:REINFORCED_GREEN_CHRISTMAS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: forbidden_arcanus:tyr_helmet, forbidden_arcanus:tyr_chestplate, forbidden_arcanus:tyr_leggings, forbidden_arcanus:tyr_boots
    # 
    # Default: [
    S:TYR <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:top_hat
    # 
    # Default: [
    S:eidolon_top_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: advanced_ae:quantum_helmet, advanced_ae:quantum_chestplate, advanced_ae:quantum_leggings, advanced_ae:quantum_boots
    # 
    # Default: [
    S:QUANTUM_ALLOY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_red_ugly_christmas_sweater, maidensmerrymaking:reinforced_plaid_pajama_pants, maidensmerrymaking:reinforced_reindeer_slippers
    # 
    # Default: [
    S:REINFORCED_RED_CHRISTMAS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:pirate_mask, maidensmerrymaking:pirate_body, maidensmerrymaking:pirate_pants, maidensmerrymaking:pirate_plunderers
    # 
    # Default: [
    S:PIRATE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:mermaid_mask_iron, maidensmerrymaking:mermaid_body_iron, maidensmerrymaking:mermaid_tail_iron, maidensmerrymaking:mermaid_fins_iron
    # 
    # Default: [
    S:MERMAID_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:gravitite_helmet, aether:gravitite_chestplate, aether:gravitite_leggings, aether:gravitite_boots
    # 
    # Default: [
    S:GRAVITITE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:neptune_helmet, aether:neptune_chestplate, aether:neptune_leggings, aether:neptune_boots
    # 
    # Default: [
    S:NEPTUNE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: sgjourney:jaffa_helmet, sgjourney:jaffa_chestplate, sgjourney:jaffa_leggings, sgjourney:jaffa_boots, sgjourney:jackal_helmet, sgjourney:falcon_helmet
    # 
    # Default: [
    S:JAFFA <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:shellite_helmet, thermal_extra:shellite_chestplate, thermal_extra:shellite_leggings, thermal_extra:shellite_boots
    # 
    # Default: [
    S:thermal_extra_shellite_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: sgjourney:naquadah_helmet, sgjourney:naquadah_chestplate, sgjourney:naquadah_leggings, sgjourney:naquadah_boots
    # 
    # Default: [
    S:NAQUADAH <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_bride_mask, maidensmerrymaking:reinforced_bride_body, maidensmerrymaking:reinforced_bride_breeches, maidensmerrymaking:reinforced_bride_boots
    # 
    # Default: [
    S:REINFORCED_BRIDE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:pirate_mask_leather, maidensmerrymaking:pirate_body_leather, maidensmerrymaking:pirate_pants_leather, maidensmerrymaking:pirate_plunderers_leather
    # 
    # Default: [
    S:PIRATE_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:frankenstein_mask_leather, maidensmerrymaking:frankenstein_body_leather, maidensmerrymaking:frankenstein_leggings_leather, maidensmerrymaking:frankenstein_feet_leather
    # 
    # Default: [
    S:FRANKENSTEIN_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: redstone_arsenal:flux_elytra
    # 
    # Default: [
    S:redstone_arsenal_flux_elytra <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:merman_mask_leather, maidensmerrymaking:merman_body_leather, maidensmerrymaking:merman_tail_leather, maidensmerrymaking:merman_fins_leather
    # 
    # Default: [
    S:MERMAN_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:red_santa_hat
    # 
    # Default: [
    S:RED_SANTA_HAT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:mummy_mask_leather, maidensmerrymaking:mummy_body_leather, maidensmerrymaking:mummy_leggings_leather, maidensmerrymaking:mummy_feet_leather
    # 
    # Default: [
    S:MUMMY_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:twinite_helmet, thermal_extra:twinite_chestplate, thermal_extra:twinite_leggings, thermal_extra:twinite_boots
    # 
    # Default: [
    S:thermal_extra_twinite_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:scuba_mask
    # 
    # Default: [
    S:mekanism_scuba_mask <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:bride_mask_leather, maidensmerrymaking:bride_body_leather, maidensmerrymaking:bride_breeches_leather, maidensmerrymaking:bride_boots_leather
    # 
    # Default: [
    S:BRIDE_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:cursium_helmet, cataclysm:cursium_chestplate, cataclysm:cursium_leggings, cataclysm:cursium_boots
    # 
    # Default: [
    S:CURSIUM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: immersiveengineering:armor_faraday_helmet, immersiveengineering:armor_faraday_chestplate, immersiveengineering:armor_faraday_leggings, immersiveengineering:armor_faraday_boots
    # 
    # Default: [
    S:immersiveengineering_armor_faraday_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_reindeer_ears
    # 
    # Default: [
    S:REINFORCED_REINDEER_EARS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:green_ugly_christmas_sweater_iron, maidensmerrymaking:striped_pajama_pants_iron, maidensmerrymaking:santa_slippers_iron
    # 
    # Default: [
    S:GREEN_CHRISTMAS_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: forbidden_arcanus:draco_arcanus_helmet, forbidden_arcanus:draco_arcanus_chestplate, forbidden_arcanus:draco_arcanus_leggings, forbidden_arcanus:draco_arcanus_boots
    # 
    # Default: [
    S:DRACO_ARCANUS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:red_santa_hat_iron
    # 
    # Default: [
    S:RED_SANTA_HAT_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:mermaid_mask, maidensmerrymaking:mermaid_body, maidensmerrymaking:mermaid_tail, maidensmerrymaking:mermaid_fins
    # 
    # Default: [
    S:MERMAID <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: eidolon:warlock_hat, eidolon:warlock_cloak, eidolon:warlock_boots
    # 
    # Default: [
    S:eidolon_warlock_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ars_elemental:earth_hat, ars_elemental:earth_robes, ars_elemental:earth_leggings, ars_elemental:earth_boots
    # 
    # Default: [
    S:ars_elemental_earth_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: undergarden:ancient_helmet, undergarden:ancient_chestplate, undergarden:ancient_leggings
    # 
    # Default: [
    S:ANCIENT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:mummy_mask_iron, maidensmerrymaking:mummy_body_iron, maidensmerrymaking:mummy_leggings_iron, maidensmerrymaking:mummy_feet_iron
    # 
    # Default: [
    S:MUMMY_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:bunny_hood_iron, maidensmerrymaking:bunny_costume_iron, maidensmerrymaking:bunny_pants_iron, maidensmerrymaking:bunny_feet_iron
    # 
    # Default: [
    S:ANGORA_WOOL_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:lumium_helmet, thermal_extra:lumium_chestplate, thermal_extra:lumium_leggings, thermal_extra:lumium_boots
    # 
    # Default: [
    S:thermal_extra_lumium_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:free_runners
    # 
    # Default: [
    S:mekanism_free_runners <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:sentry_boots
    # 
    # Default: [
    S:SENTRY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: endermanoverhaul:badlands_hood, endermanoverhaul:savanna_hood, endermanoverhaul:snowy_hood
    # 
    # Default: [
    S:endermanoverhaul_badlands_hood <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:jetpack_armored
    # 
    # Default: [
    S:mekanism_jetpack_armored <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:refined_obsidian_helmet, mekanismtools:refined_obsidian_chestplate, mekanismtools:refined_obsidian_leggings, mekanismtools:refined_obsidian_boots
    # 
    # Default: [
    S:mekanismtools_refined_obsidian_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:bunny_hood_leather, maidensmerrymaking:bunny_costume_leather, maidensmerrymaking:bunny_pants_leather, maidensmerrymaking:bunny_feet_leather
    # 
    # Default: [
    S:ANGORA_WOOL_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:signalum_helmet, thermal_extra:signalum_chestplate, thermal_extra:signalum_leggings, thermal_extra:signalum_boots
    # 
    # Default: [
    S:thermal_extra_signalum_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:lapis_lazuli_helmet, mekanismtools:lapis_lazuli_chestplate, mekanismtools:lapis_lazuli_leggings, mekanismtools:lapis_lazuli_boots
    # 
    # Default: [
    S:mekanismtools_lapis_lazuli_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:face_mask, gtceu:rubber_gloves
    # 
    # Default: [
    S:BAD_PPE_EQUIPMENT <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:mermaid_mask_leather, maidensmerrymaking:mermaid_body_leather, maidensmerrymaking:mermaid_tail_leather, maidensmerrymaking:mermaid_fins_leather
    # 
    # Default: [
    S:MERMAID_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: immersiveengineering:armor_steel_helmet, immersiveengineering:armor_steel_chestplate, immersiveengineering:armor_steel_leggings, immersiveengineering:armor_steel_boots
    # 
    # Default: [
    S:immersiveengineering_armor_steel_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:soul_infused_helmet, thermal_extra:soul_infused_chestplate, thermal_extra:soul_infused_leggings, thermal_extra:soul_infused_boots
    # 
    # Default: [
    S:thermal_extra_soul_infused_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: cataclysm:bloom_stone_pauldrons
    # 
    # Default: [
    S:CRAB <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:corrupt_helmet, voidscape:corrupt_chest, voidscape:corrupt_legs, voidscape:corrupt_boots
    # 
    # Default: [
    S:voidscape_corrupt_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:scuba_tank
    # 
    # Default: [
    S:mekanism_scuba_tank <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_merman_mask, maidensmerrymaking:reinforced_merman_body, maidensmerrymaking:reinforced_merman_tail, maidensmerrymaking:reinforced_merman_fins
    # 
    # Default: [
    S:REINFORCED_MERMAN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reindeer_ears_leather
    # 
    # Default: [
    S:REINDEER_EARS_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal:beekeeper_helmet, thermal:beekeeper_chestplate, thermal:beekeeper_leggings, thermal:beekeeper_boots
    # 
    # Default: [
    S:thermal_beekeeper_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:bride_mask_iron, maidensmerrymaking:bride_body_iron, maidensmerrymaking:bride_breeches_iron, maidensmerrymaking:bride_boots_iron
    # 
    # Default: [
    S:BRIDE_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: everythingcopper:copper_helmet, everythingcopper:copper_chestplate, everythingcopper:copper_leggings, everythingcopper:copper_boots
    # 
    # Default: [
    S:everythingcopper_copper_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:ichor_helmet, voidscape:ichor_chest, voidscape:ichor_legs, voidscape:ichor_boots
    # 
    # Default: [
    S:voidscape_ichor_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_bunny_hood, maidensmerrymaking:reinforced_bunny_costume, maidensmerrymaking:reinforced_bunny_pants, maidensmerrymaking:reinforced_bunny_feet
    # 
    # Default: [
    S:REINFORCED_ANGORA_WOOL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:hazmat_mask, mekanism:hazmat_gown, mekanism:hazmat_pants, mekanism:hazmat_boots
    # 
    # Default: [
    S:mekanism_hazmat_mask <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanism:jetpack
    # 
    # Default: [
    S:mekanism_jetpack <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: allthearcanistgear:allthemodium_boots, allthearcanistgear:allthemodium_leggings, allthearcanistgear:allthemodium_robes, allthearcanistgear:allthemodium_hat, allthearcanistgear:vibranium_boots, allthearcanistgear:vibranium_leggings, allthearcanistgear:vibranium_robes, allthearcanistgear:vibranium_hat, allthearcanistgear:unobtainium_boots, allthearcanistgear:unobtainium_leggings, allthearcanistgear:unobtainium_robes, allthearcanistgear:unobtainium_hat
    # 
    # Default: [
    S:allthearcanistgear_allthemodium_boots <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:obsidian_helmet, aether:obsidian_chestplate, aether:obsidian_leggings, aether:obsidian_boots
    # 
    # Default: [
    S:OBSIDIAN <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: ars_elemental:fire_hat, ars_elemental:fire_robes, ars_elemental:fire_leggings, ars_elemental:fire_boots
    # 
    # Default: [
    S:ars_elemental_fire_hat <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:green_ugly_christmas_sweater, maidensmerrymaking:striped_pajama_pants, maidensmerrymaking:santa_slippers
    # 
    # Default: [
    S:GREEN_CHRISTMAS <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:red_ugly_christmas_sweater_leather, maidensmerrymaking:plaid_pajama_pants_leather, maidensmerrymaking:reindeer_slippers_leather
    # 
    # Default: [
    S:RED_CHRISTMAS_LEATHER <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: voidscape:voidic_crystal_helmet, voidscape:voidic_crystal_chest, voidscape:voidic_crystal_legs, voidscape:voidic_crystal_boots
    # 
    # Default: [
    S:voidscape_voidic_crystal_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:mummy_mask, maidensmerrymaking:mummy_body, maidensmerrymaking:mummy_leggings, maidensmerrymaking:mummy_feet
    # 
    # Default: [
    S:MUMMY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:phoenix_helmet, aether:phoenix_chestplate, aether:phoenix_leggings, aether:phoenix_boots
    # 
    # Default: [
    S:PHOENIX <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: forbidden_arcanus:mortem_helmet, forbidden_arcanus:mortem_chestplate, forbidden_arcanus:mortem_leggings, forbidden_arcanus:mortem_boots
    # 
    # Default: [
    S:MORTEM <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:reinforced_mummy_mask, maidensmerrymaking:reinforced_mummy_body, maidensmerrymaking:reinforced_mummy_leggings, maidensmerrymaking:reinforced_mummy_feet
    # 
    # Default: [
    S:REINFORCED_MUMMY <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:nanomuscle_chestplate, gtceu:nanomuscle_leggings, gtceu:nanomuscle_boots, gtceu:nanomuscle_helmet, gtceu:quarktech_chestplate, gtceu:quarktech_leggings, gtceu:quarktech_boots, gtceu:quarktech_helmet, gtceu:advanced_nanomuscle_chestplate, gtceu:advanced_quarktech_chestplate
    # 
    # Default: [
    S:ARMOR <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:manasteel_helmet, botania:manasteel_chestplate, botania:manasteel_leggings, botania:manasteel_boots
    # 
    # Default: [
    S:MANASTEEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:abyssal_helmet, thermal_extra:abyssal_chestplate, thermal_extra:abyssal_leggings, thermal_extra:abyssal_boots
    # 
    # Default: [
    S:thermal_extra_abyssal_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: redstone_arsenal:flux_helmet, redstone_arsenal:flux_chestplate, redstone_arsenal:flux_leggings, redstone_arsenal:flux_boots
    # 
    # Default: [
    S:redstone_arsenal_flux_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: aether:valkyrie_helmet, aether:valkyrie_chestplate, aether:valkyrie_leggings, aether:valkyrie_boots
    # 
    # Default: [
    S:VALKYRIE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:bride_mask, maidensmerrymaking:bride_body, maidensmerrymaking:bride_breeches, maidensmerrymaking:bride_boots
    # 
    # Default: [
    S:BRIDE <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:bunny_hood, maidensmerrymaking:bunny_costume, maidensmerrymaking:bunny_pants, maidensmerrymaking:bunny_feet
    # 
    # Default: [
    S:ANGORA_WOOL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: botania:terrasteel_helmet, botania:terrasteel_chestplate, botania:terrasteel_leggings, botania:terrasteel_boots, mythicbotany:alfsteel_helmet, mythicbotany:alfsteel_chestplate, mythicbotany:alfsteel_leggings, mythicbotany:alfsteel_boots
    # 
    # Default: [
    S:TERRASTEEL <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: maidensmerrymaking:pirate_mask_iron, maidensmerrymaking:pirate_body_iron, maidensmerrymaking:pirate_pants_iron, maidensmerrymaking:pirate_plunderers_iron
    # 
    # Default: [
    S:PIRATE_IRON <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:enderium_helmet, thermal_extra:enderium_chestplate, thermal_extra:enderium_leggings, thermal_extra:enderium_boots
    # 
    # Default: [
    S:thermal_extra_enderium_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: mekanismtools:refined_glowstone_helmet, mekanismtools:refined_glowstone_chestplate, mekanismtools:refined_glowstone_leggings, mekanismtools:refined_glowstone_boots
    # 
    # Default: [
    S:mekanismtools_refined_glowstone_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal:diving_helmet, thermal:diving_chestplate, thermal:diving_leggings, thermal:diving_boots
    # 
    # Default: [
    S:thermal_diving_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: thermal_extra:dragonsteel_helmet, thermal_extra:dragonsteel_chestplate, thermal_extra:dragonsteel_leggings, thermal_extra:dragonsteel_boots
    # 
    # Default: [
    S:thermal_extra_dragonsteel_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:bronze_helmet, gtceu:bronze_chestplate, gtceu:bronze_leggings, gtceu:bronze_boots
    # 
    # Default: [
    S:gtceu_bronze_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:titanium_helmet, gtceu:titanium_chestplate, gtceu:titanium_leggings, gtceu:titanium_boots
    # 
    # Default: [
    S:gtceu_titanium_helmet <
     >

    # A list of material-based prefix names for this material group. May be empty.
    # Items in this group: gtceu:steel_helmet, gtceu:steel_chestplate, gtceu:steel_leggings, gtceu:steel_boots
    # 
    # Default: [
    S:gtceu_steel_helmet <
     >
}


formatting {
    # The format string that will be used when a suffix is applied.
    # Default: %s the %s
    S:"Suffix Format"=%s the %s

    # The format string that will be used to indicate ownership.
    # Default: %s's
    S:"Ownership Format"=%s's
}


