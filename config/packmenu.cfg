# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# 

general {
    # If the title (the giant minecraft text) is drawn.
    # Default: true
    B:"Draw Title"=false

    # If the splash text is drawn.
    # Default: true
    B:"Draw Splash"=true

    # If forge information is drawn at the top center.  This includes beta and update warnings.
    # Default: true
    B:"Draw Forge Info"=true

    # If the vanilla panorama, and it's fade-in, are rendered.  Enabling this disables the use of the custom background options.
    # Default: false
    B:"Draw Panorama"=false

    # If the resource pack is loaded from /resources instead of /resources.zip
    # Default: true
    B:"Folder Pack"=true

    # If the Panorama has a fade-in effect.
    # Default: false
    B:"Panorama Fade In"=false

    # A multiplier on panorama speed.
    # Default: 1.0; Range: [0.01 ~ 100.0]
    S:"Panorama Speed"=1.0

    # The number of variations of panorama that exist.  Panorama files other than the original set must have the form panorama<y>_<z>.png.  For example the first file of varation #2 would be panorama1_0.png
    # Default: 1; Range: [1 ~ 10]
    I:"Panorama Variations"=1
}


title {
    # The anchor point for this element.
    # Default: TITLE
    S:"Anchor Point"=TITLE

    # The X offset for this element.
    # Default: 0; Range: [-1000 ~ 1000]
    I:"X Offset"=0

    # The Y Offset for this element.
    # Default: 0; Range: [-1000 ~ 1000]
    I:"Y Offset"=0
}


"forge info" {
    # The anchor point for this element.
    # Default: FORGE
    S:"Anchor Point"=FORGE

    # The X offset for this element.
    # Default: 0; Range: [-1000 ~ 1000]
    I:"X Offset"=0

    # The Y Offset for this element.
    # Default: 0; Range: [-1000 ~ 1000]
    I:"Y Offset"=0
}


"splash text" {
    # The anchor point for this element.
    # Default: SPLASH
    S:"Anchor Point"=SPLASH

    # The X offset for this element.
    # Default: 0; Range: [-1000 ~ 1000]
    I:"X Offset"=0

    # The Y Offset for this element.
    # Default: 0; Range: [-1000 ~ 1000]
    I:"Y Offset"=0

    # The rotation value of the splash text.
    # Default: -20.0; Range: [-360.0 ~ 360.0]
    S:Rotation=-20.0

    # The color of the splash text.
    # Default: -256; Range: [-2147483647 ~ 2147483647]
    I:Color=-256
}


logo {
    # The location of the logo texture.  Must be a png file.  Should contain the extension.
    # Default: packmenu:textures/gui/logo.png
    S:"Texture Path"=packmenu:textures/gui/logo.png

    # The X offset of the logo.
    # Default: -650; Range: [-500000 ~ 500000]
    I:"X Offset"=-650

    # The Y offset of the logo.
    # Default: 0; Range: [-500000 ~ 500000]
    I:"Y Offset"=0

    # The width of the logo.
    # Default: 100; Range: [0 ~ 500000]
    I:Width=100

    # The height of the logo.
    # Default: 100; Range: [0 ~ 500000]
    I:Height=100

    # The width of the logo's texture.
    # Default: 300; Range: [0 ~ 500000]
    I:"Texture Width"=300

    # The height of the logo's texture.
    # Default: 300; Range: [0 ~ 500000]
    I:"Texture Height"=300

    # The anchor point of the logo.  The types of anchor points are available on the wiki.
    # Default: DEFAULT_LOGO
    S:"Anchor Point"=DEFAULT_LOGO

    # If the logo is enabled or not.
    # Default: true
    B:"Enable Logo"=true
}


slideshow {
    # The list of textures to be displayed on the slideshow.  If empty, the slideshow is ignored.
    # Default: [
    S:Textures <
     >

    # How long between slideshow transitions.
    # Default: 200; Range: [1 ~ 1000000]
    I:Duration=200

    # How long the slideshow transition lasts.
    # Default: 20; Range: [1 ~ 1000000]
    I:"Transition Duration"=20
}


support {
    # The URL that the link on the supporters page goes to.
    # Default: https://www.patreon.com/Shadows_of_Fire?fan_landing=true
    S:"Patreon Url"=https://www.patreon.com/Shadows_of_Fire?fan_landing=true
}


