
[core]

	[core.general]
		#The ingredient network observation frequency slowdown rate in ticks.
		ingredientNetworkObserverFrequencyDecreaseFactor = 5
		#If network change events should be logged. Only enable this when debugging.
		logChangeEvents = false
		#When true, use the LONG number format style. Otherwise, use the SHORT style.
		numberCompactUseLongStyle = false
		#The minimum number of integer digits to include in the result of the compact operator
		numberCompactMinimumIntegerDigits = 1
		#If an anonymous mod startup analytics request may be sent to our analytics service.
		analytics = false
		#The minimum number of fractional digits to include in the result of the compact operator
		numberCompactMinimumFractionDigits = 0
		#The number of threads that the ingredient network observer can use.
		ingredientNetworkObserverThreads = 4
		#The frequency in ticks at which ingredient network should be observed after a position's contents are changed.
		ingredientNetworkObserverFrequencyForced = 0
		#The maximum number of integer digits to include in the result of the compact operator
		numberCompactMaximumIntegerDigits = 3
		#The ingredient network observation frequency slowdown rate in ticks.
		ingredientNetworkObserverFrequencyIncreaseFactor = 1
		#The fastest possible frequency in ticks at which ingredient network should be observed.
		ingredientNetworkObserverFrequencyMin = 5
		#If cable models should be cached for rendering optimization.
		cacheCableModels = true
		#If corrupted networks should automatically be restored on first tick.
		recreateCorruptedNetworks = true
		#The maximum number of fractional digits to include in the result of the compact operator
		numberCompactMaximumFractionDigits = 2
		#The slowest possible frequency in ticks at which ingredient network should be observed.
		ingredientNetworkObserverFrequencyMax = 40
		#If the ingredient network observer can work on separate thread.
		ingredientNetworkObserverEnableMultithreading = true
		#When enabled, networks will stop ticking and values will not be shown and evaluated again. This can be used to fix crashing networks by temporarily enabling this option.
		safeMode = false
		#The maximum frequency at which speach messages can be played in milliseconds.
		speachMaxFrequency = 1000
		#The maximum network energy transfer rate.
		energyRateLimit = 2147483647
		#How deep the recursion stack on an operator can become. This is to avoid game crashes when building things like the omega operator.
		operatorRecursionLimit = 256
		#If the version checker should be enabled.
		versionChecker = false

[general]

	[general.general]
		#The energy usage multiplier for networks.
		energyConsumptionMultiplier = 0
		#The base energy usage for the fluid reader.
		fluidReaderBaseConsumption = 1
		#The base energy usage for the display panel when it has a variable.
		panelDisplayBaseConsumptionEnabled = 2
		#The base energy usage for the audio writer.
		audioWriterBaseConsumption = 1
		#The base energy usage for the inventory reader.
		inventoryReaderBaseConsumption = 1
		#The base energy usage for the dynamic light panel.
		panelLightDynamicBaseConsumption = 0
		#The base energy usage for the entity reader.
		entityReaderBaseConsumption = 1
		#The base energy usage for the static light panel.
		panelLightStaticBaseConsumption = 0
		#The base energy usage for the inventory writer.
		inventoryWriterBaseConsumption = 1
		#The base energy usage for the network reader.
		networkReaderBaseConsumption = 1
		#The base energy usage for the entity writer.
		entityWriterBaseConsumption = 1
		#The base energy usage for the omni-directional connector.
		connectorOmniDirectionalBaseConsumption = 128
		#The base energy usage for the extra-dimensional reader.
		extraDimensionalReaderBaseConsumption = 1
		#The base energy usage for the machine writer.
		machineWriterBaseConsumption = 1
		#The base energy usage for the proxy.
		proxyBaseConsumption = 2
		#The maximum render distance for part overlays to render. The higher, the more resource intensive.
		partOverlayRenderdistance = 15
		#The base energy usage for the audio reader.
		audioReaderBaseConsumption = 1
		#The base energy usage for the redstone reader.
		redstoneReaderBaseConsumption = 1
		#The base energy usage for the materializer.
		materializerBaseConsumption = 1
		#The base energy usage for the world reader.
		worldReaderBaseConsumption = 1
		#The base energy usage for the display panel when does not have a variable.
		panelDisplayBaseConsumptionDisabled = 1
		#The base energy usage for the redstone writer.
		redstoneWriterBaseConsumption = 1
		#The base energy usage for the variable store.
		variablestoreBaseConsumption = 4
		#The base energy usage for the effect writer.
		effectWriterBaseConsumption = 1
		#The base energy usage for the mono-directional connector.
		connectorMonoDirectionalBaseConsumption = 32
		#The base energy usage for the block reader.
		blockReaderBaseConsumption = 1
		#The default port for running the network diagnostics HTTP server.
		diagnosticsWebServerPort = 3030
		#The base energy usage for the machine reader.
		machineReaderBaseConsumption = 1

[machine]

	[machine.general]
		#The maximum offset in blocks a part can target.
		maxPartOffset = 32
		#Priority list of mod id's when determining tag-based recipe outputs.
		recipeTagOutputModPriorities = []
		#The default update frequency in ticks to use for new parts.
		defaultPartUpdateFreq = 1
		#The NBT tags that are not allowed to be read by displaying NBT tags or performing operations on them.
		nbtTagBlacklist = []
		#The maximum offset in blocks a directional connector can look for its target.
		maxDirectionalConnectorOffset = 512
		#The distance from which part offsets should be shown.
		partOffsetRenderDistance = 16
		#The maximum values that Part Offset items will have when dropped from a broken part.
		enchancementOffsetPartDropValue = 4

	[machine.cable]
		#If cable shapes should be determined dynamically. Disable this if FPS issues would occur.
		dynamicShape = true

	[machine.invisible_light]
		#If invisible light should act as full a block
		invisibleLightBlock = true

	[machine.energy_battery]
		#The 1/X fraction of the battery capacity that is allowed to be transfered per tick.
		energyRateCapacityFraction = 2000
		#The maximum capacity visible in the creative tabs. (Make sure that you do not cross the max int size)
		maxCreativeCapacity = 40960000
		#The minimum energy transfer rate per tick.
		minEnergyRate = 2000
		#The default capacity of an energy battery.
		capacity = 1000000
		#The maximum capacity shown in creative tabs. (Make sure that you do not cross the max int size)
		maxCreativeTabCapacity = 655360000

	[machine.delay]
		#The maximum value history length that can be maintained..
		maxHistoryCapacity = 1024

	[machine.mechanical_squeezer]
		#The energy capacity of a mechanical squeezer.
		capacity = 100000
		#How many mB per tick can be auto-ejected.
		autoEjectFluidRate = 500
		#The energy consumption rate.
		consumptionRate = 80

	[machine.mechanical_drying_basin]
		#The energy consumption rate.
		consumptionRate = 80
		#The energy capacity of a mechanical drying basin.
		capacity = 100000

[worldgeneration]

	[worldgeneration.menril_log_filled]
		#The 1/x chance at which Menril Wood will be filled with Menril Resin when generated, the higher this value, the lower the chance.
		filledMenrilLogChance = 10

[item]

	[item.menril_berries]
		#If the berries should give the night vision effect when eaten.
		nightVision = true

	[item.on_the_dynamics_of_integration]
		#If the info book should automatically obtained when the player first spawns.
		obtainOnSpawn = false
		#If the info book can give item rewards for tutorial completion.
		bookRewards = true

