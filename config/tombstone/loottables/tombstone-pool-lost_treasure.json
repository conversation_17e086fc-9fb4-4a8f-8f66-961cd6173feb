{"name": "tombstone:lost_treasure", "rolls": 1.0, "bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "weight": 10, "functions": [{"function": "tombstone:check_enabled"}], "name": "tombstone:essence_of_undeath"}, {"type": "minecraft:item", "weight": 40, "functions": [{"function": "tombstone:random_in_tag", "rl": "minecraft:decorated_pot_sherds"}], "name": "minecraft:book"}, {"type": "minecraft:item", "weight": 20, "name": "minecraft:bundle"}, {"type": "minecraft:item", "weight": 20, "functions": [{"function": "tombstone:check_enabled"}], "name": "tombstone:bag_of_seeds"}, {"type": "minecraft:item", "weight": 15, "functions": [{"function": "tombstone:random_magic_tablet", "ancient": true}], "name": "tombstone:strange_tablet"}, {"type": "minecraft:item", "weight": 20, "functions": [{"function": "tombstone:delayed_nbt_loot"}], "name": "tombstone:scroll_of_knowledge"}, {"type": "minecraft:item", "weight": 50, "functions": [{"function": "tombstone:delayed_nbt_loot"}], "name": "tombstone:magic_scroll"}, {"type": "minecraft:item", "weight": 10, "functions": [{"function": "tombstone:check_enabled"}], "name": "tombstone:gemstone_of_familiar"}, {"type": "minecraft:item", "weight": 10, "functions": [{"function": "tombstone:delayed_nbt_loot"}], "name": "tombstone:voodoo_poppet"}, {"type": "minecraft:item", "weight": 10, "functions": [{"function": "tombstone:delayed_nbt_loot"}], "name": "tombstone:receptacle_of_familiar"}, {"type": "minecraft:item", "weight": 20, "functions": [{"function": "tombstone:delayed_nbt_loot"}], "name": "tombstone:fishing_rod_of_misadventure"}, {"type": "minecraft:item", "weight": 100, "functions": [{"function": "tombstone:check_enabled"}], "name": "tombstone:lost_tablet"}, {"type": "minecraft:empty", "weight": 675}], "conditions": [{"condition": "minecraft:entity_properties", "predicate": {"type_specific": {"in_open_water": true, "type": "fishing_hook"}}, "entity": "this"}]}