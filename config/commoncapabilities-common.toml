
[core]

	[core.general]
		#If mod compatibility loader should crash hard if errors occur in that process.
		crashOnModCompatCrash = false
		#If the recipe loader should crash when finding invalid recipes.
		crashOnInvalidRecipe = false
		#If an anonymous mod startup analytics request may be sent to our analytics service.
		analytics = false
		#If the version checker should be enabled.
		versionChecker = false

[machine]

	[machine.general]
		#The NBT Paths that should be filtered away when checking equality.
		ignoreNbtPathsForEqualityFilters = ["$.ForgeCaps[\"astralsorcery:cap_item_amulet_holder\"]", "$.binding", "$.energy"]

