
[core]

	[core.general]
		#If mod compatibility loader should crash hard if errors occur in that process.
		crashOnModCompatCrash = false
		#Set 'true' to enable development debug mode. This will result in a lower performance!
		debug = false
		#The anonymous id used by the analytics service.
		anonymousAnalyticsID = "218d75bf-7c24-487a-9ec5-5c92ff318212"
		#If the recipe loader should crash when finding invalid recipes.
		crashOnInvalidRecipe = false
		#If an anonymous mod startup analytics request may be sent to our analytics service.
		analytics = false
		#If the version checker should be enabled.
		versionChecker = false

[general]

	[general.general]
		#If a button should be added to the main menu to open a dev world (shift-click creates a new world).
		devWorldButton = false

