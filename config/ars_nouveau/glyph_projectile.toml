
#General settings
[general]
	#Is Enabled?
	enabled = true
	#Cost
	#Range: > -2147483648
	cost = 10
	#Is Starter Glyph?
	starter = true
	#The maximum number of times this glyph may appear in a single spell
	#Range: > 1
	per_spell_limit = 2147483647
	#The tier of the glyph
	#Range: 1 ~ 99
	glyph_tier = 1
	#Limits the number of times a given augment may be applied to a given effect
	#Example entry: "glyph_amplify=5"
	augment_limits = []
	#How much an augment should cost when used on this effect or form. This overrides the default cost in the augment config.
	#Example entry: "glyph_amplify=50"
	augment_cost_overrides = []
	#Max lifespan of the projectile, in seconds.
	#Range: > 0
	max_lifespan = 60

