
#Configuration file for player states.
#You can adjust stamina delta (negative value means consumption / positive value means gain) and
#recovery delay (in ticks) of all player states registered in the game.
#To reload the config, use the following command: /paraglider reloadPlayerStates
#
[paraglider]

	[paraglider.ascending]
		#Range: > -2147483648
		staminaDelta = -3
		#Range: > 0
		recoveryDelay = 10

	[paraglider.breathing_underwater]
		#Range: > -2147483648
		staminaDelta = 20
		#Range: > 0
		recoveryDelay = 0

	[paraglider.flying]
		#Range: > -2147483648
		staminaDelta = 20
		#Range: > 0
		recoveryDelay = 0

	[paraglider.idle]
		#Range: > -2147483648
		staminaDelta = 20
		#Range: > 0
		recoveryDelay = 0

	[paraglider.midair]
		#Range: > -2147483648
		staminaDelta = 0
		#Range: > 0
		recoveryDelay = 0

	[paraglider.on_vehicle]
		#Range: > -2147483648
		staminaDelta = 20
		#Range: > 0
		recoveryDelay = 0

	[paraglider.panic_paragliding]
		#Range: > -2147483648
		staminaDelta = -3
		#Range: > 0
		recoveryDelay = 10

	[paraglider.paragliding]
		#Range: > -2147483648
		staminaDelta = -3
		#Range: > 0
		recoveryDelay = 10

	[paraglider.running]
		#Range: > -2147483648
		staminaDelta = -10
		#Range: > 0
		recoveryDelay = 10

	[paraglider.swimming]
		#Range: > -2147483648
		staminaDelta = -6
		#Range: > 0
		recoveryDelay = 10

	[paraglider.underwater]
		#Range: > -2147483648
		staminaDelta = 3
		#Range: > 0
		recoveryDelay = 0

