#The dimension you can always travel to the Twilight Forest from, as well as the dimension you will return to. Defaults to the overworld. (domain:regname).
originDimension = "minecraft:overworld"
#Allow portals to the Twilight Forest to be made outside of the 'origin' dimension. May be considered an exploit.
allowPortalsInOtherDimensions = false
#Allow portals only for admins (Operators). This severely reduces the range in which the mod usually scans for valid portal conditions, and it scans near ops only.
adminOnlyPortals = false
#Disable Twilight Forest portal creation entirely. Provided for server operators looking to restrict action to the dimension.
disablePortalCreation = false
#Determines if new portals should be pre-checked for safety. If enabled, portals will fail to form rather than redirect to a safe alternate destination.
#Note that enabling this also reduces the rate at which portal formation checks are performed.
checkPortalDestination = false
#Set this true if you want the lightning that zaps the portal to not set things on fire. For those who don't like fun.
portalLightning = false
#If false, the return portal will require the activation item.
shouldReturnPortalBeUsable = true
#Use a valid advancement resource location as a string. For example, using the string "minecraft:story/mine_diamond" will lock the portal behind the "Diamonds!" advancement. Invalid/Empty Advancement resource IDs will leave the portal entirely unlocked.
portalUnlockedByAdvancement = ""
#The max amount of water spaces the mod will check for when creating a portal. Very high numbers may cause issues.
#Range: > 4
maxPortalSize = 64
#If true, Keepsake Caskets that are spawned when a player dies will not be accessible by other players. Use this if you dont want people taking from other people's death caskets. NOTE: server operators will still be able to open locked caskets.
uuid_locking = false
#If true, disables the ability to make Skull Candles by right clicking a vanilla skull with a candle. Turn this on if you're having mod conflict issues for some reason.
skull_candles = false
#If false, items that come enchanted when you craft them (such as ironwood or steeleaf gear) will not show this way in the creative inventory.
#Please note that this doesnt affect the crafting recipes themselves, you will need a datapack to change those.
default_item_enchantments = true
#If true, Twilight Forest's bosses will put their drops inside of a chest where they originally spawned instead of dropping the loot directly.
#Note that the Knight Phantoms are not affected by this as their drops work differently.
boss_drop_chests = true
#Dictates how many blocks down from a cloud block should the game logic check for handling weather related code.
#Lower if experiencing low tick rate. Set to 0 to turn all cloud precipitation logic off.
#Range: > 0
cloudBlockPrecipitationDistance = 32

#Settings that are not reversible without consequences.
["Dimension Settings"]
	#If true, players spawning for the first time will spawn in the Twilight Forest.
	newPlayersSpawnInTF = false
	#If true, the return portal will spawn for new players that were sent to the TF if `spawn_in_tf` is true.
	portalForNewPlayer = false

#Settings for all things related to the uncrafting table.
["Uncrafting Table"]
	#Multiplies the total XP cost of uncrafting an item and rounds up.
	#Higher values means the recipe will cost more to uncraft, lower means less. Set to 0 to disable the cost altogether.
	#Note that this only affects reversed crafting recipes, uncrafting recipes will still use the same cost as they normally would.
	#Range: 0.0 ~ 1.7976931348623157E308
	uncraftingXpCostMultiplier = 1.0
	#Multiplies the total XP cost of repairing an item and rounds up.
	#Higher values means the recipe will cost more to repair, lower means less. Set to 0 to disable the cost altogether.
	#Range: 0.0 ~ 1.7976931348623157E308
	repairingXpCostMultiplier = 1.0
	#If you don't want to disable uncrafting altogether, and would rather disable certain recipes, this is for you.
	#To add a recipe, add the mod id followed by the name of the recipe. You can check this in things like JEI.
	#Example: "twilightforest:firefly_particle_spawner" will disable uncrafting the particle spawner into a firefly jar, firefly, and poppy.
	#If an item has multiple crafting recipes and you wish to disable them all, add the item to the "twilightforest:banned_uncraftables" item tag.
	#If you have a problematic ingredient, like infested towerwood for example, add the item to the "twilightforest:banned_uncrafting_ingredients" item tag.
	disableUncraftingRecipes = ["twilightforest:giant_log_to_oak_planks"]
	#If true, this will invert the above uncrafting recipe list from a blacklist to a whitelist.
	flipRecipeList = false
	#Here, you can disable all items from certain mods from being uncrafted.
	#Input a valid mod id to disable all uncrafting recipes from that mod.
	#Example: "twilightforest" will disable all uncrafting recipes from this mod.
	blacklistedUncraftingModIds = []
	#If true, this will invert the above option from a blacklist to a whitelist.
	flipIdList = false
	#If true, the uncrafting table will also be allowed to uncraft shapeless recipes.
	#The table was originally intended to only take shaped recipes, but this option remains for people who wish to keep the functionality.
	enableShapelessCrafting = false
	#Disables the uncrafting function of the uncrafting table. Recommended as a last resort if there's too many things to change about its behavior (or you're just lazy, I dont judge).
	#Do note that special uncrafting recipes are not disabled as the mod relies on them for other things.
	disableUncrafting = false
	#Disables any usage of the uncrafting table, as well as prevents it from showing up in loot or crafted.
	#Please note that table has more uses than just uncrafting, you can read about them here! http://benimatic.com/tfwiki/index.php?title=Uncrafting_Table
	#It is highly recommended to keep the table enabled as the mod has special uncrafting exclusive recipes, but the option remains for people that dont want the table to be functional at all.
	#If you are looking to just prevent normal crafting recipes from being reversed, consider using the 'disableUncrafting' option instead.
	disableUncraftingTable = true

#Settings for all things related to the magic trees.
["Magic Trees"]
	#If true, prevents the Timewood Core from functioning.
	disableTimeCore = false
	#Defines the radius at which the Timewood Core works. Can be a number anywhere between 1 and 128.
	#Range: 1 ~ 128
	timeCoreRange = 16
	#If true, prevents the Transformation Core from functioning.
	disableTransformationCore = false
	#Defines the radius at which the Transformation Core works. Can be a number anywhere between 1 and 128.
	#Range: 1 ~ 128
	transformationCoreRange = 16
	#If true, prevents the Minewood Core from functioning.
	disableMiningCore = false
	#Defines the radius at which the Minewood Core works. Can be a number anywhere between 1 and 128.
	#Range: 1 ~ 128
	miningCoreRange = 16
	#If true, prevents the Sortingwood Core from functioning.
	disableSortingCore = false
	#Defines the radius at which the Sortingwood Core works. Can be a number anywhere between 1 and 128.
	#Range: 1 ~ 128
	sortingCoreRange = 16

#We recommend downloading the Shield Parry mod for parrying, but these controls remain for without.
["Shield Parrying"]
	#Set to true to parry non-Twilight projectiles.
	parryNonTwilightAttacks = false
	#The amount of ticks after raising a shield that makes it OK to parry a projectile.
	#Range: > 0
	shieldParryTicksArrow = 40

