{"attributes": {"forge:step_height_addition": {"enabled": false, "min": {"default": -512, "value": -512}, "max": {"default": 512, "value": 512}}, "attributeslib:ghost_health": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1000, "value": 1000}}, "minecraft:generic.follow_range": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 2048, "value": 2048}}, "attributeslib:prot_shred": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "attributeslib:overheal": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10, "value": 10}}, "attributeslib:armor_shred": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 2, "value": 2}}, "attributeslib:healing_received": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1000, "value": 1000}}, "irons_spellbooks:nature_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "forge:entity_gravity": {"enabled": false, "min": {"default": -8, "value": -8}, "max": {"default": 8, "value": 8}}, "irons_spellbooks:nature_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "attributeslib:armor_pierce": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1000, "value": 1000}}, "irons_spellbooks:lightning_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:holy_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "minecolonies:mc_mob_damage": {"enabled": false, "min": {"default": 1, "value": 1}, "max": {"default": 20, "value": 20}}, "attributeslib:cold_damage": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1000, "value": 1000}}, "attributeslib:experience_gained": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1000, "value": 1000}}, "minecraft:generic.luck": {"enabled": true, "min": {"default": -1024, "value": -1024}, "max": {"default": 1024, "value": 1024}}, "irons_spellbooks:fire_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "eidolon:persistent_soul_hearts": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 2000, "value": 2000}}, "forge:entity_reach": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1024, "value": 1024}}, "irons_spellbooks:summon_damage": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "ars_nouveau:ars_nouveau.perk.saturation": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10000, "value": 10000}}, "irons_spellbooks:eldritch_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "eidolon:chanting_speed": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:cast_time_reduction": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:cooldown_reduction": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "attributeslib:dodge_chance": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "minecraft:zombie.spawn_reinforcements": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "attributeslib:fire_damage": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1000, "value": 1000}}, "irons_spellbooks:ice_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "minecraft:generic.attack_knockback": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 5, "value": 1000000}}, "attributeslib:crit_chance": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10, "value": 10}}, "forge:swim_speed": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1024, "value": 1024}}, "ars_nouveau:ars_nouveau.perk.wixie": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1024, "value": 1024}}, "minecraft:generic.armor_toughness": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 100, "value": 1000000}}, "irons_spellbooks:fire_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:eldritch_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "ars_nouveau:ars_elemental.perk.summon_power": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10000, "value": 10000}}, "eidolon:magic_power": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10, "value": 10}}, "irons_spellbooks:ender_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:holy_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "attributeslib:crit_damage": {"enabled": false, "min": {"default": 1, "value": 1}, "max": {"default": 100, "value": 100}}, "forge:nametag_distance": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 64, "value": 64}}, "irons_spellbooks:blood_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:evocation_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "forge:block_reach": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1024, "value": 1024}}, "minecraft:generic.attack_speed": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 1024, "value": 1024}}, "ars_nouveau:ars_nouveau.perk.mana_regen": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 2000, "value": 2000}}, "minecraft:generic.attack_damage": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 2048, "value": 1000000}}, "attributeslib:draw_speed": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 4, "value": 4}}, "irons_spellbooks:ender_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "ars_nouveau:ars_nouveau.perk.feather": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "minecraft:generic.armor": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 200, "value": 1000000}}, "irons_spellbooks:evocation_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:blood_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "attributeslib:prot_pierce": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 34, "value": 34}}, "minecraft:generic.flying_speed": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 1024, "value": 1024}}, "attributeslib:current_hp_damage": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "irons_spellbooks:lightning_spell_power": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "attributeslib:mining_speed": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10, "value": 10}}, "minecraft:generic.movement_speed": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 2048, "value": 2048}}, "caelus:fall_flying": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "ars_nouveau:ars_nouveau.perk.max_mana": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10000, "value": 10000}}, "botania:pixie_spawn_chance": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "irons_spellbooks:spell_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "eidolon:max_soul_hearts": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 2000, "value": 2000}}, "minecraft:generic.max_health": {"enabled": true, "min": {"default": 1, "value": 1}, "max": {"default": 2048, "value": 1000000}}, "irons_spellbooks:mana_regen": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 100, "value": 100}}, "irons_spellbooks:ice_magic_resist": {"enabled": false, "min": {"default": -100, "value": -100}, "max": {"default": 100, "value": 100}}, "attributeslib:arrow_damage": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10, "value": 10}}, "minecraft:horse.jump_strength": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 2, "value": 2}}, "attributeslib:arrow_velocity": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10, "value": 10}}, "ars_nouveau:ars_nouveau.perk.spell_damage": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10000, "value": 10000}}, "irons_spellbooks:max_mana": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1000000, "value": 1000000}}, "ars_nouveau:ars_nouveau.perk.warding": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 1024, "value": 1024}}, "minecraft:generic.knockback_resistance": {"enabled": true, "min": {"default": 0, "value": 0}, "max": {"default": 1, "value": 1}}, "attributeslib:life_steal": {"enabled": false, "min": {"default": 0, "value": 0}, "max": {"default": 10, "value": 10}}}}