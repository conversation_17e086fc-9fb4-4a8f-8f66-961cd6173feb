# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# Hostile Networks Config

# All entries in this config file are synced from server to client.


power {
    # The maximum FE stored in the Simulation Chamber.
    # Default: 2000000; Range: [1 ~ 2147483647]
    I:"Sim Chamber Power Cap"=2000000

    # The maximum FE stored in the Loot Fabricator.
    # Default: 1000000; Range: [1 ~ 2147483647]
    I:"Loot Fab Power Cap"=1000000

    # The FE/t cost of the Loot Fabricator.
    # Default: 256; Range: [0 ~ 2147483647]
    I:"Loot Fab Power Cost"=256
}


models {
    # If true, right clicking a blank data model on a mob will attune it to that mob. If disabled, you will need to provide players with a way to get attuned models!
    # Default: true
    B:"Right Click To Attune"=true

    # Whether the Simulation Chamber will upgrade the data on a model. (0 = No, 1 = Yes, 2 = Only up to tier boundaries)
    # Default: 1; Range: [0 ~ 2]
    I:"Sim Chamber Upgrades Model"=1

    # Whether killing mobs will upgrade the data on a model. Note: If you disable this, be sure to add a way for players to get non-Faulty models!
    # Default: true
    B:"Killing Upgrades Model"=true

    # If true, the accuracy of the model increases as it gains progress towards the next tier. If false, always uses the base accuracy of the current tier.
    # Default: true
    B:"Continuous Accuracy"=true
}


