#This is an example boolean property
exampleBoolean = true
#Range: > -2147483648
exampleInt = 42
exampleString = "Hello World"
exampleMultilineString = "Hello World"
#Allowed Values: Hello, World
exampleEnum = "Hello"
exampleStringList = ["Hello", "World"]
exampleIntList = [12, 24]
exampleEnumList = ["Hello", "World"]

[exampleCategory]
	#This is an example category
	#This is an example string inside a category
	innerField = "I am inside"
	#Range: -3.4028234663852886E38 ~ 3.4028234663852886E38
	exampleFloat = 42.84000015258789

