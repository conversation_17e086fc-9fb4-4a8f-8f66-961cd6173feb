
["Metal Renderer Configuration"]
	#Force Metal renderer even on non-Mac systems
	forceMetal = false

[Performance]
	#Enable async path tracing for culling
	asyncTracing = true
	#Number of threads for path tracing
	#Range: 1 ~ 8
	tracingThreads = 2
	#Max tracing distance in blocks
	#Range: 1.0 ~ 16.0
	traceDistance = 6.0
	#Fallback simple culling distance
	#Range: 4.0 ~ 32.0
	fallbackDistance = 16.0

[Leaves]
	#Use advanced leaf culling algorithm
	advancedLeafCulling = true
	#Minimum connected leaves for simple culling
	#Range: 1 ~ 6
	minConnections = 2

["Renderer Configuration"]
	#Enable leaf rendering optimizations
	enabled = true
	#Culling depth (1-5)
	#Higher values = better performance but may cause visual artifacts
	#Range: 1 ~ 5
	cullingDepth = 4
	#Random rejection rate (0.0-1.0)
	#Prevents visible culling patterns
	#Range: 0.0 ~ 1.0
	rejectionRate = 0.55

	["Renderer Configuration"."Ultra Culling"]
		#启用超激进剔除（对所有方块/实体生效）
		ultraCulling = true
		#剔除深度（1-4）
		#越大剔除越激进，但可能出现bug
		#Range: 1 ~ 4
		ultraDepth = 4
		#仅剔除背向玩家的面（防止正面nothing）
		ultraBackfaceOnly = 0.95

["Advanced Features"]
	#Enable mangrove roots optimization
	optimizeMangrove = true
	#Use advanced culling algorithm
	#More precise but slightly slower
	advancedCulling = true

["FPS Settings"]
	#Enable FPS reduction when window is inactive
	reduceFpsWhenInactive = true
	#FPS limit when window is inactive (5-60)
	#Range: 5 ~ 60
	inactiveFpsLimit = 10
	#Enable render distance reduction when window is inactive
	reduceRenderDistanceWhenInactive = true
	#Render distance when window is inactive (2-12)
	#Range: 2 ~ 12
	inactiveRenderDistance = 2

["实体优化"]
	#启用实体tick优化
	enable = true
	#水平检测范围(方块)
	#Range: 1 ~ 256
	horizontalRange = 64
	#垂直检测范围(方块)
	#Range: 1 ~ 256
	verticalRange = 32
	#忽略已死亡的实体
	ignoreDead = false

["物品实体"]
	#优化物品实体tick
	enable = false

["袭击事件"]
	#在袭击中保持袭击者tick
	enable = true

[Entities]
	optimizeEntities = true
	#Range: 1 ~ 256
	horizontalRange = 64
	#Range: 1 ~ 256
	verticalRange = 32
	ignoreDeadEntities = false
	entityWhitelist = ["minecraft:ender_dragon"]
	entityModWhitelist = ["create"]
	tickRaidersInRaid = true
	raidEntityWhitelist = ["minecraft:witch"]

[Items]
	optimizeItems = false
	itemWhitelist = ["minecraft:diamond"]

[Messages]
	sendLoginMessage = true

["Progressive Entity Loading Settings"]
	#Number of entities to load per tick
	#Range: 1 ~ 100
	entityLoadBatchSize = 10
	#Number of tile entities to load per tick
	#Range: 1 ~ 50
	tileEntityLoadBatchSize = 5
	#Minimum tick interval (ms) between entity updates
	#Range: 1 ~ 100
	minTickInterval = 10
	#Maximum tick interval (ms) between entity updates
	#Range: 10 ~ 1000
	maxTickInterval = 100
	#TPS threshold for adjusting tick rate (percentage of 20)
	#Range: 0.1 ~ 1.0
	tpsAdjustThreshold = 0.85

#实体优化设置 | Entity Optimization Settings
[entity_optimization]
	#优化实体碰撞检测 | Optimize entity collision detection
	disableEntityCollisions = true
	#优化实体AI计算 | Optimize entity AI calculations
	optimizeEntityAI = true
	#实体激活范围 (方块) | Entity activation range (blocks)
	#Range: 16 ~ 128
	entityActivationRange = 32
	#启用死亡实体清理 | Enable dead entity cleanup
	entityCleanup = true
	#减少远处实体的同步频率 | Reduce entity sync frequency for distant entities
	reduceEntityUpdates = true

#物品优化设置 | Item Optimization Settings
[item_optimization]
	#物品优化设置| Item Optimization Settings
	OpenIO = true
	#合并物品的最大堆叠数量（0表示无限制）| Maximum stack size for merged items (0 = no limit)
	#Range: > -1
	maxStackSize = -1
	#物品合并检测半径（单位：方块）| Item merge detection radius in blocks
	#Range: 0.1 ~ 10.0
	mergeDistance = 1.5
	#0: 禁用 1: 白名单模式 2: 黑名单模式 | 0: Disabled, 1: Whitelist, 2: Blacklist
	#Range: 0 ~ 2
	listMode = 0
	#白名单/黑名单中的物品注册名列表 | Item registry names for whitelist/blacklist
	itemList = []
	#是否在合并后的物品上显示堆叠数量 | Whether to show stack count on merged items
	showStackCount = true

	[item_optimization.stack_size]
		#启用自定义堆叠大小 | Enable custom stack sizes
		enabled = true
		#最大物品堆叠大小 (1-9999) | Maximum item stack size (1-9999)
		#Range: 1 ~ 9999
		maxStackSize = 64

#内存优化设置 | Memory Optimization Settings
[memory_optimization]
	#内存清理间隔(秒) | Memory cleanup interval (seconds)
	#Range: 60 ~ 3600
	cleanInterval = 600
	#是否在清理时触发垃圾回收 | Whether to trigger garbage collection during cleanup
	enableGC = false

#调试设置 | Debug Settings
[debug]
	#启用调试日志 | Enable debug logging
	debug = false
	#记录方块相关事件 | Log block related events
	logBlockEvents = false

#区块优化设置 | Chunk Optimization Settings
[chunk_optimization]
	#主动卸载非活动区块 | Aggressively unload inactive chunks
	aggressiveChunkUnloading = true
	#区块卸载延迟 (秒) | Chunk unload delay (seconds)
	#Range: 10 ~ 600
	chunkUnloadDelay = 60
	#当玩家移动短距离时减少区块更新频率 | Reduce chunk update frequency when player moves short distances
	reduceChunkUpdates = true
	#过滤冗余的方块更新数据包 | Filter redundant block update packets
	filterRedundantBlockUpdates = true
	#异步区块生成的线程数 | Number of threads for async chunk generation
	#Range: 1 ~ 8
	chunkGenThreads = 2

#异步优化设置 | Async Optimization Settings
[async_optimization]
	#启用异步粒子处理 | Enable asynchronous particle processing
	asyncParticles = true
	#每tick处理的最大异步操作数 | Max async operations processed per tick
	#Range: 100 ~ 10000
	maxAsyncOpsPerTick = 1000
	#出错后禁用该事件类型的异步处理 | Disable async for event type after errors
	disableAsyncOnError = true
	#异步事件超时时间(秒) | Timeout in seconds for async events
	#Range: 1 ~ 10
	asyncEventTimeout = 2
	#等待异步事件完成 | Wait for async events to complete
	waitForAsyncEvents = false

	[async_optimization.async_cpu_config]
		#异步系统最大CPU核心数 | Max CPU Cores for async system (only for async threads, not world async)
		#Range: 2 ~ 9999
		maxCPUPro = 2
		#最大线程数 | Max Threads (only for general async threads, not dedicated async threads)
		#Range: 2 ~ 9999
		maxthreads = 2

#事件系统设置 | Event System Settings
[event_system]
	#启用高性能异步事件功能
	featureEnabled = true
	#不应异步处理的事件类列表 | List of event classes that should NOT be processed asynchronously
	#支持通配符 (如 'net.minecraftforge.event.entity.living.*') | Supports wildcards (e.g. 'net.minecraftforge.event.entity.living.*')
	classBlacklist = ["net.minecraftforge.event.TickEvent", "net.minecraftforge.event.level.LevelTickEvent", "net.minecraftforge.event.entity.living.*"]
	#不应异步处理的模组ID列表 | List of mod IDs whose events should NOT be processed asynchronously
	modBlacklist = []
	#启用严格的类存在检查 (推荐关闭为稳定性) | Enable strict class existence checking (recommended for stability)
	strictClassChecking = false

