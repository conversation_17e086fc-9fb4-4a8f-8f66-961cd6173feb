recipes:
  # Whether to generate Flawed and Chipped Gems for materials and recipes involving them.
  # Useful for mods like TerraFirmaCraft.
  # Default: false
  generateLowQualityGems: false

  # Whether to remove Block/Ingot compression and decompression in the Crafting Table.
  # Default: true
  disableManualCompression: false

  # Change the recipe of Rods in the Lathe to 1 Rod and 2 Small Piles of Dust, instead of 2 Rods.
  # Default: true
  harderRods: false

  # Whether to make crafting recipes for Bricks, Firebricks, Nether Bricks, and Coke Bricks harder.
  # Default: false
  harderBrickRecipes: false

  # Whether to nerf Wood crafting to 2 Planks from 1 Log, and 2 Sticks from 2 Planks.
  # Default: false
  nerfWoodCrafting: false

  # Whether to make Wood related recipes harder.
  # Excludes sticks and planks.
  # Default: false
  hardWoodRecipes: false

  # Recipes for Buckets, Cauldrons, Hoppers, and Iron Bars require Iron Plates, Rods, and more.
  # Default: true
  hardIronRecipes: false

  # Whether to make Redstone related recipes harder.
  # Default: false
  hardRedstoneRecipes: false

  # Whether to make Vanilla Tools and Armor recipes harder.
  # Excludes Flint and Steel, and Buckets.
  # Default: false
  hardToolArmorRecipes: false

  # Whether to make miscellaneous recipes harder.
  # Default: false
  hardMiscRecipes: false

  # Whether to make Glass related recipes harder. Default: true
  hardGlassRecipes: false

  # Whether to nerf the Paper crafting recipe.
  # Default: true
  nerfPaperCrafting: false

  # Recipes for items like Iron Doors, Trapdoors, Anvil require Iron Plates, Rods, and more.
  # Default: true
  hardAdvancedIronRecipes: false

  # Whether to make coloring blocks like Concrete or Glass harder.
  # Default: false
  hardDyeRecipes: false

  # Whether to remove charcoal smelting recipes from the vanilla furnace.
  # Default: true
  harderCharcoalRecipe: false

  # Whether to make the Flint and Steel recipe require steel parts.
  # Default: true.
  flintAndSteelRequireSteel: false

  # Whether to remove Vanilla Block Recipes from the Crafting Table.
  # Default: false
  removeVanillaBlockRecipes: false

  # Whether to remove Vanilla TNT Recipe from the Crafting Table.
  # Default: true
  removeVanillaTNTRecipe: false

  # How many Multiblock Casings to make per craft. Either 1, 2, or 3.
  # Default: 2
  casingsPerCraft: 2

  # Whether to nerf the output amounts of the first circuit in a set to 1 (from 2) and SoC to 2 (from 4).
  # Default: false
  harderCircuitRecipes: false

  # Whether to nerf machine controller recipes.
  # Default: false
  hardMultiRecipes: false

  # Whether tools should have enchants or not. Like the flint sword getting fire aspect.
  # Default: false
  enchantedTools: true

worldgen:
  # Rubber Tree spawn chance (decimal % per chunk)
  # Default: 0.5
  rubberTreeSpawnChance: 0.5

  # Should all Stone Types drop unique Ore Item Blocks?
  # Default: false (meaning only Stone, Netherrack, and Endstone)
  allUniqueStoneTypes: false

  # Should Sand-like ores fall?
  # This includes gravel, sand, and red sand ores.
  # Default: false (no falling ores)
  sandOresFall: false

  # Whether to increase number of rolls for dungeon chests. Increases dungeon loot drastically.
  # Default: true
  # WARNING: Currently unimplemented.
  increaseDungeonLoot: true

  # Allow GregTech to add additional GregTech Items as loot in various structures.
  # Default: true
  addLoot: true

  oreVeins:
    # The grid size (in chunks) for ore vein generation
    # Default: 3
    oreVeinGridSize: 3

    # The maximum random offset (in blocks) from the grid for generating an ore vein.
    # Default: 12
    oreVeinRandomOffset: 12

    # Prevents regular vanilla ores from being generated outside GregTech ore veins
    # Default: true
    removeVanillaOreGen: false

    # Prevents vanilla's large ore veins from being generated
    # Default: true
    removeVanillaLargeOreVeins: false

    # Distance between bedrock ore veins in chunks, if enabled.
    # Default: 16
    bedrockOreDistance: 16

    # Make bedrock ore/fluid veins infinite?
    # Default: false
    infiniteBedrockOresFluids: false

    # Generate ores indicators above ore veins
    # Default: true
    oreIndicators: true

    # Sets the maximum number of chunks that may be cached for ore vein generation.
    # Higher values may improve world generation performance, but at the cost of more RAM usage.
    # If you substantially increase the ore vein grid size, random vein offset, or have very large (custom) veins, you may need to increase this value as well.
    # Default: 512 (requires restarting the server / re-opening the world)
    oreGenerationChunkCacheSize: 512

    # Sets the maximum number of chunks for which ore indicators may be cached.
    # If you register any custom veins with very large indicator ranges (or modify existing ones that way), you may need to increase this value.
    # Default: 2048 (requires restarting the server / re-opening the world)
    oreIndicatorChunkCacheSize: 2048

machines:
  # Whether to require a Wrench, Wirecutter, or other GregTech tools to break machines, casings, wires, and more.
  # Default: true
  requireGTToolsForBlocks: false

  # Whether machines explode in rainy weather or when placed next to certain terrain, such as fire or lava
  # Default: false
  shouldWeatherOrTerrainExplosion: false

  # Energy use multiplier for electric items.
  # Default: 100
  energyUsageMultiplier: 100

  # Energy use multiplier for prospectors.
  # Default: 100
  prospectorEnergyUseMultiplier: 100

  # Whether machines or boilers damage the terrain when they explode.
  # Note machines and boilers always explode when overloaded with power or met with special conditions, regardless of this config.
  # Default: true
  doesExplosionDamagesTerrain: false

  # Enables Safe Active Transformers, removing their ability to explode if unformed while transmitting/receiving power.
  # Default: false
  harmlessActiveTransformers: false

  # Whether to play machine sounds while machines are active.
  # Default: true
  machineSounds: true

  # Duration in ticks that batching will try to reach.
  # Default: 100
  batchDuration: 100

  # Whether Steam Multiblocks should use Steel instead of Bronze.
  # Default: false
  steelSteamMultiblocks: false

  # Whether to enable the cleanroom, required for various recipes.
  # Default: true
  enableCleanroom: true

  # Whether multiblocks should ignore all cleanroom requirements.
  # This does nothing if enableCleanroom is false.
  # Default: false
  cleanMultiblocks: false

  # Block to replace mined ores with in the miner and multiblock miner.
  # Default: minecraft:cobblestone
  replaceMinedBlocksWith: minecraft:cobblestone

  # Whether to enable Assembly Line research for recipes.
  # Default: true
  enableResearch: true

  # Whether to enable the Maintenance Hatch, required for Multiblocks.
  # Default: true
  enableMaintenance: true

  # Whether to enable World Accelerators, which accelerate ticks for surrounding Tile Entities, Crops, etc.
  # Default: true
  enableWorldAccelerators: true

  # List of TileEntities that the World Accelerator should not accelerate.
  # GregTech TileEntities are always blocked.
  # Entries must be in a fully qualified format. For example: appeng.tile.networking.TileController
  # Default: none
  worldAcceleratorBlacklist:

  # Whether to use GT6-style pipe and cable connections, meaning they will not auto-connect unless placed directly onto another pipe or cable.
  # Default: true
  gt6StylePipesCables: true

  # Whether the machine's circuit slot need to be inserted a real circuit.
  ghostCircuit: true

  # Maximum number of failed recipe matches to cache per machine to improve performance.
  # Default: 100
  maxFailedRecipeCache: 100

  # Maximum number of failed recipes to check per tick to prevent lag.
  # Default: 10
  maxFailedRecipeChecksPerTick: 10

  # Threshold in milliseconds to log slow recipe serialization operations.
  # Default: 50
  slowSerializationThreshold: 50

  # Threshold in milliseconds to log slow recipe deserialization operations.
  # Default: 100
  slowDeserializationThreshold: 100

  # Threshold in milliseconds to log slow recipe search operations.
  # Default: 50
  slowRecipeSearchThreshold: 50

  # Whether to add a "Bedrock Ore Miner" (also enables bedrock ore generation)
  # Default: false
  doBedrockOres: false

  # What Kind of material should the bedrock ore miner output?
  # Default: "raw"
  bedrockOreDropTagPrefix: raw

  # The base amount of ticks per block for electric singleblock ore miners
  # Default: 320
  minerSpeed: 320

  # Makes nearly every GCYM Multiblock require blocks which set their maximum voltages.
  # Default: false
  enableTieredCasings: false

  # Minimum distance between Long Distance Item Pipe Endpoints
  # Default: 50
  ldItemPipeMinDistance: 50

  # Minimum distance betweeb Long Distance Fluid Pipe Endpoints
  # Default: 50
  ldFluidPipeMinDistance: 50

  # Whether ONLY owners can open a machine gui
  # Default: false
  onlyOwnerGUI: false

  # Whether ONLY owners can break a machine
  # Default: false
  onlyOwnerBreak: false

  # Minimum op level to bypass the ownership checks
  # Default: 2
  ownerOPBypass: 2

  # If High Tier (>UV-tier) GT content should be registered.
  # Items and Machines enabled with this config will have missing recipes by default.
  # This is intended for modpack developers only, and is not playable without custom tweaks or addons.
  # Other mods can override this to true, regardless of the config file.
  # Default: false
  highTierContent: false

  # Whether the Assembly Line should require the item inputs to be in order.
  # Default: true
  orderedAssemblyLineItems: true

  # Whether the Assembly Line should require the fluid inputs to be in order.
  # (Requires Ordered Assembly Line Item Inputs to be enabled.)
  # Default: true
  orderedAssemblyLineFluids: false

  # Default maximum parallel of steam multiblocks
  # Default: 8
  steamMultiParallelAmount: 8

  # Small Steam Boiler Options
  smallBoilers:
    # The amount of steam a Steam Solid Boiler produces per second at max temperature.
    # Default: 120
    solidBoilerBaseOutput: 120

    # The amount of steam a High Pressure Steam Solid Boiler produces per second at max temperature.
    # Default: 300
    hpSolidBoilerBaseOutput: 300

    # The amount of steam a Steam Liquid Boiler produces per second at max temperature.
    # Default: 240
    liquidBoilerBaseOutput: 240

    # The amount of steam a High Pressure Steam Liquid Boiler produces per second at max temperature.
    # Default: 600
    hpLiquidBoilerBaseOutput: 600

    # The amount of steam a Steam Solar Boiler produces per second at max temperature.
    # Default: 120
    solarBoilerBaseOutput: 120

    # The amount of steam a High Pressure Steam Solar Boiler produces per second at max temperature.
    # Default: 360
    hpSolarBoilerBaseOutput: 360

  # Large Steam Boiler Options
  largeBoilers:
    # The conversion rate between water and steam in Large Boilers.
    # Default: 160
    steamPerWater: 160

    # The max temperature of the Large Bronze Boiler.
    # Default: 800
    bronzeBoilerMaxTemperature: 800

    # The heat speed of the Large Bronze Boiler.
    # Default: 1
    bronzeBoilerHeatSpeed: 1

    # The max temperature of the Large Steel Boiler.
    # Default: 1800
    steelBoilerMaxTemperature: 1800

    # The heat speed of the Large Steel Boiler.
    # Default: 1
    steelBoilerHeatSpeed: 1

    # The max temperature of the Large Titanium Boiler.
    # Default: 3200
    titaniumBoilerMaxTemperature: 3200

    # The heat speed of the Large Titanium Boiler.
    # Default: 1
    titaniumBoilerHeatSpeed: 1

    # The max temperature of the Large Tungstensteel Boiler.
    # Default: 6400
    tungstensteelBoilerMaxTemperature: 6400

    # The heat speed of the Large Tungstensteel Boiler.
    # Default: 2
    tungstensteelBoilerHeatSpeed: 2

client:
  # Whether or not to enable Emissive Textures for GregTech Machines.
  # Default: true
  machinesEmissiveTextures: true

  # Whether or not sounds should be played when using tools outside of crafting.
  # Default: true
  toolUseSounds: true

  # Whether or not sounds should be played when crafting with tools.
  # Default: true
  toolCraftingSounds: true

  # The default color to overlay onto machines.
  # #FFFFFF is no coloring (default).
  # #D2DCFF is the classic blue from GT5.
  defaultPaintingColor: #FFFFFF

  # The default color to overlay onto Machine (and other) UIs.
  # #FFFFFF is no coloring (like GTCE) (default).
  # #D2DCFF is the classic blue from GT5.
  defaultUIColor: #FFFFFF

  # Use VBO cache for multiblock preview.
  # Disable if you have issues with rendering multiblocks.
  # Default: true
  useVBO: true

  # Duration of the multiblock in-world preview (s)
  # Default: 10
  inWorldPreviewDuration: 10

  # Duration of UI animations in ms
  # Default: 300
  animationTime: 300

  armorHud:
    # Sets HUD location
    # 1 - left-upper corner
    # 2 - right-upper corner
    # 3 - left-bottom corner
    # 4 - right-bottom corner
    # Default: 1
    hudLocation: 1

    # Horizontal offset of HUD.
    # Default: 0
    hudOffsetX: 0

    # Vertical offset of HUD.
    # Default: 0
    hudOffsetY: 0

  renderer:
    # Render fluids in multiblocks that support them?
    # Default: true
    renderFluids: true

    # Whether or not to color tiered machine highlights in the tier color
    # Default: true
    coloredTieredMachineOutline: true

    # Whether or not to color wire/cable highlights based on voltage tier
    # Default: true
    coloredWireOutline: true

# Config options for Tools and Armor
tools:
  # Random chance for electric tools to take actual damage
  # Default: 10%
  rngDamageElectricTools: 10

  # Amount of blocks that can be spray painted at once
  # Default: 16
  sprayCanChainLength: 16

  # Delay in ticks between each log being broken when tree felling
  # Default: 2
  treeFellingDelay: 2

  # NanoSaber Options
  nanoSaber:
    # The additional damage added when the NanoSaber is powered.
    # Default: 20.0
    nanoSaberDamageBoost: 20.0

    # The base damage of the NanoSaber.
    # Default: 5.0
    nanoSaberBaseDamage: 5.0

    # Should Zombies spawn with charged, active NanoSabers on hard difficulty?
    # Default: true
    zombieSpawnWithSabers: true

    # The EU/t consumption of the NanoSaber.
    # Default: 64
    energyConsumption: 64

  # NightVision Goggles Voltage Tier. Default: 1 (LV)
  voltageTierNightVision: 1

  # NanoSuit Voltage Tier. Default: 3 (HV)
  voltageTierNanoSuit: 3

  # Advanced NanoSuit Chestplate Voltage Tier.
  # Default: 3 (HV)
  voltageTierAdvNanoSuit: 3

  # QuarkTech Suit Voltage Tier.
  # Default: 5 (IV)
  voltageTierQuarkTech: 5

  # Advanced QuarkTech Suit Chestplate Voltage Tier.
  # Default: 5 (LuV)
  voltageTierAdvQuarkTech: 6

  # Electric Impeller Jetpack Voltage Tier.
  # Default: 2 (MV)
  voltageTierImpeller: 2

  # Advanced Electric Jetpack Voltage Tier.
  # Default: 3 (HV)
  voltageTierAdvImpeller: 3

# Config options for Game Mechanics
gameplay:
  # Enable hazardous materials
  # Default: true
  hazardsEnabled: false

  # Whether hazards are applied to all valid items, or just GT's.
  # true = all, false = GT only.
  # Default: true
  universalHazards: false

  # Whether environmental hazards like pollution or radiation are active
  # Default: false
  environmentalHazards: false

  # How much environmental hazards decay per chunk, per tick.
  # Default: 0.001
  environmentalHazardDecayRate: 0.001

# Config options for Mod Compatibility
compat:
  # Config options regarding GTEU compatibility with other energy systems
  energy:
    # Enable Native GTEU to Forge Energy (RF and alike) on GT Cables and Wires.
    # This does not enable nor disable Converters.
    # Default: true
    nativeEUToFE: true

    # Enable GTEU to FE (and vice versa) Converters.
    # Default: false
    enableFEConverters: true

    # Forge Energy to GTEU ratio for converting FE to EU.
    # Only affects converters.
    # Default: 4 FE == 1 EU
    feToEuRatio: 4

    # GTEU to Forge Energy ratio for converting EU to FE.
    # Affects native conversion and Converters.
    # Default: 4 FE == 1 EU
    euToFeRatio: 4

  # Config options regarding GTCEu compatibility with AE2
  ae2:
    # The interval between ME Hatch/Bus interact ME network.
    # It may cause lag if the interval is too small.
    # Default: 2 sec
    updateIntervals: 40

    # The energy consumption of ME Hatch/Bus.
    # Default: 4.0AE/t
    meHatchEnergyUsage: 1.0

  # Config options regarding GTCEu compatibility with minimap mods
  minimap:
    # Toggle specific map mod integration on/off (need to restart for this to take effect)
    toggle:
      # FTB Chunks integration enabled
      ftbChunksIntegration: false

      # Journey Map integration enabled
      journeyMapIntegration: true

      # Xaerox's map integration enabled
      xaerosMapIntegration: true

    # The radius, in blocks, that picking up a surface rock will search for veins in.
    # -1 to disable.
    # Default: 24
    surfaceRockProspectRange: 24

    # The radius, in blocks, that clicking an ore block will search for veins in.
    # -1 to disable
    # Default: 24
    oreBlockProspectRange: 24

    # The map scale at which displayed ores will stop scaling.
    oreScaleStop: 1.0

    # The size, in pixels, of ore icons on the map
    oreIconSize: 32

    # The string prepending ore names in the ore vein tooltip
    oreNamePrefix: -

    # The color to draw a box around the ore icon with.
    # Accepts either an ARGB hex color prefixed with # or the string 'material' to use the ore's material color
    borderColor: #00000000

    # Which part of the screen to anchor buttons to
    # Default: "BOTTOM_LEFT"
    # Allowed values:
    # - TOP_LEFT
    # - TOP_CENTER
    # - TOP_RIGHT
    # - RIGHT_CENTER
    # - BOTTOM_RIGHT
    # - BOTTOM_CENTER
    # - BOTTOM_LEFT
    # - LEFT_CENTER
    buttonAnchor: BOTTOM_LEFT

    # Which direction the buttons will go
    # Default: "VERTICAL"
    # Allowed values:
    # - VERTICAL
    # - HORIZONTAL
    direction: HORIZONTAL

    # How horizontally far away from the anchor to place the buttons
    # Default: 20
    xOffset: 20

    # How vertically far away from the anchor to place the buttons
    # Default: 0
    yOffset: 0

  # Whether to hide facades of all blocks in JEI and creative search menu.
  # Default: true
  hideFacadesInRecipeViewer: true

  # Whether to hide filled cells in JEI and creative search menu.
  # Default: true
  hideFilledCellsInRecipeViewer: true

  # Whether to hide the ore processing diagrams in JEI
  # Default: false
  hideOreProcessingDiagrams: false

  # Whether Gregtech should remove smelting recipes from the vanilla furnace for ingots requiring the Electric Blast Furnace.
  # Default: true
  removeSmeltingForEBFMetals: true

  # Whether dimension markers should show the dimension tier value.
  # Default: false
  showDimensionTier: false

dev:
  # Debug general events? (will print recipe conficts etc. to server's debug.log)
  # Default: false
  debug: false

  # Debug ore vein placement? (will print placed veins to server's debug.log)
  # Default: false (no placement printout in debug.log)
  debugWorldgen: false

  # Generate ores in superflat worlds?
  # Default: false
  doSuperflatOres: true

  # Dump all registered GT recipes?
  # Default: false
  dumpRecipes: false

  # Dump all registered GT models/blockstates/etc?
  # Default: false
  dumpAssets: false

