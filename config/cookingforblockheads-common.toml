#If true, a cow can be squished into a Milk Jar by dropping an anvil on top.
cowJarEnabled = true
#The amount of milk the cow in a jar generates per tick.
#Range: > -2147483648
cowJarMilkPerTick = 1
#Ex Compressum compatibility. Multiplier applied to the milk per tick for Compressed Cow in a Jar.
#Range: -1.7976931348623157E308 ~ 1.7976931348623157E308
compressedCowJarMilkMultiplier = 9.0
#Set this to true if you'd like the sink to require water to be piped in, instead of providing infinite of it.
sinkRequiresWater = false
#Enabling this will make the kitchen counters have twice as much inventory space.
largeCounters = false
#Set this to true if you'd like to disallow automation of the oven (pipes and such won't be able to insert/extract)
disallowOvenAutomation = false
#The fuel multiplier for the cooking oven. Higher values means fuel lasts longer, 1.0 is furnace default.
#Range: -1.7976931348623157E308 ~ 1.7976931348623157E308
ovenFuelTimeMultiplier = 0.33000001311302185
#The cooking time multiplier for the cooking oven. Higher values means it will take longer.
#Range: -1.7976931348623157E308 ~ 1.7976931348623157E308
ovenCookTimeMultiplier = 1.0
#Set this to true if you'd like the oven to only accept cooking oil as fuel (requires Pam's Harvestcraft)
ovenRequiresCookingOil = false
#Set to false if you don't want ingredients to be marked with a special icon in the recipe book.
showIngredientIcon = true
#Toasting toasted bread again will turn into charcoal (only if no other mod adding toast is present). Set to false to disable.
allowVeryToastedBread = true

