
[General]
	#Allows a player to teleport to a located biome when in creative mode, opped, or in cheat mode.
	allowTeleport = true
	#Allows players to view the precise coordinates and distance of a located structure on the HUD, rather than relying on the direction the compass is pointing.
	displayCoordinates = true
	#biomeSize * radiusModifier = maxSearchRadius. Raising this value will increase search accuracy but will potentially make the process more resource .
	#Range: 0 ~ 1000000
	radiusModifier = 2500
	#biomeSize * sampleSpaceModifier = sampleSpace. Lowering this value will increase search accuracy but will make the process more resource intensive.
	#Range: 0 ~ 1000000
	sampleSpaceModifier = 16
	#A list of biomes that the compass will not be able to search for, specified by resource location. The wildcard character * can be used to match any number of characters, and ? can be used to match one character. Ex: ["minecraft:savanna", "minecraft:desert", "minecraft:*ocean*"]
	biomeBlacklist = []
	#The maximum number of samples to be taken when searching for a biome.
	#Range: 0 ~ 1000000
	maxSamples = 50000

