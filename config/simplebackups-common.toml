#If set false, no backups are being made.
enabled = true
#Defines the backup type.
#- FULL_BACKUPS - always creates full backups
#- MODIFIED_SINCE_LAST - only saves the files which changed since last (partial) backup
#- MODIFIED_SINCE_FULL - saves all files which changed after the last full backup
#Allowed Values: FULL_BACKUPS, MOD<PERSON>IED_SINCE_LAST, MODIFIED_SINCE_FULL
backupType = "FULL_BACKUPS"
#How often should a full backup be created if only modified files should be saved? This creates a full backup when x minutes are over and the next backup needs to be done. Once a year is default.
#Range: 1 ~ 5259600
fullBackupTimer = 525960
#The max amount of backup files to keep.
#Range: 1 ~ 32767
backupsToKeep = 10
#The time between two backups in minutes
#5 = each 5 minutes
#60 = each hour
#1440 = each day
#Range: 1 ~ 32767
timer = 120
#The compression level, 0 is no compression (less cpu usage) and takes a lot of space, 9 is best compression (most cpu usage) and takes less space. -1 is default
#Range: -1 ~ 9
compressionLevel = -1
#Should message be sent when backup is in the making?
sendMessages = true
#The max size of storage the backup folder. If it takes more storage, old files will be deleted.
#Needs to be written as <number><space><storage type>
#Valid storage types: B, KB, MB, GB, TB
maxDiskSize = "25 GB"
#Used to define the output path.
outputPath = "simplebackups"
#Create backups, even if nobody is online
noPlayerBackups = false
#Should sub-directories be generated for each world?
#Keep in mind that all configs above, including backupsToKeep and maxDiskSize, will be calculated for each sub directory.
createSubDirs = false
#Use an internal tick counter instead of the real world time. The value of the timer will be converted to ticks. When the timer is over, the backup will be created.
#Keep in mind that lagging servers will result in larger gaps between two backups, e.g. 10 FPS in average will result in double the time set between backups.
useTickCounter = false

[mod_compat]
	#Should backup notifications be sent to Discord by using mc2discord? (needs to be installed)
	mc2discord = true

