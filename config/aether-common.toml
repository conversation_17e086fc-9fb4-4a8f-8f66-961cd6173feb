
[Gameplay]
	#Use the default Curios menu instead of the Aether's Accessories Menu. WARNING: Do not enable this without emptying your equipped accessories
	"Use default Curios' menu" = false
	#On world creation, the player is given an Aether Portal Frame item to automatically go to the Aether with
	"Gives player Aether Portal Frame item" = false
	#When the player enters the Aether, they are given a Book of Lore and Golden Parachutes as starting loot
	"Gives starting loot on entry" = true
	#Moves the message for when a player attacks the Slider with an incorrect item to be above the hotbar instead of in chat
	"Reposition attack message above hotbar" = false
	#Determines whether the Sun Spirit's dialogue when meeting him should play through every time you meet him
	"Repeat Sun Spirit's battle dialogue" = true
	#Determines if a message that links The Aether mod's Patreon should show
	"Show Patreon message" = true

["Data Pack"]
	#Sets the Aether Temporary Freezing data pack to be added to new worlds automatically
	"Add Temporary Freezing automatically" = false
	#Sets the Aether Ruined Portals data pack to be added to new worlds automatically
	"Add Ruined Portals automatically" = false

[Modpack]
	#Determines whether bosses should display a randomized name above their boss bar
	"Randomize boss names" = true

