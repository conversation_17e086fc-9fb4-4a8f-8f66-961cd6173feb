#ME Extend Import/Export Bus speed multiplier
#Range: 2 ~ 128
exBusMultiplier = 8
#ME Infinity Cell idle energy cost (unit: AE/t)
#Range: 0.1 ~ 64.0
cost = 8.0
#The max range between two wireless connector
#Range: 10.0 ~ 10000.0
range = 1000.0
#ME Infinity Cell types (item or fluid's id)
types = ["minecraft:water", "minecraft:cobblestone"]
#The AE device/part that can be packed by ME Packing Tape
whitelist = ["expatternprovider:ex_interface_part", "expatternprovider:ex_pattern_provider_part", "expatternprovider:ex_interface", "expatternprovider:ex_pattern_provider", "expatternprovider:ex_drive", "ae2:cable_interface", "ae2:cable_pattern_provider", "ae2:interface", "ae2:pattern_provider", "ae2:drive"]
#Disable Extended Inscriber's item render, it only works in client side.
disableItemRender = false

[device]
	#Size multiplier of oversize interface
	#Range: 2 ~ 4096
	oversize_interface_multiplier = 16
	#Set multiplier for specific AEKeyType in oversize interface
	custom_oversize_interface_multiplier = ["appbot:mana 2", "appflux:flux 4"]
	#The max size of Assembler Matrix
	#Range: 3 ~ 16
	assembler_matrix_max_size = 6

