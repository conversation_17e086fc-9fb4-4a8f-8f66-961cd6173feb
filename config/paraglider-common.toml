
#Easy to access switches to toggle side features on and off.
#Most of them requires server restart or datapack reload. All of them, actually.
[features]
	#For those who wants to remove Spirit Orbs generated in the world, more specifically...
	#  * Spirit Orbs generated in various chests
	#  * Spirit Orbs dropped by spawners and such
	#Note that bargain recipe for Heart Containers/Stamina Vessels will persist, even if this option is disabled.
	spiritOrbGens = true
	#For those who wants to remove entirety of Heart Containers from the game, more specifically...
	#  * Heart Containers obtained by "challenges" (i.e. Killing dragon, wither, raid)
	#  * Bargains using Heart Containers (custom recipes won't be affected)
	#Note that if this option is disabled while staminaVessels is enabled, "challenges" will drop stamina vessels instead.
	heartContainers = true
	#For those who wants to remove entirety of Stamina Vessels from the game, more specifically...
	#  * Bargains using Stamina Vessels (custom recipes won't be affected)
	staminaVessels = true
	#For those who wants to remove all structures added by this mod. Requires restart.
	structures = true

[debug]
	debugPlayerMovement = false
	traceMovementPacket = false
	traceVesselPacket = false
	traceBargainPacket = false
	traceWindPacket = false

