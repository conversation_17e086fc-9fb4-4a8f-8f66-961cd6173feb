{"general": {"unsupportedDeveloperTools": false, "matterCannonBlockDamage_comment": "Enables the ability of the Matter Cannon to break blocks.", "matterCannonBlockDamage": true, "tinyTntBlockDamage_comment": "Enables the ability of Tiny TNT to break blocks.", "tinyTntBlockDamage": true, "channels_comment": "Changes the channel capacity that cables provide in AE2.", "channels": "default", "spatialAnchorEnableRandomTicks_comment": "Whether Spatial Anchors should force random chunk ticks and entity spawning.", "spatialAnchorEnableRandomTicks": false}, "automation": {"formationPlaneEntityLimit": 128}, "facades": {"allowBlockEntities_comment": "Unsupported: Allows whitelisting block entities as facades. Could work, have render issues, or corrupt your world. USE AT YOUR OWN RISK.", "allowBlockEntities": false}, "craftingCPU": {"craftingCalculationTimePerTick": 5, "craftingSimulatedExtraction_comment": "When true: simulate extraction of all the network's contents when starting a crafting job calculation. When false: use the cached available content list (same as terminals). Enabling might work a bit better, but it will significantly reduce performance.", "craftingSimulatedExtraction": false}, "crafting": {"disassemblyCrafting_comment": "Enable shift-clicking with the crafting units in hand to disassemble them.", "disassemblyCrafting": true, "growthAccelerator_comment": "Number of ticks between two crystal growth accelerator ticks", "growthAccelerator": 10}, "spatialio": {"spatialPowerMultiplier": 1250.0, "spatialPowerExponent": 1.35}, "logging": {"blockUpdateLog": false, "packetLog": false, "craftingLog": false, "debugLog": false, "gridLog": false, "chunkLoggerTrace_comment": "Enable stack trace logging for the chunk loading debug command", "chunkLoggerTrace": false}, "battery": {"chargerChargeRate_comment": "The chargers charging rate factor, which is applied to the charged items charge rate. 2 means it charges everything twice as fast. 0.5 half as fast.", "chargerChargeRate": 1.0, "wirelessTerminal": 1600000, "chargedStaff": 8000, "entropyManipulator": 200000, "portableCell": 20000, "colorApplicator": 20000, "matterCannon": 200000}, "worldGen": {"spawnPressesInMeteorites": true, "spawnFlawlessOnly": false}, "wireless": {"wirelessBaseCost": 8.0, "wirelessCostMultiplier": 1.0, "wirelessBaseRange": 16.0, "wirelessBoosterRangeMultiplier": 1.0, "wirelessBoosterExp": 1.5, "wirelessHighWirelessCount": 64.0, "wirelessTerminalDrainMultiplier": 1.0}, "PortableCells": {"allowDisassembly_comment": "Allow disassembly of portable cells into the recipe ingredients using shift+right-click", "allowDisassembly": true}, "PowerRatios": {"ForgeEnergy": 0.5, "UsageMultiplier": 1.0, "GridEnergyStoragePerNode_comment": "How much energy can the internal grid buffer storage per node attached to the grid.", "GridEnergyStoragePerNode": 25.0, "CrystalResonanceGeneratorRate_comment": "How much energy a crystal resonance generator generates per tick.", "CrystalResonanceGeneratorRate": 20.0, "p2pTunnelEnergyTax_comment": "The cost to transport energy through an energy P2P tunnel expressed as a factor of the transported energy.", "p2pTunnelEnergyTax": 0.025, "p2pTunnelTransportTax_comment": "The cost to transport items/fluids/etc. through P2P tunnels, expressed in AE energy per equivalent I/O bus operation for the transported object type (i.e. items=per 1 item, fluids=per 125mb).", "p2pTunnelTransportTax": 0.025}, "Condenser": {"MatterBalls": 256, "Singularity": 256000}, "tickRates": {"_comment": " Min / Max Tickrates for dynamic ticking, most of these components also use sleeping, to prevent constant ticking, adjust with care, non standard rates are not supported or tested.", "InterfaceMin": 5, "InterfaceMax": 120, "ImportBusMin": 5, "ImportBusMax": 40, "ExportBusMin": 5, "ExportBusMax": 60, "AnnihilationPlaneMin": 2, "AnnihilationPlaneMax": 120, "METunnelMin": 5, "METunnelMax": 20, "InscriberMin": 1, "InscriberMax": 20, "ChargerMin": 10, "ChargerMax": 10, "IOPortMin": 1, "IOPortMax": 5, "VibrationChamberMin": 10, "VibrationChamberMax": 40, "StorageBusMin": 5, "StorageBusMax": 60, "ItemTunnelMin": 5, "ItemTunnelMax": 60, "LightTunnelMin": 5, "LightTunnelMax": 60}, "vibrationChamber": {"_comment": "Settings for the Vibration Chamber", "baseEnergyPerFuelTick_comment": "AE energy produced per fuel burn tick (reminder: coal = 1600, block of coal = 16000, lava bucket = 20000 burn ticks)", "baseEnergyPerFuelTick": 5.0, "minEnergyPerGameTick_comment": "Minimum amount of AE/t the vibration chamber can slow down to when energy is being wasted.", "minEnergyPerGameTick": 4, "baseMaxEnergyPerGameTick_comment": "Maximum amount of AE/t the vibration chamber can speed up to when generated energy is being fully consumed.", "baseMaxEnergyPerGameTick": 40}}