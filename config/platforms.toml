#Sets Frame climbing behaviour
#NONE: Can not be climbed
#LADDER: Ascend via walking. Descends automatically. <PERSON><PERSON><PERSON> holds position.
#SCAFFOLD: Ascend via jumping. Descend via sneaking. Automatically holds position.
#HYBRID: Ascend via walking. Descend via sneaking. Automatically holds position.
#Allowed Values: NONE, LADDER, SCAFFOLD, HYBRID
frameClimbMode = "HYBRID"

#Craft Output Control
[crafting]
	#Multiplies the craft amount by the given value if a 'compressed' block is used (2x value if two are used)
	#See 'data/forge/tags/items/storage_blocks/' to know what qualifies
	#Note: Final value will be capped at a full stack
	#Range: 1 ~ 16
	"'Compressed' Block Multiplier" = 4
	#Range: 1 ~ 16
	Flat = 4
	#Range: 1 ~ 16
	Floor = 2
	#Range: 1 ~ 16
	Inclines = 2
	#Range: 1 ~ 16
	Roofs = 2
	#Range: 1 ~ 16
	Frame = 2
	#Range: 1 ~ 16
	Railings = 4

#Platform Dust Covering Options
[covering]
	#Enables the manual placement of sand, snow, and gravel via sneak place
	#Other blocks are supported as long as the extend FallingBlock and use Material.SAND
	canManuallyAdd = true
	#When enabled the amount of snow/sand built up on platforms will change with the weather
	isEnvironmentReactive = true
	#When enabled walking on platforms will disturb the collected snow/sand
	#Regardless of setting you can still remove layers by right clicking with a shovel
	isEntityReactive = true
	#1 in x chance sand will passively accumulate
	#Disabled when set to zero, or when 'isEnvironmentReactive' is disabled
	#Range: 0 ~ 1000
	chanceSand = 4
	#1 in x chance sand will accumulate during a storm
	#Disabled when set to zero, or when 'isEnvironmentReactive' is disabled
	#Range: 0 ~ 1000
	chanceSandStorm = 3
	#1 in x chance snow will accumulate while snowing
	#Disabled when set to zero, or when 'isEnvironmentReactive' is disabled
	#Range: 0 ~ 1000
	chanceSnowStorm = 3
	#1 in x chance rain will wash away dust layers
	#Disabled when set to zero, or when 'isEnvironmentReactive' is disabled
	#Range: 0 ~ 1000
	chanceRainWash = 3

