
["Stargate Journey Common Config"]

	["Stargate Journey Common Config".server]
		#Stargate Journey General Config
		#Disable energy requirements for blocks added by Stargate Journey
		disable_energy_requirements = true

	["Stargate Journey Common Config"."ZPM Config"]

		["Stargate Journey Common Config"."ZPM Config".server]
			#The energy that can be extracted from a single level of entropy
			#Range: 1 ~ 9223372036854775807
			zpm_energy_per_level_of_entropy = 100000000000
			#Maximum amount of energy that can be transferred from the ZPM Hub in one tick
			#Range: 1 ~ 9223372036854775807
			zpm_hub_max_transfer = 100000000000

	["Stargate Journey Common Config"."Interface Config"]

		["Stargate Journey Common Config"."Interface Config".server]
			#The default amount of energy the Interface will try to provide until it stops
			#Range: 1 ~ 2147483647
			default_energy_target = 200000
			#The amount of energy a Basic Interface can hold
			#Range: 1 ~ 2147483647
			basic_interface_capacity = 10000000
			#The maximum amount of energy a Basic Interface can transfer at once
			#Range: 1 ~ 2147483647
			basic_interface_max_transfer = 100000
			#The amount of energy a Crystal Interface can hold
			#Range: 1 ~ 2147483647
			crystal_interface_capacity = 100000000
			#The maximum amount of energy a Crystal Interface can transfer at once
			#Range: 1 ~ 2147483647
			crystal_interface_max_transfer = 10000000
			#The amount of energy an Advanced Crystal Interface can hold
			#Range: 1 ~ 2147483647
			advanced_crystal_interface_capacity = 1000000000
			#The maximum amount of energy an Advanced Crystal Interface can transfer at once
			#Range: 1 ~ 2147483647
			advanced_crystal_interface_max_transfer = 100000000

	["Stargate Journey Common Config"."Stargate Config"]

		["Stargate Journey Common Config"."Stargate Config".server]
			#If true, Stargate will load the chunk it's in while it's connected to another Stargate
			stargate_loads_chunk_when_connected = true
			#The maximum amount of time the Stargate will be open for in seconds
			#Range: 10 ~ 2280
			max_wormhole_open_time = 228
			#If false, the Wormhole connection can only be ended from the dialing side
			end_connection_from_both_ends = true
			#ENABLED - Two way travel possible; CREATIVE_ONLY - Two way travel limited to Players in Creative Mode; DISABLED - Two way travel impossible
			#Allowed Values: ENABLED, CREATIVE_ONLY, DISABLED
			two_way_wormholes = "CREATIVE_ONLY"
			#If true, going through the wrong side of the wormhole will result in death
			reverse_wormhole_kills = true
			#If true, allow the destruction of Blocks by Kawooshes
			kawoosh_destroys_blocks = true
			#If true, allow the disintegration of Entities by Kawooshes
			kawoosh_disintegrates_entities = true
			#If true, allow the disintegration of Items by Kawooshes
			kawoosh_disintegrates_items = true
			#Decides if 8-chevron addresses can be used for dialing within the same galaxy
			allow_interstellar_8_chevron_addresses = false
			#Decides if two Stargates from the same Solar System should be able to connect
			allow_system_wide_connections = true
			#Enables the use of redstone for manual Stargate dialing
			enable_redstone_dialing = true
			#The maximum amount of blocks allowed within the ring area before Stargate becomes obstructed
			#Range: 1 ~ 21
			max_obstructive_blocks = 12
			#Enables choosing the Address when first creating a Stargate by right-clicking it with a renamed Control Crystal
			enable_address_choice = false
			#Enables upgrading the Classic Stargate
			enable_classic_stargate_upgrades = true
			#Enables creating Stargate Variants
			enable_stargate_variants = true
			#If true, Stargate item will always display the 9-Chevron Address of the Stargate in the inventory
			always_display_stargate_id = false
			#If true, the Universe Stargate will always rotate in the best direction; If false, the Universe Stargate will alternate between rotation directions
			universe_stargate_best_direction = true
			#If true, the Universe Stargate will rotate faster (Fast full rotation takes 108 ticks, slow full toration takes 162 ticks)
			universe_fast_rotation = true
			#FAST - Incoming Chevrons take 4 Ticks to lock; MEDIUM - Incoming Chevrons take 8 Ticks to lock; SLOW - Incoming Chevrons take 12 Ticks to lock
			#Allowed Values: SLOW, MEDIUM, FAST
			universe_chevron_lock_speed = "SLOW"
			#FAST - Incoming Chevrons take 4 Ticks to lock; MEDIUM - Incoming Chevrons take 8 Ticks to lock; SLOW - Incoming Chevrons take 12 Ticks to lock
			#Allowed Values: SLOW, MEDIUM, FAST
			milky_way_chevron_lock_speed = "SLOW"
			#FAST - Incoming Chevrons take 4 Ticks to lock; MEDIUM - Incoming Chevrons take 8 Ticks to lock; SLOW - Incoming Chevrons take 12 Ticks to lock
			#Allowed Values: SLOW, MEDIUM, FAST
			pegasus_chevron_lock_speed = "MEDIUM"
			#FAST - Incoming Chevrons take 4 Ticks to lock; MEDIUM - Incoming Chevrons take 8 Ticks to lock; SLOW - Incoming Chevrons take 12 Ticks to lock
			#Allowed Values: SLOW, MEDIUM, FAST
			classic_chevron_lock_speed = "SLOW"
			#FAST - Incoming Chevrons take 4 Ticks to lock; MEDIUM - Incoming Chevrons take 8 Ticks to lock; SLOW - Incoming Chevrons take 12 Ticks to lock
			#Allowed Values: SLOW, MEDIUM, FAST
			tollan_chevron_lock_speed = "MEDIUM"
			#The maximum connection time can be extended by increasing the energy input
			enable_energy_bypass = false
			#If true, the wormhole will draw power from both connected Stargates
			can_draw_power_from_both_ends = false
			#The amount of energy cost of keeping the wormhole open each tick for system-wide connections
			#Range: 0 ~ 9223372036854775807
			system_wide_connection_energy_draw = 5
			#The amount of energy cost of keeping the wormhole open each tick for interstellar connections
			#Range: 0 ~ 9223372036854775807
			interstellar_connection_energy_draw = 50
			#The amount of energy cost of keeping the wormhole open each tick for intergalactic connections
			#Range: 0 ~ 9223372036854775807
			intergalactic_connection_energy_draw = 50000
			#The amount of energy required to establish a connection inside a solar system
			#Range: 0 ~ 9223372036854775807
			system_wide_connection_energy_cost = 50000
			#The amount of energy required to establish a connection inside the galaxy
			#Range: 0 ~ 9223372036854775807
			interstellar_connection_energy_cost = 100000
			#The amount of energy required to establish a connection outside the galaxy
			#Range: 0 ~ 9223372036854775807
			intergalactic_connection_energy_cost = 100000000000
			#The maximum amount of energy the Stargate can hold
			#Range: 0 ~ 9223372036854775807
			stargate_energy_capacity = 1000000000000
			#The maximum amount of energy the Stargate can receive at once
			#Range: 0 ~ 9223372036854775807
			stargate_energy_max_receive = 1000000000000
			#The energy required to keep the Stargate open after exceeding the maximum open time is multiplied by this number
			#Range: > 1
			energy_bypass_multiplier = 100000

	["Stargate Journey Common Config"."Iris Config"]

		["Stargate Journey Common Config"."Iris Config".server]
			#If true, players in Creative Mode will be able to pass through the Stargate even when the Iris is closed on the other side
			creative_ignores_iris = false
			#The Iris can break any Blocks with Block Strength below the Iris Strength
			#Range: 0.0 ~ 1.7976931348623157E308
			iris_breaking_strength = 0.5
			#Durability of the Copper Iris
			#Range: > 1
			copper_iris_durability = 1024
			#Durability of the Iron Iris
			#Range: > 1
			iron_iris_durability = 2048
			#Durability of the Gold Iris
			#Range: > 1
			gold_iris_durability = 1024
			#Durability of the Diamond Iris
			#Range: > 1
			diamond_iris_durability = 8192
			#Durability of the Netherite Iris
			#Range: > 1
			netherite_iris_durability = 16384
			#Durability of the Naquadah Alloy Iris
			#Range: > 1
			naquadah_alloy_iris_durability = 8192
			#Durability of the Trinium Iris
			#Range: > 1
			trinium_iris_durability = 16384
			#Durability of the Bronze Iris
			#Range: > 1
			bronze_iris_durability = 4096
			#Durability of the Steel Iris
			#Range: > 1
			steel_iris_durability = 4096

	["Stargate Journey Common Config"."Transmission Config"]

		["Stargate Journey Common Config"."Transmission Config".server]
			#Max number of times a transmission can be forwarded before it disappears
			#Range: 1 ~ 16
			max_transmission_jumps = 1
			#Max distance from which the GDO transmission can reach a transmission receiver
			#Range: 1 ~ 128
			max_gdo_transmission_distance = 20
			#Max distance from which the Transceiver transmission can reach a transmission receiver
			#Range: 1 ~ 128
			max_transceiver_transmission_distance = 20
			#Max distance from which the Stargate transmission can reach a transmission receiver
			#Range: 1 ~ 128
			max_stargate_transmission_distance = 20

	["Stargate Journey Common Config"."Naquadah Generator Config"]

		["Stargate Journey Common Config"."Naquadah Generator Config".server]
			#The maximum amount of fuel stored in a single Naquadah Fuel Rod
			#Range: > 1
			naquadah_rod_max_fuel = 256
			#The amount of time in ticks for which the Mark I Naquadah Generator will generate energy from one unit of fuel
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_i_reaction_time = 100
			#The amount of FE generated per one tick of reaction by the Mark I Naquadah Generator
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_i_energy_per_tick = 100
			#The amount of energy a Mark I Naquadah Generator can hold
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_i_capacity = 100000
			#The maximum amount of energy a Mark I Naquadah Generator can transfer at once
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_i_max_transfer = 100000
			#The amount of time in ticks for which the Mark II Naquadah Generator will generate energy from one unit of fuel
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_ii_reaction_time = 150
			#The amount of FE generated per one tick of reaction by the Mark II Naquadah Generator
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_ii_energy_per_tick = 120
			#The amount of energy a Mark II Naquadah Generator can hold
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_ii_capacity = 1000000
			#The maximum amount of energy a Mark II Naquadah Generator can transfer at once
			#Range: 1 ~ 9223372036854775807
			naquadah_generator_mark_ii_max_transfer = 1000000

	["Stargate Journey Common Config"."Stargate Network Config"]

		["Stargate Journey Common Config"."Stargate Network Config".server]
			#Stargate Network will use addresses from datapacks
			use_datapack_addresses = true
			#Stargate Network will generate random Solar System for each dimension not registered through a datapack
			generate_random_solar_systems = true
			#Stargate Network will randomize addresses based on the world seed
			random_addresses_from_seed = true
			#Stargates Network will not consider DHDs when choosing preferred Stargate
			disable_dhd_preference = false

	["Stargate Journey Common Config"."Generation Config"]

		["Stargate Journey Common Config"."Generation Config".server]
			#X chunk center offset of structures that contain a Stargate
			#Range: -512 ~ 512
			stargate_generation_center_x_chunk_offset = 0
			#Z chunk center offset of structures that contain a Stargate
			#Range: -512 ~ 512
			stargate_generation_center_z_chunk_offset = 0
			#X chunk bounds within which a Structure containing a Stargate may generate
			#Range: 0 ~ 64
			stargate_generation_x_bound = 64
			#Z chunk bounds within which a Structure containing a Stargate may generate
			#Range: 0 ~ 64
			stargate_generation_z_bound = 64
			#X chunk bounds within which a Buried Stargate may generate
			#Range: 0 ~ 64
			buried_stargate_generation_x_bound = 64
			#Z chunk bounds within which a Buried Stargate may generate
			#Range: 0 ~ 64
			buried_stargate_generation_z_bound = 64

	["Stargate Journey Common Config"."Tech Config"]

		["Stargate Journey Common Config"."Tech Config".server]
			#If true Kara Kesh won't require its user to have Naquadah in their bloodstream
			disable_kara_kesh_requirements = true
			#The amount of Heavy Liquid Naquadah a Personal Shield can hold
			#Range: 1 ~ 10000
			personal_shield_capacity = 300
			#The amount of energy an Energy Crystal can hold
			#Range: 1 ~ 9223372036854775807
			energy_crystal_capacity = 50000
			#The amount of energy an Advanced Energy Crystal can hold
			#Range: 1 ~ 9223372036854775807
			advanced_energy_crystal_capacity = 100000
			#The amount of energy that can be transfered into and out of an Energy Crystal per tick
			#Range: 1 ~ 9223372036854775807
			energy_crystal_max_transfer = 1500
			#The amount of energy that can be transfered into and out of an Advanced Energy Crystal per tick
			#Range: 1 ~ 9223372036854775807
			advanced_energy_crystal_max_transfer = 3000
			#The amount of energy a Transfer Crystal can transfer per tick
			#Range: 1 ~ 9223372036854775807
			transfer_crystal_max_transfer = 2500
			#The amount of energy an Advanced Transfer Crystal can transfer per tick
			#Range: 1 ~ 9223372036854775807
			advanced_transfer_crystal_max_transfer = 5000

	["Stargate Journey Common Config"."Genetic Config"]

		["Stargate Journey Common Config"."Genetic Config".server]
			#Percentage of Players who will inherit the Ancient Gene
			#Range: 0 ~ 100
			player_ata_gene_inheritance_chance = 30
			#Percentage of Villagers who will inherit the Ancient Gene
			#Range: 0 ~ 100
			villager_player_ata_gene_inheritance_chance = 30
			#Probability of the Prototype Ancient Gene Therapy working
			#Range: 0 ~ 100
			prototype_ata_gene_therapy_success_rate = 50
			#Probability of the Ancient Gene Therapy working
			#Range: 0 ~ 100
			ata_gene_therapy_success_rate = 100

