
[General]
	#Silence the server-wide Wither spawn and death broadcast sounds.
	silenceWither = true
	#Silence the wandering trader's ambient sound.
	silenceTrader = true
	#Silence the server-wide Ender Dragon Death broadcast sound.
	silenceDragon = true
	#Silence the server-wide Thunder broadcast sound caused by the Lightning event
	silenceLightning = true
	#A list of sounds to silence, discoverable with the toggle command /listen 
	#enter one sound event per line with no commas.
	silenceUs = [""]
	#If enabled the console will load up spam showing what sounds are being received and whether or not they are being canceled
	debugMode = false

