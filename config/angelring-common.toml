
#General Angel Ring 2 configuration options.
[general]
	#Defines how much XP will be drained from player when flying by wearing classic Angel Ring? Put 0 if you need to disable XP requirement for flight.
	#Range: > 0
	XPCost = 3
	#Defines how much ticks is required to pass between each XP drain event of classic Angel Ring?
	#Range: > 1
	TicksPerDrain = 50
	#Defines how much FE the Energetic Angel Ring will drain every tick while flying.
	#Range: > 1
	EnergeticFEPerTick = 150
	#Defines how much FE the Energetic Angel Ring can store.
	#Range: > 1
	EnergeticFECapacity = 3000000
	#Defines how much FE the Leadstone Angel Ring will drain every tick while flying.
	#Range: > 1
	LeadstoneFEPerTick = 250
	#Defines how much FE the Leadstone Angel Ring can store.
	#Range: > 1
	LeadstoneFECapacity = 2500000
	#Defines how much FE the Hardened Angel Ring will drain every tick while flying.
	#Range: > 1
	HardenedFEPerTick = 200
	#Defines how much FE the Hardened Angel Ring can store.
	#Range: > 1
	HardenedFECapacity = 5000000
	#Defines how much FE the Reinforced Angel Ring will drain every tick while flying.
	#Range: > 1
	ReinforcedFEPerTick = 100
	#Defines how much FE the Reinforced Angel Ring can store.
	#Range: > 1
	ReinforcedFECapacity = 8000000
	#Defines how much FE the Resonant Angel Ring will drain every tick while flying.
	#Range: > 1
	ResonantFEPerTick = 50
	#Defines how much FE the Resonant Angel Ring can store.
	#Range: > 1
	ResonantFECapacity = 16000000
	#Defines at what XP Level to start displaying a warning of low XP for the Classic Angel Ring. Set to -1 to disable.
	#Range: > -1
	XPWarningLevel = 3
	#Defines at what percentage to start displaying a warning of low power for Energy Angel Rings. Set to -1 to disable.
	#Range: -1 ~ 100
	EnergyWarningPercentage = 5

