#Adds modded loot from vanilla structure's loot tables and injects them into Repurposed Structure's loot tables.
#Example: Snowy Pyramid gets all modded items that vanilla Desert Temple can have.
importModdedItems = true
#Add the identifiers for Repurposed Structures's loottable you want to turn off the automatic modded item importing code for.
#Separate multiple entries with a comma.
#Example: "repurposed_structures:chests/mansions/birch, repurposed_structures:chests/mineshafts/jungle"
blacklistedRSLoottablesFromImportingModdedItems = ""

