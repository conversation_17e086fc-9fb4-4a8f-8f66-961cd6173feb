
["Miscellaneous Settings"]
	#----------------------------------------------------------------------------------------------------+
	# Enables the dungeon crawl developer tools. Do not use this for normal gameplay.
	#
	enable_tools = false
	#----------------------------------------------------------------------------------------------------+
	# Enables extended debug logging to help detecting errors. Enabled by default.
	#
	extended_debug = true

["World Generation"]
	#----------------------------------------------------------------------------------------------------+
	# Whether falling blocks like sand or gravel should drop down after being placed during dungeon generation.
	#
	tick_falling_blocks = true
	#----------------------------------------------------------------------------------------------------+
	# When enabled, the dungeons will ignore caves instead of trying to adjust to them (by not generating specific blocks).
	#
	solid = false

["Dungeon Settings"]
	#----------------------------------------------------------------------------------------------------+
	# Whether the dungeons should have secret rooms or not.
	#
	secret_rooms = true
	#----------------------------------------------------------------------------------------------------+
	# Whether the hell stage should be built with blocks from the overworld instead from the nether.
	#
	no_nether_blocks = false
	#----------------------------------------------------------------------------------------------------+
	# Whether loot tables of certain spawner entities should be overwritten.
	# For example, wither skeletons from dungeon spawners will never drop skulls if this is enabled.
	#
	overwrite_entity_loot_tables = true
	#----------------------------------------------------------------------------------------------------+
	# Whether custom mob spawners with equipment, etc.. should be used.
	#
	custom_spawners = true
	#----------------------------------------------------------------------------------------------------+
	#
	#Whether the dungeons should generate at a fixed height or not. Enable this if the dungeons are generating too high.
	fixed_generation_height = false
	#----------------------------------------------------------------------------------------------------+
	# The activation range for the spawners in the dungeons.
	#
	#Range: 1 ~ 64
	spawner_activation_range = 12
	#----------------------------------------------------------------------------------------------------+
	# Whether mobs from spawners should despawn naturally or not.
	#
	natural_despawn = true
	#----------------------------------------------------------------------------------------------------+
	# The number of different entities per spawner. Increasing the number increases the diversity of the monster equipment.
	#
	#Range: 1 ~ 128
	spawner_entities = 6

