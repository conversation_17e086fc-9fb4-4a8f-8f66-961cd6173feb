# Determines if croptopia salt will generate in rivers. Defaults to true
generateSaltInWorld = true
# Include both capitalizations in case this gets fixed
rightCLickHarvest = false
rightClickHarvest = false
treeConfig=[
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "minecraft:plains",
            "minecraft:forest",
            "minecraft:sunflower_plains",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:birch_taiga",
            "terralith:blooming_valley",
            "terralith:blooming_plateau",
            "terralith:highlands",
            "terralith:steppe"
        ]
        featureName="orange_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="dragonfruit_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
            "terralith:blooming_valley"
        ]
        featureName="kumquat_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="banana_tree_placed"
    },
    {
        acceptableBiomes=[
            "traverse:autumnal_woods",
            "minecraft:flower_forest",
            "byg:autumnal_forest",
            "byg:aspen_forest",
            "traverse:autumnal_wooded_hills",
            "byg:jacaranda_forest",
            "byg:autumnal_taiga",
            "traverse:wooded_plateau",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "byg:orchard",
            "traverse:woodlands",
            "traverse:wooded_island",
            "minecraft:forest"
        ]
        featureName="plum_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="date_tree_placed"
    },
    {
        acceptableBiomes=[
            "minecraft:dark_forest",
            "byg:weeping_witch_forest",
            "byg:dacite_ridges",
            "byg:ebony_woods",
            "byg:maple_taiga",
            "byg:twilight_meadow"
        ]
        featureName="cashew_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="mango_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="coconut_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="apricot_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="nutmeg_tree_placed"
    },
    {
        acceptableBiomes=[
            "traverse:autumnal_woods",
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:autumnal_forest",
            "byg:aspen_forest",
            "traverse:autumnal_wooded_hills",
            "byg:jacaranda_forest",
            "byg:autumnal_taiga",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="persimmon_tree_placed"
    },
    {
        acceptableBiomes=[
            "minecraft:dark_forest",
            "byg:weeping_witch_forest",
            "byg:dacite_ridges",
            "byg:ebony_woods",
            "byg:maple_taiga",
            "byg:twilight_meadow"
        ]
        featureName="almond_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:birch_taiga",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="avocado_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="fig_tree_placed"
    },
    {
        acceptableBiomes=[
            "minecraft:sparse_jungle",
            "byg:white_mangrove_marshes",
            "byg:tropical_rainforest",
            "byg:temperate_rainforest",
            "byg:cypress_swamplands",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "traverse:lush_swamp",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="cinnamon_tree_placed"
    },
    {
        acceptableBiomes=[
            "minecraft:plains",
            "minecraft:sunflower_plains",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley",
            "terralith:blooming_plateau",
            "terralith:highlands",
            "terralith:steppe"
        ]
        featureName="peach_tree_placed"
    },
    {
        acceptableBiomes=[
            "traverse:wooded_island",
            "minecraft:plains",
            "minecraft:sunflower_plains",
            "traverse:wooded_plateau",
            "byg:prairie",
            "traverse:woodlands",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley",
            "terralith:blooming_plateau",
            "terralith:highlands",
            "terralith:steppe"
        ]
        featureName="apple_tree_placed"
    },
    {
        acceptableBiomes=[
            "traverse:autumnal_woods",
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:autumnal_forest",
            "byg:aspen_forest",
            "traverse:autumnal_wooded_hills",
            "byg:jacaranda_forest",
            "byg:autumnal_taiga",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="pear_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:tropical_rainforest",
            "minecraft:sparse_jungle",
            "byg:tropical_islands",
            "minecraft:jungle",
            "traverse:mini_jungle",
            "byg:crag_gardens",
            "terralith:amethyst_canyon",
            "terralith:amethyst_rainforest",
            "terralith:jungle_mountains",
            "terralith:tropical_jungle",
        ]
        featureName="grapefruit_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley",
            "terralith:steppe"
        ]
        featureName="starfruit_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="nectarine_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="lemon_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "byg:cherry_blossom_forest",
            "traverse:woodlands",
            "traverse:wooded_island",
            "minecraft:forest",
            "traverse:wooded_plateau",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="cherry_tree_placed"
    },
    {
        acceptableBiomes=[
            "byg:orchard",
            "minecraft:flower_forest",
            "byg:aspen_forest",
            "minecraft:forest",
            "minecraft:windswept_forest",
            "byg:red_oak_forest",
            "terralith:blooming_valley",
            "terralith:lavender_forest",
            "terralith:sakura_grove",
            "terralith:sakura_valley"
        ]
        featureName="lime_tree_placed"
    },
    {
        acceptableBiomes=[
            "minecraft:dark_forest",
            "byg:weeping_witch_forest",
            "byg:dacite_ridges",
            "byg:ebony_woods",
            "byg:maple_taiga",
            "byg:twilight_meadow",
            "terralith:birch_taiga",
        ]
        featureName="pecan_tree_placed"
    },
    {
        acceptableBiomes=[
            "minecraft:dark_forest",
            "byg:weeping_witch_forest",
            "byg:dacite_ridges",
            "byg:ebony_woods",
            "byg:maple_taiga",
            "byg:twilight_meadow",
            "terralith:birch_taiga",
            "terralith:steppe"
        ]
        featureName="walnut_tree_placed"
    }
]
