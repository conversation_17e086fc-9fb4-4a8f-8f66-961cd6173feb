
[common]
	#Set this to false if you want the (de)compress feature to work outside of crafting GUIs (only works if installed on server while in the player inventory)
	compressRequiresCraftingGrid = true
	#A list of modid:name entries that will not be crafted by the compress key.
	compressDenylist = ["minecraft:sandstone", "minecraft:iron_trapdoor"]

[client]
	#If set to true, right-clicking the result slot in a crafting table will craft a full stack.
	rightClickCraftsStack = true
	#We both know JEI is much better. This option hides <PERSON><PERSON>'s crafting book button instead of moving it.
	hideVanillaCraftingGuide = true
	#Set to 'DEFAULT' to enable both buttons and hotkeys. Set to 'BUTTONS' to enable buttons only. Set to 'HOTKEYS' to enable hotkeys only. Set to 'DISABLED' to disable completely.
	#Allowed Values: DEFAULT, BUTTONS, HOTKEYS, DISABLED
	mode = "DEFAULT"
	#Add mod ids here of mods that you wish to disable Crafting Tweaks support for.
	disabledAddons = []

