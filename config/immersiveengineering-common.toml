#IMPORTANT NOTICE:
#THIS IS ONLY THE COMMON CONFIG. It does not contain all the values adjustable for IE.
#All modifiers for machines, all ore gen, the retrogen features and most other adjustable values have been moved to immersiveengineering-server.toml.
#That file is PER WORLD, meaning you have to go into 'saves/<world name>/serverconfig' to adjust it. Those changes will then only apply for THAT WORLD.
#You can then take that config file and put it in the 'defaultconfigs' folder to make it apply automatically to all NEW worlds you generate FROM THERE ON.
#This may appear confusing to many of you, but it is a new sensible way to handle configuration, because the server configuration is synced when playing multiplayer.
importantInfo = true
#A list of preferred Mod IDs that results of IE processes should stem from, aka which mod you want the copper to come from.
#This affects the ores dug by the excavator, as well as those crushing recipes that don't have associated IE items. This list is in oreder of priority.
preferredOres = ["immersiveengineering", "minecraft"]

[debug]

	[debug.wires]
		#Enable detailed logging for the wire network. This can be useful for developers to track down issues related to wires.
		enableWireLogger = false
		#Run sanity checks on the wire network after every interaction. This will cause a decent amount of lag and a lot of log spam if the wire network isn't fully intact. Only enable when asked to by an IE developer.
		validateNets = false

#A list of all mods that IE has integrated compatability for
#Setting any of these to false disables the respective compat
[compat]
	theoneprobe = true
	curios = true
	computercraft = true

