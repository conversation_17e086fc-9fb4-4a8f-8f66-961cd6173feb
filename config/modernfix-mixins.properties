# This is the configuration file for ModernFix.
# In general, prefer using the config screen to editing this file. It can be accessed
# via the standard mod menu on your respective mod loader. Changes will, however,
# require restarting the game to take effect.
#
# The following options can be enabled or disabled if there is a compatibility issue.
# Add a line with your option name and =true or =false at the bottom of the file to enable
# or disable a rule. For example:
#   mixin.perf.dynamic_resources=true
# Do not include the #. You may reset to defaults by deleting this file.
#
# Available options:
#  mixin.bugfix.buffer_builder_leak=true # (default)
#  mixin.bugfix.chunk_deadlock=true # (default)
#  mixin.bugfix.cofh_core_crash=true # (default)
#  mixin.bugfix.concurrency=true # (default)
#  mixin.bugfix.ctm_resourceutil_cme=true # (default)
#  mixin.bugfix.ender_dragon_leak=true # (default)
#  mixin.bugfix.entity_pose_stack=true # (default)
#  mixin.bugfix.extra_experimental_screen=true # (default)
#  mixin.bugfix.fix_config_crashes=true # (default)
#  mixin.bugfix.forge_at_inject_error=true # (default)
#  mixin.bugfix.forge_vehicle_packets=true # (default)
#  mixin.bugfix.missing_block_entities=false # (default)
#  mixin.bugfix.model_data_manager_cme=true # (default)
#  mixin.bugfix.packet_leak=false # (default)
#  mixin.bugfix.paper_chunk_patches=true # (default)
#  mixin.bugfix.recipe_book_type_desync=true # (default)
#  mixin.bugfix.registry_ops_cme=true # (default)
#  mixin.bugfix.removed_dimensions=true # (default)
#  mixin.bugfix.restore_old_dragon_movement=false # (default)
#  mixin.bugfix.unsafe_modded_shape_caches=true # (default)
#  mixin.bugfix.world_leaks=true # (default)
#  mixin.bugfix.world_screen_skipped=true # (default)
#  mixin.devenv=false # (default)
#  mixin.feature.blockentity_incorrect_thread=false # (default)
#  mixin.feature.branding=true # (default)
#  mixin.feature.cause_lag_by_disabling_threads=false # (default)
#  mixin.feature.direct_stack_trace=false # (default)
#  mixin.feature.disable_unihex_font=false # (default)
#  mixin.feature.integrated_server_watchdog=true # (default)
#  mixin.feature.log_stdout_in_log_files=true # (default)
#  mixin.feature.mcfunction_profiling=true # (default)
#  mixin.feature.measure_time=true # (default)
#  mixin.feature.registry_event_progress=false # (default)
#  mixin.feature.remove_chat_signing=false # (default)
#  mixin.feature.remove_telemetry=true # (default)
#  mixin.feature.snapshot_easter_egg=true # (default)
#  mixin.feature.spam_thread_dump=false # (default)
#  mixin.feature.spark_profile_launch=false # (default)
#  mixin.feature.spark_profile_world_join=false # (default)
#  mixin.feature.stalled_chunk_load_detection=false # (default)
#  mixin.feature.warn_missing_perf_mods=true # (default)
#  mixin.launch.class_search_cache=true # (default)
#  mixin.perf.blast_search_trees=true # (default)
#  mixin.perf.blast_search_trees.force=false # (default)
#  mixin.perf.cache_blockstate_cache_arrays=true # (default)
#  mixin.perf.cache_model_materials=true # (default)
#  mixin.perf.cache_profile_texture_url=true # (default)
#  mixin.perf.cache_strongholds=true # (default)
#  mixin.perf.cache_upgraded_structures=true # (default)
#  mixin.perf.chunk_meshing=true # (default)
#  mixin.perf.clear_mixin_classinfo=false # (default)
#  mixin.perf.compact_bit_storage=true # (default)
#  mixin.perf.compress_unihex_font=true # (default)
#  mixin.perf.datapack_reload_exceptions=true # (default)
#  mixin.perf.dedicated_reload_executor=true # (default)
#  mixin.perf.deduplicate_climate_parameters=false # (default)
#  mixin.perf.deduplicate_location=false # (default)
#  mixin.perf.deduplicate_wall_shapes=true # (default)
#  mixin.perf.dynamic_dfu=true # (default)
#  mixin.perf.dynamic_entity_renderers=false # (default)
#  mixin.perf.dynamic_resources=false # (default)
#  mixin.perf.dynamic_resources.ctm=true # (default)
#  mixin.perf.dynamic_resources.ldlib=true # (default)
#  mixin.perf.dynamic_resources.supermartijncore=true # (default)
#  mixin.perf.dynamic_sounds=true # (default)
#  mixin.perf.dynamic_structure_manager=true # (default)
#  mixin.perf.fast_forge_dummies=true # (default)
#  mixin.perf.fast_registry_validation=true # (default)
#  mixin.perf.faster_ingredients=true # (default)
#  mixin.perf.faster_item_rendering=false # (default)
#  mixin.perf.faster_structure_location=true # (default)
#  mixin.perf.faster_texture_stitching=true # (default)
#  mixin.perf.fix_loop_spin_waiting=true # (default)
#  mixin.perf.forge_cap_retrieval=true # (default)
#  mixin.perf.forge_registry_alloc=true # (default)
#  mixin.perf.forge_registry_lambda=true # (default)
#  mixin.perf.ingredient_item_deduplication=false # (default)
#  mixin.perf.kubejs=true # (default)
#  mixin.perf.lazy_search_tree_registry=true # (default)
#  mixin.perf.memoize_creative_tab_build=true # (default)
#  mixin.perf.model_optimizations=true # (default)
#  mixin.perf.mojang_registry_size=true # (default)
#  mixin.perf.patchouli_deduplicate_books=true # (default)
#  mixin.perf.potential_spawns_alloc=true # (default)
#  mixin.perf.reduce_blockstate_cache_rebuilds=true # (default)
#  mixin.perf.remove_biome_temperature_cache=true # (default)
#  mixin.perf.remove_spawn_chunks=false # (default)
#  mixin.perf.resourcepacks=true # (default)
#  mixin.perf.smart_ingredient_sync=true # (default)
#  mixin.perf.state_definition_construct=true # (default)
#  mixin.perf.tag_id_caching=true # (default)
#  mixin.perf.thread_priorities=true # (default)
#  mixin.perf.ticking_chunk_alloc=true # (default)
#  mixin.perf.worldgen_allocation=false # (default)
#  mixin.safety=true # (default)
#
# User overrides go here.
mixin.perf.dynamic_resources=true
