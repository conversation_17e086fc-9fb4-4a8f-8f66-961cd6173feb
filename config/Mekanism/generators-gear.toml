
#Mekanism Generators Gear Config. This config is synced from server to client.
[generators-gear]

	#MekaSuit Settings
	[generators-gear.mekasuit]
		#Geothermal charging rate (Joules) of pants per tick, per degree above ambient, per upgrade installed. This value scales down based on how much of the MekaSuit Pants is submerged. Fire is treated as having a temperature of ~200K above ambient, lava has a temperature of 1,000K above ambient.
		geothermalChargingRate = "10.5000"

		[generators-gear.mekasuit.damage_absorption]
			#Percent of heat damage negated by MekaSuit Pants with maximum geothermal generator units installed. This number scales down linearly based on how many units are actually installed.
			#Range: 0.0 ~ 1.0
			heatDamageReductionRatio = 0.8

