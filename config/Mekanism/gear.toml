
#Gear Config. This config is synced from server to client.
[gear]

	#Atomic Disassembler Settings
	[gear.atomic_disassembler]
		#Base Energy (Joules) usage of the Atomic Disassembler. (Gets multiplied by speed factor)
		energyUsage = "10"
		#Cost in Joules of using the Atomic Disassembler as a weapon.
		energyUsageWeapon = "2000"
		#The max Atomic Disassembler Vein Mining Block Count.
		#Range: 2 ~ 1000000
		miningCount = 128
		#Enable the 'Slow' mode for the Atomic Disassembler.
		slowMode = true
		#Enable the 'Fast' mode for the Atomic Disassembler.
		fastMode = true
		#Enable the 'Vein Mining' mode for the Atomic Disassembler.
		veinMining = false
		#The bonus attack damage of the Atomic Disassembler when it is out of power. (Value is in number of half hearts)
		#Range: 0 ~ 1000
		minDamage = 4
		#The bonus attack damage of the Atomic Disassembler when it has at least energyUsageWeapon power stored. (Value is in number of half hearts)
		#Range: 1 ~ 10000
		maxDamage = 20
		#Attack speed of the Atomic Disassembler.
		#Range: -4.0 ~ 100.0
		attackSpeed = -2.4
		#Maximum amount (joules) of energy the Atomic Disassembler can contain.
		maxEnergy = "1000000"
		#Amount (joules) of energy the Atomic Disassembler can accept per tick.
		chargeRate = "5000"

	#Configurator Settings
	[gear.configurator]
		#Maximum amount (joules) of energy the Configurator can contain.
		maxEnergy = "60000"
		#Amount (joules) of energy the Configurator can accept per tick.
		chargeRate = "300"
		#Energy usage in joules of using the configurator to configure machines.
		energyPerConfigure = "400"
		#Energy cost in joules for each item the configurator ejects from a machine on empty mode.
		energyPerItem = "8"

	#Electric Bow Settings
	[gear.electric_bow]
		#Maximum amount (joules) of energy the Electric Bow can contain.
		maxEnergy = "120000"
		#Amount (joules) of energy the Electric Bow can accept per tick.
		chargeRate = "600"
		#Cost in Joules of using the Electric Bow.
		energyUsage = "120"
		#Cost in Joules of using the Electric Bow with flame mode active.
		energyUsageFire = "1200"

	#Energy Tablet Settings
	[gear.energy_tablet]
		#Maximum amount (joules) of energy the Energy Tablet can contain.
		maxEnergy = "1000000"
		#Amount (joules) of energy the Energy Tablet can accept per tick.
		chargeRate = "5000"

	#Flamethrower Settings
	[gear.flamethrower]
		#Flamethrower Gas Tank capacity in mB.
		#Range: 1 ~ 9223372036854775807
		maxGas = 24000
		#Amount of hydrogen the Flamethrower can accept per tick.
		#Range: 1 ~ 9223372036854775807
		fillRate = 16
		#Determines whether or not the Flamethrower can destroy items if it fails to smelt them.
		destroyItems = true

	#Free Runner Settings
	[gear.free_runner]
		#Energy cost/multiplier in Joules for reducing fall damage with free runners. Energy cost is: FallDamage * freeRunnerFallEnergyCost. (1 FallDamage is 1 half heart)
		fallEnergyCost = "50"
		#Percent of damage taken from falling that can be absorbed by Free Runners when they have enough power.
		#Range: 0.0 ~ 1.0
		fallDamageReductionRatio = 1.0
		#Maximum amount (joules) of energy Free Runners can contain.
		maxEnergy = "64000"
		#Amount (joules) of energy the Free Runners can accept per tick.
		chargeRate = "320"

		#Armored Free Runner Settings
		[gear.free_runner.armored]
			#Armor value of the Armored Free Runners
			#Range: > 0
			armor = 3
			#Toughness value of the Armored Free Runners.
			#Range: 0.0 ~ 3.4028234663852886E38
			toughness = 2.0
			#Knockback resistance value of the Armored Free Runners.
			#Range: 0.0 ~ 3.4028234663852886E38
			knockbackResistance = 0.0

	#Jetpack Settings
	[gear.jetpack]
		#Jetpack Gas Tank capacity in mB.
		#Range: 1 ~ 9223372036854775807
		maxGas = 24000
		#Amount of hydrogen the Jetpack can accept per tick.
		#Range: 1 ~ 9223372036854775807
		fillRate = 16

		#Armored Jetpack Settings
		[gear.jetpack.armored]
			#Armor value of the Armored Jetpack.
			#Range: > 0
			armor = 8
			#Toughness value of the Armored Jetpack.
			#Range: 0.0 ~ 3.4028234663852886E38
			toughness = 2.0
			#Knockback resistance value of the Armored Jetpack.
			#Range: 0.0 ~ 3.4028234663852886E38
			knockbackResistance = 0.0

	#Network Reader Settings
	[gear.network_reader]
		#Maximum amount (joules) of energy the Network Reader can contain.
		maxEnergy = "60000"
		#Amount (joules) of energy the Network Reader can accept per tick.
		chargeRate = "300"
		#Energy usage in joules for each network reading.
		energyUsage = "400"

	#Portable Teleporter Settings
	[gear.portable_teleporter]
		#Maximum amount (joules) of energy the Portable Teleporter can contain.
		maxEnergy = "1000000"
		#Amount (joules) of energy the Portable Teleporter can accept per tick.
		chargeRate = "5000"
		#Delay in ticks before a player is teleported after clicking the Teleport button in the portable teleporter.
		#Range: 0 ~ 6000
		delay = 0

	#Scuba Tank Settings
	[gear.scuba_tank]
		#Scuba Tank Gas Tank capacity in mB.
		#Range: 1 ~ 9223372036854775807
		maxGas = 24000
		#Amount of oxygen the Scuba Tank Gas Tank can accept per tick.
		#Range: 1 ~ 9223372036854775807
		fillRate = 16

	#Seismic Reader Settings
	[gear.seismic_reader]
		#Maximum amount (joules) of energy the Seismic Reader can contain.
		maxEnergy = "12000"
		#Amount (joules) of energy the Seismic Reader can accept per tick.
		chargeRate = "60"
		#Energy usage in joules required to use the Seismic Reader.
		energyUsage = "250"

	#Canteen Settings
	[gear.canteen]
		#Maximum amount of Nutritional Paste storable by the Canteen.
		#Range: > 1
		maxStorage = 64000
		#Rate at which Nutritional Paste can be transferred into a Canteen.
		#Range: > 1
		transferRate = 128

	#Meka-Tool Settings
	[gear.mekatool]
		#Base energy (Joules) usage of the Meka-Tool. (Gets multiplied by speed factor)
		energyUsage = "10"
		#Silk touch energy (Joules) usage of the Meka-Tool. (Gets multiplied by speed factor)
		energyUsageSilk = "100"
		#Cost in Joules of using the Meka-Tool to deal 4 units of damage.
		energyUsageWeapon = "2000"
		#Cost in Joules of using the Meka-Tool to teleport 10 blocks.
		energyUsageTeleport = "1000"
		#Maximum distance a player can teleport with the Meka-Tool.
		#Range: 3 ~ 1024
		maxTeleportReach = 100
		#Base bonus damage applied by the Meka-Tool without using any energy.
		#Range: 0 ~ 100000
		baseDamage = 4
		#Attack speed of the Meka-Tool.
		#Range: -4.0 ~ 100.0
		attackSpeed = -2.4
		#Efficiency of the Meka-Tool with energy but without any upgrades.
		#Range: 0.1 ~ 100.0
		baseEfficiency = 4.0
		#Energy capacity (Joules) of the Meka-Tool without any installed upgrades. Quadratically scaled by upgrades.
		baseEnergyCapacity = "16000000"
		#Amount (joules) of energy the Meka-Tool can accept per tick. Quadratically scaled by upgrades.
		chargeRate = "100000"
		#Cost in Joules of using the Meka-Tool as a hoe.
		energyUsageHoe = "10"
		#Cost in Joules of using the Meka-Tool as a shovel for making paths and dowsing campfires.
		energyUsageShovel = "10"
		#Cost in Joules of using the Meka-Tool as an axe for stripping logs, scraping, or removing wax.
		energyUsageAxe = "10"
		#Cost in Joules of using the Meka-Tool to shear entities.
		energyUsageShearEntity = "10"
		#Enable the 'Extended Vein Mining' mode for the Meka-Tool. (Allows vein mining everything not just ores/logs)
		extendedMining = true

	#MekaSuit Settings
	[gear.mekasuit]
		#Energy capacity (Joules) of MekaSuit items without any installed upgrades. Quadratically scaled by upgrades.
		baseEnergyCapacity = "16000000"
		#Amount (joules) of energy the MekaSuit can accept per tick. Quadratically scaled by upgrades.
		chargeRate = "100000"
		#Energy usage (Joules) of MekaSuit when adding 0.1 to jump motion.
		baseJumpEnergyUsage = "1000"
		#Energy usage (Joules) per second of the MekaSuit when flying with the Elytra Unit.
		elytraEnergyUsage = "32000"
		#Energy usage (Joules) of MekaSuit when lessening a potion effect.
		energyUsagePotionTick = "40000"
		#Energy cost/multiplier in Joules for reducing magic damage via the inhalation purification unit. Energy cost is: MagicDamage * energyUsageMagicPrevent. (1 MagicDamage is 1 half heart).
		energyUsageMagicReduce = "1000"
		#Energy cost/multiplier in Joules for reducing fall damage with MekaSuit Boots. Energy cost is: FallDamage * freeRunnerFallEnergyCost. (1 FallDamage is 1 half heart)
		energyUsageFall = "50"
		#Energy usage (Joules) of MekaSuit when adding 0.1 to sprint motion.
		energyUsageSprintBoost = "100"
		#Energy usage (Joules) of MekaSuit per tick when flying via Gravitational Modulation.
		energyUsageGravitationalModulation = "1000"
		#Charge rate of inventory items (Joules) per tick.
		inventoryChargeRate = "10000"
		#Solar recharging rate (Joules) of helmet per tick, per upgrade installed.
		solarRechargingRate = "500"
		#Energy usage (Joules) of MekaSuit per tick of using vision enhancement.
		energyUsageVisionEnhancement = "500"
		#Energy usage (Joules) of MekaSuit per tick of using hydrostatic repulsion.
		energyUsageHydrostaticRepulsion = "500"
		#Energy usage (Joules) of MekaSuit per half-food of nutritional injection.
		energyUsageNutritionalInjection = "20000"
		#Energy usage (Joules) of MekaSuit per unit of damage applied.
		energyUsageDamage = "100000"
		#Energy usage (Joules) of MekaSuit per tick of attracting a single item.
		energyUsageItemAttraction = "250"
		#Should the Gravitational Modulation unit give off vibrations when in use.
		gravitationalVibrations = true
		#Maximum amount of Nutritional Paste storable by the nutritional injection unit.
		#Range: > 1
		nutritionalMaxStorage = 128000
		#Rate at which Nutritional Paste can be transferred into the nutritional injection unit.
		#Range: > 1
		nutritionalTransferRate = 256
		#Maximum amount of Hydrogen storable in the jetpack unit.
		#Range: 1 ~ 9223372036854775807
		jetpackMaxStorage = 48000
		#Rate at which Hydrogen can be transferred into the jetpack unit.
		#Range: 1 ~ 9223372036854775807
		jetpackTransferRate = 256
		#Armor value of MekaSuit Helmets.
		#Range: > 0
		helmetArmor = 3
		#Armor value of MekaSuit BodyArmor.
		#Range: > 0
		bodyArmorArmor = 8
		#Armor value of MekaSuit Pants.
		#Range: > 0
		pantsArmor = 6
		#Armor value of MekaSuit Boots.
		#Range: > 0
		bootsArmor = 3
		#Toughness value of the MekaSuit.
		#Range: 0.0 ~ 3.4028234663852886E38
		toughness = 3.0
		#Knockback resistance value of the MekaSuit.
		#Range: 0.0 ~ 3.4028234663852886E38
		knockbackResistance = 0.10000000149011612

		[gear.mekasuit.damage_absorption]
			#Percent of damage taken from falling that can be absorbed by MekaSuit Boots when they have enough power.
			#Range: 0.0 ~ 1.0
			fallDamageReductionRatio = 1.0
			#Percent of damage taken from magic damage that can be absorbed by MekaSuit Helmet with Purification unit when it has enough power.
			#Range: 0.0 ~ 1.0
			magicDamageReductionRatio = 1.0
			#Percent of damage taken from other non explicitly supported damage types that don't bypass armor when the MekaSuit has enough power and a full suit is equipped.
			#Note: Support for specific damage types can be added by adding an entry for the damage type in the damageReductionRatio config.
			#Range: 0.0 ~ 1.0
			unspecifiedDamageReductionRatio = 1.0
			#Map representing the percent of damage from different damage types that can be absorbed by the MekaSuit when there is enough power and a full suit is equipped.
			#Values may be in the range [0.0, 1.0].
			#See the #mekainsm:mekasuit_always_supported damage type tag for allowing damage that bypasses armor to be blocked.
			damageReductionRatio = ["minecraft:cactus,1.0", "minecraft:cramming,1.0", "minecraft:dragon_breath,1.0", "minecraft:dry_out,1.0", "minecraft:fall,1.0", "minecraft:falling_anvil,1.0", "minecraft:falling_block,1.0", "minecraft:falling_stalactite,1.0", "minecraft:fly_into_wall,1.0", "minecraft:freeze,1.0", "minecraft:generic,1.0", "minecraft:hot_floor,1.0", "minecraft:in_fire,1.0", "minecraft:in_wall,1.0", "minecraft:lava,1.0", "minecraft:lightning_bolt,1.0", "minecraft:on_fire,1.0", "minecraft:sonic_boom,0.75", "minecraft:stalagmite,1.0", "minecraft:sweet_berry_bush,1.0", "minecraft:wither,1.0"]

