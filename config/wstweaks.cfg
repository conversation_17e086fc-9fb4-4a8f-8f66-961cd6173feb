# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# 

general {
    # The chance for a skull shard to drop. 1 = 100%, 0.5 = 50%, etc
    # Default: 1.0; Range: [0.0 ~ 1.0]
    S:"Shard Drop Chance"=1.0

    # If skeletons in ALL biomes are converted, instead of just the nether.
    # Default: false
    B:"Convert All Biomes"=false

    # The chance for skeletons to be converted in all biomes, when enabled. 1 = 100%, 0.5 = 50%, etc
    # Default: 0.15; Range: [0.0 ~ 1.0]
    S:"All Biomes Chance"=0.15

    # If stone swords and other trash are removed from wither skeleton drop tables.
    # Default: true
    B:"Delete Swords"=true

    # If converted skeletons receive bows (Wither Skeletons always shoot flaming arrows).
    # Default: true
    B:"Give Bows"=true
}


blades {
    # [Unsynced]
    # [Requires Restart]
    # The durability of immolation blades.
    # Default: 4096; Range: [1 ~ 65536]
    I:Durability=4096

    # [Unsynced]
    # [Requires Restart]
    # The attack damage of immolation blades. This is a modifier, so the real value is always 1 higher.
    # Default: 11.0; Range: [1.0 ~ 4096.0]
    S:"Attack Damage"=11.0

    # [Unsynced]
    # [Requires Restart]
    # The attack speed of immolation blades. This is a modifier, so the real value is a bit different.
    # Default: -2.0; Range: [-4096.0 ~ 4096.0]
    S:"Attack Speed"=-2.0
}


