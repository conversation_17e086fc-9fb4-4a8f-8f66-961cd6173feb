
[IFSoulsMachines]

	[IFSoulsMachines.ConfigSoulSurge]
		#How long in ticks a soul last to accelerate ticks
		#Range: > 1
		SOUL_TIME = 300
		#How many extra ticks the surge will accelerate for tile entities
		#Range: > 0
		ACCELERATION_TICK = 2
		#How many extra ticks the surge will accelerate for mobs
		#Range: > 0
		ENTITIES_ACCELERATION_TICK = 4
		#How many extra ticks the surge will accelerate for blocks
		#Range: > 0
		BLOCK_ACCELERATION_TICK = 4
		#How often a random tick block will be accelerated, by default 3% of the ticks (random)
		#Range: 0.0 ~ 1.0
		RANDOM_TICK_ACCELERATION_CHANCE = 0.03

	[IFSoulsMachines.ConfigSoulLaserBase]
		#Max soul storage tank amount
		#Range: > 1
		SOUL_STORAGE_AMOUNT = 1350
		#Max progress of the machine
		#Range: > 1
		MAX_PROGRESS = 20
		#Kill the warden when it's life reaches near to 0 or keep it alive
		KILL_WARDEN = true
		#Damage done to the warden when an operation is done
		#Range: > 0
		DAMAGE_PER_OPERATION = 4
		#Souls generated when an operation is done
		#Range: > 1
		SOULS_PER_OPERATION = 1

