/*
    This config file makes it possible to switch off any Minecraft structure.
    To disable a structure, simply set the value of that structure to "false".
    To change the rarity of a structure category, use the other file in the folder.
*/
{
	"igloos": {
		"igloo": true
	},
	"desert_pyramids": {
		"desert_pyramid": true
	},
	"end_cities": {
		"end_city": true
	},
	"ocean_ruins": {
		"ocean_ruin_cold": true,
		"ocean_ruin_warm": true
	},
	"shipwrecks": {
		"shipwreck": true,
		"shipwreck_beached": true
	},
	"woodland_mansions": {
		"mansion": true
	},
	"ancient_cities": {
		"ancient_city": true
	},
	"buried_treasures": {
		"buried_treasure": true
	},
	"villages": {
		"village_plains": true,
		"village_desert": true,
		"village_savanna": true,
		"village_snowy": true,
		"village_taiga": true
	},
	"nether_complexes": {
		"fortress": true,
		"bastion_remnant": true
	},
	"ocean_monuments": {
		"monument": true
	},
	"jungle_temples": {
		"jungle_pyramid": true
	},
	"nether_fossils": {
		"nether_fossil": true
	},
	"mineshafts": {
		"mineshaft": true,
		"mineshaft_mesa": true
	},
	"pillager_outposts": {
		"pillager_outpost": true
	},
	"swamp_huts": {
		"swamp_hut": true
	},
	"strongholds": {
		"stronghold": true
	},
	"ruined_portals": {
		"ruined_portal": true,
		"ruined_portal_desert": true,
		"ruined_portal_jungle": true,
		"ruined_portal_swamp": true,
		"ruined_portal_mountain": true,
		"ruined_portal_ocean": true,
		"ruined_portal_nether": true
	}
}