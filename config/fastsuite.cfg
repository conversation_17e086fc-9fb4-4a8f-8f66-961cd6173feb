# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# FastSuite Configuration

general {
    # A list of recipe types which may only be looked up on the main thread. Add a recipe type to this list if errors start happening.
    # Default: [
    S:"Single Threaded Recipe Types" <
     >

    # The max time, in seconds, that a recipe lookup may take before aborting the lookup and logging an error.
    # Default: 25; Range: [1 ~ 300]
    I:"Max Recipe Lookup Time"=25

    # If true, the stacks used as recipe inputs will be locked and throw an error if modified during parallel matching. Useful for debugging.
    # Default: false
    B:"Lock Crafting Input Stacks"=false

    # If true, FastSuite parallelize all recipes without validation. This can cause crashes if a recipe is not thread safe.
    # Default: false
    B:"Unsafe Mode"=false
}


