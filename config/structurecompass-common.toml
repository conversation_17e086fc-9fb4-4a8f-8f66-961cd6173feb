
#General settings
[general]
	#Sets the range in blocks in which the structure compasses can locate structures [default: 10000]
	#Range: > 0
	compassRange = 10000
	#Defines if the structure compass should only locate unexplored structures. A structure is tagged as explored when the compass is used to find it. [default: false]]
	locateUnexplored = false
	#Defines which structures can't be searched with the Structure Compass
	#(Supports wildcard *, Example: 'minecraft:*' will blacklist anything in the minecraft domain)
	structureBlacklist = ["minecraft:monument", "minecraft:desert_pyramid", "minecraft:stronghold", "minecraft:fortress", "minecraft:mineshaft"]
	#Defines if the structure compass should locate structures asynchronously [default: false]
	locateAsync = true

