{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "high_voltage"
	group: "1DA67E79B40AB130"
	icon: "gtceu:advanced_integrated_circuit"
	id: "37A5A4A81CCB67E5"
	order_index: 4
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"欢迎来到高压阶段!激动人心的旅程即将开始!"
				""
				"首先让我们着手制造不锈钢,为高压机器生产做准备"
			]
			id: "70C952B8FF3418F6"
			rewards: [
				{
					count: 2
					id: "3DD4C1CB19B61BC0"
					item: "gtceu:good_electronic_circuit"
					random_bonus: 2
					type: "item"
				}
				{
					count: 4
					id: "560585DB784F4FE1"
					item: "gtceu:transistor"
					random_bonus: 4
					type: "item"
				}
			]
			size: 1.5d
			subtitle: "系好安全带迎接&e高压时代"
			tasks: [{
				id: "12A2731FC755513E"
				item: "gtceu:advanced_integrated_circuit"
				type: "item"
			}]
			x: -7.5d
			y: 2.0d
		}
		{
			dependencies: [
				"5FAA73C52082DC48"
				"39ACF4D6503067F3"
				"0AC6B223857CAE94"
			]
			description: [
				"原本需要真空管的配方现可用此物替代!"
				""
				"&e提示:&r在JEI中可通过输入&b$circuits&r搜索所有电路类型,输入&b$circuits/ulv&r可筛选超低压层级电路"
			]
			id: "54EDFCD65E088296"
			rewards: [{
				exclude_from_claim_all: true
				id: "1A4D6DC9EE76A564"
				table_id: 822291801189586703L
				type: "loot"
			}]
			subtitle: "经济型超低压方案"
			tasks: [{
				id: "28B1CB0A8A7BDC67"
				item: "gtceu:nand_chip"
				type: "item"
			}]
			x: 0.0d
			y: -0.5d
		}
		{
			dependencies: [
				"5FAA73C52082DC48"
				"0AC6B223857CAE94"
				"3FDC2A2BBAC0EF1B"
			]
			description: [
				"你或许疑惑:为何要制造低压芯片？这不是&e高压&r阶段吗？"
				""
				"确实如此——但若你需要新的低压机器呢？难道不想以更低成本获取吗？"
				""
				"只要配方支持低压运行,操作40台低压机器比操作10台高压机器&a节能4倍&f"
			]
			id: "4DA2B92417DD41F8"
			rewards: [{
				exclude_from_claim_all: true
				id: "13EFD18BC1202874"
				table_id: 822291801189586703L
				type: "loot"
			}]
			tasks: [{
				id: "194037A529925A4E"
				item: "gtceu:microchip_processor"
				type: "item"
			}]
			x: 2.0d
			y: -0.5d
		}
		{
			dependencies: [
				"5FAA73C52082DC48"
				"0AC6B223857CAE94"
				"3FDC2A2BBAC0EF1B"
			]
			description: [
				"终极形态的&b中压&r电路!"
				""
				"&e注意:&r目前尚无法制作最廉价配方,该配方将在四级电压阶段解锁"
			]
			id: "19DB4970B3D11C1B"
			rewards: [{
				exclude_from_claim_all: true
				id: "6180BBEBBF79201E"
				table_id: 822291801189586703L
				type: "loot"
			}]
			tasks: [{
				id: "48BE641C2530BA61"
				item: "gtceu:micro_processor"
				type: "item"
			}]
			x: 4.5d
			y: 1.0d
		}
		{
			dependencies: [
				"19DB4970B3D11C1B"
				"5FAA73C52082DC48"
				"0AC6B223857CAE94"
				"791E5CB36B5C1E73"
				"0DF9014435C8F4D2"
			]
			description: [
				"升级我们的&e高压&r电路配方!"
				""
				"真的比之前更经济吗？没错!若存疑可&a对比&f新旧配方"
			]
			id: "732D201794C228DD"
			rewards: [{
				exclude_from_claim_all: true
				id: "0BDA4E315F8989B4"
				table_id: 822291801189586703L
				type: "loot"
			}]
			tasks: [{
				id: "3C92729C9842C491"
				item: "gtceu:micro_processor_assembly"
				type: "item"
			}]
			x: 8.5d
			y: 2.0d
		}
		{
			dependencies: [
				"732D201794C228DD"
				"46F5DC6A7FF02DF2"
				"5FAA73C52082DC48"
				"62BBF61C9849CA26"
			]
			description: ["将这台&b高级&a电路组装机&f&r移入&e无尘室&r,迎接&5极端电压&r时代的到来!"]
			id: "0DB4226BA23A5C09"
			rewards: [
				{
					count: 8
					id: "02B6BDFD96A5521C"
					item: "gtceu:inductor"
					random_bonus: 4
					type: "item"
				}
				{
					count: 6
					id: "73F7B222D3532B0D"
					item: "gtceu:capacitor"
					random_bonus: 6
					type: "item"
				}
				{
					count: 5
					id: "43A2B5C5388A39EE"
					item: "gtceu:diode"
					random_bonus: 5
					type: "item"
				}
			]
			size: 1.5d
			subtitle: "这台超级计算机能赢棋局吗？"
			tasks: [{
				id: "7497784245BE7D72"
				item: "gtceu:micro_processor_computer"
				type: "item"
			}]
			x: 11.5d
			y: 2.0d
		}
		{
			dependencies: ["70C952B8FF3418F6"]
			description: [
				"唯一需要高压电路制造的中压机器"
				""
				"该设备将助你开启下一阶段电路——&e微处理器&r的制造征程"
			]
			id: "0AC6B223857CAE94"
			rewards: [{
				exclude_from_claim_all: true
				id: "44A07D73596C22E2"
				table_id: 822291801189586703L
				type: "loot"
			}]
			tasks: [{
				id: "7004B0FA13A8D4D1"
				item: "gtceu:mv_circuit_assembler"
				type: "item"
			}]
			x: 0.0d
			y: 2.0d
		}
		{
			dependencies: [
				"1DF42E30A24A9DEC"
				"6F49C691CF79D4B8"
				"012366E4E4095FC4"
				"70C952B8FF3418F6"
			]
			description: [
				"最小尺寸为5x5x5,最大尺寸为15x15x15,在此范围内的任何尺寸都有效!"
				""
				"&a洁净室&r多方块结构是中空的,因为你需要将机器放入其中运行需要洁净环境的配方——例如制作&5EV电路&r时,必须将&b&a电路组装机&f&r &n置于&r洁净室内"
				""
				"别忘了安装&e能源仓&r、&e&a维护仓&f&r和&e&a铁门&f&r!"
				""
				"若能实现无线传输,则&n无需&r使用&e穿透仓&r"
				""
				"若使用&9发电机&r供电则必须配备&3二极管&r,因为发电机污染等级过高不能直接接入洁净室"
				""
				"若使用&c&a能源转换器&f&r则&n无需&r二极管,因为能源转换器是...清洁的？"
			]
			icon: "gtceu:cleanroom"
			id: "62BBF61C9849CA26"
			min_width: 300
			rewards: [{
				exclude_from_claim_all: true
				id: "2FAE59F32C8DD482"
				table_id: 822291801189586703L
				type: "loot"
			}]
			subtitle: "一尘不染"
			tasks: [{
				id: "564B0D84C5688FF2"
				item: "gtceu:cleanroom"
				type: "item"
			}]
			x: 1.0d
			y: 5.500000000000005d
		}
		{
			dependencies: ["681110DE6B4E6ED8"]
			description: [
				"用塑料板包裹钢架后浇筑混凝土,待其固化成&b塑钢混凝土"
				""
				"洁净室的边缘和地板必须使用塑钢混凝土"
			]
			id: "1DF42E30A24A9DEC"
			tasks: [{
				id: "1945295F5A5C547D"
				item: "gtceu:plascrete"
				type: "item"
			}]
			x: 1.0d
			y: 6.500000000000005d
		}
		{
			description: ["可用洁净室玻璃替代塑钢混凝土建造墙体(但不可用于边缘或地板)"]
			id: "6F49C691CF79D4B8"
			shape: "square"
			tasks: [{
				id: "71760579D96768C3"
				item: "gtceu:cleanroom_glass"
				type: "item"
			}]
			x: 2.0d
			y: 6.500000000000005d
		}
		{
			dependencies: [
				"70C952B8FF3418F6"
				"4FD6092D9C2A485C"
			]
			description: ["终于能用不锈钢制造&e高压&r机器了!"]
			id: "61262BC1C525E6F1"
			rewards: [
				{
					count: 8
					id: "3B4D781A444552F6"
					item: "gtceu:raw_tantalite"
					random_bonus: 4
					type: "item"
				}
				{
					count: 8
					id: "3ED98AFAB9371EFA"
					item: "gtceu:raw_chromite"
					random_bonus: 4
					type: "item"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "2BBF580104DF2C41"
				item: "gtceu:stainless_steel_ingot"
				type: "item"
			}]
			x: -7.5d
			y: -1.9500000000000002d
		}
		{
			dependencies: [
				"124C2A904BF84254"
				"0BAD21557994331D"
			]
			description: [
				"在便捷的&e&a化学反应器&f&r中放入&a塑料电路板&f、铜箔和你制备的三氯化铁,即可得到塑料&a电路板&f"
				""
				"这是所有微处理器电路的基础材料"
			]
			id: "5FAA73C52082DC48"
			rewards: [
				{
					count: 4
					id: "564C05DF78F326F8"
					item: "gtceu:copper_foil"
					random_bonus: 8
					type: "item"
				}
				{
					id: "2BD51BAAEA3CF3B8"
					item: "gtceu:plastic_circuit_board"
					random_bonus: 2
					type: "item"
				}
			]
			tasks: [{
				id: "418A5680FB2534F3"
				item: "gtceu:plastic_printed_circuit_board"
				type: "item"
			}]
			x: 4.5d
			y: -3.5d
		}
		{
			dependencies: ["54BEC01D84237DBC"]
			description: [
				"盐酸与铁粉将发生&e化学反应&r生成三氯化铁"
				""
				"还能回收部分氢气!"
				""
				"别忘了设置&a程序参数&r,此处应设为1"
				""
				"建议为此配置&b物流请求器&r"
			]
			id: "0BAD21557994331D"
			rewards: [{
				id: "240F4D3E97A3FF81"
				item: "gtceu:iron_iii_chloride_bucket"
				type: "item"
			}]
			tasks: [{
				id: "0CBFE2B699BFEC8F"
				item: "gtceu:iron_iii_chloride_bucket"
				type: "item"
			}]
			x: 5.5d
			y: -5.0d
		}
		{
			description: [
				"需使用&e弯折机&r制作薄板和箔材"
				""
				"可选择直接&e流体固化&r聚乙烯成板,或先铸锭再压制成块,最后&e切割&r一次性获得9张板材"
				""
				"至此你会发现达成目标有多种工艺路线,请大胆尝试不同制作方法!"
			]
			id: "791E5CB36B5C1E73"
			min_width: 250
			rewards: [{
				id: "3F393D71E9CA863A"
				item: "gtceu:polyethylene_bucket"
				random_bonus: 2
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "3A9CA36D3074377E"
				item: "gtceu:capacitor"
				type: "item"
			}]
			x: 9.0d
			y: 1.0d
		}
		{
			dependencies: ["12F24C9C6D2AF887"]
			description: [
				"现在明白为何强制要求使用&a镍锌铁氧体&f锭了吧？这个配方就是原因!"
				""
				"使用可自产的&a镍锌铁氧体&f与&a退火铜&f性价比最高"
				""
				"制作NZF环和细导线有多种选择:&e挤压机&r能使单锭产出最多环材,&e线材轧机&r可将锭材加工成导线再精轧成细导线"
			]
			id: "0DF9014435C8F4D2"
			min_width: 250
			rewards: [{
				count: 2
				id: "07C9E792514A730A"
				item: "gtceu:fine_annealed_copper_wire"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "3DB9A596BBFFF2FD"
				item: "gtceu:inductor"
				type: "item"
			}]
			x: 7.5d
			y: 2.8d
		}
		{
			description: [
				"没错,&e激光刻蚀&r又需要新透镜...建议提前批量制作"
				""
				"提醒:&n刻蚀&r工序制作晶圆,&n切割&r工序制作芯片"
			]
			id: "3FDC2A2BBAC0EF1B"
			rewards: [{
				count: 2
				id: "2BB66AF9D7458521"
				item: "gtceu:silicon_wafer"
				random_bonus: 2
				type: "item"
			}]
			shape: "square"
			tasks: [
				{
					id: "3265E94FDBE6EF04"
					item: "gtceu:diamond_lens"
					type: "item"
				}
				{
					id: "2AF7637FB5C18FF9"
					item: "gtceu:cpu_wafer"
					type: "item"
				}
				{
					id: "24283490238E0432"
					item: "gtceu:cpu_chip"
					type: "item"
				}
			]
			title: "CPU芯片"
			x: 3.2d
			y: 0.20000000000000018d
		}
		{
			dependencies: ["0B54990168F9B136"]
			description: ["将晶圆&e切割&r成正式芯片"]
			id: "39ACF4D6503067F3"
			rewards: [{
				id: "18F7C546E935D265"
				type: "xp"
				xp: 250
			}]
			tasks: [{
				id: "31899448B5C51F55"
				item: "gtceu:simple_soc"
				type: "item"
			}]
			x: 0.0d
			y: -1.5d
		}
		{
			dependencies: ["16154B77454631F4"]
			description: [
				"这个小家伙能让我们以最低成本制作ULV电路"
				""
				"没错,真空管的前身就是ULV电路"
				""
				"&e扩展知识&r:SoC(片上系统)本质上是集成了全部计算功能的微型计算机"
			]
			id: "0B54990168F9B136"
			rewards: [{
				count: 2
				id: "5AA67878AA880DF6"
				item: "gtceu:silicon_wafer"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "5DFF6A6EA91263D1"
				item: "gtceu:simple_soc_wafer"
				type: "item"
			}]
			x: 0.0d
			y: -2.5d
		}
		{
			dependencies: ["62161044F3F3AB87"]
			description: ["新的一天,&e激光刻蚀机&r又需要新透镜"]
			id: "16154B77454631F4"
			rewards: [{
				id: "3C750604EAB74C87"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "69AB81B8D1A9F5B1"
				item: "gtceu:cyan_glass_lens"
				type: "item"
			}]
			x: 0.0d
			y: -3.5d
		}
		{
			dependencies: ["78DC12C2EB504E56"]
			description: ["回到&e车床&r将板材加工成透镜"]
			id: "1AF005E292E409D1"
			rewards: [{
				id: "1D704E20CBE202D5"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "76B5D119B25F6C72"
				item: "gtceu:glass_lens"
				type: "item"
			}]
			x: 1.0d
			y: -5.5d
		}
		{
			description: [
				"有几种制作方法,选择最适合你的方式!"
				""
				"&e提取&r玻璃成液体,然后通过&e流体固化&r制成玻璃板"
				""
				"&a粉碎&r玻璃成玻璃粉,再通过&a合金冶炼&r制成玻璃板"
				""
				"使用&b切割机&r直接将玻璃加工成玻璃板"
			]
			id: "78DC12C2EB504E56"
			rewards: [{
				count: 4
				id: "57A66EDD265A39CA"
				item: "minecraft:glass"
				random_bonus: 4
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "180F78B65C332EB0"
				item: "gtceu:glass_plate"
				type: "item"
			}]
			x: 1.0d
			y: -6.5d
		}
		{
			dependencies: ["503E5B82A6C89278"]
			description: [
				"在&e流体固化机&r中使用聚氯乙烯可获得板材"
				""
				"将板材与铜箔、硫酸一起放入&e化学反应器&r可得到2个塑料电路板!"
				""
				"敬请期待,后续我们将升级配方实现一次性获得8个珍贵塑料电路板"
			]
			id: "124C2A904BF84254"
			rewards: [{
				count: 2
				id: "63194085296077A3"
				item: "gtceu:copper_foil"
				random_bonus: 6
				type: "item"
			}]
			tasks: [{
				id: "1EB80E78DF5EF7E8"
				item: "gtceu:plastic_circuit_board"
				type: "item"
			}]
			x: 3.5d
			y: -5.0d
		}
		{
			dependencies: ["68526BA198AADD8E"]
			description: [
				"银粉与4个电金粉在&e搅拌机&r的&a程序2&r模式下可合成&b蓝色&a合金粉&f"
				""
				"直接在熔炉中熔炼该粉末即可获得锭"
			]
			id: "46F5DC6A7FF02DF2"
			rewards: [{
				count: 2
				id: "0228AFD949216481"
				item: "gtceu:blue_alloy_dust"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "1F1D7E8BE88CEE72"
				item: "gtceu:blue_alloy_dust"
				type: "item"
			}]
			x: 11.5d
			y: 0.5d
		}
		{
			description: ["若在&a下界&f找不到&b电金矿&f,可通过金银合金与红石在&e搅拌机&r的&a程序1&r模式下合成"]
			id: "68526BA198AADD8E"
			rewards: [{
				id: "5CD5715A50ED58A6"
				item: "gtceu:electrotine_dust"
				random_bonus: 2
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "2A56E86FD7FAD5C3"
				item: "gtceu:electrotine_dust"
				type: "item"
			}]
			x: 11.5d
			y: -1.0d
		}
		{
			dependencies: ["6A243EDB2A99C76D"]
			description: ["两桶&b氧气&r与&b铁氧体混合粉&r在&e&a电力高炉&f&r中直接冶炼成锭——无需冷却!"]
			id: "12F24C9C6D2AF887"
			rewards: [{
				id: "619CB50531BF872F"
				type: "xp"
				xp: 250
			}]
			tasks: [{
				id: "440EC85E5B64E941"
				item: "gtceu:nickel_zinc_ferrite_ingot"
				type: "item"
			}]
			x: 6.5d
			y: 2.8d
		}
		{
			description: [
				"回到&b&a进阶搅拌机&f&r,但需切换&a程序电路&r设置.建议可专门为&d程序2&r配方制作新机器"
				""
				"本次需要铁粉、镍粉和锌粉"
			]
			hide_dependency_lines: true
			id: "6A243EDB2A99C76D"
			rewards: [{
				id: "2A3E3F5D7F7AA750"
				type: "xp"
				xp: 250
			}]
			tasks: [{
				id: "3C4BC945B9C3088A"
				item: "gtceu:ferrite_mixture_dust"
				type: "item"
			}]
			x: 5.5d
			y: 2.8d
		}
		{
			dependencies: ["70C952B8FF3418F6"]
			description: [
				"牢记&e4 RF:1 EU&r的转换率!高压阶段512 EU对应2048 RF需求"
				""
				"部分配方会消耗全部&a每刻&f能量,请确保产能跟得上!"
			]
			id: "7F64202F2C0BAD2A"
			optional: true
			rewards: [
				{
					count: 4
					id: "2C1ED2430A00AD96"
					item: "gtceu:gold_single_wire"
					random_bonus: 8
					type: "item"
				}
				{
					count: 2
					id: "62E8644FD3999AA8"
					item: "gtceu:red_alloy_single_wire"
					random_bonus: 4
					type: "item"
				}
				{
					count: 3
					id: "249C9FBBE6B1A11F"
					item: "gtceu:stainless_steel_plate"
					random_bonus: 2
					type: "item"
				}
			]
			subtitle: "电力充足吗？"
			tasks: [{
				id: "30ECDAAF21DA2EA9"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:hv_1a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:hv_4a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:hv_8a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:hv_16a_energy_converter"
							}
						]
					}
				}
				title: "任意高压能量转换器"
				type: "item"
			}]
			x: -9.5d
			y: 3.0d
		}
		{
			description: [
				"过滤机壳能有效净化空气中的有害颗粒,创造&e洁净&r环境"
				""
				"建造无尘室时请用&a扳手&r拆卸,否则不会掉落"
				""
				"天花板需全部铺设过滤机壳,仅留1格位置放置无尘室控制器"
			]
			id: "012366E4E4095FC4"
			rewards: [{
				id: "1ED49BD4715F5C22"
				item: {
					Count: 1
					id: "gtceu:titanium_wrench"
					tag: {
						Damage: 0
						GT.Tool: {
							Damage: 0
						}
					}
				}
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "2BEFA1048918A566"
				item: "gtceu:filter_casing"
				type: "item"
			}]
			x: 1.0d
			y: 4.500000000000005d
		}
		{
			dependencies: ["0115271C840CD387"]
			description: ["建议适时&a切换模式&f,从蒸汽发电转为生产苯或&a高辛烷值汽油&f,配合燃气/内燃发电机使用"]
			id: "1AE7E363AFDC976B"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "7D4A3C0082C83545"
				table_id: 822291801189586703L
				type: "loot"
			}]
			tasks: [{
				id: "158C08BCA0775AAB"
				item: "gtceu:steel_large_boiler"
				type: "item"
			}]
			x: -11.5d
			y: 1.0d
		}
		{
			dependencies: ["70C952B8FF3418F6"]
			description: ["是时候升级锅炉了？"]
			id: "0115271C840CD387"
			optional: true
			rewards: [
				{
					count: 6
					id: "1DCAD5968AC33349"
					item: "alltheores:steel_ingot"
					random_bonus: 6
					type: "item"
				}
				{
					count: 2
					id: "1DCFFAFE732C70A5"
					item: "gtceu:stainless_steel_ingot"
					random_bonus: 2
					type: "item"
				}
			]
			subtitle: "蒸汽太多了"
			tasks: [{
				id: "419FB15F3A540B74"
				item: "gtceu:hv_steam_turbine"
				type: "item"
			}]
			x: -9.5d
			y: 1.0d
		}
		{
			description: [
				"少量硫酸配合青色染料与2份盐粉可制成液态青色染料"
				""
				"放心,不需要你手动装桶"
			]
			id: "5B1C5C9F9CCC51EB"
			rewards: [{
				id: "1B5919E9C28B7CC3"
				item: "gtceu:salt_dust"
				random_bonus: 2
				type: "item"
			}]
			tasks: [
				{
					id: "341403FEFBFC92C0"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:dyes/cyan"
						}
					}
					title: "&a青色染料&f"
					type: "item"
				}
				{
					count: 2L
					icon: "gtceu:salt_dust"
					id: "4BF92CAB53EAFC26"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "gtceu:salt_dust"
								}
								{
									Count: 1b
									id: "railcraft:saltpeter_dust"
								}
							]
						}
					}
					title: "&a盐&f"
					type: "item"
				}
			]
			x: -1.0d
			y: -5.5d
		}
		{
			dependencies: ["1957A39483E15508"]
			description: [
				"氯乙烯与少量氧气反应可得&a聚氯乙烯&f"
				""
				"&e&l提示:&r&r值得为此配置&b物流请求系统&r!"
			]
			id: "503E5B82A6C89278"
			rewards: [{
				id: "6857F83D8FD3F6B1"
				item: "gtceu:polyvinyl_chloride_bucket"
				type: "item"
			}]
			tasks: [{
				id: "1E1447D81D30B2DC"
				item: "gtceu:polyvinyl_chloride_bucket"
				type: "item"
			}]
			x: 3.5d
			y: -6.0d
		}
		{
			dependencies: ["6A2D7380E340B77D"]
			description: [
				"跳过乙烯制备教程(前文已涵盖)"
				""
				"将乙烯与氯气在&e化学反应器&r中合成&a氯乙烯&f"
			]
			id: "1957A39483E15508"
			rewards: [{
				id: "7158F0108957AFA6"
				item: "gtceu:ethylene_bucket"
				type: "item"
			}]
			tasks: [{
				id: "17068C6DBE553D0C"
				item: "gtceu:vinyl_chloride_bucket"
				type: "item"
			}]
			x: 3.5d
			y: -7.0d
		}
		{
			description: [
				"氯气来源多样!例如:&e电解&r方钠石、岩盐、盐粉、磷灰石粉或普通盐水"
				""
				"若选择盐水方案,获取方式如下:稳定供应&d恶魂之泪&r时,可与水进行&e化学反应&r"
				""
				"或使用&e搅拌机&r混合盐粉与水,但此时不如直接&e电解&r盐粉"
			]
			id: "6A2D7380E340B77D"
			min_width: 250
			rewards: [{
				id: "318C0812568DBB25"
				item: "gtceu:salt_water_bucket"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "66B862CA537DB886"
				item: "gtceu:chlorine_bucket"
				type: "item"
			}]
			x: 5.0d
			y: -7.0d
		}
		{
			dependencies: [
				"5B1C5C9F9CCC51EB"
				"70C952B8FF3418F6"
				"1AF005E292E409D1"
			]
			description: ["将液体&b青色染料&r与这台机器中的&a玻璃透镜&f结合,可将其染成&b&a玻璃透镜&f(青色)"]
			id: "62161044F3F3AB87"
			rewards: [{
				exclude_from_claim_all: true
				id: "07A027709277FDBF"
				table_id: 822291801189586703L
				type: "loot"
			}]
			tasks: [{
				id: "47CB7449CC8F0EF6"
				item: "gtceu:hv_chemical_bath"
				type: "item"
			}]
			x: 0.0d
			y: -4.5d
		}
		{
			dependencies: [
				"4AAF27F0C27FBDB3"
				"6A2D7380E340B77D"
			]
			description: ["将少量氢气与氯气放入&e化学反应器&r中,可制得氯化氢"]
			id: "54BEC01D84237DBC"
			rewards: [{
				id: "0D1FCABDF38F8263"
				item: "gtceu:chlorine_bucket"
				type: "item"
			}]
			tasks: [{
				id: "16F0BF8BAA58100B"
				item: "gtceu:hydrochloric_acid_bucket"
				type: "item"
			}]
			x: 5.5d
			y: -6.0d
		}
		{
			description: [
				"与氯气类似,氢气也有多种来源途径"
				""
				"例如,你可以尝试&e离心处理&r针铁矿或黄褐铁矿粉,或者采用&e电解法&r——后者或许更符合你的操作习惯."
				""
				"水和盐水都是优质的氢来源,且盐水还能额外提供氯气!"
			]
			id: "4AAF27F0C27FBDB3"
			rewards: [{
				id: "0B6945E709A80205"
				item: "gtceu:salt_water_bucket"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "526401AFE4B0A3E0"
				item: "gtceu:hydrogen_bucket"
				type: "item"
			}]
			x: 6.0d
			y: -7.0d
		}
		{
			description: [
				"[ \"\", { \"text\": \"If you made the \" }, { \"text\":\"黏土&a处理&f线\", \"color\":\"aqua\", \"clickEvent\": { \"action\":\"change_page\", \"value\":\"6275C90E5890C1E4\" }, \"underlined\":\"true\", \"hoverEvent\": { \"action\":\"show_text\", \"contents\": { \"text\":\"&a点击这里&f获取提醒\" } } }, { \"text\":\"之前,你可以简单地暂停\" }, { \"text\":\"电解机\", \"color\":\"yellow\" }, { \"text\":\"来获取黏土粉尘与石粉混合\" } ]"
				""
				"至少制作这些东西不会毁掉桶或搅拌机"
			]
			id: "681110DE6B4E6ED8"
			shape: "square"
			tasks: [{
				id: "472EA07FB476A589"
				item: "gtceu:concrete_bucket"
				type: "item"
			}]
			x: 0.0d
			y: 6.500000000000005d
		}
		{
			description: [
				"可在&b&a进阶搅拌机&f&r中,配合&a程序电路&r设定,混合铁、镍/殷钢、锰、铬粉来制作"
				""
				"&e锰粉&r是加工黑钨矿、锰铝榴石、橄榄石、钽铁矿、软锰矿、钼铅矿或白钨矿时的副产品"
				""
				"&c前瞻建议&r:推荐优先处理钽铁矿和黑钨矿"
				""
				"&d铬粉&r可通过加工铬铁矿或红宝石获取,二者还能通过电解进一步提纯铬产量!"
			]
			id: "4FD6092D9C2A485C"
			min_width: 300
			rewards: [
				{
					count: 2
					id: "28038032F33B2E27"
					item: "gtceu:manganese_dust"
					random_bonus: 2
					type: "item"
				}
				{
					count: 2
					id: "180190869A816D67"
					item: "gtceu:chromium_dust"
					random_bonus: 2
					type: "item"
				}
			]
			tasks: [{
				id: "75E95ED497B33048"
				item: "gtceu:stainless_steel_dust"
				type: "item"
			}]
			x: -7.5d
			y: -3.5d
		}
		{
			dependencies: [
				"7D2033E579767AF8"
				"7985FD6195CF45FD"
			]
			description: [
				"厌倦了&a四处勘探&f矿石却收获寥寥？想寻找深埋地底的石油？你需要&e高压&r级&b勘探仪&r!"
				""
				"该工具能扫描周围4区块半径范围,精准定位目标矿脉"
				""
				"潜行+右键可切换至流体&a侦测模式&f,定位基岩下方的油藏——配合&e&a流体钻探平台&r即可开采这些原油!"
			]
			id: "464110726B823072"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "0DE357DECB5408EE"
				table_id: 822291801189586703L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "01D4B896D65C8BBB"
				item: "gtceu:prospector.hv"
				type: "item"
			}]
			x: -7.5d
			y: 5.5d
		}
		{
			dependencies: ["70C952B8FF3418F6"]
			description: [""]
			id: "7D2033E579767AF8"
			optional: true
			rewards: [
				{
					count: 4
					id: "4CF3A9A09A04A0C5"
					item: "gtceu:chromium_dust"
					random_bonus: 4
					type: "item"
				}
				{
					count: 2
					id: "40C3128ACE20CD10"
					item: "gtceu:gold_single_wire"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "63D4F36863D9FC65"
				item: "gtceu:hv_emitter"
				type: "item"
			}]
			x: -6.5d
			y: 4.5d
		}
		{
			dependencies: ["70C952B8FF3418F6"]
			description: [
				"将&c&a能量水晶粉&f&r放入&e高压灭菌器&r可制成&b能量电池"
				""
				"该电池可储存长达&a10分钟&r的高压电力"
			]
			icon: "gtceu:hv_autoclave"
			id: "7985FD6195CF45FD"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "426B997417B7B1C0"
				table_id: 822291801189586703L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [
				{
					id: "701BFCC15D8BB682"
					item: "gtceu:hv_autoclave"
					type: "item"
				}
				{
					count: 9L
					id: "1656EF71F486E929"
					item: "gtceu:energium_dust"
					type: "item"
				}
			]
			x: -8.5d
			y: 4.5d
		}
		{
			dependencies: ["70C952B8FF3418F6"]
			description: [
				"达到&e高压&r阶段后,&e粉碎机&r将解锁&d副产物&r系统"
				""
				"这些副产物往往极具价值,在进阶过程中会多次派上用场"
			]
			id: "63E4149FF75592C8"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "7CE55F08FEE1B6BB"
				table_id: 822291801189586703L
				type: "loot"
			}]
			shape: "diamond"
			size: 1.25d
			tasks: [
				{
					id: "06B18A65A44DCFCE"
					item: "gtceu:hv_macerator"
					type: "item"
				}
				{
					id: "4EDC2FF341141AC8"
					item: "gtceu:hv_ore_washer"
					type: "item"
				}
				{
					id: "4F10C6E54812EBC1"
					item: "gtceu:hv_thermal_centrifuge"
					type: "item"
				}
			]
			title: "矿石&a处理&f升级"
			x: -7.5d
			y: 4.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"根据&e版权所有&r协议,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若你看到本提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "237142F17C67D5C4"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "226FD985DD96A40B"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "5852963393AB8B26"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 1.0d
			y: 3.5d
		}
	]
	title: "高压电"
}
