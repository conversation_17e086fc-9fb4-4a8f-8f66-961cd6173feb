{
	default_hide_dependency_lines: false
	default_quest_shape: "rsquare"
	filename: "applied_energistics_2"
	group: "1AC60211DE7427FC"
	icon: "ae2:controller"
	id: "07210DDF872160BA"
	images: [
		{
			height: 0.5d
			hover: ["合成&aATM之星&f所需材料"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.5d
			x: 3.0d
			y: 4.25d
		}
		{
			height: 0.5d
			hover: ["合成&aATM之星&f所需材料"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.5d
			x: 5.0d
			y: 5.5d
		}
	]
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"&l欢迎来到 &b&d应用能源2&f&f!\\n\\n&d应用能源2&f(简称 &oAE2&r)是一个功能极其丰富的 &b数字存储&f 模组,可提供超高效存储和各种自动化功能,从游戏初期到终局都能大幅增强你的模组化Minecraft体验.\\n\\n要开始使用AE2,你需要开采模组中的一种重要资源——&b&a赛特斯石英&f&f.就像紫水晶一样,你可以找到 &a赛特斯石英水晶&f,开采它们可获得 &a赛特斯石英&f &e粉尘&f 和 &e晶体&f.\\n\\n如需了解本章节之外的AE2信息,请查阅维基:"
				"&o&bappliedenergistics.github.io&f&r."
			]
			icon: "ae2:certus_quartz_crystal"
			id: "2893F483C10293E6"
			rewards: [
				{
					count: 8
					id: "3B8F9C922DCD426E"
					item: "ae2:certus_quartz_dust"
					type: "item"
				}
				{
					id: "04731B877AA067A3"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			size: 1.5d
			subtitle: "虚拟存储系统"
			tasks: [
				{
					id: "43FD619EF9A41E76"
					item: "ae2:certus_quartz_dust"
					type: "item"
				}
				{
					id: "7B9519E1AF53A9A3"
					item: "ae2:certus_quartz_crystal"
					type: "item"
				}
			]
			title: "&d应用能源2&f"
			x: 0.0d
			y: 0.5d
		}
		{
			dependencies: ["2893F483C10293E6"]
			description: ["在AE2中起步时,你首先需要制作 &b充能器&f 和 &b刻印机&f.\\n\\n&b充能器&f 在通电后可将 &e&a赛特斯石英水晶&f&r 充能.这些 &e充能赛特斯水晶&r 可用于制作 &e福鲁伊克斯晶体&f,这是模组中另一种重要资源.它还能为任何储能物品充电.\\n\\n&b刻印机&f 能将AE2中的各种石英晶体转化为粉尘形态,但更重要的是用于制造特殊的 &e印刷电路&f 和 &e处理器&f,下一章节会详细说明."]
			id: "68B0B3DAF1145191"
			rewards: [
				{
					id: "132524D80F352F06"
					table_id: 727499692191347770L
					type: "random"
				}
				{
					id: "230CB9AC06A29B7A"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "基础机器"
			tasks: [
				{
					id: "29C4195E8366DBDD"
					item: "ae2:charger"
					type: "item"
				}
				{
					id: "1F40760C2108BA36"
					item: "ae2:inscriber"
					type: "item"
				}
			]
			title: "首要之事"
			x: 2.0d
			y: 0.5d
		}
		{
			dependencies: ["68B0B3DAF1145191"]
			description: ["是时候寻找含有 &b&a陨石&f&f 的 &e陨石&f 了.它们可能在地表或地下,中心藏有 &b神秘立方体&f.\\n\\n这个立方体包含刻印机所需的各种 &e压印模板&f,用于制作AE2的特殊合成部件.\\n\\n定位陨石最简单的方法是制作 &e&a陨石罗盘&f&r:将指南针放入充能器中即可获得."]
			icon: "ae2:calculation_processor_press"
			id: "51236544BFEF487B"
			rewards: [
				{
					count: 4
					id: "1745CCFAC5C46D28"
					item: "ae2:sky_stone_block"
					type: "item"
				}
				{
					id: "34DBB0E01FC07555"
					table_id: 727499692191347770L
					type: "random"
				}
				{
					id: "53D03CFF7586058B"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "'漫画#42:寻找鲍比[菲舍尔]'"
			tasks: [
				{
					id: "4D57E005D20BEDB9"
					item: "ae2:meteorite_compass"
					type: "item"
				}
				{
					advancement: "ae2:main/presses"
					criterion: ""
					id: "4D25BF3C4F05025D"
					type: "advancement"
				}
			]
			title: "寻找陨石"
			x: 4.0d
			y: 0.5d
		}
		{
			dependencies: ["68B0B3DAF1145191"]
			description: ["虽然刻印机和充能器本身支持RF/FE能源,但ME网络主要使用其专属的AE能源系统.\\n\\nRF/FE能以2:1的比例转换为AE能源,既可以通过 &aME控制器&f 直接转换,也可以通过 &b&a能源接收器&f&f 实现——后者可连接至网络的任何部分."]
			id: "00611844AFD5C31E"
			rewards: [
				{
					id: "218200BE2C9DD409"
					table_id: 727499692191347770L
					type: "random"
				}
				{
					id: "241ED08CDD530739"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "接入能源"
			tasks: [{
				id: "5B4DDF66C36AF356"
				item: "ae2:energy_acceptor"
				type: "item"
			}]
			title: "&a能源接收器&f"
			x: 3.0d
			y: -0.5d
		}
		{
			dependencies: ["00611844AFD5C31E"]
			description: ["默认情况下,没有 &aME控制器&f 的ME网络仅有800AE的内部缓存(每增加一个控制器可额外提供8kAE).对于大型网络,这可能不足以为连接的设备持续供电.\\n\\n&b&a能源元件&f&f 能显著扩展ME网络的储能:每个普通 &a能源元件&f 提供200kAE,而每个 &b致密型&f &a能源元件&f 提供1.6MAE.这些元件可放置在网络任意位置来增加能源缓冲."]
			id: "037488EF1F3581CE"
			rewards: [
				{
					id: "07AC6981E4FBB7B3"
					table_id: 727499692191347770L
					type: "random"
				}
				{
					id: "20DA7014A7644F65"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "19CE95EDCB04AA89"
					item: {
						Count: 1
						id: "ae2:energy_cell"
						tag: { }
					}
					type: "item"
				}
				{
					id: "3606A3CEDFF76F77"
					item: {
						Count: 1
						id: "ae2:dense_energy_cell"
						tag: { }
					}
					type: "item"
				}
			]
			title: "&a能源元件&f"
			x: 5.0d
			y: -0.5d
		}
		{
			dependencies: ["037488EF1F3581CE"]
			description: ["&b&a能量卡&f&f 可用于大多数可充电设备(如 &a便携元件&f 和后续章节介绍的无线终端),以扩展 &o其&r 能量缓冲."]
			id: "6D54B45CDA70FEAB"
			rewards: [
				{
					id: "4807A39C56865642"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "198B5EB3662F4C18"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			tasks: [{
				id: "42C1EB9094F67B62"
				item: "ae2:energy_card"
				type: "item"
			}]
			title: "&a能量卡&f"
			x: 7.0d
			y: -0.5d
		}
		{
			dependencies: ["51236544BFEF487B"]
			description: ["当你集齐全套所需的&e压印模板&r后,就可以开始制作&e处理器&f了.这是制造绝大多数ME联网设备的重要合成材料."]
			id: "15564C11744D6AA0"
			rewards: [
				{
					id: "6F428D141799FF83"
					type: "xp"
					xp: 100
				}
				{
					id: "622636484932AB15"
					table_id: 5871764666515020368L
					type: "random"
				}
			]
			tasks: [
				{
					id: "2B6D34E0CD1999C9"
					item: "ae2:logic_processor"
					type: "item"
				}
				{
					id: "54C120660252682A"
					item: "ae2:calculation_processor"
					type: "item"
				}
				{
					id: "41282D3DCFEB5136"
					item: "ae2:engineering_processor"
					type: "item"
				}
			]
			title: "处理器"
			x: 6.0d
			y: 0.5d
		}
		{
			dependencies: ["68B0B3DAF1145191"]
			description: ["接下来你需要的最重要资源当属&e福鲁伊克斯&f,它广泛应用于AE2设备中,是制作ME网络所有线缆的基础原料.\\n\\n通常需要批量生产,方法是将&e&a下界石英&f&f、&e&o充能&r&e&a赛特斯石英&f&r和&e红石&f投入水中合成&e&a福鲁伊克斯水晶&f&r,之后可在压印机中研磨成粉."]
			icon: "ae2:fluix_crystal"
			id: "4BF0BB763BFFACF0"
			rewards: [
				{
					count: 4
					id: "724ADD8BA4137929"
					item: "ae2:fluix_dust"
					type: "item"
				}
				{
					id: "6F34DE4A8A1E9829"
					table_id: 727499692191347770L
					type: "random"
				}
				{
					id: "34AA471D42A56F02"
					type: "xp"
					xp: 10
				}
			]
			tasks: [
				{
					id: "0FC1380E32E02533"
					item: "ae2:fluix_dust"
					type: "item"
				}
				{
					id: "4498692EC5F9E09B"
					item: "ae2:fluix_crystal"
					type: "item"
				}
			]
			title: "&a福鲁伊克斯水晶&f"
			x: 3.0d
			y: 1.5d
		}
		{
			dependencies: ["4BF0BB763BFFACF0"]
			description: ["连接ME网络需要线缆.&eME玻璃线缆&f是最基础的线缆类型,也是制作其他线缆的第一步.\\n\\n玻璃线缆及其&e包覆&f版本每段最多传输8个频道.包覆线缆可进一步合成&e致密&f版本(32频道),但总线/终端等'复合'设备需通过普通线缆连接.\\n\\n所有线缆可用染料染色.未染色('福鲁伊克斯色')线缆可连接任意颜色,但不同颜色线缆互不连接."]
			id: "5C22E3103544B120"
			min_width: 300
			rewards: [
				{
					id: "23EF97F2F01671B6"
					table_id: 727499692191347770L
					type: "random"
				}
				{
					id: "2D1445E4299E8619"
					type: "xp"
					xp: 10
				}
			]
			tasks: [
				{
					id: "40A7CC56DACC2623"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "ae2:glass_cable"
						}
					}
					title: "玻璃电缆"
					type: "item"
				}
				{
					id: "64EAD3DE84E94F02"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "ae2:covered_cable"
						}
					}
					title: "包覆电缆"
					type: "item"
				}
				{
					id: "14DEFFB80CC96BC1"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "ae2:covered_dense_cable"
						}
					}
					title: "密集包覆电缆"
					type: "item"
				}
			]
			title: "基础线缆"
			x: 5.0d
			y: 1.5d
		}
		{
			dependencies: ["5C22E3103544B120"]
			description: ["&bME智能线缆&f和&b致密智能线缆&f在频道分配功能上与包覆线缆相同,但会通过彩色线条直观显示当前使用的频道数量."]
			id: "5233A447BAA4593C"
			rewards: [
				{
					id: "7493EF469CDF6FB6"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "7F6B396987F63DB7"
					type: "xp"
					xp: 10
				}
				{
					count: 2
					id: "5EBB888A8D38FAC8"
					item: "ae2:quartz_fiber"
					type: "item"
				}
			]
			shape: "diamond"
			subtitle: "'超·智能·线缆·技术'"
			tasks: [
				{
					id: "7FC3DAA1BD5016A0"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "ae2:smart_cable"
						}
					}
					title: "智能电缆"
					type: "item"
				}
				{
					id: "38E290AC5E011888"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "ae2:smart_dense_cable"
						}
					}
					title: "密集智能电缆"
					type: "item"
				}
			]
			title: "高级线缆"
			x: 5.0d
			y: 3.0d
		}
		{
			dependencies: ["15564C11744D6AA0"]
			description: ["&e终端&f提供ME网络内容访问功能.标准&e&aME终端&f&f将所有物品显示为可存取的网格界面.\\n\\n&eME合成终端&f在&aME终端&f基础上增加了合成网格,可直接使用网络中可见的物品进行合成."]
			id: "22C4318523A43B49"
			rewards: [
				{
					id: "6D1EBB1DBB711A5A"
					table_id: 5871764666515020368L
					title: "&a随机奖励&f"
					type: "random"
				}
				{
					id: "7B32D21CD627FB4A"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "[此处应有Linux用户梗]"
			tasks: [
				{
					id: "6C7282A058006A21"
					item: "ae2:terminal"
					type: "item"
				}
				{
					id: "2CFD7CA282DFB2FF"
					item: "ae2:crafting_terminal"
					type: "item"
				}
			]
			title: "终端"
			x: 8.0d
			y: 0.5d
		}
		{
			dependencies: [
				"40A7CC56DACC2623"
				"2F16B6A173525277"
			]
			description: ["&d应用能源2&f的核心是其数字存储系统,使用&e&a存储单元&f&f实现.可通过&e&aME箱子&f&r(单单元)或&e&aME驱动器&f&r(多单元)访问.\\n\\n&b&aME箱子&f&f专用于单个存储单元,顶部自带专属终端界面.&b&aME驱动器&f&f可容纳10个存储单元(占用1格方块/1个ME频道),但需通过外部终端访问."]
			id: "4E8A05C3BFA80540"
			rewards: [
				{
					id: "107B36337206758B"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "5D0CC7AC4BD78134"
					type: "xp"
					xp: 100
				}
			]
			shape: "gear"
			size: 1.5d
			subtitle: "物品文件化管理系统"
			tasks: [
				{
					id: "74AA3FA06B3574A8"
					item: "ae2:chest"
					type: "item"
				}
				{
					id: "58C4DF6CFBDF8577"
					item: "ae2:drive"
					type: "item"
				}
			]
			title: "&a存储&f"
			x: 11.0d
			y: 1.5d
		}
		{
			dependencies: ["40A7CC56DACC2623"]
			description: ["为简化操作,AE2提供了一整套用于管理ME数据的设备,即在世界各地移动存储物品.\\n\\n其中就包括&b&aME接口&f&f.作为输入设备,接口允许通过外部管道输入任何物品/流体等,这些内容会自动存储到接口连接的ME网络中.\\n\\n作为输出设备,接口可配置为从ME网络到其内部库存中保持特定物品的可用存量,便于其他玩家或管道等外部来源获取物品."]
			icon: "ae2:interface"
			id: "74FC0DDDB91DB172"
			rewards: [
				{
					id: "12DC218C9BBC8422"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "341F5229D281635E"
					type: "xp"
					xp: 10
				}
			]
			shape: "gear"
			size: 1.5d
			subtitle: "输入输出,一应俱全!"
			tasks: [{
				id: "7241918F270CA402"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "ae2:interface"
					}
				}
				title: "&aME接口&f"
				type: "item"
			}]
			title: "输入输出设备"
			x: 24.0d
			y: 1.5d
		}
		{
			dependencies: ["40A7CC56DACC2623"]
			description: ["存储系统固然重要,但如果塞满需要手动加工的原材,那还有什么意义？\\n\\nAE2的&e自动合成&f系统始于&bME样板终端&f.&a样板&f终端存放着被称为&b样板&f的特殊物品(下个任务详解),这些样板记录了将输入物品转化为特定产出的配方.每个终端最多容纳9个样板.\\n\\n通常需要在基地各处部署多个样板终端,这时&b样板&a访问终端&f&f就能让你远程访问整个ME网络中所有终端的内容."]
			icon: "ae2:pattern_provider"
			id: "51DE3157DE3E57B8"
			rewards: [
				{
					id: "50D0A08E66B3F6DF"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "12405240582D9D1A"
					type: "xp"
					xp: 10
				}
			]
			shape: "gear"
			size: 1.5d
			subtitle: "订单已接收"
			tasks: [
				{
					id: "338A6DA0D711B7DC"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "ae2:pattern_provider"
						}
					}
					title: "ME样板供应器"
					type: "item"
				}
				{
					id: "299121F0AFF40604"
					item: "ae2:pattern_access_terminal"
					type: "item"
				}
			]
			title: "自动合成"
			x: 13.0d
			y: -2.5d
		}
		{
			dependencies: [
				"22C4318523A43B49"
				"78311531069807DE"
			]
			description: ["大型ME网络的最后关键组件就是&b&aME控制器&f&f.\\n\\n单个控制器方块每面提供32个频道(总计216个),远超临时网络的8个标准频道.\\n\\n但&aME控制器&f实际上是多方块结构,最大可扩展至7x7x7.只要不在同一平面被4个方块包围,每个独立方块都能提供独立频道组.\\n\\n一个ME网络同时只能存在一个多方块控制器.若在网络不同部位连接多个控制器,将导致整个网络冲突宕机."]
			id: "2F16B6A173525277"
			rewards: [
				{
					id: "345C7C78BABD07F6"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "6593BF772EE96538"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "6C80AA2FD67BD192"
				item: "ae2:controller"
				type: "item"
			}]
			title: "&aME控制器&f"
			x: 9.0d
			y: 1.5d
		}
		{
			dependencies: ["2893F483C10293E6"]
			description: ["&b石英扳手&f具备科技模组扳手的所有功能:右键点击旋转方块,潜行右键拆卸设备.\\n\\nAE2提供赛特斯石英和下界石英两种材质的扳手."]
			id: "1B686954D34A0F23"
			rewards: [{
				id: "3F733DD53ED27710"
				type: "xp"
				xp: 10
			}]
			shape: "circle"
			subtitle: "功能如你所想"
			tasks: [{
				id: "1076BC82EECB73F5"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "ae2:quartz_wrench"
					}
				}
				title: "石英扳手"
				type: "item"
			}]
			title: "石英扳手"
			x: 1.0d
			y: 1.5d
		}
		{
			dependencies: [
				"22C4318523A43B49"
				"1076BC82EECB73F5"
			]
			description: ["&b&a网络工具&f&f是AE2中的重要设备,可显示网络详情如能耗和存储状态.\\n\\n额外功能是自带存储&e升级卡&f的小型库存,查看可升级设备界面时可直接替换.但不同于普通扳手,它不能旋转ME网络方块,仅支持潜行拆卸."]
			id: "6431A384DDFBF439"
			tasks: [{
				id: "7BDCDEB679A9969C"
				item: "ae2:network_tool"
				type: "item"
			}]
			title: "&a网络工具&f"
			x: 9.0d
			y: -0.5d
		}
		{
			dependencies: ["4E8A05C3BFA80540"]
			description: ["每个存储单元的容量由其制作时使用的&e组件&f决定.\\n\\n最基础的组件是&b&a1k-ME存储组件&f&f,能为存储单元提供&e1024&f'字节'的存储空间.更多细节将在后面说明."]
			id: "2FB231069D2E4E77"
			rewards: [
				{
					id: "6E23A177701DE35C"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "7878EA4ADE367154"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "第一个千字节单元"
			tasks: [{
				id: "64CCF1FB42AA41CE"
				item: "ae2:cell_component_1k"
				type: "item"
			}]
			title: "创建你的第一个&a存储单元&f"
			x: 14.0d
			y: 1.5d
		}
		{
			dependencies: [
				"64CCF1FB42AA41CE"
				"2FB231069D2E4E77"
			]
			description: ["&a第二&f级存储组件,提供&e4096&f字节存储空间."]
			id: "3B42CCC19D23EC6D"
			rewards: [
				{
					id: "0DED7909D8F260FB"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "4D1AC818C4203ADC"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "x4"
			tasks: [{
				id: "066F1BBF3D0863C5"
				item: "ae2:cell_component_4k"
				type: "item"
			}]
			title: "4k存储组件"
			x: 15.0d
			y: 2.5d
		}
		{
			dependencies: [
				"066F1BBF3D0863C5"
				"3B42CCC19D23EC6D"
			]
			description: ["第三级存储组件,提供&e16384&f字节存储空间."]
			id: "219932CB19258C16"
			rewards: [
				{
					id: "3D78D39AC9F9149F"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "42CEB9C64CCDCFEC"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "x4^2"
			tasks: [{
				id: "076237ECA6D5DE58"
				item: "ae2:cell_component_16k"
				type: "item"
			}]
			title: "16k存储组件"
			x: 14.5d
			y: 3.0d
		}
		{
			dependencies: [
				"076237ECA6D5DE58"
				"219932CB19258C16"
			]
			description: ["第四级存储组件,提供&e65536&f字节存储空间."]
			id: "523853C1C4E688BA"
			rewards: [
				{
					id: "74692DCB87938B3B"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "16FC3AB2ACBFF4B3"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "x4^3"
			tasks: [{
				id: "32FFC36DEEA7792D"
				item: "ae2:cell_component_64k"
				type: "item"
			}]
			title: "64k存储组件"
			x: 15.0d
			y: 3.5d
		}
		{
			dependencies: [
				"32FFC36DEEA7792D"
				"523853C1C4E688BA"
			]
			description: ["第五级也是AE2标准最高级存储组件,提供&e262144&f字节存储空间."]
			id: "5F56892CD904C40F"
			rewards: [
				{
					id: "109149BBD22105C5"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "4DE1354173902859"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "x4^4"
			tasks: [{
				id: "2C4616CD2EBB58C6"
				item: "ae2:cell_component_256k"
				type: "item"
			}]
			title: "256k存储组件"
			x: 15.5d
			y: 3.0d
		}
		{
			dependencies: [
				"2C4616CD2EBB58C6"
				"5F56892CD904C40F"
			]
			description: ["在大型模组包游玩过程中,当标准最高级存储单元也无法满足物品存储需求时,&d&aMEGA存储单元&f&f扩展模组就能派上用场.\\n\\n该模组新增的第一级组件是&b&a1M-MEGA存储组件&f&f,提供1024千字节(即&e1048576&f字节)存储空间."]
			id: "460A8F17F3ED6CAF"
			rewards: [
				{
					id: "006EED7533375FD2"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "295E028CA7E21B31"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "414F819C190CFDCF"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "第一个百万字节单元"
			tasks: [{
				id: "19BF2D67291056DE"
				item: "megacells:cell_component_1m"
				type: "item"
			}]
			title: "1M存储组件"
			x: 16.5d
			y: 4.0d
		}
		{
			dependencies: [
				"19BF2D67291056DE"
				"460A8F17F3ED6CAF"
			]
			description: ["&a第二&f级MEGA存储组件,提供&e4194304&f(4096k)字节存储空间."]
			id: "25DBA00422301EDC"
			rewards: [
				{
					id: "5696B9C3D424839F"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "71734366561CE3E6"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "32340AD4F677375F"
				item: "megacells:cell_component_4m"
				type: "item"
			}]
			title: "4M存储组件"
			x: 19.0d
			y: 3.9999999999999996d
		}
		{
			dependencies: [
				"32340AD4F677375F"
				"25DBA00422301EDC"
			]
			description: ["第三级MEGA存储组件,提供&e16777216&f(16384k)字节存储空间."]
			id: "0E809747193ED3A9"
			rewards: [
				{
					id: "2083392434D82627"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "61A6C4D61B2B0E98"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "76A0C582AECC4702"
				item: "megacells:cell_component_16m"
				type: "item"
			}]
			title: "16M存储组件"
			x: 19.5d
			y: 4.499999999999998d
		}
		{
			dependencies: [
				"76A0C582AECC4702"
				"0E809747193ED3A9"
			]
			description: ["第四级MEGA存储组件,提供&e67108864&f(65536k)字节存储空间."]
			id: "3CE3D9245F8EC005"
			rewards: [
				{
					id: "4637E22B312275B1"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "591679B6C9CF5681"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "50CD83AC904EC47E"
				item: "megacells:cell_component_64m"
				type: "item"
			}]
			title: "64M存储组件"
			x: 20.0d
			y: 3.9999999999999996d
		}
		{
			dependencies: [
				"50CD83AC904EC47E"
				"3CE3D9245F8EC005"
			]
			description: ["第五级也是最高级MEGA存储组件,提供&e268435456&f(262144k)字节存储空间."]
			id: "51A57E142C686C8F"
			rewards: [
				{
					id: "3F61E69AB87C08FF"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "6871321BB014C03D"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "0D5DA83B0C0665C8"
				item: "megacells:cell_component_256m"
				type: "item"
			}]
			title: "256M存储组件"
			x: 19.5d
			y: 3.5d
		}
		{
			dependencies: ["2FB231069D2E4E77"]
			description: ["使用1k存储组件,你现在可以制作1k ME物品&a存储单元&f来建立数字物品存储系统.由于大部分存储需求都来自物品,你可能需要制作多个&bME物品&a存储单元&f&f.\\n\\n每个ME物品&a存储单元&f最多可存储63种不同类型物品.每新增一种物品类型都会占用存储单元总字节容量的特定比例.对于&a循环检测&f已存在的类型,每1'字节'可存储8个同类型物品.\\n\\n例如:一个仅存储圆石的1k物品&a存储单元&f最多可容纳8128个圆石(8*1024b - 8b/类型*1类型).\\n\\n空存储单元可通过潜行状态下手持右键点击空气来拆解,回收其组件和外壳."]
			id: "361CCBD353D6FF34"
			rewards: [
				{
					id: "402B607EA6D67580"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "318D3CF0DD1E3A58"
					type: "xp"
					xp: 10
				}
				{
					id: "06150B632CA535D8"
					item: "ae2:item_cell_housing"
					type: "item"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "52521FCD58B2FEF0"
				item: {
					Count: 1
					id: "ae2:item_storage_cell_1k"
					tag: { }
				}
				title: "ME物品&a存储单元&f"
				type: "item"
			}]
			title: "物品存储单元"
			x: 16.5d
			y: 1.5d
		}
		{
			dependencies: ["361CCBD353D6FF34"]
			description: ["并非只有物品才能存储在ME存储单元中.&bME流体&a存储单元&f&f可以储存液体,例如水、熔岩以及各种模组添加的油类和燃料,这里仅举几个例子.\\n\\n请注意,所有存储单元无论设计用于存储什么,其区别仅在于外壳；物品和流体存储单元都使用相同类型的存储组件来制作.\\n\\n对于流体,1字节等于8桶(8000mb)."]
			id: "5E24012A3D9B72A1"
			rewards: [
				{
					id: "1AB0AE41CCA6C48E"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "687E190D01E7344A"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "61D93B13D496547D"
				item: {
					Count: 1
					id: "itemfilters:id_regex"
					tag: {
						value: ":fluid_storage_cell_"
					}
				}
				title: "ME流体&a存储单元&f"
				type: "item"
			}]
			title: "流体存储"
			x: 18.5d
			y: 1.5d
		}
		{
			dependencies: ["4E8A05C3BFA80540"]
			description: ["&b&a元件工作台&f&f允许对存储单元进行'分区'以存储特定物品,即设置白名单过滤器.它还允许使用某些升级卡对存储单元进行升级,例如&a反相卡&f,将上述白名单改为黑名单.\\n\\n通过工作台,还可以为存储单元设置更高或更低的'优先级',即让该存储单元优先接收某些物品直到装满,或等待其他更高优先级的存储单元先装满."]
			id: "2F556E7919582D2D"
			rewards: [
				{
					id: "60C2464FFC06FB37"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "4956E92D2FDA5190"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "19F84B1451F7D602"
				item: "ae2:cell_workbench"
				type: "item"
			}]
			title: "&a元件工作台&f"
			x: 12.0d
			y: 2.5d
		}
		{
			dependencies: ["4E8A05C3BFA80540"]
			description: ["&b&aME-IO端口&f&f允许快速重新排列ME网络存储的内容,在不同的存储介质之间转移,例如ME存储单元和连接到存储总线的外部容器.\\n\\n当在左侧的输入槽中插入存储单元时,可以切换&aIO端口&f的功能,将存储单元中的物品清空到其他ME存储中,或者用分散在其他存储中的特定物品填充(最好是分区过的)存储单元."]
			id: "7B7D1F0CB326B28F"
			rewards: [
				{
					id: "32864F0FE8996DE8"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "4531F48DEB750518"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "碎片整理"
			tasks: [{
				id: "23261340EF9D48AF"
				item: "ae2:io_port"
				type: "item"
			}]
			title: "&aME-IO端口&f"
			x: 12.0d
			y: 0.5d
		}
		{
			dependencies: ["74FC0DDDB91DB172"]
			description: ["&bME输入总线&f会定期从总线所朝向的外部存储中吸取物品.可以选择设置过滤器,仅从该容器中吸取特定物品."]
			id: "5E7E35CCAF1C88EE"
			rewards: [
				{
					id: "6F34C1E65B9B9204"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "462A0C71B591C11F"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "输入"
			tasks: [{
				id: "060BD30C77262BDA"
				item: "ae2:import_bus"
				type: "item"
			}]
			title: "ME输入总线"
			x: 23.0d
			y: 0.5d
		}
		{
			dependencies: ["74FC0DDDB91DB172"]
			description: ["&bME输出总线&f会定期将其白名单过滤器中的物品输出到总线所朝向的外部存储中.与输入总线不同,输出总线必须设置过滤器才能工作."]
			id: "083D458032F0325C"
			rewards: [
				{
					id: "0F7B866B6B54A6D2"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "311337C7850C46F1"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "输出"
			tasks: [{
				id: "6D6E070CB7998FA1"
				item: "ae2:export_bus"
				type: "item"
			}]
			title: "ME输出总线"
			x: 25.0d
			y: 0.5d
		}
		{
			dependencies: ["5E7E35CCAF1C88EE"]
			description: ["&b&aME破坏面板&f&f可用于自动破坏其前方的任何方块,并将方块掉落的物品直接送回ME网络.\\n\\n作为额外的好处,破坏面板可以像普通挖掘工具一样附魔,以相同的方式影响方块的掉落.例如,当附魔时运时,它非常适合处理矿石方块.\\n任何附加的附魔都会显著增加面板每次&a破坏&f方块时的能量消耗.给面板附魔&e效率&f会降低所有其他附魔带来的能量消耗,而&e耐久&f附魔则让面板有时可以完全不消耗能量,类似于普通工具上的耐久附魔."]
			id: "140DE53DC0FCD9F4"
			rewards: [
				{
					id: "6EA69EB703D93D1F"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "43B0C50B9F25E213"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "进入"
			tasks: [{
				id: "3F715007AD9C0D58"
				item: "ae2:annihilation_plane"
				type: "item"
			}]
			title: "ME破坏面板"
			x: 23.0d
			y: -1.5d
		}
		{
			dependencies: ["083D458032F0325C"]
			description: ["&b&aME成型面板&f&f的功能类似于存储总线,但将世界本身视为其存储介质.换句话说,它会将过滤器中的任何方块直接放置在其前方.\\n\\n这在某些情况下非常有用,例如某些方块只需放置并用特定工具破坏即可进行处理."]
			id: "525F25F4ADE45B50"
			rewards: [
				{
					id: "0850790D93DB56E2"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "4DA3D1592D0E57DA"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "取出"
			tasks: [{
				id: "0BEBC087F970093E"
				item: "ae2:formation_plane"
				type: "item"
			}]
			title: "&aME成型面板&f"
			x: 25.0d
			y: -1.5d
		}
		{
			dependencies: ["74FC0DDDB91DB172"]
			description: ["&bP2P&f(点对点)是AE2中的强大系统,无需任何中间ME存储即可实现物品、流体等内容的传输.\\n\\n用特定物品右键点击P2P通道可将其&e调谐&f为传输其他内容的通道,例如通过管道传输物品、通过电缆传输能量、红石信号以及(默认情况下)甚至ME连接本身.\\n\\nP2P通道必须使用&e&a内存卡&f&f相互链接,详情见下一任务."]
			id: "1710B3D05215A71E"
			rewards: [
				{
					id: "005990362F9FDD61"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "682CBB4D63EC2625"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "去掉中间商"
			tasks: [{
				id: "36A7C775D94798EE"
				item: "ae2:me_p2p_tunnel"
				type: "item"
			}]
			title: "&aP2P通道&f"
			x: 26.0d
			y: 1.5d
		}
		{
			dependencies: ["74FC0DDDB91DB172"]
			description: ["&bME存储总线&f面向外部存储容器时,可使该容器如同ME网络的一部分般使用,允许纯粹通过ME从容器中取出或放入物品.\\n\\n存储总线可设置过滤器并分配特定优先级,使特定物品优先进入连接的存储,但不会将网络中其他位置的过滤物品自动转移到其连接的存储中."]
			id: "7EFBAF3E281D2EBE"
			rewards: [
				{
					id: "7EC06E5DA9EA41BC"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "74C059194CC4F45D"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "备用箱子"
			tasks: [{
				id: "294FA6663BE38B7C"
				item: "ae2:storage_bus"
				type: "item"
			}]
			title: "ME存储总线"
			x: 24.0d
			y: -0.5d
		}
		{
			dependencies: ["1710B3D05215A71E"]
			description: ["&b&a内存卡&f&f是具有两种功能的工具.&a简单&f的功能是保存各种设备的配置(如白名单过滤器),以便复制到同类型的其他设备上.\\n\\n&a秒&f的功能是将&eP2P通道&f链接在一起.链接时,被链接的P2P通道会被分配一个唯一ID,该ID存储在内存卡上以供进一步链接."]
			id: "55186B8602689B66"
			rewards: [
				{
					id: "433DCE2CD821C784"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "58A650BEBD72CCDB"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			tasks: [{
				id: "2D19A4EF2E4B7A55"
				item: {
					Count: 1
					id: "ae2:memory_card"
					tag: { }
				}
				title: "&a内存卡&f"
				type: "item"
			}]
			title: "&a内存卡&f"
			x: 26.0d
			y: -0.5d
		}
		{
			dependencies: ["2F556E7919582D2D"]
			description: ["&b均衡分配卡&f是存储元件的升级,可预先分配一定数量的物品,供任何单一类型占用.\\n\\n此行为类似于功能性存储抽屉,每个隔间固定存放一定数量的堆叠物品,防止物品从一个隔间溢出到其他隔间并挤占其他类型的物品空间."]
			id: "3195A7AA874163CD"
			rewards: [
				{
					id: "70C47DA0DCEA52BB"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "7425BEBDF51C1684"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			subtitle: "ME存储&m元件&r抽屉"
			tasks: [{
				id: "5E1E2F6E86A3E0F3"
				item: "ae2:equal_distribution_card"
				type: "item"
			}]
			title: "均衡分配卡"
			x: 12.0d
			y: 3.5d
		}
		{
			dependencies: ["2F556E7919582D2D"]
			description: ["此行为类似于功能性存储抽屉,每个隔间固定存放一定数量的堆叠物品,防止物品从一个隔间溢出到其他隔间并挤占其他类型的物品空间."]
			id: "33ADE41526C39AFD"
			rewards: [
				{
					id: "7FDED9CF7F39532F"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "48C92D1EB16C0CA2"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			tasks: [{
				id: "2D72E699C4D506CE"
				item: "ae2:void_card"
				type: "item"
			}]
			title: "溢出销毁卡"
			x: 13.0d
			y: 2.5d
		}
		{
			dependencies: ["361CCBD353D6FF34"]
			description: ["&b&a便携元件&f&f与普通元件功能相同,可插入箱子或驱动器并填充物品.\\n\\n但与普通元件不同,其内容也可通过元件物品本身独立访问,有点像数字化的ME风格背包."]
			id: "77C9EE701F72586D"
			rewards: [
				{
					id: "6A5F5E6ABCD405F1"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "3280EFC4446DD684"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "65C439FD14C5EEB9"
				item: {
					Count: 1
					id: "itemfilters:id_regex"
					tag: {
						value: ":portable_(.*)_cell_"
					}
				}
				title: "ME &a便携元件&f"
				type: "item"
			}]
			title: "&a便携存储&f"
			x: 17.5d
			y: 2.5d
		}
		{
			dependencies: ["7EFBAF3E281D2EBE"]
			description: ["&b&a容量卡&f&f可使升级后的总线携带更大的过滤器.升级后的导入和导出总线可从仅支持一个过滤物品扩展到最多九个槽位的过滤器,而存储总线则从18个槽位扩展到最多63个."]
			id: "371A382CF1DDF2B2"
			rewards: [
				{
					id: "370E5557C95C8C9D"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "45FEE3C549C049B0"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			tasks: [{
				id: "6DC55B5EA1534444"
				item: "ae2:capacity_card"
				type: "item"
			}]
			title: "&a容量卡&f"
			x: 24.0d
			y: -2.5d
		}
		{
			dependencies: ["74FC0DDDB91DB172"]
			description: ["AE2中一个重要概念是'&e子网划分&f'技术,即通过额外的独立ME网络(称为&e子网&f)与主网络协同工作,执行特定功能或流程.\\n\\n子网与完全独立网络的区别在于使用&b&a石英纤维&f&f作为连接部件.当在两段不相连的线缆间放置&a石英纤维&f时,它不会传输任何数据或频道,仅传递能量.\\n\\n因此,纤维另一端的子网可完全由主网络供能,无需额外发电设备."]
			id: "2077D64428E9C067"
			rewards: [
				{
					id: "165710BE640551ED"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "3337CC6EF03730C7"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "这个功能不仅限于制作线缆"
			tasks: [{
				id: "05A8DD006B623032"
				item: "ae2:quartz_fiber"
				type: "item"
			}]
			title: "忘记提及"
			x: 25.0d
			y: 2.5d
		}
		{
			dependencies: ["51DE3157DE3E57B8"]
			description: ["&b样板&f用于存储由样板供应器执行的编码配方.要在样板上编码配方,必须使用&bME样板编码终端&f.\\n\\n样板可设置为存储常规&e合成&f配方(需要在其供应器表面使用&e&a分子装配室&f&f),或更通用的'&e处理&f'配方(供应器可将输入物品发送至其他机器或专用处理设备)."]
			icon: "ae2:blank_pattern"
			id: "2C04B3BA507D5673"
			rewards: [
				{
					count: 8
					id: "7E23E751506B04D0"
					item: "ae2:blank_pattern"
					type: "item"
				}
				{
					id: "0741594A950C662F"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "20EB24D02799A27D"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "58BF9A4EAD7C88E6"
					item: "ae2:pattern_encoding_terminal"
					type: "item"
				}
				{
					id: "08D08A1400F7348F"
					item: "ae2:blank_pattern"
					type: "item"
				}
			]
			title: "样板"
			x: 14.0d
			y: -3.5d
		}
		{
			dependencies: ["2C04B3BA507D5673"]
			description: ["&b&a分子装配室&f&f是AE2的自动工作台,用于执行所有&e合成&f样板任务.\\n\\n&a分子装配室&f可为连接的ME设备传输能量和频道,但自身不占用频道.单个样板供应器最多可连接6个分子装配室(若使用)以实现并行合成.\\n\\n每个分子装配室都有专用插槽存放单个合成样板,供能时可独立运作.此模式下,放入匹配物品将自动按样板合成结果."]
			id: "4597D3B3BDC2BED5"
			rewards: [
				{
					id: "48E60A68CD703710"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "59D9A5E482B1D2A6"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "5639418C0364E5A1"
				item: "ae2:molecular_assembler"
				type: "item"
			}]
			title: "&a分子装配室&f"
			x: 16.0d
			y: -3.5d
		}
		{
			dependencies: ["51DE3157DE3E57B8"]
			description: ["配置特定物品及其数量后,&b&aME标准发信器&f&f会根据网络中该物品存储量(低于/高于/等于设定值)发出红石信号.\\n\\n例如可用于当资源低于最低储量时,自动触发红石信号启动机器进行自动合成."]
			id: "3DDB0DDA7571B2C1"
			rewards: [
				{
					id: "7791DE05E46C6030"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "2465F9C03BFFB3B8"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "3E4BDC8D3B7F727C"
				item: "ae2:level_emitter"
				type: "item"
			}]
			title: "&aME标准发信器&f"
			x: 15.0d
			y: -2.5d
		}
		{
			dependencies: ["3DDB0DDA7571B2C1"]
			description: ["单个ME设备可配置为响应红石信号.安装&b&a红石卡&f&f后,设备可设置为仅在红石供能时工作.\\n\\n通过使用&bME开关总线&f,此功能可应用于整个ME网络的&o区段&r.总线另一侧网络区段仅在红石供能时上线(使用&e反转&f开关总线时则下线)."]
			id: "1AAF0B31B47AF23D"
			rewards: [
				{
					id: "15D28C974906C579"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "11376476D428D86A"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			tasks: [
				{
					id: "4D92DB15F45D3F10"
					item: "ae2:redstone_card"
					type: "item"
				}
				{
					id: "651CCE50554D6ABC"
					item: "ae2:toggle_bus"
					type: "item"
				}
			]
			title: "红石控制ME"
			x: 17.0d
			y: -2.5d
		}
		{
			dependencies: ["1AAF0B31B47AF23D"]
			description: ["当作为升级组件应用于任何支持的设备(如接口或输出总线)时,&b合成卡&f可使该设备自动发送自身对所需(已过滤)物品的合成请求.可设置合成CPU仅响应这些请求,以避免占用玩家请求合成的CPU资源.\\n\\n当&e等级发射器&f装备合成卡时,可配置其通过红石信号直接参与合成:既可在检测到指定物品的合成任务时&o持续&r发射信号,也可专门&o触发&r该物品的合成.后者适用于仅需红石信号即可触发合成的情况,甚至无需合成模板."]
			id: "5E6585F7627247E3"
			rewards: [
				{
					id: "24B873E672174D86"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "09DCAA3326CEE78F"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			subtitle: "自动化升级形态!"
			tasks: [{
				id: "356C06E894DC659B"
				item: "ae2:crafting_card"
				type: "item"
			}]
			title: "合成卡"
			x: 19.0d
			y: -2.5d
		}
		{
			dependencies: ["51DE3157DE3E57B8"]
			description: ["AE2自然提供了大幅加速晶体生长的专属方案.\\n\\n将这些设备环绕萌芽晶体布置,接通能源后即可见证晶体快速生长!"]
			id: "5AA3E5DFECB4AC4D"
			rewards: [
				{
					id: "0BE24B3629BD5016"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "67FE782D7A821701"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "3837D36BE3CC29A6"
				item: "ae2:growth_accelerator"
				type: "item"
			}]
			title: "&a生长加速器&f"
			x: 12.0d
			y: -3.5d
		}
		{
			dependencies: ["4597D3B3BDC2BED5"]
			description: ["&b&a加速卡&f&f的效果取决于升级设备类型:可提升设备运行速度,或使其单次执行更多操作.\\n\\n对于&e&a分子装配室&f&f,装备全套5张卡可将合成耗时从无卡时的1秒缩短至1&o游戏刻&r."]
			id: "1F7DFA5AA65F2812"
			rewards: [
				{
					id: "6223D6D4A01B9122"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "394DBCEAD38760C0"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			tasks: [{
				id: "495253F1301A8070"
				item: "ae2:speed_card"
				type: "item"
			}]
			title: "加速卡"
			x: 18.0d
			y: -3.5d
		}
		{
			dependencies: ["40A7CC56DACC2623"]
			description: ["除物品与流体存储外,AE2还原生支持将完整&e建筑结构&f存入存储元件,其原理与&d更多空间/压缩空间&f模组类似.\\n\\n&b&a空间IO端口&f&f可通过红石信号激活,将&b空间 containment结构&f内的建筑捕获至特制存储元件中.\\n\\n若生物或玩家在空间捕获时处于SCS内,他们也将被传送至该空间所在的专属维度.若您是被捕获者,请确保有往返该维度的方式."]
			id: "01F3F0C25BA72BDA"
			rewards: [
				{
					id: "2AB07DF6AB7560BF"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "71A9F7793267D5CD"
					type: "xp"
					xp: 10
				}
			]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "0EFC322997906572"
				item: "ae2:spatial_io_port"
				type: "item"
			}]
			title: "空间IO"
			x: 16.5d
			y: 6.0d
		}
		{
			dependencies: ["01F3F0C25BA72BDA"]
			description: ["&a空间&f containment结构(SCS)由&b&a空间塔&f&f构成的立方体框架组成,用于界定待捕获空间.最小配置需沿长宽高三个维度各布置一排空间塔基座.\\n\\n空间IO功能极其耗能,尤其在捕获大空间时(最大128x128x128).在目标空间周围增加空间塔数量可提升&e能效&f,降低捕获能耗.\\n\\n需注意每个空间塔(无论长度)均占用1个频道.超大空间项目建议搭建专用网络并配备控制器."]
			id: "18DFB25DC48D8BF7"
			rewards: [
				{
					id: "664A4B1C34FC382C"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "60430373241A6839"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "4E0C5E3A98293C08"
				item: "ae2:spatial_pylon"
				title: "空间单元组件"
				type: "item"
			}]
			title: "&a空间塔&f"
			x: 19.5d
			y: 6.0d
		}
		{
			dependencies: ["18DFB25DC48D8BF7"]
			description: ["&b&a空间锚&f&f是空间IO系列的配套设备,功能为区块加载器.接入ME网络后,只要网络保持通电,该锚定器将强制加载ME网络所有线缆与设备占据的区块(子网络除外)."]
			id: "2897FA291E5A38D8"
			rewards: [
				{
					id: "1560015D5E8AFFEA"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "29737736E2B314EA"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "68A36AB341CC016D"
				item: "ae2:spatial_anchor"
				type: "item"
			}]
			title: "&a空间锚&f"
			x: 20.5d
			y: 5.0d
		}
		{
			dependencies: ["18DFB25DC48D8BF7"]
			description: ["&b空间 &a存储单元&f&f 用于各自容纳一个定义好的空间体积,共有三种不同容量,最大可存储2x2x2、16x16x16和128x128x128方块的空间.\\n\\n编码时,存储单元会被分配到空间存储&e维度&f中的一个区域,并获得与该区域对应的唯一ID.已编码的存储单元仍可通过&a空间IO端口&f来提取存储的方块和实体.\\n\\n此后,任何位于存储单元空间区域内或与初始编码时使用的SCS大小相同的SCS中的方块,都可以分别移回主世界或空间维度.如果空间区域和SCS中同时存在方块,它们将简单地交换位置."]
			id: "6F3D0A248B5A9CA2"
			rewards: [
				{
					id: "40FB6290F9B4CF05"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "0CB125E5654F810B"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "75924053D6F5B242"
				item: {
					Count: 1
					id: "itemfilters:id_regex"
					tag: {
						value: "ae2:spatial_storage_cell_"
					}
				}
				title: "空间&a存储单元&f"
				type: "item"
			}]
			title: "空间存储单元"
			x: 18.5d
			y: 5.0d
		}
		{
			dependencies: ["51DE3157DE3E57B8"]
			description: ["在执行自动合成任务之前,你需要一个设备来存储合成请求本身以及多步合成过程中的中间物品.这个设备被称为&e合成CPU&f.\\n\\n合成CPU是一个多方块结构,至少需要一个&b合成存储&f方块,可选地搭配其他合成单元.这个多方块结构可以做成任意大小,但必须是一个实心长方体才能正常形成和运作."]
			id: "30E853CE699E669B"
			rewards: [
				{
					id: "380CA842CF1F8374"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "57230DB26A88BF90"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "下载更多内存"
			tasks: [{
				id: "08DA73B1AC17E5F5"
				item: {
					Count: 1
					id: "itemfilters:id_regex"
					tag: {
						value: "(.*)crafting_storage"
					}
				}
				title: "合成存储器"
				type: "item"
			}]
			title: "合成存储"
			x: 14.0d
			y: -1.5d
		}
		{
			dependencies: ["30E853CE699E669B"]
			description: ["&b&a并行处理单元&f&f 通过让模式提供者更快地向连接的设备发送物品或同时工作以制作多种所需材料,来加速合成任务.\\n\\n基础AE2协处理器提供一个协处理器'线程'来协助此过程,而来自&d&aMEGA存储单元&f&f的协处理单元则在一个方块中提供4个线程."]
			id: "69B7DE2283B4EE6C"
			rewards: [
				{
					id: "30447C55F39E6DA1"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "33A5EAC376E2828C"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "下载更多CPU核心"
			tasks: [{
				id: "5EC8D891031E34EF"
				item: {
					Count: 1
					id: "itemfilters:id_regex"
					tag: {
						value: "(.*)crafting_accelerator"
					}
				}
				title: "合成协处理器"
				type: "item"
			}]
			title: "合成协处理器"
			x: 16.0d
			y: -1.5d
		}
		{
			dependencies: ["69B7DE2283B4EE6C"]
			description: ["&b合成监视器&f 显示正在合成的整体物品,以及仍在合成中的剩余数量.\\n\\n基础AE2监视器和MEGA监视器功能相同,但出于美观目的提供不同版本."]
			id: "1348995F64A94396"
			rewards: [
				{
					id: "67F817808F544C51"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "77A6ACEA6F5542AF"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "下载更多显存"
			tasks: [{
				id: "2D386EFC23C45581"
				item: {
					Count: 1
					id: "itemfilters:id_regex"
					tag: {
						value: "(ae2|mega)(.*)crafting_monitor"
					}
				}
				title: "合成监控器"
				type: "item"
			}]
			title: "合成监视器"
			x: 18.0d
			y: -1.5d
		}
		{
			dependencies: ["5C22E3103544B120"]
			description: ["&b&a石英切割刀&f&f,与扳手类似,有下界石英和赛特斯石英两种版本,是手边必备的实用合成工具.\\n\\n线缆也可以通过在其间放置&b线缆锚点&f来分离,而无需染色.使用此刀可以制作线缆锚点,同一把刀最多可制作50次后损坏.\\n\\n线缆锚点还可用于制作&b线缆伪装板&f,让你通过用任意方块的表面覆盖线缆来将其隐藏在墙内.虽然伪装配方在JEI中隐藏,但你仍可以通过在合成网格中用4个线缆锚点围绕任意普通方块来制作它们.\\n\\n除了制作锚点,切割刀还有另一个用途:右键点击刀会打开一个小GUI,允许你制作&b&a名称压印模板&fes&f.当给定名称后,这些模板可以在压印机中使用,将任何输入物品重命名为模板的名称.两个这样的模板可以在压印机中连接,将物品重命名为顶部模板的名称,后跟底部模板的名称."]
			icon: "ae2:cable_anchor"
			id: "6144202A97C6CD1C"
			min_width: 300
			rewards: [
				{
					id: "4330FAC592266D62"
					table_id: 727499692191347770L
					type: "random"
				}
				{
					id: "0BAC4AB615750E48"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "刀下之物"
			tasks: [
				{
					id: "23D186249A999B8C"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "ae2:knife"
						}
					}
					title: "&a石英切割刀&f"
					type: "item"
				}
				{
					id: "2B8EC37AF863F9A6"
					item: "ae2:cable_anchor"
					type: "item"
				}
				{
					id: "1FFAB0FE9A2D46F2"
					item: {
						Count: 1
						id: "ae2:name_press"
						tag: { }
					}
					type: "item"
				}
			]
			title: "线缆锚点"
			x: 4.5d
			y: 2.5d
		}
		{
			dependencies: [
				"066F1BBF3D0863C5"
				"19CE95EDCB04AA89"
				"5C22E3103544B120"
			]
			description: ["&b&a染色器&f&f 是一种可充电工具,能够对世界中的线缆进行染色.它的功能类似于一个专用的 &e存储元件&f,可以装载原版染料或特定颜色的 &e染色球&f,同时还能用 &e雪球&f 来清除颜色,将线缆恢复为荧石变体.\\n\\n给线缆上色会消耗其内置电池的100AE能量,单次充电最多可为3400段线缆染色."]
			id: "03E6FA4DCB71162E"
			rewards: [
				{
					id: "72469A8B94967668"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "2AD4CA497DAF5DDE"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "6691176371563341"
				item: {
					Count: 1
					id: "ae2:color_applicator"
					tag: { }
				}
				type: "item"
			}]
			title: "&a染色器&f"
			x: 5.5d
			y: 2.5d
		}
		{
			dependencies: [
				"40A7CC56DACC2623"
				"2FB231069D2E4E77"
			]
			description: ["最后这三件物品更像是AE2提供的额外玩具,并不完全属于任何任务树分类.\\n\\n&b充能法杖&f 就是个简单的充能法杖,每次攻击造成3颗心伤害并消耗300AE能量.它仍采用1.9版本前的战斗机制,意味着你可以像过去那样疯狂连击.\\n\\n&b&a熵变机械臂&f&f 对世界中特定方块 &o使用&r 时,会就地熔炼它们,比如将沙子熔成玻璃、金属矿石熔成金属锭.否则就只是在方块上点火.\\n\\n最后,&b&a物质炮&f&f 功能类似 &a染色器&f,作为专门储存'弹药'的容器,可装载 &a物质球&f 和铁/金粒等弹药进行发射,对生物造成伤害或破坏方块.重型弹药 &a物质球&f 可用 &b&a染色球&f&f 替代,会在被击中方块的表面形成染色飞溅效果.&b流明 &a染色球&f&f 是 &a物质炮&f 专用的染色球变体,其染色飞溅效果还会在世界上发出微光."]
			hide_dependency_lines: true
			id: "5CD8D169181C7339"
			rewards: [
				{
					id: "653C5DBC5B2DFB83"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "79695D39F0DA9907"
					type: "xp"
					xp: 10
				}
			]
			shape: "square"
			subtitle: "&a温度过高&f 导致ATF故障"
			tasks: [
				{
					id: "3AC5F84892DC0717"
					item: "ae2:charged_staff"
					type: "item"
				}
				{
					id: "68BE70918BD7F81B"
					item: "ae2:entropy_manipulator"
					type: "item"
				}
				{
					id: "3B720F63D105F7DF"
					item: {
						Count: 1
						id: "ae2:matter_cannon"
						tag: { }
					}
					type: "item"
				}
			]
			title: "这些...武器？"
			x: 15.0d
			y: 0.5d
		}
		{
			dependencies: ["2F16B6A173525277"]
			description: ["实现无线网络接入的下一个装置是 &bME无线接入点&f.&a访问&f 点用于通过 &e无线终端&f 开放网络无线接入,其有效范围取决于接入点内插入的 &b无线增幅器&f 数量(范围有限)."]
			hide_dependency_lines: true
			id: "2B31E6C1707D8195"
			rewards: [
				{
					id: "72CFEEE18B84AC11"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "7C6512C24B5A19A3"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "3DF8F1A72EBCD73F"
					item: "ae2:wireless_access_point"
					type: "item"
				}
				{
					id: "60F10069FD956D54"
					item: "ae2:wireless_booster"
					type: "item"
				}
			]
			title: "ME无线接入点"
			x: 9.0d
			y: 4.5d
		}
		{
			dependencies: ["2F16B6A173525277"]
			description: ["关于无线扩展ME网络本身,第一步确实有些非传统.\\n\\n&b&a物质聚合器&f&f 是AE2的垃圾桶,会销毁放入其中的任何物品.但当安装 &e存储组件&f 时,聚合器能利用被销毁物品的残余能量,储存足够浓缩能量后可制作两种特殊合成物品.\\n\\n第一种是 &b&a物质球&f&f,需要至少1k存储组件和256个物品的销毁材料才能合成."]
			hide_dependency_lines: true
			id: "5BB887411B8B38FA"
			rewards: [
				{
					id: "3AF0C07815901F6E"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "0F9CCE69ADAF459C"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "184FBC2F91C37271"
					item: "ae2:condenser"
					type: "item"
				}
				{
					id: "167B710CB84B12C1"
					item: "ae2:matter_ball"
					type: "item"
				}
			]
			title: "凝聚物质"
			x: 7.0d
			y: 4.5d
		}
		{
			dependencies: ["2B31E6C1707D8195"]
			description: ["&b无线终端&f 功能与普通终端完全相同,但采用无线连接.\\n\\n使用前需先通过放入 &e无线接入点&r 右上角插槽进行 &e链接&f 网络.若未链接网络、超出范围或电力不足,终端将无法工作.\\n\\n无线终端还可安装 &e&a能量卡&f&f 升级来扩大内置电池容量."]
			id: "16299B9AE87257DC"
			rewards: [
				{
					id: "7C69F2A048685089"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "3A1C068ABC13E7EE"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "这正是你需要的"
			tasks: [
				{
					id: "195B7DC4D7815D29"
					item: "ae2:wireless_terminal"
					type: "item"
				}
				{
					id: "384594EA34F0985B"
					item: "ae2:wireless_crafting_terminal"
					type: "item"
				}
			]
			title: "无线终端"
			x: 11.0d
			y: 6.0d
		}
		{
			dependencies: ["16299B9AE87257DC"]
			description: ["&dAE无限增幅器&f附加组件提供两种定制无线增幅器,可实现无限连接范围,并可选跨维度支持."]
			id: "234DC1702333EB18"
			rewards: [
				{
					id: "166910739B51C0F9"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "6724FE14F1AF7CB1"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "我可没在写玩具总动员的台词"
			tasks: [
				{
					id: "745C92ED1392EE1A"
					item: "aeinfinitybooster:infinity_card"
					type: "item"
				}
				{
					id: "4EDA481E8668C82B"
					item: "aeinfinitybooster:dimension_card"
					type: "item"
				}
			]
			title: "AE无限增幅器"
			x: 14.0d
			y: 6.0d
		}
		{
			dependencies: ["5BB887411B8B38FA"]
			description: ["当使用64k或更高容量的存储元件时,&a物质聚合器&f能够将物质高度压缩生成&b奇点&f.每个奇点需要消耗256000个物品——足足相当于&o4000组物品&r!\\n\\n试试将奇点与末影尘&f一起扔到地上会发生什么.虽然会对周围环境造成破坏,但幸运的是AE2自带的&b微型TNT&f可以最小化破坏范围."]
			icon: "ae2:singularity"
			id: "3E3DF8E967D95DB0"
			rewards: [
				{
					id: "545E8CF0C43F9B5B"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "6C2DAC14419B90E4"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			subtitle: "还不算反物质"
			tasks: [
				{
					id: "3458474D44BD443F"
					item: "ae2:singularity"
					type: "item"
				}
				{
					id: "523A126499263FE0"
					item: "ae2:ender_dust"
					type: "item"
				}
				{
					id: "3A45E1C2AE35002B"
					item: "ae2:tiny_tnt"
					type: "item"
				}
			]
			title: "超凝聚态物质"
			x: 5.0d
			y: 4.5d
		}
		{
			dependencies: ["371A382CF1DDF2B2"]
			description: ["&b&a模糊卡&f&f能让过滤物品时忽略所有NBT元数据(如耐久度或附魔),而&b&a反相卡&f&f则可将总线上的过滤器从白名单模式切换为黑名单模式."]
			id: "6E15447FC3D678E0"
			rewards: [
				{
					id: "6FF117C99E79959D"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "27958B72A4A4CD9E"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			subtitle: "剩余两种IO卡"
			tasks: [
				{
					id: "79E894E41241B6A2"
					item: "ae2:fuzzy_card"
					type: "item"
				}
				{
					id: "6AAE42DF0347D1F4"
					item: "ae2:inverter_card"
					type: "item"
				}
			]
			title: "模糊化处理"
			x: 26.0d
			y: -2.5d
		}
		{
			dependencies: ["3E3DF8E967D95DB0"]
			description: ["若正确完成前置任务,你应已将奇点转化为一对&b量子纠缠奇点&f.这些新型奇点用于连接环形装置&e量子网络桥接器&f.\\n\\n当两个桥接器通过量子纠缠奇点建立连接并用AE能源(如能量单元)激活后,ME网络即可实现跨长距离甚至跨维度的无线扩展."]
			id: "0B218DD73FE8D985"
			rewards: [
				{
					id: "202B598DA89EB6EA"
					table_id: 5871764666515020368L
					type: "random"
				}
				{
					id: "25C4692C8D47D950"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			tasks: [
				{
					count: 16L
					id: "7D869E1310E92BED"
					item: "ae2:quantum_ring"
					type: "item"
				}
				{
					count: 2L
					id: "20AA67ECEA653B99"
					item: "ae2:quantum_link"
					type: "item"
				}
				{
					count: 2L
					id: "6590872EF147B7DF"
					item: "ae2:quantum_entangled_singularity"
					type: "item"
				}
			]
			title: "量子网络桥接器"
			x: 3.0d
			y: 3.5d
		}
		{
			dependencies: ["5C22E3103544B120"]
			description: ["在&d应用能源2&f中,每个ME网络都有特定数量的&e频道&f,这决定了网络可容纳的设备总量.\\n\\n经验而言,任何直接处理ME数据(存取网络物品)或进行输入输出的设备都会占用频道.而仅涉及网络内部能源存储的组件(如&e&a能量单元&f&f和&e压印机&f)则&o不&r占用频道.\\n\\n未安装&b&aME控制器&f&f的网络称为'临时网络',最多仅支持8个频道."]
			id: "78311531069807DE"
			rewards: [{
				id: "0608B89F87FECB34"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			tasks: [{
				id: "7E0D9E6342295AB0"
				type: "checkmark"
			}]
			title: "频道系统前言"
			x: 7.0d
			y: 1.5d
		}
		{
			dependencies: ["361CCBD353D6FF34"]
			description: ["除存储磁盘外,你还可以用下界合金制作与磁盘功能完全相反的存储设备.\\n\\n&d&aMEGA存储单元&f&f提供专属的&b大容量&a存储单元&f&f,每个单元仅能存储&o一种&r物品类型,但容量近乎&o&l无限&r*.需预先通过&a元件工作台&f设置过滤条件才能存入指定物品.\\n\\n*&o技术上为'长整型最大值',懂的都懂"]
			id: "0F03E75CF79BADD7"
			subtitle: "批量存储方案"
			tasks: [{
				id: "4C2F435902156183"
				item: {
					Count: 1
					id: "megacells:bulk_item_cell"
					tag: { }
				}
				type: "item"
			}]
			title: "大容量物品存储"
			x: 17.5d
			y: 0.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方团队&r或&2社区贡献者&r为AllTheMods整合包制作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若你看到本提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "0F0FAD595155DF17"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "613F2A6A0026C685"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "26D2701D55A6733B"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: -1.0d
		}
	]
	title: "&d应用能源2&f"
}
