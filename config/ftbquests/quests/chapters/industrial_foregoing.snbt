{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "industrial_foregoing"
	group: "2B51AC12041E3F89"
	icon: "industrialforegoing:common_black_hole_unit"
	id: "193F91842D2ED7D9"
	images: [
		{
			height: 0.3d
			hover: ["atm9.quest.ae2.img.star"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 12.506802721088391d
			y: 7.437925170068027d
		}
		{
			height: 0.3d
			hover: ["atm9.quest.ae2.img.star"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 11.5d
			y: -3.5d
		}
		{
			height: 1.0d
			hover: [
				"atm9.quest.industrialForegoing.hover.mycelialReactorFeatures.1"
				"atm9.quest.industrialForegoing.hover.mycelialReactorFeatures.2"
			]
			image: "industrialforegoing:block/reactor_front_mycelial"
			rotation: 0.0d
			width: 1.0d
			x: 8.0d
			y: 17.0d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.crimedMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_crimed"
			rotation: 0.0d
			width: 1.0d
			x: 7.5d
			y: 14.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.culinaryMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_culinary"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 14.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.deathMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_death"
			rotation: 0.0d
			width: 1.0d
			x: 9.5d
			y: 14.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.disenchantmentMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_disenchantment"
			rotation: 0.0d
			width: 1.0d
			x: 6.5d
			y: 14.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.enderMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_ender"
			rotation: 0.0d
			width: 1.0d
			x: 10.5d
			y: 15.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.explosiveMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_explosive"
			rotation: 0.0d
			width: 1.0d
			x: 10.5d
			y: 16.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.frostyMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_frosty"
			rotation: 0.0d
			width: 1.0d
			x: 10.5d
			y: 17.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.furnaceMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_furnace"
			rotation: 0.0d
			width: 1.0d
			x: 10.5d
			y: 18.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.halitosisMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_halitosis"
			rotation: 0.0d
			width: 1.0d
			x: 9.5d
			y: 19.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.magmaMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_magmatic"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 19.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.meatallurgicMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_meatallurgic"
			rotation: 0.0d
			width: 1.0d
			x: 7.5d
			y: 19.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.netherstarMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_netherstar"
			rotation: 0.0d
			width: 1.0d
			x: 6.5d
			y: 19.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.pinkMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_pink"
			rotation: 0.0d
			width: 1.0d
			x: 5.5d
			y: 18.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.potionMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_potion"
			rotation: 0.0d
			width: 1.0d
			x: 5.5d
			y: 17.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.rocketMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_rocket"
			rotation: 0.0d
			width: 1.0d
			x: 5.5d
			y: 16.5d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.slimeyMycelialGenerator"]
			image: "industrialforegoing:block/generators/generator_front_slimey"
			rotation: 0.0d
			width: 1.0d
			x: 5.5d
			y: 15.5d
		}
		{
			click: "#0EC2053B191C55C6"
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.fluidExtractor"]
			image: "industrialforegoing:block/tree_fluid_extractor_back"
			rotation: 0.0d
			width: 1.0d
			x: 1.3346938775509756d
			y: 2.910034013605461d
		}
		{
			click: "#294C729B9EBD7A3C"
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.blockPlacer"]
			image: "industrialforegoing:block/block_placer_back"
			rotation: 0.0d
			width: 1.0d
			x: 0.4367346938775043d
			y: 2.01207482993199d
		}
		{
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.acaciaLogs"]
			image: "chipped:block/acacia_log/firewood_acacia_log"
			rotation: 0.0d
			width: 1.0d
			x: 0.4399659863945189d
			y: 2.909523809523833d
		}
		{
			click: "#0EC2053B191C55C6"
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.fluidExtractor"]
			image: "industrialforegoing:block/tree_fluid_extractor_back"
			rotation: 0.0d
			width: 1.0d
			x: 0.4367346938775043d
			y: 3.828401360544234d
		}
		{
			click: "#0EC2053B191C55C6"
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.fluidExtractor"]
			image: "industrialforegoing:block/tree_fluid_extractor_back"
			rotation: 0.0d
			width: 1.0d
			x: -0.461224489795967d
			y: 2.910034013605461d
		}
		{
			click: "#6FF04DD735346BED"
			height: 1.0d
			hover: ["atm9.quest.industrialForegoing.latexProcessingUnit"]
			image: "industrialforegoing:block/latex_processing_unit_front"
			rotation: 0.0d
			width: 1.0d
			x: 3.1301020408163396d
			y: 2.9013605442176953d
		}
		{
			height: 1.0d
			image: "jei:textures/jei/atlas/gui/icons/arrow_next.png"
			rotation: 0.0d
			width: 1.0d
			x: 2.130612244897911d
			y: 2.8896258503401455d
		}
		{
			height: 7.0d
			image: "atm:textures/questpics/industrialforegoing/mycelial_reactor.png"
			rotation: 0.0d
			width: 12.0d
			x: 18.0d
			y: 17.0d
		}
		{
			height: 7.0d
			hover: [
				"atm9.quest.industrialForegoing.hover.witherInStasisFeatures.1"
				"atm9.quest.industrialForegoing.hover.witherInStasisFeatures.2"
			]
			image: "atm:textures/questpics/industrialforegoing/ether_gas_setup.png"
			order: 10
			rotation: 0.0d
			width: 8.0d
			x: 21.0d
			y: 2.0d
		}
		{
			height: 1.0d
			image: "minecraft:block/furnace_front_on"
			rotation: 0.0d
			width: 1.0d
			x: 4.967346938775449d
			y: 2.8896258503401455d
		}
		{
			height: 1.0d
			image: "jei:textures/jei/atlas/gui/icons/arrow_next.png"
			rotation: 0.0d
			width: 1.0d
			x: 4.028571428571368d
			y: 2.8896258503401455d
		}
	]
	order_index: 6
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["欢迎来到&a&d工业先锋&f&f!"]
			icon: {
				Count: 1
				id: "patchouli:guide_book"
				tag: {
					"patchouli:book": "industrialforegoing:industrial_foregoing"
				}
			}
			id: "55820773BDD5319D"
			rewards: [{
				id: "0201A0D475099871"
				type: "xp"
				xp: 10
			}]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "1997E42FA9EA414C"
				type: "checkmark"
			}]
			title: "&d工业先锋&f"
			x: 1.007653061224488d
			y: -3.0d
		}
		{
			dependencies: ["6E616DB197387C86"]
			description: [
				"从原木中提取乳胶."
				""
				"请查看JEI了解可接受的原木类型及乳胶产量.金合欢木是最佳选择."
			]
			id: "0EC2053B191C55C6"
			rewards: [
				{
					id: "2D3F9D6C16FE7EAF"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "62522B4A46251119"
					table_id: 5124217649411489500L
					type: "loot"
				}
			]
			tasks: [{
				id: "3CEAE2B50AD7C5ED"
				item: "industrialforegoing:fluid_extractor"
				type: "item"
			}]
			x: 0.9872448979591795d
			y: 0.0034013605442169137d
		}
		{
			dependencies: ["55820773BDD5319D"]
			id: "6E616DB197387C86"
			rewards: [{
				id: "31C7A99204AFBBF7"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "rsquare"
			tasks: [{
				id: "62D9743FABC21813"
				item: "industrialforegoing:machine_frame_pity"
				type: "item"
			}]
			x: 1.0d
			y: -1.8299319727891188d
		}
		{
			dependencies: ["6E616DB197387C86"]
			id: "33532408B21A5378"
			optional: true
			rewards: [{
				count: 5
				id: "3F63CCEB0E0789AF"
				item: "minecraft:coal"
				type: "item"
			}]
			tasks: [{
				id: "3DD12A969161A5B6"
				item: "industrialforegoing:pitiful_generator"
				type: "item"
			}]
			x: 2.0d
			y: -1.0d
		}
		{
			dependencies: ["321FA7348E532F4E"]
			id: "6FF04DD735346BED"
			rewards: [
				{
					id: "0FB5D498F42B6185"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "165FAC8C2F75A7F9"
					table_id: 5124217649411489500L
					type: "loot"
				}
			]
			tasks: [{
				id: "163B2434699EEF1A"
				item: "industrialforegoing:latex_processing_unit"
				type: "item"
			}]
			x: 3.5d
			y: 0.0d
		}
		{
			dependencies: ["6E616DB197387C86"]
			id: "339DF320DDCAD98B"
			rewards: [
				{
					count: 5
					id: "4BEE831683A26355"
					item: "industrialforegoing:item_transporter_type"
					type: "item"
				}
				{
					count: 5
					id: "754E8997E9493E96"
					item: "industrialforegoing:fluid_transporter_type"
					type: "item"
				}
			]
			tasks: [
				{
					id: "5AE0559068C0050A"
					item: "industrialforegoing:item_transporter_type"
					type: "item"
				}
				{
					id: "7D17F00002916197"
					item: "industrialforegoing:fluid_transporter_type"
					type: "item"
				}
			]
			title: "物品 \\ &a流体传输&f"
			x: 0.0d
			y: -1.0d
		}
		{
			dependencies: ["0EC2053B191C55C6"]
			id: "321FA7348E532F4E"
			rewards: [{
				count: 2
				id: "6D991DABAE36FE01"
				item: {
					Count: 1
					id: "mekanism:basic_fluid_tank"
					tag: {
						BlockEntityTag: {
							FluidTanks: [{
								Tank: 0b
								stored: {
									Amount: 14000
									FluidName: "industrialforegoing:latex"
								}
							}]
							ForgeCaps: { }
							Items: [ ]
							activeState: 0b
							currentRedstone: 15
							editMode: 0
							id: "mekanism:basic_fluid_tank"
							redstone: 0b
							updateDelay: 0
						}
						display: {
							Lore: ["\"(+NBT)\""]
						}
						mekData: {
							FluidTanks: [{
								Tank: 0b
								stored: {
									Amount: 14000
									FluidName: "industrialforegoing:latex"
								}
							}]
							Items: [ ]
							securityMode: 0
						}
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "1D19038D0227D7EC"
				item: "industrialforegoing:latex_bucket"
				type: "item"
			}]
			x: 2.155102040816274d
			y: 0.007993197278921116d
		}
		{
			dependencies: ["6FF04DD735346BED"]
			description: ["熔炼后可获得塑料,这是&d工业先锋&f模组的主要资源"]
			id: "0EA9E52B67B533DF"
			rewards: [{
				id: "2152C5EA52C0D06F"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "578EB2E46656AC16"
				item: "industrialforegoing:dryrubber"
				type: "item"
			}]
			x: 5.0d
			y: 0.0d
		}
		{
			dependencies: ["0EA9E52B67B533DF"]
			id: "690CFF61CE787D43"
			rewards: [
				{
					count: 5
					id: "41FDECEBC7DDB3F6"
					item: "industrialforegoing:plastic"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "10813338F04C0398"
					table_id: 5124217649411489500L
					type: "loot"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "77579FFDFEA734EB"
				item: "industrialforegoing:plastic"
				type: "item"
			}]
			x: 7.5d
			y: 0.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "163BEB03C415E187"
			rewards: [{
				id: "06BB8635159DB685"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "53836C0727B39DCF"
					item: "industrialforegoing:common_black_hole_unit"
					type: "item"
				}
				{
					id: "15A028284A4863C1"
					item: "industrialforegoing:common_black_hole_tank"
					type: "item"
				}
			]
			title: "普通型 &a黑洞存储&f"
			x: 9.5d
			y: -1.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "57C4A0BAE739E903"
			rewards: [{
				exclude_from_claim_all: true
				id: "5B87BEEF76D40DA7"
				table_id: 5124217649411489500L
				type: "loot"
			}]
			tasks: [{
				id: "47C0E765FD874FCC"
				item: "industrialforegoing:dissolution_chamber"
				type: "item"
			}]
			x: 7.5d
			y: 2.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			description: [
				"&a被动型生物&f → 产出更多&a粉红史莱姆&f"
				"&a敌对型生物&f → 产出更多肉类"
			]
			id: "1823CC81D613892B"
			rewards: [
				{
					id: "222D5521FF5BCB8C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "1DDA30E9635266E1"
					table_id: 5124217649411489500L
					type: "loot"
				}
			]
			tasks: [{
				id: "4CEA3179A4BE3336"
				item: "industrialforegoing:mob_slaughter_factory"
				type: "item"
			}]
			x: 9.0d
			y: 0.0d
		}
		{
			dependencies: ["1823CC81D613892B"]
			id: "0E8647B8EB4AAC41"
			rewards: [
				{
					id: "6AD72BC1A1F77913"
					item: {
						Count: 1
						id: "mekanism:basic_fluid_tank"
						tag: {
							mekData: {
								FluidTanks: [{
									Tank: 0b
									stored: {
										Amount: 14000
										FluidName: "industrialforegoing:pink_slime"
									}
								}]
								Items: [ ]
								securityMode: 0
							}
						}
					}
					type: "item"
				}
				{
					id: "0D8B60F8BD60B5CB"
					item: {
						Count: 1
						id: "mekanism:basic_fluid_tank"
						tag: {
							mekData: {
								FluidTanks: [{
									Tank: 0b
									stored: {
										Amount: 14000
										FluidName: "industrialforegoing:meat"
									}
								}]
								Items: [ ]
								securityMode: 0
							}
						}
					}
					type: "item"
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "3F52AEF4BF3FE722"
					item: "industrialforegoing:pink_slime_bucket"
					type: "item"
				}
				{
					id: "6F7BCA742E93DB2A"
					item: "industrialforegoing:meat_bucket"
					type: "item"
				}
			]
			title: "&a粉红史莱姆&f \\ &a肉汤&f"
			x: 11.5d
			y: 0.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "616CFD4078D67B51"
			rewards: [{
				count: 8
				id: "6C5045E055FD5551"
				item: "industrialforegoing:conveyor"
				type: "item"
			}]
			tasks: [{
				id: "3AE63F706CF41E9B"
				item: "industrialforegoing:conveyor"
				type: "item"
			}]
			x: 6.5d
			y: 1.0d
		}
		{
			dependencies: ["616CFD4078D67B51"]
			id: "3027584AA6138E6D"
			rewards: [{
				count: 8
				id: "7D6160CAB260B39C"
				item: "industrialforegoing:conveyor"
				type: "item"
			}]
			tasks: [
				{
					id: "706E6BE855C4AE5C"
					item: "industrialforegoing:conveyor_insertion_upgrade"
					type: "item"
				}
				{
					id: "1D7CB07E1E6F7A29"
					item: "industrialforegoing:conveyor_extraction_upgrade"
					type: "item"
				}
			]
			title: "传送带插入 \\ 提取装置"
			x: 5.5d
			y: 1.5d
		}
		{
			dependencies: ["616CFD4078D67B51"]
			id: "06094615950AC062"
			optional: true
			rewards: [{
				count: 8
				id: "66A3E488F1A371C4"
				item: "industrialforegoing:conveyor"
				type: "item"
			}]
			tasks: [
				{
					id: "1F78346AC1AA58B4"
					item: "industrialforegoing:conveyor_detection_upgrade"
					type: "item"
				}
				{
					id: "3DB99B677678BCDD"
					item: "industrialforegoing:conveyor_bouncing_upgrade"
					type: "item"
				}
				{
					id: "1A64390CFB75F256"
					item: "industrialforegoing:conveyor_dropping_upgrade"
					type: "item"
				}
				{
					id: "387D0194F77E1870"
					item: "industrialforegoing:conveyor_blinking_upgrade"
					type: "item"
				}
				{
					id: "4C5292076C0A9E83"
					item: "industrialforegoing:conveyor_splitting_upgrade"
					type: "item"
				}
			]
			title: "其他传送带升级组件"
			x: 6.5d
			y: 2.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "427C3AFC0FF131CD"
			optional: true
			rewards: [{
				id: "2753E05691F3DCC7"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "31CC6D6C9BD14E1E"
					item: "industrialforegoing:fluid_collector"
					type: "item"
				}
				{
					id: "62B10DA5B0939647"
					item: "industrialforegoing:fluid_placer"
					type: "item"
				}
			]
			title: "流体系统"
			x: 9.0d
			y: -2.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			description: ["用于自动化放置/破坏方块,在乳胶自动化生产中尤为实用"]
			id: "2CCFEE98FE3B2E97"
			optional: true
			rewards: [{
				id: "43BDBF6B274E95D2"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "796061CC713A9A91"
					item: "industrialforegoing:block_breaker"
					type: "item"
				}
				{
					id: "294C729B9EBD7A3C"
					item: "industrialforegoing:block_placer"
					type: "item"
				}
			]
			title: "方块设备"
			x: 8.5d
			y: -3.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "485AFAE5BBEF2FC7"
			optional: true
			rewards: [{
				id: "19210F4E78C3B032"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "6C73F85B66281095"
					item: "industrialforegoing:animal_feeder"
					type: "item"
				}
				{
					id: "30C6C62788FBA50C"
					item: "industrialforegoing:animal_rancher"
					type: "item"
				}
				{
					id: "7C6B876555C4EA90"
					item: "industrialforegoing:animal_baby_separator"
					type: "item"
				}
			]
			title: "动物养殖"
			x: 6.0d
			y: -2.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "6C001E18093FC037"
			optional: true
			rewards: [{
				id: "20588A723CB45234"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "3B2B3F7BC100A618"
					item: "industrialforegoing:plant_gatherer"
					type: "item"
				}
				{
					id: "77DA9E89A314968B"
					item: "industrialforegoing:plant_sower"
					type: "item"
				}
			]
			title: "植物培育"
			x: 6.5d
			y: -3.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "540B857F043C24D5"
			optional: true
			rewards: [{
				id: "0F06B8DC62695CBF"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "6DBD9D66E78E0D58"
					item: "industrialforegoing:bioreactor"
					type: "item"
				}
				{
					id: "28A15B47904E918A"
					item: "industrialforegoing:biofuel_generator"
					type: "item"
				}
			]
			title: "生物能源"
			x: 5.5d
			y: -1.0d
		}
		{
			dependencies: ["690CFF61CE787D43"]
			id: "3E6706BC4C318A40"
			optional: true
			rewards: [{
				id: "5AABE475B6A7A7E1"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "376F8FB39341A76F"
					item: "industrialforegoing:sewage_composter"
					type: "item"
				}
				{
					id: "3A617FF0FF39C742"
					item: "industrialforegoing:spores_recreator"
					type: "item"
				}
			]
			title: "其他机械设备"
			x: 7.5d
			y: -3.5d
		}
		{
			dependencies: [
				"690CFF61CE787D43"
				"1823CC81D613892B"
			]
			description: ["通过管道输送的肉块,美味"]
			id: "4C366515E3CCB0B2"
			tasks: [{
				id: "381C116FAD6B2236"
				item: "industrialforegoing:meat_feeder"
				type: "item"
			}]
			x: 9.0d
			y: 1.5d
		}
		{
			dependencies: ["57C4A0BAE739E903"]
			id: "3514E9C1A8C7400C"
			rewards: [
				{
					id: "3CB88390179E68C1"
					type: "xp_levels"
					xp_levels: 1
				}
				{
					exclude_from_claim_all: true
					id: "6DD952E9DAB7B8A7"
					table_id: 5124217649411489500L
					type: "loot"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "719FBA82094C5EC2"
				item: "industrialforegoing:machine_frame_simple"
				type: "item"
			}]
			x: 7.5d
			y: 3.5d
		}
		{
			dependencies: [
				"3514E9C1A8C7400C"
				"0E8647B8EB4AAC41"
			]
			id: "0BCCDE24D378F260"
			rewards: [{
				exclude_from_claim_all: true
				id: "7A3A00513891003C"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			shape: "rsquare"
			tasks: [{
				id: "6C58377563CFA587"
				item: "industrialforegoing:machine_frame_advanced"
				type: "item"
			}]
			x: 11.5d
			y: 3.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "22702838FC507A2E"
			optional: true
			rewards: [{
				id: "3BA98A7B936CA54C"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "7BC5A77A97F5FEA3"
				item: "industrialforegoing:hydroponic_bed"
				type: "item"
			}]
			x: 6.0d
			y: 5.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "0D2DD9AA960843A3"
			optional: true
			rewards: [{
				id: "6EC4D088A0648FD0"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "782FFDDEED06401F"
				item: "industrialforegoing:plant_fertilizer"
				type: "item"
			}]
			x: 9.0d
			y: 6.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "0AD768E4CC10358C"
			optional: true
			rewards: [{
				id: "774E775539F872B9"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "183086ACEB713E82"
				item: "industrialforegoing:mycelial_furnace"
				type: "item"
			}]
			x: 7.5d
			y: 6.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "605A5AC65BC7E864"
			rewards: [{
				id: "739A3B6C7097D073"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "0B132D9A270D8A51"
				item: "industrialforegoing:marine_fisher"
				type: "item"
			}]
			x: 6.0d
			y: 4.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "377F505175DFB790"
			optional: true
			rewards: [{
				id: "20AD506FD5ACFE33"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "54E7A3F21AE2CA54"
				item: "industrialforegoing:mycelial_culinary"
				type: "item"
			}]
			x: 7.5d
			y: 7.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "45DA9A3DA47AF2F0"
			optional: true
			rewards: [{
				id: "2F665DF88E574553"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "3ECC5C50C2436DB2"
				item: "industrialforegoing:mycelial_frosty"
				type: "item"
			}]
			x: 7.5d
			y: 9.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "0B7E3FD8B8CB04A2"
			rewards: [{
				id: "2D71CD1E71EBE9EC"
				type: "xp"
				xp: 100
			}]
			tasks: [
				{
					id: "2A17E5658713BB56"
					item: "industrialforegoing:simple_black_hole_unit"
					type: "item"
				}
				{
					id: "7B9445CBF7FBAF14"
					item: "industrialforegoing:simple_black_hole_tank"
					type: "item"
				}
			]
			title: "简易型 &a黑洞存储&f"
			x: 9.0d
			y: 4.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "224C07AC71C5F40E"
			optional: true
			rewards: [{
				id: "6C877D1A3C5DA88E"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "353C9F6B5E56C76E"
				item: "industrialforegoing:fermentation_station"
				type: "item"
			}]
			x: 6.0d
			y: 6.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "29C9EBD333E59A35"
			optional: true
			rewards: [{
				id: "06E705400BFA0E5C"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "345746E883043F2A"
				item: "industrialforegoing:mycelial_pink"
				type: "item"
			}]
			x: 7.5d
			y: 8.5d
		}
		{
			dependencies: ["3514E9C1A8C7400C"]
			id: "1684D52FDAAC894B"
			optional: true
			rewards: [{
				id: "0DC27341E5CC2E46"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "720D4B73408CD1F0"
				item: "industrialforegoing:mob_detector"
				type: "item"
			}]
			x: 9.0d
			y: 5.5d
		}
		{
			dependencies: ["0E8647B8EB4AAC41"]
			id: "408203C29BAABA44"
			rewards: [{
				id: "2638272F9CC5DC85"
				item: "industrialforegoing:pink_slime_ingot"
				type: "item"
			}]
			tasks: [{
				id: "2B1369FBDB43CED5"
				item: "industrialforegoing:pink_slime_ingot"
				type: "item"
			}]
			x: 14.5d
			y: 0.0d
		}
		{
			dependencies: [
				"408203C29BAABA44"
				"0BCCDE24D378F260"
			]
			id: "0B35172E47705205"
			rewards: [{
				exclude_from_claim_all: true
				id: "0D6B326AA5539931"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "79D439E38B286B8E"
				item: "industrialforegoing:washing_factory"
				type: "item"
			}]
			x: 14.5d
			y: 2.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "418E57E34FFC19E1"
			rewards: [{
				exclude_from_claim_all: true
				id: "69FCF0A4D6B92D47"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [
				{
					id: "21285C89735F67FC"
					item: "industrialforegoing:ore_laser_base"
					type: "item"
				}
				{
					id: "7EB10ED3F8398FF4"
					item: "industrialforegoing:fluid_laser_base"
					type: "item"
				}
				{
					count: 4L
					id: "39B89950413E16A6"
					item: "industrialforegoing:laser_drill"
					type: "item"
				}
			]
			title: "&a镭射钻&f(&a虚空矿工&f)"
			x: 12.0d
			y: 5.0d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "2E8E292ED596A104"
			rewards: [{
				exclude_from_claim_all: true
				id: "594B225D020201DC"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "7B6F79D3A759D5D5"
				item: "industrialforegoing:laser_lens10"
				type: "item"
			}]
			x: 11.0d
			y: 5.0d
		}
		{
			dependencies: [
				"2E8E292ED596A104"
				"418E57E34FFC19E1"
			]
			description: [
				"建议:"
				"使用某种防凋灵玻璃"
			]
			id: "7E39FB9F3E973009"
			rewards: [{
				exclude_from_claim_all: true
				id: "0DFE99C957A9D7C7"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "1225664C1E9E28D3"
				item: "industrialforegoing:ether_gas_bucket"
				type: "item"
			}]
			x: 11.5d
			y: 6.5d
		}
		{
			dependencies: ["7B4AF35313D7D779"]
			id: "0F8FE6692717AA6A"
			rewards: [{
				exclude_from_claim_all: true
				id: "1ABB19B5E00E3E6D"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "2FD098DEE8336866"
				item: "industrialforegoing:mycelial_reactor"
				type: "item"
			}]
			x: 11.5d
			y: 11.0d
		}
		{
			dependencies: ["7E39FB9F3E973009"]
			id: "7B4AF35313D7D779"
			rewards: [{
				exclude_from_claim_all: true
				id: "3D2DF24F9574CC0B"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "0DC23FAFFD8954C2"
				item: "industrialforegoing:machine_frame_supreme"
				type: "item"
			}]
			x: 11.5d
			y: 8.0d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "1D5895CD14AB88EF"
			rewards: [{
				exclude_from_claim_all: true
				id: "6E1D6D1D640EEF09"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [
				{
					id: "0F4D170395901E5C"
					item: "industrialforegoing:advanced_black_hole_unit"
					type: "item"
				}
				{
					id: "1B5535E1F65BA9F2"
					item: "industrialforegoing:advanced_black_hole_tank"
					type: "item"
				}
			]
			x: 15.5d
			y: 2.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "65C5D30F48B77D20"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "149850904760F703"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "285EB289271FF17A"
				item: "industrialforegoing:mycelial_death"
				type: "item"
			}]
			x: 18.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "7DC044EFFDC208D7"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "1A72C528C5669C74"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "4AF8DF7BC38E419B"
				item: "industrialforegoing:mycelial_slimey"
				type: "item"
			}]
			x: 14.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "014262BDF1BBA54D"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "44298E7999EB7B94"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "56ACF4EA4F91E75C"
				item: "industrialforegoing:mycelial_rocket"
				type: "item"
			}]
			x: 22.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "5186CB4CD85B530C"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "227F6EF7AE909565"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "1E468D6A624E64E2"
				item: "industrialforegoing:mycelial_potion"
				type: "item"
			}]
			x: 15.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "287B47E27EBC2C18"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "20881D56F5192E84"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "4F813C90DC8DE4F0"
				item: "industrialforegoing:enchantment_factory"
				type: "item"
			}]
			x: 14.5d
			y: 4.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "776EE1F5C4565146"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "298134FBAFA1E326"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "6F96A800F8BDA3FD"
				item: "industrialforegoing:mycelial_crimed"
				type: "item"
			}]
			x: 21.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "3AFDE3396861A944"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "759BBDD0EC70369D"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "74E4102326A71945"
				item: "industrialforegoing:enchantment_applicator"
				type: "item"
			}]
			x: 15.5d
			y: 4.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "4A8C60412E59E971"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "211F476F3CB6AE8E"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "18A5C86DE82D9671"
				item: "industrialforegoing:enchantment_sorter"
				type: "item"
			}]
			x: 16.5d
			y: 4.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "2DC012EF21FB359E"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "0C6011376A9A8C7B"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "45B306381A723562"
				item: "industrialforegoing:enchantment_extractor"
				type: "item"
			}]
			x: 17.5d
			y: 4.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "272E27EFE40C913A"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "6351B04D5DB904AC"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "0D2B10E4838ACFA2"
				item: "industrialforegoing:mycelial_ender"
				type: "item"
			}]
			x: 20.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "7CB4D47ABC295B92"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "61BD7D63BC90F9D2"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "43C5F3AECB9F88F1"
				item: "industrialforegoing:mob_crusher"
				type: "item"
			}]
			x: 16.5d
			y: 3.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "15551AC6C68E12E0"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "28036A4A8587E827"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "532B0FD90F4D6AF6"
				item: "industrialforegoing:mob_duplicator"
				type: "item"
			}]
			x: 15.5d
			y: 3.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "393A7BA6768A3F56"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "6750356BF00D4D8C"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "185FE487EF5B3069"
				item: "industrialforegoing:mycelial_disenchantment"
				type: "item"
			}]
			x: 16.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "1BF511A13DF35C3A"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "35B1C843F3FA5B7A"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "2C993EEE2B11D440"
				item: "industrialforegoing:mycelial_magma"
				type: "item"
			}]
			x: 17.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "38FF05B6A26DB2EC"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "0C0798BE81F2F11E"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "04F5D3F7B29D441B"
				item: "industrialforegoing:mycelial_explosive"
				type: "item"
			}]
			x: 19.5d
			y: 6.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "06F84E2C484FAC5B"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "7919B502DD10AFCE"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "5949367631EAD828"
				item: "industrialforegoing:material_stonework_factory"
				type: "item"
			}]
			x: 16.5d
			y: 2.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "4F3EF1574F31A7E2"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "40F175309FD9450A"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "0D1F3F6898F4E776"
				item: "industrialforegoing:stasis_chamber"
				type: "item"
			}]
			x: 17.5d
			y: 3.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "2782EA80C1C74EBD"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "661D05D57C1FF7DF"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "3EFA144FCF33A935"
				item: "industrialforegoing:potion_brewer"
				type: "item"
			}]
			x: 14.5d
			y: 3.5d
		}
		{
			dependencies: ["0BCCDE24D378F260"]
			id: "34AA079FFAFC64BD"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "7B005F23D46A4F76"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "363855E6A408A2E5"
				item: "industrialforegoing:fluid_sieving_machine"
				type: "item"
			}]
			x: 17.5d
			y: 2.5d
		}
		{
			dependencies: ["0E8647B8EB4AAC41"]
			id: "65C147F5282E8FCD"
			tasks: [{
				id: "7055454F2F8936C7"
				item: "industrialforegoing:infinity_charger"
				type: "item"
			}]
			x: 11.5d
			y: -1.5d
		}
		{
			dependencies: ["65C147F5282E8FCD"]
			id: "41E8550FC36ABCA5"
			rewards: [{
				id: "1382AE5136BAB6C3"
				type: "xp_levels"
				xp_levels: 2
			}]
			tasks: [
				{
					id: "6D1A2543E374542A"
					item: {
						Count: 1
						id: "industrialforegoing:infinity_trident"
						tag: {
							CanCharge: 1b
							Channeling: 0b
							Energy: 0L
							Fluid: {
								Amount: 0
								FluidName: "biofuel"
							}
							Loyalty: 0
							Riptide: 0
							Selected: "POOR"
							Special: 0b
						}
					}
					type: "item"
				}
				{
					id: "798860AC37F98D4E"
					item: {
						Count: 1
						id: "industrialforegoing:infinity_drill"
						tag: {
							CanCharge: 1b
							Energy: 0L
							Fluid: {
								Amount: 0
								FluidName: "biofuel"
							}
							Selected: "POOR"
							Special: 0b
						}
					}
					type: "item"
				}
				{
					id: "75341FD6B58D8988"
					item: {
						Count: 1
						id: "industrialforegoing:infinity_saw"
						tag: {
							CanCharge: 1b
							Energy: 0L
							Fluid: {
								Amount: 0
								FluidName: "biofuel"
							}
							Selected: "POOR"
							Special: 0b
						}
					}
					type: "item"
				}
				{
					id: "291E22AEBC7FBCE2"
					item: {
						Count: 1
						id: "industrialforegoing:infinity_hammer"
						tag: {
							Beheading: 0
							CanCharge: 1b
							Energy: 0L
							Fluid: {
								Amount: 0
								FluidName: "biofuel"
							}
							Selected: "POOR"
							Special: 0b
						}
					}
					type: "item"
				}
				{
					id: "1A3351BC0935160A"
					item: {
						Count: 1
						id: "industrialforegoing:infinity_backpack"
						tag: {
							CanCharge: 1b
							Energy: 0L
							Selected: "POOR"
							Special: 0b
						}
					}
					type: "item"
				}
			]
			x: 11.5d
			y: -3.0d
		}
		{
			dependencies: ["7B4AF35313D7D779"]
			id: "60719C4317D39E5A"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "66568091CEBF968A"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "42FC1011D077857F"
				item: "industrialforegoing:mycelial_netherstar"
				type: "item"
			}]
			x: 12.5d
			y: 10.0d
		}
		{
			dependencies: ["7B4AF35313D7D779"]
			id: "3A97E99FEC78E9C2"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "36AEF3332136C672"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "26CFE6CE592741D6"
				item: "industrialforegoing:mycelial_halitosis"
				type: "item"
			}]
			x: 10.5d
			y: 10.0d
		}
		{
			dependencies: ["7B4AF35313D7D779"]
			id: "7342B2669D96C509"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "6B90E8101E9805A5"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "077D65D9C7397844"
				item: "industrialforegoing:mycelial_meatallurgic"
				type: "item"
			}]
			x: 11.5d
			y: 10.0d
		}
		{
			dependencies: ["7B4AF35313D7D779"]
			id: "28B3591BFC0FA08B"
			rewards: [{
				exclude_from_claim_all: true
				id: "50CB633BE2D20F53"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "12A0A25980EA485E"
				item: "industrialforegoing:wither_builder"
				type: "item"
			}]
			x: 12.5d
			y: 8.0d
		}
		{
			dependencies: ["7B4AF35313D7D779"]
			id: "30BEF473F5C25983"
			rewards: [{
				exclude_from_claim_all: true
				id: "0467AE9BA58557C7"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [
				{
					id: "01FE619C47E82C71"
					item: "industrialforegoing:supreme_black_hole_unit"
					type: "item"
				}
				{
					id: "63514396DB2EEA1C"
					item: "industrialforegoing:supreme_black_hole_tank"
					type: "item"
				}
			]
			x: 10.5d
			y: 8.5d
		}
		{
			dependencies: ["7B4AF35313D7D779"]
			id: "4F846973EFB95FA5"
			rewards: [{
				exclude_from_claim_all: true
				id: "3625368F9EFEB946"
				table_id: 8352280757313595670L
				type: "loot"
			}]
			tasks: [{
				id: "5A45E3CD864C4DA1"
				item: "industrialforegoing:black_hole_controller"
				type: "item"
			}]
			x: 10.5d
			y: 7.5d
		}
		{
			dependencies: [
				"776EE1F5C4565146"
				"377F505175DFB790"
				"65C5D30F48B77D20"
				"393A7BA6768A3F56"
				"272E27EFE40C913A"
				"38FF05B6A26DB2EC"
				"45DA9A3DA47AF2F0"
				"0AD768E4CC10358C"
				"3A97E99FEC78E9C2"
				"1BF511A13DF35C3A"
				"7342B2669D96C509"
				"60719C4317D39E5A"
				"29C9EBD333E59A35"
				"5186CB4CD85B530C"
				"014262BDF1BBA54D"
				"7DC044EFFDC208D7"
				"0F8FE6692717AA6A"
			]
			description: [
				"&b&a菌丝网络产能反应堆&f&r由所有菌丝发电机同步工作构成,需放置在反应堆方块附近,总产能达&a25MFE/t&r."
				""
				"虽然原理简单,但需要自动化支持才能运作——查看每种菌丝发电机的消耗需求并进行自动化处理(部分设备较复杂,特别是&o解附魔菌丝发电机&r)."
				""
				"完成自动化后,您还可以建造更多反应堆."
			]
			id: "0FAAE744E156D8EF"
			rewards: [{
				id: "4072266D7E2B23B7"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "1F1A69738DE70DB6"
				title: "&a菌丝网络产能反应堆&f, huh?"
				type: "checkmark"
			}]
			title: "&a菌丝网络产能反应堆&f？什么？"
			x: 11.5d
			y: 14.0d
		}
		{
			dependencies: [
				"4F3EF1574F31A7E2"
				"418E57E34FFC19E1"
				"2E8E292ED596A104"
			]
			dependency_requirement: "one_started"
			description: [
				"获取首份&b&a以太气体&f&r的过程将充满挑战."
				"需使用配备&5&a紫色透镜&f&r的&a流体钻&f开采&0凋灵&r来制备."
				""
				"但别担心,&d工业先锋&f模组专门提供了&4&a冷凝室&f&r——可冻结3x3区域内所有实体,安全地在此召唤凋灵."
				""
				"&c注意保持电力供应,否则...祝您好运对抗&0凋灵&r."
			]
			id: "3CB7884B7B32CF00"
			rewards: [{
				id: "5B9934DE176BAC74"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "335D80E83360A7DB"
				title: "&a以太气体&f？嗯？"
				type: "checkmark"
			}]
			x: 21.0d
			y: 5.0d
		}
		{
			description: [
				"欢迎来到&b&d工业先锋&f&r,本模组核心资源之一是乳胶,用于制造机器框架及升级部件."
				""
				"&oJEI是您的好帮手"
				""
				"乳胶提取很简单:通过&a&a流体开采&f&r从&e原木&r提取(金合欢木产量最高)."
				""
				"------------------------"
				""
				"塑料制作流程:乳胶经&a&a胶乳加工机&f&r变成&a干橡胶&f,再熔炼为塑料."
				""
				"&b简而言之:乳胶 → &a干橡胶&f → 塑料"
			]
			id: "1BA3D15FFE7DBE59"
			tasks: [{
				id: "391AB5CA0E5C35FD"
				title: "乳胶？嗯？"
				type: "checkmark"
			}]
			title: "乳胶与塑料？怎么回事？"
			x: 3.130612244897911d
			y: 1.603911564625868d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"根据&e保留所有权利&r的许可协议,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若您能看到此说明,说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "53CFC6261F0EDDA1"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "05072073E048223B"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "29F056B23D70B9F7"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: 1.0d
			y: -4.5d
		}
	]
	title: "&d工业先锋&f"
}
