{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "food_and_farming"
	group: "6614EE2378B8AFB9"
	icon: "minecraft:cake"
	id: "05E614FDA677D85E"
	images: [
		{
			height: 2.0d
			image: "minecraft:textures/item/cake.png"
			rotation: 0.0d
			width: 2.0d
			x: 0.5d
			y: -3.5d
		}
		{
			height: 1.0d
			image: "minecraft:textures/item/wheat.png"
			rotation: 0.0d
			width: 1.0d
			x: 1.5102040816326507d
			y: -3.0714285714285765d
		}
		{
			height: 1.0d
			image: "minecraft:textures/item/wheat.png"
			rotation: -90.0d
			width: 1.0d
			x: -0.5d
			y: -3.0d
		}
		{
			height: 1.0d
			image: "minecraft:textures/item/wheat_seeds.png"
			rotation: 0.0d
			width: 1.0d
			x: 0.48979591836734215d
			y: -2.5d
		}
		{
			height: 2.0d
			image: "minecraft:textures/block/birch_sapling.png"
			rotation: 0.0d
			width: 2.0d
			x: 5.0d
			y: 3.5d
		}
		{
			height: 2.0d
			image: "farmersdelight:textures/block/cutting_board.png"
			rotation: -90.0d
			width: 2.0d
			x: 0.5d
			y: 11.0d
		}
		{
			height: 1.0d
			image: "croptopia:textures/item/cantaloupe_seed.png"
			rotation: 0.0d
			width: 1.0d
			x: -4.0d
			y: -1.0d
		}
		{
			height: 1.0d
			image: "farmersdelight:textures/item/apple_cider.png"
			rotation: 0.0d
			width: 1.0d
			x: -4.0d
			y: 1.0d
		}
		{
			height: 1.0d
			image: "minecraft:textures/item/cooked_chicken.png"
			rotation: 0.0d
			width: 1.0d
			x: 3.0d
			y: 1.0d
		}
		{
			height: 1.0d
			image: "minecraft:textures/item/cooked_porkchop.png"
			rotation: 0.0d
			width: 1.0d
			x: 4.5d
			y: -1.0d
		}
		{
			height: 2.0d
			image: "aquaculture:textures/item/catfish.png"
			rotation: 0.0d
			width: 2.0d
			x: -4.0d
			y: 4.0d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/food_and_farming.png"
			rotation: 0.0d
			width: 12.0d
			x: 0.5d
			y: -5.5d
		}
	]
	order_index: 2
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["欢迎来到食物与农耕章节!\\n\\n在这里,你将找到各种种植和制作食物的方法!"]
			icon: "minecraft:emerald"
			id: "1827DEEA2DF1B144"
			rewards: [
				{
					id: "2EFEA345691F570D"
					item: "minecraft:wheat_seeds"
					type: "item"
				}
				{
					id: "5F69C1D8D617910C"
					type: "xp"
					xp: 10
				}
			]
			shape: "square"
			size: 1.5d
			subtitle: "入门指南"
			tasks: [{
				id: "29267580E91DFEDA"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:crops"
					}
				}
				title: "任意作物"
				type: "item"
			}]
			title: "食物与农耕"
			x: 0.5d
			y: 0.0d
		}
		{
			dependencies: ["43021923E220CF68"]
			description: ["让我们收集一些种子."]
			id: "72717D1135486D7F"
			rewards: [
				{
					id: "37CC4C9D02D00F80"
					item: "minecraft:wheat"
					type: "item"
				}
				{
					id: "3EF6CC70CA664541"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			subtitle: "手撸方块1号,草方块0号"
			tasks: [{
				count: 8L
				id: "73B6ED422F9292D1"
				item: "minecraft:wheat_seeds"
				type: "item"
			}]
			title: "撸草"
			x: -3.5d
			y: 0.0d
		}
		{
			dependencies: ["18EADBAFC932F864"]
			description: ["让我们收集一些羊毛吧!"]
			icon: "minecraft:white_wool"
			id: "3EA883C0BB7BD38F"
			rewards: [
				{
					id: "59024F65DCEB9DAA"
					item: "minecraft:white_wool"
					type: "item"
				}
				{
					id: "1609AC17930BA465"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "谁的羊毛是...彩虹色的？"
			tasks: [{
				count: 3L
				icon: "minecraft:white_wool"
				id: "7F08D4E77359BF4A"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "minecraft:wool"
					}
				}
				title: "任意#minecraft:羊毛"
				type: "item"
			}]
			title: "玛丽有只小羊羔"
			x: 5.5d
			y: 0.0d
		}
		{
			dependencies: ["2E9C035EEE7E5C34"]
			id: "18EADBAFC932F864"
			rewards: [{
				id: "68AA10BF3AA0408A"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "3A9869856B3E9D54"
				item: {
					Count: 1
					id: "minecraft:shears"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "剪羊毛时间到"
			x: 4.0d
			y: 0.0d
		}
		{
			dependencies: ["2E9C035EEE7E5C34"]
			description: ["我不想问你是怎么弄到这些的."]
			id: "635620A03E3505BF"
			rewards: [
				{
					id: "0B497B4A001CFFC0"
					item: "minecraft:leather"
					type: "item"
				}
				{
					id: "5A638FDC17694EA2"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				count: 3L
				id: "5824C93C84312142"
				item: "minecraft:leather"
				type: "item"
			}]
			title: "但是...牛不能被剪毛..."
			x: 4.5d
			y: -1.0d
		}
		{
			dependencies: ["3EA883C0BB7BD38F"]
			description: ["市场会提供村民商人,只要你有足够的绿宝石就能买到任何东西.\\n\\n剧透:通常每件物品只要1颗绿宝石.但他们真的什么都有."]
			id: "1F114EB0AAB86DB4"
			rewards: [
				{
					id: "2A90BE5DD251767D"
					item: "minecraft:emerald"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "4F333F2FC9C0A269"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "1900E35FED741F5D"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			size: 1.5d
			subtitle: "村民的生成方式不止一种"
			tasks: [{
				id: "1AFEBED0226CEBC1"
				item: "farmingforblockheads:market"
				type: "item"
			}]
			title: "购买农场物资"
			x: 7.5d
			y: 0.0d
		}
		{
			description: ["经典拴绳.用它把动物赶进你的农场区域.\\n\\n这和油漆里的铅不是同一种东西."]
			id: "2E9C035EEE7E5C34"
			rewards: [{
				id: "04DF879E17A497AD"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			subtitle: "来吧小狗狗"
			tasks: [{
				id: "63282E8604721F13"
				item: "minecraft:lead"
				type: "item"
			}]
			title: "我们要用传统方法来做这件事"
			x: 2.5d
			y: 0.0d
		}
		{
			dependencies: ["635620A03E3505BF"]
			description: ["找到牛并用桶&a右键点击&f它.\\n\\n说真的,为什么没有公牛？"]
			id: "73B8A70240E6070E"
			rewards: [
				{
					count: 3
					id: "6680B692C05EDB9F"
					item: "minecraft:cooked_beef"
					type: "item"
				}
				{
					id: "486994911F40FA8D"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "为什么游戏里没有公牛？"
			tasks: [{
				id: "19A9C679111A90BC"
				item: "minecraft:milk_bucket"
				type: "item"
			}]
			title: "找到牛.挤牛奶.获利."
			x: 6.0d
			y: -1.0d
		}
		{
			dependencies: ["2E9C035EEE7E5C34"]
			description: ["鸡会自然下蛋.虽然严格来说不算自然...\\n\\n我猜这些鸡只是在做...&o下蛋运动!!!&r"]
			id: "1D2EF12FD7FDD217"
			rewards: [
				{
					count: 2
					id: "75D8B76E11F272AC"
					item: "croptopia:scrambled_eggs"
					type: "item"
				}
				{
					id: "25D2B3D4AE631792"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "...还是说我要玩？"
			tasks: [{
				count: 3L
				id: "46974912B1E8E78B"
				item: "minecraft:egg"
				type: "item"
			}]
			title: "我不想玩鸡蛋梗"
			x: 4.0d
			y: 1.0d
		}
		{
			dependencies: ["1D2EF12FD7FDD217"]
			id: "1697CC05D08B388D"
			rewards: [
				{
					id: "6ABAD303E8397549"
					item: "croptopia:fried_chicken"
					type: "item"
				}
				{
					id: "3AD62BBF556F5C60"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "不然你以为&a炸鸡&f是怎么来的？"
			tasks: [
				{
					count: 10L
					id: "264B00606E5BE374"
					item: "minecraft:feather"
					type: "item"
				}
				{
					count: 3L
					id: "763031D0E9F0850C"
					item: "minecraft:chicken"
					type: "item"
				}
			]
			title: "&a鸡&f必须走"
			x: 6.0d
			y: 1.0d
		}
		{
			icon: {
				Count: 1
				id: "minecraft:wooden_hoe"
				tag: {
					Damage: 0
				}
			}
			id: "43021923E220CF68"
			rewards: [
				{
					count: 2
					id: "70556C33AE952F49"
					item: "minecraft:wheat_seeds"
					type: "item"
				}
				{
					id: "116FA96039FC2359"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.25d
			subtitle: "等你有了自动化机器就知道它的好"
			tasks: [{
				id: "4847C9E5C9116BB7"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "minecraft:wooden_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:stone_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:iron_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:golden_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_hoe"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_hoe"
							}
							{
								Count: 1b
								id: "ae2:certus_quartz_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "ae2:fluix_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "ae2:nether_quartz_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:manasteel_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:elementium_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:bronze_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:lapis_lazuli_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:osmium_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:refined_glowstone_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:refined_obsidian_hoe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:steel_hoe"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "基础锄头"
				type: "item"
			}]
			title: "&a花盆&f"
			x: -1.5749999999999997d
			y: 0.0d
		}
		{
			dependencies: ["72717D1135486D7F"]
			id: "0A1C2A7A8617D3E4"
			rewards: [
				{
					count: 2
					id: "6EE0FD0DCE6CEEDE"
					item: "minecraft:bread"
					type: "item"
				}
				{
					id: "6DB38B87B2BB0B70"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "速通玩家最爱"
			tasks: [{
				count: 3L
				id: "6890EB2819D88073"
				item: "minecraft:wheat"
				type: "item"
			}]
			title: "小麦"
			x: -5.0d
			y: 0.0d
		}
		{
			dependencies: ["0A1C2A7A8617D3E4"]
			id: "659A903F97F93BE2"
			rewards: [
				{
					count: 2
					id: "1C449D2589FD1836"
					item: "croptopia:toast"
					type: "item"
				}
				{
					id: "382B2B8DF207F44F"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "快速又简单"
			tasks: [{
				count: 3L
				id: "03C7F9CB0DDB2D9B"
				item: "minecraft:bread"
				type: "item"
			}]
			title: "生命&a面包&f"
			x: -6.5d
			y: 0.0d
		}
		{
			dependencies: ["72717D1135486D7F"]
			description: ["&2&a亚麻种子&f&r是游戏前期获取线材的好方法!"]
			id: "5A51E26D66D1273B"
			rewards: [{
				id: "6A184032CC7F6E17"
				type: "xp"
				xp: 10
			}]
			subtitle: "谁还需要蜘蛛啊？"
			tasks: [{
				id: "52030BFD91159DAC"
				item: "supplementaries:flax_seeds"
				type: "item"
			}]
			title: "&a亚麻种子&f"
			x: -5.5d
			y: -1.0d
		}
		{
			dependencies: ["72717D1135486D7F"]
			description: ["找些甘蔗吧!"]
			id: "361DFDB1E1352D6B"
			rewards: [
				{
					id: "193719E857969260"
					item: "minecraft:sugar"
					type: "item"
				}
				{
					id: "687D9576B06FEFA7"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "这东西能长到天上去!"
			tasks: [{
				count: 10L
				id: "6118A776B40507B7"
				item: "minecraft:sugar_cane"
				type: "item"
			}]
			title: "甜蜜蜜"
			x: -5.5d
			y: 1.0d
		}
		{
			description: ["来吧.把书放进熔炉里."]
			id: "45F83C2750F70F9B"
			rewards: [{
				id: "54B713BDF265A213"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			size: 1.5d
			subtitle: "熔炼书本.绝对不会着火."
			tasks: [{
				id: "61DBF366AFC8A579"
				item: "cookingforblockheads:recipe_book"
				type: "item"
			}]
			title: "用书造厨房"
			x: 0.5d
			y: 4.0d
		}
		{
			dependencies: ["45F83C2750F70F9B"]
			description: ["合成这本书的升级版.我保证,绝对物超所值.\\n\\n我还会还你一颗钻石."]
			id: "58D5BD3106BFD94A"
			rewards: [
				{
					id: "1DEED7674A6B9079"
					item: "minecraft:diamond"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "40EA2F27F5E8685D"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "5E15B37ECAEF8EAF"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "说真的这超划算"
			tasks: [{
				id: "20FD5552BE10D679"
				item: "cookingforblockheads:crafting_book"
				type: "item"
			}]
			title: "用钻石的力量烹饪"
			x: 0.5d
			y: 5.5d
		}
		{
			dependencies: ["58D5BD3106BFD94A"]
			description: ["这个方块是组合厨房的一部分.\\n\\n它会显示你背包或厨房储物区现有食材能制作的所有食谱.\\n\\n你终于能在Minecraft打造完美厨房了."]
			id: "28C9EDBF6607E180"
			rewards: [
				{
					count: 8
					id: "145863D45AAB585A"
					item: "minecraft:bread"
					type: "item"
				}
				{
					id: "1AD9A5DFAF726359"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			size: 1.5d
			subtitle: "贝蒂·怀特出生时&a切片面包&f还没发明"
			tasks: [{
				id: "552C067A28FB8869"
				item: "cookingforblockheads:cooking_table"
				type: "item"
			}]
			title: "切片面包问世以来最伟大的发明"
			x: 0.5d
			y: 7.0d
		}
		{
			dependencies: ["28C9EDBF6607E180"]
			description: ["功能和厨房柜台一样,只是悬在空中.\\n\\n而且你不能在上面放东西."]
			hide_dependency_lines: true
			id: "58495CFBF4F20CE9"
			rewards: [
				{
					id: "5F08882A75BECB22"
					item: "cookingforblockheads:cabinet"
					type: "item"
				}
				{
					id: "1C499560DF40E2FC"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "22943343AA0140AF"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "活动空间超大!"
			tasks: [{
				count: 2L
				id: "1DAD3C12FDEB5CD4"
				item: "cookingforblockheads:cabinet"
				type: "item"
			}]
			title: "更多存储空间"
			x: 0.5d
			y: 8.5d
		}
		{
			dependencies: ["28C9EDBF6607E180"]
			description: ["该方块用于存放烹饪物品.\\n\\n你还可以在上面放置其他厨房复合方块物品,比如烤面包机和&a罐中牛&f."]
			hide_dependency_lines: true
			id: "37CA6F9F0226F10E"
			rewards: [
				{
					id: "45C4177C120927FF"
					item: "cookingforblockheads:counter"
					type: "item"
				}
				{
					id: "0B13ECD292D7D073"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "17F23B9B65A62413"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "&o我们&r需要更多烹饪空间"
			tasks: [{
				count: 2L
				id: "766C32B477A216B8"
				item: "cookingforblockheads:counter"
				type: "item"
			}]
			title: "我需要更多烹饪空间"
			x: 2.0d
			y: 8.0d
		}
		{
			dependencies: ["28C9EDBF6607E180"]
			description: ["在复合厨房中存储物品.可以叠放使用!\\n\\n按住潜行键&a右键点击&f可以打开冰箱门,直接&a右键点击&f物品即可放入."]
			hide_dependency_lines: true
			id: "66815AB6FDACCAB7"
			rewards: [
				{
					id: "07D944D551258145"
					type: "xp"
					xp: 10
				}
				{
					count: 2
					id: "2264EDF3B1BC3075"
					item: "croptopia:vanilla_ice_cream"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "48E3792992035011"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "你会因为无聊而反复开关冰箱门"
			tasks: [{
				count: 2L
				id: "1571EF760DE41F63"
				item: "cookingforblockheads:fridge"
				type: "item"
			}]
			title: "&a冰箱&f"
			x: -0.5d
			y: 8.5d
		}
		{
			dependencies: ["28C9EDBF6607E180"]
			description: ["该方块可为你的复合厨房解锁熔炼配方!"]
			hide_dependency_lines: true
			id: "7CB3FCD789747EF5"
			rewards: [
				{
					count: 8
					id: "5AB2C941E0E0397E"
					item: "minecraft:coal"
					type: "item"
				}
				{
					id: "4A5960A4F10AD94F"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "00F96B54F8F81325"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "无需计时器"
			tasks: [{
				id: "37D8D79B6B216212"
				item: "cookingforblockheads:oven"
				type: "item"
			}]
			title: "亲爱的,厨房里有熔炉"
			x: 1.5d
			y: 8.5d
		}
		{
			dependencies: ["28C9EDBF6607E180"]
			description: ["该方块会随时间产出牛奶,并为复合厨房解锁牛奶配方.\\n\\n要将牛装入罐中,需将罐子放在坑里.引诱牛掉入罐子上方的坑中,然后在牛上方掉落铁砧.\\n\\n我不知道这玻璃是什么材质,但它比我与鸡的关系还要牢固."]
			hide_dependency_lines: true
			id: "47764EFC822E462A"
			rewards: [
				{
					id: "0D48FB30B8B254C7"
					item: "minecraft:cow_spawn_egg"
					type: "item"
				}
				{
					id: "2390EE9B8E964CCA"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "285E04C219E15BE2"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "非凡的挤奶能力...狭小的生存空间"
			tasks: [{
				id: "193EA9F7C05B6F38"
				item: "cookingforblockheads:cow_jar"
				type: "item"
			}]
			title: "&a罐中牛&f"
			x: 0.5d
			y: 9.5d
		}
		{
			dependencies: ["7CB3FCD789747EF5"]
			description: ["允许为炉灶连接能源."]
			id: "1515B32545F51266"
			rewards: [
				{
					count: 3
					id: "5EB6705A66BEB997"
					item: "minecraft:redstone"
					type: "item"
				}
				{
					id: "19945E72467070D2"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "3F871506DE4A9E43"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3915BEA724A2F7ED"
				item: "cookingforblockheads:heating_unit"
				type: "item"
			}]
			title: "加热升级"
			x: 1.0d
			y: 9.0d
		}
		{
			dependencies: ["66815AB6FDACCAB7"]
			description: ["提供冰和雪的配方!"]
			id: "13AFCD3B6F62B986"
			optional: true
			rewards: [
				{
					count: 8
					id: "03911B94997F0691"
					item: "minecraft:snowball"
					type: "item"
				}
				{
					id: "7F98CDC2EA641EF7"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "7D1016D39347DFD3"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "6BDB280D22CE0118"
				item: "cookingforblockheads:ice_unit"
				type: "item"
			}]
			title: "冰,冰,宝贝"
			x: 0.0d
			y: 9.0d
		}
		{
			dependencies: ["5A51E26D66D1273B"]
			description: ["亚麻可以变成线!"]
			id: "1CBEB3ABB4260CC2"
			rewards: [{
				id: "3AE014AE6CF77A06"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				count: 4L
				id: "35864460DFF78192"
				item: "minecraft:string"
				type: "item"
			}]
			title: "种子变线"
			x: -7.0d
			y: -1.0d
		}
		{
			description: ["不过他通常五点就关门了,所以谁在乎呢."]
			id: "1DCAA0310AA55F1C"
			rewards: [{
				id: "7BB398F5B3A25045"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			size: 1.25d
			subtitle: "威利会感到骄傲"
			tasks: [{
				id: "5CB4746CCB2E2363"
				item: {
					Count: 1
					id: "minecraft:fishing_rod"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "渔夫的开端"
			x: -1.57d
			y: 4.0d
		}
		{
			dependencies: ["1DCAA0310AA55F1C"]
			id: "16D0F1E3CEB60ABF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1AD805EF5C994DC3"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "33B26FC79F880C93"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "13E86D23DA98D8E9"
				item: {
					Count: 1
					id: "aquaculture:iron_fishing_rod"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&a铁质钓竿&f"
			x: -2.5d
			y: 5.0d
		}
		{
			dependencies: ["16D0F1E3CEB60ABF"]
			id: "0A64D0937A5F7513"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0F864C7AA00CC8A9"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "69E2C012A258EEA5"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "144B351F77F0B08F"
				item: {
					Count: 1
					id: "aquaculture:gold_fishing_rod"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&a黄金钓竿&f"
			x: -3.5d
			y: 6.0d
		}
		{
			dependencies: ["0A64D0937A5F7513"]
			id: "511562EA5811306B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7DF9A8B80872B680"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "3347B9BD90C472AC"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "3FECA1F49F588499"
				item: {
					Count: 1
					id: "aquaculture:diamond_fishing_rod"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&a钻石钓竿&f"
			x: -5.0d
			y: 6.0d
		}
		{
			dependencies: ["511562EA5811306B"]
			id: "5A1DCD6C7F712A78"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5A0D21935AEB18B1"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "4E5F90F2EF0F4A40"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "31B52EAD7F931F31"
				item: {
					Count: 1
					id: "aquaculture:neptunium_fishing_rod"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&a海王钓竿&f"
			x: -6.5d
			y: 6.0d
		}
		{
			dependencies: [
				"659A903F97F93BE2"
				"1D48298525EEADC9"
			]
			description: ["是时候烤蛋糕了,如果你准备好了牛奶和鸡蛋的话."]
			id: "0893EFCAC7031FEA"
			rewards: [
				{
					id: "2C168199050CC470"
					item: "minecraft:cake"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "688019DBD32BF755"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "579A8B2C029B3EFD"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "1AA73DEFAA50FDF6"
				item: "minecraft:cake"
				type: "item"
			}]
			title: "鱼与熊掌可以兼得"
			x: -8.25d
			y: 0.0d
		}
		{
			dependencies: ["361DFDB1E1352D6B"]
			description: ["你应该已经准备好了所有材料."]
			id: "1D48298525EEADC9"
			rewards: [
				{
					count: 3
					id: "7EAE8BCBABAA8B89"
					item: "minecraft:sugar_cane"
					type: "item"
				}
				{
					id: "04AB9DC101FCAB74"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "给我撒点糖"
			tasks: [{
				count: 3L
				id: "56CAB3D2E3421C7C"
				item: "minecraft:sugar"
				type: "item"
			}]
			title: "你太甜了"
			x: -7.0d
			y: 1.0d
		}
		{
			dependencies: ["28C9EDBF6607E180"]
			description: ["该方块为复合厨房提供水源.\\n\\n通过管道或线缆抽水,还能提供无限水源."]
			hide_dependency_lines: true
			id: "0EFF1AA37772156B"
			rewards: [
				{
					id: "2B2B365E281A6940"
					item: "minecraft:bucket"
					type: "item"
				}
				{
					id: "62FB77230F1D9F9B"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "139855B12BA13469"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "能用1个方块解决何必用3个"
			tasks: [{
				id: "2872A1297BE6C40D"
				item: "cookingforblockheads:sink"
				type: "item"
			}]
			title: "&a水槽&f = 无限水源"
			x: -1.0d
			y: 8.0d
		}
		{
			description: ["&9&a植物盆&f&r让你轻松种植资源!\\n\\n这些盆子能自动培育几乎所有放入的植物,还能升级实现自动化!"]
			id: "01B70A4F230ED036"
			rewards: [
				{
					count: 2
					id: "0AC970B512BC6821"
					item: "minecraft:terracotta"
					type: "item"
				}
				{
					id: "5E4853D67766A132"
					type: "xp"
					xp: 50
				}
			]
			shape: "diamond"
			size: 1.25d
			tasks: [{
				id: "1958DBFE401157E6"
				item: "botanypots:terracotta_botany_pot"
				type: "item"
			}]
			title: "&a植物盆&f"
			x: 2.5d
			y: 4.0d
		}
		{
			dependencies: ["01B70A4F230ED036"]
			description: ["漏斗盆栽会自动将产出输送到下方容器.这可以用来实现所有作物的自动化种植!"]
			id: "2715BECC5E6FFA64"
			rewards: [
				{
					id: "3CE68937F914479A"
					item: "minecraft:hopper"
					type: "item"
				}
				{
					id: "1AC7BCFCCC100F4C"
					type: "xp"
					xp: 100
				}
			]
			shape: "gear"
			tasks: [{
				id: "572F01124B2232CD"
				item: "botanypots:terracotta_hopper_botany_pot"
				type: "item"
			}]
			title: "自动化盆栽"
			x: 3.5d
			y: 5.0d
		}
		{
			can_repeat: false
			description: [
				"该任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"该任务默认隐藏,如果你能看到这个说明,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "3EA60EB491270B5F"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "3807253D85162D5C"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "2BAD52B6D293140F"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 0.5d
			y: -1.5d
		}
	]
	title: "食物与农业"
}
