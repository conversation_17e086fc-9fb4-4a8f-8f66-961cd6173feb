{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "storage"
	group: "1AC60211DE7427FC"
	icon: "minecraft:chest"
	id: "1DB294A8F8686321"
	images: [
		{
			height: 5.0d
			image: "minecraft:textures/item/chest_minecart.png"
			rotation: 0.0d
			width: 5.0d
			x: -2.0d
			y: -3.0d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/rftools/storagescanner.png"
			rotation: 0.0d
			width: 4.5d
			x: -10.5d
			y: 12.0d
		}
		{
			height: 6.0d
			image: "atm:textures/questpics/storage/functional1.png"
			rotation: 0.0d
			width: 8.25d
			x: -18.5d
			y: -3.5d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/storage/functional2.png"
			rotation: 0.0d
			width: 10.5d
			x: -19.5d
			y: 4.0d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/helper_arrow.png"
			rotation: -90.0d
			width: 1.4723926380368098d
			x: -18.519897959183652d
			y: -0.6061224489795762d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/helper_arrow.png"
			rotation: 130.0d
			width: 2.0d
			x: -19.105612244897934d
			y: 3.59625850340138d
		}
		{
			height: 2.75d
			image: "atm:textures/questpics/storage/sophisticated1.png"
			rotation: 0.0d
			width: 5.9d
			x: 5.5d
			y: -3.6785714285714235d
		}
		{
			height: 2.5d
			image: "atm:textures/questpics/storage/sophisticated2.png"
			rotation: 0.0d
			width: 5.0d
			x: 11.083333333333343d
			y: -3.7678571428571317d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/storage/sophisticated3.png"
			rotation: 0.0d
			width: 7.0d
			x: 17.392857142857153d
			y: -4.5d
		}
	]
	order_index: 0
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["5E4BC0F59C90433A"]
			description: ["*注意:要将已放置的原版箱子升级为铁质精密储物箱,需先使用'基础级升级'进行转换."]
			id: "58514FDE153FD971"
			rewards: [
				{
					id: "56FA5A7CE451E586"
					item: "sophisticatedstorage:basic_to_iron_tier_upgrade"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "330E21D23165B829"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "3E7FBDA7A51C52A1"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "双倍容量的独立箱/桶"
			tasks: [{
				id: "77A9435B6739348E"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "sophisticatedstorage:iron_barrel"
								tag: {
									woodType: "oak"
								}
							}
							{
								Count: 1b
								id: "sophisticatedstorage:iron_chest"
								tag: {
									uuid: [I;
										767031598
										1710574310
										-2004480456
										-744336492
									]
									woodType: "oak"
								}
							}
						]
					}
				}
				title: "&a铁箱子&f/木桶"
				type: "item"
			}]
			x: 7.5d
			y: 0.5d
		}
		{
			dependencies: ["58514FDE153FD971"]
			id: "3E33730DC2115D26"
			rewards: [
				{
					id: "7BF7AC6177891566"
					item: "sophisticatedstorage:iron_to_gold_tier_upgrade"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "6B0B6F06B83B6A93"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "38840553474C9DBA"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "更宽敞的活动空间"
			tasks: [{
				id: "0427B441A750316F"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "sophisticatedstorage:gold_barrel"
								tag: {
									woodType: "oak"
								}
							}
							{
								Count: 1b
								id: "sophisticatedstorage:gold_chest"
								tag: {
									uuid: [I;
										237611848
										332743395
										-1801773020
										1918607969
									]
									woodType: "oak"
								}
							}
						]
					}
				}
				title: "&e&a金箱子&f/木桶"
				type: "item"
			}]
			x: 9.5d
			y: 0.5d
		}
		{
			dependencies: ["3E33730DC2115D26"]
			id: "4C0BDD483CCB40C4"
			rewards: [
				{
					id: "7634B130CAFA493B"
					item: "sophisticatedstorage:gold_to_diamond_tier_upgrade"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "0415C90C635B77D4"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "4C75481A20B97453"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&o超大&r活动空间"
			tasks: [{
				id: "00ED02255105A973"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "sophisticatedstorage:diamond_barrel"
								tag: {
									woodType: "oak"
								}
							}
							{
								Count: 1b
								id: "sophisticatedstorage:diamond_chest"
								tag: {
									uuid: [I;
										-821494956
										368657997
										-1650250951
										-1832415688
									]
									woodType: "oak"
								}
							}
						]
					}
				}
				title: "&b&a钻石箱子&f/木桶"
				type: "item"
			}]
			x: 11.5d
			y: 0.5d
		}
		{
			dependencies: ["4C0BDD483CCB40C4"]
			description: ["提供更多存储槽位与升级空间."]
			id: "77F241BEE9902751"
			rewards: [
				{
					id: "2084D6F08E608125"
					item: "sophisticatedstorage:diamond_to_netherite_tier_upgrade"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "33035D9E5D284A72"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "300A579BC953ABF2"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "越大越好,对吧？"
			tasks: [{
				id: "34B6EB0B801E4743"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "sophisticatedstorage:netherite_barrel"
								tag: {
									woodType: "oak"
								}
							}
							{
								Count: 1b
								id: "sophisticatedstorage:netherite_chest"
								tag: {
									uuid: [I;
										-385612189
										221532043
										-1277979957
										103954851
									]
									woodType: "oak"
								}
							}
						]
					}
				}
				title: "&5&a下界合金箱子&f/Barrel"
				type: "item"
			}]
			x: 13.5d
			y: 0.5d
		}
		{
			dependencies: ["563CFA1EF74E52E9"]
			description: ["功能类似原版箱子或木桶,但配有存储升级槽位!"]
			id: "5E4BC0F59C90433A"
			rewards: [
				{
					id: "2BD2C0D43AE4AB58"
					item: "sophisticatedstorage:basic_tier_upgrade"
					type: "item"
				}
				{
					id: "3DCEE5F8503C9C50"
					type: "xp"
					xp: 25
				}
			]
			subtitle: "&a冷杉木&f 台阶"
			tasks: [{
				id: "5241AA0EF3C2EA94"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "sophisticatedstorage:barrel"
								tag: {
									woodType: "oak"
								}
							}
							{
								Count: 1b
								id: "sophisticatedstorage:chest"
								tag: {
									uuid: [I;
										1297815649
										-950318685
										-1527103056
										1742563089
									]
									woodType: "oak"
								}
							}
						]
					}
				}
				title: "'原版' 箱子/木桶"
				type: "item"
			}]
			x: 5.5d
			y: 0.5d
		}
		{
			dependencies: ["0682DC1F2417DAEB"]
			description: ["制作任意规格的抽屉(1x1/2x1/2x2).根据类型不同,每个抽屉可存储大量单/双/四物品堆叠."]
			icon: "functionalstorage:oak_1"
			id: "2746575C929B6C50"
			rewards: [
				{
					id: "3F6E823825D51DAC"
					item: "functionalstorage:oak_1"
					type: "item"
				}
				{
					id: "57814182F5AA0BAB"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "层层堆叠"
			tasks: [{
				id: "79EC3CF180A5A940"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "functionalstorage:drawer"
					}
				}
				title: "任意 #storagedrawers:抽屉"
				type: "item"
			}]
			title: "我的第一个抽屉"
			x: -9.5d
			y: 0.5d
		}
		{
			dependencies: ["072FBEB0F6F1BC48"]
			dependency_requirement: "one_started"
			description: ["可将存入物品自动转换为粒或块形态.\\n\\n例如:放入&a铁锭&f后可提取9个铁粒,若继续存入则会合成铁块."]
			id: "2B422B7E0CE3590D"
			rewards: [
				{
					id: "20DD94A587AC244E"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
				{
					id: "7438B1DD5423F325"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "自动压缩/解压"
			tasks: [{
				id: "4AC0DA5197DEBC7B"
				item: "functionalstorage:compacting_drawer"
				type: "item"
			}]
			title: "高级&a压缩抽屉&f"
			x: -13.5d
			y: -1.0d
		}
		{
			dependencies: ["072FBEB0F6F1BC48"]
			dependency_requirement: "one_started"
			description: ["放置后使用&a&a连接工具&f&r与抽屉连接,即可作为'中枢'使用.\\n\\n双击右键可将背包内已有对应存储槽位的物品自动存入.\\n\\n&a潜行+右键&f空手点击控制器可打开升级界面,通过添加存储升级来扩大&a控制器&f作用范围.最大范围为40格半径或80x80x80立方区域."]
			id: "6FBAE89EE782DABA"
			rewards: [
				{
					count: 2
					id: "0817D478F3584AB6"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "64F4676E4C59322D"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "7265B47272F72DA0"
					type: "xp"
					xp: 100
				}
			]
			shape: "gear"
			size: 1.4d
			subtitle: "抽屉系统的&a核心枢纽&f"
			tasks: [{
				id: "1872E00684ADC839"
				item: "functionalstorage:storage_controller"
				type: "item"
			}]
			title: "存储控制器"
			x: -18.5d
			y: 0.5d
		}
		{
			description: ["跨维度运作的储物箱.\\n\\n支持安全色标系统(用染料&a右击&f方块设定).\\n\\n*注意:若他人使用相同色标组合可获取箱内物品!"]
			hide_dependency_lines: true
			id: "3247179F0F0252A2"
			rewards: [
				{
					id: "1C3AB68685A3FD62"
					item: {
						Count: 1
						id: "enderchests:ender_bag"
						tag: {
							code: "000"
							open: 0b
							owner: "all"
						}
					}
					type: "item"
				}
				{
					id: "43FE4B654819B6C4"
					item: {
						Count: 1
						id: "endertanks:ender_bucket"
						tag: {
							code: "000"
							owner: "all"
						}
					}
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "0116B19B3CFFB4DD"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "1C34CA939F8CC4EF"
					type: "xp"
					xp: 50
				}
			]
			tasks: [
				{
					id: "4E5DB7E53717FD3C"
					item: {
						Count: 1
						id: "enderchests:ender_chest"
						tag: {
							code: "000"
							owner: "all"
						}
					}
					type: "item"
				}
				{
					id: "322CF20C9C5FB841"
					item: {
						Count: 1
						id: "endertanks:ender_tank"
						tag: {
							code: "000"
							owner: "all"
						}
					}
					type: "item"
				}
			]
			title: "&a末影能量电容&f"
			x: -4.0d
			y: 3.0d
		}
		{
			description: ["欢迎来到基础存储章节!\\n\\n这里收录了所有无需能源的基础存储方案,以及实用的存储相关物品!"]
			hide_dependency_lines: false
			id: "5A94A2664BFDD7B9"
			rewards: [{
				id: "570FFAC4E65BBF46"
				type: "xp"
				xp: 10
			}]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "3E7F26D68D9A166B"
				title: "基础存储"
				type: "checkmark"
			}]
			x: -2.0d
			y: 3.0d
		}
		{
			description: ["可销毁多余物品、流体及能源的装置."]
			hide_dependency_lines: true
			id: "17DC77F7F8C68AE6"
			rewards: [
				{
					id: "69AE6A259BD33878"
					item: "trashcans:ultimate_trash_can"
					type: "item"
				}
				{
					id: "5828D3729B49DFEC"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "清理&a冗余物资&f"
			tasks: [
				{
					id: "7F4087A08A6F82AC"
					item: "trashcans:item_trash_can"
					type: "item"
				}
				{
					id: "6E37055D317DBBA9"
					item: "trashcans:liquid_trash_can"
					type: "item"
				}
				{
					id: "736FE7C0E7C491E7"
					item: "trashcans:energy_trash_can"
					type: "item"
				}
			]
			title: "&a垃圾桶&f"
			x: -2.0d
			y: 1.0d
		}
		{
			hide_dependency_lines: true
			id: "3D5852E6D0ADF651"
			rewards: [
				{
					count: 3
					id: "685834F0A29F6B78"
					item: "minecraft:ender_pearl"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "7BD9855705A23AEE"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "529CB0988073565D"
					type: "xp"
					xp: 50
				}
			]
			tasks: [
				{
					id: "61F1446D2A460827"
					item: "dimstorage:dimensional_chest"
					type: "item"
				}
				{
					id: "17FE9F196E4C6B44"
					item: "dimstorage:dimensional_tank"
					type: "item"
				}
			]
			title: "维度存储"
			x: 0.0d
			y: 3.0d
		}
		{
			dependencies: ["072FBEB0F6F1BC48"]
			description: ["使用&a&a连接工具&f&r,你可以将2个&a末影抽屉&f连接起来同步内容.\\n\\n工作原理类似&a末影箱&f,但适用于抽屉.\\n\\n连接时,先用&a连接工具&f&a右击&f第一个&a末影抽屉&f,然后左键点击第二个&a末影抽屉&f进行同步."]
			id: "1A4B1CA7EC15348E"
			rewards: [
				{
					id: "6F4D2071EA7C3055"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "17243362CFFD8E23"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "6138BBC4636A000E"
					item: "functionalstorage:ender_drawer"
					type: "item"
				}
			]
			shape: "diamond"
			subtitle: "次元抽屉...随你怎么称呼"
			tasks: [{
				id: "38E95D826B04DC4F"
				item: "functionalstorage:ender_drawer"
				type: "item"
			}]
			title: "&a末影抽屉&f!"
			x: -11.5d
			y: -1.0d
		}
		{
			description: ["功能存储是一个用于存放同类物品堆叠的模组.\\n\\n非常适合存放圆石、泥土等物品."]
			icon: "functionalstorage:storage_controller"
			id: "0682DC1F2417DAEB"
			rewards: [{
				id: "6FCCFD509D344B74"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			size: 1.5d
			subtitle: "&d储物抽屉&f的远亲"
			tasks: [{
				id: "4B299AC7F0233132"
				title: "功能性存储"
				type: "checkmark"
			}]
			x: -7.75d
			y: 0.5d
		}
		{
			description: ["&d精妙存储&f允许你用金属升级箱子/木桶来增加容量!还能添加升级过滤器增强功能.\\n\\n再也不用原版的双箱房间了!"]
			icon: {
				Count: 1
				id: "sophisticatedstorage:iron_chest"
				tag: {
					uuid: [I;
						767031598
						1710574310
						-2004480456
						-744336492
					]
					woodType: "oak"
				}
			}
			id: "563CFA1EF74E52E9"
			rewards: [{
				id: "58CC2C987D56D7F8"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			size: 1.5d
			subtitle: "可升级的箱子和木桶!"
			tasks: [{
				id: "034F49638F207523"
				title: "&d精妙存储&f"
				type: "checkmark"
			}]
			x: 3.0d
			y: 0.5d
		}
		{
			description: ["&a精妙背包&f提供可升级背包,还能使用过滤器添加炫酷功能!"]
			id: "6A2B2C5E2ADCE366"
			rewards: [{
				id: "1B303584800CE582"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			size: 1.5d
			subtitle: "&a精妙背包&f"
			tasks: [{
				id: "213B187A9625C1C7"
				item: "sophisticatedbackpacks:backpack"
				type: "item"
			}]
			title: "背包"
			x: 3.0d
			y: 4.0d
		}
		{
			dependencies: ["6A2B2C5E2ADCE366"]
			id: "0E057B7F76401421"
			rewards: [
				{
					count: 4
					id: "5AE4783A5679A0EC"
					item: "minecraft:iron_ingot"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "5525286901EED45A"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "147685E0FEB7FC6C"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "&a冷杉木&ft升级"
			tasks: [{
				id: "256F1CCC79CEB2D5"
				item: "sophisticatedbackpacks:iron_backpack"
				type: "item"
			}]
			title: "&a铁质背包&f"
			x: 5.25d
			y: 4.0d
		}
		{
			dependencies: ["6A2B2C5E2ADCE366"]
			description: ["制作背包过滤器升级时需要这个材料"]
			id: "1FE052F643401232"
			rewards: [
				{
					count: 3
					id: "4CCE40C0BEAAC9B4"
					item: "sophisticatedbackpacks:upgrade_base"
					type: "item"
				}
				{
					id: "29D2DEE3B1F4CCD1"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "背包过滤器的基座"
			tasks: [{
				id: "6DDC631193C9A496"
				item: "sophisticatedbackpacks:upgrade_base"
				type: "item"
			}]
			title: "升级基座"
			x: 5.25d
			y: 6.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "0E0CAA31480EC0A1"
			optional: true
			rewards: [{
				id: "35476CAA715FAC01"
				type: "xp"
				xp: 10
			}]
			shape: "circle"
			subtitle: "允许背包自动拾取物品"
			tasks: [{
				id: "0789D160EF625FF5"
				item: "sophisticatedbackpacks:pickup_upgrade"
				type: "item"
			}]
			title: "拾取升级"
			x: 5.25d
			y: 8.5d
		}
		{
			dependencies: ["0E0CAA31480EC0A1"]
			hide_until_deps_visible: true
			id: "1985CFD1F0425E88"
			optional: true
			rewards: [{
				id: "446399F56FA6E4CC"
				type: "xp"
				xp: 10
			}]
			subtitle: "更多过滤选项"
			tasks: [{
				id: "666C188829301BE0"
				item: "sophisticatedbackpacks:advanced_pickup_upgrade"
				type: "item"
			}]
			title: "&e高级拾取升级"
			x: 5.25d
			y: 9.5d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "156FF8B7B724DC38"
			optional: true
			rewards: [{
				id: "35A78C09A9FFDCF5"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加物品进出过滤器"
			tasks: [{
				id: "4DFC097C1EF485B3"
				item: "sophisticatedbackpacks:filter_upgrade"
				type: "item"
			}]
			title: "过滤升级"
			x: 8.25d
			y: 8.5d
		}
		{
			dependencies: ["156FF8B7B724DC38"]
			hide_until_deps_visible: true
			id: "57CF8C6C867B9BDA"
			optional: true
			rewards: [{
				id: "151F7412917DD326"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6530A152D9337A4C"
				item: "sophisticatedbackpacks:advanced_filter_upgrade"
				type: "item"
			}]
			title: "&e高级过滤升级"
			x: 8.25d
			y: 9.5d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "110D27EA86CDA62B"
			optional: true
			rewards: [{
				id: "76B257464525B414"
				type: "xp"
				xp: 10
			}]
			subtitle: "将物品磁吸进背包"
			tasks: [{
				id: "696764AB781624BD"
				item: "sophisticatedbackpacks:magnet_upgrade"
				type: "item"
			}]
			title: "磁吸升级"
			x: 9.75d
			y: 8.5d
		}
		{
			dependencies: ["110D27EA86CDA62B"]
			hide_until_deps_visible: true
			id: "11D57C768032E3F7"
			optional: true
			rewards: [{
				id: "13C336BAD2471C85"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "14A8CEB60805E90A"
				item: "sophisticatedbackpacks:advanced_magnet_upgrade"
				type: "item"
			}]
			title: "&e高级磁吸升级"
			x: 9.75d
			y: 9.5d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "5FA9BC0D8476D322"
			optional: true
			rewards: [{
				id: "199932BE4DEA0AEC"
				type: "xp"
				xp: 10
			}]
			subtitle: "自动食用背包内的食物"
			tasks: [{
				id: "5D185C4E74A8717D"
				item: "sophisticatedbackpacks:feeding_upgrade"
				type: "item"
			}]
			title: "进食升级"
			x: 11.25d
			y: 8.5d
		}
		{
			dependencies: ["5FA9BC0D8476D322"]
			hide_until_deps_visible: true
			id: "1A739D36D5E3B1AD"
			optional: true
			rewards: [{
				id: "7312D9A901607F82"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "41C87FC409006F13"
				item: "sophisticatedbackpacks:advanced_feeding_upgrade"
				type: "item"
			}]
			title: "&e高级进食升级"
			x: 11.25d
			y: 9.5d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "1CD2C6AFD788C35E"
			optional: true
			rewards: [{
				id: "12AF43D022829A8D"
				type: "xp"
				xp: 10
			}]
			subtitle: "将背包物品压缩为2x2合成形态"
			tasks: [{
				id: "0F54AF81A481180B"
				item: "sophisticatedbackpacks:compacting_upgrade"
				type: "item"
			}]
			title: "压缩升级"
			x: 12.75d
			y: 8.5d
		}
		{
			dependencies: ["1CD2C6AFD788C35E"]
			hide_until_deps_visible: true
			id: "5E4FE420B6D2C97F"
			optional: true
			rewards: [{
				id: "7CF6ACAD5031C606"
				type: "xp"
				xp: 10
			}]
			subtitle: "将背包物品压缩为3x3合成形态"
			tasks: [{
				id: "7CA41563003856B2"
				item: "sophisticatedbackpacks:advanced_compacting_upgrade"
				type: "item"
			}]
			title: "&e高级压缩升级"
			x: 12.75d
			y: 9.5d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7EFBFF5D0DA018E7"
			optional: true
			rewards: [{
				id: "302B2DF41B94D92D"
				type: "xp"
				xp: 10
			}]
			subtitle: "添加自动删除指定物品的过滤器"
			tasks: [{
				id: "04491E89A571B16F"
				item: "sophisticatedbackpacks:void_upgrade"
				type: "item"
			}]
			title: "虚空升级"
			x: 11.25d
			y: 11.0d
		}
		{
			dependencies: ["7EFBFF5D0DA018E7"]
			hide_until_deps_visible: true
			id: "2FC15D3916DBF4E4"
			optional: true
			rewards: [{
				id: "7A1D29EBB4A3D657"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "3A29FF1C2AD59B06"
				item: "sophisticatedbackpacks:advanced_void_upgrade"
				type: "item"
			}]
			title: "&e高级虚空升级"
			x: 11.25d
			y: 12.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			description: ["添加过滤器指定需要保持库存的物品.按住Shift+&a右击&f容器可自动补货."]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "4B62AF0405F68041"
			optional: true
			rewards: [{
				id: "53239E845A487416"
				type: "xp"
				xp: 10
			}]
			subtitle: "库存充足"
			tasks: [{
				id: "2F5D0ABEF5F4EDEF"
				item: "sophisticatedbackpacks:restock_upgrade"
				type: "item"
			}]
			title: "补货升级"
			x: 12.75d
			y: 11.0d
		}
		{
			dependencies: ["4B62AF0405F68041"]
			hide_until_deps_visible: true
			id: "019CA0E35F888222"
			optional: true
			rewards: [{
				id: "7FD2D3AA7A09EDDE"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "45492321B5D5A2D6"
				item: "sophisticatedbackpacks:advanced_restock_upgrade"
				type: "item"
			}]
			title: "&e高级补货升级"
			x: 12.75d
			y: 12.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			description: ["按住Shift+&a右击&f清空容器"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "00DB5495C3A44999"
			optional: true
			rewards: [{
				id: "5046009389D93ACB"
				type: "xp"
				xp: 10
			}]
			subtitle: "允许清空背包"
			tasks: [{
				id: "68CED4583435FE43"
				item: "sophisticatedbackpacks:deposit_upgrade"
				type: "item"
			}]
			title: "存入升级"
			x: 6.75d
			y: 8.5d
		}
		{
			dependencies: ["00DB5495C3A44999"]
			hide_until_deps_visible: true
			id: "4C0EAB9F795686D0"
			optional: true
			rewards: [{
				id: "066DD7372B41DD25"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "3D7F2E1489EB84AA"
				item: "sophisticatedbackpacks:advanced_deposit_upgrade"
				type: "item"
			}]
			title: "&e高级存入升级"
			x: 6.75d
			y: 9.5d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "33882172DA8021F6"
			optional: true
			rewards: [{
				id: "1A9808824A12EA98"
				type: "xp"
				xp: 10
			}]
			subtitle: "用背包物品保持玩家物品栏堆叠充足"
			tasks: [{
				id: "5F6536142CDA403C"
				item: "sophisticatedbackpacks:refill_upgrade"
				type: "item"
			}]
			title: "补充升级"
			x: 11.25d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			description: [
				"这个升级能让你的背包里再装个背包."
				""
				"这样当你想在背包里放背包时,就能在背包里放背包了."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7C07D6A33F7ADB02"
			optional: true
			rewards: [{
				id: "43381E4B3FC97DC1"
				type: "xp"
				xp: 10
			}]
			subtitle: "听说你喜欢背包套背包"
			tasks: [{
				id: "4F04DC4226148B47"
				item: "sophisticatedbackpacks:inception_upgrade"
				type: "item"
			}]
			title: "嵌套升级"
			x: 12.75d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "037415D5B965E214"
			optional: true
			rewards: [{
				id: "2CD4B4967BE67055"
				type: "xp"
				xp: 10
			}]
			subtitle: "&a背包&f永不损坏"
			tasks: [{
				id: "50348CD6A080A1D1"
				item: "sophisticatedbackpacks:everlasting_upgrade"
				type: "item"
			}]
			title: "永恒升级"
			x: 14.25d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "668C313FF46B6005"
			optional: true
			rewards: [{
				id: "497E02ECE5E9F003"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加熔炼标签页"
			tasks: [{
				id: "10C7C3214FFC012D"
				item: "sophisticatedbackpacks:smelting_upgrade"
				type: "item"
			}]
			title: "熔炼升级"
			x: 8.25d
			y: 11.0d
		}
		{
			dependencies: ["668C313FF46B6005"]
			hide_until_deps_visible: true
			id: "3A1D07AED2A841E4"
			optional: true
			rewards: [{
				id: "423AC602C60F8689"
				type: "xp"
				xp: 10
			}]
			subtitle: "在背包中自动熔炼物品"
			tasks: [{
				id: "4F90A434D3719DCB"
				item: "sophisticatedbackpacks:auto_smelting_upgrade"
				type: "item"
			}]
			title: "&e自动熔炼升级"
			x: 8.25d
			y: 12.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "26A9F402DAE15EA2"
			optional: true
			rewards: [{
				id: "0EA462D402635784"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加烟熏炉界面"
			tasks: [{
				id: "1ABC352A4A4313E5"
				item: "sophisticatedbackpacks:smoking_upgrade"
				type: "item"
			}]
			title: "烟熏升级"
			x: 5.25d
			y: 11.0d
		}
		{
			dependencies: ["26A9F402DAE15EA2"]
			hide_until_deps_visible: true
			id: "36BCE35215B2B6E9"
			optional: true
			rewards: [{
				id: "7D48AF3D744ADCC1"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4D0ACCBF77CC0846"
				item: "sophisticatedbackpacks:auto_smoking_upgrade"
				type: "item"
			}]
			title: "&e自动烟熏升级"
			x: 5.25d
			y: 12.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "1E499F94A2A193E1"
			optional: true
			rewards: [{
				id: "5425876A5BFE2A1D"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加高炉界面"
			tasks: [{
				id: "677DA84F4B304778"
				item: "sophisticatedbackpacks:blasting_upgrade"
				type: "item"
			}]
			title: "高炉升级"
			x: 6.75d
			y: 11.0d
		}
		{
			dependencies: ["1E499F94A2A193E1"]
			hide_until_deps_visible: true
			id: "26988E22BD019628"
			optional: true
			rewards: [{
				id: "1BA702A2526596F5"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "62BCE2BB281304DC"
				item: "sophisticatedbackpacks:auto_blasting_upgrade"
				type: "item"
			}]
			title: "&e自动高炉升级"
			x: 6.75d
			y: 12.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "4A854CD5EC70733E"
			optional: true
			rewards: [{
				id: "5B2FF2A6458AB382"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加工作台界面"
			tasks: [{
				id: "47BADC501A4E2AD7"
				item: "sophisticatedbackpacks:crafting_upgrade"
				type: "item"
			}]
			title: "合成升级"
			x: 15.75d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "52672B7FFFD51D16"
			optional: true
			rewards: [{
				id: "2D55FE84F9503C7A"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加切石界面"
			tasks: [{
				id: "3BA31C5CD5C35ED5"
				item: "sophisticatedbackpacks:stonecutter_upgrade"
				type: "item"
			}]
			title: "切石机升级"
			x: 8.25d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7E9E03274A88347D"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1981BAFF99F9A8DF"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "7B971CE531329394"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "增加背包内物品堆叠数量"
			tasks: [{
				id: "7E22C7FB2F953E70"
				item: "sophisticatedbackpacks:stack_upgrade_tier_1"
				type: "item"
			}]
			title: "堆叠升级 I阶"
			x: 11.25d
			y: 6.0d
		}
		{
			dependencies: ["7E9E03274A88347D"]
			hide_until_deps_visible: false
			id: "785951190FFDAA21"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "75C96C6BA5C9C432"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "34D7F634820E8DD7"
					type: "xp"
					xp: 200
				}
			]
			tasks: [{
				id: "15A92DA8E1A9106F"
				item: "sophisticatedbackpacks:stack_upgrade_tier_2"
				type: "item"
			}]
			title: "&e堆叠升级 II阶"
			x: 12.75d
			y: 6.0d
		}
		{
			dependencies: ["785951190FFDAA21"]
			hide_until_deps_visible: false
			id: "0298A17C2AAC5765"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "228A99C1F43EC558"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "327C239985DAE056"
					type: "xp"
					xp: 300
				}
			]
			tasks: [{
				id: "161AF6650325FA0A"
				item: "sophisticatedbackpacks:stack_upgrade_tier_3"
				type: "item"
			}]
			title: "&b堆叠升级 III阶"
			x: 14.25d
			y: 6.0d
		}
		{
			dependencies: ["0298A17C2AAC5765"]
			hide_until_deps_visible: false
			id: "7AE3C8134F5ED726"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "46D0C771D935F1C4"
					table_id: 5564196992594175882L
					type: "random"
				}
				{
					id: "4337033D7C1F4281"
					type: "xp"
					xp: 400
				}
			]
			tasks: [{
				id: "36BEE5EDBA6FAF76"
				item: "sophisticatedbackpacks:stack_upgrade_tier_4"
				type: "item"
			}]
			title: "&5堆叠升级 IV阶"
			x: 15.75d
			y: 6.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "53F861876B991C36"
			optional: true
			rewards: [{
				id: "5AE18E3BB17B47F1"
				type: "xp"
				xp: 10
			}]
			subtitle: "现在我们可以播放音乐了"
			tasks: [{
				id: "18F8BF7CC5B3126D"
				item: "sophisticatedbackpacks:jukebox_upgrade"
				type: "item"
			}]
			title: "唱片机升级"
			x: 9.75d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "615B9062FE8CCD36"
			optional: true
			rewards: [{
				id: "0D97052141459929"
				type: "xp"
				xp: 10
			}]
			subtitle: "自动切换为最适合当前观察方块的工具"
			tasks: [{
				id: "24165D460DCC2212"
				item: "sophisticatedbackpacks:tool_swapper_upgrade"
				type: "item"
			}]
			title: "&a工具切换器&f升级"
			x: 9.75d
			y: 11.0d
		}
		{
			dependencies: ["615B9062FE8CCD36"]
			hide_until_deps_visible: true
			id: "15CD4BFDC56E9510"
			optional: true
			rewards: [{
				id: "616303A9E3B78413"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "700D630FE0FB8B9D"
				item: "sophisticatedbackpacks:advanced_tool_swapper_upgrade"
				type: "item"
			}]
			title: "&e高级&a工具切换器&f升级"
			x: 9.75d
			y: 12.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "0D4C64DD58567758"
			optional: true
			rewards: [{
				id: "5F43A9D422BE05D5"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加液体存储功能"
			tasks: [{
				id: "6CA01A42CC183F87"
				item: "sophisticatedbackpacks:tank_upgrade"
				type: "item"
			}]
			title: "储罐升级"
			x: 5.25d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "4C2A60FC1F1E0328"
			optional: true
			rewards: [{
				id: "429562B29A5D8A36"
				type: "xp"
				xp: 10
			}]
			subtitle: "为背包添加能量存储功能"
			tasks: [{
				id: "056ECFB6B80F6A37"
				item: "sophisticatedbackpacks:battery_upgrade"
				type: "item"
			}]
			title: "电池升级"
			x: 6.75d
			y: 7.0d
		}
		{
			dependencies: ["1FE052F643401232"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "6E9041744C592573"
			optional: true
			rewards: [{
				id: "193F228DF534F792"
				type: "xp"
				xp: 10
			}]
			subtitle: "可从储罐升级中抽取液体"
			tasks: [{
				id: "0B9FAD684B03985A"
				item: "sophisticatedbackpacks:pump_upgrade"
				type: "item"
			}]
			title: "泵升级"
			x: 6.75d
			y: 6.0d
		}
		{
			dependencies: ["6E9041744C592573"]
			hide_until_deps_visible: true
			id: "56B80A7EBFE21428"
			optional: true
			rewards: [{
				id: "7206EF38C9AA764B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "5B7A8E1F725C2815"
				item: "sophisticatedbackpacks:advanced_pump_upgrade"
				type: "item"
			}]
			title: "&e高级泵升级"
			x: 8.25d
			y: 6.0d
		}
		{
			dependencies: ["56B80A7EBFE21428"]
			hide_until_deps_visible: true
			id: "6E3D53D1C4569A89"
			optional: true
			rewards: [{
				id: "69E16F6B3D781159"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "3B52B2670A826CE4"
				item: "sophisticatedbackpacks:xp_pump_upgrade"
				type: "item"
			}]
			title: "&a经验泵&f升级"
			x: 9.75d
			y: 6.0d
		}
		{
			dependencies: ["0E057B7F76401421"]
			id: "2F9B0C642A6BE30C"
			rewards: [
				{
					count: 2
					id: "3895B4A009120165"
					item: "minecraft:gold_ingot"
					random_bonus: 2
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "158BE322A99DAC18"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "7E4E5811F4697D04"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "7E9C8C24DD11EE7F"
				item: "sophisticatedbackpacks:gold_backpack"
				type: "item"
			}]
			title: "&e&a黄金背包&f"
			x: 7.25d
			y: 4.0d
		}
		{
			dependencies: ["2F9B0C642A6BE30C"]
			id: "45268A619787288F"
			rewards: [
				{
					count: 2
					id: "2E794D147447A3E2"
					item: "minecraft:diamond"
					random_bonus: 2
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "1821E984D977524B"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "46BF9C7865DEF10C"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "040F32155AD31A4C"
				item: "sophisticatedbackpacks:diamond_backpack"
				type: "item"
			}]
			title: "&b&a钻石背包&f"
			x: 9.25d
			y: 4.0d
		}
		{
			dependencies: ["45268A619787288F"]
			id: "67704F7341EDCC49"
			optional: true
			rewards: [
				{
					count: 4
					id: "0E9C01BFE4BB86F2"
					item: "sophisticatedbackpacks:upgrade_base"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "040F46810BB6C345"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "0692F9FA656DF9AD"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "62B16ED18AAB714F"
				item: "sophisticatedbackpacks:netherite_backpack"
				type: "item"
			}]
			title: "&5&a下界合金背包&f"
			x: 11.25d
			y: 4.0d
		}
		{
			dependencies: ["2746575C929B6C50"]
			description: ["用于将抽屉连接到控制器并连接&a末影抽屉&f.\\n\\n要将抽屉连接到控制器,请&a右键点击&f&a控制器&f方块开始配置.&a右键点击&f抽屉可将其加入网络.\\n\\n手持工具时会显示哪些抽屉已连接到控制器."]
			id: "072FBEB0F6F1BC48"
			rewards: [
				{
					id: "3625DED76F225AAE"
					item: "functionalstorage:oak_1"
					type: "item"
				}
				{
					id: "1C9DA834CECB25E9"
					type: "xp"
					xp: 50
				}
			]
			shape: "circle"
			tasks: [{
				id: "62C6F14FE6A82305"
				item: {
					Count: 1
					id: "functionalstorage:linking_tool"
					tag: {
						Action: "ADD"
						Mode: "SINGLE"
					}
				}
				type: "item"
			}]
			title: "&a连接工具&f"
			x: -12.5d
			y: 0.5d
		}
		{
			dependencies: ["2746575C929B6C50"]
			id: "485D5664A17E16DF"
			rewards: [
				{
					id: "389E00F11BC30473"
					type: "xp"
					xp: 10
				}
				{
					count: 4
					id: "367143472E975AC8"
					item: "minecraft:copper_ingot"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "6217BA32207183D1"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "8倍存储升级"
			tasks: [{
				id: "2F64FAD1FD2D3BAA"
				item: "functionalstorage:copper_upgrade"
				type: "item"
			}]
			title: "&e铜质升级"
			x: -9.5d
			y: -0.9785714285714064d
		}
		{
			dependencies: ["485D5664A17E16DF"]
			id: "3B570B3DB5F6D2CB"
			rewards: [
				{
					id: "419CCB3888465495"
					type: "xp"
					xp: 10
				}
				{
					count: 4
					id: "2DD72079356465CE"
					item: "minecraft:gold_ingot"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "3CEB38F19FD621C4"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "16倍存储升级"
			tasks: [{
				id: "25677521E16A9A63"
				item: "functionalstorage:gold_upgrade"
				type: "item"
			}]
			title: "&e金质升级"
			x: -10.0d
			y: -1.4785714285714064d
		}
		{
			dependencies: ["3B570B3DB5F6D2CB"]
			id: "1B72E95569B07E18"
			rewards: [
				{
					id: "7DDEB81C6D62844E"
					type: "xp"
					xp: 10
				}
				{
					count: 2
					id: "349616860CC71998"
					item: "minecraft:diamond"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "66529711E25F3DB7"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "24倍存储升级"
			tasks: [{
				id: "5F8923D392E224BB"
				item: "functionalstorage:diamond_upgrade"
				type: "item"
			}]
			title: "&b钻石升级"
			x: -9.4952380952381d
			y: -2.0d
		}
		{
			dependencies: ["1B72E95569B07E18"]
			id: "0E5AE195158CF344"
			rewards: [
				{
					id: "36853439572CA76B"
					type: "xp"
					xp: 10
				}
				{
					id: "5FC4E884680BC919"
					item: "minecraft:ancient_debris"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "6D7ABAF9FA2AD2A9"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "32倍存储升级"
			tasks: [{
				id: "1E992354ECC75B6D"
				item: "functionalstorage:netherite_upgrade"
				type: "item"
			}]
			title: "&5下界合金升级"
			x: -9.0d
			y: -1.4785714285714064d
		}
		{
			dependencies: ["563CFA1EF74E52E9"]
			description: ["为避免&o整个任务章节&r被过滤升级占满,请查看精妙背包升级.\\n\\n你需要制作&d精妙存储&f的等效物品,但功能基本相同."]
			id: "4007DFA7CC3A5FF2"
			rewards: [
				{
					count: 3
					id: "573A85406505C80F"
					item: "sophisticatedstorage:upgrade_base"
					type: "item"
				}
				{
					id: "34E6939A2878A929"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "用于箱子和木桶的实用工具"
			tasks: [{
				id: "57E17CFF237DB302"
				item: "sophisticatedstorage:upgrade_base"
				type: "item"
			}]
			title: "升级基座"
			x: 5.5d
			y: 2.0d
		}
		{
			dependencies: ["072FBEB0F6F1BC48"]
			description: ["&a配置&f工具用于锁定抽屉,使其记住内部存储的物品.\\n\\n这在设置从抽屉提取和存入物品的系统时很有用.&a锁定&f的抽屉不会替换被锁定的物品!"]
			id: "508A8366219175FE"
			rewards: [
				{
					id: "2E141DF65F0D3931"
					item: "functionalstorage:gold_upgrade"
					type: "item"
				}
				{
					id: "6A90B5A53F3FC368"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "2F275302DA90D4E4"
				item: {
					Count: 1
					id: "functionalstorage:configuration_tool"
					tag: {
						Mode: "LOCKING"
					}
				}
				type: "item"
			}]
			title: "配置工具"
			x: -12.5d
			y: 2.5d
		}
		{
			description: ["想要使用&9&d精致存储&f&r或&9&d应用能源2&f&r创建虚拟存储系统吗？\\n\\n这两种都是升级存储的好方法,但必须讨论&eNBT物品&r及其最佳存储方式.\\n\\nNBT物品是带有额外标签的物品.附魔物品、有耐久度的物品、&d神化&f宝石...这些都是带有NBT标签的物品.因此它们通常无法堆叠.\\n\\n当你在RS或AE2等存储系统中大量存放这类物品时,可能会对你的存档或服务器造成问题.\\n\\n因此,最好将它们存放在箱子或背包中!"]
			icon: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:protection"
						lvl: 1s
					}]
				}
			}
			id: "7EF57BBEAA4B6B08"
			min_width: 300
			rewards: [
				{
					id: "709280A79BC54D7E"
					item: "minecraft:chest"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "4F1C07541B66F171"
					table_id: 487623848494439020L
					type: "random"
				}
				{
					id: "36EA1ABB667B1E62"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "21FBC4E0F668347C"
				title: "NBT与你!"
				type: "checkmark"
			}]
			x: -2.0d
			y: 5.0d
		}
		{
			dependencies: ["6FBAE89EE782DABA"]
			description: ["当连接到存储系统时作为&a控制器&f使用,可进行物品输入输出.本质上是抽屉系统的入口节点."]
			id: "61A22707DBC83818"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5C18E9269EFD1CAD"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "00A676F87013580C"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
				{
					id: "0258F1F46A0CC9EB"
					type: "xp"
					xp: 100
				}
			]
			shape: "gear"
			size: 1.3d
			tasks: [{
				id: "28D2CA2BA72CBA7F"
				item: "functionalstorage:controller_extension"
				type: "item"
			}]
			title: "&a控制器&f访问点"
			x: -18.5d
			y: 2.5d
		}
		{
			dependencies: ["072FBEB0F6F1BC48"]
			description: ["该抽屉会自动将放入物品转换为压缩形态."]
			id: "4E4C8BCD45766F03"
			rewards: [
				{
					id: "40CC70872A021F01"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
				{
					id: "4BF4F6407A0CD34F"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "自动压缩/解压"
			tasks: [{
				id: "150E7B8293B53A14"
				item: "functionalstorage:simple_compacting_drawer"
				type: "item"
			}]
			title: "简易&a压缩抽屉&f"
			x: -14.0d
			y: -0.5d
		}
		{
			dependencies: ["6CD64FB93AD15B3D"]
			description: ["该升级会将抽屉最大容量降为1组(64个).适用于保持适量库存."]
			id: "122FBF5110E4CFF2"
			rewards: [
				{
					id: "7073532E2AB42EC4"
					type: "xp"
					xp: 10
				}
				{
					id: "6DC85CDE0D262B73"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
			]
			tasks: [{
				id: "234CA1589FD40C1F"
				item: "functionalstorage:iron_downgrade"
				type: "item"
			}]
			title: "降级升级组件"
			x: -10.0d
			y: 4.0d
		}
		{
			dependencies: ["6CD64FB93AD15B3D"]
			description: ["该升级使抽屉能从指定面自动吸取物品."]
			id: "11D4EEAC61C7E530"
			rewards: [
				{
					id: "004546CBCE58ED8D"
					type: "xp"
					xp: 10
				}
				{
					id: "483127A4B08936A6"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
			]
			tasks: [{
				id: "7A8FC5DEA767C4CB"
				item: "functionalstorage:collector_upgrade"
				type: "item"
			}]
			title: "收集者升级组件"
			x: -11.0d
			y: 2.5d
		}
		{
			dependencies: ["6CD64FB93AD15B3D"]
			description: ["该升级允许抽屉从任意面抽取物品."]
			id: "7649D648A3688D4A"
			rewards: [
				{
					id: "18A3080B3E2034B8"
					type: "xp"
					xp: 10
				}
				{
					id: "50AC5224223A3E3A"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
			]
			tasks: [{
				id: "361C5BAD8E696654"
				item: "functionalstorage:puller_upgrade"
				type: "item"
			}]
			title: "抽取升级组件"
			x: -8.0d
			y: 3.5d
		}
		{
			dependencies: ["6CD64FB93AD15B3D"]
			description: ["该升级允许抽屉向任意面输出物品."]
			id: "19DFAEB6D88F3580"
			rewards: [
				{
					id: "52ECB2FF47657347"
					type: "xp"
					xp: 10
				}
				{
					id: "07F1E3EBC80C757F"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
			]
			tasks: [{
				id: "7CE83107BC866C4F"
				item: "functionalstorage:pusher_upgrade"
				type: "item"
			}]
			title: "推送升级组件"
			x: -8.0d
			y: 2.5d
		}
		{
			dependencies: ["6CD64FB93AD15B3D"]
			description: ["当抽屉满时自动销毁多余物品.可有效防止库存溢出或系统堵塞."]
			id: "1CACE201C6AA0913"
			rewards: [
				{
					id: "33496DB9BF055108"
					type: "xp"
					xp: 10
				}
				{
					id: "10580CFC65870287"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
			]
			tasks: [{
				id: "322B685A58D540CC"
				item: "functionalstorage:void_upgrade"
				type: "item"
			}]
			title: "虚空升级组件"
			x: -9.0d
			y: 4.0d
		}
		{
			dependencies: ["6CD64FB93AD15B3D"]
			description: ["使抽屉各面输出红石信号,信号强度反映存储状态.\\n多格抽屉可配置监控特定格位.\\n\\n0表示空\\n15表示满."]
			id: "70902EF96F060672"
			rewards: [
				{
					id: "49AD46C626A80F7E"
					type: "xp"
					xp: 10
				}
				{
					id: "4C70B2868F386BAC"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
			]
			tasks: [{
				id: "47C4FF797CAC9ACA"
				item: "functionalstorage:redstone_upgrade"
				type: "item"
			}]
			title: "红石升级组件"
			x: -11.0d
			y: 3.5d
		}
		{
			dependencies: ["2746575C929B6C50"]
			description: ["除存储类升级外,还有其他实用升级组件."]
			id: "6CD64FB93AD15B3D"
			tasks: [{
				id: "504D99AC55F08CEC"
				type: "checkmark"
			}]
			title: "其他升级组件"
			x: -9.5d
			y: 2.0d
		}
		{
			description: ["&dRF工具箱&f作为功能强大的模组,包含多类实用机器/方块,存储系统是其中之一."]
			icon: "rftoolsstorage:storage_scanner"
			id: "5F2BEB5E7B72BA97"
			rewards: [{
				id: "3C2F23F7DB10D595"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			size: 1.5d
			tasks: [{
				id: "362D501E0D663695"
				title: "&dRF工具箱&f 存储"
				type: "checkmark"
			}]
			title: "&dRF工具箱&f存储系统"
			x: -7.75d
			y: 8.5d
		}
		{
			dependencies: ["5F2BEB5E7B72BA97"]
			description: ["&a存储检测器&f是强大的前期存储\"系统\",耗能实现20格范围内无线读取/存取物品.\\n\\n可通过它进行跨库存合成.\\n\\n\\n  &o注意建议:&r不兼容双联箱子(会识别为两个独立箱子,显示&a可用2次&f相同物品),可点击星标标记其一,或避免使用双联箱子."]
			id: "54EA80F374440D7C"
			rewards: [{
				id: "557569E8ED92C52A"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			subtitle: "无需再四处寻找物品"
			tasks: [{
				id: "5FC74BA4476E845F"
				item: "rftoolsstorage:storage_scanner"
				type: "item"
			}]
			title: "&a存储检测器&f"
			x: -10.5d
			y: 10.0d
		}
		{
			dependencies: ["5F2BEB5E7B72BA97"]
			description: ["&a模块化存储器&f是&dRF工具箱&f中的核心存储组件,它以数字化形式存储物品.正如其名,你可以将多个存储器组合成一个整体使用.\\n\\n 存储容量取决于你使用的&a存储模块&f等级.\\n\\n 数字系统对垃圾物品和高NBT值物品更为敏感.什么是NBT？请查阅本章节中部的\"NBT与你\"任务说明."]
			id: "38DB158EC76A8103"
			rewards: [{
				id: "6815E9D63CCDD079"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			subtitle: "数字化存储"
			tasks: [{
				id: "7086C329E169CCAD"
				item: "rftoolsstorage:modular_storage"
				type: "item"
			}]
			title: "&a模块化存储器&f"
			x: -10.5d
			y: 7.5d
		}
		{
			dependencies: ["38DB158EC76A8103"]
			description: ["&a存储模块&f是&a模块化存储器&f的存储盘片,每个等级可容纳的物品堆叠数不同.\\n\\n 这个最初级的模块可存储100组物品.注意:这可以是100组石头,或是100件无法堆叠的盔甲/工具.因此请谨慎向系统存入垃圾物品."]
			id: "4E68F5940F56CB87"
			rewards: [{
				id: "25DE5F89052876CD"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			tasks: [{
				id: "6025A561379EF1CA"
				item: "rftoolsstorage:storage_module0"
				type: "item"
			}]
			title: "&a一阶存储模块&f"
			x: -12.0d
			y: 7.5d
		}
		{
			dependencies: ["4E68F5940F56CB87"]
			description: ["二阶模块,200组存储空间."]
			id: "098649183C786A5A"
			rewards: [{
				id: "0E063D9A26D719B8"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			tasks: [{
				id: "2D896A48DB1120D2"
				item: "rftoolsstorage:storage_module1"
				type: "item"
			}]
			title: "&a二阶存储模块&f"
			x: -12.5d
			y: 7.0d
		}
		{
			dependencies: ["098649183C786A5A"]
			description: ["三阶模块,300组存储空间."]
			id: "2829977AFC6BE757"
			rewards: [{
				id: "6444293BE66B02D5"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			tasks: [{
				id: "3E08C9BB2122328A"
				item: "rftoolsstorage:storage_module2"
				type: "item"
			}]
			title: "&a三阶存储模块&f"
			x: -13.0d
			y: 7.5d
		}
		{
			dependencies: ["2829977AFC6BE757"]
			description: ["四阶模块,500组存储空间."]
			id: "7840C2F15874622B"
			rewards: [{
				id: "4A95F7DEAB64EF45"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			tasks: [{
				id: "7371DF5A0057F6CA"
				item: "rftoolsstorage:storage_module3"
				type: "item"
			}]
			title: "&a四阶存储模块&f"
			x: -12.5d
			y: 8.0d
		}
		{
			dependencies: ["38DB158EC76A8103"]
			description: ["过滤模块用途广泛,主要功能是限制哪些物品可以进入&a模块化存储器&f,无论是玩家手动放入,还是通过漏斗/管道等外部输入."]
			id: "5611C20E8B58448B"
			rewards: [{
				id: "65135FBB78FCA426"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "076EFCC8C3F0C4C9"
				item: {
					Count: 1
					id: "rftoolsbase:filter_module"
					tag: { }
				}
				type: "item"
			}]
			title: "&a存储过滤模块&f"
			x: -11.5d
			y: 6.5d
		}
		{
			dependencies: ["563CFA1EF74E52E9"]
			description: ["&a控制器&f可将多个存储器整合为单一的大型存储多方块结构.\\n\\n 由此构建的大规模存储网络,可与应用能源2、&d精致存储&f或&d动态联合/集成动力&f等模组协同工作——控制器将作为所有连接存储器的统一输入输出接口.\\n\\n 连接方式:只需将存储器放置在控制器相邻位置,或与已连接控制器的存储器相邻(最大有效范围:控制器周围14格内)"]
			id: "1FE17B1C7C639F88"
			rewards: [{
				id: "7C8696F19318FAC1"
				type: "xp"
				xp: 50
			}]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "00BFBD3FEC121599"
				item: "sophisticatedstorage:controller"
				type: "item"
			}]
			title: "&d精妙存储&f控制器"
			x: 5.5d
			y: -1.0d
		}
		{
			dependencies: [
				"1FE17B1C7C639F88"
				"78E51D22C16C4686"
			]
			description: ["存储链接器可连接未与控制器直接相连的存储器.\\n\\n 这是无线连接存储器\"多方块结构\"的解决方案,无需对每个存储器单独&a右键点击&f.\\n\\n 有效范围仍为14格."]
			id: "0108F3EFDFD6DFD0"
			rewards: [{
				id: "657FD0DD66F55304"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "031CC9264895DCFC"
				item: "sophisticatedstorage:storage_link"
				type: "item"
			}]
			title: "存储链接器"
			x: 17.0d
			y: -1.0d
		}
		{
			dependencies: ["1FE17B1C7C639F88"]
			description: ["控制器也能通过存储工具实现无线连接.只需&a右键点击&f控制器绑定工具,再&a右键点击&f目标存储器即可建立链接.\\n\\n 无线连接范围与传统方式相同,均为14格."]
			id: "78E51D22C16C4686"
			rewards: [{
				id: "67845FB550277753"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "3D7AA8FBC90E1087"
				item: "sophisticatedstorage:storage_tool"
				type: "item"
			}]
			title: "存储工具"
			x: 11.5d
			y: -2.0d
		}
		{
			dependencies: ["072FBEB0F6F1BC48"]
			description: ["想要定制化抽屉？这些抽屉外部可模仿任意方块的纹理,正面则显示不同材质.\\n\\n 具体操作方法请查看物品提示."]
			icon: "functionalstorage:framed_1"
			id: "1310C1E14C259807"
			rewards: [
				{
					id: "0621CF83BC6182A4"
					item: "functionalstorage:oak_1"
					type: "item"
				}
				{
					id: "64967CD3AC757A82"
					type: "xp"
					xp: 5
				}
			]
			tasks: [{
				id: "6528C25DF6E980E2"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "functionalstorage:framed_1"
							}
							{
								Count: 1b
								id: "functionalstorage:framed_2"
							}
							{
								Count: 1b
								id: "functionalstorage:framed_4"
							}
							{
								Count: 1b
								id: "functionalstorage:compacting_framed_drawer"
							}
							{
								Count: 1b
								id: "functionalstorage:framed_simple_compacting_drawer"
							}
							{
								Count: 1b
								id: "functionalstorage:framed_storage_controller"
							}
							{
								Count: 1b
								id: "functionalstorage:framed_controller_extension"
							}
						]
					}
				}
				type: "item"
			}]
			title: "&a镶框抽屉&f"
			x: -12.5d
			y: -1.5d
		}
		{
			dependencies: ["072FBEB0F6F1BC48"]
			description: ["如果你认为本模组只能存储物品,那就错了——这里还有&a流体抽屉&f.\\n 它们的工作原理与普通抽屉相同,但存储的是流体而非物品."]
			id: "38F9E9A830D3EA0C"
			rewards: [
				{
					id: "6B41798241947F7A"
					type: "xp"
					xp: 10
				}
				{
					id: "435F44696124D1EE"
					item: "functionalstorage:copper_upgrade"
					type: "item"
				}
			]
			tasks: [{
				id: "072A4FC654BFE3A0"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "functionalstorage:fluid_1"
							}
							{
								Count: 1b
								id: "functionalstorage:fluid_2"
							}
							{
								Count: 1b
								id: "functionalstorage:fluid_4"
							}
						]
					}
				}
				type: "item"
			}]
			title: "&a流体抽屉&f"
			x: -13.5d
			y: 1.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r创作,专用于AllTheMods整合包."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可协议,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"该任务默认隐藏,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "66388AB3CCBE3BF5"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "638F02CE74B2568A"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "442310EB7CC1518F"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -4.0d
			y: 1.0d
		}
	]
	title: "基础存储"
}
