{
	default_hide_dependency_lines: false
	default_quest_shape: "diamond"
	filename: "tips_and_tricks"
	group: ""
	icon: {
		Count: 1
		id: "constructionwand:infinity_wand"
		tag: {
			wand_options: { }
		}
	}
	id: "1BE666F01EFFC00D"
	images: [{
		height: 3.0d
		image: "atm:textures/questpics/tips_and_tricks.png"
		rotation: 0.0d
		width: 12.0d
		x: 1.0d
		y: 1.0d
	}]
	order_index: 3
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["实现无限水自动化的简易物品.需要更多水？再造一个就行!"]
			id: "722978AC0C1B2649"
			rewards: [
				{
					id: "145F9B3A17676EAA"
					item: "pipez:fluid_pipe"
					type: "item"
				}
				{
					id: "1159FE6CDF262C46"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "&a适用于&f反应堆冷却剂"
			tasks: [{
				id: "6855AE257D62AA37"
				item: "cookingforblockheads:sink"
				type: "item"
			}]
			title: "无限水"
			x: 3.0d
			y: 6.0d
		}
		{
			description: ["使用此物品可缩小体型.既有助于自动化建设,也充满趣味性."]
			id: "7EC8814940C4C3D7"
			rewards: [
				{
					id: "156D559B0B56BA9A"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "6BCF264E89365C5D"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "亲爱的我把我自己缩小了"
			tasks: [{
				id: "30C938C85BED7956"
				item: {
					Count: 1
					id: "shrink:shrinking_device"
					tag: { }
				}
				type: "item"
			}]
			title: "&a个人缩放设备&f"
			x: 1.5d
			y: 8.0d
		}
		{
			description: ["睡袋允许您在夜间入睡.\\n\\n吊床则让您能在白天休息.\\n\\n两者都不会重置您的重生点."]
			id: "14C8FC3F19190054"
			rewards: [
				{
					count: 2
					id: "34037270BA530831"
					item: "comforts:rope_and_nail"
					type: "item"
				}
				{
					id: "0CA8B7411485A9D0"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "ZZZzzz..."
			tasks: [
				{
					id: "51AA3A27028426C5"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "comforts:hammock_white"
								}
								{
									Count: 1b
									id: "comforts:hammock_orange"
								}
								{
									Count: 1b
									id: "comforts:hammock_magenta"
								}
								{
									Count: 1b
									id: "comforts:hammock_light_blue"
								}
								{
									Count: 1b
									id: "comforts:hammock_yellow"
								}
								{
									Count: 1b
									id: "comforts:hammock_lime"
								}
								{
									Count: 1b
									id: "comforts:hammock_pink"
								}
								{
									Count: 1b
									id: "comforts:hammock_gray"
								}
								{
									Count: 1b
									id: "comforts:hammock_light_gray"
								}
								{
									Count: 1b
									id: "comforts:hammock_cyan"
								}
								{
									Count: 1b
									id: "comforts:hammock_purple"
								}
								{
									Count: 1b
									id: "comforts:hammock_blue"
								}
								{
									Count: 1b
									id: "comforts:hammock_brown"
								}
								{
									Count: 1b
									id: "comforts:hammock_green"
								}
								{
									Count: 1b
									id: "comforts:hammock_red"
								}
								{
									Count: 1b
									id: "comforts:hammock_black"
								}
							]
						}
					}
					title: "吊床"
					type: "item"
				}
				{
					id: "04903282E935F3D3"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "comforts:sleeping_bag_white"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_orange"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_magenta"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_light_blue"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_yellow"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_lime"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_pink"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_gray"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_cyan"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_light_gray"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_purple"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_blue"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_brown"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_green"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_red"
								}
								{
									Count: 1b
									id: "comforts:sleeping_bag_black"
								}
							]
						}
					}
					title: "&a睡袋&f"
					type: "item"
				}
			]
			title: "舒适用品"
			x: 3.0d
			y: 5.0d
		}
		{
			description: ["快速&a切换工具模式&f的便捷方式.\\n\\n通过在铁砧上搭配皮带包升级可扩展容量."]
			id: "378C95C18798D413"
			rewards: [
				{
					count: 2
					id: "40BCE4D56FE98018"
					item: "toolbelt:pouch"
					type: "item"
				}
				{
					id: "4E495FC797AED7A4"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "7FD9A02EA29669F6"
				item: "toolbelt:belt"
				type: "item"
			}]
			title: "&a工具皮带&f"
			x: 2.5d
			y: 5.5d
		}
		{
			description: ["&a强化版&f &9建筑之杖&r可提供实用工具辅助建造.\\n\\n手持魔杖右键方块表面时,若背包有对应方块,可将该面延伸建造."]
			id: "2A2E3D020B1F5126"
			rewards: [{
				id: "03F64743F4F59499"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "176150A0FE674662"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "constructionwand:stone_wand"
								tag: {
									Damage: 0
									wand_options: { }
								}
							}
							{
								Count: 1b
								id: "constructionwand:iron_wand"
								tag: {
									Damage: 0
									wand_options: { }
								}
							}
							{
								Count: 1b
								id: "constructionwand:diamond_wand"
								tag: {
									Damage: 0
									wand_options: { }
								}
							}
							{
								Count: 1b
								id: "constructionwand:infinity_wand"
								tag: {
									wand_options: { }
								}
							}
						]
					}
				}
				title: "&a建造用星杖&f"
				type: "item"
			}]
			title: "&a建筑之杖&f"
			x: 0.5d
			y: 8.0d
		}
		{
			dependencies: [
				"722978AC0C1B2649"
				"7EC8814940C4C3D7"
				"14C8FC3F19190054"
				"378C95C18798D413"
				"2A2E3D020B1F5126"
				"05F186C95510BD4B"
				"0060BCEDABC9BE2E"
			]
			hide_dependency_lines: true
			id: "0F8F37D7E12078F5"
			rewards: [{
				id: "032158E75A1291D8"
				type: "xp"
				xp: 10
			}]
			shape: "circle"
			subtitle: "完成所有提示成就!"
			tasks: [{
				id: "42DA8E971B27ACED"
				type: "checkmark"
			}]
			title: "成就达人"
			x: 1.0d
			y: 5.5d
		}
		{
			description: ["本页面收录了助你冒险旅程的实用物品与信息!"]
			id: "0C856BBB1679A7DD"
			rewards: [{
				id: "5FA6EA3609ABF6BE"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 2.0d
			subtitle: "及其他实用物品!"
			tasks: [{
				id: "3DC008A578A93CCF"
				title: "技巧与诀窍!"
				type: "checkmark"
			}]
			title: "技巧与窍门!"
			x: 1.0d
			y: 3.0d
		}
		{
			description: ["这是简易磁吸装置!\\n\\n专业建议:可通过按键绑定&a切换开关&f功能"]
			id: "3FC002E5A6C08DCC"
			rewards: [
				{
					id: "0F5B656ED410D441"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "36D0962726704052"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			subtitle: "简易磁吸装置"
			tasks: [{
				id: "27CB2988681DB62C"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "simplemagnets:basicmagnet"
								tag: { }
							}
							{
								Count: 1b
								id: "simplemagnets:advancedmagnet"
								tag: { }
							}
						]
					}
				}
				title: "&a简易磁铁&f"
				type: "item"
			}]
			title: "&a简易磁铁&f"
			x: 1.0d
			y: 7.5d
		}
		{
			description: ["显示可搜索的生物群系列表.\\n\\n选定群系后点击「搜索」,左上角将显示信息,指南针会指向目标群系方向."]
			id: "70B6C9409AE69284"
			rewards: [
				{
					id: "554BFA90C78DEDD5"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "262AC9191FD415A3"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			subtitle: "生物群系定位器"
			tasks: [{
				id: "595B3FA9F32B712D"
				item: {
					Count: 1
					id: "naturescompass:naturescompass"
					tag: { }
				}
				type: "item"
			}]
			title: "&a自然指南针&f"
			x: 1.0d
			y: 8.5d
		}
		{
			description: ["放置&9&a巨型火把&f&r后,64格半径内将阻止敌对生物自然生成.\\n\\n有效杜绝基地阴暗处的敌怪生成"]
			id: "05F186C95510BD4B"
			rewards: [
				{
					id: "2EA020DC0A263031"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "466A0AFE85E7A5D4"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				id: "6418A273A57B203C"
				item: "torchmaster:megatorch"
				type: "item"
			}]
			title: "防止&a怪物生成&f"
			x: -1.0d
			y: 5.0d
		}
		{
			description: ["&9经验水晶&r可通过献祭等级或从储罐输入来存储经验值.\\n\\n点击按钮即可将储存的经验转化为自身等级!"]
			id: "0060BCEDABC9BE2E"
			rewards: [{
				id: "57949E0DF2C7478A"
				item: "minecraft:experience_bottle"
				type: "item"
			}]
			tasks: [{
				id: "189B87AF99321862"
				item: "utilitix:experience_crystal"
				type: "item"
			}]
			title: "经验存储"
			x: -1.0d
			y: 6.0d
		}
		{
			description: ["需要移动工作台？便携锻造台如何？\\n\\n&9合成&a便携工具&f&r新增多种便携式工作台!游戏前期必备神器."]
			id: "0790D3BB481162A6"
			rewards: [{
				id: "2D7265C36210BDDB"
				type: "xp"
				xp: 10
			}]
			tasks: [
				{
					id: "6038387EA35404CD"
					item: "crafting_on_a_stick:crafting_table"
					type: "item"
				}
				{
					id: "0BF4F9C40FF717DE"
					item: "crafting_on_a_stick:smithing_table"
					type: "item"
				}
				{
					id: "405411318EA7A80C"
					item: "crafting_on_a_stick:anvil"
					type: "item"
				}
			]
			title: "便携式合成台"
			x: -0.5d
			y: 5.5d
		}
		{
			description: ["能源生成系统已重新平衡!\\n\\n西瓜发电可能不是你想要的方案!\\n\\n燃气发电机效率仅为常规的11%.\\n\\n作为补偿:初始Powah发电机已增强 &d极限反应堆&f得到强化 &d通用机械&f &a生物发生器&f性能提升 其他发电机模组的初始输出也全面提高!\\n\\n&8机密情报:毗邻灵魂岩浆的Powah硝基热能发电机可产出31.5k rf/t且仅消耗水源——此消息来源请保密"]
			icon: "mekanismgenerators:gas_burning_generator"
			id: "437AF6A8A63413A8"
			rewards: [{
				id: "4CDC7F753855B46A"
				type: "xp"
				xp: 10
			}]
			subtitle: "能源?? 超能!"
			tasks: [{
				id: "2C621D97D1ED56DE"
				title: "RF发电"
				type: "checkmark"
			}]
			title: "RF能源体系"
			x: 5.5d
			y: 6.0d
		}
		{
			description: ["&4提升天赋上限需将末影升级球体(上限26个)与Caliburn投入魔法湖&r \\n &5将Caliburn转化为Morgan需用其击杀监守者"]
			icon: {
				Count: 1
				ForgeCaps: {
					Parent: {
						MAHOUTSUKAI_ATTACK_CAP: 0.0d
						MAHOUTSUKAI_ATTACK_DAMAGE: 0.0f
					}
				}
				id: "mahoutsukai:morgan"
				tag: {
					Damage: 0
				}
			}
			id: "51CEE80605BFF40C"
			rewards: [{
				id: "2E8A795741E4854D"
				type: "xp"
				xp: 10
			}]
			subtitle: "ATM9魔法使改动"
			tasks: [{
				id: "655D747AB2AECCF2"
				title: "魔法少女(Mahou Tsukai)"
				type: "checkmark"
			}]
			title: "魔法使(Mahou Tsukai)"
			x: 5.0d
			y: 5.5d
		}
		{
			description: ["&e移除AI:&r&m&4&a紫颂果&f&r&r &a金苹果&f \\n &e忽略玩家:&r&m&4&a下界之星&f&r&r 海灵核心 \\n &e增加实体上限:&r&a恶魂之泪&f | 最大&m&432&r&r 16 \\n &e缩短最小生成延迟:&r &m&4糖&r&r &aATM锭&f | 最小&m&420&r&r 100 \\n &e缩短最大生成延迟:&r &m&4时钟&r&r &a难得素锭&f | 最小&m&420&r&r 100"]
			icon: "minecraft:spawner"
			id: "600CFCF3612AEE9A"
			min_width: 370
			rewards: [{
				id: "50C8D9EC8D53EB82"
				type: "xp"
				xp: 10
			}]
			subtitle: "&d神化&f 刷怪笼改动"
			tasks: [{
				id: "30F1C1BC1AD18EA5"
				title: "&d神化&f"
				type: "checkmark"
			}]
			title: "&d神化&f"
			x: 5.5d
			y: 5.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r创作,专用于AllTheMods整合包."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,此任务不得用于任何非官方发布的公开整合包."
				""
				""
				""
				"此任务默认隐藏,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "21492239FE2B6937"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "6432B335C250C941"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "15DB990611A5962E"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: 1.0d
			y: 4.5d
		}
	]
	title: "技巧与诀窍"
}
