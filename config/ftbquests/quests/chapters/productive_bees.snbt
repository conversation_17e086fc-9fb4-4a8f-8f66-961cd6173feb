{
	default_hide_dependency_lines: false
	default_quest_shape: "hexagon"
	filename: "productive_bees"
	group: "6614EE2378B8AFB9"
	icon: {
		Count: 1
		id: "patchouli:guide_book"
		tag: {
			"patchouli:book": "productivebees:guide"
		}
	}
	id: "26E6ED94168A05C4"
	images: [
		{
			height: 5.0d
			image: "atm:textures/questpics/bees/productive_bees.png"
			rotation: 0.0d
			width: 20.0d
			x: 1.0d
			y: -10.0d
		}
		{
			height: 2.0d
			image: "minecraft:textures/item/honeycomb.png"
			rotation: 0.0d
			width: 2.0d
			x: -3.0d
			y: -7.0d
		}
		{
			height: 2.0d
			image: "minecraft:textures/item/honey_bottle.png"
			rotation: 0.0d
			width: 2.0d
			x: 5.0d
			y: -7.0d
		}
		{
			height: 0.3d
			hover: ["合成&aATM之星&f所需"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: -7.99d
			y: 3.0d
		}
	]
	order_index: 5
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"欢迎来到&9&d资源蜜蜂&f&r世界!"
				""
				"首先你需要收集蜂巢和&a蜂蜜瓶&f!找到蜂箱后让蜜蜂工作一段时间.当蜂箱满时用剪刀可获得蜂巢,用玻璃瓶可获得&a蜂蜜瓶&f!"
				""
				"&9重要提示&r:蜜蜂任务需要蜂巢,相关配方不会显示.如需查询请使用JEI!"
			]
			id: "13AA91D39A2CABF2"
			rewards: [
				{
					id: "1FE7DFBA702FBE64"
					item: "minecraft:dandelion"
					type: "item"
				}
				{
					id: "4D695A49C4060AFD"
					type: "xp"
					xp: 10
				}
				{
					count: 2
					id: "0E5E0909FA99CEA5"
					item: "minecraft:honeycomb"
					random_bonus: 2
					type: "item"
				}
				{
					id: "718C0C8ABA0F5052"
					item: "minecraft:honey_bottle"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [
				{
					id: "54CAFE539297E507"
					item: "minecraft:honeycomb"
					type: "item"
				}
				{
					id: "3440EB31A4FA230D"
					item: "minecraft:honey_bottle"
					type: "item"
				}
			]
			x: 1.0d
			y: -6.0d
		}
		{
			dependencies: ["29EE878BC8D3A742"]
			description: [
				"使用原版方法制作&9蜂箱&r来建立你的养蜂场吧!"
				""
				"每个蜂箱可容纳3只蜜蜂,不过我们很快会升级它..."
				""
				"蜜蜂需要特定花朵才能产蜜.原版蜜蜂可用任意花朵,但模组中多数蜜蜂需要特定方块!记得查看JEI&a获取详情&f."
			]
			id: "109AD73F016D576B"
			rewards: [{
				count: 2
				id: "398062170AD6E34C"
				item: "minecraft:honeycomb"
				type: "item"
			}]
			tasks: [{
				id: "33E5A303B907306B"
				item: "minecraft:beehive"
				type: "item"
			}]
			title: "你的第一个蜂箱!"
			x: 1.0d
			y: -1.5d
		}
		{
			dependencies: ["13AA91D39A2CABF2"]
			description: [
				"要建立养蜂场,我们需要先找到并捕捉蜜蜂."
				""
				"对蜜蜂右键点击即可捕捉!"
				""
				"探险时也可能获得&e&a坚固蜜蜂笼&f&r,记得留意!"
			]
			id: "29EE878BC8D3A742"
			rewards: [
				{
					id: "495A8E79141F31AC"
					type: "xp"
					xp: 10
				}
				{
					id: "3BAFF21709BCF43F"
					item: "productivebees:sturdy_bee_cage"
					type: "item"
				}
			]
			tasks: [{
				count: 4L
				id: "2B86DC6F7E579E0B"
				item: "productivebees:bee_cage"
				type: "item"
			}]
			title: "捕捉蜜蜂!"
			x: 1.0d
			y: -3.5d
		}
		{
			dependencies: ["109AD73F016D576B"]
			description: [
				"用原版蜂箱合成&e高级蜂箱&r,可用橡木或任意木材制作."
				""
				"蜜蜂会在此进出,并将蜂巢存入物品栏.放入玻璃瓶可获得&a蜂蜜瓶&f."
				""
				"我们需要大量蜂巢来制作饲料!"
			]
			id: "7C169A4A39F37FAC"
			rewards: [
				{
					count: 2
					id: "0E4EE8248A540A3A"
					item: "minecraft:honeycomb"
					type: "item"
				}
				{
					id: "379FD843A5076141"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.5d
			subtitle: "无需再用剪刀"
			tasks: [{
				icon: "productivebees:advanced_oak_beehive"
				id: "0EF1CA8DED2FF38C"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "productivebees:advanced_beehives"
					}
				}
				title: "高级蜂箱"
				type: "item"
			}]
			title: "高级蜂箱"
			x: 1.0d
			y: 0.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: [
				"木质蜂巢用于吸引木匠蜂和&a蓝色条纹蜜蜂&f."
				""
				"&a深色橡木蜂巢&f可吸引3种不同蜜蜂."
				""
				"可放置在任何主世界生物群系."
			]
			hide_until_deps_visible: false
			id: "00A17728A387B426"
			rewards: [{
				id: "13A6FF7C32F2A33F"
				type: "xp"
				xp: 10
			}]
			subtitle: "&a适用于&f所有主世界生物群系"
			tasks: [
				{
					id: "1F2A2E5E1319BBAA"
					item: "productivebees:oak_wood_nest"
					type: "item"
				}
				{
					id: "05B1A9B5E710DC24"
					item: "productivebees:dark_oak_wood_nest"
					type: "item"
				}
				{
					id: "3F43AC1C5F87ACA3"
					item: "productivebees:jungle_wood_nest"
					type: "item"
				}
				{
					id: "06DB37B0D01CE868"
					item: "productivebees:spruce_wood_nest"
					type: "item"
				}
				{
					id: "406D976F0C6B5CB8"
					item: "productivebees:birch_wood_nest"
					type: "item"
				}
				{
					id: "3328BB57F3B6FDDA"
					item: "productivebees:acacia_wood_nest"
					type: "item"
				}
			]
			title: "木质蜂巢"
			x: -4.0d
			y: 5.0d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["&a石头蜂巢&f可放置在任何主世界生物群系,用于吸引&a石匠蜜蜂&f或&a挖掘者蜜蜂&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "369D3AF332181DA8"
			rewards: [{
				id: "2FAF74F05A3FB855"
				type: "xp"
				xp: 10
			}]
			subtitle: "在主世界任意生物群系吸引蜜蜂"
			tasks: [{
				id: "03A3CD871B01984F"
				item: "productivebees:stone_nest"
				type: "item"
			}]
			x: -5.0d
			y: 5.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: [
				"&e&a泥土蜂巢&f&r可放置在任何主世界生物群系吸引蜜蜂."
				""
				"可吸引&a灰烬穴居蜜蜂&f、&a巧克力穴居蜜蜂&f和&a切叶蜜蜂&f."
			]
			hide_until_deps_visible: false
			id: "73C70B15582958A5"
			rewards: [{
				id: "7BF7221FDB6D90C9"
				type: "xp"
				xp: 10
			}]
			subtitle: "任意主世界生物群系"
			tasks: [{
				id: "75AC4440283F2FD2"
				item: "productivebees:coarse_dirt_nest"
				type: "item"
			}]
			x: -4.0d
			y: 4.0d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["在沙漠生物群系放置&a沙子蜂巢&f会吸引巧克力穴居蜂或&a灰烬穴居蜜蜂&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "06044B4452A6B720"
			rewards: [{
				id: "73C97FC16BFC308A"
				type: "xp"
				xp: 10
			}]
			subtitle: "在&a沙漠类生物群系&f吸引蜜蜂"
			tasks: [{
				id: "583141D1F71510B3"
				item: "productivebees:sand_nest"
				type: "item"
			}]
			x: -5.0d
			y: 4.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["在雪原生物群系放置&a雪蜂巢&f会吸引&a汗蜜蜂&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "264BFB8C5F79616F"
			rewards: [{
				id: "5109858DAD10D79F"
				type: "xp"
				xp: 10
			}]
			subtitle: "在寒冷生物群系吸引&a汗蜜蜂&f"
			tasks: [{
				id: "01C9E075D831E1AD"
				item: "productivebees:snow_nest"
				type: "item"
			}]
			x: -6.0d
			y: 3.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: [
				"&a沙砾蜂巢&f可在河流或&a沙滩生物群系&f吸引蜜蜂."
				""
				"可吸引&a灰烬穴居蜜蜂&f、&a巧克力穴居蜜蜂&f和&a挖掘者蜜蜂&f."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "09FD3D0B9BCAEB5A"
			rewards: [{
				id: "12F728528AF6A6D3"
				type: "xp"
				xp: 10
			}]
			subtitle: "在河流和&a沙滩生物群系&f吸引蜜蜂"
			tasks: [{
				id: "3091212589824103"
				item: "productivebees:gravel_nest"
				type: "item"
			}]
			x: -5.0d
			y: 3.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["&a甘蔗蜂巢&f适用于任意主世界生物群系,可吸引&a石匠蜜蜂&f或&a甘蔗蜜蜂&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "5CC9CB0911954215"
			rewards: [{
				id: "53A40EE86E11B2C0"
				type: "xp"
				xp: 10
			}]
			subtitle: "在主世界任意生物群系吸引蜜蜂"
			tasks: [{
				id: "3BE7523330044E2F"
				item: "productivebees:sugar_cane_nest"
				type: "item"
			}]
			x: -6.0d
			y: 4.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["在沼泽生物群系放置&a黏液蜂巢&f会吸引&a黏液蜜蜂&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "79D6A251FE3761B3"
			rewards: [{
				id: "4D2E30FE89E3148F"
				type: "xp"
				xp: 10
			}]
			subtitle: "在沼泽生物群系吸引&a黏液蜜蜂&f"
			tasks: [{
				id: "255B5D268D66C748"
				item: "productivebees:slimy_nest"
				type: "item"
			}]
			x: -6.0d
			y: 5.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["当放置在&a下界&f并给予荧石时,&a荧石蜂巢&f会吸引一只&a发光蜜蜂&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "41CF7CAEE4F60CCD"
			rewards: [{
				id: "310F333754B88B57"
				type: "xp"
				xp: 10
			}]
			subtitle: "在&a下界&f吸引&a发光蜜蜂&f"
			tasks: [{
				id: "09F5BBEBACBA280D"
				item: "productivebees:glowstone_nest"
				type: "item"
			}]
			x: -7.0d
			y: 5.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "027FD9135DEC4949"
			rewards: [{
				id: "1EC49763E8E46D47"
				type: "xp"
				xp: 10
			}]
			subtitle: "放置在&a下界&f并给予&a恶魂之泪&f时会吸引&a恶魂蜜蜂&f"
			tasks: [{
				id: "432B3806B0D8F356"
				item: "productivebees:soul_sand_nest"
				type: "item"
			}]
			x: -8.0d
			y: 5.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: [
				"这个蜂巢会吸引&e&a石英蜜蜂&f&r.你需要使用&a下界石英&f而非&a蜜蜂小食&f来吸引蜜蜂."
				""
				"获取石英块最简单的方法是用&a精准采集&f镐开采."
				""
				"提示:制作&d寂静装备&f工具时,黄铜具有&a精准采集&f特性."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "6D484150536536B9"
			rewards: [{
				id: "5A21046DE47F6AD0"
				type: "xp"
				xp: 100
			}]
			subtitle: "在&a下界&f吸引&a石英蜜蜂&f"
			tasks: [{
				id: "4356F84A57C41F62"
				item: "productivebees:nether_quartz_nest"
				type: "item"
			}]
			x: -7.0d
			y: 3.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["将&a下界砖蜂巢&f放置在&a下界&f并给予&a岩浆膏&f时会吸引一只&a熔岩蜜蜂&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "349D19F2FCC34B84"
			rewards: [{
				id: "4A620DDFDE9BDB88"
				type: "xp"
				xp: 10
			}]
			subtitle: "放置在&a下界&f时会吸引&a熔岩蜜蜂&f"
			tasks: [{
				id: "3722A83F8591AC9F"
				item: "productivebees:nether_brick_nest"
				type: "item"
			}]
			x: -7.0d
			y: 4.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["要吸引蜜蜂到这个蜂巢,你需要使用爆裂的&a紫颂果果实&f而非&a蜜蜂小食&f."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "1E9BD4B74DAEA9FC"
			rewards: [{
				id: "121400F2EFEEB64C"
				type: "xp"
				xp: 10
			}]
			subtitle: "放置在&a末地&f时会吸引&a末影蜜蜂&f"
			tasks: [{
				id: "23A45F19E1E556BB"
				item: "productivebees:end_stone_nest"
				type: "item"
			}]
			x: -8.0d
			y: 4.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: [
				"&a黑曜石蜂巢&f放置在&a末地&f时会吸引&a末影龙蜜蜂&f."
				""
				"这些蜂巢不接受&a蜜蜂小食&f,而是使用&a龙息&f."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "3155E4212045BC8E"
			rewards: [{
				id: "69CAC89B71A6CB41"
				type: "xp"
				xp: 100
			}]
			subtitle: "在&a末地&f吸引&a末影龙蜜蜂&f"
			tasks: [{
				id: "727AAC7516CF8B5B"
				item: "productivebees:obsidian_nest"
				type: "item"
			}]
			x: -8.0d
			y: 3.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			icon: "minecraft:iron_ingot"
			id: "114D668C691A2BDF"
			rewards: [{
				id: "70F5DFF5B0D6BEF7"
				type: "xp"
				xp: 100
			}]
			subtitle: "灰烬采矿 + 结晶"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:iron"
						}
					}
				}
				id: "6D4F62833424ADC0"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:iron"
							}
						}
					}
				}
				title: "&a铁蜜脾&f"
				type: "item"
			}]
			title: "&a铁蜜蜂&f"
			x: 1.0d
			y: 9.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: ["&a灰烬穴居蜜蜂&f由泥土、砂砾或&a沙子蜂巢&f生成."]
			hide_dependency_lines: true
			icon: {
				Count: 1
				id: "minecraft:stone_pickaxe"
				tag: {
					Damage: 0
				}
			}
			id: "37367101B3DAA70F"
			rewards: [{
				id: "5834F59FFE9CB283"
				type: "xp"
				xp: 10
			}]
			subtitle: "由&a泥土蜂巢&f生成"
			tasks: [{
				id: "3DE460F842195173"
				title: "&a灰烬穴居蜜蜂&f"
				type: "checkmark"
			}]
			x: -4.0d
			y: 12.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: [
				"&a结晶&f蜜蜂由&a下界石英蜂巢&f生成."
				""
				"这种蜜蜂是制作许多其他金属蜜蜂(如铁和铜蜜蜂)的必需品."
			]
			hide_dependency_lines: true
			icon: "minecraft:quartz"
			id: "4C47EB9D2CE26BC6"
			rewards: [{
				id: "492A77FECC7B1FBB"
				type: "xp"
				xp: 100
			}]
			subtitle: "在&a下界&f由&a下界石英蜂巢&f生成"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:crystalline"
						}
					}
				}
				id: "6E21D8349EEB2A11"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:crystalline"
							}
						}
					}
				}
				title: "水晶蜜脾"
				type: "item"
			}]
			title: "&a石英蜜蜂&f"
			x: -4.0d
			y: 11.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			icon: "minecraft:copper_ingot"
			id: "75CD4EE6A542D687"
			rewards: [{
				id: "4AD65F5EA93306F1"
				type: "xp"
				xp: 100
			}]
			subtitle: "结晶 + 灰烬采矿"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:copper"
						}
					}
				}
				id: "263F0E416A8E1110"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:copper"
							}
						}
					}
				}
				title: "&a铜蜜脾&f"
				type: "item"
			}]
			title: "&a铜蜜蜂&f"
			x: 2.0d
			y: 11.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: ["&a锡蜜蜂&f由&a石英蜜蜂&f与&a灰烬穴居蜜蜂&f繁殖获得."]
			hide_dependency_lines: true
			icon: "alltheores:tin_ingot"
			id: "589EB4602E3F9EEE"
			rewards: [{
				id: "7FA16F264547ECF7"
				type: "xp"
				xp: 100
			}]
			subtitle: "灰烬采矿 + 结晶"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:tin"
						}
					}
				}
				id: "17C7DC04BC22C0D7"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:tin"
							}
						}
					}
				}
				title: "&a锡蜜脾&f"
				type: "item"
			}]
			title: "&a锡蜜蜂&f"
			x: 3.0d
			y: 10.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			icon: "alltheores:aluminum_ingot"
			id: "0D5D76B3551CD5A7"
			rewards: [{
				id: "1E87DDE8F03C8AA8"
				type: "xp"
				xp: 100
			}]
			subtitle: "结晶 + 灰烬采矿"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:aluminum"
						}
					}
				}
				id: "79712C13C6597E82"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:aluminum"
							}
						}
					}
				}
				title: "铝蜜脾"
				type: "item"
			}]
			title: "&a铝蜜蜂&f"
			x: -1.0d
			y: 11.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			icon: "minecraft:gold_ingot"
			id: "029AA03790F055E8"
			rewards: [{
				id: "1DC7AED40698F09D"
				type: "xp"
				xp: 100
			}]
			subtitle: "结晶 + 石匠"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:gold"
						}
					}
				}
				id: "59665D8476656B9F"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:gold"
							}
						}
					}
				}
				title: "&a金蜜脾&f"
				type: "item"
			}]
			title: "&a金蜜蜂&f"
			x: 0.0d
			y: 10.0d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:brick"
			id: "24888CAD50B32B04"
			rewards: [{
				id: "40B999E6FCDAC7A8"
				type: "xp"
				xp: 10
			}]
			subtitle: "使用&a石头蜂巢&f生成"
			tasks: [{
				id: "4BC7F46B28DC6BA0"
				title: "&a石匠蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a石匠蜜蜂&f"
			x: -3.0d
			y: 13.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: [
				"在&d资源蜜蜂&f中,你不需要花费大量时间四处飞行寻找特定蜜蜂."
				""
				"相反,你可以使用蜂巢和&e&a蜜蜂小食&f&r来生成它们."
				""
				"使用这些物品,你可以创建一些蜂巢并&a右击&f它们来吸引蜜蜂.某些蜂巢需要特殊物品而非&a蜜蜂小食&f,所以请务必查看JEI以&a了解更多信息&f!"
				""
				"确保查看你需要处于哪个生物群系才能吸引正确的蜜蜂!"
			]
			id: "131EC039435B8878"
			rewards: [
				{
					id: "4F2B6C905FB137F0"
					item: "productivebees:honey_treat"
					random_bonus: 2
					type: "item"
				}
				{
					id: "327E963A18728867"
					type: "xp"
					xp: 10
				}
			]
			shape: "gear"
			size: 3.0d
			tasks: [{
				id: "1C677CD515E8862B"
				item: "productivebees:honey_treat"
				type: "item"
			}]
			x: 1.0d
			y: 4.5d
		}
		{
			dependencies: ["29EE878BC8D3A742"]
			description: ["右击你正在寻找的类型的蜂巢会为你指向另一个蜂巢的方向!"]
			id: "68CE30097BADE20E"
			optional: true
			rewards: [
				{
					id: "6C1DDDA29011FD24"
					item: "minecraft:honey_bottle"
					type: "item"
				}
				{
					id: "14996378A430180C"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "10D83EFF8DF8830B"
				item: {
					Count: 1
					id: "productivebees:nest_locator"
					tag: { }
				}
				type: "item"
			}]
			title: "寻找蜂巢"
			x: 2.0d
			y: -2.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: ["&a新材料发现&f基础用于制作&d资源蜜蜂&f中的各种升级."]
			hide_dependency_lines: true
			id: "66324D7D0C51AEAC"
			rewards: [
				{
					id: "279DEFA11701B0C6"
					type: "xp"
					xp: 10
				}
				{
					id: "190005A9561D76D2"
					item: "productivebees:upgrade_base"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "26D057A4EDC423A9"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			size: 1.5d
			tasks: [{
				id: "52AA599123DF9D78"
				item: "productivebees:upgrade_base"
				type: "item"
			}]
			x: -3.5d
			y: 0.5d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			id: "6DBF9CAB37B9BBF3"
			rewards: [
				{
					id: "532DF28E46411BAA"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "01C9C90A328265CE"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			subtitle: "提高蜜蜂生产力120%"
			tasks: [{
				id: "23697EE31757EBEB"
				item: "productivebees:upgrade_productivity"
				type: "item"
			}]
			x: -5.0d
			y: 0.5d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			description: [
				"可以放置在蜂箱或离心机中."
				""
				"在蜂箱中时,它减少蜜蜂在蜂箱中停留的时间20%%."
				""
				"放置在离心机中时,它提高处理速度."
				""
				"这些效果可以叠加."
			]
			id: "500BEAD94C97DF96"
			rewards: [
				{
					id: "7B68706C7E4D9550"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "23E4F84BF4C64AB3"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "音速蜜蜂"
			tasks: [{
				id: "2F69E114B9F17B9A"
				item: "productivebees:upgrade_time"
				type: "item"
			}]
			x: -2.5d
			y: -0.5d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			description: [
				"安装在蜂箱中时,每次蜂蜜交付时有5%%的几率生成一只新的幼蜂."
				""
				"放置在捕捉器中时,它只允许捕捉器捕捉幼蜂."
				""
				"你可以叠加这些以提高几率."
			]
			id: "2182492BCC1B33D8"
			rewards: [
				{
					id: "4423FDFB72B07AFC"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "60C16B8456B350DF"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			subtitle: "繁殖幼蜂"
			tasks: [{
				id: "04E3182336EE3967"
				item: "productivebees:upgrade_breeding"
				type: "item"
			}]
			x: -3.5d
			y: 2.0d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			id: "397E2D14BDE7DED0"
			rewards: [
				{
					id: "08153982346B6697"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "2DD19E19BEC19037"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			subtitle: "伐木与&a石料蜜蜂&f会产出方块而非碎片"
			tasks: [{
				id: "2289A74F096C389C"
				item: "productivebees:upgrade_comb_block"
				type: "item"
			}]
			x: -4.5d
			y: -0.5d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			description: ["你需要这些来培育&a末影蜜蜂&f."]
			id: "198EDBAC1D6E2339"
			rewards: [
				{
					id: "741664A37CF3CE09"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "6D74E7326BB52C6E"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			subtitle: "防止蜜蜂在蜂巢内传送"
			tasks: [{
				id: "09CA837E39927465"
				item: "productivebees:upgrade_anti_teleport"
				type: "item"
			}]
			x: -3.5d
			y: -1.0d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			description: ["主要用于捕蜂器."]
			id: "14623D25561BA61B"
			rewards: [
				{
					id: "7047D156FCBF9AE4"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "075F2B26AEEAD55B"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			subtitle: "增加机器工作范围"
			tasks: [{
				id: "6E8978E798AB54F7"
				item: "productivebees:upgrade_range"
				type: "item"
			}]
			x: -2.5d
			y: 1.5d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			id: "57167FE67CFAC255"
			rewards: [
				{
					id: "1A390E7C759A0668"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "7DCEF23469265229"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			subtitle: "用于向过滤器添加蜜蜂"
			tasks: [{
				id: "553DD93D10666049"
				item: "productivebees:upgrade_filter"
				type: "item"
			}]
			x: -4.5d
			y: 1.5d
		}
		{
			dependencies: ["66324D7D0C51AEAC"]
			id: "1DF026030780AE96"
			rewards: [
				{
					id: "6AA353FFF5071D08"
					item: "productivebees:honey_treat"
					type: "item"
				}
				{
					id: "0E1128C2FFC5105B"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "从蜂巢蜜蜂体内提取基因"
			tasks: [{
				id: "1B0C60960B1ABA92"
				item: "productivebees:upgrade_bee_sampler"
				type: "item"
			}]
			x: -2.0d
			y: 0.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: [
				"&9离心机&r用于将蜂巢中的蜜脾加工成有用物品和蜂蜜!虽然初期使用普通&9离心机&r即可,但尽快升级为&e&a动力离心机&f&r是必须的.这是依靠电力驱动的更快速离心机!"
				""
				"若追求最高效的蜜脾处理方式,&c&a热能离心机&f&r速度更快,甚至能处理&a&a蜜脾块&f&r!"
				""
				"使用&a风驰&f等级可进一步提升所有离心机效率."
			]
			id: "33A0E06FE5CFD8F3"
			rewards: [
				{
					count: 2
					id: "498F2AE7D6D03C4B"
					item: "minecraft:honeycomb"
					random_bonus: 2
					type: "item"
				}
				{
					id: "412881B0F7ADFF14"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&a处理&f蜜脾"
			tasks: [{
				id: "65D52E6A67DD11EB"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "productivebees:centrifuge"
							}
							{
								Count: 1b
								id: "productivebees:powered_centrifuge"
							}
							{
								Count: 1b
								id: "productivebees:heated_centrifuge"
							}
						]
					}
				}
				title: "离心机"
				type: "item"
			}]
			x: 0.0d
			y: 1.5d
		}
		{
			dependencies: [
				"5F1784E562C29B66"
				"100ACB5C8A359BF0"
			]
			description: ["将&a末影蜜蜂&f与&a青金石蜜蜂&f杂交可获得&a钻石蜜蜂&f!"]
			icon: "minecraft:diamond"
			id: "1D720AC88431BD70"
			rewards: [
				{
					count: 2
					id: "7A4D2E2C591FB3CD"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:diamond"
							}
						}
					}
					type: "item"
				}
				{
					id: "1EF8A565196DEB2C"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "末影 + 青金石"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:diamond"
						}
					}
				}
				id: "4EF5B261BAD2AC7D"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:diamond"
							}
						}
					}
				}
				title: "&a钻石蜂巢&f"
				type: "item"
			}]
			title: "&a钻石蜜蜂&f"
			x: 8.0d
			y: 4.5d
		}
		{
			dependencies: ["117241986C99E475"]
			description: ["将&a红石蜜蜂&f与&a蓝色条纹蜜蜂&f杂交可获得&a青金石蜜蜂&f!"]
			icon: "minecraft:lapis_lazuli"
			id: "100ACB5C8A359BF0"
			rewards: [
				{
					id: "7633FEC136EBA1D4"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "1ED82242D8118B20"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:lapis"
							}
						}
					}
					type: "item"
				}
			]
			subtitle: "红石 + 蓝纹"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:lapis"
						}
					}
				}
				id: "429FA8057B666565"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:lapis"
							}
						}
					}
				}
				title: "&a青金石蜂巢&f"
				type: "item"
			}]
			title: "&a青金石蜜蜂&f"
			x: 7.0d
			y: 4.5d
		}
		{
			dependencies: ["437DB2CE10D33A08"]
			description: ["让&a发光蜜蜂&f与&a巧克力穴居蜜蜂&f杂交可获得&a红石蜜蜂&f!"]
			disable_toast: true
			icon: "minecraft:redstone"
			id: "117241986C99E475"
			rewards: [
				{
					count: 2
					id: "0E473480AE6A71DF"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:redstone"
							}
						}
					}
					type: "item"
				}
				{
					id: "1AA439A6CAB4A90A"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "发光 + 巧克力矿工"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:redstone"
						}
					}
				}
				id: "345245C32DB7B4D4"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:redstone"
							}
						}
					}
				}
				title: "&a红石蜂巢&f"
				type: "item"
			}]
			title: "&a红石蜜蜂&f"
			x: 7.0d
			y: 5.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: ["制作&5&a末地石蜂巢&f&r并前往&a末地&f捕捉这种蜜蜂!"]
			hide_dependency_lines: true
			icon: "minecraft:ender_pearl"
			id: "5F1784E562C29B66"
			rewards: [
				{
					count: 2
					id: "565C007060C1F9EC"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:ender"
							}
						}
					}
					type: "item"
				}
				{
					id: "1EC07E1836DAB17A"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&9需要&a末地石蜂巢&f"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:ender"
						}
					}
				}
				id: "43515B26807F9E01"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:ender"
							}
						}
					}
				}
				title: "&a末影蜜脾&f"
				type: "item"
			}]
			title: "&a末影蜜蜂&f"
			x: 8.0d
			y: 3.5d
		}
		{
			dependencies: ["4C0302FF4F63B52E"]
			description: ["你需要准备&e&a荧石蜂巢&f&r并前往&a下界&f捕捉这种蜜蜂!"]
			hide_dependency_lines: false
			icon: "minecraft:glowstone_dust"
			id: "437DB2CE10D33A08"
			rewards: [
				{
					count: 2
					id: "33CF41454AF3192E"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:glowing"
							}
						}
					}
					type: "item"
				}
				{
					id: "7274DFEAC7126A2D"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:glowing"
						}
					}
				}
				id: "17B0E19125FCFA1A"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:glowing"
							}
						}
					}
				}
				title: "&a发光蜜脾&f"
				type: "item"
			}]
			title: "&a发光蜜蜂&f"
			x: 6.0d
			y: 5.0d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "croptopia:chocolate"
			id: "486060882E507CF1"
			rewards: [{
				id: "6C008390E836F306"
				type: "xp"
				xp: 10
			}]
			subtitle: "由&a泥土蜂巢&f生成"
			tasks: [{
				id: "4DFDEAD81868CEF4"
				title: "&a巧克力穴居蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a巧克力穴居蜜蜂&f"
			x: -5.0d
			y: 12.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:blue_dye"
			id: "2CA4D7253DA1825F"
			rewards: [{
				id: "0D9788B0439CE954"
				type: "xp"
				xp: 10
			}]
			subtitle: "使用木质蜂巢生成"
			tasks: [{
				id: "12A2CE1C3AE8A033"
				title: "&a蓝色条纹蜜蜂&f"
				type: "checkmark"
			}]
			x: -4.0d
			y: 15.5d
		}
		{
			dependencies: [
				"1D720AC88431BD70"
				"39A19138C501B16F"
			]
			description: ["获得&a钻石蜜蜂&f后,将其与&a黏液蜜蜂&f杂交可培育绿宝石蜜蜂!"]
			icon: "minecraft:emerald"
			id: "5563BD4934297522"
			rewards: [
				{
					count: 2
					id: "50B0611117EE75AB"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:emerald"
							}
						}
					}
					type: "item"
				}
				{
					id: "0FE7E2772FA1C09A"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&a钻石蜜蜂&f + &a黏液蜜蜂&f"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:emerald"
						}
					}
				}
				id: "7B7C1C5BFEC92058"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:emerald"
							}
						}
					}
				}
				title: "绿宝石蜜脾"
				type: "item"
			}]
			title: "绿宝石蜜蜂"
			x: 7.0d
			y: 3.5d
		}
		{
			dependencies: ["4C0302FF4F63B52E"]
			description: ["在沼泽生物群系使用&a黏液蜂巢&f可吸引这类蜜蜂."]
			icon: "minecraft:slime_ball"
			id: "39A19138C501B16F"
			rewards: [
				{
					count: 2
					id: "6A5C1F186FAFFADA"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:slimy"
							}
						}
					}
					type: "item"
				}
				{
					id: "5E11CF7211ADB15A"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&9需要&a黏液蜂巢&f"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:slimy"
						}
					}
				}
				id: "5E017E6B7E3F56B7"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:slimy"
							}
						}
					}
				}
				title: "&a粘滑蜂巢&f"
				type: "item"
			}]
			title: "&a黏液蜜蜂&f"
			x: 6.0d
			y: 4.0d
		}
		{
			dependencies: ["1D720AC88431BD70"]
			icon: "minecraft:netherite_ingot"
			id: "2CA3707BEE2E3C0D"
			rewards: [
				{
					count: 2
					id: "6B10EC7376089305"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:netherite"
							}
						}
					}
					type: "item"
				}
				{
					id: "36535E022831C4FD"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "给&a钻石蜜蜂&f喂食&a块状&f下界合金"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:netherite"
						}
					}
				}
				id: "065E5450AC87F1D5"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:netherite"
							}
						}
					}
				}
				title: "&a远古蜜脾&f"
				type: "item"
			}]
			title: "&a远古蜜蜂&f(下界合金)"
			x: 9.0d
			y: 4.5d
		}
		{
			dependencies: ["76E94639E90FEB4E"]
			icon: "minecraft:wither_skeleton_skull"
			id: "399882F3C51DD282"
			rewards: [
				{
					count: 2
					id: "2A44E3F699DB9247"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:withered"
							}
						}
					}
					type: "item"
				}
				{
					id: "095AA005F163ECC5"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "给&a骷髅蜜蜂&f喂食凋零玫瑰"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:withered"
						}
					}
				}
				id: "5BFAA4BB6651F71A"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:withered"
							}
						}
					}
				}
				title: "&a凋灵蜜脾&f"
				type: "item"
			}]
			title: "&a凋灵蜜蜂&f"
			x: 10.0d
			y: 3.5d
		}
		{
			dependencies: [
				"2CA3707BEE2E3C0D"
				"399882F3C51DD282"
			]
			description: ["将&a凋灵蜜蜂&f与&a远古蜜蜂&f杂交可获得&aATM蜜蜂&f."]
			icon: "allthemodium:raw_allthemodium"
			id: "6EFFF0DC80C1C8A3"
			rewards: [
				{
					count: 2
					id: "52BF6B50DD93E0C7"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:allthemodium"
							}
						}
					}
					type: "item"
				}
				{
					id: "43280AD345FCEAC4"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4328B4BBA9A222F2"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			subtitle: "远古 + 凋灵"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:allthemodium"
						}
					}
				}
				id: "5851FC52DDFE7826"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:allthemodium"
							}
						}
					}
				}
				title: "&aATM蜜脾&f"
				type: "item"
			}]
			title: "&aATM蜜蜂&f"
			x: 10.0d
			y: 4.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: ["在无光照区域放置空的&e高级蜂箱&r,蜜蜂会随时间推移自行入住."]
			hide_dependency_lines: true
			icon: "minecraft:skeleton_skull"
			id: "76E94639E90FEB4E"
			rewards: [
				{
					count: 2
					id: "7A6D1C4C8A908A18"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:skeletal"
							}
						}
					}
					type: "item"
				}
				{
					id: "70F080D7463D7B92"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "黑暗环境中用空蜂箱生成"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:skeletal"
						}
					}
				}
				id: "4203F7ED807F3D30"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:skeletal"
							}
						}
					}
				}
				title: "骷髅蜜脾"
				type: "item"
			}]
			title: "&a骷髅蜜蜂&f"
			x: 9.0d
			y: 3.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: ["在&a末地&f放置&a黑曜石蜂巢&f可吸引这种蜜蜂."]
			hide_dependency_lines: true
			icon: "minecraft:dragon_head"
			id: "00FD36C207845895"
			rewards: [
				{
					count: 2
					id: "52BFF2CF6ECE1B6B"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:draconic"
							}
						}
					}
					type: "item"
				}
				{
					id: "7BB5EF8129DEFE69"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&9需要&a黑曜石蜂巢&f"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:draconic"
						}
					}
				}
				id: "2CC38211F4C54ED8"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:draconic"
							}
						}
					}
				}
				title: "&a龙蜜脾&f"
				type: "item"
			}]
			title: "&a末影龙蜜蜂&f"
			x: 8.0d
			y: 5.5d
		}
		{
			dependencies: [
				"2CA3707BEE2E3C0D"
				"00FD36C207845895"
			]
			description: ["将&a末影龙蜜蜂&f与&a远古蜜蜂&f杂交可获得&a振金蜜蜂&f!"]
			icon: "allthemodium:raw_vibranium"
			id: "6E819CCD57B15D54"
			rewards: [
				{
					count: 2
					id: "7E3C72B5CB548A0D"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:vibranium"
							}
						}
					}
					type: "item"
				}
				{
					id: "525D801E1FF98425"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "1FDBF76E4CB0BB03"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			subtitle: "远古 + 龙息"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:vibranium"
						}
					}
				}
				id: "21F18F576B555114"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:vibranium"
							}
						}
					}
				}
				title: "&a振金蜜脾&f"
				type: "item"
			}]
			title: "&a振金蜜蜂&f"
			x: 9.0d
			y: 5.5d
		}
		{
			dependencies: [
				"6EFFF0DC80C1C8A3"
				"6E819CCD57B15D54"
			]
			description: [
				"获得全矿物与&a振金蜜蜂&f后,将它们杂交可获得&a难得素蜜蜂&f."
				""
				"培育方法:给&aATM蜜蜂&f喂食4块振金锭,再给&a振金蜜蜂&f喂食4块难得素锭."
			]
			icon: "allthemodium:raw_unobtainium"
			id: "2BE538246C672689"
			rewards: [
				{
					count: 2
					id: "252172AFDCBF882B"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:unobtainium"
							}
						}
					}
					type: "item"
				}
				{
					id: "4E4DBE9FF2C1BBD0"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "752568EB48133C34"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			subtitle: "全矿物 + 振金"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:unobtainium"
						}
					}
				}
				id: "271BDC9E7B37BCB5"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:unobtainium"
							}
						}
					}
				}
				title: "&a难得素蜜脾&f"
				type: "item"
			}]
			title: "&a难得素蜜蜂&f"
			x: 10.0d
			y: 5.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			icon: "alltheores:zinc_ingot"
			id: "7EAFF64FFE8B5378"
			rewards: [{
				id: "4537E0AF362216D5"
				type: "xp"
				xp: 100
			}]
			subtitle: "铁 + 汗液"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:zinc"
						}
					}
				}
				id: "181135E3A83C5B9E"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:zinc"
							}
						}
					}
				}
				title: "&a锌蜜脾&f"
				type: "item"
			}]
			title: "&a锌蜜蜂&f"
			x: 1.0d
			y: 12.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:electrum_ingot"
			id: "3054D016D2EF25FF"
			rewards: [{
				id: "6FA6170C19493AB7"
				type: "xp"
				xp: 100
			}]
			subtitle: "金 + 银"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:electrum"
						}
					}
				}
				id: "299DE26FF7293F34"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:electrum"
							}
						}
					}
				}
				title: "&a琥珀金蜜脾&f"
				type: "item"
			}]
			title: "&a琥珀金蜜蜂&f"
			x: 0.0d
			y: 14.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:platinum_ingot"
			id: "57ACD83205988834"
			rewards: [{
				id: "0C66CBCE5AF692A1"
				type: "xp"
				xp: 100
			}]
			subtitle: "金蜜蜂 + 末影蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:platinum"
						}
					}
				}
				id: "726FF2C87E9E972F"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:platinum"
							}
						}
					}
				}
				title: "&a铂蜜脾&f"
				type: "item"
			}]
			title: "&a铂金蜜蜂&f"
			x: 0.0d
			y: 11.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:lead_ingot"
			id: "2DFFEB29B6CBFD99"
			rewards: [{
				id: "31ED03CD02AD0E62"
				type: "xp"
				xp: 100
			}]
			subtitle: "铁蜜蜂 + 蓝带蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:lead"
						}
					}
				}
				id: "7830C98BD32DB5F2"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:lead"
							}
						}
					}
				}
				title: "铅蜜脾"
				type: "item"
			}]
			title: "&a铅蜜蜂&f"
			x: 1.0d
			y: 10.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			icon: "minecraft:blaze_rod"
			id: "0072C4F028C327CB"
			rewards: [{
				id: "05136B66D31720B1"
				type: "xp"
				xp: 100
			}]
			subtitle: "岩浆蜜蜂 + 游牧蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:blazing"
						}
					}
				}
				id: "6B7DB7E426D328BC"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:blazing"
							}
						}
					}
				}
				title: "&a烈焰蜜脾&f"
				type: "item"
			}]
			title: "&a烈焰蜜蜂&f"
			x: -1.0d
			y: 10.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:brass_ingot"
			id: "5DF26D712B643655"
			rewards: [{
				id: "024DA8780F619DC0"
				type: "xp"
				xp: 100
			}]
			subtitle: "铜蜜蜂 + 锌蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:brass"
						}
					}
				}
				id: "79AEDC66EB312BCA"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:brass"
							}
						}
					}
				}
				title: "黄铜蜜脾"
				type: "item"
			}]
			title: "&a黄铜蜜蜂&f"
			x: 1.0d
			y: 14.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "twilightforest:fallen_leaves"
			id: "6BEE3578BD2C713C"
			rewards: [{
				id: "1C28FA3EA99E8BF1"
				type: "xp"
				xp: 10
			}]
			subtitle: "从&a泥土蜂巢&f生成"
			tasks: [{
				id: "4A6ADD52B113E3CA"
				title: "&a切叶蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a切叶蜜蜂&f"
			x: -4.0d
			y: 14.0d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "productivebees:spawn_egg_neon_cuckoo_bee"
			id: "683B58B699D4D381"
			rewards: [{
				id: "713068FC6AD69883"
				type: "xp"
				xp: 100
			}]
			subtitle: "在含有&a蓝色条纹蜜蜂&f的蜂巢中生成"
			tasks: [{
				id: "0CDFF60D581118E3"
				title: "&a杜鹃蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a杜鹃蜜蜂&f"
			x: -5.0d
			y: 16.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: [
				"若想获得此蜜蜂,需先拥有&a灰烬穴居蜜蜂&f"
				""
				"当&a灰烬穴居蜜蜂&f安居巢中时,有概率被&a牧游蜜蜂&f取代"
			]
			hide_dependency_lines: true
			icon: {
				Count: 1
				id: "minecraft:wooden_sword"
				tag: {
					Damage: 0
				}
			}
			id: "6786B08C30D26037"
			rewards: [{
				id: "4421D42829BE3B93"
				type: "xp"
				xp: 100
			}]
			subtitle: "在含有&a灰烬穴居蜜蜂&f的蜂巢中生成"
			tasks: [{
				id: "64248C6FBC867D56"
				title: "&a牧游蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a牧游蜜蜂&f"
			x: -3.0d
			y: 15.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "productivebees:sugar_cane_nest"
			id: "097DE7038A746847"
			rewards: [{
				id: "447B59A2C9693336"
				type: "xp"
				xp: 10
			}]
			subtitle: "从&a甘蔗蜂巢&f生成"
			tasks: [{
				id: "6887F86C8673DAF6"
				title: "&a甘蔗蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a甘蔗蜜蜂&f"
			x: -5.0d
			y: 15.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:sugar"
			id: "40E4F1172A164DD1"
			rewards: [{
				id: "69806B463D43C9FD"
				type: "xp"
				xp: 100
			}]
			subtitle: "在丛林破坏&a可可果荚&f时有概率生成"
			tasks: [{
				id: "49F452C12DF14873"
				item: "productivebees:sugarbag_honeycomb"
				type: "item"
			}]
			title: "&a蜜袋蜜蜂&f"
			x: -4.0d
			y: 17.0d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:snowball"
			id: "447CAC472A99835B"
			rewards: [{
				id: "5F80C0F9DAB3D6F7"
				type: "xp"
				xp: 10
			}]
			subtitle: "从&a雪蜂巢&f生成"
			tasks: [{
				id: "0A77407CE9055F04"
				title: "&a汗蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a汗蜂&f"
			x: -5.0d
			y: 13.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:yellow_dye"
			id: "2226555D9552236E"
			rewards: [{
				id: "42FD59C35B7B5AD1"
				type: "xp"
				xp: 10
			}]
			subtitle: "从多数木质蜂巢生成"
			tasks: [{
				id: "3CDFAF37BFB1C89C"
				title: "&a黄色木匠蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a黄色木匠蜜蜂&f"
			x: -3.0d
			y: 14.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:zombie_head"
			id: "371E09ED2A3F6BDC"
			rewards: [{
				id: "5AE0AE1E13A9AD43"
				type: "xp"
				xp: 100
			}]
			subtitle: "在黑暗处空的进阶蜂箱中生成"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:zombie"
						}
					}
				}
				id: "71E81E13769F2FF0"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:zombie"
							}
						}
					}
				}
				title: "僵尸蜜蜂蜜脾"
				type: "item"
			}]
			title: "僵尸蜂"
			x: -3.0d
			y: 12.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:bronze_ingot"
			id: "6E4C3B87FABE9EFA"
			rewards: [{
				id: "50DDB12B5BB338D2"
				type: "xp"
				xp: 100
			}]
			subtitle: "铜蜜蜂 + 锡蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:bronze"
						}
					}
				}
				id: "55F718D796CEB1B1"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:bronze"
							}
						}
					}
				}
				title: "&a青铜蜜脾&f"
				type: "item"
			}]
			title: "&a青铜蜜蜂&f"
			x: 1.0d
			y: 13.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:coal"
			id: "71667FFC8011525C"
			rewards: [{
				id: "0BEB353ACC73AD12"
				type: "xp"
				xp: 100
			}]
			subtitle: "岩浆蜜蜂 + 切叶蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:coal"
						}
					}
				}
				id: "3EDE79B7957D45AF"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:coal"
							}
						}
					}
				}
				title: "&a煤蜂巢&f"
				type: "item"
			}]
			title: "&a煤炭蜜蜂&f"
			x: 2.0d
			y: 10.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			icon: "alltheores:constantan_ingot"
			id: "52B22C07818981D0"
			rewards: [{
				id: "3EECD8A6FAE44C39"
				type: "xp"
				xp: 100
			}]
			subtitle: "铜蜜蜂 + 镍蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:constantan"
						}
					}
				}
				id: "4FCEB24FC83D22A9"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:constantan"
							}
						}
					}
				}
				title: "&a康铜蜜脾&f"
				type: "item"
			}]
			title: "&a康铜蜜蜂&f"
			x: 2.0d
			y: 14.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: ["这是培育专用蜂种"]
			hide_dependency_lines: true
			icon: "minecraft:chicken"
			id: "2E51F09F6D9E5EF8"
			rewards: [{
				id: "228543A8ADEDCE7E"
				type: "xp"
				xp: 100
			}]
			subtitle: "农夫蜂 + 牧场蜂"
			tasks: [{
				id: "34C16667CDF3E53A"
				type: "checkmark"
			}]
			title: "培育蜂"
			x: -1.0d
			y: 14.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:red_dye"
			id: "5962DC39E5874FB9"
			rewards: [{
				id: "29F6DF8F52E48EC6"
				type: "xp"
				xp: 100
			}]
			subtitle: "青金石蜜蜂 + 骸骨蜜蜂"
			tasks: [{
				id: "4FC90CCB41E58D5A"
				title: "&a染料蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a染料蜜蜂&f"
			x: 1.0d
			y: 15.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:enderium_ingot"
			id: "0D97CDE92F3B1A83"
			rewards: [{
				id: "7F7B0C8A87197491"
				type: "xp"
				xp: 100
			}]
			subtitle: "铅蜜蜂 + 钻石/铂金蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:enderium"
						}
					}
				}
				id: "208145EA6533CDDB"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:enderium"
							}
						}
					}
				}
				title: "&a末影锭蜜脾&f"
				type: "item"
			}]
			title: "&a末影锭蜜蜂&f"
			x: -1.0d
			y: 12.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:experience_bottle"
			id: "0FB1FC640471363A"
			rewards: [{
				id: "5477AEFA094D2108"
				type: "xp"
				xp: 100
			}]
			subtitle: "青金石蜜蜂 + 绿宝石蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:experience"
						}
					}
				}
				id: "1E28BFC0AB5CF2FE"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:experience"
							}
						}
					}
				}
				title: "&a经验蜜脾&f"
				type: "item"
			}]
			title: "&a经验蜜蜂&f"
			x: -1.0d
			y: 15.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: {
				Count: 1
				id: "minecraft:iron_hoe"
				tag: {
					Damage: 0
				}
			}
			id: "4897C7BB3139C6C6"
			rewards: [{
				id: "09794E3BE0974561"
				type: "xp"
				xp: 100
			}]
			subtitle: "伐木蜂 + 牧场蜂"
			tasks: [{
				id: "212BB84583FE4B8B"
				title: "&a农夫蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a农夫蜜蜂&f"
			x: 2.0d
			y: 16.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "tombstone:grave_dust"
			id: "4351DAA8B607BCBB"
			rewards: [{
				id: "3E53E43ACB7E4CB8"
				type: "xp"
				xp: 100
			}]
			subtitle: "幽灵蜜蜂 + 骸骨/僵尸蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:grave"
						}
					}
				}
				id: "6D15CFE8E1CF6D25"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:grave"
							}
						}
					}
				}
				title: "坟墓蜜脾"
				type: "item"
			}]
			title: "&a坟墓蜜蜂&f"
			x: 0.0d
			y: 15.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:invar_ingot"
			id: "734A72A4C898BDFB"
			rewards: [{
				id: "624B0D86B65B6DF9"
				type: "xp"
				xp: 100
			}]
			subtitle: "铁蜜蜂 + 镍蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:invar"
						}
					}
				}
				id: "0C0D6A9D01FED776"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:invar"
							}
						}
					}
				}
				title: "&a殷钢蜜脾&f"
				type: "item"
			}]
			title: "&a殷钢蜜蜂&f"
			x: -1.0d
			y: 13.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:oak_log"
			id: "37045B986619A03D"
			rewards: [{
				id: "32F2905E46B0A46F"
				type: "xp"
				xp: 100
			}]
			subtitle: "黄蜜蜂 + &a绿色木匠蜜蜂&f"
			tasks: [{
				id: "6047113DC2263E46"
				title: "&a木场蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a木场蜜蜂&f"
			x: 3.0d
			y: 15.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:lumium_ingot"
			id: "150FB369BEFFCBBF"
			rewards: [{
				id: "76A118868DEFF156"
				type: "xp"
				xp: 100
			}]
			subtitle: "银蜜蜂 + 锡蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:lumium"
						}
					}
				}
				id: "4DA6445DB5F3B85E"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:lumium"
							}
						}
					}
				}
				title: "&a流明蜜脾&f"
				type: "item"
			}]
			title: "&a流明蜜蜂&f"
			x: 3.0d
			y: 12.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "integrateddynamics:crystalized_menril_chunk"
			id: "7B40A9DAA119DE59"
			rewards: [{
				id: "1FD5CBE1FB0136F8"
				type: "xp"
				xp: 100
			}]
			subtitle: "水晶蜜蜂 + 霓虹杜鹃蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:menril"
						}
					}
				}
				id: "22F5DD43B8A7C452"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:menril"
							}
						}
					}
				}
				title: "&a门瑞欧蜜脾&f"
				type: "item"
			}]
			title: "&a霓虹杜鹃蜂&f"
			x: 3.0d
			y: 16.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:nickel_ingot"
			id: "4FE7F45C72EF5DC1"
			rewards: [{
				id: "1822D65E1764B5C3"
				type: "xp"
				xp: 100
			}]
			subtitle: "铁蜜蜂 + 汗蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:nickel"
						}
					}
				}
				id: "05E237133AC3F46B"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:nickel"
							}
						}
					}
				}
				title: "&a镍蜜脾&f"
				type: "item"
			}]
			title: "&a镍蜜蜂&f"
			x: 0.0d
			y: 12.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:obsidian"
			id: "60B985069E0E643E"
			rewards: [{
				id: "72ADB76DCE00FAEF"
				type: "xp"
				xp: 100
			}]
			subtitle: "岩浆蜜蜂 + 汗蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:obsidian"
						}
					}
				}
				id: "50823C029014781A"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:obsidian"
							}
						}
					}
				}
				title: "&a黑曜石蜜脾&f"
				type: "item"
			}]
			title: "&a黑曜石蜜蜂&f"
			x: 1.0d
			y: 16.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:osmium_ingot"
			id: "67482ED4B18F828D"
			rewards: [{
				id: "30C7803BBB6B7F56"
				type: "xp"
				xp: 100
			}]
			subtitle: "铁蜜蜂 + 霓虹杜鹃蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:osmium"
						}
					}
				}
				id: "4471A530B55D4140"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:osmium"
							}
						}
					}
				}
				title: "&a锇蜜脾&f"
				type: "item"
			}]
			title: "&a锇蜜蜂&f"
			x: 3.0d
			y: 11.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:stone"
			id: "6E093D16B12E12B3"
			rewards: [{
				id: "5865B75ED379363E"
				type: "xp"
				xp: 100
			}]
			subtitle: "巧克力矿工蜂 + 挖掘蜂"
			tasks: [{
				id: "471F062B01D0DDA1"
				title: "&a石料蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a石料蜜蜂&f"
			x: 0.0d
			y: 16.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:uranium_ingot"
			id: "734F61A03FFA13ED"
			rewards: [{
				id: "0E772C4611E7B761"
				type: "xp"
				xp: 100
			}]
			subtitle: "苦力怕蜂 + 铁蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:radioactive"
						}
					}
				}
				id: "5C358DFF9CD0D1D9"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:radioactive"
							}
						}
					}
				}
				title: "&a辐射蜜脾&f"
				type: "item"
			}]
			title: "&a辐射蜜蜂&f"
			x: 2.0d
			y: 12.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:egg"
			id: "3D7480E4F9063E93"
			rewards: [{
				id: "4548200E1DB36178"
				type: "xp"
				xp: 100
			}]
			subtitle: "伐木蜂 + 汗蜂"
			tasks: [{
				id: "56E50AF4DB0B9F30"
				item: "productivebees:honeycomb_milky"
				type: "item"
			}]
			title: "&a牧场蜜蜂&f"
			x: 3.0d
			y: 14.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:signalum_ingot"
			id: "6F7AC41B703028CC"
			rewards: [{
				id: "24B5AA1306FA8DAA"
				type: "xp"
				xp: 100
			}]
			subtitle: "银蜜蜂 + 铜蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:signalum"
						}
					}
				}
				id: "5B5DBA0A7644A551"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:signalum"
							}
						}
					}
				}
				title: "&a信素蜜脾&f"
				type: "item"
			}]
			title: "&a信息素蜜蜂&f"
			x: 3.0d
			y: 13.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:string"
			id: "5CF2A069A3CC4EF6"
			rewards: [{
				id: "4AE2620E06B52BBA"
				type: "xp"
				xp: 100
			}]
			subtitle: "树脂蜜蜂 + 芦苇蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:silky"
						}
					}
				}
				id: "5DE213018E8C64A7"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:silky"
							}
						}
					}
				}
				title: "&a丝滑蜜脾&f"
				type: "item"
			}]
			title: "&a丝绢蜜蜂&f"
			x: -1.0d
			y: 16.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:silver_ingot"
			id: "09223F02621781BF"
			rewards: [{
				id: "7DA4E88B8BB15693"
				type: "xp"
				xp: 100
			}]
			subtitle: "铁蜜蜂 + 石匠蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:silver"
						}
					}
				}
				id: "47BAD4AA76F9CF82"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:silver"
							}
						}
					}
				}
				title: "&a银蜜脾&f"
				type: "item"
			}]
			title: "&a银蜜蜂&f"
			x: 1.0d
			y: 11.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:steel_ingot"
			id: "02F3133A9006BC1E"
			rewards: [{
				id: "0FC5BC7A7E459E87"
				type: "xp"
				xp: 100
			}]
			subtitle: "铁蜜蜂 + 煤炭蜜蜂"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:steel"
						}
					}
				}
				id: "01F96E4C1D881AD1"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:steel"
							}
						}
					}
				}
				title: "&a钢蜜脾&f"
				type: "item"
			}]
			title: "&a钢蜜蜂&f"
			x: 2.0d
			y: 15.0d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:soul_sand"
			id: "5F080CFA1DC7F435"
			rewards: [{
				id: "340488322E26DDE0"
				type: "xp"
				xp: 100
			}]
			subtitle: "在&a下界&f使用&a灵魂沙蜂巢&f生成"
			tasks: [{
				id: "281AD60CC0AB054F"
				item: "productivebees:honeycomb_ghostly"
				type: "item"
			}]
			title: "&a恶魂蜜蜂&f"
			x: -3.0d
			y: 16.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:magma_block"
			id: "614937A2F5823F3E"
			rewards: [{
				id: "14454A91BA2CAE69"
				type: "xp"
				xp: 100
			}]
			subtitle: "在&a下界&f使用&a下界砖蜂巢&f生成"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:magmatic"
						}
					}
				}
				id: "3202568944BCBF77"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:magmatic"
							}
						}
					}
				}
				title: "&a熔岩蜜脾&f"
				type: "item"
			}]
			title: "&a熔岩蜜蜂&f"
			x: -5.0d
			y: 11.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: [
				"想骑蜜蜂飞行吗？"
				""
				"&a大黄蜂蜜蜂&f会自然生成于主世界,可作为坐骑!"
				""
				"制作&e&a蜂蜜钓竿&f&r,给&a大黄蜂蜜蜂&f装上鞍,即可翱翔天际!"
			]
			hide_dependency_lines: true
			icon: "minecraft:saddle"
			id: "7941938014E97A30"
			rewards: [{
				id: "10E2A71BE96232EF"
				type: "xp"
				xp: 100
			}]
			subtitle: "从主世界的&a大黄蜂蜂巢&f生成"
			tasks: [
				{
					id: "4ADD8F6F75D38DE6"
					item: {
						Count: 1
						id: "productivebees:treat_on_a_stick"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "31D0730D68948E3B"
					item: "minecraft:saddle"
					type: "item"
				}
			]
			title: "&a大黄蜂蜜蜂&f"
			x: -5.0d
			y: 14.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			hide_dependency_lines: true
			icon: "minecraft:gravel"
			id: "1C474B46AECCCFE9"
			rewards: [{
				id: "3202C04BE7D817FE"
				type: "xp"
				xp: 10
			}]
			subtitle: "使用沙砾或&a石头蜂巢&f生成"
			tasks: [{
				id: "6E0961A56194F0A8"
				title: "&a挖掘者蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a挖掘者蜜蜂&f"
			x: -3.0d
			y: 11.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:amethyst_cluster"
			id: "35EAB77C195E594E"
			rewards: [{
				id: "712F1D5B6F5C3E3B"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a紫水晶&f喂养&a钻石蜜蜂&f"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:amethyst"
						}
					}
				}
				id: "0F2BCC279B5731AB"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:amethyst"
							}
						}
					}
				}
				title: "紫水晶蜜脾"
				type: "item"
			}]
			title: "&a紫水晶蜜蜂&f"
			x: 6.0d
			y: 11.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:brown_mushroom"
			id: "01F7F3589EBD8872"
			rewards: [{
				id: "52ABD90406552164"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a棕色蘑菇&f喂养蘑菇蜜蜂!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:brown_shroom"
						}
					}
				}
				id: "30FE30A7FA067459"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:brown_shroom"
							}
						}
					}
				}
				title: "&a棕色蘑菇蜜蜂&f Comb"
				type: "item"
			}]
			title: "&a棕色蘑菇蜜蜂&f"
			x: 7.0d
			y: 11.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [
				"会拾取物品并带回蜂巢."
				""
				"不如&a囤积者蜜蜂&f高效."
			]
			hide_dependency_lines: true
			icon: "minecraft:hopper"
			id: "094D28B7A0170039"
			rewards: [{
				id: "1218C1901D969C1D"
				type: "xp"
				xp: 10
			}]
			subtitle: "用漏斗喂养普通蜜蜂!"
			tasks: [{
				id: "632F557D97247215"
				title: "&a收集者蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a收集者蜜蜂&f"
			x: 6.0d
			y: 17.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:creeper_head"
			id: "18F948FF9FE015FB"
			rewards: [{
				id: "4C709117DECA0969"
				type: "xp"
				xp: 100
			}]
			subtitle: "用TNT喂养普通蜜蜂!"
			tasks: [{
				id: "4E787C04DB76613F"
				item: "productivebees:honeycomb_powdery"
				type: "item"
			}]
			title: "苦力怕蜜蜂"
			x: 5.0d
			y: 11.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: [""]
			hide_dependency_lines: true
			icon: "minecraft:crimson_fungus"
			id: "176ACC61DC206E55"
			rewards: [{
				id: "138D9A13CD9CBC15"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a绯红菌&f喂养蘑菇蜜蜂!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:crimson"
						}
					}
				}
				id: "3B35F86B42989063"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:crimson"
							}
						}
					}
				}
				title: "&a绯红菌蜜蜂&f Comb"
				type: "item"
			}]
			title: "&a绯红菌蜜蜂&f"
			x: 7.0d
			y: 12.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "ae2:fluix_crystal"
			id: "33422FBDAE11AE82"
			rewards: [{
				id: "24B5A6BA1F9F31BB"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a末影珍珠&f喂养&a空间蜜蜂&f!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:fluix"
						}
					}
				}
				id: "669BC6911F43DB26"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:fluix"
							}
						}
					}
				}
				title: "荧石蜜脾"
				type: "item"
			}]
			title: "&a末影蜜蜂&f"
			x: 5.0d
			y: 15.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:ice"
			id: "244ACD7024566001"
			rewards: [{
				id: "3AA4DE5C8F5D0955"
				type: "xp"
				xp: 100
			}]
			subtitle: "用冰喂养&a汗蜜蜂&f!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:frosty"
						}
					}
				}
				id: "7CD2C8E1A5F4B3D2"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:frosty"
							}
						}
					}
				}
				title: "&a霜冻蜜脾&f"
				type: "item"
			}]
			title: "&a霜冻蜜蜂&f"
			x: 7.0d
			y: 13.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			description: ["会收集地面物品并带回蜂巢."]
			hide_dependency_lines: true
			icon: "minecraft:shulker_box"
			id: "309059DEDEE7AECD"
			rewards: [{
				id: "438D89082142A748"
				type: "xp"
				xp: 100
			}]
			subtitle: "用潜影壳喂养&a收集者蜜蜂&f!"
			tasks: [{
				id: "0F45E8D41C62BE78"
				title: "&a囤积者蜜蜂&f"
				type: "checkmark"
			}]
			title: "&a囤积者蜜蜂&f"
			x: 5.0d
			y: 16.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:peridot"
			id: "761D21C6FBD13EE1"
			rewards: [{
				id: "75FB934FE110A59C"
				type: "xp"
				xp: 100
			}]
			subtitle: "用橄榄石喂养&a钻石蜜蜂&f!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:peridot"
						}
					}
				}
				id: "3DE2AD0FFF364185"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:peridot"
							}
						}
					}
				}
				title: "橄榄石蜜脾"
				type: "item"
			}]
			title: "&a橄榄石蜜蜂&f"
			x: 5.0d
			y: 12.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "mysticalagriculture:prosperity_shard"
			id: "55882C10292B6A3D"
			rewards: [{
				id: "677634D163F8DDDB"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a活化水晶块&f喂养&a石英蜜蜂&f!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:prosperity"
						}
					}
				}
				id: "544D288D9F81C6D8"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:prosperity"
							}
						}
					}
				}
				title: "&a活化水晶蜜脾&f"
				type: "item"
			}]
			title: "繁荣蜜蜂"
			x: 6.0d
			y: 14.0d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:red_mushroom"
			id: "37CD9BF281903F56"
			rewards: [{
				id: "7AB48FA4789D87C9"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a红色蘑菇&f喂养蘑菇蜜蜂!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:red_shroom"
						}
					}
				}
				id: "39899761508BFACE"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:red_shroom"
							}
						}
					}
				}
				title: "&a红色蘑菇蜜蜂&f Comb"
				type: "item"
			}]
			title: "&a红色蘑菇蜜蜂&f"
			x: 7.0d
			y: 14.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:ruby"
			id: "35E8F1CC0080E45E"
			rewards: [{
				id: "240612DCF75988AC"
				type: "xp"
				xp: 100
			}]
			subtitle: "用红宝石喂养&a钻石蜜蜂&f!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:ruby"
						}
					}
				}
				id: "2EA19C4E46380CDA"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:ruby"
							}
						}
					}
				}
				title: "红宝石蜜脾"
				type: "item"
			}]
			title: "红宝石蜜蜂"
			x: 5.0d
			y: 13.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "alltheores:sapphire"
			id: "3AF30E1EC163E2E3"
			rewards: [{
				id: "6DBD9CDC28903F43"
				type: "xp"
				xp: 100
			}]
			subtitle: "用蓝宝石喂养&a钻石蜜蜂&f!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:sapphire"
						}
					}
				}
				id: "46C7D666D3A4A3D9"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:sapphire"
							}
						}
					}
				}
				title: "蓝宝石蜜脾"
				type: "item"
			}]
			title: "蓝宝石蜜蜂"
			x: 5.0d
			y: 14.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "mysticalagriculture:soulium_ingot"
			id: "1EBD5E4410A6DF34"
			rewards: [{
				id: "7DA224232096A3B0"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a灵魂匕首&f喂养&a恶魂蜜蜂&f!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:soulium"
						}
					}
				}
				id: "70E72633E49DFF88"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:soulium"
							}
						}
					}
				}
				title: "&a灵魂蜜脾&f"
				type: "item"
			}]
			title: "&a灵魂蜜蜂&f"
			x: 7.0d
			y: 16.5d
		}
		{
			dependencies: ["17419401147B5C02"]
			hide_dependency_lines: true
			icon: "minecraft:warped_fungus"
			id: "2982D38BD5EE6349"
			rewards: [{
				id: "1D2DDC683FE6A9C3"
				type: "xp"
				xp: 100
			}]
			subtitle: "用&a诡异菌&f喂养蘑菇蜜蜂!"
			tasks: [{
				icon: {
					Count: 1
					id: "productivebees:configurable_honeycomb"
					tag: {
						EntityTag: {
							type: "productivebees:warped"
						}
					}
				}
				id: "094F0FCF9A64EE00"
				item: {
					Count: 1
					id: "itemfilters:strong_nbt"
					tag: {
						value: {
							EntityTag: {
								type: "productivebees:warped"
							}
						}
					}
				}
				title: "诡异菌蜜脾"
				type: "item"
			}]
			title: "&a诡异菌蜜蜂&f"
			x: 7.0d
			y: 15.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: [
				"除了用蜂巢吸引蜜蜂外,大多数蜜蜂需要通过&e繁殖&r或&9转化&r来获得.(没错,这不是拼写错误)"
				""
				"&e&a蜜蜂繁殖&f&r需要两只蜜蜂和特定喂养物品使其交配."
				""
				"&9&a蜜蜂转化&f&r需要给蜜蜂喂食特定物品将其转化为新品种."
			]
			id: "17419401147B5C02"
			rewards: [{
				id: "1836D4B5770F396C"
				type: "xp"
				xp: 10
			}]
			subtitle: "&a翔空&f与&a蜜蜂&f"
			tasks: [{
				id: "2153473228DA4678"
				title: "&a蜜蜂繁殖&f"
				type: "checkmark"
			}]
			title: "蜜蜂繁殖与转化"
			x: 1.0d
			y: 7.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: [
				"若想将&a蜜蜂农场&f变成资源工厂,首先需要用&e蜂巢&r生成蜜蜂."
				""
				"蜂巢必须放置在特定生物群系(可在JEI中查找顶部'I'图标查看)."
				""
				"用&9&a蜜蜂小食&f&r&a右击&f蜂巢可吸引蜜蜂.没有小食蜂巢无法运作!!"
				""
				"&9重要提示&r:某些蜜蜂仅栖息于蜂巢,不会进入高级蜂箱.若未产出蜂巢,它们必须住在蜂巢中."
			]
			id: "120BDCB70AD352AC"
			rewards: [{
				id: "0B45A4897BE75592"
				type: "xp"
				xp: 10
			}]
			subtitle: "需要&a蜜蜂小食&f"
			tasks: [{
				id: "359396879CBA62D5"
				title: "蜂巢生成"
				type: "checkmark"
			}]
			x: -2.0d
			y: 4.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: ["扩展箱可放置于高级蜂箱顶部,将蜂箱容量提升至最多5个蜜蜂槽位."]
			id: "47CEFA06392C4211"
			rewards: [
				{
					count: 2
					id: "4C6D0AFFA31E7912"
					item: "minecraft:honeycomb"
					random_bonus: 2
					type: "item"
				}
				{
					id: "1824DE857066CD43"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "扩展蜜蜂存储空间"
			tasks: [{
				icon: "productivebees:expansion_box_oak"
				id: "5860D1574D18BA1A"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "productivebees:expansion_boxes"
					}
				}
				title: "扩展箱"
				type: "item"
			}]
			x: 2.0d
			y: 1.5d
		}
		{
			dependencies: ["131EC039435B8878"]
			description: [
				"获取ATM蜜蜂需要大量捕捉、繁殖和喂养工作."
				""
				"任务线后续部分会列出所需蜜蜂品种."
				""
				"请务必查看JEI和&9蜜蜂大百科&r以&a了解更多信息&f!"
			]
			icon: "allthemodium:allthemodium_ore"
			id: "4C0302FF4F63B52E"
			rewards: [{
				id: "22627B27D2839167"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6754D9E162472CA1"
				title: "全金属进程"
				type: "checkmark"
			}]
			x: 4.0d
			y: 4.5d
		}
		{
			dependencies: ["29EE878BC8D3A742"]
			description: [
				"可在初始获得的奇异典籍中找到,若遗失可重新合成!"
				""
				"本指南将助你掌握所有蜜蜂知识."
			]
			id: "6C9E88A61ECCF898"
			optional: true
			rewards: [{
				id: "1471AEAB1BD9E5DB"
				type: "xp"
				xp: 100
			}]
			subtitle: "&a蜜蜂&f手册"
			tasks: [{
				id: "7CB163461B2D1723"
				item: {
					Count: 1
					id: "patchouli:guide_book"
					tag: {
						"patchouli:book": "productivebees:guide"
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: -2.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: ["收集基因时会获得特质百分比.可在工作台组合叠加,或放入&a基因检索器&f自动合并."]
			hide_dependency_lines: true
			id: "419DD6FE84B91749"
			rewards: [
				{
					id: "5C1387EEB2AC5D4E"
					type: "xp"
					xp: 10
				}
				{
					count: 2
					id: "5E255C49AD125390"
					item: "minecraft:honeycomb"
					type: "item"
				}
			]
			subtitle: "&a基因&f组合器与存储箱"
			tasks: [{
				id: "2E829A75260DFF75"
				item: "productivebees:gene_indexer"
				type: "item"
			}]
			x: 4.0d
			y: 0.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: [
				"&a装瓶机&f有两种功能:封装蜂蜜与挤压蜜蜂获取基因."
				""
				"获取蜜蜂基因时,需在装瓶机上方间隔一格处放置活塞."
				""
				"将蜜蜂置于装瓶机顶部,激活活塞即可挤压出基因瓶,后续可放入离心机处理."
				""
				"注意:确保装瓶机内已放置空瓶."
				"{image:atm:textures/questpics/bees/bottler.png width:100 height:150 align:1}"
			]
			hide_dependency_lines: true
			id: "6F978C4D561F35EC"
			rewards: [
				{
					count: 2
					id: "58ED69D187E94890"
					item: "minecraft:honeycomb"
					type: "item"
				}
				{
					id: "08B9B9C77F1239AD"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "2560F92C8A497C16"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			subtitle: "蜂蜜我压榨了&a蜜蜂&f"
			tasks: [{
				id: "531FB6E56B95C1CA"
				item: "productivebees:bottler"
				type: "item"
			}]
			x: 5.0d
			y: 0.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: [
				"用于捕捉周围飞行的蜜蜂."
				""
				"你可以使用过滤器升级来筛选想要捕捉的蜜蜂种类,或者使用幼蜂升级专门捕捉幼蜂."
			]
			hide_dependency_lines: true
			id: "7B5A0BFD47D96BDE"
			rewards: [
				{
					count: 2
					id: "5A5DC2058B09B6F5"
					item: "minecraft:honeycomb"
					type: "item"
				}
				{
					id: "6BB1ACB9F4FF3532"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "蜜蜂捕捉器"
			tasks: [{
				id: "4FCB8A72367ADF6E"
				item: "productivebees:catcher"
				type: "item"
			}]
			x: 6.0d
			y: 0.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: [
				"可加速幼蜂的&a生长速度&f,或生成新的刷怪蛋."
				""
				"将笼装幼蜂与20个&a蜜蜂小食&f放入即可使其成年."
				""
				"制作刷怪蛋需将目标蜜蜂基因与蜂蜜小食结合,然后在此机器中用鸡蛋生成新刷怪蛋."
				""
				"基因最高可合成100%%纯度,此时鸡蛋转化成功率将达到100%%."
			]
			hide_dependency_lines: true
			id: "3E301F3EEC8C763A"
			rewards: [
				{
					count: 2
					id: "2855225AAB5CB15B"
					item: "minecraft:honeycomb"
					type: "item"
				}
				{
					id: "4E9C7BCD64A5B238"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "幼蜂培育所"
			tasks: [{
				id: "3D89286F2B7BF9FB"
				item: "productivebees:incubator"
				type: "item"
			}]
			x: 5.0d
			y: -0.5d
		}
		{
			dependencies: ["7C169A4A39F37FAC"]
			description: [
				"为你的蜜蜂骄傲吗？"
				""
				"想将它们装进罐子展示吗？"
				""
				"放置&a蜜蜂罐&f后,用管道或漏斗将笼装蜜蜂导入罐中."
			]
			hide_dependency_lines: true
			id: "32FDC5FFA000DAF8"
			rewards: [
				{
					count: 2
					id: "23284D6510C4B17A"
					item: "minecraft:honeycomb"
					type: "item"
				}
				{
					id: "3E37E54642DD9ABE"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "蜜蜂展示"
			tasks: [{
				id: "4DA861A4D91D15D5"
				item: "productivebees:jar_oak"
				type: "item"
			}]
			x: 5.0d
			y: 1.5d
		}
		{
			dependencies: ["120BDCB70AD352AC"]
			description: ["该蜂巢需用&a金锭堆&f(而非&a蜜蜂小食&f)吸引蜜蜂."]
			hide_dependency_lines: true
			id: "160BD0185954C891"
			rewards: [{
				id: "76C1F3990EFECFDB"
				type: "xp"
				xp: 100
			}]
			subtitle: "置于&a下界&f时可引诱&a黄金蜜蜂&f"
			tasks: [{
				id: "4EE84594A8A996AF"
				item: "productivebees:nether_gold_nest"
				type: "item"
			}]
			x: -9.0d
			y: 4.5d
		}
		{
			dependencies: ["6DBF9CAB37B9BBF3"]
			id: "01A0612C516B4F7F"
			rewards: [
				{
					id: "78311D0F7C60994E"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "53466516293198D5"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				id: "123810EB2AEB0EAB"
				item: "productivebees:upgrade_productivity_2"
				type: "item"
			}]
			x: -6.0d
			y: 0.5d
		}
		{
			dependencies: ["01A0612C516B4F7F"]
			id: "58ACADCBA57BC1DB"
			rewards: [
				{
					id: "0156FA9A01DFAC03"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "7DFAC9BC1DF0F99D"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			tasks: [{
				id: "0F962A0E762836D4"
				item: "productivebees:upgrade_productivity_3"
				type: "item"
			}]
			x: -7.0d
			y: 0.5d
		}
		{
			dependencies: ["58ACADCBA57BC1DB"]
			id: "4DDF647FE6494DE1"
			rewards: [
				{
					id: "78E6295E2CD504BD"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "7C1C2FCB825216F2"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			tasks: [{
				id: "3FD59559C494AE6B"
				item: "productivebees:upgrade_productivity_4"
				type: "item"
			}]
			x: -8.0d
			y: 0.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经明确授权不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务已隐藏,若你看到此提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "23ABDBFC4123E6DC"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "7B7D16D9A49B8308"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "012F21258836AF43"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 1.0d
			y: -7.5d
		}
	]
	title: "&d资源蜜蜂&f"
}
