{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "insane_voltage"
	group: "1DA67E79B40AB130"
	icon: "gtceu:micro_processor_mainframe"
	id: "00E59A3B38CB7EEA"
	order_index: 6
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["当时我正深陷莎莎酱中,满身机油\\n\\n总之,希望你准备好处理大量流体工作了!"]
			id: "64F77D41B2D057B8"
			rewards: [
				{
					count: 16
					id: "4A52F07AC21B87A7"
					item: "gtceu:ram_chip"
					random_bonus: 16
					type: "item"
				}
				{
					count: 3
					id: "1113B413B2540E61"
					item: "gtceu:smd_diode"
					random_bonus: 3
					type: "item"
				}
				{
					count: 3
					id: "437C2DC8C857BE2B"
					item: "gtceu:smd_inductor"
					random_bonus: 3
					type: "item"
				}
			]
			size: 1.5d
			subtitle: "是时候深入探索了"
			tasks: [{
				id: "6625077A99326B9D"
				item: "gtceu:micro_processor_mainframe"
				type: "item"
			}]
			x: -7.0d
			y: 3.0d
		}
		{
			dependencies: [
				"4993431DBE076E1B"
				"239E32216382AA5D"
				"1809493D8765E67A"
				"2CC2E23077A0509F"
				"46542F4A273E64EB"
				"12D84337AEB56002"
				"1896B90F5FD66AFA"
			]
			description: [
				"HV电路板的终极形态,但还不是最具成本效益的——目前如此"
				""
				"现阶段你将用这些来制造更高级的电路板,直至LuV级!"
			]
			id: "2BE754C8D2C0C76E"
			rewards: [{
				exclude_from_claim_all: true
				id: "27B451F27389D57B"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "23FD670B4CA5374A"
				item: "gtceu:nano_processor"
				type: "item"
			}]
			x: 4.0d
			y: 0.7999999999999954d
		}
		{
			dependencies: [
				"4993431DBE076E1B"
				"2BE754C8D2C0C76E"
				"239E32216382AA5D"
				"574019B5B7CA43E0"
			]
			description: ["更便宜的EV电路板!别忘了更新你的合成配方!"]
			id: "19CD3E69746F2849"
			rewards: [{
				exclude_from_claim_all: true
				id: "0A8846449D3DD62C"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "1B983DA0344AF5F1"
				item: "gtceu:nano_processor_assembly"
				type: "item"
			}]
			x: 7.0d
			y: 0.7999999999999954d
		}
		{
			dependencies: [
				"4993431DBE076E1B"
				"19CD3E69746F2849"
				"1A73520CB284217F"
				"15A1D6D05A785919"
			]
			description: ["更简易的IV电路板,但为何止步于此!继续前进,你就能获得梦寐以求的疯狂电压电路板!"]
			id: "592113082337004B"
			rewards: [{
				exclude_from_claim_all: true
				id: "5ADAACCEC401ECAA"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "5C457C02EC77E036"
				item: "gtceu:nano_processor_computer"
				type: "item"
			}]
			x: 10.0d
			y: 0.7999999999999954d
		}
		{
			dependencies: [
				"592113082337004B"
				"4993431DBE076E1B"
			]
			description: ["终于,&dLuV&r时代到来了——恭喜!"]
			id: "2ACB94B77EF072EB"
			rewards: [{
				exclude_from_claim_all: true
				id: "1CD88B51F862BAF8"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "7351298A63054CA3"
				item: "gtceu:nano_processor_mainframe"
				type: "item"
			}]
			x: 9.999999999999998d
			y: 3.0d
		}
		{
			dependencies: [
				"64F77D41B2D057B8"
				"692C9BA71EA0F0A7"
				"540B4CE165EE4D5B"
			]
			description: [
				"终于获得钨钢,现在可以制造IV级机器了!"
				""
				"这还能为电力高炉升级线圈!"
			]
			id: "12905D5778274DEE"
			rewards: [{
				count: 8
				id: "042553192AA896F7"
				item: "gtceu:tungsten_steel_ingot"
				random_bonus: 8
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "2FDC7161CD796F5B"
				item: "gtceu:tungsten_steel_ingot"
				type: "item"
			}]
			x: -7.0d
			y: 0.8000000000000003d
		}
		{
			dependencies: ["540B4CE165EE4D5B"]
			description: ["普通钨有什么用？既然你问了,它是许多IV级机器的关键组件,主要以钨电缆的形式存在"]
			icon: "gtceu:tungsten_ingot"
			id: "08D48D7C2C6EEF54"
			rewards: [{
				count: 8
				id: "219B073AAA2814B2"
				item: "gtceu:tungsten_ingot"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				icon: "gtceu:tungsten_ingot"
				id: "36397CBA6D39DC0D"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:ingots/tungsten"
					}
				}
				title: "&a钨锭&f"
				type: "item"
			}]
			x: -9.67142857142858d
			y: 2.3392857142857153d
		}
		{
			dependencies: [
				"533DB1666B11489A"
				"12905D5778274DEE"
				"1FF8B0E2D10C88E9"
			]
			description: ["在&e&a电力高炉&f&r上安装16个这种线圈,就能熔炼高达&c4500开尔文&r的配方!相当于4227摄氏度或7640华氏度!"]
			id: "0A848E0B9F485B2C"
			rewards: [
				{
					count: 6
					id: "393AD63D08C4587C"
					item: "gtceu:molybdenum_dust"
					random_bonus: 6
					type: "item"
				}
				{
					count: 8
					id: "2561A6499CDB6E06"
					item: "gtceu:ruthenium_dust"
					random_bonus: 8
					type: "item"
				}
				{
					count: 8
					id: "0BE7E36683619E3D"
					item: "gtceu:chromium_dust"
					random_bonus: 8
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "2212F4C424085656"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			tasks: [{
				id: "0DABFD3310034563"
				item: "gtceu:rtm_alloy_coil_block"
				type: "item"
			}]
			x: -2.5d
			y: 0.7999999999999999d
		}
		{
			dependencies: [
				"4AE3A2326EA07B7A"
				"74D47A8DF93294E4"
			]
			description: [
				"聚苯并咪唑是一种以耐热性著称的塑料"
				""
				"随着进度推进你会将其用于多种用途,特别是在ZPM及以上等级时,但主要用途是制造高级电路元件!"
			]
			hide_dependent_lines: true
			id: "1D9194E89D14BA85"
			rewards: [
				{
					id: "7454CBA7D956781A"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 16000
								FluidName: "gtceu:polybenzimidazole"
								capacity: 16000
							}
						}
						id: "evilcraft:dark_tank"
						tag: {
							Fluid: {
								Amount: 16000
								FluidName: "gtceu:polybenzimidazole"
							}
							capacity: 16000
						}
					}
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "765B73430C8BD292"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			shape: "gear"
			size: 1.25d
			subtitle: "终于获得PBI"
			tasks: [{
				id: "02B34C5571D15E05"
				item: "gtceu:polybenzimidazole_bucket"
				type: "item"
			}]
			x: -0.5d
			y: -4.200000000000005d
		}
		{
			dependencies: [
				"64F77D41B2D057B8"
				"587B4048E0BCEB27"
			]
			description: [
				"EV级&a电路组装机&f,这是制造更高级电路板并进一步降低低级电路板成本的关键一步!"
				""
				"在达到&3纳米处理器&r级电路板前,你还需要处理不少流体工作"
			]
			id: "4993431DBE076E1B"
			rewards: [{
				exclude_from_claim_all: true
				id: "241A825C518CA7D1"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "52D651790115A82E"
				item: "gtceu:ev_circuit_assembler"
				type: "item"
			}]
			x: 7.0d
			y: 3.0d
		}
		{
			dependencies: [
				"41EE8B40BA43DADE"
				"1D9194E89D14BA85"
				"6F0AD70D3748D8D2"
				"45D9E32D75F5ACAE"
			]
			description: ["别忘了,制造这些后就可以更新那些使用普通晶体管的旧电路配方,改用高级晶体管"]
			id: "1809493D8765E67A"
			rewards: [
				{
					count: 4
					id: "092E25B584C9B2A5"
					item: "gtceu:hssg_dust"
					random_bonus: 4
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "4C5E99E3A5CF135D"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			tasks: [{
				id: "4AD1E09D72AD01E3"
				item: "gtceu:advanced_smd_transistor"
				type: "item"
			}]
			x: 5.5d
			y: -1.5d
		}
		{
			dependencies: [
				"1D9194E89D14BA85"
				"45D9E32D75F5ACAE"
			]
			description: [
				"以前你一次能制造32个&a贴片电阻&f,现在降为16个高级&a贴片电阻&f"
				""
				"这真的是提升吗？是的!主要是因为高级&a贴片电阻&f用量更少,而且普通型号终究会被淘汰..."
			]
			id: "2CC2E23077A0509F"
			rewards: [
				{
					count: 4
					id: "1559A17E6BEF564D"
					item: "gtceu:graphene_dust"
					random_bonus: 4
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "754E11897AC4D998"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			tasks: [{
				id: "3667F2D12BD686DD"
				item: "gtceu:advanced_smd_resistor"
				type: "item"
			}]
			x: 4.0d
			y: -1.5d
		}
		{
			dependencies: [
				"1D9194E89D14BA85"
				"45D9E32D75F5ACAE"
				"599BABC83E76A711"
				"1AB86FD8776634D0"
			]
			description: [
				"终于解锁了纳米处理器级的HV电路板!"
				""
				"严格来说不用高级SMD元件也能制造,但那有什么乐趣呢？"
			]
			id: "239E32216382AA5D"
			rewards: [
				{
					count: 6
					id: "261F7E2E3450A321"
					item: "gtceu:hsss_dust"
					random_bonus: 6
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "24F7EAB4EA5990C0"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			tasks: [{
				id: "38023F3A53A1FFF3"
				item: "gtceu:advanced_smd_capacitor"
				type: "item"
			}]
			x: 7.0d
			y: -1.5d
		}
		{
			dependencies: [
				"1D9194E89D14BA85"
				"7E7FAA72581D2186"
				"4AA6D19469FB306F"
				"45D9E32D75F5ACAE"
			]
			description: [
				"终于获得最后一种高级SMD元件"
				""
				"务必在后续配方中使用高级SMD元件,因为成本更低"
			]
			id: "1A73520CB284217F"
			rewards: [
				{
					count: 4
					id: "4A67FAA8B49E8FDF"
					item: "gtceu:niobium_titanium_dust"
					random_bonus: 4
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "15067CF3E3F6062F"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			tasks: [{
				id: "6971A717F3A6343E"
				item: "gtceu:advanced_smd_diode"
				type: "item"
			}]
			x: 10.0d
			y: -1.5d
		}
		{
			dependencies: [
				"546F6933699B9BF3"
				"4720F9EDF894330C"
				"1D9194E89D14BA85"
				"45D9E32D75F5ACAE"
				"1AB86FD8776634D0"
			]
			description: ["高级电感器!记得持续更新那些旧配方"]
			id: "574019B5B7CA43E0"
			rewards: [
				{
					count: 8
					id: "26CA1388FAD74DE1"
					item: "gtceu:palladium_dust"
					random_bonus: 8
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "377F61DFB3FDCCD7"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			tasks: [{
				id: "227F895573C8DCA9"
				item: "gtceu:advanced_smd_inductor"
				type: "item"
			}]
			x: 8.5d
			y: -1.5d
		}
		{
			description: [
				"&d&a钯金矿石&f&r相当稀有,替代获取方法包括将&b粉碎铂金&r或&e粉碎的&a月光石&f&r放入&c水银&r中进行&e&a化学浸洗&f&r"
				""
				"水银可通过&e离心&r&a红石粉&f轻松获得"
			]
			id: "546F6933699B9BF3"
			rewards: [{
				count: 16
				id: "2990B163CA334434"
				item: "gtceu:raw_palladium"
				random_bonus: 16
				type: "item"
			}]
			shape: "square"
			tasks: [{
				icon: "gtceu:palladium_dust"
				id: "64A8B8EDC7F58D0D"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:dusts/palladium"
					}
				}
				title: "&a钯金粉&f"
				type: "item"
			}]
			x: 8.5d
			y: -5.0d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: ["仔细检查&l&n&c不要&r&r&r将&a电力高炉&r设为&b程序1&r,否则会开始把硅粉熔炼成锭"]
			id: "2CC82170BCE61940"
			rewards: [
				{
					count: 4
					id: "59836EEDB97CB0A0"
					item: "gtceu:arsenic_dust"
					random_bonus: 6
					type: "item"
				}
				{
					count: 8
					id: "123D68DC491E126E"
					item: "gtceu:gallium_dust"
					random_bonus: 8
					type: "item"
				}
			]
			tasks: [{
				id: "7CFE79284D3FFB61"
				item: "gtceu:phosphorus_boule"
				type: "item"
			}]
			x: -1.5d
			y: 4.0d
		}
		{
			dependencies: ["2CC82170BCE61940"]
			description: [
				"用这个能获得更多晶圆片并制造更高级芯片!"
				""
				"是时候升级那些旧的硅晶圆配方了？我想是的!"
			]
			id: "5F270891C953486E"
			rewards: [{
				count: 8
				id: "3487CEB26A6EE0B9"
				item: "gtceu:phosphorus_wafer"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "03BCFA5724D80E16"
				item: "gtceu:phosphorus_wafer"
				type: "item"
			}]
			x: -0.5d
			y: 4.0d
		}
		{
			dependencies: ["5F270891C953486E"]
			description: [
				"收藏品又添一枚透镜!"
				""
				"SoC代表System on Chip(片上系统)"
			]
			id: "3CA2D15D1EC4D852"
			rewards: [{
				exclude_from_claim_all: true
				id: "00E8E0CE1D800A29"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [
				{
					icon: "gtceu:hv_laser_engraver"
					id: "783B177BEB5F921E"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "gtceu:hv_laser_engraver"
								}
								{
									Count: 1b
									id: "gtceu:ev_laser_engraver"
								}
								{
									Count: 1b
									id: "gtceu:iv_laser_engraver"
								}
							]
						}
					}
					title: "高压、极压或超压激光刻录机"
					type: "item"
				}
				{
					id: "5409F09E8903E29E"
					item: "gtceu:yellow_glass_lens"
					type: "item"
				}
				{
					id: "57C79A6CB61B5A7E"
					item: "gtceu:soc_wafer"
					type: "item"
				}
				{
					id: "6AB0EDD32811CF42"
					item: "gtceu:soc"
					type: "item"
				}
			]
			title: "片上系统"
			x: 0.5d
			y: 4.0d
		}
		{
			dependencies: [
				"3CA2D15D1EC4D852"
				"4993431DBE076E1B"
			]
			description: ["终于,通过这个你实现了最便宜的低压电路——恭喜!"]
			id: "143BF8A4944C471D"
			progression_mode: "flexible"
			rewards: [{
				id: "7FE9B479172D31D9"
				type: "xp"
				xp: 100
			}]
			subtitle: "前所未有的便宜!"
			tasks: [{
				id: "12938981FF431405"
				item: "gtceu:microchip_processor"
				type: "item"
			}]
			x: 1.5d
			y: 4.0d
		}
		{
			dependencies: ["62F4355AE3E08FD9"]
			description: [
				"将&a四氟乙烯&r与少量空气或更好的&b&a氧气&f&r进行&e化学反应&r,得到最终产物&e聚四氟乙烯&r!"
				""
				"终于得到PTFE了!这将作为制造化学惰性外壳的基础材料,我们很快会用它来制作&a&a大型化学反应釜&f"
				""
				"当你拥有&aLCR&r后,还可以通过添加&c&a四氯化钛&f一次性生产更多PTFE"
			]
			id: "329916B2CB8342B2"
			rewards: [
				{
					id: "642AC2D4AF44CD23"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 16000
								FluidName: "gtceu:polytetrafluoroethylene"
								capacity: 16000
							}
						}
						id: "evilcraft:dark_tank"
						tag: {
							Fluid: {
								Amount: 16000
								FluidName: "gtceu:polytetrafluoroethylene"
							}
							capacity: 16000
						}
					}
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "226C5A86F8DC5DBB"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			shape: "gear"
			size: 1.25d
			tasks: [{
				id: "524E5BA2E040B028"
				item: "gtceu:polytetrafluoroethylene_bucket"
				type: "item"
			}]
			x: 2.0d
			y: -3.700000000000001d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: [
				"如果之前没有制作&5EV组装机&r,现在正是时候!"
				""
				"你将用它为&a电弧炉&r制作&9钨钢线圈&r"
			]
			id: "533DB1666B11489A"
			rewards: [{
				exclude_from_claim_all: true
				id: "4430812BC676F0A3"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "3EC0D7EE35B856B9"
				item: "gtceu:ev_assembler"
				type: "item"
			}]
			x: -2.5d
			y: 2.0d
		}
		{
			dependencies: ["41EE8B40BA43DADE"]
			description: [
				"你需要熔炼这个并将其压平成&b金属箔&r,用来制作构成&d高级&a贴片电容&f的精密层"
				""
				"如果难以找到&a&a铱矿石&f&r,你可以尝试培育&5&a铱蜜蜂&f&r,或者开始加工&6铂金线™&r的&e稀有金属混合物&r"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"2EE52FD7129D3D87\"}, \"text\": \"How to: Rarest Metal Mixture\", \"color\": \"yellow\", \"hoverEvent\": { \"action\":\"show_text\", \"contents\": { \"text\":\"&a点击此处&f开启任务!\" } }}"
			]
			id: "599BABC83E76A711"
			rewards: [
				{
					count: 16
					id: "138DF43E45BCBD66"
					item: "alltheores:raw_iridium"
					random_bonus: 16
					type: "item"
				}
				{
					count: 12
					id: "5C2C7A388507B5C3"
					item: "gtceu:hsss_dust"
					random_bonus: 12
					type: "item"
				}
			]
			tasks: [{
				id: "04CE037EB56DFF14"
				item: "gtceu:hsss_dust"
				type: "item"
			}]
			x: 6.199999999999999d
			y: -2.8999999999999995d
		}
		{
			dependencies: ["6517B8748E3A6831"]
			description: ["第一阶的&3高速钢&r,你会大量制作这种材料,因为它作为其他变种高速钢粉的基础"]
			id: "41EE8B40BA43DADE"
			rewards: [
				{
					count: 16
					id: "6A763543C172EE2F"
					item: "gtceu:raw_molybdenum"
					random_bonus: 16
					type: "item"
				}
				{
					count: 16
					id: "0781A410E8911398"
					item: "gtceu:tungsten_steel_dust"
					random_bonus: 16
					type: "item"
				}
			]
			tasks: [{
				id: "753FCEFC65478AE3"
				item: "gtceu:hssg_dust"
				type: "item"
			}]
			x: 5.5d
			y: -5.0d
		}
		{
			dependencies: [
				"0907800ED4793F96"
				"64F77D41B2D057B8"
			]
			description: [
				"[ \"You'll need \", { \"text\": \"Laminated Glass\", \"color\": \"green\", \"underlined\": \"true\", \"clickEvent\": { \"action\": \"change_page\", \"value\": \"0907800ED4793F96\" } }, \" to make this\" ]"
				""
				"获得&e搅拌机&r后,就可以制作一些&d高速钢&r(HSS)变体了!"
			]
			hide_dependency_lines: true
			id: "6517B8748E3A6831"
			rewards: [{
				exclude_from_claim_all: true
				id: "76037C8D92CC42FF"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "04E1415B59A1A150"
				item: "gtceu:iv_mixer"
				type: "item"
			}]
			x: 5.5d
			y: -6.5d
		}
		{
			description: [
				"&a矿石处理&r烧绿石、软锰矿和钽铁矿都会产生铌副产品"
				""
				"你也可以&e电解&r烧绿石稳定获取铌!"
				""
				"务必囤积这种材料,随着进度推进,你会大量使用其&d箔片&r和&2细导线&r形态"
			]
			id: "4AA6D19469FB306F"
			rewards: [
				{
					count: 16
					id: "0337587DBD2054B7"
					item: "gtceu:raw_tantalite"
					random_bonus: 16
					type: "item"
				}
				{
					count: 16
					id: "6C9F07642B8CB709"
					item: "gtceu:raw_pyrochlore"
					random_bonus: 16
					type: "item"
				}
			]
			tasks: [{
				id: "3C40CC44FAE84350"
				item: "gtceu:niobium_titanium_dust"
				type: "item"
			}]
			x: 11.3d
			y: -2.6000000000000014d
		}
		{
			dependencies: ["17C2D1915C2D0B4C"]
			description: [
				"在&a程序1&r上将&d铟&r、&b镓&r和&e磷&r粉末&e混合&r"
				""
				"这种材料将取代&3高级&a贴片二极管&f中的&a砷化镓&f,并广泛用于将&cMPIC晶圆&r升级到更高电压等级"
			]
			hide_dependent_lines: true
			id: "7E7FAA72581D2186"
			rewards: [{
				count: 6
				id: "2A637109039BCDE1"
				item: "gtceu:indium_gallium_phosphide_dust"
				random_bonus: 6
				type: "item"
			}]
			tasks: [{
				id: "13EC865DB73C17C9"
				item: "gtceu:indium_gallium_phosphide_dust"
				type: "item"
			}]
			x: 10.0d
			y: -2.5999999999999996d
		}
		{
			dependencies: ["677B8753B7904E95"]
			description: [
				"将&d&a铟富集溶液&f&r与&b&a铝粉&f&r进行&e化学反应&r得到&3小堆&a铟粉&f"
				""
				"然后你可以手动将4份合成1个&3&a铟粉&f&r,或用&e打包机&r在&a程序1&r上自动化生产"
			]
			id: "17C2D1915C2D0B4C"
			rewards: [{
				count: 4
				id: "69609DC8E28B6F94"
				item: "gtceu:indium_dust"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "2DF2279A961A2744"
				item: "gtceu:indium_dust"
				type: "item"
			}]
			x: 10.0d
			y: -3.6999999999999993d
		}
		{
			dependencies: ["41EE8B40BA43DADE"]
			description: [
				"老实说我不知道E、G或S字母的含义...但这仍然是高速钢!"
				""
				"你需要熔炼它并制成&b环&r用于&d高级SMD电感"
			]
			id: "4720F9EDF894330C"
			rewards: [{
				count: 12
				id: "51FF1B9CA03B0275"
				item: "gtceu:hsse_dust"
				random_bonus: 12
				type: "item"
			}]
			tasks: [{
				id: "4F79A05731E4FE40"
				item: "gtceu:hsse_dust"
				type: "item"
			}]
			x: 7.0d
			y: -5.0d
		}
		{
			description: [
				"将&b&a纯化&f闪锌矿&r和&5&a纯化&f方铅矿&r与&e&a硫酸&f&r&e混合&r,就能获得最珍贵资源之一的起点——&d&a铟富集溶液&f"
				""
				"这东西重要到值得专门建立一套&a矿石处理&r系统"
			]
			id: "677B8753B7904E95"
			rewards: [
				{
					count: 16
					id: "5F954A0EC859F3A3"
					item: "gtceu:raw_sphalerite"
					random_bonus: 16
					type: "item"
				}
				{
					count: 16
					id: "3DD5E0AACAFE6DDA"
					item: "gtceu:raw_galena"
					random_bonus: 16
					type: "item"
				}
			]
			shape: "square"
			tasks: [{
				id: "50AC2C6F8B887369"
				item: "gtceu:indium_concentrate_bucket"
				type: "item"
			}]
			x: 10.0d
			y: -5.0d
		}
		{
			dependencies: [
				"07E750F8184C4362"
				"1C19DD6B3E34012E"
			]
			description: ["老规矩,我们将&3&a氢氟酸&f&r与&5氯仿&r进行&e化学反应&r制作&d四氟乙烯"]
			id: "62F4355AE3E08FD9"
			rewards: [{
				id: "7B90B13C8CD31B5A"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6209FBB5F559FC96"
				item: "gtceu:tetrafluoroethylene_bucket"
				type: "item"
			}]
			x: 2.0d
			y: -5.200000000000001d
		}
		{
			description: ["在&a程序1&r的&e&a化学反应器&f&r中将&3氯气&r和&c甲烷&r结合制成&5氯仿"]
			id: "1C19DD6B3E34012E"
			rewards: [{
				id: "0B623B35DB9095E2"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "66EB810BCE505C9F"
				item: "gtceu:chloroform_bucket"
				type: "item"
			}]
			x: 3.0d
			y: -6.199999999999999d
		}
		{
			dependencies: ["43EBA1D735267C85"]
			description: [
				"将氢气与&a氟气&f进行&e化学反应&r制得"
				""
				"后期还可以在&a电弧炉&r中用氢气重新处理&5三氟化钛&r回收部分&a氢氟酸&f"
			]
			id: "07E750F8184C4362"
			rewards: [{
				id: "1BADDD52EB152112"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "5EF03747135E0D8F"
				item: "gtceu:hydrofluoric_acid_bucket"
				type: "item"
			}]
			x: 2.0d
			y: -6.200000000000001d
		}
		{
			description: ["我最喜欢的&a氟气&f来源？简单,&e电解&r&2&a氟石粉&f"]
			id: "43EBA1D735267C85"
			rewards: [{
				count: 16
				id: "2127226B7DCD7DB7"
				item: "gtceu:raw_fluorite"
				random_bonus: 16
				type: "item"
			}]
			tasks: [{
				id: "682A3DC9CB37D399"
				item: "gtceu:fluorine_bucket"
				type: "item"
			}]
			x: 2.0d
			y: -7.200000000000001d
		}
		{
			dependencies: ["62E7B8817D22678F"]
			description: ["又一个&e&a化学反应器&f&r配方,这次使用&e银金箔&r和&9&a过硫酸钠&f&r或&0三氯化铁"]
			id: "46542F4A273E64EB"
			rewards: [{
				count: 16
				id: "46B29861549EBC3B"
				item: "gtceu:electrum_foil"
				random_bonus: 8
				type: "item"
			}]
			tasks: [{
				id: "5134D69523321A94"
				item: "gtceu:epoxy_printed_circuit_board"
				type: "item"
			}]
			x: 3.0d
			y: 0.7999999999999954d
		}
		{
			dependencies: ["56B7CC072B8E9B48"]
			description: [
				"没错,又一个&e&a化学反应器&f配方"
				""
				"500mB &a硫酸&f + 8金箔 + 环氧树脂板 = 1 &a环氧树脂电路板&f"
			]
			id: "62E7B8817D22678F"
			rewards: [{
				count: 3
				id: "1194D1487287AB33"
				item: "gtceu:epoxy_plate"
				random_bonus: 3
				type: "item"
			}]
			tasks: [{
				id: "0FF97EF9CC8D0F44"
				item: "gtceu:epoxy_circuit_board"
				type: "item"
			}]
			x: 2.0d
			y: 0.7999999999999954d
		}
		{
			dependencies: [
				"79E97623309DEF84"
				"2A32CA82627E5FA7"
			]
			description: [
				"&a氢氧化钠粉&f再次派上用场!将其与环氧氯丙烷和&a双酚A&f进行&e化学反应&r制作液态环氧树脂"
				""
				"你可以直接&e流体固化&r环氧树脂成板材"
				""
				"我们将以此为基础制作&b纳米处理器&r电路板"
			]
			id: "56B7CC072B8E9B48"
			rewards: [{
				count: 12
				id: "6D06B3FF8E27033D"
				item: "gtceu:sodium_hydroxide_dust"
				random_bonus: 12
				type: "item"
			}]
			tasks: [{
				id: "722CEDC6467CBF31"
				item: "gtceu:epoxy_plate"
				type: "item"
			}]
			x: 1.0d
			y: 0.7999999999999954d
		}
		{
			dependencies: [
				"225F77308C834EA8"
				"05F0FCBBEADA8489"
			]
			description: [
				"制作&e环氧树脂&r的关键原料"
				""
				"丙酮、苯酚和&a盐酸&f在&e&a化学反应器&f&r中通过&a程序1&r反应生成"
			]
			id: "79E97623309DEF84"
			rewards: [{
				id: "2747EA249191EA85"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "7D13ABA27E15B6A3"
				item: "gtceu:bisphenol_a_bucket"
				type: "item"
			}]
			x: 0.5d
			y: -0.20000000000000462d
		}
		{
			dependencies: ["32E9B33C9D7981ED"]
			description: [
				"&2甘油&r与&7&a盐酸&f&r在&e&a化学反应器&f&r中生成&c环氧氯丙烷"
				""
				"也可选用替代配方:使用&a烯丙基氯&f和&a次氯酸&f进行反应"
			]
			id: "2A32CA82627E5FA7"
			rewards: [{
				id: "6698F36A022322C2"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6E63FE29D8A9A884"
				item: "gtceu:epichlorohydrin_bucket"
				type: "item"
			}]
			x: 1.5d
			y: -0.20000000000000462d
		}
		{
			dependencies: ["47871110028991D3"]
			description: [
				"确实可以不使用&aLCR反应釜&r生产甘油,但无法实现批量制备!"
				""
				"我的标准配方是:&a氢氧化钠粉&f + &e54桶&a鱼油&f&r + &c9桶乙醇&r = &d9桶甘油"
				""
				"该反应同时产出大量&a生物柴油&f,若加工为十六烷值&a高能柴油&f将是绝佳燃料"
			]
			id: "32E9B33C9D7981ED"
			rewards: [{
				count: 32
				id: "40E6CFF065A57E47"
				item: "minecraft:tropical_fish"
				random_bonus: 32
				type: "item"
			}]
			tasks: [{
				id: "4319527D23F45B63"
				item: "gtceu:glycerol_bucket"
				type: "item"
			}]
			x: 2.0d
			y: -1.2000000000000046d
		}
		{
			dependencies: ["329916B2CB8342B2"]
			description: [
				"需注意&a化学反应器&r配方可概括为:&d3个输入仓&r + &5个输出仓&r + &e1个输入总线&r + &e1个输出总线"
				""
				"完成大量聚四氟乙烯生产后,&a&a大型化学反应釜&f&r终于准备就绪!"
				""
				"某些化学反应(如制作&3聚苯并咪唑&r/PBI)必须使用&aLCR反应釜"
			]
			icon: "gtceu:large_chemical_reactor"
			id: "47871110028991D3"
			min_width: 300
			rewards: [{
				exclude_from_claim_all: true
				id: "686ED1EA06DFDE2D"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "74509DEDE480E6D3"
				item: "gtceu:large_chemical_reactor"
				type: "item"
			}]
			x: 2.0d
			y: -2.200000000000001d
		}
		{
			dependencies: ["6AD5F24DA80B93A8"]
			description: [
				"是否尝试过&e电解&r&9&a盐水&f&r？这是获取&a氯气&r的优质途径(对合成二氯苯尤为重要),副产品&3&a氢氧化钠粉&f"
				""
				"随后可将&a氢氧化钠粉&f与二氯苯&e化学反应&r生成&e苯酚&r!"
			]
			id: "225F77308C834EA8"
			rewards: [{
				id: "25070EA1DD56541C"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "4E39727CD1515BC8"
				item: "gtceu:phenol_bucket"
				type: "item"
			}]
			x: -0.5d
			y: -0.7000000000000046d
		}
		{
			dependencies: ["646918F4D700749B"]
			description: ["使用&e&a流体加热器&f&r或&e蒸馏塔&r运行&a程序1&r,将&3&a乙酸钙溶液&f&r转化为&c丙酮"]
			id: "05F0FCBBEADA8489"
			rewards: [{
				id: "10DB38A855C00F8C"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "58219164CA17C0ED"
				item: "gtceu:acetone_bucket"
				type: "item"
			}]
			x: -0.5d
			y: 0.2999999999999954d
		}
		{
			dependencies: [
				"225F77308C834EA8"
				"38BD153513DC2334"
			]
			description: ["转化为&3聚苯并咪唑&r时可回收半数&e苯酚&r"]
			id: "4AE3A2326EA07B7A"
			rewards: [{
				id: "1BCFE363242C28DA"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:phenol"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:phenol"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "这个怎么发音？"
			tasks: [{
				id: "07267D8026DD87B4"
				item: "gtceu:diphenyl_isophthalate_bucket"
				type: "item"
			}]
			x: -0.5d
			y: -2.2000000000000046d
		}
		{
			dependencies: [
				"47871110028991D3"
				"0D68FA968FD2BEF4"
			]
			description: [
				"需使用&a&a大型化学反应釜&f&r进行制备"
				""
				"注意&a锌粉&r为&c&l非消耗品&r,自动合成配方中无需包含——直接放入输入总线即可"
				""
				"&3氨气&r可通过&e化学反应&r&9氢气&r与&b&a氮气&f&r制得(来自主世界&e&a集气器&f&r,经&a&a真空冷冻机&f&r输送至&a&a蒸馏塔&f)"
			]
			id: "74D47A8DF93294E4"
			min_width: 300
			rewards: [{
				exclude_from_claim_all: true
				id: "0B036AF2C512A810"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "2F89534B88E0DF3F"
				item: "gtceu:diaminobenzidine_bucket"
				type: "item"
			}]
			x: 0.7000000000000006d
			y: -3.3000000000000043d
		}
		{
			dependencies: ["2483DD28AC6E7EA2"]
			description: [
				"若已建造&a&a大型化学反应釜&f&r即可批量生产"
				""
				"否则只能使用&e&a化学反应器&f&r配合&e&a小撮铜粉&f&r、&9氢气&r和&2硝基氯苯"
			]
			id: "0D68FA968FD2BEF4"
			rewards: [{
				id: "5204075D54036125"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "12F0E44FF6E412B9"
				item: "gtceu:dichlorobenzidine_bucket"
				type: "item"
			}]
			x: 0.6999999999999997d
			y: -4.400000000000002d
		}
		{
			dependencies: [
				"72DC025BC059DF96"
				"6F8E6D84D31651D5"
			]
			description: [
				"将氯苯与&a硝酸混酸&f进行&e化学反应&r生成&2硝基氯苯"
				""
				"副产品&e&a稀硫酸&f&r可经&e蒸馏&r提纯为&a浓硫酸&f"
			]
			id: "2483DD28AC6E7EA2"
			rewards: [{
				id: "7BEAB6689E33EC52"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4DAAE03F1A961070"
				item: "gtceu:nitrochlorobenzene_bucket"
				type: "item"
			}]
			x: 0.7000000000000002d
			y: -5.600000000000001d
		}
		{
			dependencies: ["39B625FA20EAA025"]
			description: ["&e&a浓硫酸&f&r与&c&a浓硫酸&f&r混合生成&e&a硝酸混酸&f"]
			id: "72DC025BC059DF96"
			rewards: [{
				id: "1A0A4F758AA39803"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "70F5FC5B8C666064"
				item: "gtceu:nitration_mixture_bucket"
				type: "item"
			}]
			x: 0.6999999999999997d
			y: -6.800000000000001d
		}
		{
			description: [
				"需准备&9氨气&r和&a&a大型化学反应釜&f&r,或大量&b&a二氧化氮&f"
				""
				"幸运的是,获取&a二氧化氮&f很简单:末地&3&aHV集气器&f&r运行&2程序3&r,配合IV级&a&a真空冷冻机&f&r和IV级&a&a蒸馏塔&f&r"
			]
			id: "39B625FA20EAA025"
			rewards: [{
				exclude_from_claim_all: true
				id: "794F78C0C2915F25"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "68D0BF02B52C546A"
				item: "gtceu:nitric_acid_bucket"
				type: "item"
			}]
			x: 0.6999999999999997d
			y: -8.0d
		}
		{
			description: [
				"氯气与苯在&e&a化学反应器&f&r运行&a程序1&r生成&2氯苯"
				""
				"需要更多&0苯&r？可尝试&a蒸馏&r&e深度蒸汽裂解&a重燃油&f"
			]
			id: "6F8E6D84D31651D5"
			rewards: [{
				id: "0249677FD8FC36BD"
				item: "gtceu:benzene_bucket"
				type: "item"
			}]
			tasks: [{
				id: "061565CB40A69AB3"
				item: "gtceu:chlorobenzene_bucket"
				type: "item"
			}]
			x: -0.5d
			y: -5.600000000000005d
		}
		{
			dependencies: ["7E407CDBFD85E65F"]
			description: ["[ \"Some \", { \"text\": \"Acetic Acid\", \"underlined\": \"true\", \"color\": \"aqua\", \"clickEvent\": { \"action\": \"change_page\", \"value\": \"7E407CDBFD85E65F\" }, \"hoverEvent\": { \"action\":\"show_text\", \"contents\": { \"text\":\"&a点击此处&f接取&a乙酸&f任务\" } } }, \" and Calcite Dust in a \", { \"text\": \"Chemical Reactor\", \"color\":\"yellow\" }, \" will get you this\" ]"]
			hide_dependency_lines: true
			id: "646918F4D700749B"
			rewards: [{
				count: 8
				id: "0CDEE0ED9ACFE836"
				item: "gtceu:raw_calcite"
				random_bonus: 8
				type: "item"
			}]
			tasks: [{
				id: "1A05D01D8AD64CFC"
				item: "gtceu:dissolved_calcium_acetate_bucket"
				type: "item"
			}]
			x: -1.5d
			y: 0.30000000000000004d
		}
		{
			description: ["&0苯&r库存不足？&a蒸馏&r&e深度蒸汽裂解&a重燃油&f是优质来源"]
			id: "6AD5F24DA80B93A8"
			rewards: [{
				id: "166126D39E35DF1A"
				item: "gtceu:benzene_bucket"
				type: "item"
			}]
			tasks: [{
				id: "35D4D665BA3535F7"
				item: "gtceu:dichlorobenzene_bucket"
				type: "item"
			}]
			x: -1.5d
			y: -0.7000000000000046d
		}
		{
			dependencies: ["6A86F8FB2B3A3425"]
			description: [
				"6份锡粉、3份铅粉和1份锑粉在&e混合机&r的&a程序3&r中可合成10份&a焊锡粉&f"
				""
				"这将使我们能用更少的锡来制作电路,以及一些需要特定焊锡合金的物品!"
				""
				"你可以使用&e提取机&r将粉末转化为液态形式"
			]
			id: "12D84337AEB56002"
			rewards: [{
				count: 5
				id: "3CBCA6B1206AD6EE"
				item: "gtceu:soldering_alloy_dust"
				random_bonus: 5
				type: "item"
			}]
			tasks: [{
				id: "3BA1E80264EF114C"
				item: "gtceu:soldering_alloy_dust"
				type: "item"
			}]
			x: 3.0d
			y: -0.5d
		}
		{
			description: [
				"&a矿石处理&r &c辉锑矿&r有几率获得锑"
				""
				"将辉锑矿粉放入&e离心机&r可稳定获得锑"
				""
				"锑在后期极其重要,请务必囤积,不要全部做成焊锡合金!"
			]
			id: "6A86F8FB2B3A3425"
			rewards: [{
				count: 16
				id: "0FCE0E5A7C7D9469"
				item: "gtceu:antimony_dust"
				random_bonus: 8
				type: "item"
			}]
			tasks: [{
				id: "3F4D687187DC2893"
				item: "gtceu:antimony_dust"
				type: "item"
			}]
			x: 3.0d
			y: -1.5d
		}
		{
			dependencies: [
				"00BF4E98A328801D"
				"07FC106AFE76E766"
			]
			description: [
				"主要用途是与钢粉&e混合&r制成&d钨钢&r"
				""
				"有时需要将此粉末放入&e电弧炉&r的&a程序1&r中获取热锭,然后在&e&a真空冷冻机&f&r中冷却得到&3&a钨锭&f"
			]
			icon: "gtceu:tungsten_dust"
			id: "540B4CE165EE4D5B"
			rewards: [{
				count: 16
				id: "5DEF392D66887C7A"
				item: "gtceu:tungsten_dust"
				random_bonus: 16
				type: "item"
			}]
			tasks: [{
				id: "46B1AE17CF5CAD6E"
				item: "gtceu:tungsten_dust"
				type: "item"
			}]
			x: -9.7d
			y: 0.8d
		}
		{
			dependencies: [
				"4321E32D0EA3367C"
				"0299E59D23F16ACB"
			]
			description: ["在&e&a化学浸洗器&f&r中用&b&a盐酸&f&r进行酸浴处理\\n\\n随后需要&e电解&r来提取&d钨"]
			id: "00BF4E98A328801D"
			rewards: [
				{
					count: 16
					id: "4E0E2BD8FB1470F5"
					item: "gtceu:raw_tungstate"
					random_bonus: 16
					type: "item"
				}
				{
					count: 16
					id: "7D8D05AF9F7547FF"
					item: "gtceu:raw_scheelite"
					random_bonus: 16
					type: "item"
				}
			]
			tasks: [{
				id: "7AB2FCA85A20F48C"
				item: "gtceu:tungstic_acid_dust"
				type: "item"
			}]
			x: -9.700000000000001d
			y: -2.0d
		}
		{
			description: [
				"你应该知道怎么做,通过&a矿石处理&r将钨酸盐或白钨矿粉碎成粉末"
				""
				"原始矿石生成于&e&a末地&f层&r的&d&a挖矿维度&f&r中,Y坐标-63至0之间,与锂矿伴生.地表甚至可能出现白钨矿指示物!"
			]
			id: "0299E59D23F16ACB"
			rewards: [
				{
					count: 16
					id: "2E226ED22CDBCC65"
					item: "gtceu:raw_tungstate"
					random_bonus: 16
					type: "item"
				}
				{
					count: 16
					id: "1C24E6A8D8B2A4A8"
					item: "gtceu:raw_scheelite"
					random_bonus: 16
					type: "item"
				}
			]
			tasks: [{
				id: "54231849B7094A32"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:tungstate_dust"
							}
							{
								Count: 1b
								id: "gtceu:scheelite_dust"
							}
						]
					}
				}
				title: "钨酸盐或白钨矿粉"
				type: "item"
			}]
			x: -9.7d
			y: -3.5d
		}
		{
			dependencies: ["19BA973FECFA3B06"]
			description: [
				"将你的&a末影之眼&f在氡气中进行快速化学浴处理,即可获得这些&d量子之眼"
				""
				"除非你想把所有&e激光刻录机&r升级到EV级(IV级是更好的升级),否则不必大量制作"
			]
			id: "587B4048E0BCEB27"
			rewards: [
				{
					id: "5E94E1957ABD2CF7"
					item: "minecraft:ender_eye"
					type: "item"
				}
				{
					id: "73B592A0EFE989B7"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "411CD5BC3965C8DF"
				item: "gtceu:quantum_eye"
				type: "item"
			}]
			x: 6.5d
			y: 4.0d
		}
		{
			description: [
				"获取&d&a氡气&f主要有两种方式"
				""
				"最佳方式(因为能获得其他产物)是在&b&a末地&f&r维度使用&e高压&r&e&a集气器&f&r,将收集的&e末影之气&r在&1IV级&r&a真空冷冻机&f中液化,再通过IV级&a蒸馏塔&f获得&a氡气&f"
				""
				"替代方案是建造&a&a大型化学反应釜&f&r,直接用&3末影之气&r与&a铀粉&f和&a钚锭&f反应——甚至能回收钚!"
			]
			hide_dependency_lines: true
			id: "19BA973FECFA3B06"
			min_width: 300
			rewards: [{
				exclude_from_claim_all: true
				id: "04C6CC2C47B853A4"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "08FD542F3DED5422"
				item: "gtceu:radon_bucket"
				type: "item"
			}]
			x: 7.5d
			y: 4.0d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: ["将此机器升级至&5EV级&r可解锁最终制造&b钨&r所需的配方"]
			hide_dependency_lines: true
			id: "4321E32D0EA3367C"
			rewards: [{
				exclude_from_claim_all: true
				id: "6AF0E3294F739382"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "738F3984052AAF67"
				item: "gtceu:ev_chemical_bath"
				type: "item"
			}]
			x: -8.2d
			y: -2.0d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: ["需要&5EV级电解机&r从&a钨酸&f中提取&a钨粉&f"]
			hide_dependency_lines: true
			id: "07FC106AFE76E766"
			rewards: [{
				exclude_from_claim_all: true
				id: "175694EB574CA428"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "6FAA7B053A5B7226"
				item: "gtceu:ev_electrolyzer"
				type: "item"
			}]
			x: -11.3d
			y: 0.7999999999999999d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: ["需要此等级的混合机来制作&3钨钢粉&r和&d钒&a镓粉&f"]
			hide_dependency_lines: true
			id: "692C9BA71EA0F0A7"
			rewards: [{
				exclude_from_claim_all: true
				id: "656E72FBB3C85963"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			subtitle: "搅拌钩在哪？"
			tasks: [{
				id: "7DC755D1965AD27F"
				item: "gtceu:ev_mixer"
				type: "item"
			}]
			x: -7.0d
			y: -1.1999999999999993d
		}
		{
			dependencies: ["175B7F7D98D45208"]
			description: ["两个这样的部件可使多方块结构达到IV级!"]
			id: "273DC98BA2785BD6"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "5517968C529A888F"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "105F1E2F7F3CE430"
				item: "gtceu:ev_energy_input_hatch"
				type: "item"
			}]
			x: 2.5d
			y: 5.0d
		}
		{
			dependencies: ["4B16C957EADC183B"]
			description: ["&l&e注意&r:每个能源舱能承受2安培电流,因此若在多方块结构上安装两个,实际可升级至&dLuV级&r!"]
			id: "2F1DF3971B75E63C"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "554BB706DA3D06A6"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "306674A64BA37598"
				item: "gtceu:iv_energy_input_hatch"
				type: "item"
			}]
			x: 2.5d
			y: 7.0d
		}
		{
			dependencies: ["273DC98BA2785BD6"]
			description: ["IV级多方块结构的全能能源解决方案!\\n\\n&e&l注:&r不提升配方等级,仅加快处理速度."]
			id: "2BE364CCE684AD45"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "6C5CEAA5B106A9BC"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "2EDF8035D4375117"
				item: "gtceu:ev_energy_input_hatch_4a"
				type: "item"
			}]
			x: 3.5d
			y: 5.0d
		}
		{
			dependencies: ["2F1DF3971B75E63C"]
			description: ["既然一个就够,为何要装两个能源舱？这个能源舱单体能承受4安培IV级电流!\\n\\n原因在于:除非安装两个,否则这个能源舱不会提升配方等级,仅改变配方处理速度."]
			id: "653260450BEDB6AB"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "423BAB711C8CA031"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "55672931C894C132"
				item: "gtceu:iv_energy_input_hatch_4a"
				type: "item"
			}]
			x: 3.5d
			y: 7.0d
		}
		{
			dependencies: ["692C9BA71EA0F0A7"]
			description: [
				"3份钒粉 + 1份镓粉在&a程序1&r模式下可合成此物"
				""
				"后续阶段将大量需求此材料,目前主要用于制造高级表面贴装器件"
				""
				"钒可通过&e离心处理&r&2&a钒磁铁矿&f粉末获得,该矿物分布于&e&a末地&f&r或&3主世界"
				""
				"替代方案:可对&c红宝石&r或&9蓝宝石&r浆液进行&e离心处理"
			]
			id: "465F4502C4D5DCFE"
			rewards: [{
				count: 8
				id: "683127D56633C2C9"
				item: "gtceu:gallium_dust"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "7B93FC8915128873"
				item: "gtceu:vanadium_gallium_dust"
				type: "item"
			}]
			x: -7.0d
			y: -4.0d
		}
		{
			dependencies: ["5F270891C953486E"]
			description: ["该晶圆是所有高阶光子集成电路(PIC)晶圆的基础基板,后续阶段需大量制备"]
			id: "4838AD09C9590D2F"
			rewards: [{
				exclude_from_claim_all: true
				id: "5D5442A16257DFD6"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [
				{
					id: "37417147A0F312A1"
					item: "gtceu:mpic_wafer"
					type: "item"
				}
				{
					id: "06D2B23E601FD4EA"
					item: "gtceu:brown_glass_lens"
					type: "item"
				}
			]
			x: 0.5d
			y: 5.0d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			hide_dependency_lines: true
			id: "5873949AB76FBF85"
			rewards: [{
				exclude_from_claim_all: true
				id: "42B1F28669A2172B"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "43BC5F4CF9C41D05"
				item: "gtceu:ev_cutter"
				type: "item"
			}]
			x: 1.5d
			y: 6.0d
		}
		{
			dependencies: [
				"4838AD09C9590D2F"
				"5873949AB76FBF85"
			]
			id: "175B7F7D98D45208"
			rewards: [{
				count: 2
				id: "75F11CAED241B7CA"
				item: "gtceu:mpic_chip"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "26B441421818B730"
				item: "gtceu:mpic_chip"
				type: "item"
			}]
			x: 1.5d
			y: 5.0d
		}
		{
			dependencies: [
				"465F4502C4D5DCFE"
				"0A848E0B9F485B2C"
			]
			description: ["研发&c零点模块(ZPM)&r阶段将频繁使用此材料,目前用于制造&d高级&a贴片晶体管&f&r和&e&a高功率IC晶圆&f"]
			hide_dependent_lines: true
			id: "6F0AD70D3748D8D2"
			rewards: [{
				count: 5
				id: "57DFE01303D76273"
				item: "gtceu:vanadium_gallium_ingot"
				random_bonus: 5
				type: "item"
			}]
			tasks: [{
				id: "41E7743A2A8A0476"
				item: "gtceu:vanadium_gallium_ingot"
				type: "item"
			}]
			x: -2.5d
			y: -4.0d
		}
		{
			dependencies: [
				"4838AD09C9590D2F"
				"03C756EB123CA953"
				"7E7FAA72581D2186"
				"6F0AD70D3748D8D2"
			]
			description: ["通过升级中功率集成电路(MPIC)来实现更高阶的能量传输!"]
			id: "4F8C6F49F9D7EE4D"
			rewards: [{
				exclude_from_claim_all: true
				id: "3E575D413B2B7579"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "6F81ED6B43252DC6"
				item: "gtceu:hpic_wafer"
				type: "item"
			}]
			x: 0.5d
			y: 7.0d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: [
				"用于将中&a功率电路&f(&aMPIC&r)晶圆升级为高功率(&eHPIC&r)版本,以适配更大容量的能量舱!"
				""
				"该合成需在&b无尘室&r内进行"
			]
			id: "03C756EB123CA953"
			rewards: [{
				exclude_from_claim_all: true
				id: "3553FD26F2E02470"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "783D93D534AFC84F"
				item: "gtceu:iv_chemical_reactor"
				type: "item"
			}]
			x: -0.5d
			y: 7.0d
		}
		{
			dependencies: ["0907800ED4793F96"]
			description: [
				"说到需要层压玻璃的机器...这是我们要制造的首个型号!"
				""
				"需使用更高阶的切割机处理高阶芯片,才能实现更高功率等级"
			]
			id: "6D6AA1A0D8AB01DE"
			rewards: [{
				exclude_from_claim_all: true
				id: "2C21FC6E62426E6E"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "657DC3B9C2057A7A"
				item: "gtceu:iv_cutter"
				type: "item"
			}]
			x: 1.5d
			y: 8.0d
		}
		{
			dependencies: ["2D3A6B7907FDB9B1"]
			description: [
				"恭喜!你已成功研制出新一代玻璃材料——&d层压玻璃&r!"
				""
				"该材料广泛用于制造IV级和LuV级机械"
			]
			id: "0907800ED4793F96"
			rewards: [{
				count: 3
				id: "048A0310B5446608"
				item: "gtceu:laminated_glass"
				random_bonus: 3
				type: "item"
			}]
			tasks: [{
				id: "24AEE48613787976"
				item: "gtceu:laminated_glass"
				type: "item"
			}]
			x: 0.5d
			y: 8.0d
		}
		{
			dependencies: [
				"4C83B804A6AEB033"
				"4EF3FD6C59413C49"
			]
			description: ["这是合成链中最后的&e化学反应&r,终极目标正是珍贵的&d层压玻璃&r"]
			id: "2D3A6B7907FDB9B1"
			rewards: [{
				id: "143F393D3CEC9FF3"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "470744DC5229F216"
				item: "gtceu:polyvinyl_butyral_bucket"
				type: "item"
			}]
			x: -0.5d
			y: 8.0d
		}
		{
			dependencies: ["5C38DAD6BA43A7F1"]
			description: [
				"常规方法是将&3乙烯&r与&e&a乙酸乙烯酯&f&r以1:1比例合成&e&a聚乙酸乙烯酯&f&r，但使用&b氧气&r可将产出比提升至3:2"
				""
				"若添加微量&d&a四氯化钛&f&r，更可将比例优化至2:1！"
				""
				"无论采用何种方案,请注意合成所需的&a&a编程电路&f&r型号"
			]
			id: "4EF3FD6C59413C49"
			min_width: 300
			rewards: [{
				id: "4512EA5EA6BDF96D"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "751A81792263724C"
				item: "gtceu:polyvinyl_acetate_bucket"
				type: "item"
			}]
			x: -2.0d
			y: 7.0d
		}
		{
			description: [
				"&e电解&r丙烷可获得&e丙烯"
				""
				"通过&a分馏&r处理&b重度蒸汽&a裂化石脑油&f&r获取丙烯"
				""
				"若需近乎无限的供应源,可从&c液态地狱乙烯&r的&a分馏&r过程中获取&9&a一氧化碳&f"
			]
			id: "4C83B804A6AEB033"
			rewards: [{
				id: "6354612EE7F9CC4B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "1DFC03AF10A7050D"
				item: "gtceu:butyraldehyde_bucket"
				type: "item"
			}]
			x: -2.0d
			y: 8.0d
		}
		{
			dependencies: ["7E407CDBFD85E65F"]
			description: ["在&e&a化学反应器&f中使用&a乙酸&f,额外添加乙烯和氧气,切换至&a程序3&r模式进行反应"]
			id: "5C38DAD6BA43A7F1"
			rewards: [{
				id: "382E158E2107DADA"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4A3A4DC6D4C4A556"
				item: "gtceu:vinyl_acetate_bucket"
				type: "item"
			}]
			x: -3.0d
			y: 7.0d
		}
		{
			description: ["在&e&a化学反应器&f&r中以&a程序2&r模式使氧气与乙烯反应,是制备&a乙酸&f的途径之一"]
			id: "7E407CDBFD85E65F"
			rewards: [{
				id: "5135899D8594558A"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "47D630FAA125ACD5"
				item: "gtceu:acetic_acid_bucket"
				type: "item"
			}]
			x: -4.0d
			y: 7.0d
		}
		{
			dependencies: ["5F270891C953486E"]
			description: ["是否知道或非逻辑门(NOR)可用于构建所有其他逻辑门？这正是电路制造中频繁使用它的原因!"]
			hide_dependent_lines: true
			id: "15A1D6D05A785919"
			rewards: [{
				exclude_from_claim_all: true
				id: "7D9864200223A08B"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [
				{
					id: "610265C7A4A5EF5F"
					item: "gtceu:nor_memory_wafer"
					type: "item"
				}
				{
					id: "26A237A47DBC5F09"
					item: "gtceu:pink_glass_lens"
					type: "item"
				}
			]
			x: -0.5d
			y: 5.5d
		}
		{
			dependencies: ["5F270891C953486E"]
			description: ["本次堆叠的是与非门(NAND)芯片激光刻录机,既可用于数据存储,也是制造&a晶体处理器&f超级计算机的关键设备——后续将详细说明!"]
			hide_dependent_lines: true
			id: "77AAC2F643E92A88"
			rewards: [{
				exclude_from_claim_all: true
				id: "06F533E1F4E6C6DD"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [
				{
					id: "024376E20D2E1DA1"
					item: "gtceu:nand_memory_wafer"
					type: "item"
				}
				{
					id: "0E307E705A9C051A"
					item: "gtceu:gray_glass_lens"
					type: "item"
				}
			]
			x: -1.5d
			y: 5.0d
		}
		{
			dependencies: [
				"4F8C6F49F9D7EE4D"
				"6D6AA1A0D8AB01DE"
			]
			id: "4B16C957EADC183B"
			rewards: [{
				id: "215390373F2BFC50"
				item: "gtceu:hpic_chip"
				random_bonus: 1
				type: "item"
			}]
			tasks: [{
				id: "0558EEA3CB72F59F"
				item: "gtceu:hpic_chip"
				type: "item"
			}]
			x: 1.5d
			y: 7.0d
		}
		{
			dependencies: ["13BAD4B9C69117C6"]
			description: [
				"我们将通过&e化学反应&r使&0&a粗制碳网&f&r与&e&a液态荧石&f&r及&b&aCPU晶圆&f&r结合,制成&3&a纳米CPU晶圆&f"
				""
				"荧石粉经&e提取机&r处理可得液态荧石"
			]
			id: "1896B90F5FD66AFA"
			rewards: [
				{
					count: 2
					id: "23B211F5E5B530B7"
					item: "gtceu:cpu_wafer"
					random_bonus: 2
					type: "item"
				}
				{
					count: 8
					id: "4140E59E9F9A5CBE"
					item: "gtceu:carbon_fibers"
					random_bonus: 8
					type: "item"
				}
			]
			tasks: [{
				id: "75671C8E193FB933"
				item: "gtceu:nano_cpu_wafer"
				type: "item"
			}]
			x: 3.0d
			y: 2.0d
		}
		{
			dependencies: ["56B7CC072B8E9B48"]
			description: [
				"存在多种制备&a粗制碳网&f的方案"
				""
				"方案一:在&e高压釜&r中使用&e9mB环氧树脂&r和4份&a碳粉&f,可获得4份产物"
				""
				"也可选用&d9mB聚苯并咪唑&r和8份&a碳粉&f,&2产出量将提升至16份!"
			]
			id: "13BAD4B9C69117C6"
			rewards: [{
				count: 12
				id: "0D64031DC03EA6CD"
				item: "gtceu:carbon_dust"
				random_bonus: 12
				type: "item"
			}]
			tasks: [{
				id: "3126CA0CD31A6EA2"
				item: "gtceu:carbon_fibers"
				type: "item"
			}]
			x: 2.0d
			y: 2.0d
		}
		{
			dependencies: ["47871110028991D3"]
			description: [
				"你可以选择获取&3萘&r或&2二甲苯&r,两者各有优势"
				""
				"避开&a&a热解炉&f&r的一条途径是使用&0木炭&r配合&e提取器&r制作&8&a木焦油&f&r,再将其&a蒸馏&r成&2二甲苯"
				""
				"但实际上你最好还是建造&a&a热解炉&f&r来燃烧原木或煤炭,并对产物进行&a蒸馏&r处理"
			]
			id: "38BD153513DC2334"
			rewards: [{
				exclude_from_claim_all: true
				id: "3F9B95BA289B80CE"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "70C9A799B2294DAC"
				item: "gtceu:phthalic_acid_bucket"
				type: "item"
			}]
			x: 0.7d
			y: -2.2d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: [
				"这么快就需要升级了吗？没错,确实如此"
				""
				"我们需要&eIV级组装机&r来制造任何&d高级表面贴装器件&r(SMD)"
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "45D9E32D75F5ACAE"
			rewards: [{
				exclude_from_claim_all: true
				id: "5ACB560F9E15C52F"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			tasks: [{
				id: "3C372FDBEAEDA75B"
				item: "gtceu:iv_assembler"
				type: "item"
			}]
			x: 4.0d
			y: -3.5d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: ["虽然并非必须,但IV级研磨机可以加速处理谢尔顿矿石,因为这条产线可能相当&n&l&2耗时&r"]
			hide_dependency_lines: true
			id: "16B44F78707E148E"
			rewards: [{
				exclude_from_claim_all: true
				id: "7771EA6688348CD0"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			shape: "diamond"
			size: 1.5d
			subtitle: "嘿,研磨机,呀哈!"
			tasks: [{
				id: "783804D20825EC31"
				item: "gtceu:iv_macerator"
				type: "item"
			}]
			x: -6.0d
			y: -3.1d
		}
		{
			dependencies: ["16B44F78707E148E"]
			description: ["&a处理&f谢尔顿矿石并进行提纯,可以让你获得最高品质的&a铂系矿泥渣&f.这些矿泥含有推进进度所需的资源."]
			id: "2FDACD6F153D5B64"
			rewards: [{
				count: 16
				id: "02F35F6C06C59F87"
				item: "gtceu:raw_cooperite"
				random_bonus: 32
				type: "item"
			}]
			subtitle: "我的朋友谢尔顿晚上去了俱乐部"
			tasks: [{
				id: "3E85E016A9FC831F"
				item: "gtceu:raw_cooperite"
				type: "item"
			}]
			x: -4.5d
			y: -3.1d
		}
		{
			dependencies: ["2FDACD6F153D5B64"]
			description: [
				"&l&e&a王水&f&r&r是浓缩&a硫酸&f与&a盐酸&f的混合物，通常按1:3比例配制。炼金术士因其能溶解&l&e黄金&r&r的特性，将其命名为"
				"皇家水"
				"(字面意思)."
				""
			]
			id: "0DD389A24F5F8CDD"
			rewards: [{
				id: "2BD13756A8D0E6D0"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:aqua_regia"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:aqua_regia"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "我是芭比娃娃,生活在芭比世界...懂的都懂"
			tasks: [{
				id: "167A231788D5F833"
				item: "gtceu:aqua_regia_bucket"
				type: "item"
			}]
			x: -4.0d
			y: -2.0500000000000007d
		}
		{
			dependencies: ["2FDACD6F153D5B64"]
			description: ["&a铂系矿泥渣&f可加工成多种重要资源,助你继续推进进度."]
			id: "4632DA3CE9D95064"
			rewards: [{
				count: 8
				id: "029845A689A9ED7D"
				item: "gtceu:platinum_group_sludge_dust"
				random_bonus: 16
				type: "item"
			}]
			subtitle: "钱!*蟹老板语气*"
			tasks: [{
				id: "6D5FD9D06E3AD241"
				item: "gtceu:platinum_group_sludge_dust"
				type: "item"
			}]
			x: -5.0d
			y: -2.0500000000000007d
		}
		{
			dependencies: [
				"4632DA3CE9D95064"
				"20A01B6A6B1177CB"
				"0DD389A24F5F8CDD"
			]
			description: [
				"这些惰性物质不会保持太久!处理后你将获得大量新资源,它们还能进一步加工成实用材料!"
				""
				"记得建立自动化产线持续处理,因为后续需要大量这些加工产物."
			]
			id: "7CC3BD5F3D66A637"
			rewards: [{
				count: 6
				id: "1B047E534CB265CB"
				item: "gtceu:inert_metal_mixture_dust"
				random_bonus: 6
				type: "item"
			}]
			subtitle: "氡气飘进咖啡馆,服务员说"
			tasks: [{
				id: "2F0E1D0E62D27DF0"
				item: "gtceu:inert_metal_mixture_dust"
				type: "item"
			}]
			x: -4.5d
			y: -1.0500000000000007d
		}
		{
			dependencies: ["64F77D41B2D057B8"]
			description: [
				"又升一级,加工速度再次提升.但别松懈,处理&a新月&f资源时还需要进一步升级来缩短加工时间.继续努力!"
				""
				"你做得太棒了!!"
			]
			hide_dependency_lines: true
			id: "20A01B6A6B1177CB"
			rewards: [{
				exclude_from_claim_all: true
				id: "1BBA9D4D6693F074"
				table_id: 6202000790833671070L
				type: "loot"
			}]
			subtitle: "你让我天旋地转...拜托,你早料到会有这个梗..."
			tasks: [{
				id: "7609E81C3C3C8F78"
				item: "gtceu:iv_centrifuge"
				type: "item"
			}]
			x: -5.5d
			y: -1.0d
		}
		{
			dependencies: ["7CC3BD5F3D66A637"]
			description: ["现在我们已经分解了惰性金属混合物,获得两种新资源,在&dLuv&r阶段都极其珍贵!务必建立自动化产线确保稳定供应!"]
			id: "439556C571592D81"
			rewards: [
				{
					id: "36D580AD734CBBA6"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 16000
								FluidName: "gtceu:rhodium_sulfate"
								capacity: 16000
							}
						}
						id: "evilcraft:dark_tank"
						tag: {
							Fluid: {
								Amount: 16000
								FluidName: "gtceu:rhodium_sulfate"
							}
							capacity: 16000
						}
					}
					type: "item"
				}
				{
					count: 6
					id: "775ACF8F8D043BB8"
					item: "gtceu:ruthenium_tetroxide_dust"
					random_bonus: 6
					type: "item"
				}
			]
			shape: "gear"
			subtitle: "买一送一!超值优惠!"
			tasks: [
				{
					id: "195C8E82DDC275B1"
					item: "gtceu:ruthenium_tetroxide_dust"
					type: "item"
				}
				{
					id: "18C94C4E8484B27A"
					item: "gtceu:rhodium_sulfate_bucket"
					type: "item"
				}
			]
			x: -3.5d
			y: -1.0500000000000007d
		}
		{
			dependencies: ["439556C571592D81"]
			description: ["现在我们有了纯净钌,又可以把它弄脏啦!哈哈!用混合机制作钌粉.后续工艺和物品(包括装配线)都需要大量钌粉."]
			id: "40249CD28957E7EB"
			rewards: [{
				count: 6
				id: "09A68E6B45224827"
				item: "gtceu:ruthenium_dust"
				random_bonus: 6
				type: "item"
			}]
			subtitle: "清理灰尘"
			tasks: [{
				id: "270A0E25457E1C36"
				item: "gtceu:ruthenium_dust"
				type: "item"
			}]
			x: -4.5d
			y: 0.0d
		}
		{
			dependencies: ["40249CD28957E7EB"]
			id: "1FF8B0E2D10C88E9"
			rewards: [{
				count: 6
				id: "3FB909CCA3040943"
				item: "gtceu:ruthenium_dust"
				random_bonus: 6
				type: "item"
			}]
			tasks: [{
				id: "0020D722D78214B3"
				item: "gtceu:rtm_alloy_dust"
				type: "item"
			}]
			x: -3.5d
			y: 0.0d
		}
		{
			dependencies: [
				"41EE8B40BA43DADE"
				"45D9E32D75F5ACAE"
			]
			description: [
				"没错,本章节包含两个&e电弧炉&r线圈升级!"
				""
				"你需要制作这些线圈来熔炼高速钢S型和E型变体,用于高级SMD组件"
				""
				"&l&e注意:&r&r制作纳米处理器并不强制要求先做高级SMD组件"
			]
			id: "1AB86FD8776634D0"
			rewards: [
				{
					count: 16
					id: "0FCE644F73F35583"
					item: "gtceu:tungsten_dust"
					random_bonus: 16
					type: "item"
				}
				{
					count: 16
					id: "1DC9FB0667F710A0"
					item: "gtceu:hssg_dust"
					random_bonus: 16
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "47CD64F1B76FEC3C"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			tasks: [{
				id: "231B87784B7EC352"
				item: "gtceu:hssg_coil_block"
				type: "item"
			}]
			x: 7.0d
			y: -3.4999999999999996d
		}
		{
			dependencies: ["4632DA3CE9D95064"]
			description: [
				"&a铱矿石&f确实非常稀有,如果你还没找到也很正常"
				""
				"我们可以通过&e铂系生产线™&r来获取铱"
			]
			hide_dependency_lines: true
			id: "2EE52FD7129D3D87"
			optional: true
			rewards: [{
				count: 8
				id: "2BB47D5128FF5D50"
				item: "gtceu:rarest_metal_mixture_dust"
				random_bonus: 16
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "2AF36DE65F7CBA3C"
				item: "gtceu:rarest_metal_mixture_dust"
				type: "item"
			}]
			x: 3.5d
			y: -2.5d
		}
		{
			dependencies: [
				"2EE52FD7129D3D87"
				"47871110028991D3"
			]
			description: [
				"只有&e&a大型化学反应釜&f&r才能处理这个反应!"
				""
				"我们不会使用酸性锇溶液,因为锇资源很丰富"
			]
			id: "7C9D8120B5C5BD9B"
			optional: true
			rewards: [{
				count: 8
				id: "3B3D8C01C31D63D8"
				item: "gtceu:iridium_metal_residue_dust"
				random_bonus: 16
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "35F58171ECC99A6A"
				item: "gtceu:iridium_metal_residue_dust"
				type: "item"
			}]
			x: 3.0d
			y: -3.0d
		}
		{
			dependencies: ["7C9D8120B5C5BD9B"]
			description: [
				"这一步很简单,我们只需将残留物放入&e离心机&r,就能得到&a氯化铱&f和一些废渣"
				""
				"如果你想进一步处理那些废渣也可以,但这对获取珍贵的铱并非必要步骤"
			]
			id: "6E6D9527AC093199"
			optional: true
			rewards: [{
				count: 8
				id: "04637191A807D0C9"
				item: "gtceu:iridium_chloride_dust"
				random_bonus: 16
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "4671D5F7C6163DF9"
				item: "gtceu:iridium_chloride_dust"
				type: "item"
			}]
			x: 3.0d
			y: -4.0d
		}
		{
			dependencies: ["6E6D9527AC093199"]
			description: ["最后只需通过&e化学反应&r将氯从铱上分离出来!"]
			id: "02190383FEE793ED"
			optional: true
			rewards: [
				{
					count: 8
					id: "73F219D4D8F72B5A"
					item: "alltheores:iridium_dust"
					random_bonus: 16
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "35A9005AF56D5554"
					table_id: 6202000790833671070L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "074663BA515D70DF"
				item: "alltheores:iridium_dust"
				type: "item"
			}]
			x: 3.5d
			y: -4.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"该任务默认隐藏,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "0D0EE0D7770F7B5C"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "5F01CDDCA2549DC3"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "78FED3ADEBDCD785"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: -7.0d
			y: 4.5d
		}
	]
	title: "疯狂电压"
}
