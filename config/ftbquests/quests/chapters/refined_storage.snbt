{
	default_hide_dependency_lines: false
	default_quest_shape: "diamond"
	filename: "refined_storage"
	group: "1AC60211DE7427FC"
	icon: "refinedstorage:creative_controller"
	id: "15AAF17B6665223D"
	order_index: 2
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["&9&d精致存储&f&r是一个基于网络的大容量存储模组.\\n\\n要开始使用这个模组,你需要先制作&e控制器&r.\\n\\n&a控制器&f是你存储网络的'核心'.当提供能量时,它会激活所有连接的RS组件.每个网络只能有一个控制器."]
			id: "01F18150EC923482"
			rewards: [
				{
					id: "13BC4987B9B25D15"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "762245D6E1A1C755"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "gear"
			size: 2.0d
			subtitle: "入门指南"
			tasks: [{
				id: "196B8B6D59ABFC74"
				item: "refinedstorage:controller"
				type: "item"
			}]
			title: "&d&d精致存储&f"
			x: -6.0d
			y: -2.5d
		}
		{
			dependencies: ["7604D8B3C9FCCFD9"]
			description: ["&a1k存储磁盘&f可以存储1000个物品."]
			id: "4101F8275B41C79B"
			rewards: [{
				id: "0E75917E7E00D711"
				table_id: 4001436279668650237L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "1121780C6403D660"
				item: "refinedstorage:1k_storage_part"
				type: "item"
			}]
			title: "&e&a1k 存储元件&f"
			x: -0.5d
			y: -0.5d
		}
		{
			dependencies: ["4101F8275B41C79B"]
			description: ["&a4k存储磁盘&f可以存储4000个物品."]
			id: "3FD801D7DE9176FB"
			rewards: [
				{
					id: "45B99CC9C8BFE5A7"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "103ED5D40508531F"
					table_id: 407746579787184593L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "1420D2099CBB6833"
				item: "refinedstorage:4k_storage_part"
				type: "item"
			}]
			title: "&e&a4k 存储元件&f"
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["3FD801D7DE9176FB"]
			description: ["&a16k存储磁盘&f可以存储16000个物品."]
			id: "03C78165C5CBD9E1"
			rewards: [
				{
					id: "52C389613C90C469"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "5BC7096137A2F4BB"
					table_id: 407746579787184593L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "0346A00E2F8F7532"
				item: "refinedstorage:16k_storage_part"
				type: "item"
			}]
			title: "&a&a16k存储元件&f"
			x: -0.5d
			y: 0.5d
		}
		{
			dependencies: ["03C78165C5CBD9E1"]
			description: ["&a64k存储磁盘&f可以存储64000个物品."]
			id: "3F8EEE1AD4420702"
			rewards: [
				{
					id: "4E028E9A2CD6831A"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "12AD2DB65B2AC554"
					table_id: 407746579787184593L
					type: "random"
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "02A8CBA1DAC84DAE"
				item: "refinedstorage:64k_storage_part"
				type: "item"
			}]
			title: "&b&a64k存储元件&f"
			x: -0.5d
			y: 1.5d
		}
		{
			dependencies: ["123807F4BBE75120"]
			description: ["&a65536k存储磁盘&f可以存储65536000个物品."]
			id: "1D2700821045CCF2"
			rewards: [
				{
					id: "66A881D347175219"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "2640929F7C28E93D"
					table_id: 4001436279668650237L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "7E7B3341B5C8D8CA"
				item: "extradisks:65536k_storage_part"
				type: "item"
			}]
			title: "&5&a65536k存储元件&f"
			x: 0.0d
			y: 5.5d
		}
		{
			dependencies: ["7DD638E4111D66E7"]
			description: ["&a256k存储磁盘&f可以存储256000个物品."]
			id: "4C1F13F5A0E273EF"
			rewards: [{
				id: "03F16EB9DA9624AD"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "04B88AEDB2799159"
				item: "extradisks:256k_storage_part"
				type: "item"
			}]
			title: "&e&a256k存储元件&f"
			x: 0.0d
			y: 3.5d
		}
		{
			dependencies: ["4C1F13F5A0E273EF"]
			description: ["&a1024k存储磁盘&f可以存储1024000个物品."]
			id: "7C94F6AE2A793858"
			rewards: [{
				id: "12217A7AEFD0B703"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "15F9ABA22EFEF7E6"
				item: "extradisks:1024k_storage_part"
				type: "item"
			}]
			title: "&e&a1024k存储元件&f"
			x: 0.5d
			y: 4.0d
		}
		{
			dependencies: ["7C94F6AE2A793858"]
			description: ["&a4096k存储磁盘&f可以存储4096000个物品."]
			id: "52ACADDFCB0E22AB"
			rewards: [
				{
					id: "7736581800AE4CE2"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "42EABE802EFE97D6"
					table_id: 4001436279668650237L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "71E4327C2E1FBB04"
				item: "extradisks:4096k_storage_part"
				type: "item"
			}]
			title: "&a4096k存储元件&f"
			x: 1.0d
			y: 4.5d
		}
		{
			dependencies: ["52ACADDFCB0E22AB"]
			description: ["&a16384k存储磁盘&f可以存储16384000个物品."]
			id: "123807F4BBE75120"
			rewards: [
				{
					id: "0A61A7DF1E582A48"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "0D67B9E6DC5FB74D"
					table_id: 4001436279668650237L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "6C599CC5ADBA425B"
				item: "extradisks:16384k_storage_part"
				type: "item"
			}]
			title: "&b&a16384k存储元件&f"
			x: 0.5d
			y: 5.0d
		}
		{
			dependencies: ["1D2700821045CCF2"]
			description: ["&a262m存储磁盘&f可以存储...嗯...262m个物品."]
			id: "4966162FBA933453"
			rewards: [
				{
					id: "1D4393DDBCE3FA6D"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "03768788B310EC53"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "485B34813CB30B7A"
					table_id: 1739527894044761161L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3C2952EABDCBF187"
				item: "extradisks:262144k_storage_part"
				type: "item"
			}]
			title: "&4&a262m存储元件&f"
			x: -0.5d
			y: 5.0d
		}
		{
			dependencies: ["4966162FBA933453"]
			description: ["你肯定不会相信这个.\\n\\n&a1048m存储磁盘&f可以存储1048m个物品."]
			id: "718A2040D868E09F"
			rewards: [
				{
					id: "2724B0206935EB5F"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "58C7B778FC651DD0"
					table_id: 1739527894044761161L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "4E565846EED470F5"
				item: "extradisks:1048576k_storage_part"
				type: "item"
			}]
			title: "&2&a1048m存储元件&f"
			x: -1.0d
			y: 4.5d
		}
		{
			dependencies: [
				"3751015CD5C84134"
				"3F8EEE1AD4420702"
			]
			dependency_requirement: "one_completed"
			description: ["它和普通的一样,但是更高级.\\n\\n这些用于容纳更大的&a磁盘驱动器&f."]
			id: "7DD638E4111D66E7"
			rewards: [{
				id: "1D1650B2B18131E3"
				table_id: 4001436279668650237L
				type: "random"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "099B55FD3A94FE91"
				item: "extradisks:advanced_storage_housing"
				type: "item"
			}]
			title: "高级&a磁盘外壳&f"
			x: -1.5d
			y: 2.5d
		}
		{
			dependencies: ["65C8A43FEDBA3835"]
			description: ["&9合成器&r用于存储合成配方,让网络系统知道如何合成物品.将配方放入合成器后,你就能从网格中'请求'合成物品.操作方法是按住CTRL+Shift点击已有配方的物品,打开合成GUI.\\n\\n该方块还可以朝向机器放置以使用&e&a处理样板&f&r.例如,将合成器对准熔炉时,可以放入需要熔炉的配方(比如冶炼原始处理器).只要合成器连接到系统且熔炉装有输入器,就能请求合成冶炼后的处理器版本!"]
			id: "44881E39AB1D62AA"
			min_width: 300
			rewards: [
				{
					count: 4
					id: "3351B9B6898579ED"
					item: "refinedstorage:pattern"
					random_bonus: 4
					type: "item"
				}
				{
					id: "6831B9D1AB4346E0"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			size: 1.25d
			subtitle: "自动化合成!"
			tasks: [{
				id: "202A7EA0318662C5"
				item: "refinedstorage:crafter"
				type: "item"
			}]
			title: "合成器"
			x: 2.0d
			y: -8.5d
		}
		{
			dependencies: ["44881E39AB1D62AA"]
			description: ["升级版合成器,可存储更多配方并提高合成速度."]
			hide_until_deps_visible: true
			id: "45FF08E4DB6F7F0E"
			rewards: [
				{
					count: 2
					id: "78D26C79FAC05CFF"
					item: "refinedstorage:pattern"
					type: "item"
				}
				{
					id: "784DBA5C53E77B0F"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			tasks: [{
				id: "0ED2706B229F3AA7"
				item: "extrastorage:iron_crafter"
				type: "item"
			}]
			title: "铁质合成器"
			x: 1.5d
			y: -8.0d
		}
		{
			dependencies: ["45FF08E4DB6F7F0E"]
			description: ["升级版合成器,可存储更多配方并提高合成速度."]
			hide_until_deps_visible: true
			id: "4A198E875A6B0A64"
			rewards: [
				{
					count: 3
					id: "5ACCF4B3C45E2989"
					item: "refinedstorage:pattern"
					type: "item"
				}
				{
					id: "0F53B827EFCD88B3"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			tasks: [{
				id: "1AA53ECBAE795C70"
				item: "extrastorage:gold_crafter"
				type: "item"
			}]
			title: "&e黄金合成器"
			x: 1.5d
			y: -9.0d
		}
		{
			dependencies: ["4A198E875A6B0A64"]
			description: ["升级版合成器,可存储更多配方并提高合成速度."]
			hide_until_deps_visible: true
			id: "189018F641FBB44E"
			rewards: [
				{
					count: 4
					id: "23E76AEB4BEA3B45"
					item: "refinedstorage:pattern"
					type: "item"
				}
				{
					id: "3659A7F955F0E035"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			tasks: [{
				id: "461E78CF8E7FCD04"
				item: "extrastorage:diamond_crafter"
				type: "item"
			}]
			title: "&b钻石合成器"
			x: 2.5d
			y: -9.0d
		}
		{
			dependencies: ["189018F641FBB44E"]
			description: ["升级版合成器,可存储更多配方并提高合成速度."]
			hide_until_deps_visible: true
			id: "24BD32102AFA1691"
			rewards: [
				{
					count: 5
					id: "09E39857FC64780B"
					item: "refinedstorage:pattern"
					type: "item"
				}
				{
					id: "5706B3CCE55E4250"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			tasks: [{
				id: "4FEB0889EEBC1BDA"
				item: "extrastorage:netherite_crafter"
				type: "item"
			}]
			title: "&5下界合金合成器"
			x: 2.5d
			y: -8.0d
		}
		{
			dependencies: ["01F18150EC923482"]
			description: ["&9线缆&r用于将方块和物品连接到系统.\\n\\n初期可以通过相邻放置方块来连接系统,但线缆能让你扩展网络!这是任何&d精致存储&f网络的基础设施."]
			id: "6904EC449FBEE387"
			rewards: [
				{
					count: 8
					id: "3940AD01E5D76358"
					item: "refinedstorage:cable"
					random_bonus: 8
					type: "item"
				}
				{
					id: "1174925025CC0A56"
					table_id: 4001436279668650237L
					type: "random"
				}
			]
			shape: "octagon"
			size: 1.5d
			subtitle: "系统连接"
			tasks: [{
				id: "1A2C2B95FDA9D2DA"
				item: "refinedstorage:cable"
				type: "item"
			}]
			title: "线缆"
			x: -6.0d
			y: -5.0d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["&9输入器&r用于从连接的方块中提取物品.\\n\\n例如可用在熔炉上,将成品提取到系统中.\\n\\n记得给它们装上&e速度&r或&e堆叠&r升级!\\n\\n还可以过滤提取的物品.需要更多过滤槽位？升级它!"]
			hide_until_deps_visible: true
			id: "532229D285CA4858"
			rewards: [{
				id: "1DE253380778110D"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			subtitle: "物品输入!"
			tasks: [{
				id: "0E57EE9B975D05C3"
				item: "refinedstorage:importer"
				type: "item"
			}]
			title: "输入器"
			x: -6.5d
			y: -6.5d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["该接口会自动破坏其连接的方块.\\n\\n也可设置为白名单模式,适用于自动化特定流程(比如在Create模组中制作外壳)."]
			hide_until_deps_visible: true
			id: "6092490EC9008A05"
			rewards: [{
				id: "706F07A4768DE9FA"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			subtitle: "方块破坏!"
			tasks: [{
				id: "5762E9CE9A7D3FCA"
				item: "refinedstorage:destructor"
				type: "item"
			}]
			title: "破坏面板"
			x: -4.5d
			y: -6.0d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["该接口会朝其指向的方向放置指定方块.\\n\\n可通过GUI指定方块,非常适合自动化!"]
			hide_until_deps_visible: true
			id: "2CFEE04BA574921E"
			rewards: [{
				id: "3CDBEEDBD20A61DE"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			subtitle: "方块放置!"
			tasks: [{
				id: "2BB33FEBA2B071DF"
				item: "refinedstorage:constructor"
				type: "item"
			}]
			title: "构造面板"
			x: -5.5d
			y: -6.5d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["&9输出器&r用于将物品从系统推入网络存储的方块中.\\n\\n可用于向熔炉填充矿石/燃料,或将材料送入指定箱子等.\\n\\n也可以过滤推送的物品.需要更多过滤槽位？升级它!"]
			hide_until_deps_visible: true
			id: "4CD83943865018EA"
			rewards: [{
				id: "40C3EDC23E165575"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			subtitle: "物品输出!"
			tasks: [{
				id: "528F054C15BB7191"
				item: "refinedstorage:exporter"
				type: "item"
			}]
			title: "输出器"
			x: -7.5d
			y: -6.0d
		}
		{
			dependencies: ["532229D285CA4858"]
			id: "616B6046EF5DDF21"
			rewards: [{
				id: "74D65FAD1FCC2C5D"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "6947092EB1DF6DCE"
				item: "cabletiers:elite_importer"
				type: "item"
			}]
			title: "精英输入器"
			x: -6.5d
			y: -7.5d
		}
		{
			dependencies: ["616B6046EF5DDF21"]
			id: "13A0714444BA79AB"
			rewards: [{
				id: "4DC81065EB1C50CD"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "60057B7F36565B42"
				item: "cabletiers:ultra_importer"
				type: "item"
			}]
			title: "终极输入器"
			x: -6.5d
			y: -8.5d
		}
		{
			dependencies: ["6092490EC9008A05"]
			description: ["拥有18个过滤槽位,工作效率提升2倍."]
			id: "6D81DF90E9C2C049"
			rewards: [{
				id: "37B580B60ADC023E"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "6AD21D58B8EE01A3"
				item: "cabletiers:elite_destructor"
				type: "item"
			}]
			title: "&a精英破坏面板&f"
			x: -4.5d
			y: -7.0d
		}
		{
			dependencies: ["6D81DF90E9C2C049"]
			description: ["拥有36个过滤槽位,工作效率提升6倍."]
			id: "0D4FE97640F2CA60"
			rewards: [{
				id: "13DA1147475AF6BF"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "1AC9A19F8B27A12C"
				item: "cabletiers:ultra_destructor"
				type: "item"
			}]
			title: "&a终极破坏面板&f"
			x: -4.5d
			y: -8.0d
		}
		{
			dependencies: ["2CFEE04BA574921E"]
			description: ["拥有2个合成槽位."]
			id: "6DB06E3984D0CF97"
			rewards: [{
				id: "15DEC3015C064B0E"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "32C8173865CAFD14"
				item: "cabletiers:elite_constructor"
				type: "item"
			}]
			title: "&a精英成型面板&f"
			x: -5.5d
			y: -7.5d
		}
		{
			dependencies: ["6DB06E3984D0CF97"]
			description: ["拥有4个合成槽位."]
			id: "1F55A483008F74FE"
			rewards: [{
				id: "6E7CC639224A46F1"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "140A523F7D4EFC3E"
				item: "cabletiers:ultra_constructor"
				type: "item"
			}]
			title: "&a终极成型面板&f"
			x: -5.5d
			y: -8.5d
		}
		{
			dependencies: ["4CD83943865018EA"]
			description: ["比普通输出器多9个过滤槽位,速度提升2倍."]
			id: "6973A5923AF7BF05"
			rewards: [{
				id: "18AB4575E10AC20E"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "4E291FDCE1763291"
				item: "cabletiers:elite_exporter"
				type: "item"
			}]
			title: "精英输出器"
			x: -7.5d
			y: -7.0d
		}
		{
			dependencies: ["6973A5923AF7BF05"]
			description: ["比普通输出器多27个过滤槽位,速度提升6倍,并内置堆叠升级."]
			id: "5BB7648DC10E1E08"
			rewards: [{
				id: "7E7CF0891D09698A"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "515DEAD74B07300C"
				item: "cabletiers:ultra_exporter"
				type: "item"
			}]
			title: "超级输出器"
			x: -7.5d
			y: -8.0d
		}
		{
			dependencies: ["7604D8B3C9FCCFD9"]
			description: ["&a64k流体存储元件&f用于合成&a64k流体存储磁盘&f."]
			id: "4B81E84CAE814BA9"
			rewards: [{
				id: "2DAA28CC2526657D"
				table_id: 4001436279668650237L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "3F9B17A338B2EEFE"
				item: "refinedstorage:64k_fluid_storage_part"
				type: "item"
			}]
			title: "&e64k流体元件&f"
			x: -2.5d
			y: -0.5d
		}
		{
			dependencies: ["4B81E84CAE814BA9"]
			description: ["&a256k流体存储元件&f用于合成&a256k流体存储磁盘&f."]
			id: "39C8E1705EF1CD31"
			rewards: [{
				id: "2F50B3D8AFF81B1E"
				table_id: 4001436279668650237L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "440737C224526B0B"
				item: "refinedstorage:256k_fluid_storage_part"
				type: "item"
			}]
			title: "&e256k流体元件&f"
			x: -3.0d
			y: 0.0d
		}
		{
			dependencies: ["39C8E1705EF1CD31"]
			description: ["&a1024k流体存储元件&f用于合成&a1024k流体存储磁盘&f."]
			id: "60244F26B9ABED49"
			rewards: [{
				id: "5A3901DB229F523B"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "40AEA1CE52FAFCAB"
				item: "refinedstorage:1024k_fluid_storage_part"
				type: "item"
			}]
			title: "&a1024k流体元件&f"
			x: -2.5d
			y: 0.5d
		}
		{
			dependencies: ["60244F26B9ABED49"]
			description: ["&a4096k流体存储元件&f用于合成&a4096k流体存储磁盘&f."]
			id: "3751015CD5C84134"
			rewards: [{
				id: "1BF095899A5270F5"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "2B57DE504688FC8D"
				item: "refinedstorage:4096k_fluid_storage_part"
				type: "item"
			}]
			title: "&b4096k流体元件&f"
			x: -2.5d
			y: 1.5d
		}
		{
			dependencies: ["7DD638E4111D66E7"]
			description: ["&a16384k流体存储元件&f用于合成&a16384k流体存储磁盘&f."]
			id: "53CA0D6B4D5A17D5"
			rewards: [{
				id: "632DE7C72BB9103B"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "62F1F92687582BAF"
				item: "extradisks:16384k_fluid_storage_part"
				type: "item"
			}]
			title: "&e16384k流体元件&f"
			x: -3.0d
			y: 3.5d
		}
		{
			dependencies: ["53CA0D6B4D5A17D5"]
			description: ["&a65536k流体存储元件&f用于合成&a65536k流体存储磁盘&f."]
			id: "64D6C395B6304F40"
			rewards: [{
				id: "78883095DF1F6CC5"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "3D29353D5475642D"
				item: "extradisks:65536k_fluid_storage_part"
				type: "item"
			}]
			title: "&e65536k流体元件&f"
			x: -4.0d
			y: 4.5d
		}
		{
			dependencies: ["64D6C395B6304F40"]
			description: ["&a262m流体存储元件&f用于合成&a262m流体存储磁盘&f."]
			id: "17920C436DDBBD07"
			rewards: [
				{
					id: "71F146A064E4D55C"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "14685B5F1C545420"
					table_id: 4001436279668650237L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5DDDBCF17E30A7C4"
				item: "extradisks:262144k_fluid_storage_part"
				type: "item"
			}]
			title: "&262m流体元件&f"
			x: -3.0d
			y: 5.5d
		}
		{
			dependencies: ["17920C436DDBBD07"]
			description: ["&a1048m流体存储元件&f用于合成&a1048m流体存储磁盘&f."]
			id: "49772923B8AF0F1F"
			rewards: [
				{
					id: "25461173A1F7474B"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					exclude_from_claim_all: true
					id: "5E7B9F095B8158C8"
					table_id: 1739527894044761161L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "0EE5758F4584DD05"
				item: "extradisks:1048576k_fluid_storage_part"
				type: "item"
			}]
			title: "&b1048m流体元件&f"
			x: -2.0d
			y: 4.5d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["这种线缆接口可能是你在&d精致存储&f之旅中最重要的物品.\\n\\n该接口将外部存储连接到你的&d精致存储&f网络,让系统能够读取存储容器中的数据,使你能在终端中访问这些物品!\\n\\n适用于多种容器,包括但不限于:箱子、木桶、抽屉、&a抽屉控制器&f等!\\n\\n如果你暂时无法制作存储元件,而散落着许多箱子,这是组建存储网络的最佳方案!"]
			hide_until_deps_visible: true
			id: "253657BE5D9D9162"
			min_width: 300
			rewards: [{
				id: "33EE37E408A14D2A"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "4D9E7CCF420BDE8C"
				item: "refinedstorage:external_storage"
				type: "item"
			}]
			title: "外部存储器"
			x: -7.0d
			y: -4.0d
		}
		{
			dependencies: ["01F18150EC923482"]
			description: ["&9&a权限管理器&f&r允许你为网络用户分配访问权限.\\n\\n放置后需为每位玩家创建&9&a安全卡&f&r来配置权限.若未配置任何卡片,所有玩家将拥有完全访问权限.\\n\\n若要限制未配置玩家的权限,可放置一张未绑定玩家的卡片.注意:&a权限管理器&f的放置者始终拥有管理权限."]
			id: "3B84215240D9F2CB"
			min_width: 300
			optional: true
			rewards: [
				{
					id: "03E85C433BFA3B18"
					item: "refinedstorage:security_card"
					type: "item"
				}
				{
					id: "604536C15104A0C8"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "5426A98A63199515"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			subtitle: "守护你的存储网络!"
			tasks: [{
				id: "20F3020C07C9D22B"
				item: "refinedstorage:security_manager"
				type: "item"
			}]
			title: "&a权限管理器&f"
			x: -7.0d
			y: -1.5d
		}
		{
			dependencies: ["12A43F82FC67A289"]
			description: ["太棒了!我们可以虚拟存储物品了,但如何查看网络中的物品呢？\\n\\n你需要创建一个&9终端&r来访问系统,这是查看存储物品的最基础界面."]
			id: "43E8912CB307E421"
			rewards: [
				{
					id: "673FBE94CC27445E"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "0E35D05AA810F2C0"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "68A80F00CAFF427D"
				item: "refinedstorage:grid"
				type: "item"
			}]
			title: "访问网络存储"
			x: 2.0d
			y: -2.5d
		}
		{
			dependencies: ["43E8912CB307E421"]
			description: ["在访问存储系统的同时提供工作台功能!\\n\\n说实话,没人会只用普通终端."]
			id: "0F00BBDF3618B3BA"
			rewards: [
				{
					id: "41A66CDD13A10EFB"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "53189DA11AA43777"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5B74E6CB7AB75F92"
				item: "refinedstorage:crafting_grid"
				type: "item"
			}]
			title: "合成终端"
			x: 1.5d
			y: -1.5d
		}
		{
			dependencies: ["59F5ED931FD70C55"]
			description: ["此网格可让你将配方刻录到网络用样板中.\\n\\n创建样板配方的最简单方法是在JEI中查找物品,然后点击配方右下角的'+'按钮.这会将配方放入网格中.\\n\\n右侧顶部槽位用于存放空白样板.放入空白样板后,点击下方箭头即可将配方刻录到样板上.\\n\\n随后将样板放入合成器即可使用!"]
			hide_until_deps_visible: true
			id: "65C8A43FEDBA3835"
			min_width: 300
			rewards: [{
				id: "3FAE50501AEA16E9"
				table_id: 4001436279668650237L
				type: "random"
			}]
			shape: "square"
			tasks: [{
				id: "79E70744DBC24777"
				item: "refinedstorage:pattern_grid"
				type: "item"
			}]
			title: "&a样板终端&f"
			x: 2.0d
			y: -6.5d
		}
		{
			dependencies: ["43E8912CB307E421"]
			description: ["需要存储流体？你需要&9&a流体终端&f&r来查看流体磁盘中的内容.\\n\\n专业提示:若要存储来自&d通用机械&f的气体,可尝试先用&a回旋式气液转换机&f将其转化为液体."]
			id: "51E9D03CD755A607"
			rewards: [
				{
					id: "663DB8F9C5006DA9"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "62DB4906D15A4AA4"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "4EAE7D47133842B1"
				item: "refinedstorage:fluid_grid"
				type: "item"
			}]
			title: "&a流体终端&f"
			x: 2.5d
			y: -1.5d
		}
		{
			dependencies: ["6B04FC81351CD1AB"]
			description: ["允许无线访问存储系统."]
			id: "15ECBC8E174FA39B"
			rewards: [
				{
					id: "73C96DD0654FF5CE"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "627DFE6503A864D9"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "1488D961F51ED182"
				item: "refinedstorage:wireless_grid"
				type: "item"
			}]
			title: "无线终端"
			x: 5.5d
			y: -3.5d
		}
		{
			dependencies: ["6B04FC81351CD1AB"]
			description: ["允许无线访问流体网格."]
			id: "7FA85713C86166DA"
			rewards: [
				{
					id: "598120E2B2A936E9"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "5E65E7082749DB48"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "77FE4FDC2F752084"
				item: "refinedstorage:wireless_fluid_grid"
				type: "item"
			}]
			title: "无线&a流体终端&f"
			x: 6.5d
			y: -3.5d
		}
		{
			dependencies: ["6B04FC81351CD1AB"]
			description: ["允许无线访问终端,并内置工作台功能."]
			id: "744842B2AED28344"
			rewards: [
				{
					id: "6B5DF00E7050694D"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "553AC9C05E0564EA"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5C72FB6EFDAF0813"
				item: "refinedstorageaddons:wireless_crafting_grid"
				type: "item"
			}]
			title: "无线合成终端"
			x: 4.5d
			y: -3.5d
		}
		{
			dependencies: ["43E8912CB307E421"]
			description: ["&a便携式终端&f是不连接网络的便携存储设备.可让你在不接入网络时操作存储磁盘."]
			hide_dependency_lines: false
			hide_until_deps_visible: false
			id: "66D85424EC74EDCC"
			optional: true
			rewards: [{
				id: "2977A7581BEC6C48"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "diamond"
			subtitle: "移动终端"
			tasks: [{
				id: "301E0669A3CD84EF"
				item: {
					Count: 1
					id: "refinedstorage:portable_grid"
					tag: { }
				}
				type: "item"
			}]
			title: "&a便携式终端&f"
			x: 2.0d
			y: -1.0d
		}
		{
			dependencies: ["01F18150EC923482"]
			description: ["存储物品需要创建磁盘并放入&a&a磁盘驱动器&f&r.可通过放置在控制器旁或使用管道接入网络.\\n\\n驱动器提供8个存储磁盘插槽."]
			id: "12A43F82FC67A289"
			rewards: [
				{
					id: "4657EF2161AC3ADD"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "0B750449B490A597"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "rsquare"
			size: 1.25d
			subtitle: "磁盘时刻!"
			tasks: [{
				id: "3724447C3E0F9942"
				item: "refinedstorage:disk_drive"
				type: "item"
			}]
			title: "创建虚拟存储"
			x: -1.5d
			y: -2.5d
		}
		{
			dependencies: ["12A43F82FC67A289"]
			description: ["允许将物品和流体从一个磁盘转移到其他网格的磁盘中."]
			hide_until_deps_visible: true
			id: "4844DEF57A2287B4"
			rewards: [{
				id: "70D006DD94C36966"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "057EA89AE615DA6D"
				item: "refinedstorage:disk_manipulator"
				type: "item"
			}]
			title: "&a磁盘操纵器&f"
			x: -1.5d
			y: -4.0d
		}
		{
			dependencies: ["43E8912CB307E421"]
			description: ["&9样板&r是自动合成的核心.它们存储配方让你的RS网络知道如何合成物品."]
			id: "59F5ED931FD70C55"
			rewards: [
				{
					id: "1FA71DB84EE6B7D8"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					count: 8
					id: "4348758E87B788AE"
					item: "refinedstorage:pattern"
					type: "item"
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "45359CC3EC90B74B"
				item: "refinedstorage:pattern"
				type: "item"
			}]
			title: "自动合成!"
			x: 2.0d
			y: -4.449999999999999d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["连接系统后,此方块会显示网络中指定物品的当前数量."]
			hide_until_deps_visible: true
			id: "64E85274D24FE394"
			optional: true
			rewards: [
				{
					id: "6AC46C5C8136A9C3"
					type: "xp"
					xp: 100
				}
				{
					id: "1491EF64254BB838"
					table_id: 4001436279668650237L
					type: "random"
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "59E1432B592B686C"
				item: "refinedstorage:storage_monitor"
				type: "item"
			}]
			title: "&a存储监控器&f"
			x: -5.0d
			y: -4.0d
		}
		{
			dependencies: ["43E8912CB307E421"]
			dependency_requirement: "one_started"
			description: ["若要无线访问&d精致存储&f网络,首先需要制作&9无线发射器&r.\\n\\n可将其安装在系统任意位置,这是使用无线终端的前提条件."]
			hide_dependency_lines: false
			id: "6B04FC81351CD1AB"
			rewards: [{
				id: "5050A99AB9DA31EE"
				table_id: 4001436279668650237L
				type: "random"
			}]
			shape: "hexagon"
			subtitle: "远程存储"
			tasks: [{
				id: "48935A6041672A29"
				item: "refinedstorage:wireless_transmitter"
				type: "item"
			}]
			title: "&5无线访问"
			x: 5.5d
			y: -2.5d
		}
		{
			dependencies: ["4844DEF57A2287B4"]
			hide_until_deps_visible: true
			id: "7C26D0295ABBF5BD"
			rewards: [{
				id: "1E8B6CC202D9C975"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "247C1CD892AA4E07"
				item: "cabletiers:elite_disk_manipulator"
				type: "item"
			}]
			title: "&a精英磁盘操纵器&f"
			x: -1.5d
			y: -5.0d
		}
		{
			dependencies: ["7C26D0295ABBF5BD"]
			hide_until_deps_visible: true
			id: "1C8CEA8E12D9F2E3"
			rewards: [{
				id: "55C4ACA6CF61E7A8"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "6CF001677DE268D7"
				item: "cabletiers:ultra_disk_manipulator"
				type: "item"
			}]
			title: "&a终极磁盘操纵器&f"
			x: -1.5d
			y: -6.0d
		}
		{
			dependencies: ["2E2811D1A0F2A492"]
			description: ["'我需要更大范围!!!'\\n\\n将此装置放入无线发射器,即可实现无限范围访问网络存储.\\n\\n但该效果无法跨维度生效....."]
			id: "3E0D29502690E02F"
			rewards: [
				{
					id: "020CFD2156077264"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "51EB8245F9BF2EBB"
					table_id: 407746579787184593L
					type: "random"
				}
			]
			shape: "circle"
			tasks: [{
				id: "4DD51C8F38808EB9"
				item: "rsinfinitybooster:infinity_card"
				type: "item"
			}]
			title: "无限范围增强器"
			x: 5.5d
			y: -0.5d
		}
		{
			dependencies: ["12A43F82FC67A289"]
			dependency_requirement: "one_started"
			description: ["现在是制作&d精致存储&f的'硬盘驱动器'的时候了.为此,我们需要一个&9&a磁盘外壳&f&r,将其与&a存储部件&r结合以制作&d存储磁盘&r.只需制作所需尺寸的部件,然后与外壳结合即可制作磁盘.\\n\\n&a存储&f磁盘用于在放入&a磁盘驱动器&f后虚拟存储物品.它必须放入&a磁盘驱动器&f中.&a存储&f磁盘在世界中掉落时不会消失."]
			hide_until_deps_visible: true
			id: "7604D8B3C9FCCFD9"
			min_width: 300
			rewards: [
				{
					id: "2A14130CAC3461EA"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "6116F11EC66ADB9D"
					table_id: 407746579787184593L
					type: "random"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "546B04909ABE8383"
				item: "refinedstorage:storage_housing"
				type: "item"
			}]
			title: "&a磁盘外壳&f"
			x: -1.5d
			y: -1.0d
		}
		{
			dependencies: ["01F18150EC923482"]
			description: ["由于&a控制器&f的限制,你可能最终希望将不同系统连接到主中心,而无需用无数电缆连接它们.\\n\\n这是通过使用&e&a网络变送器&f&r和&e接收器&r实现的.\\n\\n&9变送器&r应连接到主系统,无论你的&a控制器&f在哪里.\\n\\n&9接收器&r应放在你想要外部网络的地方.例如,这可以是你的基地中单独的部分,那里有蜜蜂、怪物农场等.\\n\\n要将接收器连接到主网络,你需要使用&e&a网卡&f&r.要绑定&a网卡&f,右键点击&a网络接收器&f,然后将&a网卡&f放入连接到主系统的&a网络变送器&f中."]
			id: "3E890BABC4C41370"
			min_width: 300
			rewards: [
				{
					id: "7D644616E6416286"
					item: "refinedstorage:network_card"
					type: "item"
				}
				{
					id: "7D1DCEA1DB748856"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "7321631CFD0F9E9D"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			subtitle: "无线连接网络!"
			tasks: [
				{
					id: "2E028480454ECBB5"
					item: "refinedstorage:network_transmitter"
					type: "item"
				}
				{
					id: "1CB0AFE99559C1B5"
					item: "refinedstorage:network_receiver"
					type: "item"
				}
			]
			title: "网格网络"
			x: -5.0d
			y: -1.5d
		}
		{
			dependencies: [
				"4B81E84CAE814BA9"
				"4101F8275B41C79B"
			]
			dependency_requirement: "one_completed"
			description: ["不喜欢将物品存储在驱动器中？\\n\\n你可以创建存储&9方块&r,然后将方块放在系统中的某个地方.\\n\\n无论什么物品最终进入方块,当你破坏它时,这些物品也会被存储在里面.\\n\\n如果你想取消制作它,可以在持有它时潜行&a右击&f."]
			id: "62625CAF6649AB53"
			optional: true
			rewards: [{
				id: "2757EAF400D978C1"
				table_id: 4001436279668650237L
				type: "random"
			}]
			shape: "rsquare"
			tasks: [{
				id: "4540AB0231FE3B1A"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "refinedstorage:64k_fluid_storage_block"
							}
							{
								Count: 1b
								id: "refinedstorage:1k_storage_block"
							}
						]
					}
				}
				title: "存储方块"
				type: "item"
			}]
			x: -1.5d
			y: 0.5d
		}
		{
			dependencies: ["6092490EC9008A05"]
			description: ["这些升级为你的破坏者添加附魔.\\n\\n想知道为什么你会使用这个？想象一下,有一个建造者或采石场将丝绸之触矿石泵入你的系统.你可以让构造者放置这些矿石,然后让带有时运附魔的破坏者破坏它们以获得更多的原矿石."]
			hide_dependency_lines: false
			id: "787415570026FFAA"
			min_width: 300
			optional: true
			rewards: [{
				id: "0EB941B66B8E55EB"
				table_id: 3567941291661635734L
				type: "random"
			}]
			shape: "diamond"
			tasks: [
				{
					id: "416E0011066156D2"
					item: "refinedstorage:silk_touch_upgrade"
					type: "item"
				}
				{
					id: "086992D773D34C15"
					item: "refinedstorage:fortune_3_upgrade"
					type: "item"
				}
			]
			title: "破坏者升级"
			x: -3.5d
			y: -6.0d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			dependency_requirement: "one_completed"
			description: ["&9&a风驰&f升级&r正如其名.它将提高界面/方块的工作速度.需要进口器更快地拉取物品？想让你的合成器工作得更快？这就是这个升级!\\n\\n&e堆叠升级&r通过一次传输一组物品而不是一个物品来提高传输速度.与&a风驰&f升级结合使用!"]
			hide_until_deps_visible: true
			id: "2B27E4E04A628C60"
			rewards: [
				{
					id: "10EE1EA9372509F0"
					table_id: 3567941291661635734L
					type: "random"
				}
				{
					id: "4B859F9679182D36"
					item: "refinedstorage:speed_upgrade"
					random_bonus: 2
					type: "item"
				}
			]
			tasks: [
				{
					id: "0B3AB478E6493DBC"
					item: "refinedstorage:speed_upgrade"
					type: "item"
				}
				{
					id: "2B39C12AEFA55C59"
					item: "refinedstorage:stack_upgrade"
					type: "item"
				}
			]
			title: "加速一切!"
			x: -7.5d
			y: -5.0d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["&9过滤器&r是用于网格中的物品,用于指定哪些物品或流体可以显示."]
			hide_until_deps_visible: true
			id: "35EC08D610382860"
			optional: true
			rewards: [{
				id: "1361E62DF27B02EE"
				table_id: 4001436279668650237L
				type: "random"
			}]
			tasks: [{
				id: "5BFCB4F5C8CCA8C9"
				item: "refinedstorage:filter"
				type: "item"
			}]
			title: "过滤器"
			x: -4.5d
			y: -5.0d
		}
		{
			dependencies: ["6B04FC81351CD1AB"]
			description: ["要增加与系统的无线连接范围,你需要制作&9范围升级&r.\\n\\n无线变送器最多只能容纳4个.这里有一个免费的."]
			hide_until_deps_visible: true
			id: "2E2811D1A0F2A492"
			rewards: [
				{
					id: "56D7CD357A4D790B"
					item: "refinedstorage:range_upgrade"
					type: "item"
				}
				{
					id: "3A61D12F2542D63D"
					table_id: 4001436279668650237L
					type: "random"
				}
			]
			shape: "circle"
			subtitle: "我指的是传输范围"
			tasks: [{
				id: "7AB53C2469B768E0"
				item: "refinedstorage:range_upgrade"
				type: "item"
			}]
			title: "增加WiFi范围"
			x: 5.5d
			y: -1.5d
		}
		{
			dependencies: ["3E0D29502690E02F"]
			description: ["但这个可以!\\n\\n这允许你从任何维度无线访问你的RS系统."]
			id: "3EE9958D84A1252C"
			rewards: [
				{
					id: "6E00F9CDE957D741"
					table_id: 407746579787184593L
					type: "random"
				}
				{
					id: "07BCC4BCD1A84D6E"
					table_id: 1739527894044761161L
					type: "random"
				}
			]
			shape: "circle"
			tasks: [{
				id: "19BBDF4958C7FD63"
				item: "rsinfinitybooster:dimension_card"
				type: "item"
			}]
			title: "&a维度卡&f"
			x: 5.5d
			y: 0.5d
		}
		{
			dependencies: ["6904EC449FBEE387"]
			description: ["&9调节器升级&r可让你在方块或机器中维持特定数量的物品.举个典型例子:你想让熔炉里始终保持64个煤炭.只需将这个升级模块放入连接熔炉的导出器中,并将数值设为64.系统就会自动维持熔炉燃料充足!\\n\\n如果需要合成物品怎么办？&9合成升级&r正是为此设计.当合成器内已学习配方时,在接口添加此升级模块,系统就会在物品短缺时自动合成.\\n\\n沿用之前的例子,假设我们有神秘农业农场产出&a煤炭原质&f.将煤炭配方存入系统连接的合成器后,在燃料导出器中安装此升级模块.当系统煤炭耗尽时,合成升级会让系统自动生产更多煤炭!"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "30700E99091B712A"
			min_width: 300
			rewards: [
				{
					id: "085EEF00946EFDB6"
					item: "refinedstorage:regulator_upgrade"
					type: "item"
				}
				{
					id: "684D20A983D190DB"
					item: "refinedstorage:crafting_upgrade"
					type: "item"
				}
				{
					id: "3D7CA9D37CFD6B43"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			tasks: [
				{
					id: "33CE02A9C852D835"
					item: "refinedstorage:regulator_upgrade"
					type: "item"
				}
				{
					id: "37BB3E6C1BF5560C"
					item: "refinedstorage:crafting_upgrade"
					type: "item"
				}
			]
			title: "界面物品调控"
			x: -8.5d
			y: -6.0d
		}
		{
			dependencies: ["65C8A43FEDBA3835"]
			description: ["&9合成监控器&r接入系统后,可实时查看当前合成队列中的物品.\\n\\n当需要取消故障或卡住的合成任务时,这个设备必不可少."]
			id: "7044EAB5EDF32BBC"
			rewards: [
				{
					id: "70D5957342CD81E6"
					table_id: 4001436279668650237L
					type: "random"
				}
				{
					id: "566184EFEA58D66A"
					table_id: 3567941291661635734L
					type: "random"
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "79FEA8AA18E5ED45"
				item: "refinedstorage:crafting_monitor"
				type: "item"
			}]
			title: "合成队列监控"
			x: 1.0d
			y: -6.5d
		}
		{
			dependencies: ["01F18150EC923482"]
			description: ["扳手具有两大功能:\\n-> 旋转方块 \\n-> 拆除&d精致存储&f的外壳.\\n\\n使用时潜行并&a右键点击&f即可."]
			id: "1187F9D4F4E0D254"
			subtitle: "旋转吧小方块"
			tasks: [{
				id: "5EF5F78F14C5BEC0"
				item: "refinedstorage:wrench"
				type: "item"
			}]
			title: "扳手"
			x: -6.0d
			y: -1.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若你看到本提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "22BB192B1958B894"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "018045F124343014"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "1A1CB7786D31C490"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -7.5d
			y: -2.5d
		}
	]
	title: "&d精致存储&f"
}
