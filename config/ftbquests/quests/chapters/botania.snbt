{
	default_hide_dependency_lines: false
	default_min_width: 200
	default_quest_shape: ""
	filename: "botania"
	group: "02FE661031A105D8"
	icon: "botania:lexicon"
	id: "1883B79BDB2AAE5D"
	images: [{
		height: 0.3d
		hover: ["合成&aATM之星&f所需材料"]
		image: "allthetweaks:item/atm_star"
		rotation: 0.0d
		width: 0.3d
		x: 24.0d
		y: 1.8d
	}]
	order_index: 4
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "586AF33A5298B77D"
			rewards: [{
				id: "63281A9275BE311F"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4E6AF23CA82F1C32"
				item: "botania:white_mystical_flower"
				type: "item"
			}]
			x: 5.0d
			y: -4.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "3A8286DF6F667F74"
			rewards: [{
				id: "187D54D9F8554BFF"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4A5B6A054BC2618A"
				item: "botania:orange_mystical_flower"
				type: "item"
			}]
			x: 3.5d
			y: -5.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "520E3DAD6C87B989"
			rewards: [{
				id: "34D6ADEBEA37C6C9"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "3590A9F43523CB5F"
				item: "botania:magenta_mystical_flower"
				type: "item"
			}]
			x: 6.5d
			y: -5.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "6B2A61833A05C56E"
			rewards: [{
				id: "1604784A1ACC60E4"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "668A0B25AB8A7D66"
				item: "botania:light_blue_mystical_flower"
				type: "item"
			}]
			x: 2.5d
			y: -3.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "573838380230C65D"
			rewards: [{
				id: "3504F4736EEDF2C6"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "11B83581781F5029"
				item: "botania:yellow_mystical_flower"
				type: "item"
			}]
			x: 5.5d
			y: -5.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "3C16EB51FA4CA1D9"
			rewards: [{
				id: "08B4BC73CBCACDC9"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4117B65D86284D80"
				item: "botania:lime_mystical_flower"
				type: "item"
			}]
			x: 3.0d
			y: -4.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "5295793DD0FBEC3D"
			rewards: [{
				id: "2E9165F4BEC99E2B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "1BD13B156A778B94"
				item: "botania:pink_mystical_flower"
				type: "item"
			}]
			x: 6.0d
			y: -6.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7C757F654BD74016"
			rewards: [{
				id: "0269B8785074D081"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6D8B27FE2BAB27FF"
				item: "botania:gray_mystical_flower"
				type: "item"
			}]
			x: 4.5d
			y: -5.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "68CB1D1BAD343540"
			rewards: [{
				id: "34F3AFC0C00CA4E7"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "1D7C76D9F6688247"
				item: "botania:light_gray_mystical_flower"
				type: "item"
			}]
			x: 7.0d
			y: -4.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7BFCD0822FC37F50"
			rewards: [{
				id: "75BD2F537F37A95F"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "0F7774409C025470"
				item: "botania:cyan_mystical_flower"
				type: "item"
			}]
			x: 7.5d
			y: -3.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "196C47DDEEE313B4"
			rewards: [{
				id: "701E439C1816BC84"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "0A3D840D77B31838"
				item: "botania:purple_mystical_flower"
				type: "item"
			}]
			x: 3.5d
			y: -3.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "6CA3A5E801C82C19"
			rewards: [{
				id: "63E5B4F2FC93096F"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "5FE74EB95A4EA4D1"
				item: "botania:blue_mystical_flower"
				type: "item"
			}]
			x: 4.0d
			y: -4.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "0B59E29219F4588C"
			rewards: [{
				id: "168CC876146C3AB7"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "7FA8567C2B70BA43"
				item: "botania:brown_mystical_flower"
				type: "item"
			}]
			x: 6.0d
			y: -4.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "781B75C63237C8E3"
			rewards: [{
				id: "1153BCE864466F45"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "556DC0BEDA4BB8E3"
				item: "botania:red_mystical_flower"
				type: "item"
			}]
			x: 4.0d
			y: -6.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "28A1E86651472522"
			rewards: [{
				id: "3D9818D12990102D"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "1CC547383983CA1C"
				item: "botania:black_mystical_flower"
				type: "item"
			}]
			x: 6.5d
			y: -3.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "2AE020FE64D401F5"
			rewards: [{
				id: "37DC08484A15D4D1"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "175FDE59183B85E2"
				item: "botania:green_mystical_flower"
				type: "item"
			}]
			x: 5.0d
			y: -6.0d
		}
		{
			dependencies: ["79BE48D56622542F"]
			description: [
				"&a白雏菊&f&r是您将制作的首批花朵之一!"
				""
				"此花朵可将周围8格内的&2原木&r或&9石头&r分别转化为&2活木&r或&9活石&r.转化范围仅限于花朵周围1格半径内."
			]
			hide_until_deps_visible: false
			id: "4E02DC8A474A4A2F"
			rewards: [
				{
					id: "1F36B10E3BEBA932"
					item: "botania:pure_daisy"
					random_bonus: 1
					type: "item"
				}
				{
					id: "25024DCB06A60854"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "53794EAD5CE56471"
				item: "botania:pure_daisy"
				type: "item"
			}]
			title: "我们的第一朵花"
			x: 7.0d
			y: 2.5d
		}
		{
			dependencies: ["4E02DC8A474A4A2F"]
			description: ["使用&b白雏菊&f&r将石头转化为活石!"]
			id: "23ADD20D9B1AE0F3"
			rewards: [
				{
					count: 4
					id: "426D86B36BE8B608"
					item: "minecraft:stone"
					random_bonus: 4
					type: "item"
				}
				{
					id: "4D7BE00A8ADC513A"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				count: 8L
				id: "66BBC650D92AD2D0"
				item: "botania:livingrock"
				type: "item"
			}]
			title: "&7活石"
			x: 7.0d
			y: 1.0d
		}
		{
			dependencies: ["4E02DC8A474A4A2F"]
			description: ["使用&b白雏菊&f&r将原木转化为活木!"]
			id: "597E4D997A20736D"
			rewards: [
				{
					count: 4
					id: "4DADC5BC88012019"
					item: "minecraft:oak_log"
					random_bonus: 4
					type: "item"
				}
				{
					id: "7DBF2C3A190ED049"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				count: 8L
				id: "3CC2E22FFC03582A"
				item: "botania:livingwood_log"
				type: "item"
			}]
			title: "&2活木"
			x: 7.0d
			y: 4.0d
		}
		{
			dependencies: ["23ADD20D9B1AE0F3"]
			description: [
				"用我们制作的&b活石&r,现在可以制作&9魔力池&f&r."
				""
				"&a魔力池&f是我们储存生成魔力的容器.当其中储存足够魔力后,我们就能开始将特定资源转化为魔力资源,如魔力钢、&a魔力钻石&f.制作这些资源时,只需将物品投入存有足够魔力的池中."
				""
				"要&b生成魔力&r,您需要制作&2产能花&f&r,例如&9水绣球&r或&c末影焰&r."
				""
				"注意:&a魔力池&f可储存巨量魔力.手持&2森林法杖&f&r查看池子即可显示当前魔力值."
			]
			hide_until_deps_visible: false
			id: "7A359C1F5E041C4F"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "01DBBD8365FD6B97"
					table_id: 5897485726455770200L
					type: "random"
				}
				{
					id: "3F5C579642C1615A"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			size: 1.25d
			tasks: [
				{
					id: "57D1954039E03CDB"
					item: "botania:mana_pool"
					type: "item"
				}
				{
					id: "558649C69096D2D7"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "botania:hydroangeas"
								}
								{
									Count: 1b
									id: "botania:endoflame"
								}
							]
						}
					}
					title: "初始 &a产能花&f"
					type: "item"
				}
			]
			title: "&b魔力"
			x: 9.0d
			y: 1.7999999999999994d
		}
		{
			dependencies: [
				"7A359C1F5E041C4F"
				"205BBDFBCA582E3B"
			]
			description: [
				"将特定物品投入&b魔力池&f&r,即可用&9魔力&r进行灌注."
				""
				"所需魔力值请通过JEI查看配方!"
				""
				"小技巧:若魔力充足,可直接制作魔力钢或&a魔力钻石&f方块以节省时间!"
			]
			hide_until_deps_visible: false
			id: "70C5A902C0D26628"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0AD8DE07B46AF042"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "1F416DEEB92E89D8"
					type: "xp"
					xp: 50
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "43C86CA84DC14E8A"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "botania:manasteel_ingot"
							}
							{
								Count: 1b
								id: "botania:mana_pearl"
							}
							{
								Count: 1b
								id: "botania:mana_diamond"
							}
							{
								Count: 1b
								id: "botania:mana_string"
							}
							{
								Count: 1b
								id: "botania:mana_powder"
							}
							{
								Count: 1b
								id: "botania:quartz_mana"
							}
							{
								Count: 1b
								id: "botania:manasteel_block"
							}
							{
								Count: 1b
								id: "botania:mana_glass"
							}
							{
								Count: 1b
								id: "botania:mana_diamond_block"
							}
						]
					}
				}
				title: "魔力灌注材料"
				type: "item"
			}]
			title: "制作魔力灌注资源"
			x: 12.5d
			y: 2.5d
		}
		{
			description: [
				"&a&d植物魔法&f&r是伪装成魔法模组的科技模组,内含大量酷炫饰品和玩具!"
				""
				"开启&d植物魔法&f之旅需要大量&9神秘花&f&r!"
				""
				"更多物品信息请查阅&a植物魔法辞典&d&f&r."
			]
			id: "13D401048A926A74"
			rewards: [
				{
					id: "79AC804008C918D8"
					item: "botania:lexicon"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "7A10DF6D1C9627FC"
					table_id: 5897485726455770200L
					type: "random"
				}
				{
					id: "19ED2A28735EC524"
					item: "botania:flower_bag"
					type: "item"
				}
			]
			shape: "octagon"
			size: 1.5d
			tasks: [{
				id: "51EFBB115AE2746A"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "botania:mystical_flowers"
					}
				}
				title: "任意 #植物魔法:神秘花"
				type: "item"
			}]
			title: "欢迎来到&a&d植物魔法&f"
			x: 5.0d
			y: 0.0d
		}
		{
			dependencies: ["13D401048A926A74"]
			description: [
				"现在有了基础花朵,我们可以用&9花药台&f&r将普通植物转化为功能性产能植物!"
				""
				"要激发&a神秘花&f花瓣的能量,需先向花药台注入&b水&r.可通过&a右键点击&f水桶或直接将水桶扔进花药台完成."
				""
				"制作物品时,只需将配方材料投入花药台.&a空手右键点击&f也可取出物品."
				""
				"配方完成后20秒内,&a空手右键点击&f可自动填充上次配方材料,便于批量制作!"
			]
			id: "79BE48D56622542F"
			min_width: 250
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4646649853967E6F"
					table_id: 5897485726455770200L
					type: "random"
				}
				{
					id: "3CAE6C7AC360FDC9"
					type: "xp"
					xp: 10
				}
			]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "6BBFC01F3C0CC252"
				item: "botania:apothecary_default"
				type: "item"
			}]
			x: 5.0d
			y: 2.5d
		}
		{
			dependencies: ["13D401048A926A74"]
			description: ["你可以制作一些&a&a花肥&f&r,它们的作用类似于骨粉,但专用于&d植物魔法&f的花朵!"]
			hide_until_deps_visible: false
			id: "371E5E1E435E41AA"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0A3F5AADD4540725"
					table_id: 5897485726455770200L
					type: "random"
				}
				{
					id: "3070CD8684E71599"
					type: "xp"
					xp: 10
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "32655EB912B77D13"
				item: "botania:fertilizer"
				type: "item"
			}]
			x: 5.0d
			y: -2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			description: ["&c炽焰花&r会吸收附近掉落的任何可燃物品或方块,每次一个.然后将其燃烧以产生&b魔力&r."]
			id: "177658CA70DB0C89"
			rewards: [
				{
					id: "6B5C1767E42B69B9"
					item: "minecraft:coal_block"
					type: "item"
				}
				{
					id: "3E0E8CD62EB71566"
					type: "xp"
					xp: 10
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "0F3DB7859AC1FA8A"
				item: "botania:endoflame"
				type: "item"
			}]
			title: "&c燃烧物品&r生成魔力"
			x: 1.5d
			y: 4.0d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			description: [
				"这是一种杂项花朵,用于测量你在&a魔力池&f中的盈亏情况."
				""
				"使用时将&d魔力星&r放置在魔力池旁,观察花朵颜色变化.若发出红光表示池子处于亏损状态,若发出蓝光则表示有盈余!"
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "0F9DABC15ED589CB"
			optional: true
			rewards: [{
				id: "1757180C5FC5A320"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "3CCC0EF8316FC97D"
				item: "botania:manastar"
				type: "item"
			}]
			x: -1.0d
			y: -1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "11671E9021F8FDDE"
			optional: true
			rewards: [{
				id: "4BC31CE9FE930876"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "5BAA16D7598FF17A"
				item: "botania:dandelifeon"
				type: "item"
			}]
			x: -7.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "78D96736B0B7F12A"
			optional: true
			rewards: [{
				id: "110F28702A946AC5"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "1B4F0D2059B8026B"
				item: "botania:entropinnyum"
				type: "item"
			}]
			x: -5.0d
			y: 4.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "6E7A4F653B0C1383"
			optional: true
			rewards: [{
				id: "7AA7BD3489BDE052"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "064F00766BFAC083"
				item: "botania:gourmaryllis"
				type: "item"
			}]
			x: -2.0d
			y: -2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			description: [
				"这些花朵通过消耗周围3x3区域内的&b水源方块&r产生魔力.效率缓慢,最终会凋零."
				""
				"下方是基础配置示例."
				""
				"{image:atm:textures/questpics/botania/hydrosample.png width:150 height:125 align:1}"
			]
			hide_dependency_lines: false
			hide_until_deps_visible: false
			id: "5D4F9B54A54EEBD6"
			rewards: [
				{
					id: "7290957F0169C06E"
					item: "botania:hydroangeas"
					type: "item"
				}
				{
					id: "50C0DA8F19F519F4"
					item: "cookingforblockheads:sink"
					type: "item"
				}
				{
					id: "1FCDEEAB8174E4E1"
					type: "xp"
					xp: 10
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "361773BACA86686E"
				item: "botania:hydroangeas"
				type: "item"
			}]
			title: "使用&9水&r生成魔力"
			x: 1.5d
			y: 1.0d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "1CCB55703C1C3511"
			optional: true
			rewards: [{
				id: "391C5072F3EFA4FA"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "5E904CD770B69066"
				item: "botania:kekimurus"
				type: "item"
			}]
			x: -1.0d
			y: 6.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7D5981F097FD4237"
			optional: true
			rewards: [{
				id: "6D7842714E46455E"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "24A17B911DF7B6A3"
				item: "botania:munchdew"
				type: "item"
			}]
			x: -6.0d
			y: 3.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "350C732A4A76C93F"
			optional: true
			rewards: [{
				id: "7CBE0D6B85E4C698"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "2A82329EFC75B3CB"
				item: "botania:narslimmus"
				type: "item"
			}]
			x: -2.0d
			y: 7.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "2E70AA1EAFA36A82"
			optional: true
			rewards: [{
				id: "2D82D35167A0B822"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "2FDD86AE03028860"
				item: "botania:rafflowsia"
				type: "item"
			}]
			x: -3.0d
			y: 6.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "4C19AD490502D252"
			optional: true
			rewards: [{
				id: "2DB6C4E435C1BEBB"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "09C57FE747E4FDB1"
				item: "botania:rosa_arcana"
				type: "item"
			}]
			x: -4.0d
			y: 5.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "4E5F2803B20A3E93"
			optional: true
			rewards: [{
				id: "1240970AE089DA75"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "27A5137AD2895362"
				item: "botania:shulk_me_not"
				type: "item"
			}]
			x: -3.0d
			y: -1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "3E80563D8E4C5398"
			optional: true
			rewards: [{
				id: "36D02AAACC5C913A"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "03C99E0B3F3B2800"
				item: "botania:spectrolus"
				type: "item"
			}]
			x: -5.0d
			y: 0.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "66B7E4451AC4EF70"
			optional: true
			rewards: [{
				id: "6408A878638B4A4F"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "6D6367DDF0FF15EB"
				item: "botania:thermalily"
				type: "item"
			}]
			x: -6.0d
			y: 1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "6E90B369F414EB45"
			optional: true
			rewards: [{
				id: "705081BA53D1AB47"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "0B7875FF8CA719B7"
				item: "botania:agricarnation"
				type: "item"
			}]
			x: -5.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "136567EA7DC27DC7"
			optional: true
			rewards: [{
				id: "48443AF7C7600026"
				type: "xp"
				xp: 50
			}]
			shape: "square"
			tasks: [{
				id: "3AEB80A6F92651FF"
				item: "botania:bellethorn"
				type: "item"
			}]
			x: -4.0d
			y: -0.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7A35D3039E47E24D"
			optional: true
			rewards: [{
				id: "70C244D17A41AD07"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "54DDD30C2FB19B64"
				item: "botania:bergamute"
				type: "item"
			}]
			x: -4.0d
			y: 1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "46959DF790DA9312"
			optional: true
			rewards: [{
				id: "692FFE927E2BDDA3"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "588D1526F8E13BF7"
				item: "botania:bubbell"
				type: "item"
			}]
			x: -3.0d
			y: 1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7BB2EE5D33229891"
			optional: true
			rewards: [{
				id: "0300495737AD2924"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "1F18CE807E81A7EC"
				item: "botania:clayconia"
				type: "item"
			}]
			x: -2.0d
			y: 1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			dependency_requirement: "one_started"
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7D72A3EB294A658C"
			optional: true
			rewards: [{
				id: "594188DD54DE3EE4"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "5BC990D9FA30C81B"
				item: "botania:daffomill"
				type: "item"
			}]
			x: -2.0d
			y: 0.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "37B5FE0302A90F35"
			optional: true
			rewards: [{
				id: "06C6C0B7808924D6"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "0A4C0E499F9D2224"
				item: "botania:dreadthorn"
				type: "item"
			}]
			x: -3.0d
			y: 0.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "256DCEA4B27608E2"
			optional: true
			rewards: [{
				id: "4AD76BBE8D4AF6B3"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "5CEAA28BFDDFCC5F"
				item: "botania:exoflame"
				type: "item"
			}]
			x: -1.0d
			y: 1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "1025A352CF7131FD"
			optional: true
			rewards: [{
				id: "352D8C9B91ABE45F"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "7318E29D6291C991"
				item: "botania:fallen_kanade"
				type: "item"
			}]
			x: 0.0d
			y: 1.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "18BD20B1D713FEBC"
			optional: true
			rewards: [{
				id: "7F7D3E99B055C3B1"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "6826F3DB5C759EF2"
				item: "botania:heisei_dream"
				type: "item"
			}]
			x: -1.0d
			y: 4.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "305EDB583A9380FC"
			optional: true
			rewards: [{
				id: "3045B98D9536DBE8"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "74F64B1A74B30D28"
				item: "botania:hopperhock"
				type: "item"
			}]
			x: 1.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "79BC197AA5999EF7"
			optional: true
			rewards: [{
				id: "4A2357FA548F8D36"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "53DFF5E0788632CF"
				item: "botania:hyacidus"
				type: "item"
			}]
			x: 0.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "3CA42D084CD48E4D"
			optional: true
			rewards: [{
				id: "576DE11A620A0F0D"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "4F3C22369EFE4771"
				item: "botania:jaded_amaranthus"
				type: "item"
			}]
			x: -1.0d
			y: 0.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7B6D9EDC4F06104C"
			optional: true
			rewards: [{
				id: "5430B07D113C63BC"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "31FE4492CDAB3619"
				item: "botania:jiyuulia"
				type: "item"
			}]
			x: -2.0d
			y: -0.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "474551E50F99D8AC"
			optional: true
			rewards: [{
				id: "2DF883FDE6570848"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "4571B438D249F3D9"
				item: "botania:loonium"
				type: "item"
			}]
			x: -3.0d
			y: 4.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "018B2BE3D7698107"
			optional: true
			rewards: [{
				id: "3E6FA02C12DB0E83"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "7EB485F46B1A3DAB"
				item: "botania:marimorphosis"
				type: "item"
			}]
			x: -4.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "334A0673FD07BF87"
			optional: true
			rewards: [{
				id: "0902C8F0AAD4BB00"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "7B0A4890B1EA4866"
				item: "botania:medumone"
				type: "item"
			}]
			x: -3.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "58B0D3B672042B20"
			optional: true
			rewards: [{
				id: "20318101FE4BAE80"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "5365DC5EBDAB7F1F"
				item: "botania:orechid"
				type: "item"
			}]
			x: -2.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "00E5E6BCEB716581"
			optional: true
			rewards: [{
				id: "746FEFBEDAFAB430"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "4A5E3FD182F32743"
				item: "botania:orechid_ignem"
				type: "item"
			}]
			x: -1.0d
			y: 2.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "067FDF87F4703205"
			optional: true
			rewards: [{
				id: "5A971A64D2C62A72"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "12DA09A6C97199B7"
				item: "botania:pollidisiac"
				type: "item"
			}]
			x: -2.0d
			y: 4.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "0324A530DF0D9D11"
			optional: true
			rewards: [{
				id: "722B7CC563487E91"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "603469596723B2A8"
				item: "botania:rannuncarpus"
				type: "item"
			}]
			x: -4.0d
			y: 3.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "473B72A0BC98A469"
			optional: true
			rewards: [{
				id: "7F3497B3CCFBEBB6"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "18EDE2C0E6133C84"
				item: "botania:solegnolia"
				type: "item"
			}]
			x: -3.0d
			y: 3.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "04606CC66363B7A2"
			optional: true
			rewards: [{
				id: "52FEDB72D380FC4A"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "3C55D19D9BD9C886"
				item: "botania:spectranthemum"
				type: "item"
			}]
			x: -2.0d
			y: 3.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "7A4335AEE81D23D8"
			optional: true
			rewards: [{
				id: "52C80B5DA125A76A"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "2FDEF1E13E2AA8C8"
				item: "botania:tangleberrie"
				type: "item"
			}]
			x: -1.0d
			y: 3.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "5151959325E1F10B"
			optional: true
			rewards: [{
				id: "47513B880DB6A741"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "349D8E7F10ADA938"
				item: "botania:tigerseye"
				type: "item"
			}]
			x: 0.0d
			y: 3.5d
		}
		{
			dependencies: ["7B3FAF5CA4DD217C"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "2E0BCC25FB030043"
			optional: true
			rewards: [{
				id: "0F201F298BA39C53"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "3DD0065C7C329AA1"
				item: "botania:vinculotus"
				type: "item"
			}]
			x: -2.0d
			y: 5.5d
		}
		{
			dependencies: ["7A359C1F5E041C4F"]
			description: ["&0&a黑莲花&f&r仅能在宝箱中找到,可投入非空的&a魔力池&f中提供大量浓缩魔力."]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "5E2AA5695D1F21D7"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "172AEB481DEA9F1E"
				table_id: 5354288240016506720L
				type: "random"
			}]
			size: 0.5d
			tasks: [{
				id: "490EAAA3703A576F"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "botania:black_lotus"
							}
							{
								Count: 1b
								id: "botania:blacker_lotus"
							}
						]
					}
				}
				title: "&a黑莲花&f"
				type: "item"
			}]
			title: "稀有的魔力灌注莲花"
			x: 10.0d
			y: 2.5d
		}
		{
			dependencies: ["205BBDFBCA582E3B"]
			description: [
				"&2&a魔力发射器&f&r通过发射&a魔力脉冲&f来引导魔力流向."
				""
				"使用&2&a森林法杖&f&r将其绑定至方块可设定发射方向.持杖时还能查看魔力缓冲器及&a魔力脉冲&f."
			]
			id: "1C48F2612F2FA828"
			rewards: [
				{
					id: "1045A682FCD7A70E"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "5253A1CE709CFB4F"
					table_id: 2930089542465018839L
					type: "random"
				}
			]
			tasks: [{
				id: "0B0D56F682F2791A"
				item: "botania:mana_spreader"
				type: "item"
			}]
			title: "引导魔力"
			x: 9.0d
			y: 4.5d
		}
		{
			dependencies: ["597E4D997A20736D"]
			description: [
				"&2&a森林法杖&f&r是探索&d植物魔法&f的必备物品."
				""
				"法杖有&a绑定&r和&9功能&r两种模式."
				""
				"&a&a绑定模式&f&r用于连接魔法花卉与方块:潜行&a右击&f首个目标后,再潜行&a右击&f第二个目标即可建立连接."
				""
				"&9&a工作模式&f&r可像扳手一样旋转方块."
			]
			hide_until_deps_visible: false
			id: "205BBDFBCA582E3B"
			rewards: [{
				id: "2240BDE8F6BEE2D2"
				type: "xp"
				xp: 25
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "59FB36F10FBA2E3D"
				item: {
					Count: 1
					id: "botania:twig_wand"
					tag: {
						color1: 0
						color2: 0
					}
				}
				type: "item"
			}]
			title: "&d植物魔法&f的万能工具"
			x: 9.0d
			y: 3.2d
		}
		{
			dependencies: ["0631F054A9B97A40"]
			description: [
				"可在&2&a魔力发射器&f&r前端加装&9&a魔力透镜&f&r进行升级,但基础透镜无特殊效果."
				""
				"需通过合成升级透镜来改变魔力脉冲效果.可用染料染色,或配合&a魔力珍珠&f制作彩虹透镜."
				""
				"将特定符文与物品结合透镜可强化发射器.用&a&a粘液球&f&r融合两个透镜还能制作&9复合透镜&r,叠加效果."
			]
			id: "6FBE0BF8A7ADBB26"
			min_width: 300
			rewards: [{
				id: "3B601334FFB56069"
				type: "xp"
				xp: 100
			}]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "3E76CBAA617FAA35"
				item: "botania:lens_normal"
				type: "item"
			}]
			title: "升级&a魔力发射器&f"
			x: 12.5d
			y: 8.0d
		}
		{
			dependencies: ["7A359C1F5E041C4F"]
			description: [
				"&b&a魔力分配器&f&r可将输入魔力脉冲分流至多个&a魔力池&f."
				""
				"&d魔力火花&r用于定向传输魔力,是&d植物魔法&f进阶必备."
				""
				"在&a魔力池&f上方放置火花,再在目标方块放置另一个,即可实现\"无线传输\"."
				""
				"用&2&a森林法杖&f&r潜行&a右击&f可移除火花."
			]
			id: "2CBBE8EF07B1D3DA"
			shape: "diamond"
			tasks: [
				{
					id: "6979609EF9040D79"
					item: "botania:mana_distributor"
					type: "item"
				}
				{
					id: "4EA054C6C0255900"
					item: "botania:spark"
					type: "item"
				}
			]
			title: "操控魔力存储"
			x: 8.5d
			y: 0.5d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			description: ["将&d&a炼金催化器&f&r置于&b&a魔力池&f&r下方可进行&9炼金术&r,例如将腐肉转化为皮革等实用物品."]
			id: "32DF4BC46620F952"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7A8D3CAAA3A5CFC1"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "5105A5CC9AE334EF"
					type: "xp"
					xp: 25
				}
			]
			shape: "square"
			tasks: [{
				id: "0B5B67B066E672CD"
				item: "botania:alchemy_catalyst"
				type: "item"
			}]
			x: 14.0d
			y: 3.0d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: ["与&a炼金催化器&f类似,&9&a炼造催化器&f&r置于&a魔力池&f下方时可解锁物质转化配方."]
			id: "1DCFC67A8E3DCA2C"
			rewards: [{
				exclude_from_claim_all: true
				id: "77862A79FD065930"
				table_id: 8234116511213485813L
				type: "random"
			}]
			tasks: [{
				id: "258C1DEBF18D2EBA"
				item: "botania:conjuration_catalyst"
				type: "item"
			}]
			x: 19.5d
			y: 4.0d
		}
		{
			dependencies: ["7A359C1F5E041C4F"]
			description: [
				"&9&a魔力石板&f&r是可携带的&a魔力池&f!"
				""
				"将石板投入&a魔力池&f可进行魔力存取.手持&2&a森林法杖&f&r潜行&a右击&f可切换存取模式."
				""
				"注:石板不会自然消失."
			]
			id: "2162EA0216E27D68"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "37DAB54AE1E00EE1"
					table_id: 2930089542465018839L
					type: "random"
				}
				{
					id: "45AC19C3D395E86B"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [{
				id: "0ADA4C174CEF2EEE"
				item: "botania:mana_tablet"
				type: "item"
			}]
			title: "魔力池间传输魔力"
			x: 9.5d
			y: 0.5d
		}
		{
			dependencies: ["2162EA0216E27D68"]
			description: ["&9&a魔力之戒&f&r是可佩戴的&a魔力石板&f,可像饰品般&a装备&f."]
			id: "379AC75F99212089"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "425600519223D73F"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "2174A01222882E0A"
					type: "xp"
					xp: 50
				}
			]
			shape: "diamond"
			tasks: [{
				id: "088F2F276200B262"
				item: "botania:mana_ring"
				type: "item"
			}]
			x: 9.0d
			y: 0.0d
		}
		{
			dependencies: ["379AC75F99212089"]
			description: ["用泰拉钢锭升级&a魔力之戒&f可制成&d高阶&a魔力之戒&f&r,魔力储量提升4倍."]
			id: "2F01BF021E1BE0A2"
			shape: "diamond"
			tasks: [{
				id: "3678AFFD31942CF3"
				item: "botania:mana_ring_greater"
				type: "item"
			}]
			x: 9.0d
			y: -1.0d
		}
		{
			dependencies: ["6A56574BC562B227"]
			description: ["用泰拉钢锭升级&a光环之戒&f可大幅提升魔力生成速率."]
			id: "70C25BFA6FFA1799"
			rewards: [
				{
					id: "3D5C2F4B20A5829D"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "50D6037CA594C823"
					table_id: 5354288240016506720L
					type: "random"
				}
			]
			tasks: [{
				id: "34385B9406728C43"
				item: "botania:aura_ring_greater"
				type: "item"
			}]
			x: 13.5d
			y: 6.5d
		}
		{
			dependencies: ["0631F054A9B97A40"]
			description: ["佩戴&9&a光环之戒&f&r时会持续生成微量魔力,并自动存入背包中的储魔物品."]
			hide_dependency_lines: false
			id: "6A56574BC562B227"
			rewards: [
				{
					id: "4B467BD98812A91F"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "675B5F81B4D13A7F"
					table_id: 5354288240016506720L
					type: "random"
				}
			]
			tasks: [{
				id: "04EDE2305855A16B"
				item: "botania:aura_ring"
				type: "item"
			}]
			x: 13.5d
			y: 5.5d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			description: [
				"虽然&b魔力编织长袍&r防御力一般,但套装效果极其强大!"
				""
				"穿戴全套四件时,魔力工具/法杖的消耗降低.&a套装&f还能自动消耗背包魔力修复耐久."
			]
			id: "05A845C811A9A4A0"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5129E241C4B02A2D"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "31C822F989CE89D1"
					type: "xp"
					xp: 100
				}
			]
			shape: "square"
			tasks: [
				{
					id: "6F1EBC95C7BC93DF"
					item: "botania:manaweave_cloth"
					type: "item"
				}
				{
					id: "380292B73C32D32D"
					item: {
						Count: 1
						id: "botania:manaweave_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "0E65D022473C0996"
					item: {
						Count: 1
						id: "botania:manaweave_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "75862C1E5E59C465"
					item: {
						Count: 1
						id: "botania:manaweave_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "0E6BE4209A3BFC82"
					item: {
						Count: 1
						id: "botania:manaweave_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "魔力编织长袍"
			x: 14.0d
			y: 2.0d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			description: ["与&a铁甲&f属性相近,&9魔力钢护甲&r拥有更强的附魔能力与耐久度,且能自动消耗使用者背包魔力修复."]
			hide_until_deps_visible: false
			id: "64DB69F741EA5BEE"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3C241581C56EEA14"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "02369900BB48EB59"
					type: "xp"
					xp: 50
				}
			]
			tasks: [
				{
					id: "731919E7ED474146"
					item: {
						Count: 1
						id: "botania:manasteel_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "6ABD8D027FD1C170"
					item: {
						Count: 1
						id: "botania:manasteel_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "41EBB5AAF5F27DCA"
					item: {
						Count: 1
						id: "botania:manasteel_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "248AD65A97AFB7DB"
					item: {
						Count: 1
						id: "botania:manasteel_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "魔力钢护甲"
			x: 12.5d
			y: -0.5d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			id: "7F6B1E12FF35C9B4"
			rewards: [{
				id: "42C319B92CE57B4F"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "15721B09FEE7A75E"
				item: {
					Count: 1
					id: "botania:manasteel_pick"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 13.5d
			y: -1.0d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			id: "087A99014FE8BAC2"
			rewards: [{
				id: "308B2A25CF474BDA"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "443C2EF5FFE8D265"
				item: {
					Count: 1
					id: "botania:manasteel_shovel"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 11.5d
			y: 0.0d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			id: "7E0A8892ABC7C368"
			rewards: [{
				id: "5FAF2B25C21AE1C5"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "541045912841BC47"
				item: {
					Count: 1
					id: "botania:manasteel_axe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 12.5d
			y: -1.5d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			id: "609284EA5EDD6A64"
			rewards: [{
				id: "68E57DDA535C30D4"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "3AD34C5C835969B6"
				item: {
					Count: 1
					id: "botania:manasteel_hoe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 13.5d
			y: 0.0d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			id: "07F65584D1525191"
			rewards: [{
				id: "43F14C9FC6B8D8A8"
				type: "xp"
				xp: 50
			}]
			tasks: [{
				id: "76BA8B9CAF7F42CF"
				item: {
					Count: 1
					id: "botania:manasteel_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 11.5d
			y: -1.0d
		}
		{
			dependencies: ["23A2865FBE7831AB"]
			description: ["与魔力钢护甲类似,&a&a泰拉钢护甲&f&r能自动修复,耐久度堪比钻石护甲."]
			hide_until_deps_visible: false
			id: "10F2044D080D6209"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4A91D22C8DE9DA54"
					table_id: 8273749113129900182L
					type: "random"
				}
				{
					id: "652C453A738FEC70"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "321F81A5090D1093"
					table_id: 8273749113129900182L
					type: "random"
				}
			]
			tasks: [
				{
					id: "7502EF4CC54F1E6E"
					item: {
						Count: 1
						id: "botania:terrasteel_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "30BFF28FAC3C6862"
					item: {
						Count: 1
						id: "botania:terrasteel_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "1295FFF7C7E5C731"
					item: {
						Count: 1
						id: "botania:terrasteel_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "69DDDE2C4D586735"
					item: {
						Count: 1
						id: "botania:terrasteel_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&a泰拉钢护甲&f"
			x: 16.5d
			y: -0.5d
		}
		{
			dependencies: ["23A2865FBE7831AB"]
			description: ["&2&a泰拉断裂者&f&r可消耗魔力瞬间砍倒整棵树."]
			id: "3C84D684B3A5D7B6"
			rewards: [
				{
					id: "3102E56C03312F24"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "326A5F913707136C"
					table_id: 8273749113129900182L
					type: "random"
				}
			]
			tasks: [{
				id: "1B5BCFFB27A87601"
				item: {
					Count: 1
					id: "botania:terra_axe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 16.5d
			y: -1.5d
		}
		{
			dependencies: ["23A2865FBE7831AB"]
			description: ["&2&a泰拉之刃&f&r具有&a钻石剑&f的伤害,有时会发射与近战伤害等同的能量光束."]
			id: "24C0F267B330CD23"
			rewards: [
				{
					id: "570A696F06A33952"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "204F56C4F8ADFF81"
					table_id: 8273749113129900182L
					type: "random"
				}
			]
			tasks: [{
				id: "679F0AA9731DAF88"
				item: {
					Count: 1
					id: "botania:terra_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 15.5d
			y: 0.0d
		}
		{
			dependencies: ["23A2865FBE7831AB"]
			description: [
				"&9&a泰拉粉碎者&f&r不仅是镐子——投入&a魔力池&f时能像&a魔力石板&f般储存海量魔力(但无法释放)."
				""
				"工具内魔力值决定其等级(D最低,SS最高)."
				""
				"等级提升不加速挖掘,但会增强&b主动技能&r的范围效果(潜行&a右击&f切换).激活时根据等级扩大采掘范围,D级无效果."
				""
				"注:激活状态将持续消耗储存魔力."
			]
			id: "4143C1CF83DE922E"
			min_width: 300
			rewards: [
				{
					id: "55F2CC37949D63B0"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "47CD2C394D2DD21C"
					table_id: 8273749113129900182L
					type: "random"
				}
			]
			tasks: [{
				id: "65D28D69164FF766"
				item: {
					Count: 1
					id: "botania:terra_pick"
					tag: {
						Damage: 0
						mana: 9999
					}
				}
				type: "item"
			}]
			x: 17.5d
			y: 0.0d
		}
		{
			dependencies: ["23A2865FBE7831AB"]
			description: [
				"厌倦用铲子平整草地？试试&2大地之杖&r!"
				""
				"持续按住&a右击&f&a充能&f后,可消耗魔力将周围地形平整至使用者高度."
				""
				"注:被移除的方块不可恢复."
			]
			id: "2EB6088D4E85DA42"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "73689EA3C32824EF"
					table_id: 8273749113129900182L
					type: "random"
				}
				{
					id: "158F5E8E623CFFCA"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "1DCCF34086BB0191"
				item: "botania:terraform_rod"
				type: "item"
			}]
			title: "平整土地"
			x: 16.5d
			y: 4.5d
		}
		{
			dependencies: ["70C5A902C0D26628"]
			description: [
				"&9符文&r是&d植物魔法&f中许多高级合成配方的重要组件,它们需要在&a符文祭坛&f上制作."
				""
				"使用祭坛时,首先将所需符文的材料放置其上.可以通过&a右键点击&f或丢入物品完成.同时需要魔力供给,请确保有获取魔力的&a魔力发射器&f指向祭坛."
				""
				"放置完材料后,用魔杖悬停查看祭坛会显示合成进度.完成后,在祭坛上丢入一块活石,再用魔杖收取符文."
				""
				"注意:&a符文祭坛&f配方中使用的符文作为催化剂不会被消耗.与&a花药台&f类似,空手右键点击祭坛可重新填充上次配方使用的物品."
			]
			id: "0631F054A9B97A40"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "50F75BE1FA9127B8"
					table_id: 3061540174582862923L
					type: "random"
				}
				{
					id: "576C91544B6A673C"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "1B91D26F0A805561"
					item: "botania:runic_altar"
					type: "item"
				}
				{
					id: "5A06ED36DD9E9681"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "botania:rune_water"
								}
								{
									Count: 1b
									id: "botania:rune_fire"
								}
								{
									Count: 1b
									id: "botania:rune_earth"
								}
								{
									Count: 1b
									id: "botania:rune_air"
								}
								{
									Count: 1b
									id: "botania:rune_mana"
								}
							]
						}
					}
					title: "基础符文"
					type: "item"
				}
			]
			title: "符文制作"
			x: 12.5d
			y: 4.5d
		}
		{
			dependencies: ["0631F054A9B97A40"]
			description: ["在&a植物酿造台&f中使用药瓶、魔力和试剂,可以制作类似药水的&9魔法药剂&r."]
			id: "30F7B68093D76590"
			optional: true
			rewards: [
				{
					id: "1E29B9EBAF70CA6C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "7B105607C3C65B49"
					table_id: 5354288240016506720L
					type: "random"
				}
			]
			tasks: [{
				id: "4E9712523E4B94E8"
				item: "botania:brewery"
				type: "item"
			}]
			x: 11.5d
			y: 5.5d
		}
		{
			dependencies: ["30F7B68093D76590"]
			description: ["&2熏香&f可在&a植物酿造台&f中注入药剂效果.用打火石点燃&9香盘&f后,能以香盘为中心30格范围释放药剂效果,持续时间是液体药剂的60倍."]
			id: "0D9354ABA2AE2A11"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "68FFE65FDA2BB958"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "401FF6D8EC4E3C9B"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "0B549F7000B06C0D"
				item: "botania:incense_stick"
				type: "item"
			}]
			x: 11.5d
			y: 6.5d
		}
		{
			dependencies: ["0631F054A9B97A40"]
			description: ["制作&a泰拉钢&r需要先搭建多方块结构,首先要合成&9泰拉钢凝聚板&r."]
			id: "65238A6DCDDDFA0D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "589278A3E65A7788"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					exclude_from_claim_all: true
					id: "317543457A71EF99"
					table_id: 3061540174582862923L
					type: "random"
				}
				{
					id: "369F0385E3D1A45B"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "76F8BB9B8C181006"
				item: "botania:terra_plate"
				type: "item"
			}]
			title: "锻造更强金属"
			x: 14.5d
			y: 4.5d
		}
		{
			dependencies: ["65238A6DCDDDFA0D"]
			description: [
				"现在该搭建&9T.A.平台&r来制作&a泰拉钢&r了!"
				""
				"用5块活石与4块青金石按棋盘格摆放(如下图).将平台核心置于中央活石上即可完成!"
				""
				"泰拉钢需消耗大量魔力,需使用&9魔力火花&r.在&a魔力池&f上方布置多个火花,再于T.A.平台上放置一个来引导魔力."
				""
				"平台就绪后,向核心投掷&a魔力钢锭&f、&a魔力钻石&f和&a魔力珍珠&f即可开始合成."
				""
				"{image:atm:textures/questpics/botania/t_a_plate_base.png width:200 height:150 align:1}"
			]
			icon: "botania:terrasteel_ingot"
			id: "23A2865FBE7831AB"
			min_width: 400
			rewards: [
				{
					exclude_from_claim_all: true
					id: "08B1BD3A6822FB15"
					table_id: 8273749113129900182L
					type: "random"
				}
				{
					id: "5F4FB5C3386BFBE9"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [
				{
					count: 5L
					id: "70E6334F11A6C4C8"
					item: "botania:livingrock"
					type: "item"
				}
				{
					count: 4L
					id: "6DE2A53806A697FF"
					item: "minecraft:lapis_block"
					type: "item"
				}
				{
					id: "1DFC017C335D814E"
					item: "botania:terrasteel_ingot"
					type: "item"
				}
			]
			title: "&a合成&r &d泰拉钢"
			x: 16.5d
			y: 2.5d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: [
				"使用精灵材料可通过&d强化组件&r升级火花.手持强化组件&a右键点击&f火花即可升级.潜行状态下用魔杖&a右键点击&f可&a清除强化&f."
				""
				"扩散强化:使火花能抽取魔力池中的魔力为附近玩家的魔力容器充能."
				""
				"支配强化:使火花能从附近未强化火花的魔力池中抽取魔力."
				""
				"衰退强化:使火花会将自身魔力池中的魔力分配给附近未强化或扩散强化的火花."
				""
				"隔绝强化:阻止该火花与任何支配/衰退强化火花交互."
			]
			id: "5CB5F4CD521F6361"
			min_width: 300
			rewards: [{
				id: "02D21C31266770EA"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "28B797392467C851"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "botania:spark_upgrade_dispersive"
							}
							{
								Count: 1b
								id: "botania:spark_upgrade_dominant"
							}
							{
								Count: 1b
								id: "botania:spark_upgrade_recessive"
							}
							{
								Count: 1b
								id: "botania:spark_upgrade_isolated"
							}
						]
					}
				}
				title: "火花增幅器"
				type: "item"
			}]
			title: "火花强化"
			x: 21.5d
			y: 4.0d
		}
		{
			dependencies: ["1EF7664F5D8C5FF1"]
			description: [
				"虽然无法进入&d精灵界传送门&r,但可以通过向门内投掷特定物品进行&2精灵交易&f(每次交易都会消耗魔力).这些材料还能用于升级装备,比如制作&2精灵魔力发射器&f."
				""
				"向门内投入&a植物魔法辞典&d&f可升级为附有&e精灵知识&r的版本,获得更多&d植物魔法&f的奥秘."
			]
			icon: "botania:dragonstone"
			id: "3A20210242A1C865"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2FB7339358015C17"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "54485635405DA167"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "6A9E12C2483D4883"
					item: {
						Count: 1
						id: "botania:lexicon"
						tag: {
							"botania:elven_unlock": 1b
						}
					}
					type: "item"
				}
				{
					id: "5BD644B2A02DF9C0"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "botania:pixie_dust"
								}
								{
									Count: 1b
									id: "botania:quartz_elven"
								}
								{
									Count: 1b
									id: "botania:elf_glass"
								}
								{
									Count: 1b
									id: "botania:elementium_block"
								}
								{
									Count: 1b
									id: "botania:elementium_ingot"
								}
								{
									Count: 1b
									id: "botania:dreamwood_log"
								}
								{
									Count: 1b
									id: "botania:dreamwood"
								}
								{
									Count: 1b
									id: "botania:dragonstone_block"
								}
								{
									Count: 1b
									id: "botania:dragonstone"
								}
							]
						}
					}
					title: "亚尔夫海姆资源"
					type: "item"
				}
			]
			title: "精灵交易"
			x: 20.5d
			y: 2.5d
		}
		{
			dependencies: ["23A2865FBE7831AB"]
			description: [
				"建造&d精灵界传送门&r需先用8块活木、3块&a微光活木&f和&9&a精灵门核心&f&r搭建门框."
				""
				"门框完成后,需至少&d2个&a魔力池&f&r、大量魔力以及在池上放置&a&a自然水晶&f&r来激活.魔力池需位于核心11x11x11范围内."
				""
				"准备就绪后,用魔杖&a右击&f精灵核心即可激活传送门."
				""
				"注意:虽然激活需&a魔力池&f储备大量魔力,但传送门开启不消耗魔力.每次传送材料会消耗少量魔力,若不足则门户关闭."
				""
				"可参考&a《植物魔法&d辞典》&r辅助建造."
				""
				"{image:atm:textures/questpics/botania/elven_portal.png width:200 height:175 align:1}"
			]
			id: "1EF7664F5D8C5FF1"
			min_width: 500
			rewards: [
				{
					exclude_from_claim_all: true
					id: "36E4AC6637BA21F6"
					table_id: 8273749113129900182L
					type: "random"
				}
				{
					id: "2082E1DD4BF81D00"
					type: "xp"
					xp: 50
				}
			]
			shape: "octagon"
			tasks: [
				{
					id: "420577A05AAC9A18"
					item: "botania:alfheim_portal"
					type: "item"
				}
				{
					count: 2L
					id: "184D19EC756FBCD8"
					item: "botania:natura_pylon"
					type: "item"
				}
				{
					count: 3L
					id: "763D84A4488A3008"
					item: "botania:glimmering_livingwood_log"
					type: "item"
				}
				{
					count: 8L
					id: "08AECABD8E75A495"
					item: "botania:livingwood"
					type: "item"
				}
			]
			title: "开启传送门"
			x: 18.5d
			y: 2.5d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: [
				"后续旅程需要获取&6&a盖亚魂&f&r,需举行&9&a盖亚魔法仪式&f&r."
				""
				"你需要在&a活跃信标&r周围放置4个&a盖亚水晶&f,并准备一块泰拉钢锭.搭建完成后,潜行状态下用泰拉钢锭&a右键点击&f信标,准备迎接毕生难忘的战斗."
				""
				"若需搭建指导,可随时查阅&aLexica &d植物魔法&f&r手册.寻找&9&a盖亚魔法仪式&f&r章节获取帮助."
				""
				"{image:atm:textures/questpics/botania/gaia_ritual.png width:300 height:100 align:1}"
			]
			id: "04CE14D92603FC7A"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7B29C2347C48D513"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "2E6A747011B11C90"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					count: 4L
					id: "21810A64B844C70E"
					item: "botania:gaia_pylon"
					type: "item"
				}
				{
					id: "157BF2FA7A40BE0D"
					item: "minecraft:beacon"
					type: "item"
				}
			]
			title: "&a召唤&r &5&a盖亚守护者&f"
			x: 22.5d
			y: 2.5d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: [
				"与多数&d植物魔法&f装备相同,&9元素金属&r套装可通过魔力自动修复."
				""
				"穿戴者受伤时还有概率召唤&a精灵仙子&r."
			]
			hide_until_deps_visible: false
			id: "2F388BE777C43EB6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "49B5B0B509F9EDA5"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "2FFD52FE7AC45EFB"
					type: "xp"
					xp: 250
				}
			]
			tasks: [
				{
					id: "5820572224CA1F4C"
					item: {
						Count: 1
						id: "botania:elementium_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "3E0174D5D8FB4712"
					item: {
						Count: 1
						id: "botania:elementium_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "2E1C97AD850434EC"
					item: {
						Count: 1
						id: "botania:elementium_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "4B1C5F307D76F97A"
					item: {
						Count: 1
						id: "botania:elementium_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			x: 20.5d
			y: -0.5d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: [
				"可清除圆石、泥土、下界岩等常见方块,仅保留矿石等珍贵资源."
				""
				"可与&a泰拉粉碎者&f在工作台合成,使后者继承前者的能力(不可逆)."
			]
			id: "2230244B2CE5851D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "77A5CC9DBA8AF0D7"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "1C4EA5C60A107648"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "6348A2F342100CF9"
				item: {
					Count: 1
					id: "botania:elementium_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 21.5d
			y: -1.0d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: ["破坏受重力影响的方块时,会自动破坏其上方或下方所有同类方块."]
			id: "1C53DEE5CED13E92"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "05325B6767FF2977"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "6E0CD99B45AE52A3"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "48201CEFFBFD0000"
				item: {
					Count: 1
					id: "botania:elementium_shovel"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 19.5d
			y: 0.0d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: ["在给予致命一击时,有几率从特定生物或玩家身上掉落头颅.也可附魔抢夺效果."]
			id: "76A4FCDF04AD6656"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "131C3FAC197CAC8A"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "70AD0078EC883225"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "3B60A8D021393754"
				item: {
					Count: 1
					id: "botania:elementium_axe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 20.5d
			y: -1.5d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: ["可立即湿润其创造的耕地.是否有更贴切的表述方式？"]
			id: "1BCA04665A8F5EF5"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "627101145C66356F"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "74433A30F54CDA76"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "64D7F9E3A9A910FC"
				item: {
					Count: 1
					id: "botania:elementium_hoe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 21.5d
			y: 0.0d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: ["提高被击中时生成精灵的概率,并增强已生成精灵的强度."]
			id: "497C981D49B7FBD6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "638FD95544B37B2B"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "302C4454A0407139"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "1BF03F0B36123366"
				item: {
					Count: 1
					id: "botania:elementium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 19.5d
			y: -1.0d
		}
		{
			dependencies: ["0331107259F7B68C"]
			description: ["将&2&a精灵魔力发射器&f&r与龙晶之力及&e&a盖亚魂&f&r结合,可打造最强&a魔力发射器&f."]
			hide_until_deps_visible: false
			id: "4E03AC9D56202353"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "42CC72F0E805BE6B"
					table_id: 402489266001286151L
					type: "random"
				}
				{
					id: "635C477D56FB88B2"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "2D5337F7895CC160"
				item: "botania:gaia_spreader"
				type: "item"
			}]
			x: 24.5d
			y: 4.0d
		}
		{
			dependencies: ["04CE14D92603FC7A"]
			description: [
				"击败&9&a盖亚守护者&f&r后,你将获得&e&a盖亚魂&f&r作为奖励."
				""
				"游戏难度及参与仪式的人数将决定掉落数量."
			]
			icon: "botania:gaia_head"
			id: "0331107259F7B68C"
			rewards: [
				{
					id: "44B68600014CC3BD"
					item: "botania:terrasteel_ingot"
					random_bonus: 1
					type: "item"
				}
				{
					id: "3914369E0BBE0076"
					type: "xp"
					xp: 1000
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [
				{
					id: "3C21A030F242C7A2"
					item: "botania:life_essence"
					type: "item"
				}
				{
					entity: "botania:doppleganger"
					icon: "botania:gaia_head"
					id: "615316948CD5BB35"
					type: "kill"
					value: 1L
				}
			]
			title: "&e&a盖亚魂&f"
			x: 24.5d
			y: 2.5d
		}
		{
			dependencies: ["0331107259F7B68C"]
			description: ["渴望更高挑战,或需要更多&e&a盖亚魂&f&r？尝试将4个&a盖亚魂&f与泰拉钢锭结合,用以激活盖亚的&a魔法仪式&f."]
			hide_until_deps_visible: false
			id: "27CD17C4B3613171"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "08028478C5B94310"
					table_id: 402489266001286151L
					type: "random"
				}
				{
					id: "6F718ABCAB882225"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "3DE2A054C6F00FBE"
				item: "botania:gaia_ingot"
				type: "item"
			}]
			title: "&5&a盖亚守护者&f 2.0"
			x: 26.0d
			y: 2.5d
		}
		{
			dependencies: ["27CD17C4B3613171"]
			description: ["使用盖亚锭召唤强化版&5&a盖亚守护者&f&r可获得更多&e&a盖亚魂&f&r,同时守护者还会掉落&e&a命运骰子&f&r."]
			id: "50A2D68A83A50F47"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "40F40838ECBF9A36"
					table_id: 7175652334583451871L
					type: "random"
				}
				{
					id: "73704F1B32D0A15F"
					type: "xp"
					xp: 1000
				}
			]
			shape: "gear"
			size: 3.0d
			tasks: [{
				id: "5348CBD95103744A"
				item: "botania:dice"
				type: "item"
			}]
			title: "&e&a命运骰子&f"
			x: 28.0d
			y: 2.5d
		}
		{
			dependencies: ["634E71DBAE81D197"]
			description: [
				"&9&a多媒体漏斗&f&r是索引的简易版,可通过红石信号从网络中请求物品."
				""
				"配置请求物品时,需将目标物品放入方块上的物品展示框.若存在多个展示框,漏斗会随机选择.旋转框内物品可调整请求数量."
				""
				"更多信息请查阅&a&a辞典&f &d植物魔法&f&r."
			]
			id: "4ECFB1256BD6A7AA"
			rewards: [{
				id: "0924F6AEDAFE95F8"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "27FBB10E434FBD29"
				item: "botania:corporea_funnel"
				type: "item"
			}]
			x: 19.5d
			y: 6.5d
		}
		{
			dependencies: ["3A20210242A1C865"]
			description: [
				"通过&d植物魔法&f,可在容器上方使用&9&a多媒体火花&f&r构建&d物质网络&r."
				""
				"网络需至少一个&b主&r&9&a多媒体火花&f&r运作,并可通过任意数量&a多媒体火花&f扩展.同色火花会在8格范围内自动连接形成物品网络."
				""
				"火花仅能识别正下方容器,且仅能从顶部存取物品.所有网络内物品均可被物质漏斗或索引等方块调用."
			]
			id: "634E71DBAE81D197"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "54DAB6AA41E2D70B"
					table_id: 8234116511213485813L
					type: "random"
				}
				{
					id: "24B9FEA1C04B9C8C"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "5F0667A0930319F8"
					item: "botania:corporea_spark"
					type: "item"
				}
				{
					id: "3844BA7E922E8B45"
					item: "botania:corporea_spark_master"
					type: "item"
				}
			]
			title: "&d物质网络"
			x: 20.5d
			y: 5.5d
		}
		{
			dependencies: ["634E71DBAE81D197"]
			description: [
				"&5&a多媒体索引&f&r是访问&9物质网络&r的交互方块,需配合上方&a多媒体火花&f使用."
				""
				"靠近索引时,它将拦截附近玩家的&a聊天栏信息&f.玩家可输入所需物品(如&b10 &a铁锭&f&r),若网络中存在该物品则会自动吐出."
				""
				"更多信息请查阅&a&a辞典&f &d植物魔法&f&r."
			]
			id: "7DF7F9CD9A795E69"
			rewards: [{
				exclude_from_claim_all: true
				id: "69C1BD816E1E421A"
				table_id: 8234116511213485813L
				type: "loot"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "3611F94A69522BA9"
				item: "botania:corporea_index"
				type: "item"
			}]
			x: 20.5d
			y: 7.5d
		}
		{
			dependencies: ["634E71DBAE81D197"]
			description: ["&9&a多媒体水晶魔方&f&r用于显示上方火花所属物质网络中指定物品的总量,需手持该物品&a右击&f激活."]
			id: "7440E522FC31C341"
			optional: true
			rewards: [{
				id: "400B276E83E4995C"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "1550070280A21DC5"
				item: "botania:corporea_crystal_cube"
				type: "item"
			}]
			x: 21.5d
			y: 6.5d
		}
		{
			dependencies: ["50A2D68A83A50F47"]
			hide_until_deps_visible: false
			id: "4E7B1FF823C85AD6"
			optional: true
			rewards: [{
				id: "3FBDEEEB45C98853"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "05993154671C0697"
				item: "botania:thor_ring"
				type: "item"
			}]
			x: 29.0d
			y: 4.5d
		}
		{
			dependencies: ["50A2D68A83A50F47"]
			hide_until_deps_visible: false
			id: "0C56CB7815A1355C"
			optional: true
			rewards: [{
				id: "2B5648DCC65FFD87"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "078E142753C8FA7B"
				item: {
					Count: 1
					id: "botania:odin_ring"
					tag: {
						baubleUUID: [I;
							-68256365
							-428781930
							-1518441386
							-645335533
						]
					}
				}
				type: "item"
			}]
			x: 27.0d
			y: 4.5d
		}
		{
			dependencies: ["50A2D68A83A50F47"]
			hide_until_deps_visible: false
			id: "1EE1EF4695A39876"
			optional: true
			rewards: [{
				id: "5CD71910DADB23AD"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "46081AC3349324C9"
				item: "botania:loki_ring"
				type: "item"
			}]
			x: 30.0d
			y: 3.5d
		}
		{
			dependencies: ["50A2D68A83A50F47"]
			hide_until_deps_visible: false
			id: "6AFC5FE71A80913E"
			optional: true
			rewards: [{
				id: "0423B21E4757A2E6"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "408C9E36F8C27D8C"
				item: "botania:infinite_fruit"
				type: "item"
			}]
			x: 30.0d
			y: 1.5d
		}
		{
			dependencies: ["50A2D68A83A50F47"]
			hide_until_deps_visible: false
			id: "780B0A790C6AF9DB"
			optional: true
			rewards: [{
				id: "01E797D246D95395"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "371A7D51171C67D4"
				item: "botania:king_key"
				type: "item"
			}]
			x: 29.0d
			y: 0.5d
		}
		{
			dependencies: ["50A2D68A83A50F47"]
			hide_until_deps_visible: false
			id: "16DE70DE2463A5DD"
			optional: true
			rewards: [{
				id: "020513DCA252A8B9"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "337F2A21222F5FFC"
				item: "botania:flugel_eye"
				type: "item"
			}]
			x: 27.0d
			y: 0.5d
		}
		{
			dependencies: ["79BE48D56622542F"]
			description: [
				"使用&a花药台&f,我们可以制作多种在冒险中提供帮助的花朵,这些花朵分为两大类:&9&a功能花&f&r和&a&a产能花&f&r."
				""
				"&9&a功能花&f&r是辅助日常工作的花朵.例如&7漏斗花&r会像漏斗一样运作,拾取周围特定范围内的物品.部分&a功能花&f需要消耗&d魔力&r才能运作."
				""
				"&a&a产能花&f&r是通过不同方式产生&d魔力&r的花朵.最常用的产能花是&c炽焰花&r,它通过消耗附近煤炭等可燃物来生成魔力."
				""
				"在本任务线中,任务图标为&c方形&r的是&a产能花&f,而&9圆形&r图标的是&a功能花&f.你还可以通过&a植物魔法辞典&d植物魔法&f&r查阅每种花朵的具体功能."
			]
			hide_until_deps_visible: false
			id: "7B3FAF5CA4DD217C"
			min_width: 400
			tasks: [{
				id: "7F362CF1D18C7FB1"
				title: "&a功能类植物&f"
				type: "checkmark"
			}]
			title: "功能性花朵与产能类植物"
			x: 3.0d
			y: 2.5d
		}
		{
			dependencies: ["1C48F2612F2FA828"]
			description: [
				"将&4红石&r与&2&a魔力发射器&f&r组合时,会制成&9&a红石魔力发射器&f&r."
				""
				"该装置可设置为仅在接收到红石信号时发射魔力脉冲."
			]
			id: "4C22BA5824662C7E"
			optional: true
			rewards: [{
				id: "51FB8A5FF90A645C"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			tasks: [{
				id: "06C2FA45E55D9369"
				item: "botania:redstone_spreader"
				type: "item"
			}]
			x: 9.0d
			y: 5.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["以牺牲初始容量和加快魔力损耗为代价,大幅提升&a魔力脉冲&f的传输速度."]
			id: "2D1533268C835672"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5C17A2BAAE05CDD2"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "27C4C4ECBDD8CFB2"
					type: "xp"
					xp: 50
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "4447633E574741FD"
				item: "botania:lens_speed"
				type: "item"
			}]
			x: 10.5d
			y: 8.0d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["使&a魔力脉冲&f的魔力携带量翻倍,但会降低速度并增加远距离传输时的魔力损耗."]
			id: "7A82D66B1B138F0F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "29C1754402E13135"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "57CA8F5150562EDE"
					type: "xp"
					xp: 50
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "1F0DDF8D08AA2D09"
				item: "botania:lens_power"
				type: "item"
			}]
			x: 14.0d
			y: 9.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["显著延长&a魔力脉冲&f开始流失魔力的时间,但同时会降低脉冲速度."]
			id: "4CED346215DDBAC0"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "48EA8678DF91AC9F"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "7362FD9B9AB62AB8"
					type: "xp"
					xp: 50
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "58A4565F18420732"
				item: "botania:lens_time"
				type: "item"
			}]
			x: 14.5d
			y: 8.0d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["缩短&a魔力脉冲&f开始流失魔力的时间间隔,但会减缓流失速率."]
			id: "5A7FF6D0AED656DC"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "273BCA13B1064BEA"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "136B2862652086D9"
					type: "xp"
					xp: 50
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "5FBA2E1E91367CB5"
				item: "botania:lens_efficiency"
				type: "item"
			}]
			x: 11.0d
			y: 9.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["允许&a魔力脉冲&f在碰到墙壁时反弹."]
			id: "504ADBD7E2476827"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2CFF85B91874AFC6"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "477A3BF01D3F9CAF"
					type: "xp"
					xp: 50
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "46729DA26469F064"
				item: "botania:lens_bounce"
				type: "item"
			}]
			x: 12.5d
			y: 10.0d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["使重力影响&a魔力脉冲&f,形成抛物线轨迹,同时略微延长魔力开始流失前的持续时间."]
			hide_dependency_lines: true
			id: "665B4A8FF5277316"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "21CE36095A7F5C3A"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "48A5A90DEE2E614E"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "5698F1ECAA82616E"
				item: "botania:lens_gravity"
				type: "item"
			}]
			x: 13.0d
			y: 12.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["允许&a魔力脉冲&f消耗自身魔力穿透方块."]
			hide_dependency_lines: true
			id: "3722E5A8BE99C5B1"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1B1024EA29969901"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "65BDF707606009F8"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "671D9D2E3EDF31D9"
				item: "botania:lens_mine"
				type: "item"
			}]
			x: 12.0d
			y: 11.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["允许&a魔力脉冲&f消耗自身魔力对击中的生物造成伤害."]
			hide_dependency_lines: true
			id: "533624CA5C2A7BBD"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7B810EA8019CFF94"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "696217E057A6C1AD"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "6BF8FAA764F765F1"
				item: "botania:lens_damage"
				type: "item"
			}]
			x: 11.0d
			y: 11.0d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["该透镜允许&a魔力脉冲&f穿墙而过,但会缩短魔力保持完整的时间."]
			hide_dependency_lines: true
			id: "5EF08646D8F4C432"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "49FD56577500B0F7"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "435A71444B76B6C4"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "7EF55A6F1C4D84A7"
				item: "botania:lens_phantom"
				type: "item"
			}]
			x: 10.0d
			y: 10.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["使&a魔力脉冲&f自动追踪附近可接收魔力的方块,同时略微降低脉冲速度."]
			hide_dependency_lines: true
			id: "79A19D0B2F94EFDC"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2C85E7993D64B178"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "7923DCD4C5364E57"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "6F5CDA3C118F1D62"
				item: "botania:lens_magnet"
				type: "item"
			}]
			x: 13.0d
			y: 11.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["为&a魔力脉冲&f注入熵能力量——简单来说,当它击中无法接收魔力的物体时会变成炸弹."]
			hide_dependency_lines: true
			id: "7AD4D212633DAEC8"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4EECA46E6980C324"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "186E7D9A67A97FAF"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "0A25BA85F56FB561"
				item: "botania:lens_explosive"
				type: "item"
			}]
			x: 14.0d
			y: 11.0d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["允许&a魔力脉冲&f影响附近的掉落物、经验球和下坠方块,使其沿脉冲运动矢量方向移动."]
			hide_dependency_lines: true
			id: "5732936F1FD1D4AE"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "53671F15B34268D8"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "62FDD1E9AF0AC18A"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "3686E63D875EBFB8"
				item: "botania:lens_influence"
				type: "item"
			}]
			x: 15.0d
			y: 10.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["当&a魔力脉冲&f通过该透镜击中方块时,目标方块会像沙砾般下落."]
			hide_dependency_lines: true
			id: "3CD45533D0BDF26D"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "020B10B81BDFFB10"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "7FC8A1841922B1CC"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "1732E57F7595BB7E"
				item: "botania:lens_weight"
				type: "item"
			}]
			x: 11.0d
			y: 12.0d
		}
		{
			dependencies: [
				"6FBE0BF8A7ADBB26"
				"3A20210242A1C865"
			]
			description: [
				"需先进行染色处理."
				""
				"允许&a魔力脉冲&f为击中的可染色方块(包括相连方块)重新着色,对绵羊同样有效."
			]
			hide_dependency_lines: true
			id: "68DC8A2B4BEF9878"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "20C096E1BC6C902D"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "2BE5158003577809"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "1C3B5F68898949A4"
				item: "botania:lens_paint"
				type: "item"
			}]
			x: 14.0d
			y: 14.5d
		}
		{
			dependencies: [
				"6FBE0BF8A7ADBB26"
				"3A20210242A1C865"
			]
			description: ["使&a魔力脉冲&f在击中方块时发射节日烟花."]
			hide_dependency_lines: true
			id: "478352E24CE1F45F"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7422EA204F8C7542"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "1871B498998A7526"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "2F04E0FD92572BE8"
				item: "botania:lens_firework"
				type: "item"
			}]
			x: 12.0d
			y: 14.5d
		}
		{
			dependencies: [
				"6FBE0BF8A7ADBB26"
				"3A20210242A1C865"
			]
			description: ["将此透镜安装在&a魔力发射器&f上,可使其发射连续的魔力粒子而非短促脉冲.不消耗魔力.非常适合装饰用途."]
			hide_dependency_lines: true
			id: "7B95C7407A1F5550"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3B429056F8C1F9DF"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "7C77D58570AC86F4"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "6D405361F834914E"
				item: "botania:lens_flare"
				type: "item"
			}]
			x: 11.0d
			y: 14.5d
		}
		{
			dependencies: [
				"6FBE0BF8A7ADBB26"
				"3A20210242A1C865"
			]
			description: ["将此透镜安装在发射器上,仅当&a魔力脉冲&f能击中生物或玩家时才会发射."]
			hide_dependency_lines: true
			id: "0E7E559F2750F7D3"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7A9E9E51AC379587"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "34B66471DE4B62D4"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "3B9B92618158074B"
				item: "botania:lens_tripwire"
				type: "item"
			}]
			x: 11.5d
			y: 13.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["大幅减少&a魔力脉冲&f携带的魔力值,同时显著提高脉冲速度和射程."]
			hide_dependency_lines: true
			id: "182EBB06B81BDF98"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "08DDD13BFF9F543D"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "77A4DD94109B7676"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "4C80AD2C05E0CE73"
				item: "botania:lens_messenger"
				type: "item"
			}]
			x: 14.0d
			y: 12.0d
		}
		{
			dependencies: [
				"6FBE0BF8A7ADBB26"
				"3A20210242A1C865"
			]
			description: ["该透镜会使&a魔力发射器&f发射的脉冲或碰撞实体转向,使其朝向发射该脉冲的方块或实体."]
			hide_dependency_lines: true
			id: "03C475ABEFF04DEF"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2780C77794146ECB"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "651D17D05A4AEE6F"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "28089D55C29C8A5D"
				item: "botania:lens_redirect"
				type: "item"
			}]
			x: 13.0d
			y: 14.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["使&a魔力脉冲&f在击中方块时生成火焰.这种火焰仅提供照明且纯属装饰,可用另一个&a魔力脉冲&f扑灭."]
			hide_dependency_lines: true
			id: "0C557A9494F58400"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3932678AED209603"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "7BAE162D0F0DFF80"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "229487A1B2657BF0"
				item: "botania:lens_light"
				type: "item"
			}]
			x: 10.0d
			y: 11.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["允许&a魔力脉冲&f像活塞一样推动方块."]
			hide_dependency_lines: true
			id: "14EB725643E9F8FE"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3308155C8EEDE59B"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "235D599CD40C83BA"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "24EBCD71A8C65AAE"
				item: "botania:lens_piston"
				type: "item"
			}]
			x: 15.0d
			y: 11.5d
		}
		{
			dependencies: ["6FBE0BF8A7ADBB26"]
			description: ["允许&a魔力脉冲&f引燃方块,但对生物无效."]
			hide_dependency_lines: true
			id: "12BEB76EB924379C"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "385F93E69F2AC47C"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "21DDAB78B2C17D66"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "55AD493BAD9D6E10"
				item: "botania:lens_fire"
				type: "item"
			}]
			x: 12.0d
			y: 12.5d
		}
		{
			dependencies: [
				"6FBE0BF8A7ADBB26"
				"3A20210242A1C865"
			]
			description: ["关于该透镜的使用方法,请参阅&a&a辞典&f &d植物魔法&f&r."]
			hide_dependency_lines: true
			id: "4CC46A4E71320140"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5CD0D4949ABABC67"
					table_id: 5354288240016506720L
					type: "random"
				}
				{
					id: "71D834AFD30CAC03"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "58FACC24E092C8EB"
				item: "botania:lens_warp"
				type: "item"
			}]
			x: 13.5d
			y: 13.5d
		}
		{
			dependencies: ["0331107259F7B68C"]
			description: [
				"有许多饰品能驾驭&e&a盖亚魂&f&r的力量,记得去探索它们!"
				""
				"你可以在&a&a辞典&f &d植物魔法&f&r中找到所有相关描述."
			]
			id: "66F1609053B5407C"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7A3224CA289B7404"
					table_id: 402489266001286151L
					type: "random"
				}
				{
					id: "67DD0610BB6C4893"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "44A3CDE54F2A5149"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "botania:black_hole_talisman"
							}
							{
								Count: 1b
								id: "botania:spawner_mover"
							}
							{
								Count: 1b
								id: "botania:astrolabe"
							}
							{
								Count: 1b
								id: "botania:holy_cloak"
							}
							{
								Count: 1b
								id: "botania:diva_charm"
							}
							{
								Count: 1b
								id: "botania:super_lava_pendant"
							}
							{
								Count: 1b
								id: "botania:unholy_cloak"
							}
							{
								Count: 1b
								id: "botania:laputa_shard"
							}
							{
								Count: 1b
								id: "botania:super_cloud_pendant"
							}
							{
								Count: 1b
								id: "botania:flight_tiara"
							}
							{
								Count: 1b
								id: "botania:mana_bomb"
							}
							{
								Count: 1b
								id: "botania:missile_rod"
							}
							{
								Count: 1b
								id: "botania:balance_cloak"
							}
							{
								Count: 1b
								id: "botania:super_travel_belt"
							}
						]
					}
				}
				title: "盖亚饰品"
				type: "item"
			}]
			title: "&a盖亚装备与饰品"
			x: 24.5d
			y: 1.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"该任务默认隐藏,若你看到此提示,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "1C73AE750F4500E8"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "37BB8E84CF18694C"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "41DE3E2C54A0A251"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 5.0d
			y: 4.0d
		}
	]
	title: "&d植物魔法&f"
}
