{
	default_hide_dependency_lines: true
	default_quest_shape: ""
	filename: "powah"
	group: "2B51AC12041E3F89"
	icon: "powah:player_transmitter_nitro"
	id: "2A6EBEEBAB882679"
	images: [
		{
			height: 1.0d
			image: "powah:item/capacitor_basic"
			rotation: 0.0d
			width: 1.0d
			x: -9.5d
			y: 12.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_basic_tiny"
			rotation: 0.0d
			width: 1.0d
			x: -9.5d
			y: 11.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_hardened"
			rotation: 0.0d
			width: 1.0d
			x: -9.5d
			y: 13.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_blazing"
			rotation: 0.0d
			width: 1.0d
			x: -9.5d
			y: 14.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_niotic"
			rotation: 0.0d
			width: 1.0d
			x: -9.5d
			y: 15.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_spirited"
			rotation: 0.0d
			width: 1.0d
			x: -9.5d
			y: 16.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_nitro"
			rotation: 0.0d
			width: 1.0d
			x: -9.5d
			y: 17.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_nitro"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 17.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_spirited"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 16.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_niotic"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 15.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_blazing"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 14.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_hardened"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 13.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_basic"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 12.5d
		}
		{
			height: 1.0d
			image: "powah:item/capacitor_basic_tiny"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 11.5d
		}
		{
			height: 2.0d
			image: "ftbquests:tasks/input_only"
			rotation: 45.0d
			width: 2.0d
			x: -0.5d
			y: 4.5d
		}
		{
			height: 2.0d
			image: "ftbquests:block/barrier_open"
			rotation: 0.0d
			width: 2.0d
			x: -0.5d
			y: 8.0d
		}
		{
			height: 1.0d
			image: "powah:block/nitro_crystal_block"
			rotation: 45.0d
			width: 1.0d
			x: -0.5d
			y: 19.0d
		}
		{
			height: 3.0d
			image: "ftbquests:tasks/input_only"
			rotation: 0.0d
			width: 3.0d
			x: -0.5d
			y: 4.5d
		}
		{
			alpha: 150
			color: 0
			height: 10.0d
			image: "ftbquests:textures/shapes/square/outline.png"
			order: -1
			rotation: 0.0d
			width: 24.0d
			x: -0.5d
			y: 14.5d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 11.0d
		}
		{
			height: 0.025d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 12.0d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 12.0d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 13.0d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 14.0d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 15.0d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 16.0d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 17.0d
		}
		{
			alpha: 200
			height: 0.1d
			image: "minecraft:block/black_concrete"
			rotation: 0.0d
			width: 22.0d
			x: -0.5d
			y: 18.0d
		}
		{
			height: 0.9d
			image: "atm:textures/questpics/powah/text/storage_text.png"
			rotation: 0.0d
			width: 2.3019230769230767d
			x: -2.5d
			y: 10.75d
		}
		{
			height: 0.8d
			image: "atm:textures/questpics/powah/text/generation_text.png"
			rotation: 0.0d
			width: 3.3600000000000003d
			x: 5.5d
			y: 10.7d
		}
		{
			height: 0.75d
			image: "atm:textures/questpics/powah/text/useful_items_text.png"
			rotation: 0.0d
			width: 3.908823529411765d
			x: -6.5d
			y: 10.7d
		}
		{
			height: 0.75d
			image: "atm:textures/questpics/powah/text/transfer_text.png"
			rotation: 0.0d
			width: 2.6029411764705883d
			x: 1.5d
			y: 10.7d
		}
		{
			height: 1.0d
			image: "powah:block/niotic_crystal_block"
			rotation: 45.0d
			width: 1.0d
			x: -0.5d
			y: 10.0d
		}
		{
			height: 1.0d
			image: "powah:item/uraninite_raw"
			rotation: 0.0d
			width: 1.0d
			x: -10.5d
			y: 10.5d
		}
		{
			height: 1.0d
			image: "powah:item/uraninite_raw"
			rotation: 0.0d
			width: 1.0d
			x: -10.5d
			y: 18.5d
		}
		{
			height: 1.0d
			image: "powah:item/uraninite_raw"
			rotation: 0.0d
			width: 1.0d
			x: 9.5d
			y: 10.5d
		}
		{
			height: 1.0d
			image: "powah:item/uraninite_raw"
			rotation: 0.0d
			width: 1.0d
			x: 9.5d
			y: 18.5d
		}
		{
			height: 0.3d
			hover: ["合成&aATM之星&f所需"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: -5.5d
			y: 18.1d
		}
		{
			height: 0.3d
			hover: ["合成&aATM之星&f所需"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: -6.5d
			y: 18.1d
		}
	]
	order_index: 9
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"&9Powah&r是专注于&d能源&r生产、存储与传输的科技模组.从基础FE生产到&a反应堆&r的&b250k FE/t&r输出,应有尽有!"
				""
				"开局先去挖些&a铀矿&r!"
			]
			icon: "powah:book"
			id: "6B2027DA7AA6FF34"
			rewards: [
				{
					id: "344F5D87627A95C7"
					item: "powah:book"
					type: "item"
				}
				{
					id: "0A20CAF72F0E3E11"
					item: {
						Count: 1
						id: "powah:wrench"
						tag: {
							PowahWrenchNBT: { }
						}
					}
					type: "item"
				}
			]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "3C45AF8C3DC5A45E"
				item: "powah:uraninite_raw"
				type: "item"
			}]
			title: "&a欢迎来到&r &9能量之力&r!!!"
			x: -0.5d
			y: 4.5d
		}
		{
			dependencies: ["6B2027DA7AA6FF34"]
			description: [
				"本模组几乎所有机器都需要&9&a绝缘外壳&f&r."
				""
				"首先需要制作&b浆料&r和一些&a棒材&r才能推进进度!"
			]
			hide_dependency_lines: false
			icon: "powah:dielectric_casing"
			id: "7E92ED270C67FDE5"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5FC5C0DBD1862334"
					table_id: 4236052250335530963L
					type: "random"
				}
				{
					id: "4E8234C1477A2822"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			tasks: [
				{
					count: 16L
					id: "21FC6D010E5D5360"
					item: "powah:dielectric_paste"
					type: "item"
				}
				{
					id: "499AF83C86984D26"
					item: "powah:dielectric_casing"
					type: "item"
				}
			]
			title: "绝缘材料入门"
			x: -0.5d
			y: 6.5d
		}
		{
			dependencies: ["7E92ED270C67FDE5"]
			description: [
				"初期使用铁质材料制造&7基础级&r和&b标准级&r机器即可满足需求,但最终仍需通过&9充能宝珠&r制造充能材料来进阶.'"
				""
				"&9充能宝珠&f&r会利用周围9x9范围内的&a充能杆&f&r为物品充能,制造更高级材料助你突破Powah模组的&e等级体系&f&r."
				""
				"启动宝珠需将充能杆连接至供能线缆.若想加速充能,可增加充能杆数量、升级更高阶充能杆或双管齐下!用&a扳手&f&r切换至链接模式,可查看杆体与宝珠的连接状态."
				""
				"{image:atm:textures/questpics/powah/powah_energizing.png width:200 height:200 align:1}"
			]
			hide_dependency_lines: false
			icon: "powah:energizing_orb"
			id: "3DDF87A1E5F5D009"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "336EF3D109FC4797"
					table_id: 299590067093682297L
					type: "random"
				}
				{
					id: "5C3D57C7B5555A75"
					type: "xp"
					xp: 50
				}
			]
			shape: "square"
			size: 1.5d
			tasks: [
				{
					id: "37FB7E3C8FD03825"
					item: "powah:energizing_orb"
					type: "item"
				}
				{
					id: "36E378BF22554E0A"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "powah:energy_cable_starter"
								}
								{
									Count: 1b
									id: "powah:energy_cable_basic"
								}
							]
						}
					}
					title: "&a能量管道&f"
					type: "item"
				}
				{
					count: 3L
					id: "17F07D6404668DE6"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "powah:energizing_rod_starter"
								}
								{
									Count: 1b
									id: "powah:energizing_rod_basic"
								}
								{
									Count: 1b
									id: "powah:energizing_rod_hardened"
								}
								{
									Count: 1b
									id: "powah:energizing_rod_blazing"
								}
								{
									Count: 1b
									id: "powah:energizing_rod_niotic"
								}
								{
									Count: 1b
									id: "powah:energizing_rod_spirited"
								}
								{
									Count: 1b
									id: "powah:energizing_rod_nitro"
								}
							]
						}
					}
					title: "充能棒"
					type: "item"
				}
			]
			title: "&9充能宝珠"
			x: -0.5d
			y: 8.0d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: [
				"作为\"被动供能\"的最佳选择,&9&a热力发电机&f&r置于&c&a热源&f&r上方并持续供水即可产生FE."
				""
				"当前可用三种热源:产能最低的岩浆块,稍好的熔岩源块,或提供最大热量的&c&a烈焰水晶块&f&r."
			]
			id: "52E59FCB39D66BCF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "274AC988966524C0"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "6DB5053765546C84"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "6D3CE4788C112806"
				item: "powah:thermo_generator_starter"
				type: "item"
			}]
			x: 5.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["&7燃能炉&r通过燃烧煤炭等物品产生FE."]
			id: "3D5F87F8E6B89C1B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "32274A92CA7E0D29"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "1CEDF4B842E6FFAD"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "7071C57ACD94C9F5"
				item: "powah:furnator_starter"
				type: "item"
			}]
			x: 4.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["&a太阳能板&f在阳光直射时产生FE.使用&7末影透镜&r可无视遮挡物."]
			id: "7678B5DD1339833E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6EB0904004F389DA"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "65F43EC6BDE0870D"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "2B3E7BB9F4D228A7"
				item: "powah:solar_panel_starter"
				type: "item"
			}]
			x: 7.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["&c熔岩炉&r在注入熔岩时产生FE."]
			id: "0FD62827710F0AC6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "18B81A476AAD7918"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "6500F8A1FD7CDE98"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "58D16A3D3A480A37"
				item: "powah:magmator_starter"
				type: "item"
			}]
			x: 3.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: [
				"&9反应堆&r是一个3x4x3的多方块发电机,通过燃烧&a铀矿石&r作为燃料来产生FE能量."
				""
				"建造它需要制作总共36个反应堆方块.手持36个方块时,放置一个方块会自动构建整个反应堆.记得先清理出足够的空间!"
				""
				"你需要冷却反应堆以提高FE产量,可以使用固态或液态&b冷却剂&r.使用固态冷却剂时还需添加少量液态冷却剂.&b&a干冰&f&r是极佳的固态冷却剂!(注:1桶&a水&f即可)"
				""
				"保持燃料缓冲槽满载,并向反应堆添加煤炭和红石都能提升FE产量.使用对应的方块形态同样有效!"
			]
			id: "1B0087400B0B8B49"
			min_width: 300
			rewards: [
				{
					id: "09E5711FA8136C96"
					item: "powah:dry_ice"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "33E63DBBE49D7134"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "784A4CD06F2BCFC3"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				count: 36L
				id: "576DD3980CD97FBC"
				item: "powah:reactor_starter"
				type: "item"
			}]
			title: "反应堆(初级)"
			x: 6.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7D52DD751DDADA1B"
				"6D88C19F47D0D469"
			]
			description: [
				"&5&a末影道门&f&r用于将能量无线传输至相邻方块或从相邻方块接收能量,接入&7末影网络&r."
				""
				"可以理解为无线能量网络的接入点."
				""
				"注意:只能通过&a末影单元&f来增加&a能量存储容量&r."
			]
			id: "3CB6DC5B09C62CFE"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6A70A4688D512DDF"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "112DC3F34CCB36B2"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "185C323B23AA0983"
				item: "powah:ender_gate_starter"
				type: "item"
			}]
			x: 2.0d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["用于传输能量的基础线缆."]
			id: "7D52DD751DDADA1B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "45B43FBEBBDF09B0"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "6DE8006788293478"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "72A59D5484D75032"
				item: "powah:energy_cable_starter"
				type: "item"
			}]
			x: 1.0d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: [
				"&9&a玩家供电仪&f&r可无线为玩家物品充能.需先用&9&a绑定卡&f&r绑定玩家.基础版仅限&a同维度&f生效,可使用&d&a跨维度绑定卡&f&r升级."
				""
				"注:获取玩家空中珍珠需对僵尸或尸壳使用空中珍珠."
			]
			icon: "powah:player_transmitter_starter"
			id: "677365A816994C8B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7C1253E7B75722C1"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "224C0B9104AF03F3"
					type: "xp"
					xp: 10
				}
			]
			tasks: [
				{
					id: "71BD7D345E670234"
					item: "powah:player_transmitter_starter"
					type: "item"
				}
				{
					id: "1F929F1DF23460AE"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "powah:binding_card"
								}
								{
									Count: 1b
									id: "powah:binding_card_dim"
								}
							]
						}
					}
					title: "&a绑定卡&f"
					type: "item"
				}
			]
			x: -6.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["&9&a能量漏斗&f&r会为指向容器(如箱子)内的所有可充能物品充能."]
			id: "5BCA3F716348ECCD"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5E890AE83AC01458"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "199E2B2A88D2387E"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "3C2CFE54F8372650"
				item: "powah:energy_hopper_starter"
				type: "item"
			}]
			x: -7.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["该方块会抽取任何充能物品中的FE能量."]
			id: "33816AF0E699F19F"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "37AE24D2704ED63C"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "56697E45AA1BD3B3"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "43B03D8E251B44DD"
				item: "powah:energy_discharger_starter"
				type: "item"
			}]
			x: -8.5d
			y: 11.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: [
				"Powah的&9能源银行&r."
				""
				"也可用于提升无线&7末影网络&r的总能量存储容量."
			]
			id: "78202A1CF5D86B94"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5CEC3181F7E55CBD"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "659E6EAE16475B52"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "096413BFA9FB2C10"
				item: "powah:energy_cell_starter"
				type: "item"
			}]
			x: -2.0d
			y: 11.5d
		}
		{
			dependencies: [
				"78202A1CF5D86B94"
				"61A8FAEC4FF18449"
				"6D88C19F47D0D469"
			]
			dependency_requirement: "one_completed"
			description: ["&5&a末影单元&f&r能为&7末影网络&r频道存储能量.&a右键点击&f打开界面,添加&a电池&r或&9&a能源元件&f&r可提升总容量."]
			hide_dependency_lines: true
			id: "700F3FF7C23D0C0F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "769305364E781D9A"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "6F3E93B1DB1203CC"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "30E35BC580349772"
				item: "powah:ender_cell_starter"
				type: "item"
			}]
			x: -3.0d
			y: 11.5d
		}
		{
			dependencies: ["3DDF87A1E5F5D009"]
			hide_dependency_lines: true
			id: "5E090C9BB4DAA5D4"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "55FA38592D091DE5"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "72826C337A2463CA"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [{
				id: "7D5E27F786265E83"
				item: "powah:steel_energized"
				type: "item"
			}]
			title: "等级:§a充能级"
			x: -0.5d
			y: 13.5d
		}
		{
			dependencies: ["3DDF87A1E5F5D009"]
			hide_dependency_lines: true
			id: "562BD37539EE318E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7A69EACB23126AFC"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "32888D2D1DEDA263"
					type: "xp"
					xp: 50
				}
			]
			shape: "square"
			tasks: [{
				id: "1976C5DCCE8E84BD"
				item: "powah:crystal_blazing"
				type: "item"
			}]
			title: "等级:§c炽焰级"
			x: -0.5d
			y: 14.5d
		}
		{
			dependencies: ["3DDF87A1E5F5D009"]
			hide_dependency_lines: true
			id: "7D7983F39E6E818D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2C64F84E3E5A0266"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "4CB9B25219AECE2F"
					type: "xp"
					xp: 50
				}
			]
			shape: "pentagon"
			tasks: [{
				id: "64F9B19C093FEEF6"
				item: "powah:crystal_niotic"
				type: "item"
			}]
			title: "等级:§9氮素级"
			x: -0.5d
			y: 15.5d
		}
		{
			dependencies: ["3DDF87A1E5F5D009"]
			hide_dependency_lines: true
			id: "25EFC21A3C48E0B6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "05BB1CA3278200D4"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "0012C27504547683"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "216E5B85948E6E87"
				item: "powah:crystal_spirited"
				type: "item"
			}]
			title: "等级:§2烈魂级"
			x: -0.5d
			y: 16.5d
		}
		{
			dependencies: ["3DDF87A1E5F5D009"]
			hide_dependency_lines: true
			id: "4F1FFC02F4EAA2E6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1DE001DB521BBA33"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "2FF6315546889CC7"
					type: "xp"
					xp: 250
				}
			]
			shape: "octagon"
			tasks: [{
				id: "2BA798C77C6F3011"
				item: "powah:crystal_nitro"
				type: "item"
			}]
			title: "等级:§4硝化级"
			x: -0.5d
			y: 17.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["可用于为物品栏物品充能,或提升&7末影网络&r频道的总能量容量."]
			id: "61A8FAEC4FF18449"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "14433FED493BD66D"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "7984927D6A54E393"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "1D02EF09FDC433ED"
				item: {
					Count: 1
					id: "powah:battery_starter"
					tag: { }
				}
				type: "item"
			}]
			x: -5.5d
			y: 11.5d
		}
		{
			dependencies: ["66ECC26BC81D0093"]
			hide_dependency_lines: true
			id: "5A07C7A54D40FBE2"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "11D419594731E761"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "076EBFB074989F80"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "13A1F6468574ED2A"
				item: "powah:energy_discharger_basic"
				type: "item"
			}]
			x: -8.5d
			y: 12.5d
		}
		{
			dependencies: [
				"5BCA3F716348ECCD"
				"66ECC26BC81D0093"
			]
			id: "35F885A046EAE246"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "26A866150CB96015"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "5ED5D54F02F272AE"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "66DEF2F1E882F41E"
				item: "powah:energy_hopper_basic"
				type: "item"
			}]
			x: -7.5d
			y: 12.5d
		}
		{
			dependencies: [
				"677365A816994C8B"
				"66ECC26BC81D0093"
			]
			id: "740F314EE6242C13"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "548EB9CCF7F1E732"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "72A188DBD0297AF3"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "5ED7B42C22BCD280"
				item: "powah:player_transmitter_basic"
				type: "item"
			}]
			x: -6.5d
			y: 12.5d
		}
		{
			dependencies: [
				"61A8FAEC4FF18449"
				"66ECC26BC81D0093"
			]
			id: "04F11E192A334E3F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "77086CA1AA324197"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "48654FB654C4FFB6"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "64BF6A3821454638"
				item: {
					Count: 1
					id: "powah:battery_basic"
					tag: { }
				}
				type: "item"
			}]
			x: -5.5d
			y: 12.5d
		}
		{
			dependencies: [
				"3DDF87A1E5F5D009"
				"66ECC26BC81D0093"
			]
			hide_dependency_lines: true
			id: "0C481BA4C1CC0237"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1C883044138DABFB"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "13014E6A147F6EBC"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "08187D03C13CC05A"
				item: "powah:ender_cell_basic"
				type: "item"
			}]
			x: -3.0d
			y: 12.5d
		}
		{
			dependencies: [
				"78202A1CF5D86B94"
				"7E92ED270C67FDE5"
				"66ECC26BC81D0093"
			]
			dependency_requirement: "one_completed"
			hide_dependency_lines: true
			id: "66FA15A92DC655DE"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "333ECD3397C2E8CA"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "298D2BB87179CA21"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "166247828A9C16E2"
				item: "powah:energy_cell_basic"
				type: "item"
			}]
			x: -2.0d
			y: 12.5d
		}
		{
			dependencies: [
				"7D52DD751DDADA1B"
				"66ECC26BC81D0093"
			]
			dependency_requirement: "one_completed"
			hide_dependency_lines: true
			id: "5A708BF4F5091959"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6E2746A7CE4F8A12"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "3F9F58064210F179"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "6FFB4F8A40315B4F"
				item: "powah:energy_cable_basic"
				type: "item"
			}]
			x: 1.0d
			y: 12.5d
		}
		{
			dependencies: [
				"5A708BF4F5091959"
				"66ECC26BC81D0093"
			]
			id: "69D82C33347D360D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4531E9D99F71B91A"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "1F197E801453094D"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "2519EA38B1E3AA6F"
				item: "powah:ender_gate_basic"
				type: "item"
			}]
			x: 2.0d
			y: 12.5d
		}
		{
			dependencies: [
				"0FD62827710F0AC6"
				"66ECC26BC81D0093"
			]
			id: "0E3A9D5E1E8AAF89"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "499F3C25175C255E"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "20192640360C0972"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "18DA813AAFFAB349"
				item: "powah:magmator_basic"
				type: "item"
			}]
			x: 3.5d
			y: 12.5d
		}
		{
			dependencies: [
				"3D5F87F8E6B89C1B"
				"66ECC26BC81D0093"
			]
			id: "67DE5F982629BB34"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "460E0C6416E0A064"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "77DA676A51B424B1"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "2920C14299A40CD7"
				item: "powah:furnator_basic"
				type: "item"
			}]
			x: 4.5d
			y: 12.5d
		}
		{
			dependencies: [
				"52E59FCB39D66BCF"
				"66ECC26BC81D0093"
			]
			id: "60F5DC851FCFF1B2"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3E8225EF3DBD897E"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "060D1AD152581ECD"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "414DD76E288A6CF7"
				item: "powah:thermo_generator_basic"
				type: "item"
			}]
			x: 5.5d
			y: 12.5d
		}
		{
			dependencies: [
				"1B0087400B0B8B49"
				"66ECC26BC81D0093"
			]
			id: "5F6152CF085D75D6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "695DE6AE9A9329F2"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "57B3E1679990C2B0"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				count: 36L
				id: "18847CA551A72B03"
				item: "powah:reactor_basic"
				type: "item"
			}]
			title: "反应堆(基础)"
			x: 6.5d
			y: 12.5d
		}
		{
			dependencies: [
				"7678B5DD1339833E"
				"66ECC26BC81D0093"
			]
			id: "7890A1478121D2CD"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2637536BE8BBC147"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "3A6C46220F774D35"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "487DA52CCD1741A3"
				item: "powah:solar_panel_basic"
				type: "item"
			}]
			x: 7.5d
			y: 12.5d
		}
		{
			dependencies: ["5E090C9BB4DAA5D4"]
			hide_dependency_lines: true
			id: "534485E8185B71C3"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "257264EBB15C1550"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "26EBA6D243FC8A09"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "040A94FEA0038444"
				item: "powah:energy_discharger_hardened"
				type: "item"
			}]
			x: -8.5d
			y: 13.5d
		}
		{
			dependencies: [
				"35F885A046EAE246"
				"5E090C9BB4DAA5D4"
			]
			id: "04930767C52C7CC2"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5E5C1D3C7ABF9D17"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "4F497079C6C19EBE"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "5B3DDFB23CA6C703"
				item: "powah:energy_hopper_hardened"
				type: "item"
			}]
			x: -7.5d
			y: 13.5d
		}
		{
			dependencies: [
				"740F314EE6242C13"
				"5E090C9BB4DAA5D4"
			]
			id: "7444B85503DE2C53"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "07CBEA27B6841428"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "4B8A79E2928C6072"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "764E430B973B8BD9"
				item: "powah:player_transmitter_hardened"
				type: "item"
			}]
			x: -6.5d
			y: 13.5d
		}
		{
			dependencies: [
				"04F11E192A334E3F"
				"5E090C9BB4DAA5D4"
			]
			id: "604AB95A7600B150"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5A1DC827A01398E4"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "1C2A869A69144585"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "4FB89D5C29752C55"
				item: {
					Count: 1
					id: "powah:battery_hardened"
					tag: { }
				}
				type: "item"
			}]
			x: -5.5d
			y: 13.5d
		}
		{
			dependencies: [
				"66FA15A92DC655DE"
				"5E090C9BB4DAA5D4"
			]
			id: "7FEE780F37A1322E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "597278B13B0DCE91"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "1CB63C1741CAE50B"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "672A18DBFAA84594"
				item: "powah:energy_cell_hardened"
				type: "item"
			}]
			x: -2.0d
			y: 13.5d
		}
		{
			dependencies: ["5E090C9BB4DAA5D4"]
			hide_dependency_lines: true
			id: "331498A68D110F81"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4F124694464385D9"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "368C8CAD13698AB8"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "5E8665A0E3D4A148"
				item: "powah:ender_cell_hardened"
				type: "item"
			}]
			x: -3.0d
			y: 13.5d
		}
		{
			dependencies: [
				"5A708BF4F5091959"
				"5E090C9BB4DAA5D4"
			]
			dependency_requirement: "one_completed"
			hide_dependency_lines: true
			id: "029922AB09F8C76A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "46C596393E36A96B"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "33E7F0C466941D67"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "43621F44015EB06D"
				item: "powah:energy_cable_hardened"
				type: "item"
			}]
			x: 1.0d
			y: 13.5d
		}
		{
			dependencies: [
				"029922AB09F8C76A"
				"5E090C9BB4DAA5D4"
			]
			id: "5D594154819DD145"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "15561179FA3DA02E"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "38CAEE3F16B26CEB"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "5333BF78E072B674"
				item: "powah:ender_gate_hardened"
				type: "item"
			}]
			x: 2.0d
			y: 13.5d
		}
		{
			dependencies: [
				"0E3A9D5E1E8AAF89"
				"5E090C9BB4DAA5D4"
			]
			id: "6ED2F8A8DEED417D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "37E91B34E25F494D"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "71BF900C83503328"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "2AA92A1DCA9C3CAF"
				item: "powah:magmator_hardened"
				type: "item"
			}]
			x: 3.5d
			y: 13.5d
		}
		{
			dependencies: [
				"67DE5F982629BB34"
				"5E090C9BB4DAA5D4"
			]
			id: "2AE17A935862BCBF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "56DB84169DD497F9"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "0F62C43192364B78"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "297B24A480069307"
				item: "powah:furnator_hardened"
				type: "item"
			}]
			x: 4.5d
			y: 13.5d
		}
		{
			dependencies: [
				"60F5DC851FCFF1B2"
				"5E090C9BB4DAA5D4"
			]
			id: "6230C6884B800689"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3BC3FB37A2F3B9C8"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "679396D0CC02A060"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "1FAA953BA925186C"
				item: "powah:thermo_generator_hardened"
				type: "item"
			}]
			x: 5.5d
			y: 13.5d
		}
		{
			dependencies: [
				"5F6152CF085D75D6"
				"5E090C9BB4DAA5D4"
			]
			id: "05B0A7D0B991050F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1713B3EA72A983CB"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "298FF735E0518BDD"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				count: 36L
				id: "60AF685179E1AB4B"
				item: "powah:reactor_hardened"
				type: "item"
			}]
			title: "反应堆(硬化)"
			x: 6.5d
			y: 13.5d
		}
		{
			dependencies: [
				"7890A1478121D2CD"
				"5E090C9BB4DAA5D4"
			]
			id: "6C8148B3AE0A2222"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0F06DF97F7F5C468"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "02E45FFD4554B630"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "11F169284433B9B7"
				item: "powah:solar_panel_hardened"
				type: "item"
			}]
			x: 7.5d
			y: 13.5d
		}
		{
			dependencies: [
				"604AB95A7600B150"
				"562BD37539EE318E"
			]
			id: "79421463F01ED2D7"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "399E51AED917E377"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "5D1E0C5F63B02C2C"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "022DA80D4ABA1C00"
				item: {
					Count: 1
					id: "powah:battery_blazing"
					tag: { }
				}
				type: "item"
			}]
			x: -5.5d
			y: 14.5d
		}
		{
			dependencies: ["562BD37539EE318E"]
			hide_dependency_lines: true
			id: "13FDBA4BC4514F58"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "49FD98A963240BF5"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "495A35BBE79525E0"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "0E9A11E8D18F4183"
				item: "powah:energy_discharger_blazing"
				type: "item"
			}]
			x: -8.5d
			y: 14.5d
		}
		{
			dependencies: [
				"04930767C52C7CC2"
				"562BD37539EE318E"
			]
			id: "04811BA420B80046"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "75514E5D42839B97"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "76BC2C5552C75D1A"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "3F987D5B5E9410DF"
				item: "powah:energy_hopper_blazing"
				type: "item"
			}]
			x: -7.5d
			y: 14.5d
		}
		{
			dependencies: [
				"7444B85503DE2C53"
				"562BD37539EE318E"
			]
			id: "1D80E6C3327AB376"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1E5B513A15344949"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "37434D0CC9A5B0E4"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "23562B6B16954B2E"
				item: "powah:player_transmitter_blazing"
				type: "item"
			}]
			x: -6.5d
			y: 14.5d
		}
		{
			dependencies: ["562BD37539EE318E"]
			hide_dependency_lines: true
			id: "564F731F0F3E5AEC"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4995ADDF96D62FC9"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "6B9F589C2E8BC325"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "798970D8B1A023B8"
				item: "powah:ender_cell_blazing"
				type: "item"
			}]
			x: -3.0d
			y: 14.5d
		}
		{
			dependencies: [
				"7FEE780F37A1322E"
				"562BD37539EE318E"
			]
			id: "25088F06228F8561"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7B2072A45F758E4C"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "60BE5AA8BA68AA39"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "50B61EC144D1832F"
				item: "powah:energy_cell_blazing"
				type: "item"
			}]
			x: -2.0d
			y: 14.5d
		}
		{
			dependencies: [
				"029922AB09F8C76A"
				"562BD37539EE318E"
			]
			dependency_requirement: "one_completed"
			hide_dependency_lines: true
			id: "0C5936697C9B3716"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "049B68462787BAFD"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "55AAD824064689CB"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "2A02CDC7FDC41BD1"
				item: "powah:energy_cable_blazing"
				type: "item"
			}]
			x: 1.0d
			y: 14.5d
		}
		{
			dependencies: [
				"0C5936697C9B3716"
				"562BD37539EE318E"
			]
			id: "1F55B2FA187F63DD"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "28804F9A97A3E425"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "22B65065260F8A5F"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "1F8C7940E92E8229"
				item: "powah:ender_gate_blazing"
				type: "item"
			}]
			x: 2.0d
			y: 14.5d
		}
		{
			dependencies: [
				"6ED2F8A8DEED417D"
				"562BD37539EE318E"
			]
			id: "0C7D0AB254DF8CF1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "62814A9FA8AC2592"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "2721ED866794D71C"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "3F7EBFC46DB90461"
				item: "powah:magmator_blazing"
				type: "item"
			}]
			x: 3.5d
			y: 14.5d
		}
		{
			dependencies: [
				"2AE17A935862BCBF"
				"562BD37539EE318E"
			]
			id: "323F7A0C4FAC28D0"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2EEB2D20FA794E4D"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "7128821934715B0C"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "2E5070E71CCEDADE"
				item: "powah:furnator_blazing"
				type: "item"
			}]
			x: 4.5d
			y: 14.5d
		}
		{
			dependencies: [
				"6230C6884B800689"
				"562BD37539EE318E"
			]
			id: "44C1315098B9CF3F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "72A14865203125D7"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "17B757FE2EAEA913"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "33E55040BA517135"
				item: "powah:thermo_generator_blazing"
				type: "item"
			}]
			x: 5.5d
			y: 14.5d
		}
		{
			dependencies: [
				"05B0A7D0B991050F"
				"562BD37539EE318E"
			]
			id: "6754612E9AD4B9C0"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2EC728FD9C8F05B4"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "62477697CA962B0F"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				count: 36L
				id: "6CA9B3DA3B774238"
				item: "powah:reactor_blazing"
				type: "item"
			}]
			title: "反应堆(炽焰)"
			x: 6.5d
			y: 14.5d
		}
		{
			dependencies: [
				"6C8148B3AE0A2222"
				"562BD37539EE318E"
			]
			id: "7C34EED27A9737D4"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "53872D4056C7D9D8"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "4BCF7E68022B30ED"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "6F667F3C54316DA3"
				item: "powah:solar_panel_blazing"
				type: "item"
			}]
			x: 7.5d
			y: 14.5d
		}
		{
			dependencies: ["7D7983F39E6E818D"]
			hide_dependency_lines: true
			id: "555566F1148F5229"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1A18165C0855BF42"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "6A9E1326A8A119F5"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "04410F1240964912"
				item: "powah:energy_discharger_niotic"
				type: "item"
			}]
			x: -8.5d
			y: 15.5d
		}
		{
			dependencies: ["04811BA420B80046"]
			id: "5A8DE0C7D4F28E05"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "14975DBB973B9595"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "7041E7242D3861A0"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "5E09C884ABD4A22F"
				item: "powah:energy_hopper_niotic"
				type: "item"
			}]
			x: -7.5d
			y: 15.5d
		}
		{
			dependencies: ["1D80E6C3327AB376"]
			id: "2DD9994665799747"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7BE32CBC4F2E912D"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "6E4CC4AEC9D8721A"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "7FB09E6585E098BB"
				item: "powah:player_transmitter_niotic"
				type: "item"
			}]
			x: -6.5d
			y: 15.5d
		}
		{
			dependencies: ["79421463F01ED2D7"]
			id: "03798EF7AB47BB3D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "10E7F35B7262EA37"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "6A8D144C5BA3D081"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "732BEE5909694731"
				item: {
					Count: 1
					id: "powah:battery_niotic"
					tag: { }
				}
				type: "item"
			}]
			x: -5.5d
			y: 15.5d
		}
		{
			dependencies: ["7D7983F39E6E818D"]
			hide_dependency_lines: true
			id: "1CCCA214865526F3"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "26824E0692C7CD6E"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "375C89AC95AC1F3B"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "2FE8500140D5D2E8"
				item: "powah:ender_cell_niotic"
				type: "item"
			}]
			x: -3.0d
			y: 15.5d
		}
		{
			dependencies: ["25088F06228F8561"]
			id: "6EFB4BF8110F2712"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "336DE088F93C2773"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "43505AB66A3199A4"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "6B0A0DA8C0C79568"
				item: "powah:energy_cell_niotic"
				type: "item"
			}]
			x: -2.0d
			y: 15.5d
		}
		{
			dependencies: [
				"0C5936697C9B3716"
				"7D7983F39E6E818D"
			]
			dependency_requirement: "one_completed"
			hide_dependency_lines: true
			id: "68B604EF40198B8D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4777EDFDBBE22036"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "2A076AC00542C7CD"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "73D0B49F46505EB0"
				item: "powah:energy_cable_niotic"
				type: "item"
			}]
			x: 1.0d
			y: 15.5d
		}
		{
			dependencies: ["68B604EF40198B8D"]
			id: "584BD9A6F7594867"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "553724173203A123"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "782038EC955F66DE"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "6947E1FB9DF4719A"
				item: "powah:ender_gate_niotic"
				type: "item"
			}]
			x: 2.0d
			y: 15.5d
		}
		{
			dependencies: ["0C7D0AB254DF8CF1"]
			id: "03442045ED56068F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5A4A2AA7E595FEB5"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "22599F0527EEA03D"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "0AD625D068474E21"
				item: "powah:magmator_niotic"
				type: "item"
			}]
			x: 3.5d
			y: 15.5d
		}
		{
			dependencies: ["323F7A0C4FAC28D0"]
			id: "24EE138780C43447"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "09289D5CE0C59DF4"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "285C49BA853D2FCB"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "436D9B4714379730"
				item: "powah:furnator_niotic"
				type: "item"
			}]
			x: 4.5d
			y: 15.5d
		}
		{
			dependencies: ["44C1315098B9CF3F"]
			id: "28EE8C172F9DF5C1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "141433821E0AA5EF"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "61A665E0362576F0"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "3507C8B858D39314"
				item: "powah:thermo_generator_niotic"
				type: "item"
			}]
			x: 5.5d
			y: 15.5d
		}
		{
			dependencies: ["6754612E9AD4B9C0"]
			id: "0FFF2BEE5D8EBE12"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "093558B6D3AD8663"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "7DAAF18666B1C847"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				count: 36L
				id: "011563130B58F561"
				item: "powah:reactor_niotic"
				type: "item"
			}]
			title: "反应堆(氮素)"
			x: 6.5d
			y: 15.5d
		}
		{
			dependencies: ["7C34EED27A9737D4"]
			id: "39386F88CB38CF36"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "33464F89B8D1FB7E"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "5486B9D0023A62EB"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "56504998BB4B686E"
				item: "powah:solar_panel_niotic"
				type: "item"
			}]
			x: 7.5d
			y: 15.5d
		}
		{
			dependencies: ["7E92ED270C67FDE5"]
			hide_dependency_lines: true
			id: "6D88C19F47D0D469"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "06074E4D85E9625E"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "27DAD35DC7E2C8C4"
					type: "xp"
					xp: 10
				}
			]
			shape: "circle"
			tasks: [{
				id: "1BAC364D2AF426D3"
				item: "powah:capacitor_basic_tiny"
				type: "item"
			}]
			title: "等级:§7微型"
			x: -0.5d
			y: 11.5d
		}
		{
			dependencies: ["7E92ED270C67FDE5"]
			hide_dependency_lines: true
			id: "66ECC26BC81D0093"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "72AD97ECD624C9F1"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "4384750DCCD8BCAC"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "3027F7F9B0D0EB88"
				item: "powah:capacitor_basic"
				type: "item"
			}]
			title: "等级:§b基础"
			x: -0.5d
			y: 12.5d
		}
		{
			dependencies: ["03798EF7AB47BB3D"]
			id: "63E57D2745D24761"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4E00D9EA078233CE"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "1D3A0A467A2F1DB2"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "064EE05D48DAFC60"
				item: {
					Count: 1
					id: "powah:battery_spirited"
					tag: { }
				}
				type: "item"
			}]
			x: -5.5d
			y: 16.5d
		}
		{
			dependencies: ["2DD9994665799747"]
			id: "11F0D662FF4DC335"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0869AE48992AACA7"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "0CDDF00FD7A96C58"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "5409500542236E1A"
				item: "powah:player_transmitter_spirited"
				type: "item"
			}]
			x: -6.5d
			y: 16.5d
		}
		{
			dependencies: ["5A8DE0C7D4F28E05"]
			id: "18B20F4F27F37197"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0354276CBFD56D27"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "3CCEF8B4922C5AD9"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "0BB591BF32031D66"
				item: "powah:energy_hopper_spirited"
				type: "item"
			}]
			x: -7.5d
			y: 16.5d
		}
		{
			dependencies: ["25EFC21A3C48E0B6"]
			hide_dependency_lines: true
			id: "0536D7B41964FE38"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1B55A38A45331C60"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "49AED7663FF7E87A"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "26D5E58095BD9A31"
				item: "powah:energy_discharger_spirited"
				type: "item"
			}]
			x: -8.5d
			y: 16.5d
		}
		{
			dependencies: ["25EFC21A3C48E0B6"]
			hide_dependency_lines: true
			id: "486356E189081ADC"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7123F500A918DB75"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "617FF7989E95A7FF"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "60A94CD9FA76AF4B"
				item: "powah:ender_cell_spirited"
				type: "item"
			}]
			x: -3.0d
			y: 16.5d
		}
		{
			dependencies: ["6EFB4BF8110F2712"]
			id: "4449AD53BCE797E1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "14DFF6417C3A2982"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "576792D73AB84741"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "355DD6351F004027"
				item: "powah:energy_cell_spirited"
				type: "item"
			}]
			x: -2.0d
			y: 16.5d
		}
		{
			dependencies: [
				"68B604EF40198B8D"
				"25EFC21A3C48E0B6"
			]
			dependency_requirement: "one_completed"
			hide_dependency_lines: true
			id: "379EBE12E3F61679"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4628C572883B4735"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "6258FB9CBCB92B8E"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "112F14F2CFAAE9B7"
				item: "powah:energy_cable_spirited"
				type: "item"
			}]
			x: 1.0d
			y: 16.5d
		}
		{
			dependencies: ["379EBE12E3F61679"]
			id: "335927C2D234451A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7870CE9274C8B1E9"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "057CE0117FCA9D7E"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "5AA3BDCABBC5E9C1"
				item: "powah:ender_gate_spirited"
				type: "item"
			}]
			x: 2.0d
			y: 16.5d
		}
		{
			dependencies: ["03442045ED56068F"]
			id: "36B14701D95C3C21"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "03ADA0DDF99F29A6"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "6380B215B854230B"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "2E611F9024AF2142"
				item: "powah:magmator_spirited"
				type: "item"
			}]
			x: 3.5d
			y: 16.5d
		}
		{
			dependencies: ["24EE138780C43447"]
			id: "5A7C92F05AF39FDB"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "612FB730577BC7F4"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "12D1549AD6F0AE28"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "69E6F047C9CE65D5"
				item: "powah:furnator_spirited"
				type: "item"
			}]
			x: 4.5d
			y: 16.5d
		}
		{
			dependencies: ["28EE8C172F9DF5C1"]
			id: "637A8E4BBF108417"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "33E8FCF6F8E7CB05"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "7E79681CBAAC299B"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "32F356FA79D13349"
				item: "powah:thermo_generator_spirited"
				type: "item"
			}]
			x: 5.5d
			y: 16.5d
		}
		{
			dependencies: ["0FFF2BEE5D8EBE12"]
			id: "341486C9F277FEB7"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0FD775B49FC7AD8C"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "3B76ED3D475D74E5"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				count: 36L
				id: "31C9199965D38CAB"
				item: "powah:reactor_spirited"
				type: "item"
			}]
			title: "反应堆(烈魂)"
			x: 6.5d
			y: 16.5d
		}
		{
			dependencies: ["39386F88CB38CF36"]
			id: "760F80E1C273C0AD"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "046DDDDCAAF2BCEB"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "4296AEDE69358AC9"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "6A76F943F5E6E2E7"
				item: "powah:solar_panel_spirited"
				type: "item"
			}]
			x: 7.5d
			y: 16.5d
		}
		{
			dependencies: [
				"11F0D662FF4DC335"
				"4F1FFC02F4EAA2E6"
			]
			id: "348FEC9A50C2E62E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "461364E687D0F236"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "2AFB6334D91B8FD7"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "3FE9AB0CAE241A22"
				item: "powah:player_transmitter_nitro"
				type: "item"
			}]
			x: -6.5d
			y: 17.5d
		}
		{
			dependencies: ["4F1FFC02F4EAA2E6"]
			hide_dependency_lines: true
			id: "24510A5C98703C84"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4854D191BA53A416"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "57B8413287334672"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "5526935A94E25966"
				item: "powah:energy_discharger_nitro"
				type: "item"
			}]
			x: -8.5d
			y: 17.5d
		}
		{
			dependencies: [
				"18B20F4F27F37197"
				"4F1FFC02F4EAA2E6"
			]
			hide_dependency_lines: true
			id: "41DC771674A6C387"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "45CDC85FB4C3A138"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "423D4566074A0A55"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "01ABA3DA3EFD0C2A"
				item: "powah:energy_hopper_nitro"
				type: "item"
			}]
			x: -7.5d
			y: 17.5d
		}
		{
			dependencies: [
				"63E57D2745D24761"
				"4F1FFC02F4EAA2E6"
			]
			id: "7EBA36EF1858F428"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "32898EFFF794C2B0"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "29DA52B9C2CC7AC6"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "053841A23C52379F"
				item: {
					Count: 1
					id: "powah:battery_nitro"
					tag: { }
				}
				type: "item"
			}]
			x: -5.5d
			y: 17.5d
		}
		{
			dependencies: ["4F1FFC02F4EAA2E6"]
			id: "3B28A8BA318D438F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "477548F6982B6299"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "47E25C76BF619695"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "4FBBE996E440514A"
				item: "powah:ender_cell_nitro"
				type: "item"
			}]
			x: -3.0d
			y: 17.5d
		}
		{
			dependencies: [
				"4449AD53BCE797E1"
				"4F1FFC02F4EAA2E6"
			]
			id: "503CD7E104C8BA5D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "78E1CECA051B681A"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "4AC330025BE2F012"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "6B9FC8EDB5ED952E"
				item: "powah:energy_cell_nitro"
				type: "item"
			}]
			x: -2.0d
			y: 17.5d
		}
		{
			dependencies: [
				"379EBE12E3F61679"
				"4F1FFC02F4EAA2E6"
			]
			dependency_requirement: "one_completed"
			id: "3CD1F9E00E33C6C9"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "521099624333DE1E"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "737998404DAF6FD5"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "25DA30BFB0E2C71B"
				item: "powah:energy_cable_nitro"
				type: "item"
			}]
			x: 1.0d
			y: 17.5d
		}
		{
			dependencies: [
				"3CD1F9E00E33C6C9"
				"4F1FFC02F4EAA2E6"
			]
			id: "44F016F465082C55"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "68A1E17843A0B791"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "601F715D79D3E748"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "09770035C196D36D"
				item: "powah:ender_gate_nitro"
				type: "item"
			}]
			x: 2.0d
			y: 17.5d
		}
		{
			dependencies: [
				"36B14701D95C3C21"
				"4F1FFC02F4EAA2E6"
			]
			id: "4E67DE071FC9D80D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1279B07A8A80FEF8"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "41A64BC8DF294061"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "36A675359DAB88CA"
				item: "powah:magmator_nitro"
				type: "item"
			}]
			x: 3.5d
			y: 17.5d
		}
		{
			dependencies: [
				"5A7C92F05AF39FDB"
				"4F1FFC02F4EAA2E6"
			]
			id: "129F6987E144B048"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1E983232CF2EF67B"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "58FFEDCA3B80E811"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "4030018C42DCB948"
				item: "powah:furnator_nitro"
				type: "item"
			}]
			x: 4.5d
			y: 17.5d
		}
		{
			dependencies: [
				"637A8E4BBF108417"
				"4F1FFC02F4EAA2E6"
			]
			id: "71D08D1C8EA4C631"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "08E9287775B53FFD"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "290D33D880E718A3"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "7E8AFD4455E17266"
				item: "powah:thermo_generator_nitro"
				type: "item"
			}]
			x: 5.5d
			y: 17.5d
		}
		{
			dependencies: [
				"341486C9F277FEB7"
				"4F1FFC02F4EAA2E6"
			]
			id: "61DCECE1FC38E151"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "32D20D1E95358153"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "68856C5C7FBB49D4"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				count: 36L
				id: "3EA18C3E289956AE"
				item: "powah:reactor_nitro"
				type: "item"
			}]
			title: "反应堆(硝化)"
			x: 6.5d
			y: 17.5d
		}
		{
			dependencies: [
				"760F80E1C273C0AD"
				"4F1FFC02F4EAA2E6"
			]
			id: "393050BEA59F1570"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7160C8F86CD5C89D"
					table_id: 1432029627393651571L
					type: "random"
				}
				{
					id: "0D5273DE2F25A977"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "0E6B0D7B3BAC9760"
				item: "powah:solar_panel_nitro"
				type: "item"
			}]
			x: 7.5d
			y: 17.5d
		}
		{
			dependencies: [
				"7E92ED270C67FDE5"
				"6D88C19F47D0D469"
			]
			description: ["用于通过充能法球为物品充能."]
			hide_dependency_lines: true
			id: "1C273D9E046FD18A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2DEB4C0CFE7C4D29"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "601B548099153FD8"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "2E74430108DE6170"
				item: "powah:energizing_rod_starter"
				type: "item"
			}]
			x: -4.5d
			y: 11.5d
		}
		{
			dependencies: [
				"1C273D9E046FD18A"
				"66ECC26BC81D0093"
			]
			id: "4EA69350A20B0B5B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5A843A787074F467"
					table_id: 5411577453178694060L
					type: "random"
				}
				{
					id: "0C561DF80C2D9A8A"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "1B8D93C482A9186D"
				item: "powah:energizing_rod_basic"
				type: "item"
			}]
			x: -4.5d
			y: 12.5d
		}
		{
			dependencies: [
				"4EA69350A20B0B5B"
				"5E090C9BB4DAA5D4"
			]
			id: "09DECE4E7977D852"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "15E6315BB25D077E"
					table_id: 323217218064538611L
					type: "random"
				}
				{
					id: "4C7355E80870AA3E"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "11BF64326BEABFF4"
				item: "powah:energizing_rod_hardened"
				type: "item"
			}]
			x: -4.5d
			y: 13.5d
		}
		{
			dependencies: [
				"09DECE4E7977D852"
				"562BD37539EE318E"
			]
			id: "172B0296F87D6725"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7809EE77DCB57F35"
					table_id: 2050559670769664902L
					type: "random"
				}
				{
					id: "5C502AD7902DECF1"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "4FF7B35FC042EDA3"
				item: "powah:energizing_rod_blazing"
				type: "item"
			}]
			x: -4.5d
			y: 14.5d
		}
		{
			dependencies: [
				"172B0296F87D6725"
				"7D7983F39E6E818D"
			]
			id: "270B5EA3E710A209"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "44E183EC5FAFAACD"
					table_id: 5644430283229160518L
					type: "random"
				}
				{
					id: "1F35D42EC2F4A0A6"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "4779D480ED4EB6E9"
				item: "powah:energizing_rod_niotic"
				type: "item"
			}]
			x: -4.5d
			y: 15.5d
		}
		{
			dependencies: [
				"270B5EA3E710A209"
				"25EFC21A3C48E0B6"
			]
			id: "69E572EA56B3B31D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "46BB2C3AB315B384"
					table_id: 5896103029501993867L
					type: "random"
				}
				{
					id: "4D3A6B1A529D586F"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "11FD956A8B254218"
				item: "powah:energizing_rod_spirited"
				type: "item"
			}]
			x: -4.5d
			y: 16.5d
		}
		{
			dependencies: [
				"69E572EA56B3B31D"
				"4F1FFC02F4EAA2E6"
			]
			hide_dependency_lines: true
			id: "2C9EE7BE03767976"
			rewards: [
				{
					id: "084A23000B1F4C8A"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "7EBFFE8FD0DE1E38"
					table_id: 1432029627393651571L
					type: "loot"
				}
			]
			tasks: [{
				id: "4AC121B4951DE6E9"
				item: "powah:energizing_rod_nitro"
				type: "item"
			}]
			x: -4.5d
			y: 17.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r创作,专用于AllTheMods整合包."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"该任务为隐藏内容,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "1318BA3E5DA9B50C"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "0428F19A7F7919A1"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "652DD9984C65728A"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: -0.5d
			y: 2.5d
		}
	]
	title: "能量塔"
}
