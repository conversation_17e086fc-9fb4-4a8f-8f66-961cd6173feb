{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "pneumaticcraft"
	group: "2B51AC12041E3F89"
	icon: {
		Count: 1
		id: "pneumaticcraft:pneumatic_wrench"
		tag: {
			"pneumaticcraft:air": 30000
		}
	}
	id: "5E31DF282998B992"
	images: [
		{
			height: 0.3d
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 3.5d
			y: 3.5d
		}
		{
			height: 0.3d
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 24.0d
			y: 3.4000000000000004d
		}
		{
			height: 7.0d
			image: "atm:textures/questpics/pneumaticcraft/pressure_chamber.png"
			rotation: 0.0d
			width: 12.413333333333334d
			x: 2.0d
			y: 7.0d
		}
		{
			height: 8.0d
			image: "atm:textures/questpics/pneumaticcraft/assembly_line.png"
			rotation: 0.0d
			width: 15.21514629948365d
			x: 27.0d
			y: 8.0d
		}
		{
			height: 8.0d
			image: "atm:textures/questpics/pneumaticcraft/pneumatic_set.png"
			rotation: 0.0d
			width: 8.806722689075631d
			x: 19.0d
			y: 0.0d
		}
		{
			height: 1.0d
			image: "pneumaticcraft:item/pneumatic_helmet"
			rotation: 0.0d
			width: 1.0d
			x: 26.5d
			y: -1.0d
		}
		{
			height: 1.0d
			image: "pneumaticcraft:item/pneumatic_chestplate"
			rotation: 0.0d
			width: 1.0d
			x: 26.5d
			y: 0.0d
		}
		{
			height: 1.0d
			image: "pneumaticcraft:item/pneumatic_leggings"
			rotation: 0.0d
			width: 1.0d
			x: 26.5d
			y: 1.0d
		}
		{
			height: 1.0d
			image: "pneumaticcraft:item/pneumatic_boots"
			rotation: 0.0d
			width: 1.0d
			x: 26.5d
			y: 2.0d
		}
		{
			height: 1.0d
			image: "ftbchunks:textures/faces/pneumaticcraft/amadrone.png"
			rotation: 0.0d
			width: 1.0d
			x: 15.5d
			y: 7.0d
		}
		{
			height: 1.0d
			image: "ftbchunks:textures/faces/pneumaticcraft/drone.png"
			rotation: 0.0d
			width: 1.0d
			x: 21.0d
			y: 4.0d
		}
		{
			height: 1.0d
			image: "pneumaticcraft:item/salmon_tempura"
			rotation: 0.0d
			width: 1.0d
			x: 8.5d
			y: 8.5d
		}
		{
			height: 1.0d
			image: "pneumaticcraft:item/sourdough_bread"
			rotation: 0.0d
			width: 1.0d
			x: 7.5d
			y: 8.5d
		}
		{
			click: "#3CFE522A4B7CC2EC"
			height: 1.0d
			image: "pneumaticcraft:block/chest/front_panel"
			rotation: 0.0d
			width: 1.0d
			x: 5.0d
			y: -1.5d
		}
		{
			click: "#440B1E1D4951F808"
			height: 1.0d
			image: "pneumaticcraft:block/chest/front_panel_smart"
			rotation: 0.0d
			width: 1.0d
			x: 5.0d
			y: -3.0d
		}
		{
			height: 1.0d
			image: "pneumaticcraft:item/seismic_sensor"
			rotation: 0.0d
			width: 1.0d
			x: 7.0d
			y: 2.5d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/pneumaticcraft/pnc_title.png"
			rotation: 0.0d
			width: 20.88888888888889d
			x: 6.0d
			y: -7.0d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/pneumaticcraft/pnc_logo.png"
			rotation: 0.0d
			width: 4.0d
			x: 23.0d
			y: -4.0d
		}
	]
	order_index: 10
	quest_links: [ ]
	quests: [
		{
			description: [
				"&d气动工艺&f的核心是压力系统！这些任务不会涵盖模组全部内容，但仍会涉及大量知识！请记得查看JEI、阅读物品提示和PCB:R手册，它们都非常实用。\\n\\n"
				"起步阶段需要合成&3&a压缩铁锭&f&r!最简单的方法是挖个坑,投入铁锭(或铁块)后用TNT引爆!\\n\\n"
				"爆炸可能会损耗部分材料,但这个风险值得承担!"
			]
			hide_dependency_lines: false
			id: "371A34B297C8A8EF"
			min_width: 250
			rewards: [
				{
					id: "2DA556E49F7B831C"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "pneumaticcraft:book"
						}
					}
					type: "item"
				}
				{
					id: "3124670994EF9DD3"
					type: "xp"
					xp: 10
				}
			]
			size: 1.5d
			subtitle: "空气的&a力量&f!"
			tasks: [{
				id: "1D5049AE4BC0BA39"
				item: "pneumaticcraft:ingot_iron_compressed"
				type: "item"
			}]
			title: "§d气动工艺:重置版§f"
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: ["核心材料之一&3&a防爆石&f&r,先用&a压缩铁&f和普通石头合成32个."]
			id: "65E2AF881709C896"
			min_required_dependencies: 1
			rewards: [{
				id: "0D8976051123BE39"
				type: "xp"
				xp: 50
			}]
			subtitle: "需求量很大"
			tasks: [{
				count: 32L
				id: "052A7C7918C3A0D5"
				item: "pneumaticcraft:reinforced_stone"
				type: "item"
			}]
			title: "&a防爆石&f"
			x: 4.0d
			y: 0.0d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: ["&a压缩铁&f可制作&3&a压缩铁甲&f&r,基础属性与&a铁甲&f相同,但具有更好的&a盔甲韧性&f和&a击退抗性&f."]
			icon: {
				Count: 1
				id: "pneumaticcraft:compressed_iron_chestplate"
				tag: {
					Damage: 0
				}
			}
			id: "5D64250B940BC8FD"
			optional: true
			rewards: [
				{
					count: 5
					id: "2141F2BE0C5605A5"
					item: "pneumaticcraft:ingot_iron_compressed"
					random_bonus: 5
					type: "item"
				}
				{
					id: "5E2FC4A4D4AFA683"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "&a比铁更优秀&f!"
			tasks: [
				{
					id: "304A15731EA889F5"
					item: {
						Count: 1
						id: "pneumaticcraft:compressed_iron_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "3916C13A250BB5E9"
					item: {
						Count: 1
						id: "pneumaticcraft:compressed_iron_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "044425D8D6512ED7"
					item: {
						Count: 1
						id: "pneumaticcraft:compressed_iron_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "43DC656348DC56EB"
					item: {
						Count: 1
						id: "pneumaticcraft:compressed_iron_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&a压缩铁&f护甲"
			x: -2.5d
			y: 0.0d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			dependency_requirement: "one_completed"
			description: [
				"这真的可行吗？\\n\\n"
				"若要自动化该流程,可使用&a&d神秘农业&f&r或&e&d资源蜜蜂&f&r.当然也可以用...爆炸来实现自动化."
			]
			hide_dependency_lines: false
			hide_until_deps_visible: true
			id: "6FD65139CD50A8C0"
			min_required_dependencies: 1
			optional: true
			rewards: [
				{
					count: 5
					id: "6318CBE3EBAB4501"
					item: "pneumaticcraft:ingot_iron_compressed"
					random_bonus: 5
					type: "item"
				}
				{
					id: "5BDFCB2DDCBA2C60"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "厌倦了爆破作业？"
			tasks: [{
				id: "47CC78B479496E5B"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mysticalagriculture:compressed_iron_seeds"
							}
							{
								Count: 1b
								id: "productivebees:configurable_honeycomb"
								tag: {
									EntityTag: {
										type: "productivebees:compressed_iron"
									}
								}
							}
						]
					}
				}
				title: "自动化&a压缩铁&f"
				type: "item"
			}]
			x: 1.5d
			y: -1.5d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: ["输送压力需要获取&3&a压力管道&f&r.你可能会问什么压力？继续任务线就会明白它们的用途."]
			id: "40F634AC38F42B88"
			rewards: [{
				id: "740A676015A9235B"
				type: "xp"
				xp: 50
			}]
			subtitle: "压力系统？"
			tasks: [{
				count: 16L
				id: "78082DDA22FC7B9B"
				item: { Count: 16, id: "pneumaticcraft:pressure_tube" }
				type: "item"
			}]
			title: "压力传输"
			x: 0.0d
			y: 2.0d
		}
		{
			dependencies: ["3E16CF590C911129"]
			description: ["在&a空气压缩机&f中产生气压时,如果没有排放压力的地方,气压就会直接消失.在制造气压前,请确保有排放压力的通道."]
			hide_until_deps_visible: true
			id: "7B2008977702D0C8"
			rewards: [{
				id: "75CB05A0CB74D837"
				type: "xp"
				xp: 10
			}]
			subtitle: "RF或FE怎么了？"
			tasks: [{
				id: "1EEC7B67E037F766"
				title: "理解压力"
				type: "checkmark"
			}]
			x: 0.0d
			y: 5.5d
		}
		{
			dependencies: [
				"122EDEC12C9A05D2"
				"65E2AF881709C896"
			]
			description: [
				"&3&a压力室&f&r是由&3&a压力室墙壁&f&r(墙面可用&3&a压力室玻璃&f&r替代)构成的重要多方块结构.虽然&a压力室&f最小可以是3x3x3的多方块结构,但&d&a脉动黑洞&f&r需要5x5x5的结构,所以我们将建造这个尺寸.任务所需的方块就是这些.&d&a脉动黑洞&f需要4.9巴的气压.&d\\n\\n"
				"要使用&a压力室&f,你需要用之前制作的&3&a压力管道&f&r将气压导入&3&a压力室气阀&f&r,记得在&a加压阀门&f中放入安全升级.&l不同配方需要不同量的气压&r.\\n\\n"
				"你需要能够从&3&a压力室&f&r输入和输出物品,这时就需要&3&a压力室接口&f&r.外部蓝色表示用于输入物品,外部金色表示用于输出物品,可以在任意面上各放置一个."
			]
			icon: "pneumaticcraft:pressure_chamber_interface"
			id: "63F6F4EBCEB914B0"
			min_width: 400
			rewards: [
				{
					id: "32AAE9BB01CE8ECA"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "3747DBACF5E6FB76"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "加压中!"
			tasks: [
				{
					count: 95L
					id: "50C621641241CF5F"
					item: "pneumaticcraft:pressure_chamber_wall"
					type: "item"
				}
				{
					id: "0ED2199E0628F442"
					item: "pneumaticcraft:pressure_chamber_valve"
					type: "item"
				}
				{
					count: 2L
					id: "22F4EC53E46518B1"
					item: { Count: 2, id: "pneumaticcraft:pressure_chamber_interface" }
					type: "item"
				}
			]
			title: "&a压力室&f"
			x: 4.0d
			y: 4.0d
		}
		{
			dependencies: ["3E16CF590C911129"]
			dependency_requirement: "all_started"
			description: [
				"我是不是忘了说所有东西都可能爆炸...\\n\\n"
				"幸运的是有个简单的解决办法!将&3安全升级&r放入机器中可以确保它们不会爆炸,把这些升级放入任何适用的机器中."
			]
			id: "122EDEC12C9A05D2"
			rewards: [
				{
					id: "28B03CA691CFE406"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "7E934BC10CBFC140"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "要是有个简单的解决办法就好了..."
			tasks: [{
				count: 2L
				id: "6914FD46650A6B01"
				item: { Count: 2, id: "pneumaticcraft:security_upgrade" }
				type: "item"
			}]
			title: "防止爆炸？"
			x: 2.0d
			y: 4.0d
		}
		{
			dependencies: ["40F634AC38F42B88"]
			description: ["&3&a空气压缩机&f&r是制造气压的简单方法,只需放入一些可燃物品,然后见证奇迹发生!(建议至少制作3台作为起步)."]
			id: "3E16CF590C911129"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "019B8AB93E548C0B"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "43237331728E72F7"
					type: "xp"
					xp: 50
				}
			]
			size: 1.5d
			subtitle: "感受压力!"
			tasks: [{
				id: "56A35164A8E072BE"
				item: "pneumaticcraft:air_compressor"
				type: "item"
			}]
			title: "&a产出气压&f"
			x: 0.0d
			y: 4.0d
		}
		{
			dependencies: ["0AEAEA976ED0C470"]
			description: ["&3气动扳手&r用于移动或拆除你的机器和管道."]
			icon: {
				Count: 1
				id: "pneumaticcraft:pneumatic_wrench"
				tag: {
					"pneumaticcraft:air": 30000
				}
			}
			id: "4FCC20D9F8F87B2F"
			rewards: [{
				id: "7E4E5363AE1CA314"
				type: "xp"
				xp: 100
			}]
			subtitle: "工具扭矩!"
			tasks: [{
				id: "46E93CA18CA750A4"
				item: "pneumaticcraft:pneumatic_wrench"
				type: "item"
			}]
			x: 2.0d
			y: 2.5d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: [
				"当&3&a流体储罐&f&r叠加放置并使用扳手连接后,将合并为大型储罐.\\n\\n该装置可使用桶装&d气动工艺&f液体进行合成."
				""
				"{image:pneumaticcraft:textures/patchouli/small_tanks.png width:250 height:250 align:center fit:true}"
			]
			id: "461C8F1E88AA58D9"
			rewards: [{
				id: "2E1ADC3288F58BC9"
				type: "xp"
				xp: 100
			}]
			subtitle: "32,000毫巴"
			tasks: [{
				id: "0E4936A2C65706CB"
				item: "pneumaticcraft:small_tank"
				type: "item"
			}]
			x: -1.5d
			y: 1.5d
		}
		{
			dependencies: ["461C8F1E88AA58D9"]
			id: "4E1E31EDD544EE10"
			rewards: [{
				id: "5E623C1FCBEE2007"
				type: "xp"
				xp: 100
			}]
			subtitle: "64,000毫巴"
			tasks: [{
				id: "66A0A8D0385970E1"
				item: "pneumaticcraft:medium_tank"
				type: "item"
			}]
			x: -2.5d
			y: 1.5d
		}
		{
			dependencies: ["43CDC28DC56BB3E2"]
			id: "0672462DA3599484"
			rewards: [{
				id: "19280C07F491B087"
				type: "xp"
				xp: 100
			}]
			subtitle: "512,000毫巴"
			tasks: [{
				id: "338B9093062C93C3"
				item: "pneumaticcraft:huge_tank"
				type: "item"
			}]
			x: -1.5d
			y: 2.5d
		}
		{
			dependencies: ["4E1E31EDD544EE10"]
			id: "43CDC28DC56BB3E2"
			rewards: [{
				id: "6C57AEA9C70325DA"
				type: "xp"
				xp: 100
			}]
			subtitle: "128,000毫巴"
			tasks: [{
				id: "4C8F07DD812CFC23"
				item: "pneumaticcraft:large_tank"
				type: "item"
			}]
			x: -2.5d
			y: 2.5d
		}
		{
			dependencies: [
				"65E2AF881709C896"
				"40F634AC38F42B88"
			]
			description: ["&3充电站&r用于使用气压为&a&d气动工艺&f&r中的各种工具和小装置充电."]
			id: "0AEAEA976ED0C470"
			rewards: [
				{
					id: "64A38E6B4E852959"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "26955C276DB8AAF4"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "电源线在哪？"
			tasks: [{
				id: "26885ED4036D69D2"
				item: "pneumaticcraft:charging_station"
				type: "item"
			}]
			x: 2.0d
			y: 1.0d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: ["&3&a全方向漏斗&f&r可以从任何方向输入物品,而且可以升级得比普通漏斗快得多!在不需要管道时非常实用."]
			id: "74015945716FD10A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6B53F77D49304789"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "7D374B9B3931A959"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "可配置的漏斗"
			tasks: [{
				id: "6ABBA72726C25C03"
				item: "pneumaticcraft:omnidirectional_hopper"
				type: "item"
			}]
			x: -2.0d
			y: -1.0d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: ["&3&a液体漏斗&f&r是用于液体的漏斗,不能替代管道."]
			id: "0FF050EC1EA6EAED"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7B191F2B0F0CB540"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "39835886D89BFA4D"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "谁需要管道？"
			tasks: [{
				id: "23CB951DCC261A67"
				item: "pneumaticcraft:liquid_hopper"
				type: "item"
			}]
			x: -1.0d
			y: -2.0d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: ["&d气动工艺&f的核心是&3&a传输装置&f&r,它就像一个可以放置在方块之间的漏斗."]
			id: "187816477B732517"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "504F3D3F9C530CE6"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "060389F0DBBA2CA2"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "更小的漏斗"
			tasks: [{
				id: "3B651487D6138939"
				item: "pneumaticcraft:transfer_gadget"
				type: "item"
			}]
			x: -2.0d
			y: -2.0d
		}
		{
			dependencies: ["0AEAEA976ED0C470"]
			description: ["&3压力表&r用于显示机器的额外信息."]
			icon: {
				Count: 1
				id: "pneumaticcraft:manometer"
				tag: {
					"pneumaticcraft:air": 30000
				}
			}
			id: "4CA17B0A80020B2F"
			rewards: [{
				id: "4D0C70DC51EA579B"
				type: "xp"
				xp: 50
			}]
			subtitle: "实用的工具"
			tasks: [{
				id: "772A9A02784FC672"
				item: "pneumaticcraft:manometer"
				type: "item"
			}]
			x: 3.0d
			y: 2.5d
		}
		{
			dependencies: ["0AEAEA976ED0C470"]
			description: ["&3&a伪装涂覆器&f&r可用于将&d气动工艺&f的机器隐藏在其它方块中."]
			icon: {
				Count: 1
				id: "pneumaticcraft:camo_applicator"
				tag: {
					"pneumaticcraft:air": 30000
				}
			}
			id: "53518D4ED99A242F"
			optional: true
			rewards: [{
				id: "75FD12C0A3A851B1"
				type: "xp"
				xp: 50
			}]
			subtitle: "想让东西看起来更酷？"
			tasks: [{
				id: "2669B32EE3AEDEF8"
				item: "pneumaticcraft:camo_applicator"
				type: "item"
			}]
			x: 1.0d
			y: 2.5d
		}
		{
			dependencies: ["63F6F4EBCEB914B0"]
			description: ["模组的下一个阶段需要收集&3&a原油&f&r.这种石油可以自然生成在主世界的地表."]
			id: "6CA01DCE1F4A0EC3"
			rewards: [
				{
					id: "062CCB570DD4E6C7"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "44090FCBF8CFEF1D"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "从&a铺路石&f到塑料"
			tasks: [{
				id: "6C705DCE141062B0"
				item: "pneumaticcraft:oil_bucket"
				type: "item"
			}]
			title: "&a原油&f"
			x: 6.0d
			y: 4.0d
		}
		{
			dependencies: ["6CA01DCE1F4A0EC3"]
			description: [
				"虽然地表可获取&a原油&f,但钻井是大量采集的最佳方式.先用&3&a地下石油探测器&f&r定位油层,再使用装满钻杆的&3&a气举&f&r进行开采(需压力驱动)."
				""
				"{image:pneumaticcraft:textures/patchouli/oil_pumping.png width:200 height:200 align:right fit:true}"
			]
			icon: "pneumaticcraft:gas_lift"
			id: "0A912B2E2BE34920"
			min_width: 250
			rewards: [
				{
					id: "7E8E9E149E98B9ED"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "4281CB04DFF3323E"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "石油钻探"
			tasks: [
				{
					id: "14E8E26455BB6C0F"
					item: "pneumaticcraft:gas_lift"
					type: "item"
				}
				{
					count: 32L
					id: "4DFB6DCDCBFF7A2C"
					item: { Count: 64, id: "pneumaticcraft:drill_pipe" }
					type: "item"
				}
				{
					id: "7325980352624229"
					item: "pneumaticcraft:seismic_sensor"
					type: "item"
				}
			]
			x: 6.0d
			y: 2.5d
		}
		{
			dependencies: [
				"6CA01DCE1F4A0EC3"
				"75D3F3C1D4A3EAA2"
			]
			description: [
				"我们将建造的&3&a炼油厂&f&r是1x1x5的多方块结构:底层为&3&a精炼厂控制器&f&r,上层四个&3精炼输出口&r.需输入原油并加热,可产出柴油、煤油、汽油和液化石油气.侧面安装&3&a隔热板&f&r可提升保温效果.\\n"
				"{image:atm:textures/questpics/pneumaticcraft/oil_refinery.png width:300 height:150 align:center}"
			]
			icon: "pneumaticcraft:refinery"
			id: "72C09FD89C28B1EC"
			min_width: 300
			rewards: [
				{
					id: "15AC5CAEB1BEFB6C"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					exclude_from_claim_all: true
					id: "571DECEFD3E98E82"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "324B49820DBDE841"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "现在我们'终于有进展了!"
			tasks: [
				{
					id: "09F05931EFA39343"
					item: "pneumaticcraft:refinery"
					type: "item"
				}
				{
					count: 4L
					id: "35E1DCDF9712B702"
					item: { Count: 4, id: "pneumaticcraft:refinery_output" }
					type: "item"
				}
				{
					count: 19L
					id: "77B3A5EDBE2053B2"
					item: { Count: 19, id: "pneumaticcraft:thermal_lagging" }
					type: "item"
				}
			]
			title: "&a炼油厂&f"
			x: 8.0d
			y: 4.0d
		}
		{
			dependencies: ["2AED15E3AC0346FE"]
			description: ["让东西更快."]
			hide_dependency_lines: false
			id: "456B9DD108CF340F"
			rewards: [{
				id: "0CEEE8F9DF067062"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "速度!"
			tasks: [{
				id: "4B9E4AD4FDA66443"
				item: "pneumaticcraft:speed_upgrade"
				type: "item"
			}]
			x: 11.0d
			y: 0.5d
		}
		{
			dependencies: ["6CA01DCE1F4A0EC3"]
			description: ["&3&a涡流管&f&r将压力分离为热空气和冷空气.冷空气流向蓝色端,热空气流向红色端."]
			id: "5AEE86EB39FD4087"
			rewards: [
				{
					id: "2D39886AB5C24FD7"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "60AA897B8ED37C5E"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "依然不是RF..."
			tasks: [{
				id: "1E83B5E813F34290"
				item: "pneumaticcraft:vortex_tube"
				type: "item"
			}]
			title: "热能生产"
			x: 6.0d
			y: 5.5d
		}
		{
			dependencies: ["5AEE86EB39FD4087"]
			description: ["要提升热能产量,请在&a涡流管&f的冷端安装&3&a散热片&f&r."]
			hide_dependency_lines: false
			id: "3F9CCCFEF4E84166"
			optional: true
			rewards: [
				{
					id: "6E37C28754F95214"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "29E2FD4639DD1E01"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "更多热量"
			tasks: [{
				id: "2B250211827F00CD"
				item: "pneumaticcraft:heat_sink"
				type: "item"
			}]
			title: "高效热能生产"
			x: 6.0d
			y: 7.0d
		}
		{
			dependencies: ["5AEE86EB39FD4087"]
			description: ["要转移&a涡流管&f产生的热量,可以制作&3&a热管&f&r."]
			id: "75D3F3C1D4A3EAA2"
			rewards: [
				{
					id: "2772083AD7D8FD9A"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "79F9CBFFFD2B0EFA"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "我们有热量需要转移 :)"
			tasks: [{
				id: "54D71DF75158990B"
				item: "pneumaticcraft:heat_pipe"
				type: "item"
			}]
			title: "热能传输"
			x: 8.0d
			y: 5.5d
		}
		{
			dependencies: ["2AED15E3AC0346FE"]
			description: ["防止物品爆炸."]
			id: "5C7D9BCDF19B86BD"
			rewards: [{
				id: "23C2CC2A770EDB50"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "安全第一!"
			tasks: [{
				id: "4AC56C0C4FF91B28"
				item: "pneumaticcraft:security_upgrade"
				type: "item"
			}]
			x: 10.0d
			y: 0.5d
		}
		{
			dependencies: ["72C09FD89C28B1EC"]
			description: ["&3柴油&r可用于制造更多煤油或润滑油."]
			id: "13F68CA518CD287C"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "28D703305E25CDFE"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "3C579D0ECCC44A06"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "0987C92B24E85529"
				item: "pneumaticcraft:diesel_bucket"
				type: "item"
			}]
			title: "柴油"
			x: 9.5d
			y: 2.5d
		}
		{
			dependencies: ["72C09FD89C28B1EC"]
			description: ["&3煤油&r可用于制造汽油,或作为&a煤油灯&f的燃料."]
			id: "2801B7B6BF61E978"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "399F7039FB702A70"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "2D9C5700DAA58497"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "3C1F3C86CD5CD7EF"
				item: "pneumaticcraft:kerosene_bucket"
				type: "item"
			}]
			title: "煤油"
			x: 9.5d
			y: 3.5d
		}
		{
			dependencies: ["72C09FD89C28B1EC"]
			description: ["&3汽油&r可用于制造液化石油气(LPG)."]
			id: "423B8DA485C1308C"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7044949E568FA4BA"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "7D065041E51B5582"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "51B8052B00972CAE"
				item: "pneumaticcraft:gasoline_bucket"
				type: "item"
			}]
			title: "汽油"
			x: 9.5d
			y: 4.5d
		}
		{
			dependencies: ["72C09FD89C28B1EC"]
			description: ["&3液化石油气&r可用于制造&a熔融塑料&f."]
			hide_details_until_startable: false
			id: "04BE9F63E6003475"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4EAFCE9AB30D27CD"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "269EE2D0907E97F6"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "&a我们需要的&f"
			tasks: [{
				id: "67CB1F35E3D8F8E6"
				item: "pneumaticcraft:lpg_bucket"
				type: "item"
			}]
			title: "液化石油气"
			x: 9.5d
			y: 5.5d
		}
		{
			dependencies: ["2801B7B6BF61E978"]
			description: ["&3&a煤油灯&f&r是绝佳光源,通过消耗燃料(煤油最佳)来发光."]
			id: "23737103592776C2"
			rewards: [
				{
					id: "447A20F2C1AB10DD"
					item: "pneumaticcraft:kerosene_bucket"
					type: "item"
				}
				{
					id: "1F529590A17BB1A6"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "&a巨型火把&f算什么!"
			tasks: [{
				id: "3DF0C08F91227D92"
				item: "pneumaticcraft:kerosene_lamp"
				type: "item"
			}]
			x: 11.0d
			y: 3.5d
		}
		{
			dependencies: ["04BE9F63E6003475"]
			description: ["&3&a热气动加工机&f&r用于制造&a熔融塑料&f,只需泵入液化石油气、煤炭和热量."]
			id: "43EF5513A1C52A37"
			rewards: [
				{
					id: "68406EA2E5B67443"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "45991AC663D9B183"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "名字越长越好"
			tasks: [{
				id: "79AE5C89B41B2E1D"
				item: "pneumaticcraft:thermopneumatic_processing_plant"
				type: "item"
			}]
			x: 11.0d
			y: 5.5d
		}
		{
			dependencies: ["43EF5513A1C52A37"]
			description: ["将&a液态塑料&f放置在地面,或用带&a导热框架&f的箱子储存,就能得到&3塑料&r!"]
			id: "54169B69725C49DF"
			rewards: [
				{
					id: "376DC9307AAF7F32"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "4BFEEE8B2BFC4368"
					type: "xp"
					xp: 100
				}
			]
			size: 1.5d
			subtitle: "终于!"
			tasks: [{
				id: "465E4DA026258598"
				item: "industrialforegoing:plastic"
				type: "item"
			}]
			x: 14.0d
			y: 5.5d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: ["制造塑料可能耗时,若想自动化此过程,可使用&a&d神秘农业&f&r或&e&d资源蜜蜂&f&r.当然也可以通过完成之前任务来实现自动化."]
			hide_until_deps_visible: true
			id: "3F657CF4EBDDC3C4"
			optional: true
			rewards: [
				{
					count: 5
					id: "148626E8BAC5BD2A"
					item: "industrialforegoing:plastic"
					random_bonus: 5
					type: "item"
				}
				{
					id: "018B27861EF40AAF"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "非常实用!"
			tasks: [{
				id: "7D244ADE2C7FFA02"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mysticalagriculture:rubber_seeds"
							}
							{
								Count: 1b
								id: "productivebees:configurable_honeycomb"
								tag: {
									EntityTag: {
										type: "productivebees:plastic"
									}
								}
							}
						]
					}
				}
				title: "自动化塑料生产"
				type: "item"
			}]
			x: 15.0d
			y: 4.0d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: ["现在我们有了塑料,下一步是获取成品印刷电路板.首先在&a压力室&f中制作&3&a空印刷电路板&f&r."]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "pneumaticcraft:item/empty_pcb"
				}
			}
			id: "230463E830D6FFEB"
			rewards: [
				{
					id: "1C1CB7134D559AF5"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "66E0CCD2BD29491C"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "通往电路板之路"
			tasks: [{
				id: "071DA0091E3B96EA"
				item: "pneumaticcraft:empty_pcb"
				type: "item"
			}]
			x: 15.5d
			y: 5.0d
		}
		{
			dependencies: ["43EF5513A1C52A37"]
			description: ["仅需1个青金石即可完成升级,而非4个."]
			id: "5A9D6672385D3D82"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "06D227E3DC3B7F1D"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "4DB8D41BB7C4CDAF"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "节省青金石"
			tasks: [{
				id: "517D32E01C12B2AF"
				item: "pneumaticcraft:upgrade_matrix"
				type: "item"
			}]
			x: 11.0d
			y: 7.0d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: [
				"需通过&3&aAmadron平板电脑&f&r向&3Amadron&r订购物资来推进进度.使用时按住潜行键&a右击&f箱子和流体罐,将货币(绿宝石等)存入关联容器即可下单.你将看到Amadrone无人机前来收款,另一架则会送达订购物品."
				""
				"{image:atm:textures/questpics/pneumaticcraft/amadron_drone.png width:300 height:150 align:center}"
			]
			icon: {
				Count: 1
				id: "pneumaticcraft:amadron_tablet"
				tag: {
					"pneumaticcraft:air": 30000
				}
			}
			id: "0D5015B8FF482D0A"
			min_width: 300
			rewards: [
				{
					id: "43286796C1AF89A2"
					item: "pneumaticcraft:gps_tool"
					type: "item"
				}
				{
					id: "35D644DFBDEEAE47"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "不知道他们有没有当日达服务？"
			tasks: [{
				id: "513D5A4104275DA8"
				item: "pneumaticcraft:amadron_tablet"
				type: "item"
			}]
			title: "阿玛德隆"
			x: 15.5d
			y: 6.0d
		}
		{
			dependencies: [
				"230463E830D6FFEB"
				"0D5015B8FF482D0A"
			]
			description: ["&3&a印刷电路板蓝图&f&r限时特惠8绿宝石(仅限时供应)."]
			id: "7040CA41C0A3C2F2"
			rewards: [
				{
					id: "01FF619C019599FB"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "2251D4B3E135426D"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "感谢阿玛德隆!"
			tasks: [{
				id: "33C895A683077073"
				item: "pneumaticcraft:pcb_blueprint"
				type: "item"
			}]
			x: 17.0d
			y: 5.5d
		}
		{
			dependencies: ["7040CA41C0A3C2F2"]
			description: [
				"将&a空印刷电路板&f放入&3&a紫外光灯箱&f&r充能.需压力支持且暴露在阳光下."
				"{image:atm:textures/questpics/pneumaticcraft/uv_charging.png width:200 height:100 align:center}"
			]
			id: "2CC56A4F06E53307"
			rewards: [
				{
					id: "5C63A7558B4298D7"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "0505A60CF9F4E302"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "希望你带了太阳镜!"
			tasks: [{
				id: "2F86488FC900EE22"
				item: "pneumaticcraft:uv_light_box"
				type: "item"
			}]
			title: "用紫外线充电"
			x: 18.5d
			y: 5.5d
		}
		{
			dependencies: ["2AED15E3AC0346FE"]
			description: ["使物品能够自动分发."]
			id: "40924F0D64253A38"
			rewards: [{
				id: "6C4C65124083FEB8"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "分发物品？我还不熟悉她!"
			tasks: [{
				id: "6CD14F28229D0B41"
				item: "pneumaticcraft:dispenser_upgrade"
				type: "item"
			}]
			x: 12.0d
			y: 0.5d
		}
		{
			dependencies: ["2AED15E3AC0346FE"]
			description: ["增加作用范围,在不同设备中有不同效果."]
			hide_dependent_lines: true
			id: "2C5D78630C7AD57F"
			rewards: [{
				id: "59E763859A31721F"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "靠近点!"
			tasks: [{
				id: "0F12C13FEFE4EBB7"
				item: "pneumaticcraft:range_upgrade"
				type: "item"
			}]
			x: 10.5d
			y: -0.5d
		}
		{
			dependencies: ["2AED15E3AC0346FE"]
			description: ["提升设备承压能力."]
			hide_dependent_lines: true
			id: "74AE214747A3DB1A"
			rewards: [{
				id: "324B64D12813CCD3"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "加压!"
			tasks: [{
				id: "70EEE9EDA6FCC4E1"
				item: "pneumaticcraft:volume_upgrade"
				type: "item"
			}]
			x: 11.5d
			y: -0.5d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: ["以下是本模组前期可用的&3升级组件&r.按住Shift悬停查看&3升级组件&r的适用设备,或悬停设备查看可用升级类型."]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			icon: "pneumaticcraft:upgrade_matrix"
			id: "2AED15E3AC0346FE"
			min_width: 250
			rewards: [{
				id: "79ED6E827CE8B1B8"
				type: "xp"
				xp: 10
			}]
			shape: "gear"
			subtitle: "实用小工具"
			tasks: [{
				count: 4L
				id: "1DF996B4E387DB41"
				item: { Count: 4, id: "minecraft:lapis_lazuli" }
				type: "item"
			}]
			title: "简易升级"
			x: 11.0d
			y: -1.5d
		}
		{
			dependencies: ["2CC56A4F06E53307"]
			description: ["制作电路板的下一步是使用含&a蚀刻酸&f的&3&a蚀刻器&f&r.需要热量驱动."]
			id: "0D2F5CC96269F9E3"
			rewards: [
				{
					id: "7B0C167B04A527A2"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "197D93A3FD862346"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "蚀刻时刻到!"
			tasks: [{
				id: "642A986106FC39D5"
				item: "pneumaticcraft:etching_tank"
				type: "item"
			}]
			x: 20.0d
			y: 4.5d
		}
		{
			dependencies: ["2CC56A4F06E53307"]
			description: ["&3&a蚀刻酸&f&r在&a压力室&f中用&a熔融塑料&f...和其他材料制成.用于&a蚀刻器&f."]
			id: "52D3697F1F5625C7"
			rewards: [
				{
					id: "119CCFB2CF3621CC"
					item: "pneumaticcraft:etching_acid_bucket"
					type: "item"
				}
				{
					id: "13EA99979FB2E1BB"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "别问..."
			tasks: [{
				id: "4BA3DB05F9E0F440"
				item: "pneumaticcraft:etching_acid_bucket"
				type: "item"
			}]
			title: "&a蚀刻酸&f"
			x: 20.0d
			y: 6.5d
		}
		{
			dependencies: [
				"52D3697F1F5625C7"
				"0D2F5CC96269F9E3"
			]
			description: ["将充能的&a空印刷电路板&f放入&a蚀刻器&f中,加入一些&a蚀刻酸&f并加热,即可获得&3未组装的PCB板&r."]
			id: "602C3E88D96F8843"
			rewards: [
				{
					id: "6FA8708A3DD5DA03"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "2918F87BA31ABEC4"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "即将完成!"
			tasks: [{
				id: "3D3BD61439134171"
				item: "pneumaticcraft:unassembled_pcb"
				type: "item"
			}]
			x: 21.5d
			y: 5.5d
		}
		{
			dependencies: ["54169B69725C49DF"]
			hide_dependency_lines: true
			id: "7D81F2381516204D"
			rewards: [{
				id: "0149C2C25AFE3E5E"
				type: "xp"
				xp: 50
			}]
			subtitle: "成品PCB所需材料"
			tasks: [{
				count: 2L
				id: "7E538C87D777B809"
				item: "pneumaticcraft:transistor"
				type: "item"
			}]
			title: "晶体管"
			x: 23.5d
			y: 7.0d
		}
		{
			dependencies: ["54169B69725C49DF"]
			hide_dependency_lines: true
			id: "15D355EB164EBE68"
			rewards: [{
				id: "572927AB7268FB66"
				type: "xp"
				xp: 50
			}]
			subtitle: "成品PCB所需材料"
			tasks: [{
				count: 2L
				id: "6F954349AC36A8EE"
				item: { Count: 2, id: "pneumaticcraft:capacitor" }
				type: "item"
			}]
			title: "电容器"
			x: 22.5d
			y: 7.0d
		}
		{
			dependencies: [
				"602C3E88D96F8843"
				"15D355EB164EBE68"
				"7D81F2381516204D"
			]
			description: ["将未组装的PCB板与2个电容器和2个晶体管合成,即可获得&3成品PCB板&r."]
			id: "1427B3AB09510D20"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1A808648B9F15C69"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "4F51717C32C08647"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			size: 1.0d
			subtitle: "完成啦!"
			tasks: [{
				id: "7227DF9A138EF8A4"
				item: "pneumaticcraft:printed_circuit_board"
				type: "item"
			}]
			x: 23.0d
			y: 5.5d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			description: ["这是制作&d&a脉动黑洞&f&r所需的最后一步.恭喜你走到这一步!"]
			id: "6D124ACF4AE80490"
			rewards: [
				{
					id: "40FC6DAC08FEF647"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "0D17FEE5CF5FA03C"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "远程发射爆炸物!"
			tasks: [{
				id: "25DB01423306BCB0"
				item: {
					Count: 1
					id: "pneumaticcraft:micromissiles"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 24.0d
			y: 4.0d
		}
		{
			dependencies: ["65E2AF881709C896"]
			description: [
				"&3&a强化箱子&f&r是36格防爆容器,不可合并为双箱,功能类似潜影盒."
				"{image:atm:textures/questpics/pneumaticcraft/reinforced_chest_ui.png width:200 height:100 align:center}"
			]
			id: "3CFE522A4B7CC2EC"
			rewards: [
				{
					id: "178307313DD6B704"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "46A7A78A411EA102"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "比普通箱子更高级"
			tasks: [{
				id: "418BD4DDE65D5FD7"
				item: "pneumaticcraft:reinforced_chest"
				type: "item"
			}]
			x: 4.0d
			y: -1.5d
		}
		{
			dependencies: ["3CFE522A4B7CC2EC"]
			description: [
				"&3&a智能箱子&f&r是72格防爆容器,内置可配置的&a全方向漏斗&f并带有升级槽位,不可合并为双箱,功能类似潜影盒."
				"{image:atm:textures/questpics/pneumaticcraft/smart_chest_ui.png width:200 height:100 align:center}"
			]
			id: "440B1E1D4951F808"
			rewards: [{
				exclude_from_claim_all: true
				id: "26BABEF4B35848AE"
				table_id: 4882501005851525094L
				type: "random"
			}]
			subtitle: "箱子界的极客"
			tasks: [{
				id: "101850E9F188F7BD"
				item: "pneumaticcraft:smart_chest"
				type: "item"
			}]
			x: 4.0d
			y: -3.0d
		}
		{
			dependencies: ["13F68CA518CD287C"]
			description: ["&3润滑剂&r可用于制作钻头."]
			id: "520EFA7F446AFBF2"
			rewards: [{
				id: "4FF5D83D097BA4A5"
				type: "xp"
				xp: 10
			}]
			subtitle: "润滑剂生产!"
			tasks: [{
				id: "0434C940BC8BF492"
				item: "pneumaticcraft:lubricant_bucket"
				type: "item"
			}]
			title: "润滑剂"
			x: 11.0d
			y: 2.5d
		}
		{
			dependencies: ["43EF5513A1C52A37"]
			description: ["将农作物或种子放入&3&a热气动加工机&f&r中即可制作&3&a植物油&f&r."]
			id: "649167FC31EB916E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "60C24AE206E06ED6"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "31460AF4923BB7D1"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "更好的油"
			tasks: [{
				id: "5CD3D552F51AEF08"
				item: "pneumaticcraft:vegetable_oil_bucket"
				type: "item"
			}]
			title: "&a植物油&f"
			x: 9.5d
			y: 6.5d
		}
		{
			dependencies: ["649167FC31EB916E"]
			description: ["能给我来份豌豆泥配&a茄汁豆吐司&f吗,老板？"]
			id: "42951D14ADCD9332"
			rewards: [
				{
					id: "618C5C25773A6B26"
					item: "croptopia:potato_chips"
					type: "item"
				}
				{
					id: "2B26D0DEC6A0AC55"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "想来点&a炸鱼薯条&f吗？"
			tasks: [{
				id: "569BF001CEBF7853"
				item: "pneumaticcraft:cod_n_chips"
				type: "item"
			}]
			title: "&a炸鱼薯条&f!"
			x: 8.0d
			y: 6.5d
		}
		{
			dependencies: ["43EF5513A1C52A37"]
			description: ["为什么普通面包不需要酵母？"]
			id: "0D31CD79BE6CC8D2"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3D5DB02679FBDD76"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "21F9A0B0A235CB9B"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "&a酵母&f的烦恼"
			tasks: [{
				id: "0A0F2FE6667668B4"
				item: "pneumaticcraft:yeast_culture_bucket"
				type: "item"
			}]
			title: "酵母"
			x: 9.5d
			y: 7.5d
		}
		{
			dependencies: ["0D31CD79BE6CC8D2"]
			description: ["&3乙醇&r可用于制作生物柴油."]
			id: "681A3F31FB242555"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "20A04C274B28DA24"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "6F4971D5110930CE"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "1975E8C092083491"
				item: "pneumaticcraft:ethanol_bucket"
				type: "item"
			}]
			title: "乙醇"
			x: 9.5d
			y: 8.5d
		}
		{
			dependencies: ["681A3F31FB242555"]
			description: ["&3生物柴油&r可用于制作&3润滑剂&r和&3&a熔融塑料&f&r."]
			id: "39F142224D5210A9"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6182362A640B5CB4"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "78682AE1DECD47C2"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "生物/柴油"
			tasks: [{
				id: "57F34B947FB6AEE6"
				item: "pneumaticcraft:biodiesel_bucket"
				type: "item"
			}]
			title: "生物柴油"
			x: 11.0d
			y: 8.5d
		}
		{
			dependencies: ["0D31CD79BE6CC8D2"]
			description: ["用&3&a小麦粉&f&r制作.&a可用于&f制作&a鲑鱼天妇罗&f和&a酸面包&f."]
			id: "5A0860631323B5E3"
			optional: true
			rewards: [
				{
					id: "6B2D4E25CE46E3F2"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "150C67133FB8DEA7"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "你需要这个"
			tasks: [{
				id: "398EB377CFF84584"
				item: "pneumaticcraft:sourdough"
				type: "item"
			}]
			x: 8.0d
			y: 7.5d
		}
		{
			dependencies: ["371A34B297C8A8EF"]
			description: [
				"&3&a作物架&f&r覆盖农作物可加速生长."
				""
				"{image:atm:textures/questpics/pneumaticcraft/crop_support.png width:200 height:100 align:center}"
			]
			id: "21624A6500545C42"
			rewards: [
				{
					id: "5F2C69A982CD748A"
					item: "pneumaticcraft:crop_support"
					type: "item"
				}
				{
					id: "16E6DA1341482026"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "加速作物生长"
			tasks: [{
				id: "71648EA7642F2F98"
				item: "pneumaticcraft:crop_support"
				type: "item"
			}]
			x: 0.0d
			y: -2.5d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: ["&3建筑砖块&r是很酷的建筑材料,但小心它们会伤人.再次合成可使它们变得平滑."]
			icon: "pneumaticcraft:plastic_brick_red"
			id: "482E558BCFEE6B7E"
			optional: true
			rewards: [
				{
					id: "7A5825A1D15172D8"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "215BFDAC20AEE974"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "没有什么比踩到这些更疼的了"
			tasks: [{
				id: "25FF884779D98BAD"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "pneumaticcraft:plastic_bricks"
					}
				}
				title: "建筑砖块™"
				type: "item"
			}]
			x: 13.5d
			y: 7.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				"此任务被故意隐藏,如果你看到这个,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "682BCE7AD73B2D45"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "0CD2482221534ED9"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "7AAE25A5D1C4E2FA"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 2.0d
			y: 0.0d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			description: ["该模组的下一阶段需要你建立&3装配线&r.&3装配线&r的主要组件是&3&a装配控制器&f&r.\\n\\n这里是压力传输的地方."]
			hide_dependent_lines: true
			id: "09BA45CBBFB4AE26"
			rewards: [
				{
					id: "618F2634068279DD"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "2496A5C69CDBF157"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "始终需要"
			tasks: [{
				id: "5AABACDDAB9931BD"
				item: "pneumaticcraft:assembly_controller"
				type: "item"
			}]
			x: 24.5d
			y: 5.5d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: ["&3&a强化压力管道&f&r就像普通的&a压力管道&f,但这些可以承受10巴的压力."]
			id: "32C3E14578D48E6C"
			optional: true
			rewards: [
				{
					id: "4C054638B4771E81"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "7971DB6DE3A69890"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "&a增强&f&a压力管道&f"
			tasks: [{
				id: "5EE58C7A02CDDEAB"
				item: "pneumaticcraft:reinforced_pressure_tube"
				type: "item"
			}]
			x: 14.5d
			y: 7.0d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			description: ["物品需要先放在这里,然后才能被取回."]
			id: "3D819550074CA376"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "78B76A6916FD38FF"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "7AD1F952D0946927"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "始终需要"
			tasks: [{
				id: "6F6E29CA6E7C73CF"
				item: "pneumaticcraft:assembly_platform"
				type: "item"
			}]
			x: 26.0d
			y: 5.5d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			description: ["这将把成品放入你的输出箱中."]
			id: "0D50BDEFA0319EA1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7DF77FF78DBDFCD1"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "7BACC6F38439B5FA"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "始终需要"
			tasks: [{
				id: "5D7B8FC80827036D"
				item: "pneumaticcraft:assembly_io_unit_export"
				type: "item"
			}]
			x: 26.0d
			y: 4.5d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			description: ["这将从你的输入箱中取出物品."]
			id: "00075A5F9AC120ED"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1292A5E8DC67EA0C"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "2FC2571C22D32950"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "始终需要"
			tasks: [{
				id: "231904B21E2469D7"
				item: "pneumaticcraft:assembly_io_unit_import"
				type: "item"
			}]
			x: 26.0d
			y: 6.5d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			hide_dependent_lines: true
			id: "795143471F31A64A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "53C307EAD7FC89DF"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "123DFBB0EE6938CB"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "激光配方所需"
			tasks: [{
				id: "61D1591D2196CFBA"
				item: "pneumaticcraft:assembly_laser"
				type: "item"
			}]
			x: 27.5d
			y: 5.0d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			hide_dependent_lines: true
			id: "7BFE7DAF18981843"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3D353DC8F66E6637"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "05A430BF5D4B82E6"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "钻头配方所需"
			tasks: [{
				id: "7BC0D5FBCC775A7A"
				item: "pneumaticcraft:assembly_drill"
				type: "item"
			}]
			x: 27.5d
			y: 6.0d
		}
		{
			dependencies: [
				"09BA45CBBFB4AE26"
				"00075A5F9AC120ED"
				"7BFE7DAF18981843"
				"3D819550074CA376"
				"795143471F31A64A"
				"0D50BDEFA0319EA1"
			]
			description: ["虽然并非所有配方都需要相同组件,但你可以预先在&3装配线&r上安装好所有设备.下图展示了一个通用的简易&3装配线&r配置方案.阅读各组件任务说明以获取重要信息.\\n\\n&a速度升级&f能显著加快这个流程!"]
			id: "3A9AC6AB4407DC32"
			min_width: 250
			rewards: [{
				id: "570993D69CEF7F92"
				type: "xp"
				xp: 250
			}]
			size: 1.25d
			subtitle: "组装必备"
			tasks: [{
				icon: "minecraft:chest"
				id: "28A1D25CDDBF5163"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:chests"
					}
				}
				title: "任意箱子"
				type: "item"
			}]
			title: "使用装配线"
			x: 29.0d
			y: 5.5d
		}
		{
			dependencies: ["40F634AC38F42B88"]
			description: ["&3&a管道跨接点&f&r通过允许&a压力管道&f多向延伸,让你能更精准控制压力传输方向."]
			id: "7FB12EC3A5888123"
			optional: true
			rewards: [
				{
					id: "43DCF4431F49F1FA"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "3B42F9051B257982"
					type: "xp"
					xp: 50
				}
			]
			size: 0.5d
			subtitle: "不,往这边走!"
			tasks: [{
				id: "7DA329A34A338668"
				item: "pneumaticcraft:tube_junction"
				type: "item"
			}]
			x: -0.75d
			y: 2.0d
		}
		{
			dependencies: ["0AEAEA976ED0C470"]
			description: ["看来有必要提醒你可以和村民交易...\\n\\n&3压力机械师&r的工作站是充能站,他们有些非常实用的交易项目."]
			id: "61202F9BCB4C2848"
			optional: true
			rewards: [
				{
					id: "539EE29B7CC28322"
					item: "pneumaticcraft:charging_station"
					type: "item"
				}
				{
					id: "094794F9AA9C9DF6"
					type: "xp"
					xp: 10
				}
			]
			size: 0.5d
			subtitle: "呃..."
			tasks: [{
				id: "1F1348670CF2DB6F"
				item: {
					Count: 1
					id: "easy_villagers:villager"
					tag: {
						villager: {
							AbsorptionAmount: 0.0f
							Age: 0
							ArmorDropChances: [
								0.085f
								0.085f
								0.085f
								0.085f
							]
							ArmorItems: [
								{ }
								{ }
								{ }
								{ }
							]
							Attributes: [
								{
									Base: 0.0d
									Name: "minecraft:generic.knockback_resistance"
								}
								{
									Base: 0.08d
									Name: "forge:entity_gravity"
								}
								{
									Base: 0.5d
									Name: "minecraft:generic.movement_speed"
								}
								{
									Base: 0.0d
									Name: "forge:step_height_addition"
								}
							]
							Brain: {
								memories: {
									"minecraft:job_site": {
										value: {
											dimension: "minecraft:overworld"
											pos: [I;
												-57
												68
												-8
											]
										}
									}
									"minecraft:last_worked_at_poi": {
										value: 6669479L
									}
								}
							}
							CanPickUpLoot: 1b
							DeathTime: 0s
							FallFlying: 0b
							FoodLevel: 0b
							ForcedAge: 0
							Gossips: [ ]
							HandDropChances: [
								0.085f
								0.085f
							]
							HandItems: [
								{ }
								{ }
							]
							Health: 20.0f
							HurtByTimestamp: 0
							HurtTime: 0s
							Inventory: [ ]
							LastGossipDecay: 6668675L
							LastRestock: 6669479L
							LeftHanded: 0b
							Offers: {
								Recipes: [
									{
										buy: {
											Count: 14b
											id: "minecraft:emerald"
										}
										buyB: {
											Count: 1b
											id: "minecraft:air"
											tag: {
												gridType: 0
											}
										}
										demand: -8
										maxUses: 4
										priceMultiplier: 0.05f
										rewardExp: 1b
										sell: {
											Count: 1b
											id: "pneumaticcraft:pcb_blueprint"
										}
										specialPrice: 0
										uses: 0
										xp: 10
									}
									{
										buy: {
											Count: 7b
											id: "minecraft:emerald"
										}
										buyB: {
											Count: 1b
											id: "minecraft:air"
											tag: {
												gridType: 0
											}
										}
										demand: -32
										maxUses: 16
										priceMultiplier: 0.05f
										rewardExp: 1b
										sell: {
											Count: 1b
											id: "pneumaticcraft:compressed_iron_block"
										}
										specialPrice: 0
										uses: 0
										xp: 4
									}
								]
							}
							PersistenceRequired: 0b
							RestocksToday: 0
							VillagerData: {
								level: 1
								profession: "pneumaticcraft:mechanic"
								type: "minecraft:plains"
							}
							Xp: 0
						}
					}
				}
				match_nbt: true
				type: "item"
			}]
			x: 0.75d
			y: 1.0d
		}
		{
			dependencies: ["3A9AC6AB4407DC32"]
			description: ["&3高级&a压力管道&f&r是目前最好的&a压力管道&f,可承受20巴压力!\\n\\n虽然本身已非常出色,它们还能用于更酷炫的合成配方."]
			id: "64E2E05B228EA53E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7611FA379CC134FA"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "0A661066853F4729"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "全新境界!"
			tasks: [{
				id: "5109AD27D2D93269"
				item: "pneumaticcraft:advanced_pressure_tube"
				type: "item"
			}]
			x: 29.0d
			y: 3.5d
		}
		{
			dependencies: ["3A9AC6AB4407DC32"]
			description: ["使用激光&a装配程序&f可以加速&3未组装PCB&r的生产.只需将&a空白印刷电路板&f放入输入箱,注入压力后等待即可."]
			icon: "pneumaticcraft:unassembled_pcb"
			id: "3334D15766A47A46"
			rewards: [
				{
					id: "459EC8946F1718D3"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "30AAADF0D5BC1B6D"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "2A4DD9872881B85C"
					item: "pneumaticcraft:empty_pcb"
					type: "item"
				}
				{
					id: "3CE006A2358219BA"
					item: "pneumaticcraft:unassembled_pcb"
					type: "item"
				}
			]
			title: "自动化生产PCB"
			x: 29.0d
			y: 7.0d
		}
		{
			dependencies: ["64E2E05B228EA53E"]
			description: ["谁说&d气动工艺&f不能发电？\\n\\n&3&a气动能源炉&f&r能将压力转化为FE能量.查看物品提示或相关说明了解工作原理."]
			id: "5E71F8A046C60346"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3B2BA3861280AF58"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "152F71C30806E05B"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "压力=能量"
			tasks: [{
				id: "2783DD6C66C1080B"
				item: "pneumaticcraft:pneumatic_dynamo"
				type: "item"
			}]
			title: "能量生成"
			x: 27.5d
			y: 3.5d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			description: ["&3&a气动装甲&f&r本身就很出色,但若想发挥极致性能需要安装升级模块.默认按'U'键打开升级界面,部分升级需要消耗压力.\\n\\n与&d气动工艺&f其他工具升级类似,升级&3&a气动装甲&f&r需要带有压力的充能&a空间站&f.\\n\\n注意:副标题会显示该装备可安装升级的最大数量."]
			hide_dependent_lines: true
			icon: {
				Count: 1
				id: "pneumaticcraft:pneumatic_chestplate"
				tag: {
					Damage: 0
				}
			}
			id: "2E8F851A4E98F741"
			min_width: 300
			rewards: [
				{
					id: "12989A0DCCD9D58E"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "0F82AE2E701025D1"
					type: "xp"
					xp: 500
				}
			]
			shape: "hexagon"
			size: 1.2d
			subtitle: "&a钢铁侠&f"
			tasks: [
				{
					id: "0CC30ECC352D1A6E"
					item: {
						Count: 1
						id: "pneumaticcraft:pneumatic_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "5A9A57E7C0C2AF31"
					item: {
						Count: 1
						id: "pneumaticcraft:pneumatic_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "0BA590DEFD2EBB20"
					item: {
						Count: 1
						id: "pneumaticcraft:pneumatic_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "56A00F77D5089120"
					item: {
						Count: 1
						id: "pneumaticcraft:pneumatic_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&a气动装甲&f"
			x: 23.0d
			y: 0.5d
		}
		{
			dependencies: ["64E2E05B228EA53E"]
			description: ["&3&a通量压缩机&f&r能用能量产生大量压力.只需输入FE能量即可持续生成.若你有充足能源储备,这可能是获取压力最便捷的方式."]
			id: "7395B41685F4BC1A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3AB01987C0074517"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "49C6926251CEA3FB"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "能量=压力"
			tasks: [{
				id: "0FF09F8AE76CDC96"
				item: "pneumaticcraft:flux_compressor"
				type: "item"
			}]
			title: "能量转压力"
			x: 28.0d
			y: 2.0d
		}
		{
			dependencies: ["64E2E05B228EA53E"]
			description: ["&3高级&a空气压缩机&f&r工作原理与普通版相同,但性能大幅提升!"]
			id: "2745297337E13E9E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2C82FC63172523C3"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "124675AB00BD1202"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "再也不用无聊的老式&a空气压缩机&f了!"
			tasks: [{
				id: "31D49013ABE68843"
				item: "pneumaticcraft:advanced_air_compressor"
				type: "item"
			}]
			x: 30.0d
			y: 2.0d
		}
		{
			dependencies: [
				"64E2E05B228EA53E"
				"4F5348D0C83C3B0D"
			]
			description: ["&3太阳能压缩机&r利用阳光产生压力,温度越高产量越大.但需注意:过热会导致故障,需要手动修复."]
			id: "31BCE63F5E016323"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3E8713F83407DD8F"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "1F76B670027D80A4"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "38F7BA2B8FC50520"
				item: "pneumaticcraft:solar_compressor"
				type: "item"
			}]
			title: "太阳能压力"
			x: 30.5d
			y: 3.5d
		}
		{
			dependencies: ["3E16CF590C911129"]
			description: [
				"&3&a液体压缩机&f&r通过特定液体产生压力.可通过桶装右键注入、管道输入或在GUI顶部槽位放入桶装液体来添加燃料."
				""
				"{image:pneumaticcraft:textures/gui/gui_liquid_compressor.png width:200 height:200 align:left fit:true}"
			]
			id: "715B86DC82EA636B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5B4849EE527A424F"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "759EA6FD83E0DB39"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "7C9F61622A4A5E94"
				item: "pneumaticcraft:liquid_compressor"
				type: "item"
			}]
			title: "液体产生的压力"
			x: -1.5d
			y: 3.5d
		}
		{
			dependencies: ["2745297337E13E9E"]
			description: [
				"&3&a静电压缩机&f&r利用&a雷电打击&f产生大量压力.\\n\\n更多信息请查阅JEI的'信息'标签或PNC:R手册."
				""
				"{image:pneumaticcraft:textures/patchouli/electrostatic_compressor.png width:100 height:100 align:left fit:true}"
			]
			id: "5D7B34761E8FD212"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4842A0AFD206083D"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "24F35B5C9FB51563"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "35756A83B78F3F8A"
				item: "pneumaticcraft:electrostatic_compressor"
				type: "item"
			}]
			title: "闪电产生的压力"
			x: 31.5d
			y: 2.5d
		}
		{
			dependencies: ["3E16CF590C911129"]
			description: [
				"&3&a热力压缩器&f&r通过温差产生压力!热源与冷源分置两侧即可运作."
				""
				"{image:pneumaticcraft:textures/patchouli/thermal_compressor.png width:100 height:100 align:left fit:true}"
			]
			id: "7177E49BF9ECA5CF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "00749F410A25A11D"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "0F3A880C05D8A014"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "2974FD173EA30320"
				item: "pneumaticcraft:thermal_compressor"
				type: "item"
			}]
			title: "热量产生的压力"
			x: -1.5d
			y: 4.5d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: ["&3&a气动凿岩锤&f&r借助压力进行采矿,通过升级能达到惊人效果!"]
			icon: {
				Count: 1
				id: "pneumaticcraft:jackhammer"
				tag: {
					"pneumaticcraft:air": 120000
				}
			}
			id: "5303509A1C2B1CCD"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0BF4BA26B7B638F8"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "45F8190DD39B8267"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "对不起了杰克!"
			tasks: [{
				id: "7902D4BF9699E1C0"
				item: "pneumaticcraft:jackhammer"
				type: "item"
			}]
			x: 13.0d
			y: 4.0d
		}
		{
			dependencies: ["1427B3AB09510D20"]
			description: ["虽然任务线不包含无人机内容,但它们确实非常强大!有兴趣的话可以自行探索使用方式."]
			icon: "pneumaticcraft:programmer"
			id: "7770F0D62457096D"
			rewards: [
				{
					id: "6FDC3035F906DE95"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "23C0F3C8BE5171EE"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "463F46F227DFCF83"
					item: "pneumaticcraft:programmer"
					type: "item"
				}
				{
					id: "57E64A0EFD75DBBC"
					item: "pneumaticcraft:drone"
					type: "item"
				}
			]
			title: "无人机编程"
			x: 22.0d
			y: 4.0d
		}
		{
			dependencies: ["43EF5513A1C52A37"]
			description: ["当&3&a导热框架&f&r被放置在装有特定流体的容器上并冷却到足够低温时,会将流体转化为物品.\\n\\n将&a熔融塑料&f转化为塑料.\\n将水转化为冰.\\n将熔岩转化为黑曜石."]
			icon_scale: 0.9d
			id: "657DB994B029F61C"
			rewards: [
				{
					id: "003E89ADC072C520"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "456E004C88575DB0"
					type: "xp"
					xp: 50
				}
			]
			shape: "diamond"
			size: 1.1d
			tasks: [{
				id: "19DED55076DD7406"
				item: "pneumaticcraft:heat_frame"
				type: "item"
			}]
			x: 12.5d
			y: 5.5d
		}
		{
			dependencies: ["520EFA7F446AFBF2"]
			description: ["&3钻头&r可用于&a气动凿岩锤&f."]
			id: "0CC48C2AA553A314"
			rewards: [
				{
					count: 5
					id: "0AC401FEBF4F6E42"
					item: "minecraft:diamond"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "0E329BEC1182D29A"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "299CE4B3279E04C4"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "5ED9B866B6340170"
				item: "pneumaticcraft:drill_bit_diamond"
				type: "item"
			}]
			x: 12.0d
			y: 2.5d
		}
		{
			dependencies: ["3A9AC6AB4407DC32"]
			description: ["&3&a太阳能电池板&f&r用于制造太阳能压缩机."]
			id: "4F5348D0C83C3B0D"
			rewards: [{
				id: "0BB0961B1CEFF51B"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				count: 3L
				id: "1ADA892FCECCFF47"
				item: "pneumaticcraft:solar_cell"
				type: "item"
			}]
			title: "&a太阳能电池板&f"
			x: 30.5d
			y: 5.5d
		}
		{
			dependencies: ["3A9AC6AB4407DC32"]
			description: ["智能告示牌,可存储并显示任意长度文本,自动缩放确保文字始终适配.\\n\\n&a右键点击&f任意染料可重新着色,边框和背景可独立上色."]
			id: "30AD7B15682E4D0E"
			optional: true
			rewards: [
				{
					id: "607B84E77B161997"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "695D0F088BE0D6F1"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "告示牌？没听说过!"
			tasks: [{
				id: "32322B27E49C1FFC"
				item: "pneumaticcraft:aphorism_tile"
				type: "item"
			}]
			x: 30.5d
			y: 7.0d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: [
				"利用这三种组件可建造精妙的&3电梯系统&r."
				""
				"{image:pneumaticcraft:textures/patchouli/elevator.png width:150 height:150 align:left fit:true}"
			]
			icon: "pneumaticcraft:elevator_base"
			id: "4A076530297F4A97"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "520DBD2DE5FF9E9F"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "343D0A0E63932362"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "0667E3E62F944586"
					item: "pneumaticcraft:elevator_base"
					type: "item"
				}
				{
					id: "046FB06F74C0AC1C"
					item: "pneumaticcraft:elevator_caller"
					type: "item"
				}
				{
					id: "4A6BDA0F331D1B4C"
					item: "pneumaticcraft:elevator_frame"
					type: "item"
				}
			]
			title: "电梯"
			x: 14.0d
			y: 4.0d
		}
		{
			dependencies: [
				"0CC48C2AA553A314"
				"5303509A1C2B1CCD"
			]
			description: ["&3&a下界合金钻头&f&r是最强钻头,性能甚至超越&a钻石钻头&f."]
			id: "2338E5269CA23C91"
			rewards: [
				{
					id: "66ACABBFE430488E"
					item: "minecraft:netherite_ingot"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "64A3DD621B331262"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "297ACCA762B3A2DC"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&a登峰造极&f!"
			tasks: [{
				id: "69AA17BB9454C502"
				item: "pneumaticcraft:drill_bit_netherite"
				type: "item"
			}]
			x: 13.0d
			y: 2.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["每级升级提供+0.5护甲防御和+1&a盔甲韧性&f."]
			hide_until_deps_visible: true
			id: "762B8309E1A31B99"
			rewards: [{
				id: "1F50DA45FF043213"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:4"
			tasks: [{
				id: "56BDDA593D3B1F80"
				item: "pneumaticcraft:armor_upgrade"
				type: "item"
			}]
			x: 21.0d
			y: -1.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: [
				"可透视查看方块/流体详细信息.\\n"
				"{image:atm:textures/questpics/pneumaticcraft/block_tracker.png width:150 height:150 align:center}"
			]
			hide_until_deps_visible: true
			id: "002163B909070CF8"
			rewards: [{
				id: "3E2940849E104602"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "38913385C6596A2C"
				item: "pneumaticcraft:block_tracker_upgrade"
				type: "item"
			}]
			x: 21.0d
			y: 2.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["从胸甲储能槽为背包内可加压物品充能."]
			hide_until_deps_visible: true
			id: "15007DCAF701B361"
			rewards: [{
				id: "4184F7AA1CFDA194"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:6"
			tasks: [{
				id: "1928F18246D756C3"
				item: "pneumaticcraft:charging_upgrade"
				type: "item"
			}]
			x: 22.0d
			y: 0.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["右键标记位置后可自动寻路返回.当距离足够近且路径畅通时,将显示导航路径."]
			hide_until_deps_visible: true
			id: "0D9560C8E247F9E7"
			rewards: [{
				id: "63B943916BC1038D"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "6BA9AF2FC89F6318"
				item: "pneumaticcraft:coordinate_tracker_upgrade"
				type: "item"
			}]
			x: 21.0d
			y: -0.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["允许在穿戴&a气动胸甲&f时同时使用鞘翅."]
			hide_until_deps_visible: true
			id: "5103C817381181DE"
			rewards: [{
				id: "78E81C79E8102C79"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "25B9C04D15AD877B"
				item: "pneumaticcraft:elytra_upgrade"
				type: "item"
			}]
			x: 25.0d
			y: -0.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["使末影人不会主动攻击,效果类似雕刻南瓜,但不会遮挡视线."]
			hide_until_deps_visible: true
			id: "46BE62C20C720D10"
			rewards: [{
				id: "683702485F8C3DF5"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "4CD22650AF767C94"
				item: "pneumaticcraft:ender_visor_upgrade"
				type: "item"
			}]
			x: 25.0d
			y: -1.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["自动追踪附近生物(可穿墙显示).当敌对生物锁定你时会发出警告."]
			hide_until_deps_visible: true
			id: "00D7BCC68D971A53"
			rewards: [{
				id: "1D6F067C86456070"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "1770D13680FE56E5"
				item: "pneumaticcraft:entity_tracker_upgrade"
				type: "item"
			}]
			x: 21.0d
			y: 1.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["提升游泳速度."]
			hide_until_deps_visible: true
			id: "7BFC24607189B6D0"
			rewards: [{
				id: "19D8C47FAA715BED"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "62759E3F6B218631"
				item: "pneumaticcraft:flippers_upgrade"
				type: "item"
			}]
			x: 22.0d
			y: 1.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["使猪灵保持中立(效果等同金质盔甲),只需在任意装备部位安装即可生效."]
			hide_until_deps_visible: true
			id: "5D71ED16FD4E725A"
			rewards: [{
				id: "20E24F58991736B9"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "2B5CBF4C29BB8D74"
				item: "pneumaticcraft:gilded_upgrade"
				type: "item"
			}]
			x: 25.0d
			y: 1.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["获得钢铁侠式飞行能力.\\n\\n靴子每次只能安装一级该升级.每级会消耗更多气压.\\n\\n请实际使用这些升级,不要仅作合成."]
			hide_until_deps_visible: true
			id: "7953C87C488A2AF7"
			rewards: [{
				id: "4F65FCD4DFE82F0C"
				type: "xp"
				xp: 100
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "29C129CF9FD88E83"
				item: "pneumaticcraft:jet_boots_upgrade_1"
				type: "item"
			}]
			x: 23.0d
			y: 2.5d
		}
		{
			dependencies: ["7953C87C488A2AF7"]
			description: ["飞行速度超越I级."]
			id: "312331F6DB0CE5F4"
			rewards: [{
				id: "412AF3B474EC6916"
				type: "xp"
				xp: 200
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "6245D0F4985FCC51"
				item: "pneumaticcraft:jet_boots_upgrade_2"
				type: "item"
			}]
			x: 23.0d
			y: 1.5d
		}
		{
			dependencies: ["312331F6DB0CE5F4"]
			description: ["飞行速度超越II级."]
			id: "521656D425E2FDBA"
			rewards: [{
				id: "276DEDDF9D716494"
				type: "xp"
				xp: 300
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "503D496B7DA06BC3"
				item: "pneumaticcraft:jet_boots_upgrade_3"
				type: "item"
			}]
			x: 23.0d
			y: -0.5d
		}
		{
			dependencies: ["521656D425E2FDBA"]
			description: ["或许有点过快."]
			id: "36D72D759C4E8823"
			rewards: [{
				id: "09AEB7C55E9F0369"
				type: "xp"
				xp: 400
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "231FBEDE107DDE52"
				item: "pneumaticcraft:jet_boots_upgrade_4"
				type: "item"
			}]
			x: 23.0d
			y: -1.5d
		}
		{
			dependencies: ["36D72D759C4E8823"]
			description: ["100%过快!注意别摔死!"]
			id: "7D6B4AEF806AF62E"
			rewards: [{
				id: "547A45FE1424A345"
				type: "xp"
				xp: 500
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "54EEA3A3443CEAF6"
				item: "pneumaticcraft:jet_boots_upgrade_5"
				type: "item"
			}]
			x: 24.0d
			y: -1.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["获得更高跳跃能力与弹性,提升移动效率.\\n\\n护腿每次只能安装一级该升级."]
			hide_until_deps_visible: true
			id: "6674270897997BF7"
			rewards: [{
				id: "2C60D8B870084CE8"
				type: "xp"
				xp: 100
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "0E5C54B2D623F21C"
				item: "pneumaticcraft:jumping_upgrade_1"
				type: "item"
			}]
			x: 24.0d
			y: 2.5d
		}
		{
			dependencies: ["6674270897997BF7"]
			description: ["跳跃高度超越I级."]
			id: "61B479079DC1C69C"
			rewards: [{
				id: "5E94499890458B4C"
				type: "xp"
				xp: 200
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "67CA81F6C904192D"
				item: "pneumaticcraft:jumping_upgrade_2"
				type: "item"
			}]
			x: 24.0d
			y: 1.5d
		}
		{
			dependencies: ["61B479079DC1C69C"]
			description: ["跳跃高度超越II级."]
			id: "5B9F933ECD6B24FD"
			rewards: [{
				id: "112BE6DC675D2F06"
				type: "xp"
				xp: 300
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "73EDD4B23EA0A44D"
				item: "pneumaticcraft:jumping_upgrade_3"
				type: "item"
			}]
			x: 24.0d
			y: 0.5d
		}
		{
			dependencies: ["5B9F933ECD6B24FD"]
			description: ["获得超高跳跃能力."]
			id: "61BA2932EC86DA2E"
			rewards: [{
				id: "38A921DA170F7F2A"
				type: "xp"
				xp: 400
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "6121D228F525D2CB"
				item: "pneumaticcraft:jumping_upgrade_4"
				type: "item"
			}]
			x: 24.0d
			y: -0.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["穿戴时赋予玩家&a夜视&f效果."]
			hide_until_deps_visible: true
			id: "718DABA6C9E5982A"
			rewards: [{
				id: "39DC46812FF22559"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "79B7A8E4CF588749"
				item: "pneumaticcraft:night_vision_upgrade"
				type: "item"
			}]
			x: 22.0d
			y: -1.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["保护你免受&d通用机械&f辐射伤害."]
			hide_until_deps_visible: true
			id: "2DE7CC686B56881F"
			rewards: [{
				id: "13A94C21CBAB3800"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "2221DBEACD67BAA8"
				item: "pneumaticcraft:radiation_shielding_upgrade"
				type: "item"
			}]
			x: 25.0d
			y: 2.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["在水下保持呼吸,当气泡降至5时会自动补充!还能让你在水下视野更清晰!"]
			hide_until_deps_visible: true
			id: "4B4F5341D6DBDD19"
			rewards: [{
				id: "065A28BBF33279B1"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "28F4D0896C1552B8"
				item: "pneumaticcraft:scuba_upgrade"
				type: "item"
			}]
			x: 22.0d
			y: -0.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: [
				"选中一个物品,此升级模块会搜索附近箱子或地面上的该物品.若要在箱子中寻找,需配备&3&a方块追踪器&f&r升级；若需在地面搜寻,则需安装&a实体追踪器&f升级.\\n"
				"{image:atm:textures/questpics/pneumaticcraft/item_search_upgrade.png width:200 height:200 align:center}"
			]
			hide_until_deps_visible: true
			id: "7386A8433698946C"
			rewards: [{
				id: "43E18EE34C2DFE89"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "19B70A70A1FD2BFA"
				item: "pneumaticcraft:search_upgrade"
				type: "item"
			}]
			x: 21.0d
			y: 0.5d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["你受到跌落伤害时(无视护甲),附近生物也会受到同等伤害."]
			hide_until_deps_visible: true
			id: "0F49E9167B5E97C9"
			rewards: [{
				id: "0B2FD870C2FB9E0D"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:1"
			tasks: [{
				id: "060F81A9199D170A"
				item: "pneumaticcraft:stomp_upgrade"
				type: "item"
			}]
			x: 22.0d
			y: 2.5d
		}
		{
			dependencies: ["54169B69725C49DF"]
			description: [
				"&3&a气动门&f&r可作为自动门使用,并可设置为仅为你开启!"
				""
				"{image:pneumaticcraft:textures/patchouli/pneumatic_door.png width:200 height:200 align:right fit:true}"
			]
			icon: "pneumaticcraft:pneumatic_door"
			id: "6EB53CDCB80D4B66"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0DD078EC9066223B"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "5267A6735E816233"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "3DB7E828FD6AE8AC"
					item: "pneumaticcraft:pneumatic_door"
					type: "item"
				}
				{
					id: "63D65E210CBDE017"
					item: "pneumaticcraft:pneumatic_door_base"
					type: "item"
				}
			]
			x: 14.0d
			y: 3.0d
		}
		{
			dependencies: ["64E2E05B228EA53E"]
			description: ["&3&a空气接口&f&r可为可加压设备补充压力.现在你可以无需充电就使用&a气动装甲&f了!"]
			id: "64D34A6C3487C5DF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "302DA6F921459E20"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "18689BB49BC1B4D7"
					type: "xp"
					xp: 500
				}
			]
			subtitle: "无线时代!"
			tasks: [{
				id: "295BF54A9DAC634C"
				item: "pneumaticcraft:aerial_interface"
				type: "item"
			}]
			x: 29.0d
			y: 1.0d
		}
		{
			dependencies: ["2745297337E13E9E"]
			description: ["使用激光&a装配程序&f可加速生产&3未组装PCB&r.只需将&a空白印刷电路板&f放入输入箱,注入压力后等待即可."]
			id: "02643BAD1AE747C6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2110E119EBFDD314"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "5DD56E79562AE605"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "46164653473C8CDB"
				item: "pneumaticcraft:advanced_liquid_compressor"
				type: "item"
			}]
			x: 31.5d
			y: 1.5d
		}
		{
			dependencies: ["7953C87C488A2AF7"]
			description: ["只有使用过&a喷射加速模块&f才会看到此任务.\\n\\n使用&a喷射加速模块&f必定会受伤,别白费力气..."]
			icon: {
				Count: 1
				id: "pneumaticcraft:pneumatic_boots"
				tag: {
					Damage: 0
				}
			}
			id: "68F92455A9483AD6"
			invisible: true
			optional: true
			rewards: [
				{
					id: "496046BE0AECD1BA"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "7840E6CB1C39DAF9"
					type: "xp"
					xp: 100
				}
			]
			size: 0.5d
			subtitle: "你以为鞘翅飞行很难？"
			tasks: [{
				advancement: "pneumaticcraft:fly_into_wall"
				criterion: ""
				id: "7E5FD45CE2223A25"
				type: "advancement"
			}]
			title: "&a喷射加速模块&f"
			x: 23.0d
			y: 3.5d
		}
		{
			dependencies: ["0AEAEA976ED0C470"]
			description: ["&3&a泄压模块&f&r可对指向容器内的可加压设备进行泄压."]
			id: "22572456571C3F8D"
			rewards: [
				{
					id: "309D6CA3BC2F8F16"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "3FCB121F263650DF"
					type: "xp"
					xp: 50
				}
			]
			size: 0.5d
			tasks: [{
				id: "2C8686ED04F885AA"
				item: "pneumaticcraft:charging_module"
				type: "item"
			}]
			x: 3.25d
			y: 1.0d
		}
		{
			dependencies: ["22572456571C3F8D"]
			description: ["&3模块扩展卡&r能让&a泄压模块&f性能更佳."]
			id: "132DF9ECEFFB3B71"
			rewards: [
				{
					id: "540EDC9B224EA91C"
					table_id: 4882501005851525094L
					type: "random"
				}
				{
					id: "616950E067BE6F14"
					type: "xp"
					xp: 100
				}
			]
			size: 0.5d
			tasks: [{
				id: "18E6FFF0AC1612F5"
				item: "pneumaticcraft:module_expansion_card"
				type: "item"
			}]
			x: 3.25d
			y: 1.75d
		}
		{
			dependencies: ["65E2AF881709C896"]
			description: ["首先合成&3&a物流核心&f&r开启物流系统，建议阅读PNC:R手册获取完整使用教程。"]
			id: "24042326B4632DD4"
			rewards: [
				{
					id: "46BC420C749EB871"
					item: "pneumaticcraft:logistics_core"
					random_bonus: 1
					type: "item"
				}
				{
					id: "4446037C0BAB8C06"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "物品/流体传输"
			tasks: [{
				id: "743C664C4E8EE776"
				item: "pneumaticcraft:logistics_core"
				type: "item"
			}]
			title: "物流系统"
			x: 5.5d
			y: 0.0d
		}
		{
			dependencies: ["24042326B4632DD4"]
			description: ["每种&3物流框架&r可用于不同类型的自动化."]
			id: "26BEA64C80ECE1A6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4B18FC0A44934C4E"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "34FB5D5211526B35"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "框架大师!"
			tasks: [{
				id: "0D7C2FB0CA6401BD"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "pneumaticcraft:logistics_frame_active_provider"
							}
							{
								Count: 1b
								id: "pneumaticcraft:logistics_frame_default_storage"
							}
							{
								Count: 1b
								id: "pneumaticcraft:logistics_frame_passive_provider"
							}
							{
								Count: 1b
								id: "pneumaticcraft:logistics_frame_requester"
							}
							{
								Count: 1b
								id: "pneumaticcraft:logistics_frame_storage"
							}
						]
					}
				}
				title: "物流框架"
				type: "item"
			}]
			x: 7.0d
			y: -1.5d
		}
		{
			dependencies: ["24042326B4632DD4"]
			description: ["&3&a物流配置器&f&r可用于配置物流框架和&a传输节点&f."]
			icon: {
				Count: 1
				id: "pneumaticcraft:logistics_configurator"
				tag: {
					"pneumaticcraft:air": 30000
				}
			}
			id: "6B1C06C753C10E44"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1C5FC44F9C2B4998"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "356E92A63353CB70"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "02BFA4DBB32C626D"
				item: "pneumaticcraft:logistics_configurator"
				type: "item"
			}]
			x: 7.0d
			y: 0.0d
		}
		{
			dependencies: ["24042326B4632DD4"]
			description: [
				"&a原木&f框架需连接至&3&a物流模块&f&r方可运作."
				""
				"{image:pneumaticcraft:textures/patchouli/logistics_module.png width:100 height:100 align:left fit:true}"
			]
			id: "0E65BE9460B74000"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0D092D27171637CB"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "0FE191C825E1A4AF"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "0C01776A1DC7041C"
				item: "pneumaticcraft:logistics_module"
				type: "item"
			}]
			x: 7.0d
			y: 1.5d
		}
		{
			dependencies: ["142C67218BD16867"]
			description: ["&3加特林机枪&r需要压力和弹药才能运作.查看工具提示&a了解更多&f."]
			icon: {
				Count: 1
				id: "pneumaticcraft:minigun"
				tag: {
					"pneumaticcraft:air": 30000
				}
			}
			id: "495DCA49B4E80852"
			rewards: [
				{
					id: "55E064F27E895404"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "7B82E7D83DD78575"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "突突突!"
			tasks: [{
				id: "3C4D170739B83904"
				item: "pneumaticcraft:minigun"
				type: "item"
			}]
			x: 5.0d
			y: 2.0d
		}
		{
			dependencies: ["65E2AF881709C896"]
			description: ["使用&3&a加特林弹药&f&r为机枪装弹."]
			icon: {
				Count: 1
				id: "pneumaticcraft:gun_ammo"
				tag: {
					Damage: 0
				}
			}
			id: "142C67218BD16867"
			rewards: [
				{
					id: "2E4FB691E78F8E95"
					table_id: 6269051860797387566L
					type: "random"
				}
				{
					id: "7394DA2EB895A3E3"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "500BA99608E87FAF"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "pneumaticcraft:gun_ammo"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "pneumaticcraft:gun_ammo_ap"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "pneumaticcraft:gun_ammo_explosive"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "pneumaticcraft:gun_ammo_freezing"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "pneumaticcraft:gun_ammo_incendiary"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "pneumaticcraft:gun_ammo_weighted"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&a加特林机枪弹药&f"
				type: "item"
			}]
			x: 5.0d
			y: 1.0d
		}
		{
			dependencies: ["2E8F851A4E98F741"]
			description: ["扩大物品和经验吸取范围."]
			hide_until_deps_visible: true
			id: "61AD2207E770CD7F"
			rewards: [{
				id: "477F33B522B44F3D"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "最大:6"
			tasks: [{
				id: "4D1B9FC3927FAE26"
				item: "pneumaticcraft:magnet_upgrade"
				type: "item"
			}]
			x: 25.0d
			y: 0.5d
		}
		{
			dependencies: ["495DCA49B4E80852"]
			description: [
				"&3&a哨兵炮塔&f&r可部署在指定位置,设置为攻击范围内的生物或玩家.仅需配备若干&a加特林机枪弹药&f."
				""
				"{image:pneumaticcraft:textures/patchouli/sentry_turret.png width:100 height:100 align:left fit:true}"
			]
			id: "4C0D00B5305117A1"
			rewards: [
				{
					id: "46CB8E373CC748EE"
					table_id: 6490686651709956289L
					type: "random"
				}
				{
					id: "11402EF8D80100F7"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "砰砰(没有你)"
			tasks: [{
				id: "02A2904F56A5A71B"
				item: "pneumaticcraft:sentry_turret"
				type: "item"
			}]
			x: 5.0d
			y: 3.0d
		}
	]
	title: "&d气动工艺&f"
}
