{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "chapter_2"
	group: "2084F3F6FB861C5B"
	icon: "allthetweaks:atm_star"
	id: "29EFE8220836C79A"
	images: [
		{
			height: 6.0d
			image: "allthetweaks:block/atm_star_block"
			rotation: 0.0d
			width: 6.0d
			x: 0.0d
			y: 0.0d
		}
		{
			height: 8.0d
			image: "chipped:block/crying_obsidian/crying_obsidian_scales"
			order: -1
			rotation: 0.0d
			width: 8.0d
			x: 0.0d
			y: 0.0d
		}
		{
			height: 8.0d
			image: "allthetweaks:block/atm_star_block"
			order: -2
			rotation: 45.0d
			width: 8.0d
			x: 0.0d
			y: 0.0d
		}
		{
			height: 9.0d
			image: "chipped:block/crying_obsidian/crying_obsidian_scales"
			order: -3
			rotation: 45.0d
			width: 9.0d
			x: 0.0d
			y: 0.0d
		}
		{
			height: 6.0d
			image: "atm:textures/questpics/mek/sps_cutout.png"
			rotation: 0.0d
			width: 7.612612612612613d
			x: 16.0d
			y: -5.0d
		}
		{
			height: 6.0d
			image: "mekanism:item/pellet_antimatter"
			rotation: 0.0d
			width: 6.0d
			x: 20.0d
			y: 0.0d
		}
		{
			height: 3.0d
			image: "evilcraft:block/blood_flow"
			order: -2
			rotation: 0.0d
			width: 3.0d
			x: 8.0d
			y: -8.0d
		}
		{
			height: 2.5d
			image: "evilcraft:block/blood_infuser_north_off"
			rotation: 0.0d
			width: 2.5d
			x: 8.0d
			y: -8.0d
		}
		{
			height: 2.75d
			image: "evilcraft:block/hardened_blood"
			order: -1
			rotation: 0.0d
			width: 2.75d
			x: 8.0d
			y: -8.0d
		}
		{
			color: 0
			height: 3.0d
			image: "occultism:textures/gui/book/pentagram_rose.png"
			rotation: 0.0d
			width: 3.0d
			x: -7.5d
			y: -8.0d
		}
		{
			height: 2.0d
			image: "occultism:item/advancement/devil_icon"
			rotation: 0.0d
			width: 2.0d
			x: -7.5d
			y: -8.0d
		}
		{
			height: 0.5d
			image: "occultism:block/chalk_glyph/12"
			rotation: 0.0d
			width: 0.5d
			x: -7.5d
			y: -7.0d
		}
		{
			height: 0.5d
			image: "occultism:block/chalk_glyph/0"
			rotation: 0.0d
			width: 0.5d
			x: -7.5d
			y: -9.0d
		}
		{
			alpha: 230
			height: 5.0d
			image: "ars_nouveau:item/wilden_tribute"
			rotation: 0.0d
			width: 5.4375d
			x: -13.5d
			y: -5.5d
		}
		{
			height: 5.0d
			image: "allthemodium:item/piglich_heart"
			rotation: 0.0d
			width: 5.0d
			x: -12.5d
			y: 9.0d
		}
		{
			height: 5.0d
			image: "allthemodium:item/unobtainium_allthemodium_alloy_ingot"
			rotation: 0.0d
			width: 5.0d
			x: 11.5d
			y: 9.0d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/chp2.png"
			rotation: 0.0d
			width: 11.355263157894736d
			x: 0.0d
			y: -15.25d
		}
		{
			height: 4.5d
			image: "atm:textures/questpics/chap2/atmstar_title.png"
			order: 1
			rotation: 0.0d
			width: 32.462566844919785d
			x: 0.0d
			y: -13.0d
		}
		{
			height: 5.0d
			image: "cataclysm:item/flame_eye"
			rotation: 0.0d
			width: 5.0d
			x: -20.5d
			y: 0.0d
		}
	]
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: [
				"153F111B4CCC850B"
				"4F28DC3D905DDA3A"
				"03B12E7ED6B01F9A"
			]
			id: "67E154DC24F54854"
			rewards: [
				{
					id: "10C97B39C12475D3"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"湮灭大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "60DD6D09683F298A"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "47A228DD240949B7"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "17ED2BF9BAAB9268"
				item: "allthetweaks:oblivion_shard"
				type: "item"
			}]
			title: "&e湮灭大师"
			x: 0.0d
			y: -3.5d
		}
		{
			dependencies: [
				"3912DE46B5F39287"
				"1AB8C28C9E991E1C"
				"1B2898A31C297B58"
				"22D716F330A6D4CE"
				"5FD3C68D5F218D02"
			]
			id: "527453CD5A20AE38"
			rewards: [
				{
					id: "06FA9F4A937F0402"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"巨龙大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "147542B53C621B39"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "64FA1C4E02C83B1D"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "502BBFCFD28DFB00"
					table_id: 1160439751879588774L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "25D3F715AF6677B4"
				item: "allthetweaks:dragon_soul"
				type: "item"
			}]
			title: "&e巨龙大师"
			x: -2.0d
			y: -3.0d
		}
		{
			dependencies: [
				"6E29BA2E8642AF53"
				"71B824BEA02C4A34"
				"5D0C944F84E341BE"
				"124BDAF91DA74910"
				"356F450F4ADD22D7"
			]
			id: "73990028197AF1AB"
			rewards: [
				{
					id: "69D20E0F1A44244F"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"太空大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "288D32C87F308B7B"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "6CD127DE6F4CE99B"
					table_id: 7175652334583451871L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "08EC833DF79CEA1E"
				item: "allthetweaks:pulsating_black_hole"
				type: "item"
			}]
			title: "&e空间大师"
			x: 2.0d
			y: -3.0d
		}
		{
			dependencies: [
				"2B539F4F290DC4CF"
				"4D3D96B6019CA7F9"
				"4A3DF999B969C875"
				"7790E599B42A5DE5"
			]
			id: "048F2942436D3C46"
			rewards: [
				{
					id: "48F65810E60EDC84"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"&a苍穹&f大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "1C55EF91B9DAA586"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "67BC559DDAC41CD6"
					table_id: 7025454341029952768L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "7560975C9F20D969"
				item: "allthetweaks:nexium_emitter"
				type: "item"
			}]
			title: "&e&a苍穹&f大师"
			x: -3.0d
			y: -1.0d
		}
		{
			dependencies: ["695A0DC585FB6E97"]
			id: "6F598D9CFB915123"
			rewards: [
				{
					id: "0C1FE4845CDCE417"
					item: {
						Count: 1
						id: "minecraft:trident"
						tag: {
							Damage: 0
							Enchantments: [
								{
									id: "sharpness"
									lvl: 5
								}
								{
									id: "looting"
									lvl: 3
								}
								{
									id: "sweeping"
									lvl: 3
								}
							]
							display: {
								Name: "\"Master of...Patrick?\""
							}
						}
					}
					type: "item"
				}
				{
					id: "459D0B538C1F0199"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "543559249F7ABACA"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "7BF613128A236696"
				item: "allthetweaks:patrick_star"
				type: "item"
			}]
			title: "&e派大星大师？"
			x: 0.0d
			y: 3.5d
		}
		{
			dependencies: [
				"627A39E62DD49CD8"
				"709F1FA492703463"
				"6BDBF4D6086513C1"
				"6CD7A3760C6D87E6"
				"106A3D79B1CDE895"
				"0A6378C7455E45B1"
				"4CADD50E7E83CD0E"
				"6FF116239EACA390"
				"5F0482CDD3FC667D"
			]
			id: "73CD6CE2B10830B9"
			rewards: [
				{
					id: "577307C225AF3E9C"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"宇宙大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "601E59983FE5FA3A"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "58E6AB827CA786A4"
					table_id: 5196609362437981520L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "36166CF3BD5B9E77"
				item: "allthetweaks:dimensional_seed"
				type: "item"
			}]
			title: "&e宇宙主宰"
			x: 3.0d
			y: 1.0d
		}
		{
			dependencies: ["3FEA99DD35D4B7CF"]
			id: "43CE45F9FDDB91EE"
			rewards: [
				{
					id: "7367AE25EAF90CCB"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"创世大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "4CEC9C77E587FE45"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "1D2CD94B57549F77"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "4B271057EE4F90B4"
				item: "mysticalagradditions:creative_essence"
				type: "item"
			}]
			title: "&e创世之主"
			x: 2.0d
			y: 3.0d
		}
		{
			dependencies: ["1E7D0E40D0E2B026"]
			description: ["使用来自&2&d神秘农业&f&r的&a觉醒祭坛&r,你可以将振金块和难以获得之金属块融合在一起.\\n\\n你需要2个这样的&c觉醒&r金属块!"]
			id: "3109C8221B6FA608"
			rewards: [{
				exclude_from_claim_all: true
				id: "2A468D6CFB5BAB99"
				table_id: 7175652334583451871L
				type: "loot"
			}]
			tasks: [{
				count: 2L
				id: "1F7626020C21A3E0"
				item: {
					Count: 1
					id: "allthemodium:unobtainium_vibranium_alloy_block"
					tag: {
						Enchantments: [{
							id: "minecraft:unbreaking"
							lvl: 1s
						}]
						HideFlags: 1
						display: {
							Name: "[{\"text\":\"觉醒的&a难得素-振金合金&f方块\",\"italic\":false}]"
						}
					}
				}
				type: "item"
			}]
			title: "&e觉醒合金"
			x: -1.0d
			y: 7.0d
		}
		{
			dependencies: ["6EDC47E6F752C1C1"]
			description: ["要获得迷你&a下界传送门&f,你需要一个地狱灌注的&a传送核心&f.与其他核心类似,你需要对&a传送核心&f进行灌注.对于&a下界&f传送门,你至少需要&a40永恒能量&r,&c15%%-25%%量子值&r,以及至少&560%%奥术能量&r.一个示例配置是10个西瓜架、&95个回荡幽匿架&r和&95个灵魂触须幽匿架&r.然后将其与4个黑曜石、2个&a下界之星&f、1个&a诡异菌岩&f和1个&a凋灵骷髅头&f组合,即可获得迷你&a下界传送门&f."]
			hide_dependent_lines: true
			id: "6BDBF4D6086513C1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "20269AE072E73488"
					table_id: 7175652334583451871L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "2DC217B8EA06DDBB"
					table_id: 1160439751879588774L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "51FECD9DB29F9D6D"
				item: "allthetweaks:mini_nether"
				type: "item"
			}]
			title: "迷你&a下界传送门&f"
			x: 10.0d
			y: 1.0d
		}
		{
			dependencies: ["6EDC47E6F752C1C1"]
			description: ["如果你想获得迷你(末地)出口传送门,你需要一些龙部件和一个龙族灌注的&a传送核心&f.为此,你需要用最大&a永恒能量&r、&c45%%-50%%量子值&r和最大&5奥术能量&r来灌注一个&a传送核心&f.一个示例配置是&97个回荡幽匿架&r和&d3个龙族末地架&r.然后带上你的龙族&a传送核心&f、一个&a龙蛋&f(希望你有备用的)、3个&a末影水晶&f和4个&a末地石火盆&f.然后,砰!迷你(末地)出口传送门就完成了."]
			hide_dependent_lines: true
			id: "0A6378C7455E45B1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "16F2378285403206"
					table_id: 7175652334583451871L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "2B5B4D54C873012E"
					table_id: 1160439751879588774L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "102D1392A59A2D1C"
				item: "allthetweaks:mini_exit"
				type: "item"
			}]
			title: "迷你(末地)出口传送门"
			x: 9.5d
			y: 1.5d
		}
		{
			dependencies: ["6EDC47E6F752C1C1"]
			description: ["要获得迷你&a末地传送门&f,你需要一个末影灌注的&a传送核心&f.要灌注&a传送核心&f,你需要&a50永恒能量&r、&c8.5%%-13.5%%量子值&r和&532.5%%-37.5%%奥术能量&r.为此,你需要一个复杂的配置:&d4个龙族末地架&r、&92个回荡幽匿架&r、&91个灵魂触须幽匿架&r和3个西瓜架.合成&a末地&f灌注的&a传送核心&f、4个末影之眼和4个&a末地石火盆&f,即可制作迷你&a末地传送门&f."]
			hide_dependent_lines: true
			id: "627A39E62DD49CD8"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1C73CD9639A36C53"
					table_id: 7175652334583451871L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "77A0279BF43E3BE5"
					table_id: 1160439751879588774L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "37A8FF2ABA1FAE4A"
				item: "allthetweaks:mini_end"
				type: "item"
			}]
			title: "迷你&a末地传送门&f"
			x: 10.0d
			y: 2.0d
		}
		{
			dependencies: ["2435B6D970F48DF8"]
			hide_dependent_lines: true
			id: "356F450F4ADD22D7"
			rewards: [{
				exclude_from_claim_all: true
				id: "0CACCBD5DEA8D8E4"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				id: "44CFFE38FD3BBED3"
				item: {
					Count: 1
					id: "occultism:stable_wormhole"
					tag: { }
				}
				type: "item"
			}]
			title: "2个&a维度存储促动器&f"
			x: -4.0d
			y: -6.0d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "106A3D79B1CDE895"
			rewards: [{
				exclude_from_claim_all: true
				id: "34E988B4EF27C50E"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "7B1FD2C8F6B3FD73"
				item: "allthecompressed:netherrack_6x"
				type: "item"
			}]
			title: "地狱岩6X"
			x: -2.0d
			y: 20.0d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "6FF116239EACA390"
			rewards: [{
				exclude_from_claim_all: true
				id: "0F76CC40C455F862"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "4BDF3E91AC7A9679"
				item: "allthecompressed:end_stone_5x"
				type: "item"
			}]
			title: "末地石5X"
			x: 3.5d
			y: 18.5d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "4CADD50E7E83CD0E"
			rewards: [{
				exclude_from_claim_all: true
				id: "1109030720AE43A5"
				table_id: 7025454341029952768L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "70D0DA550B22F25B"
				item: "allthecompressed:emerald_block_4x"
				type: "item"
			}]
			title: "&a块&f绿宝石4X"
			x: 3.5d
			y: 16.5d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "6CD7A3760C6D87E6"
			rewards: [{
				exclude_from_claim_all: true
				id: "64A97263D428ADBA"
				table_id: 7025454341029952768L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "450849B8598BA80A"
				item: "allthecompressed:diamond_block_4x"
				type: "item"
			}]
			title: "&a块&f钻石4X"
			x: -3.5d
			y: 16.5d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "709F1FA492703463"
			rewards: [{
				exclude_from_claim_all: true
				id: "346F5AB9E4F8E509"
				table_id: 7025454341029952768L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "137FE17A6B2DBD19"
				item: "allthecompressed:obsidian_5x"
				type: "item"
			}]
			title: "黑曜石5X"
			x: -3.5d
			y: 18.5d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "5F0482CDD3FC667D"
			rewards: [{
				exclude_from_claim_all: true
				id: "2F1482F1B3369C41"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "5176CE663C335615"
				item: "allthecompressed:dirt_6x"
				type: "item"
			}]
			title: "泥土6X"
			x: 2.0d
			y: 20.0d
		}
		{
			dependencies: [
				"3178C46D9BD7FA88"
				"72B531D6BCCF42C4"
				"272CB4EDED35C47B"
				"1C5E273723637C43"
			]
			description: ["注意:要制作所需的&a飞行模块&f,你需要先制作一个&a注射器&r,然后通过用它攻击&b恶魂&r来填充它."]
			hide_dependent_lines: true
			id: "6EBCCC78DC682CC6"
			rewards: [
				{
					id: "14E98566218C16C2"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"现实大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "392529EC0611951C"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "23A2025FB9592282"
					table_id: 5196609362437981520L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "39D19F3F6F6B3D02"
				item: "allthetweaks:improbable_probability_device"
				type: "item"
			}]
			title: "&e现实之主"
			x: -2.0d
			y: 3.0d
		}
		{
			dependencies: [
				"6AF6A1985C103D9C"
				"3B13BBC38696D452"
			]
			dependency_requirement: "one_completed"
			description: ["无论你选择哪条路径来制作数字存储,制作&eSta&r都需要其中一个!"]
			hide_dependent_lines: true
			id: "272CB4EDED35C47B"
			rewards: [{
				exclude_from_claim_all: true
				id: "321B617668BB4829"
				table_id: 7025454341029952768L
				type: "loot"
			}]
			tasks: [{
				id: "4272471A1AADDC27"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "extradisks:1048576k_storage_part"
							}
							{
								Count: 1b
								id: "extradisks:1048576k_fluid_storage_part"
							}
							{
								Count: 1b
								id: "megacells:cell_component_256m"
							}
						]
					}
				}
				title: "大型数字存储部件"
				type: "item"
			}]
			title: "大型数字存储"
			x: -7.0d
			y: 13.0d
		}
		{
			dependencies: ["42E5B5C27799DD93"]
			description: ["记得把它们充满!"]
			hide_dependent_lines: true
			id: "3178C46D9BD7FA88"
			rewards: [{
				exclude_from_claim_all: true
				id: "2257F18FFBAD3593"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [{
				count: 2L
				id: "697A057C9E2E86BC"
				item: {
					Count: 1
					id: "powah:battery_nitro"
					tag: { }
				}
				match_nbt: false
				type: "item"
			}]
			title: "2个硝基电池"
			x: -4.0d
			y: 6.0d
		}
		{
			dependencies: ["42E5B5C27799DD93"]
			hide_dependent_lines: true
			id: "2B539F4F290DC4CF"
			rewards: [{
				exclude_from_claim_all: true
				id: "26A55BA86B5C6A65"
				table_id: 1160439751879588774L
				type: "loot"
			}]
			tasks: [{
				id: "11F66AFE2DCCBBBF"
				item: "powah:player_transmitter_nitro"
				type: "item"
			}]
			title: "硝基&a玩家供电仪&f"
			x: -6.0d
			y: 4.0d
		}
		{
			dependencies: [
				"6AF6A1985C103D9C"
				"3B13BBC38696D452"
			]
			dependency_requirement: "one_completed"
			description: ["无论你选择哪条路径来制作数字存储,制作&eSta&r都需要其中一个!"]
			hide_dependent_lines: true
			id: "16E4EA08B647E8E0"
			rewards: [{
				exclude_from_claim_all: true
				id: "14DB7B6B838E370E"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [{
				id: "3A046374A91F0A2D"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "universalgrid:wireless_universal_grid"
							}
							{
								Count: 1b
								id: "ae2wtlib:wireless_universal_terminal"
							}
						]
					}
				}
				title: "无线终端"
				type: "item"
			}]
			title: "无线终端"
			x: -5.5d
			y: 13.0d
		}
		{
			dependencies: ["3B13BBC38696D452"]
			hide_dependent_lines: true
			id: "4D3D96B6019CA7F9"
			rewards: [{
				exclude_from_claim_all: true
				id: "546EC7E2A8023E24"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				id: "1E0D86524C2B56C9"
				item: "ae2:singularity"
				type: "item"
			}]
			title: "奇点"
			x: -4.5d
			y: 11.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: ["&a&d气动工艺&f&r的核心在于压力!\\n\\n要开始,你需要制作一些&3&a压缩铁锭&f&r!最简单的方法是挖一个洞,扔进一些铁锭(或铁块),然后用TNT炸开!\\n\\n有些可能会在爆炸中丢失,但这是我们不得不承担的风险!"]
			hide_dependency_lines: true
			id: "088D80D8790DBD90"
			rewards: [
				{
					id: "06F40832496CDD9E"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "pneumaticcraft:book"
						}
					}
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "3B4C13EA5F222048"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			size: 1.5d
			tasks: [{
				id: "53F6F7544E28F1CC"
				item: "pneumaticcraft:ingot_iron_compressed"
				type: "item"
			}]
			title: "&d&d气动工艺&f"
			x: 11.0d
			y: -4.0d
		}
		{
			dependencies: ["3B13BBC38696D452"]
			hide_dependent_lines: true
			id: "6E29BA2E8642AF53"
			rewards: [{
				exclude_from_claim_all: true
				id: "2165187290827DB9"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				id: "1E234D148FC292E0"
				item: "ae2:quantum_ring"
				type: "item"
			}]
			title: "&aME量子环&f"
			x: -6.5d
			y: 11.0d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			description: ["别告诉别人是我说的,但确实有些有趣的方法可以制作&5惰性&a下界之星&f&r,它们能转化为真正的&a下界之星&f"]
			hide_dependency_lines: true
			id: "0A37761737B01BFD"
			rewards: [
				{
					id: "06D1AEF8DADA23BD"
					item: {
						Count: 1
						id: "minecraft:nether_star"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"众神之星\",\"italic\":false,\"color\":\"dark_purple\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "7F841182CDB587C1"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "6A5D35D95895A958"
					table_id: 5196609362437981520L
					type: "loot"
				}
			]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				count: 15L
				id: "2A37E5A3F7716D02"
				item: "allthecompressed:nether_star_block_3x"
				type: "item"
			}]
			title: "15 &a下界之星&f 3倍"
			x: 0.0d
			y: 21.0d
		}
		{
			dependencies: ["61D6C9461F10CCF1"]
			hide_dependent_lines: true
			id: "71B824BEA02C4A34"
			rewards: [{
				exclude_from_claim_all: true
				id: "258E055533C10561"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					id: "234CAE0F96ED29A7"
					item: {
						Count: 1
						id: "evilcraft:weather_container"
						tag: {
							weather: "LIGHTNING"
						}
					}
					type: "item"
				}
				{
					id: "769372D9F3317557"
					item: "evilcraft:lightning_bomb"
					type: "item"
				}
			]
			title: "天气系统"
			x: 5.0d
			y: -4.5d
		}
		{
			dependencies: ["2435B6D970F48DF8"]
			hide_dependent_lines: true
			id: "3912DE46B5F39287"
			rewards: [{
				exclude_from_claim_all: true
				id: "63B550D919011DB4"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				id: "66F4491906A5AB8C"
				item: {
					Count: 1
					id: "occultism:soul_gem"
					tag: { }
				}
				type: "item"
			}]
			title: "空灵宝石"
			x: -6.0d
			y: -4.0d
		}
		{
			dependencies: [
				"1B2898A31C297B58"
				"050AAD831C0AE375"
				"54277F570314DCE1"
				"0301633594309CC6"
			]
			id: "4775F7326720251E"
			rewards: [
				{
					id: "3F4169D77B00BC02"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"&a元素&f大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "3959AD37C75CDA96"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "7E7BC6FA774BD76D"
					table_id: 5196609362437981520L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "298D54656EEF2372"
				item: "allthetweaks:philosophers_fuel"
				type: "item"
			}]
			title: "&e元素大师&f"
			x: -3.0d
			y: 1.0d
		}
		{
			dependencies: [
				"4F1575600E7CD512"
				"69E96EE9A9A2F423"
			]
			id: "32DC639415E45075"
			rewards: [
				{
					id: "758235727BB06108"
					item: {
						Count: 1
						id: "allthemodium:allthemodium_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"亡灵大师\",\"italic\":false,\"color\":\"gold\"}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "68A18CE16C45CE2B"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "37590EC9826961A0"
					table_id: 5196609362437981520L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "43275F9CD53111B2"
				item: "allthetweaks:withers_compass"
				type: "item"
			}]
			title: "&e亡灵主宰"
			x: 3.0d
			y: -1.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"制作&6星辰&r需前往&d&d暮色森林&f&r展开冒险!"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"7732CF7AAA63DB3A\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "1B2898A31C297B58"
			rewards: [{
				exclude_from_claim_all: true
				id: "5B750078DB2B40BB"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [
				{
					id: "7B365A72AEF933E8"
					item: "twilightforest:twilight_portal_miniature_structure"
					type: "item"
				}
				{
					id: "62980DBF3A15DDBF"
					item: "twilightforest:snow_queen_trophy"
					type: "item"
				}
			]
			title: "&d暮色森林&f"
			x: -5.0d
			y: -5.0d
		}
		{
			dependencies: ["61D6C9461F10CCF1"]
			hide_dependent_lines: true
			id: "050AAD831C0AE375"
			rewards: [{
				exclude_from_claim_all: true
				id: "72FC7D1AF9A70E06"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [{
				id: "5F3FEF47C4141704"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 10000
						}
					}
					id: "evilcraft:flesh_rejuvenated"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 10000
					}
				}
				type: "item"
			}]
			title: "复苏之肉"
			x: 6.0d
			y: -3.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: ["你可能需要多挑战几次&d&a末影龙&f&r....\\n\\n制作&e星能之杖&r需要若干&d&a龙蛋&f&r和其他材料如&d&a龙鳞&f&r\\n\\n专业建议:使用&a敌对神经网络&r生成&a龙蛋&f或&a龙息&f,这样就不必反复挑战她了!"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "22D716F330A6D4CE"
			rewards: [{
				exclude_from_claim_all: true
				id: "762F3AE6F16DA659"
				table_id: 1160439751879588774L
				type: "loot"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [
				{
					id: "68B5826F43FF807F"
					item: "mysticalagradditions:dragon_scale"
					type: "item"
				}
				{
					id: "4A034A7FF01981D0"
					item: "minecraft:dragon_egg"
					type: "item"
				}
				{
					id: "4B34B0C3998AFE57"
					item: "ends_delight:dragon_tooth"
					type: "item"
				}
			]
			title: "&d末影龙残骸&f"
			x: -1.5d
			y: -7.5d
		}
		{
			dependencies: ["519604E883E6B620"]
			hide_dependent_lines: true
			id: "4F1575600E7CD512"
			rewards: [{
				exclude_from_claim_all: true
				id: "4792CC80F9435757"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [{
				id: "21FEA5B3DD72F35D"
				item: "industrialforegoing:wither_builder"
				type: "item"
			}]
			title: "&a凋灵生成装置&f"
			x: 6.5d
			y: 2.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&d&d通用机械&f&r模组可从游戏初期开始发展,直至整合包通关前都大有可为.\\n\\n制作星辰需要5份&d反物质&r,建议尽早着手准备!需要入门指引吗？"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"23983F4DC524B14B\"}, \"text\": \"Click here to start the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			id: "1D7104AE853A3D86"
			rewards: [{
				exclude_from_claim_all: true
				id: "3747AFB7230A82E6"
				table_id: 1160439751879588774L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "67FD7FB84A1DBDC1"
				item: "mekanism:supercharged_coil"
				type: "item"
			}]
			title: "&d&d通用机械&f"
			x: 12.0d
			y: -2.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&dPowah&r正如其名:专攻无限能源!!!\\n\\n需深入该模组以制作星辰所需的高级物品."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"2A6EBEEBAB882679\"}, \"text\": \"Click here to check out the Powah questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			id: "42E5B5C27799DD93"
			rewards: [{
				exclude_from_claim_all: true
				id: "2B1FB78A3FA33030"
				table_id: 487623848494439020L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "4F2E454789548772"
				item: "powah:energizing_orb"
				type: "item"
			}]
			title: "&dPowah能源"
			x: -6.0d
			y: 6.0d
		}
		{
			dependencies: ["5A912903E09F664F"]
			hide_dependent_lines: true
			id: "1AB8C28C9E991E1C"
			rewards: [{
				exclude_from_claim_all: true
				id: "7B7E2E6658668E1A"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [{
				id: "65813D5BFDD8B8C0"
				item: "ars_nouveau:summon_focus"
				type: "item"
			}]
			title: "&a召唤核心&f"
			x: -6.5d
			y: -2.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"当前发电量如何？达到1亿RF/t了吗？若未达标,我们得改进系统.\\n\\n有多种方式可满足&6星辰&r制作的能源需求,现在就开始建设吧.\\n\\n若未查阅过&a全能能源&r章节,该部分将帮助你了解能源选项!"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"5D045EF1AB73DF70\"}, \"text\": \"Click here to check out the AllThePower questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			icon: {
				Count: 1
				id: "mekanism:creative_energy_cube"
				tag: {
					mekData: {
						EnergyContainers: [{
							Container: 0b
							stored: "18446744073709551615.9999"
						}]
						componentConfig: {
							config0: {
								side0: 4
								side1: 4
								side2: 4
								side3: 4
								side4: 4
								side5: 4
							}
						}
					}
				}
			}
			id: "1939BCB2920B6C34"
			rewards: [{
				exclude_from_claim_all: true
				id: "684BA81C3C2F509F"
				table_id: 7384360297332422647L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "3C058B0D358129C8"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanismgenerators:gas_burning_generator"
							}
							{
								Count: 1b
								id: "powah:reactor_nitro"
							}
							{
								Count: 1b
								id: "biggerreactors:turbine_terminal"
							}
							{
								Count: 1b
								id: "mekanismgenerators:turbine_casing"
							}
							{
								Count: 1b
								id: "mekanismgenerators:fusion_reactor_controller"
							}
						]
					}
				}
				title: "能源选项"
				type: "item"
			}]
			title: "&c能源核心"
			x: 3.5d
			y: 13.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: ["准备好收集数百万物品了吗？希望你别还在用普通箱子!\\n\\n第一步是升级虚拟存储模组,可选择&9&d精致存储&f&r或&9&d应用能源2&f&r,它们都能实现物品&2自动合成&r功能!"]
			icon: "ars_nouveau:archwood_chest"
			id: "0F0145C3745A796F"
			rewards: [{
				id: "4B30031FFD9A209B"
				type: "xp"
				xp: 50
			}]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "2C89CEC05741A438"
				item: "ae2:silicon"
				type: "item"
			}]
			title: "&a存储系统"
			x: -3.5d
			y: 13.0d
		}
		{
			dependencies: ["0F0145C3745A796F"]
			description: [
				"&9&d精致存储&f&r是提供简易网络化存储系统的大容量存储模组."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"15AAF17B6665223D\"}, \"text\": \"Click here to start the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			id: "6AF6A1985C103D9C"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "1011BA91B88F5B47"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [{
				id: "0069AB004055D6C8"
				item: "refinedstorage:controller"
				type: "item"
			}]
			title: "&d精致存储&f"
			x: -5.5d
			y: 14.0d
		}
		{
			dependencies: ["0F0145C3745A796F"]
			description: [
				"&d应用能源2&f(简称&oAE2&r)是功能强大的&b数字存储&f模组!\\n\\n即使选择&a精致存储&r路线,仍需制作该模组部分物品来完成&6星辰&r."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"07210DDF872160BA\"}, \"text\": \"Click here to start the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			icon: "ae2:controller"
			id: "3B13BBC38696D452"
			rewards: [{
				exclude_from_claim_all: true
				id: "3637FAAC93E84867"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [{
				id: "7F1E31FC25B44926"
				item: "ae2:certus_quartz_crystal"
				type: "item"
			}]
			title: "&d应用能源2&f"
			x: -5.5d
			y: 12.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: ["制作&e&aATM之星&f&r需要海量资源!\\n\\n&a自动取款机&f整合包提供多种模组实现资源自动化生产.虽然部分设备非必需品,但这个任务线会展示所有资源获取方案!"]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "botania:item/overgrowth_seed"
				}
			}
			id: "46B515C90C13A72F"
			rewards: [{
				id: "600B88D837FD3BF2"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "5E69BDE183E08FF3"
				title: "资源采集"
				type: "checkmark"
			}]
			title: "&2资源自动化"
			x: 0.0d
			y: 16.5d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			description: [
				"&2&d神秘农业&f&r能将多数资源作物化种植!包括钻石、石英乃至&6ATM金属&r等模组材料!\\n\\n还需用下界精华合成&5&a创造精华&f&r,此步骤必不可少!"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"5C764279146E5E66\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependent_lines: true
			id: "3FEA99DD35D4B7CF"
			rewards: [{
				exclude_from_claim_all: true
				id: "173ABDF0ACD620DC"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [{
				id: "0CECD87DB952DD82"
				item: "mysticalagriculture:prosperity_seed_base"
				type: "item"
			}]
			title: "&2&d神秘农业&f"
			x: 1.5d
			y: 18.0d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			description: ["与其生成资源,何不直接开采整个世界？\\n\\n这些&a采石场设备&r能大规模开采资源,其中&d&a区块破坏者&f&r效率最高!\\n\\n建议在&a&a采矿维度&f&r使用这些设备."]
			id: "54E812911D10AA51"
			rewards: [{
				exclude_from_claim_all: true
				id: "7EE486F1C2467ABE"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [{
				id: "2D7A911DC7E15861"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:digital_miner"
								tag: {
									mekData: {
										EnergyContainers: [{
											Container: 0b
											stored: "50000"
										}]
									}
								}
							}
							{
								Count: 1b
								id: "quarryplus:adv_quarry"
							}
							{
								Count: 1b
								id: "rftoolsbuilder:builder"
							}
						]
					}
				}
				title: "示例采石场"
				type: "item"
			}]
			title: "&a世界采掘者&f"
			x: -2.0d
			y: 16.5d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			description: [
				"&a&d工业先锋&f&r提供从虚空生成资源的独特方案.\\n\\n使用&a&a镭射钻基座&f&r搭建可定制的&a&a镭射钻&f&r多方块结构,配合彩色&a&a镭射聚焦透镜&f&r可提高特定矿石产出率.\\n\\n此为获取&6玄钢&r、&6振金&r和&6难寻矿&r的唯一途径!"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"193F91842D2ED7D9\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			id: "254DD23FB7AEB36B"
			min_width: 300
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "7C217CC8B49274AF"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					id: "3F9B24F1C49F64F7"
					item: "industrialforegoing:ore_laser_base"
					type: "item"
				}
				{
					id: "4E4D168DD2B50586"
					item: "industrialforegoing:laser_drill"
					type: "item"
				}
				{
					id: "65334074F9662481"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "industrialforegoing:laser_lens0"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens1"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens2"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens3"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens4"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens5"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens6"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens7"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens8"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens9"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens10"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens11"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens12"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens13"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens14"
								}
								{
									Count: 1b
									id: "industrialforegoing:laser_lens15"
								}
							]
						}
					}
					title: "&a镭射聚焦透镜&fes"
					type: "item"
				}
			]
			title: "&d虚空采矿"
			x: 2.0d
			y: 16.5d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			description: ["最佳资源获取方式之一是&a&a怪物农场&f&r.除了用&a刷怪蛋&f改造刷怪笼,模组&a敌对神经网络&r还能通过模拟系统将能量转化为&a&a生物资源&f&r\\n\\n这是获取98,415个&a下界之星&f制作&e星能之杖&r的最佳途径."]
			id: "258B387107AEB2EB"
			rewards: [{
				exclude_from_claim_all: true
				id: "58E01BB7B3F3820A"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [{
				id: "1F32FCA856406E0C"
				item: "hostilenetworks:sim_chamber"
				type: "item"
			}]
			title: "&e&a怪物农场&f自动化"
			x: -1.5d
			y: 18.0d
		}
		{
			dependencies: ["46B515C90C13A72F"]
			description: [
				"&2&d资源蜜蜂&f&r能将普通蜜蜂改造成生产铁、钻石等资源的蜂种!\\n\\n需制作&d休眠态&a龙蛋&f&r来完成&6星辰&r."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"26E6ED94168A05C4\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			id: "6C329168001C1FB4"
			rewards: [{
				exclude_from_claim_all: true
				id: "791EE9BD42034F29"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [{
				id: "46C2FEF2F2EF757D"
				item: "productivebees:advanced_oak_beehive"
				type: "item"
			}]
			title: "&e&d资源蜜蜂&f"
			x: 0.0d
			y: 19.0d
		}
		{
			dependencies: ["585502BC014E420F"]
			description: ["制作&e&aATM之星&f&r需要大量时间和材料,如何起步？\\n\\n通往星辰之路不止一条,但终将殊途同归\\n\\n首先需要关注三大核心要素:\\n\\n&c能源系统&r、&a存储方案&r和&2资源生产&r."]
			icon: "minecraft:dragon_egg"
			id: "11B8C5F88DCB3BF5"
			rewards: [
				{
					id: "42A7A77B539294C4"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "280C7B91E4FBF3DA"
					table_id: 6573526605066559568L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 3.0d
			tasks: [{
				id: "60705F980E6AA8F3"
				title: "接下来会发生什么？"
				type: "checkmark"
			}]
			title: "后续发展"
			x: 0.0d
			y: 13.0d
		}
		{
			dependencies: ["2543F16043EE2777"]
			description: ["技巧:先在火盆上放置&a仪式基座&f,用四种野生生物掉落物各一个&a右键点击&f,最后激活仪式召唤&a荒野奇美拉&f."]
			id: "5A912903E09F664F"
			rewards: [{
				exclude_from_claim_all: true
				id: "1188FD510EB6D524"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [
				{
					id: "530903C213BF36B3"
					item: "ars_nouveau:ritual_wilden_summon"
					type: "item"
				}
				{
					id: "567A1EA88895482C"
					item: "ars_nouveau:wilden_tribute"
					type: "item"
				}
			]
			title: "召唤&a荒野奇美拉&f"
			x: -8.0d
			y: -2.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"制作星辰需通过&d&d新生魔艺&f&r探索魔法世界!\\n\\n旅程中将制作专属法术书、强化强力法术,并迎战&d&a荒野奇美拉&f&r!"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"6AEDA2F9BEB57759\"}, \"text\": \"Click here to check out the Ars Nouveau questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			id: "2543F16043EE2777"
			rewards: [{
				exclude_from_claim_all: true
				id: "2A8350A6C4F980BB"
				table_id: 487623848494439020L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "59C07F8A254D52B3"
				item: "ars_nouveau:ritual_brazier"
				type: "item"
			}]
			title: "&9&d新生魔艺&f"
			x: -9.0d
			y: -4.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&c&d邪恶工艺&f&r实则并不邪恶...只是需大量&o敌血&r作为材料.\\n\\n制作星辰物品过程中将消耗&o海量&r血液.需要入门帮助吗？"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"3456E0C530C0038E\"}, \"text\": \"Click here to start the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			id: "61D6C9461F10CCF1"
			rewards: [{
				exclude_from_claim_all: true
				id: "5CF4580AD43A8720"
				table_id: 487623848494439020L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "02FBDBA27AA86BD5"
				item: "evilcraft:dark_gem"
				type: "item"
			}]
			title: "&d&d邪恶工艺&f"
			x: 6.0d
			y: -6.75d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: ["在异界深处,你会发现整合包中最难的地牢之一:&e全钯合金金字塔&r.\\n\\n这座金字塔栖息着ATM中最强大的生物——&5猪巫妖&r.你需要找到击败它们的方法,并收集它们的&e&a猪巫妖之心&f&r来制作整合包中最顶级的合金!"]
			hide_dependency_lines: false
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "rechiseled:block/basalt_chiseled_piglin"
				}
			}
			id: "1149F92182DC2A54"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "19D6EE918FCC3EDF"
					table_id: 7384360297332422647L
					type: "loot"
				}
				{
					id: "4AE6F7370612B872"
					type: "xp"
					xp: 100
				}
			]
			size: 1.5d
			tasks: [
				{
					icon: {
						Count: 1
						id: "ftbquests:custom_icon"
						tag: {
							Icon: "minecraft:textures/entity_icon/piglin/piglin.png"
						}
					}
					id: "07853F746BBA3A90"
					structure: "allthemodium:ancient_pyramid"
					type: "structure"
				}
				{
					id: "703C12DAC547F77A"
					item: "allthemodium:piglich_heart"
					type: "item"
				}
			]
			title: "&e全钯合金金字塔"
			x: 0.0d
			y: 10.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"需要恶魔伙伴吗？&d&d神秘学&f&r正合你意!\\n\\n在恶魔征程中,你将召唤新伙伴协助制作合成星辰所需的神器."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"4C507C004144BFEE\"}, \"text\": \"Click here to check out the Occultism questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			id: "2435B6D970F48DF8"
			rewards: [{
				exclude_from_claim_all: true
				id: "685233B5241D46B1"
				table_id: 487623848494439020L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "6621F880669CE61F"
				item: "occultism:datura_seeds"
				type: "item"
			}]
			title: "&d&d神秘学&f"
			x: -6.0d
			y: -6.0d
		}
		{
			dependencies: ["1149F92182DC2A54"]
			description: ["使用&9Powah&r模组,你可以通过将&e全钯合金&r、&e振金&r和&e难得素&r锭与&d&a猪巫妖之心&f&r混合,制作整合包中最强大的&e合金&r.\\n\\n专业提示:制作&e星芒&r配方需要至少252个&d&a难得素-ATM合金锭&f&r(或28个方块)!"]
			id: "1E7D0E40D0E2B026"
			rewards: [{
				exclude_from_claim_all: true
				id: "54EC504B38C7C50F"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [
				{
					id: "0CB3B9639823340E"
					item: "allthemodium:vibranium_allthemodium_alloy_ingot"
					type: "item"
				}
				{
					id: "0B797FF932DF45C7"
					item: "allthemodium:unobtainium_vibranium_alloy_ingot"
					type: "item"
				}
				{
					id: "5CDB842724724BB0"
					item: "allthemodium:unobtainium_allthemodium_alloy_ingot"
					type: "item"
				}
			]
			title: "&e&aATM合金&f"
			x: 0.0d
			y: 8.0d
		}
		{
			dependencies: ["1E7D0E40D0E2B026"]
			id: "6A7495E7884FA8EE"
			rewards: [
				{
					id: "77E93511635DA2ED"
					item: {
						Count: 1
						id: "allthemodium:alloy_sword"
						tag: {
							Enchantments: [{ }]
							display: {
								Name: "[{\"text\":\"合金大师\",\"italic\":false}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "0832E904C70196CD"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "1F13537600C66804"
					table_id: 7175652334583451871L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "1388FAB3BDCF16F3"
					table_id: 1160439751879588774L
					type: "loot"
				}
			]
			tasks: [{
				count: 28L
				id: "3C29DD4BCE2918F0"
				item: "allthemodium:unobtainium_allthemodium_alloy_block"
				type: "item"
			}]
			title: "&e&a星芒外壳&f"
			x: 1.0d
			y: 7.0d
		}
		{
			dependencies: ["1D7104AE853A3D86"]
			hide_dependent_lines: true
			id: "1C5E273723637C43"
			rewards: [{
				exclude_from_claim_all: true
				id: "2263FD9B1E31CD20"
				table_id: 3660063683786346191L
				type: "loot"
			}]
			tasks: [{
				count: 2L
				id: "37C722DE31F0A015"
				item: "mekanism:pellet_antimatter"
				type: "item"
			}]
			title: "2个&a反物质靶丸&f"
			x: 10.0d
			y: -1.0d
		}
		{
			dependencies: ["1C5E273723637C43"]
			hide_dependent_lines: true
			id: "7790E599B42A5DE5"
			rewards: [{
				exclude_from_claim_all: true
				id: "36FB8C57B092D8E9"
				table_id: 7175652334583451871L
				type: "loot"
			}]
			tasks: [{
				id: "097C872B90686AEC"
				item: "mekanism:module_gravitational_modulating_unit"
				type: "item"
			}]
			title: "&a重力调节单元&f(&a重力舱&f)"
			x: 8.0d
			y: -1.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&2&d工业先锋&f&r模组旨在全程辅助你的模组之旅.\\n\\n你将生产大量塑料、建造&a粉红史莱姆&f刷怪塔,甚至制造灭世核弹——只为铸就星辰.需要入门指引吗？"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"193F91842D2ED7D9\"}, \"text\": \"Click here to start the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			id: "68F03C08B1AE45FF"
			rewards: [{
				exclude_from_claim_all: true
				id: "7A4A06675EC428D3"
				table_id: 487623848494439020L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "05CE516CF178AE2C"
				item: "industrialforegoing:dissolution_chamber"
				type: "item"
			}]
			title: "&d&d工业先锋&f"
			x: 9.0d
			y: 4.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: ["&d神化&f大幅改变了游戏机制,包括附魔系统.你需要使用&a灌注附魔&f来制作微型传送门.在魔法标签页查看&d神化&f附魔章节了解更多!"]
			hide_dependency_lines: true
			id: "6EDC47E6F752C1C1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6260DD78BEACFBE6"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "3AFD7D64BD115224"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "apotheosis:apoth_chronicle"
						}
					}
					type: "item"
				}
			]
			size: 1.5d
			tasks: [{
				id: "387BB93C2590F167"
				item: "minecraft:enchanting_table"
				type: "item"
			}]
			title: "&d&d神化&f"
			x: 12.0d
			y: 1.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"合成&6&aATM之星&f&r需要动用55个&a机械动力&r的&e机械合成器&r进行巨型合成.\\n\\n虽无需精通&a机械动力&r模组,但需掌握基础原理来供能."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"100C477F4E63F20A\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
				""
				"注:可用大型&a水车&f供能."
			]
			hide_dependency_lines: true
			id: "695A0DC585FB6E97"
			rewards: [{
				exclude_from_claim_all: true
				id: "5310DFDE8D8A3922"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			shape: "rsquare"
			size: 1.5d
			tasks: [
				{
					count: 55L
					id: "65487ADF2E5E3FD3"
					item: "create:mechanical_crafter"
					type: "item"
				}
				{
					id: "72BBD7FD2DD5001D"
					item: "create:mechanical_arm"
					type: "item"
				}
			]
			title: "&d机械动力"
			x: 0.0d
			y: 6.0d
		}
		{
			dependencies: ["71E08AA81089133B"]
			description: ["接下来我们需要建造一个&a&a压力室&f&r来合成物品,最重要的是制作&e星芒&r所需的&d&a脉动黑洞&f&r.\\n\\n这是由&e&a压力室墙壁&f&r构成的基础5x5x5多方块结构.和大多数多方块一样,你可以用&a压力室玻璃&f替换墙面,但框架必须由墙壁组成.\\n\\n需要通过&a阀门&r向室内输送空气制造压力,记得在阀门中安装&a安全升级&r防止爆炸!\\n\\n物品输入输出需要&a&a压力室接口&f&r,注意摆放方向:蓝色端朝外是输入,金色端朝外是输出.\\n\\n用&a空气压缩机&f增压至4.9即可制作&a黑洞&f!"]
			id: "04C7B49076E48841"
			min_width: 400
			rewards: [{
				exclude_from_claim_all: true
				id: "343DA561799ACE20"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					count: 95L
					id: "444444887EC7E37A"
					item: "pneumaticcraft:pressure_chamber_wall"
					type: "item"
				}
				{
					id: "7ECD45FC2E6662E3"
					item: "pneumaticcraft:pressure_chamber_valve"
					type: "item"
				}
				{
					count: 2L
					id: "372FC49D78C412AC"
					item: "pneumaticcraft:pressure_chamber_interface"
					type: "item"
				}
			]
			title: "&a压力室&f"
			x: 8.0d
			y: -2.5d
		}
		{
			dependencies: ["088D80D8790DBD90"]
			description: ["这里简要说明制作&e星芒&r所需内容:\\n\\n首先需要&a&a空气压缩机&f&r产生空气,通过&e&a压力管道&f&r输送.\\n\\n警告:所有设备都可能爆炸!务必安装&a安全升级&r,当压力&a过高&f时会让&a管道&f优先爆炸.\\n\\n强烈建议在所有设备安装安全装置——除非你想看烟花秀."]
			id: "71E08AA81089133B"
			rewards: [{
				exclude_from_claim_all: true
				id: "2FCDB75162B79682"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					id: "5732E73D2DEF81B5"
					item: "pneumaticcraft:air_compressor"
					type: "item"
				}
				{
					id: "484B6ACA3FD290CD"
					item: "pneumaticcraft:pressure_tube"
					type: "item"
				}
				{
					id: "3D6515FCCAEE52FF"
					item: "pneumaticcraft:security_upgrade"
					type: "item"
				}
			]
			title: "空气压缩系统"
			x: 9.5d
			y: -2.5d
		}
		{
			dependencies: ["04C7B49076E48841"]
			description: ["获取制作&d&a脉动黑洞&f&r所需的&a微型导弹&r有以下途径:\\n\\n- 通过&a&d气动工艺&f&r制作PCB手动合成\\n\\n- 在战利品箱中搜寻"]
			hide_dependent_lines: true
			id: "5D0C944F84E341BE"
			min_width: 300
			rewards: [{
				exclude_from_claim_all: true
				id: "4D0326DE9BF49DFD"
				table_id: 7384360297332422647L
				type: "loot"
			}]
			tasks: [{
				id: "0DD59333451A797F"
				item: {
					Count: 1
					id: "pneumaticcraft:micromissiles"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "微型导弹"
			x: 6.5d
			y: -2.5d
		}
		{
			dependencies: ["68F03C08B1AE45FF"]
			description: ["用于固定&d凋灵&r并提取&b&a以太气体&f&r的装置!"]
			id: "519604E883E6B620"
			rewards: [{
				exclude_from_claim_all: true
				id: "1E62239E9DA73829"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				id: "154C8B03D23EE2D9"
				item: "industrialforegoing:stasis_chamber"
				type: "item"
			}]
			title: "静滞舱"
			x: 8.0d
			y: 2.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: ["我们需要从模组&a&l沉浸工程&r中获取一把&d电磁炮&r来制作&e斯塔&r结构!\\n\\n首先,合成你的第一把&a工程师锤&r.\\n\\n该任务还会赠送你完整的模组指南书.若遇到困难,记得查阅它."]
			hide_dependency_lines: true
			id: "09EF804D70C856FB"
			rewards: [
				{
					id: "13F7319EA0FDAF7A"
					item: "immersiveengineering:manual"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "7C06AF51480D9369"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			size: 1.5d
			tasks: [{
				id: "49F657420099FE3B"
				item: {
					Count: 1
					id: "immersiveengineering:hammer"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&d&l沉浸工程&r"
			x: -16.5d
			y: 0.0d
		}
		{
			dependencies: ["09EF804D70C856FB"]
			description: ["首先需要制作的是&a&l杂酚油&r!\\n\\n用27块&a焦炉砖&r搭建3x3x3结构,再用&a工程师锤&r右键点击将其转化为焦炉.投入煤炭即可产出&a焦煤&r.\\n\\n这将用于制作&2&l防腐木&r,这是合成工作台的必要材料!"]
			id: "11C0233861D3DD0C"
			rewards: [{
				exclude_from_claim_all: true
				id: "694652EEC3639974"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [
				{
					id: "36E3BB50691AFC71"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:coal_coke"
						}
					}
					title: "&a焦煤&f"
					type: "item"
				}
				{
					id: "3D16AB1FD9007128"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "immersiveengineering:creosote_bucket"
								}
								{
									Count: 1b
									id: "thermal:creosote_bucket"
								}
								{
									Count: 1b
									id: "railcraft:creosote_bucket"
								}
							]
						}
					}
					title: "&a杂酚油桶&f"
					type: "item"
				}
			]
			title: "&a杂酚油&r"
			x: -14.5d
			y: -1.0d
		}
		{
			dependencies: ["09EF804D70C856FB"]
			description: ["本模组中你需要搭建定制多方块机械来获取资源.建造方法请查阅&a&l工程师手册&r中的&e&l重型机械&r章节\\n\\n这些结构由核心方块构成,例如&e&l轻型工程块&r或&c&l红石工程块&r.每台重型机械所需建材数量不同,请做好大量合成的准备!\\n\\n用&a工程师锤&r&a&l右击&r多方块结构特定位置即可完成组装!它们还需要能源驱动."]
			id: "27E52482C789280F"
			min_width: 300
			rewards: [{
				exclude_from_claim_all: true
				id: "0A0FC8510A7D757A"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					id: "27CA82E687C62455"
					item: "immersiveengineering:light_engineering"
					type: "item"
				}
				{
					id: "7CB91E52B7869C16"
					item: "immersiveengineering:heavy_engineering"
					type: "item"
				}
				{
					id: "79A1B15D5E9B7EF7"
					item: "immersiveengineering:steel_scaffolding_standard"
					type: "item"
				}
				{
					id: "7E83A19A79A84906"
					item: "immersiveengineering:steel_fence"
					type: "item"
				}
				{
					id: "3336C0ED40FC863B"
					item: "immersiveengineering:rs_engineering"
					type: "item"
				}
				{
					id: "1C57789C3A972D26"
					item: "immersiveengineering:fluid_pipe"
					type: "item"
				}
			]
			title: "建造多方块结构"
			x: -14.5d
			y: 1.0d
		}
		{
			dependencies: ["323F39FC300F7E30"]
			description: ["在&a&l工程师手册&r的&e&l重型机械&r分类中可以找到&a压榨机&r.\\n\\n这个多方块结构能将&3&l焦煤粉&r压榨成&3&l高定向热解石墨粉&r,熔炼后即可得到锭材!"]
			id: "0D2C935F99D1A1FE"
			rewards: [{
				exclude_from_claim_all: true
				id: "22DFBA01AA6FF8CE"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					advancement: "immersiveengineering:multiblocks/mb_squeezer"
					criterion: ""
					id: "26DE65A66FF69193"
					title: "压榨机"
					type: "advancement"
				}
				{
					id: "596E49E5EE0F44F5"
					item: "immersiveengineering:ingot_hop_graphite"
					type: "item"
				}
			]
			title: "压榨机"
			x: -10.0d
			y: -1.0d
		}
		{
			dependencies: ["0D2C935F99D1A1FE"]
			description: ["利用目前收集的所有材料,我们可以制作&e斯塔&r所需的&d电磁炮&r第一个组件!"]
			id: "1AADF0A1B42AD511"
			rewards: [{
				exclude_from_claim_all: true
				id: "2AF8A4C5BDE29A2A"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				id: "6FADDCC83421C2C4"
				item: "immersiveengineering:capacitor_hv"
				type: "item"
			}]
			title: "&a高压蓄电池&r"
			x: -8.5d
			y: -1.0d
		}
		{
			dependencies: [
				"2CBEEDDD4B6DDDD9"
				"1AADF0A1B42AD511"
			]
			description: ["通过前期制作的所有部件,现在可以合成最终所需的&d电磁炮&r了!"]
			hide_dependency_lines: false
			hide_dependent_lines: true
			id: "4A3DF999B969C875"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "337F2B0772BBEC49"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "74A6816D4BD7499C"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "35A595D6801714C6"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Items: [ ]
							Size: 3
						}
					}
					id: "immersiveengineering:railgun"
				}
				type: "item"
			}]
			title: "电磁炮"
			x: -7.0d
			y: 0.0d
		}
		{
			dependencies: ["11C0233861D3DD0C"]
			description: ["&a&l工程师装配台&r用于处理各类&e工程蓝图&r.\\n\\n你需要先制作一些基础蓝图!"]
			id: "33E3574096820D99"
			rewards: [{
				exclude_from_claim_all: true
				id: "69731965BF9A046D"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [
				{
					id: "5268158EF72A8DDF"
					item: "immersiveengineering:workbench"
					type: "item"
				}
				{
					id: "49A0BE7FB49B80DC"
					item: {
						Count: 1
						id: "immersiveengineering:blueprint"
						tag: {
							blueprint: "molds"
						}
					}
					type: "item"
				}
				{
					id: "3692C36BE9007C82"
					item: {
						Count: 1
						id: "immersiveengineering:blueprint"
						tag: {
							blueprint: "components"
						}
					}
					type: "item"
				}
			]
			title: "&a工程师装配台&r"
			x: -13.0d
			y: -1.0d
		}
		{
			dependencies: ["33E3574096820D99"]
			description: ["制作&a杂酚油&r时我们还应获得&3&l焦煤&r.\\n\\n需要将其粉碎成&a&l焦煤粉&r,可通过模组&d创世神&r的&a&l粉碎轮&r,或建造&d沉浸工程&r的&a粉碎机&r实现.\\n\\n若选择建造粉碎机,请在&e&l工程师手册&r的&a&l重型机械&r章节查阅多方块建造方法!"]
			id: "323F39FC300F7E30"
			rewards: [{
				exclude_from_claim_all: true
				id: "1256F69093253D99"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [{
				id: "02C38FA962D8920F"
				item: "immersiveengineering:dust_coke"
				type: "item"
			}]
			title: "&a焦煤粉&r"
			x: -11.5d
			y: -1.0d
		}
		{
			dependencies: ["27E52482C789280F"]
			description: ["利用新制作的建材,我们可以搭建第一台多方块机械!\\n\\n在&a&l工程师手册&r的&e&l重型机械&r分类中找到&a发酵机&r.\\n\\n投入不同植物可分解出&b乙醇&r!"]
			id: "20D4341820B44B91"
			rewards: [{
				exclude_from_claim_all: true
				id: "4A7D49511C57F046"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				advancement: "immersiveengineering:multiblocks/mb_fermenter"
				criterion: ""
				id: "45723AB59D9FC1B7"
				type: "advancement"
			}]
			title: "发酵机"
			x: -12.0d
			y: 1.0d
		}
		{
			dependencies: ["20D4341820B44B91"]
			description: ["接下来需要在手册的&e&l重型机械&r分类中找到&a精炼厂&r.\\n\\n这个多方块结构能利用&a银板&r将&b乙醇&r转化为&a乙醛&r!\\n\\n注意:用&a流体传输管道&r连接正面才能导出液体!"]
			id: "407E853A5B322E13"
			rewards: [{
				exclude_from_claim_all: true
				id: "19A197B2A7EE7C89"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					advancement: "immersiveengineering:multiblocks/mb_refinery"
					criterion: ""
					id: "3C054656AAF44209"
					title: "精炼厂"
					type: "advancement"
				}
				{
					id: "7978848A0670B07B"
					item: "alltheores:silver_plate"
					type: "item"
				}
			]
			title: "精炼厂"
			x: -10.0d
			y: 1.0d
		}
		{
			dependencies: ["407E853A5B322E13"]
			description: [
				"你需要再建造一个&a精炼厂&r来将&a乙醛&r进一步精炼成&c&a酚醛树脂&f&r.\\n\\n"
				"获得这种液体后,你需要建造最后一个多方块机器:&a&a灌装机&f&r.\\n\\n"
				"建造完成后,你可以用桶将树脂注入机器.\\n\\n"
				"使用你的&a工程师装配台&f,制作&a&a板材模具&f&r并将其扔上传送带.如果操作正确,模具会充满树脂并给你一个&d&a工程塑胶板&f&r!"
			]
			id: "2CBEEDDD4B6DDDD9"
			rewards: [{
				exclude_from_claim_all: true
				id: "63C417B7BDC73480"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [
				{
					count: 3L
					id: "35D9EEB6824E3FF4"
					item: "immersiveengineering:conveyor_basic"
					type: "item"
				}
				{
					id: "7CA6D658065E142C"
					item: "immersiveengineering:mold_plate"
					type: "item"
				}
				{
					id: "35CB81CE67784F1C"
					item: "immersiveengineering:plate_duroplast"
					type: "item"
				}
			]
			title: "&d&a工程塑胶板&f"
			x: -8.5d
			y: 1.0d
		}
		{
			dependencies: [
				"73990028197AF1AB"
				"527453CD5A20AE38"
				"43CE45F9FDDB91EE"
				"048F2942436D3C46"
				"6F598D9CFB915123"
				"4775F7326720251E"
				"32DC639415E45075"
				"6EBCCC78DC682CC6"
				"67E154DC24F54854"
				"73CD6CE2B10830B9"
			]
			description: [
				"&aATM之星&f.这是ATM9中的终极成就.恭喜!\\n\\n"
				"现在你可以制作&d创造模式&r物品了!查看&d创造模式&r任务章节了解更多!"
			]
			hide_dependency_lines: true
			id: "464D0C17601E8A2B"
			rewards: [
				{
					id: "1A44EDC903F8E8D7"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					id: "382984099BD8DCEF"
					item: "allthetweaks:trophy_atm"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "4B6504EB06DFE127"
					table_id: 5196609362437981520L
					type: "choice"
				}
				{
					exclude_from_claim_all: true
					id: "58C6756E0DF327FF"
					table_id: 6016109340720845000L
					type: "choice"
				}
			]
			shape: "gear"
			size: 4.0d
			subtitle: "征服世界."
			tasks: [{
				id: "5F6988D125AAB233"
				item: "allthetweaks:atm_star"
				type: "item"
			}]
			title: "&e&aATM之星&f"
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&d&d极限反应堆&f&r专注打造可定制的多方块反应堆与涡轮机,满足你的所有能源需求!"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"3C78926E5D301BA0\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "54277F570314DCE1"
			rewards: [{
				exclude_from_claim_all: true
				id: "26EE25A387A4BD5F"
				table_id: 3660063683786346191L
				type: "loot"
			}]
			size: 1.5d
			tasks: [{
				id: "04C33E2E80CD12DB"
				item: "bigreactors:insanite_block"
				type: "item"
			}]
			title: "&d&d极限反应堆&f"
			x: 6.0d
			y: 6.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&b&d寂静装备&f&r助你打造定制工具与护甲!还能熔炼多种锭为高强度合金——这正是合成星辰所需."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"1D42B373285DEF81\"}, \"text\": \"Click here to start the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "0301633594309CC6"
			tasks: [{
				id: "305AAE40607BC719"
				item: "silentgear:tyrian_steel_block"
				type: "item"
			}]
			title: "&a&d寂静装备&f"
			x: 0.0d
			y: -8.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&dAd Astra&r将带你离开这个世界!字面意思!\\n\\n你将穿梭星际,在冒险中开采新金属."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"769974FDAD5DBEB1\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "72B531D6BCCF42C4"
			tasks: [{
				id: "0D368F78760AB4A4"
				item: {
					Count: 1
					id: "ad_astra:jet_suit"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&d登星计划"
			x: -5.0d
			y: 5.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "73EFADE932DFEF86"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7346A003BF86216F"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "64D4016789711CD6"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "7476D0F37A40883B"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [
				{
					id: "41BAF5BDC9300E5C"
					item: {
						Count: 1
						id: "ironfurnaces:million_furnace"
						tag: {
							display: {
								Name: "{\"extra\":[{\"color\":\"red\",\"text\":\"R\"},{\"color\":\"red\",\"text\":\"a\"},{\"color\":\"light_purple\",\"text\":\"i\"},{\"color\":\"yellow\",\"text\":\"n\"},{\"color\":\"blue\",\"text\":\"b\"},{\"color\":\"red\",\"text\":\"o\"},{\"color\":\"red\",\"text\":\"w\"},{\"color\":\"light_purple\",\"text\":\" \"},{\"color\":\"yellow\",\"text\":\"F\"},{\"color\":\"green\",\"text\":\"u\"},{\"color\":\"aqua\",\"text\":\"r\"},{\"color\":\"light_purple\",\"text\":\"n\"},{\"color\":\"green\",\"text\":\"a\"},{\"color\":\"green\",\"text\":\"c\"},{\"color\":\"green\",\"text\":\"e\"}],\"text\":\"\"}"
							}
						}
					}
					type: "item"
				}
				{
					id: "27BF6E9F246839D3"
					item: "generatorgalore:ender_generator"
					type: "item"
				}
				{
					id: "5E585E35B88D84FD"
					item: {
						Count: 1
						id: "exchangers:end_exchanger"
						tag: {
							Damage: 0
							blockstate: {
								Name: "minecraft:air"
							}
							directionalPlacement: 0b
							forceDropItems: 0b
							fuzzyPlacement: 0b
							fuzzyPlacementChance: 100
							mode: 0
							range: 0
							voidItems: 0b
						}
					}
					type: "item"
				}
				{
					id: "3C5F97640A5789B6"
					item: "mythicbotany:mjoellnir"
					type: "item"
				}
				{
					id: "241075340C282F2D"
					item: "thermal_extra:abyssal_rf_coil_xfer_augment"
					type: "item"
				}
				{
					id: "79B45411428AE0A3"
					item: "computercraft:pocket_computer_advanced"
					type: "item"
				}
				{
					id: "42EE6320BAD59620"
					item: "rftoolsutility:flight_module"
					type: "item"
				}
				{
					id: "7089713515FCF89A"
					item: "advgenerators:power_capacitor_tier3"
					type: "item"
				}
			]
			title: "制作星辰所需物品"
			x: 4.0d
			y: 6.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"如果你喜欢魔法,你会喜欢玩这个模组!\\n\\n"
				"要制作这个物品,你需要冒险进入模组中的结构寻找&d符文石&r.\\n\\n"
				"你还需要前往&a下界&f击杀&5远古骑士&r获取灰烬精华.这是用来制作&d升级宝珠&r的.其他所需物品可以在战利品箱中找到,或是在主世界采矿获得."
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "124BDAF91DA74910"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1783642ADA73E65A"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "17586358EA27FD5D"
					type: "xp"
					xp: 100
				}
			]
			shape: "square"
			tasks: [{
				id: "7BB6AE1BDF0D3EB7"
				item: "irons_spellbooks:fire_upgrade_orb"
				type: "item"
			}]
			title: "&d铁魔法与法术书"
			x: 6.0d
			y: 4.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&d&d禁忌与奥秘&f&r是华丽的魔法模组,助你打造超强物品!\\n\\n需深入探索该模组才能制作星辰所需的高级物品."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"34E8FF3196478229\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			id: "6126741150866837"
			rewards: [{
				id: "55CAAE378E2CA4A4"
				type: "xp"
				xp: 10
			}]
			size: 1.5d
			tasks: [{
				id: "39C07D45FD0CA49C"
				item: "forbidden_arcanus:arcane_crystal_dust"
				type: "item"
			}]
			title: "&d&d禁忌与奥秘&f"
			x: -7.5d
			y: 4.0d
		}
		{
			dependencies: ["6126741150866837"]
			id: "652DBDD284873140"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6C4FE8F69BB89990"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "6DE729E9004261D6"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "52CD8FADF397FFDE"
				item: "forbidden_arcanus:deorum_block"
				type: "item"
			}]
			title: "&a圣金块&f"
			x: -6.5d
			y: 3.0d
		}
		{
			dependencies: ["6126741150866837"]
			id: "6F0DCB2CDD368088"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7DEF06C6761586A7"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "4A7E68819395F821"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "2230AE5FB1D4E9D2"
				item: "forbidden_arcanus:dark_rune_block"
				type: "item"
			}]
			title: "&a暗黑符文块&f"
			x: -7.5d
			y: 2.5d
		}
		{
			dependencies: [
				"652DBDD284873140"
				"6F0DCB2CDD368088"
			]
			hide_dependent_lines: true
			id: "153F111B4CCC850B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3C9C1AF5F94A5DB1"
					table_id: 1160439751879588774L
					type: "loot"
				}
				{
					id: "562028607C486543"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "3D3372C05B445DFA"
				item: {
					Count: 1
					id: "forbidden_arcanus:eternal_stella"
					tag: { }
				}
				type: "item"
			}]
			title: "&a永恒陨星&f"
			x: -6.5d
			y: 2.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&c&d血魔法&f&r通过&c血液&r创造魔法物品.这次,你可能需要献祭自己的鲜血."
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"004F28C5C85F467B\"}, \"text\": \"Click here to start the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "69E96EE9A9A2F423"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6938E6A0EDA27E1E"
					table_id: 7384360297332422647L
					type: "loot"
				}
				{
					id: "5078761CFC27F622"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "4113D575E1B1E4D2"
				item: "bloodmagic:speedrune2"
				type: "item"
			}]
			title: "&c&d血魔法&f"
			x: 1.5d
			y: -7.5d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"若你喜爱摆弄植物,又嫌它们只能做染料——&d&d植物魔法&f&r正是答案.需推进模组进度才能制作&6星辰&r!"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"1883B79BDB2AAE5D\"}, \"text\": \"Click here to check out the questline!\", \"color\": \"#55FF55\", \"underlined\": \"true\"}"
			]
			hide_dependency_lines: true
			hide_dependent_lines: true
			id: "5FD3C68D5F218D02"
			rewards: [{
				exclude_from_claim_all: true
				id: "5DDC227CE0E4E38A"
				table_id: 7384360297332422647L
				type: "loot"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "376DE283F77F6524"
				item: "botania:life_essence"
				type: "item"
			}]
			title: "&d&d植物魔法&f"
			x: -2.5d
			y: -6.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"&d深渊祭品&r是用来召唤&5利维坦&r的物品.\\n\\n"
				"要制作它,你需要在主世界找到&a沉没神殿&r.在那里,你可以从&e&a渊灵祭司&f&r和&2珊瑚巨像&r生物身上获取掉落物.\\n\\n"
				"注意:你可以制作&d&a结构指南针&f&r来定位神殿,或者像使用&a末影之眼&f一样使用&d深渊之眼&r."
			]
			hide_dependency_lines: true
			id: "266AB725974E464C"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "64BBEADE9751EA07"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "4E373C8CD820DF3C"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "405C2925273BB6EB"
				item: "cataclysm:abyssal_sacrifice"
				type: "item"
			}]
			title: "深渊祭品"
			x: -2.5d
			y: 8.0d
		}
		{
			dependencies: ["11B8C5F88DCB3BF5"]
			description: [
				"要制作我们需要的&d&a虚空锻锤&f&r,我们需要先进行一次冒险!\\n\\n"
				"这次旅程的第一站将带你前往&a下界&f的&d灵魂熔炉&r.击败那里的&c&a下界合金巨兽&f&r,获取&d&a炼狱锻锤&f&r作为掉落物!\\n\\n"
				"注意:你可以使用&d怪物之眼&r寻找结构,也可以使用&a结构指南针&f.\\n\\n"
				"继续下一页!"
				""
				"{@pagebreak}"
				"下一步，我们需要返回主世界寻找&d先驱者&r。你可以在&a远古工厂&r结构中找到这个Boss。这就像是在困难模式下与凋灵战斗。:)\\n\\n"
				"找到结构后,使用&d&a下界之星&f&r激活Boss,祝你好运!\\n\\n"
				"如果你成功击败它,你将获得一个&e&a凋灵合金块&f&r,我们将用它来制作&e&a机械融合砧&f&r.\\n\\n"
				"继续下一页!"
				""
				"{@pagebreak}"
				"我们想用制作好的融合砧将&d&a炼狱锻锤&f&r升级为&d&a虚空锻锤&f&r...但我们缺少一个关键部件:&5&a虚空核心&f&r.要获得它,前往&a末地&f并找到&e废墟城堡&r.击败里面的&d&a末影傀儡&f&r,制作你的&d&a虚空锻锤&f&r!"
			]
			hide_dependency_lines: true
			icon: {
				Count: 1
				id: "cataclysm:void_forge"
				tag: {
					Damage: 0
				}
			}
			id: "7D8ECACF214324D6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "39D114E76CEEABE4"
					table_id: 7025454341029952768L
					type: "loot"
				}
				{
					id: "1A79FDE89728BFC7"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [
				{
					id: "5C9D993FFFC365FB"
					item: {
						Count: 1
						id: "cataclysm:infernal_forge"
						tag: {
							Damage: 0
						}
					}
					match_nbt: false
					type: "item"
				}
				{
					id: "6C405D0E0582C1CD"
					item: "cataclysm:mechanical_fusion_anvil"
					type: "item"
				}
				{
					id: "0E68CCFE6F5C9ADB"
					item: "cataclysm:void_core"
					type: "item"
				}
				{
					id: "76FA4645607619E7"
					item: {
						Count: 1
						id: "cataclysm:void_forge"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&5&a虚空锻锤&f"
			x: 2.5d
			y: 8.0d
		}
		{
			dependencies: ["61D6C9461F10CCF1"]
			hide_dependent_lines: true
			id: "4F28DC3D905DDA3A"
			rewards: [{
				exclude_from_claim_all: true
				id: "37C823D0CF9BB38E"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			tasks: [{
				id: "2E3084BAD6A2F14B"
				item: "evilcraft:piercing_vengeance_focus"
				type: "item"
			}]
			x: 3.0d
			y: -6.5d
		}
		{
			dependencies: ["61D6C9461F10CCF1"]
			hide_dependent_lines: true
			id: "03B12E7ED6B01F9A"
			rewards: [{
				exclude_from_claim_all: true
				id: "28D09BBF0DA5FFAB"
				table_id: 5564196992594175882L
				type: "loot"
			}]
			tasks: [{
				id: "0B57A9CFEC9E5319"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 4000
						}
					}
					id: "evilcraft:mace_of_destruction"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 4000
					}
				}
				type: "item"
			}]
			x: 4.0d
			y: -5.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方团队&r或&2社区贡献者&r为AllTheMods整合包编写."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"该任务已设为隐藏状态,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "7EA9E1D1890BC4BE"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "61C5E5E6742910D8"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "721E0D1C9071505A"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: -10.0d
		}
	]
	title: "§a第二章§r:§6ATM之星"
}
