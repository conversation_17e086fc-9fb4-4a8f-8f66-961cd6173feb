{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "zero_point_module"
	group: "1DA67E79B40AB130"
	icon: "gtceu:quantum_processor_mainframe"
	id: "220BE62A7F8EA359"
	images: [
		{
			height: 3.0d
			hover: ["atm9.quest.gregtech.zpm"]
			image: "gtceu:item/zpm_solar_panel"
			rotation: 0.0d
			width: 3.0d
			x: 3.0d
			y: -3.0d
		}
		{
			height: 3.0d
			image: "gtceu:item/zpm_sensor"
			rotation: 0.0d
			width: 3.0d
			x: 7.5d
			y: -7.0d
		}
		{
			height: 3.0d
			image: "gtceu:item/zpm_emitter"
			rotation: 0.0d
			width: 3.0d
			x: 0.5d
			y: -7.0d
		}
		{
			height: 3.0d
			image: "gtceu:item/zpm_robot_arm"
			rotation: 0.0d
			width: 3.0d
			x: -6.5d
			y: -7.0d
		}
		{
			height: 3.0d
			image: "gtceu:item/zpm_fluid_regulator"
			rotation: 0.0d
			width: 3.0d
			x: -13.0d
			y: -7.0d
		}
		{
			height: 3.0d
			image: "gtceu:block/multiblock/fusion_reactor/fusion/overlay_front"
			rotation: 0.0d
			width: 3.0d
			x: -13.0d
			y: 1.5d
		}
	]
	order_index: 8
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"量子时代已然来临,我们的机器正在飞速发展!"
				""
				"但更强的处理能力也意味着更多挑战:能源、资源和产线管理.不过这难不倒我们,对吧？这正是我们存在的意义!"
			]
			id: "57B5100C11F76EE9"
			rewards: [{
				id: "40310A9DED8DFBED"
				type: "xp"
				xp: 1000
			}]
			shape: "diamond"
			size: 1.6d
			subtitle: "量子之后是什么？"
			tasks: [{
				id: "75C56E1C5E4AEB0C"
				item: "gtceu:quantum_processor_mainframe"
				type: "item"
			}]
			x: -12.8d
			y: 4.0d
		}
		{
			dependencies: [
				"400E51BB469CB023"
				"18DF12E242210030"
			]
			description: ["这才是真正的未来!我们正在取得进展,向着未知领域迈进!"]
			id: "72082D70F5D22632"
			rewards: [{
				exclude_from_claim_all: true
				id: "71A04B1584677E3F"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			shape: "diamond"
			size: 1.75d
			subtitle: "&a晶体处理器主机&f!"
			tasks: [{
				id: "53856B29BA2E6B12"
				item: "gtceu:crystal_processor_mainframe"
				type: "item"
			}]
			x: 7.5d
			y: 4.0d
		}
		{
			dependencies: ["57B5100C11F76EE9"]
			description: [
				"我们对此期待已久"
				""
				"正因如此,LuV阶级的&a电路组装机&f才显得弥足珍贵!"
				""
				"现在我们可以用它来制造下一阶电路了.有人说过... 进步这个词吗？"
			]
			id: "23B9FE196A09B587"
			rewards: [{
				exclude_from_claim_all: true
				id: "13761E04ED96D8DB"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "电路盛宴!"
			tasks: [{
				id: "70B2E5E51F183692"
				item: "gtceu:luv_circuit_assembler"
				type: "item"
			}]
			x: 1.0d
			y: 4.0d
		}
		{
			dependencies: [
				"23B9FE196A09B587"
				"41AE8AEA3EB144F3"
				"5A3FF46A2B275049"
			]
			description: ["目前我们只能获得2个,但通过下一阶的科技突破,每套材料可制作4个IV级处理器!超值!"]
			id: "279BC6FAF7827738"
			rewards: [{
				exclude_from_claim_all: true
				id: "1D10BF5E4DD57A65"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "IV级"
			tasks: [{
				id: "4915ABC2F75766F6"
				item: "gtceu:crystal_processor"
				type: "item"
			}]
			x: 3.0d
			y: 4.0d
		}
		{
			dependencies: [
				"279BC6FAF7827738"
				"41AE8AEA3EB144F3"
			]
			description: ["终于!现在我们有了一个合成配方,可以用1次合成获得2个LuV处理器!&a出发&foo!"]
			id: "2943989C642F93AE"
			rewards: [{
				exclude_from_claim_all: true
				id: "74263AF1FC22DD46"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "&dLuV级"
			tasks: [{
				id: "115662BF32B8E428"
				item: "gtceu:crystal_processor_assembly"
				type: "item"
			}]
			x: 4.5d
			y: 4.0d
		}
		{
			dependencies: [
				"2943989C642F93AE"
				"41AE8AEA3EB144F3"
			]
			description: [
				"ZPM阶段仍然是2换1,但我们正在取得进展."
				""
				"很快我们就能进入下一阶段,获得更好的合成回报!"
			]
			id: "400E51BB469CB023"
			rewards: [{
				exclude_from_claim_all: true
				id: "3ECE14BB3713B06B"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "ZPM级"
			tasks: [{
				id: "1C65673B6A316C26"
				item: "gtceu:crystal_processor_computer"
				type: "item"
			}]
			x: 6.0d
			y: 4.0d
		}
		{
			dependencies: [
				"4DDFD3BAA86DC342"
				"574E65B7954A13D0"
			]
			description: ["制作这个之前,我们需要一个&e研究站&r!继续推进本章进度吧!\\n\\n完成研究后,我们就能为多方块结构带来新级别的能源!"]
			id: "303A3AFA49DAC64F"
			rewards: [{
				exclude_from_claim_all: true
				id: "64D8E1362F7C77AA"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			shape: "gear"
			size: 1.5d
			subtitle: "更多能源!"
			tasks: [{
				id: "78EB177A110F9E51"
				item: "gtceu:zpm_energy_input_hatch"
				type: "item"
			}]
			x: -5.0d
			y: 1.0d
		}
		{
			dependencies: [
				"3BD5C517AD024A45"
				"4DDFD3BAA86DC342"
			]
			description: [
				"&3活性变压器&r是格雷科技设施中最有效的EU传输方式."
				""
				"使用时需要遵守以下规则:"
				"1.) 管道/激光只能直线传输.可以使用另一个&3活性变压器&r改变激光方向"
				"2.) 管道必须染色才能工作,需要使用格雷科技的&a喷漆罐&f"
			]
			icon: "gtceu:active_transformer"
			id: "278252472B5B94D4"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "251F2E5A2D1BC26D"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			shape: "gear"
			size: 1.0d
			subtitle: "激光管道"
			tasks: [
				{
					id: "0E7925D7F1B3A06B"
					item: "gtceu:active_transformer"
					type: "item"
				}
				{
					count: 18L
					id: "4300336FCC6BB7F3"
					item: "gtceu:high_power_casing"
					type: "item"
				}
				{
					id: "447E86A2C00AC55F"
					item: "gtceu:superconducting_coil"
					type: "item"
				}
			]
			x: -8.0d
			y: -1.0d
		}
		{
			dependencies: ["2036ED4A823C1456"]
			description: [
				"这些晶圆可制成最高级的传统半导体芯片——UHPIC(超&a高功率集成电路&f)."
				""
				"我们即将制造的许多机器都会大量需求这种芯片."
			]
			hide_dependency_lines: true
			id: "53C5CE6433E201BD"
			rewards: [{
				count: 4
				id: "303D615EB146BB20"
				item: "gtceu:uhpic_wafer"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "超&a高功率电路&f"
			tasks: [{
				id: "12F678374AA282E7"
				item: "gtceu:uhpic_wafer"
				type: "item"
			}]
			x: -9.0d
			y: 0.0d
		}
		{
			dependencies: ["53C5CE6433E201BD"]
			description: ["UHPIC芯片需要通过切割机将晶圆&a切割&f成芯片."]
			id: "4DDFD3BAA86DC342"
			rewards: [{
				count: 4
				id: "7D7D55F821BEDD38"
				item: "gtceu:uhpic_chip"
				random_bonus: 12
				type: "item"
			}]
			subtitle: "半导体真有趣!"
			tasks: [{
				id: "461A7534464550FF"
				item: "gtceu:uhpic_chip"
				type: "item"
			}]
			x: -8.0d
			y: 0.0d
		}
		{
			dependencies: [
				"37A1137A59A2086B"
				"4DDFD3BAA86DC342"
				"6DA0ABBC89711536"
				"551CD9E0FA2E62A7"
			]
			description: [
				"聚变反应是两个原子核结合形成新物质的过程."
				""
				"这正是我们要使用&a湮灭反应堆&f的目的——获取反应产物用于进一步加工和合成!"
				""
				"查看JEI中的多方块结构信息时，记得点击P:0&r切换到P:1&r，才能看到使用&a聚变玻璃&f的正确结构。"
			]
			icon: "gtceu:luv_fusion_reactor"
			id: "2F4258088CBFC399"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "592051FE656E1B8C"
					table_id: 5732951907492768982L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "3A1AB95942194082"
					table_id: 5732951907492768982L
					type: "loot"
				}
			]
			size: 1.0d
			subtitle: "你需要一个聚变先生!"
			tasks: [
				{
					id: "59D732639645233B"
					item: "gtceu:luv_fusion_reactor"
					type: "item"
				}
				{
					count: 16L
					id: "17FDDF7FF634030E"
					item: { Count: 16, id: "gtceu:luv_energy_input_hatch" }
					type: "item"
				}
				{
					count: 16L
					id: "711D8DB312A7C5CD"
					item: { Count: 16, id: "gtceu:lv_output_hatch" }
					type: "item"
				}
				{
					count: 16L
					id: "0655BAC9EC4C7729"
					item: { Count: 16, id: "gtceu:lv_input_hatch" }
					type: "item"
				}
				{
					count: 31L
					id: "018F9E29F29B5C16"
					item: { Count: 31, id: "gtceu:fusion_glass" }
					type: "item"
				}
				{
					count: 48L
					id: "15E8CB4915A20A97"
					item: { Count: 48, id: "gtceu:fusion_casing" }
					type: "item"
				}
				{
					count: 4L
					id: "42B9817894A7AB4C"
					item: { Count: 4, id: "gtceu:superconducting_coil" }
					type: "item"
				}
			]
			x: -2.0d
			y: -0.5d
		}
		{
			dependencies: ["3BD5C517AD024A45"]
			description: [
				"这种超导线圈在多种多方块结构和配方中都有广泛应用,它有三种不同的合成方式."
				""
				"当前我们将使用资源成本最高但最容易制作的初级版本."
			]
			id: "37A1137A59A2086B"
			rewards: [{
				exclude_from_claim_all: true
				id: "1EF50A1AD9F875F1"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "超导体!"
			tasks: [{
				id: "022330425EF7131D"
				item: "gtceu:superconducting_coil"
				type: "item"
			}]
			x: -2.7d
			y: -1.1999999999999997d
		}
		{
			dependencies: ["7EE14ED04C64E2AA"]
			description: [
				"我们快成功了!"
				""
				"现在有了多层纤维强化&a印刷电路板&f,可以直接用于下一阶段的处理器制作!"
			]
			id: "41AE8AEA3EB144F3"
			rewards: [{
				exclude_from_claim_all: true
				id: "63A3B1E5622F2745"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			shape: "gear"
			size: 1.5d
			subtitle: "完成它们!"
			tasks: [{
				id: "2D632FE10CBB8E16"
				item: "gtceu:multilayer_fiber_reinforced_printed_circuit_board"
				type: "item"
			}]
			x: 4.5d
			y: 1.5d
		}
		{
			description: ["如果对LuV阶段制作的纤维强化&a电路板&f稍改配方,就能制成多层纤维强化&a电路板&f"]
			id: "7EE14ED04C64E2AA"
			rewards: [{
				exclude_from_claim_all: true
				id: "3249BFEB689D3910"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "配方升级"
			tasks: [{
				id: "320F25520D6B4B61"
				item: "gtceu:multilayer_fiber_reinforced_circuit_board"
				type: "item"
			}]
			x: 4.5d
			y: 0.0d
		}
		{
			dependencies: ["2F4258088CBFC399"]
			description: [
				"现在&a湮灭反应堆&fMk1已运行,我们可以获取推进到下一阶段所需的稀有资源了!"
				""
				"比如铕元素——随着等级提升会频繁使用,它甚至是格雷之星(GregStar)的组成部分!"
			]
			id: "74510B6C9C16A628"
			rewards: [{
				id: "46606992F7508705"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:europium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:europium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			shape: "gear"
			size: 1.5d
			subtitle: "获取超稀有元素!"
			tasks: [{
				id: "565E8ACA3EBFC082"
				item: "gtceu:europium_bucket"
				type: "item"
			}]
			x: -1.0d
			y: 1.0d
		}
		{
			dependencies: ["35FF7974C6DD1D9F"]
			description: [
				"中子反射板对任何反应堆都至关重要.这里我们将使用铱制作&a中子反射板&f."
				""
				"这些反射板能使中子保持在反应区域内,维持链式反应."
			]
			id: "6DA0ABBC89711536"
			rewards: [{
				exclude_from_claim_all: true
				id: "5B65E946D64E0AC0"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "&a中子反射板&f？事态变得严肃了..."
			tasks: [{
				id: "7D941CD3F5CB3F94"
				item: "gtceu:neutron_reflector"
				type: "item"
			}]
			x: -1.2999999999999998d
			y: -1.1999999999999997d
		}
		{
			dependencies: ["3A8BF9BE08F54513"]
			description: [
				"&a恶魔&f核心是一件曾引发史上反应速度最快的核事故中心的物品.该核心由曼哈顿计划在二战期间制造,旨在用于研究核辐射与慢化效应."
				""
				"核心本体由钚制成,而包裹核心的两个半球体则由中子慢化剂——铍制成.铍对中子具有强吸引力,并能降低中子能量."
			]
			id: "35FF7974C6DD1D9F"
			rewards: [{
				count: 8
				id: "582AEC4B901EF28D"
				item: "gtceu:double_beryllium_plate"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "&a恶魔&f核心"
			tasks: [{
				id: "225DC73DA26D4318"
				item: "gtceu:double_beryllium_plate"
				type: "item"
			}]
			x: -0.5d
			y: -2.0d
		}
		{
			description: ["获取铍有几种途径.部分玩家可能已通过矿石加工积累了充足的&2铍&r库存.若尚未储备,现在正是开始的好时机!\\n\\n若没有矿石,也可通过电解末影珍珠粉获得."]
			id: "3A8BF9BE08F54513"
			rewards: [{
				count: 12
				id: "27D53CE56ECB2827"
				item: "gtceu:beryllium_dust"
				random_bonus: 12
				type: "item"
			}]
			subtitle: "「以格拉布萨之锤起誓...!」"
			tasks: [{
				id: "4F5F584CA7F7AB16"
				item: "gtceu:beryllium_dust"
				type: "item"
			}]
			x: -0.5d
			y: -3.0d
		}
		{
			description: [
				"硅岩在本阶段及后续阶段都将是至关重要的材料."
				""
				"从ZPM阶段开始,硅岩(简称Naq)会以多种形态广泛应用.了解硅岩起源后,这些关联就都说得通了."
			]
			id: "7D972F334DCE5626"
			rewards: [{
				count: 12
				id: "125C754C72B6AB6A"
				item: "gtceu:naquadah_dust"
				random_bonus: 12
				type: "item"
			}]
			subtitle: "必须增产硅岩!"
			tasks: [{
				id: "4F60C9A2E9323E94"
				item: "gtceu:naquadah_dust"
				type: "item"
			}]
			x: -12.0d
			y: -3.0d
		}
		{
			dependencies: ["2462AE2029F3C8E8"]
			description: [
				"现在我们有&a氟锑酸&f了.这正是我们硅岩产线所需的催化剂."
				""
				"请确保以可再生方式生产,我们需要持续维持硅岩产线运转."
			]
			id: "3C9E93426D6412E0"
			rewards: [{
				id: "27FCCE5E79FD8F97"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:fluoroantimonic_acid"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:fluoroantimonic_acid"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "液态转化"
			tasks: [{
				id: "78E198546E2EFDCC"
				item: "gtceu:fluoroantimonic_acid_bucket"
				type: "item"
			}]
			x: -13.0d
			y: -3.0d
		}
		{
			dependencies: [
				"3C9E93426D6412E0"
				"7D972F334DCE5626"
			]
			description: [
				"使用&a大型化学反应釜&f混合硅岩粉与&a氟锑酸&f可产出3种物质.我们需要的是不纯的&a富集硅岩溶液&f."
				""
				"可将三氟化钛放入电弧炉回收部分&a盐酸&f及&a钛锭&f."
				""
				"保留不纯&a硅岩溶液&f,后续将用于加工硅岩溶液/硅岩锭."
			]
			id: "0E73B4A4A1CC6B0F"
			rewards: [{
				id: "41BF54932E10D1DA"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:impure_enriched_naquadah_solution"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:impure_enriched_naquadah_solution"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "不纯但富集"
			tasks: [{
				id: "0FBFFB359759ECBC"
				item: "gtceu:impure_enriched_naquadah_solution_bucket"
				type: "item"
			}]
			x: -12.5d
			y: -2.0d
		}
		{
			dependencies: ["17148E654DD28A94"]
			description: [
				"将三氧化物转化为三氟化物."
				""
				"我们离硅岩产线所需催化剂更近一步."
			]
			id: "2462AE2029F3C8E8"
			rewards: [{
				count: 8
				id: "74A1B9514588B54C"
				item: "gtceu:antimony_trifluoride_dust"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "三氟化物"
			tasks: [{
				id: "0640CF3032CDC41C"
				item: "gtceu:antimony_trifluoride_dust"
				type: "item"
			}]
			x: -13.0d
			y: -4.0d
		}
		{
			description: [
				"硅岩产线需要&a三氧化二锑&f."
				""
				"我建议将锑处理环节全部整合在&d格雷科技&f体系中."
			]
			id: "17148E654DD28A94"
			rewards: [{
				count: 6
				id: "288ABA25C7CF459C"
				item: "gtceu:antimony_trioxide_dust"
				random_bonus: 12
				type: "item"
			}]
			subtitle: "反锑工程"
			tasks: [{
				id: "090C7C48D571C273"
				item: "gtceu:antimony_trioxide_dust"
				type: "item"
			}]
			x: -13.0d
			y: -5.0d
		}
		{
			dependencies: [
				"6206EE9A045553CC"
				"0E73B4A4A1CC6B0F"
			]
			description: [
				"硫化凯金是硅岩产线的副产品,但这个重要副产品将大量使用."
				""
				"如前所述,你应当建立自动化产线.公平地说,现阶段所有产线都应实现自动化."
			]
			id: "2D65DC315CCC60C8"
			rewards: [
				{
					count: 8
					id: "74F77DBF440B0C32"
					item: "gtceu:trinium_sulfide_dust"
					random_bonus: 8
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "7712C077085BAC34"
					table_id: 5732951907492768982L
					type: "loot"
				}
			]
			subtitle: "副产品"
			tasks: [{
				id: "5449CB95FD9E6F0E"
				item: "gtceu:trinium_sulfide_dust"
				type: "item"
			}]
			x: -12.5d
			y: -1.0d
		}
		{
			dependencies: [
				"2D65DC315CCC60C8"
				"238CC42F61006CE9"
			]
			description: ["将上个任务获得的硫化凯金与锌放入电弧炉,即可得到炽热的&a凯金锭&f.冷却后便是我们需要的凯金."]
			id: "7244FA69157727AF"
			rewards: [{
				count: 8
				id: "705E46130095E8CD"
				item: "gtceu:trinium_ingot"
				random_bonus: 8
				type: "item"
			}]
			shape: "pentagon"
			size: 1.25d
			subtitle: "熔炼凯金"
			tasks: [{
				id: "5177EC853F097069"
				item: "gtceu:trinium_ingot"
				type: "item"
			}]
			x: -10.5d
			y: -1.8000000000000003d
		}
		{
			description: ["使用至少&5EV级&r离心机从&2不纯&a富集硅岩溶液&f中分离&c硫化凯金&r."]
			id: "6206EE9A045553CC"
			rewards: [{
				id: "0365A018CECC6C4E"
				type: "xp"
				xp: 100
			}]
			subtitle: "提升转速"
			tasks: [{
				id: "2020B61D1758999F"
				type: "checkmark"
			}]
			title: "离心启动"
			x: -13.5d
			y: -1.0d
		}
		{
			dependencies: ["61BC5BBD1657D409"]
			description: [
				"现在能制造ZPM级外壳了,可以准备建造超强效机器."
				""
				"虽未达到顶级阶段,但你会注意到这些机器能以惊人速度处理前阶段工序!"
			]
			id: "7936FF3ED75DCA59"
			rewards: [
				{
					count: 2
					id: "76A3B129144684E7"
					item: "gtceu:zpm_machine_hull"
					random_bonus: 2
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "58362F2B01E043D8"
					table_id: 5732951907492768982L
					type: "loot"
				}
			]
			subtitle: "新阶段,&a新压缩空间机械&f!"
			tasks: [{
				id: "05267F2419255ADD"
				item: "gtceu:zpm_machine_hull"
				type: "item"
			}]
			x: -10.5d
			y: 1.5d
		}
		{
			dependencies: ["7E697FE6A6C8B4EE"]
			description: ["我们上次制作超导体已是许久之前.但现在每个级别的超导体都将变得更加重要,同时还能让我们实现零电流损耗的线缆传输!"]
			id: "3BD5C517AD024A45"
			rewards: [{
				count: 8
				id: "11777A53DC0E744E"
				item: "gtceu:indium_tin_barium_titanium_cuprate_single_wire"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "回忆"
			tasks: [{
				id: "093845766C9697B7"
				item: "gtceu:indium_tin_barium_titanium_cuprate_single_wire"
				type: "item"
			}]
			x: -3.5d
			y: -2.0d
		}
		{
			dependencies: ["57D12E37E980FDBB"]
			description: ["优化你的Naq生产线,因为你需要大量Naquadah板来制作ZPM和机械外壳"]
			id: "61BC5BBD1657D409"
			rewards: [{
				count: 6
				id: "66650E2532E3DD67"
				item: "gtceu:naquadah_alloy_plate"
				random_bonus: 6
				type: "item"
			}]
			subtitle: "Naq板材量产日"
			tasks: [{
				id: "4BA9F1DA1ADA2496"
				item: "gtceu:naquadah_alloy_plate"
				type: "item"
			}]
			x: -10.5d
			y: 0.5d
		}
		{
			dependencies: ["2F2B9938B63A7029"]
			description: ["我们正处于科技前沿,现在需要使用晶体芯片来制造新型处理器!!"]
			id: "5A3FF46A2B275049"
			rewards: [{
				count: 4
				id: "7509EBAF01F9CE8B"
				item: "gtceu:crystal_cpu"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "闪亮晶体"
			tasks: [{
				id: "1B6CE8D8008B8887"
				item: "gtceu:crystal_cpu"
				type: "item"
			}]
			x: 3.0d
			y: 3.0d
		}
		{
			dependencies: ["02C726A758C76630"]
			description: ["获得原始晶体芯片后,用绿宝石板在EBF中加工,就能得到晶体CPU!"]
			id: "2F2B9938B63A7029"
			rewards: [{
				count: 6
				id: "2BFE1936B5A5C790"
				item: "gtceu:engraved_crystal_chip"
				random_bonus: 6
				type: "item"
			}]
			subtitle: "加热时刻"
			tasks: [{
				id: "718A7339E4C71E22"
				item: "gtceu:engraved_crystal_chip"
				type: "item"
			}]
			x: 3.0d
			y: 2.0d
		}
		{
			dependencies: ["54118BE76738EB6D"]
			description: [
				"首次获取晶体芯片原料可能需要多次尝试."
				""
				"但一旦获得,就能通过简单循环可靠地复制:将晶体芯片原料用锻造锤加工成部件,再处理成更多晶体芯片原料,如此循环."
			]
			id: "3B0BC233A6C81B73"
			rewards: [{
				count: 2
				id: "35DAC4E38666F727"
				item: "gtceu:raw_crystal_chip"
				random_bonus: 2
				type: "item"
			}]
			subtitle: "低概率"
			tasks: [{
				id: "338730546AC68CBA"
				item: "gtceu:raw_crystal_chip"
				type: "item"
			}]
			x: 1.0d
			y: 2.5d
		}
		{
			dependencies: ["3B0BC233A6C81B73"]
			description: ["开始循环生产吧!我们需要铕元素来稳定生产更多晶体芯片原料."]
			id: "7A22E94DD83B12AE"
			rewards: [
				{
					count: 6
					id: "7B2F6BC77F293A7D"
					item: "gtceu:raw_crystal_chip_parts"
					random_bonus: 6
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "5DD9224137BACB90"
					table_id: 5732951907492768982L
					type: "loot"
				}
			]
			subtitle: "今天是土拨鼠日吗？我在循环"
			tasks: [{
				id: "74EC90490A985995"
				item: "gtceu:raw_crystal_chip_parts"
				type: "item"
			}]
			x: 2.0d
			y: 2.5d
		}
		{
			description: ["准备十几颗这种精致绿宝石,它们将在后续步骤中发挥巨大作用."]
			id: "54118BE76738EB6D"
			rewards: [{
				count: 4
				id: "43FB15EF0B3AF968"
				item: "gtceu:exquisite_emerald_gem"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "可谓精致？"
			tasks: [{
				id: "7DB2AC2D17860702"
				item: "gtceu:exquisite_emerald_gem"
				type: "item"
			}]
			x: 0.0d
			y: 2.5d
		}
		{
			dependencies: [
				"49262C7C4E9EF712"
				"664C2C33C22D372E"
			]
			description: ["这是制作下一级能量舱口的必要组件!"]
			id: "574E65B7954A13D0"
			rewards: [{
				exclude_from_claim_all: true
				id: "5B0BBB0A6E9936D3"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "零点突破!"
			tasks: [{
				id: "6A14169A3E8ADE2B"
				item: "gtceu:zpm_voltage_coil"
				type: "item"
			}]
			x: -7.0d
			y: 2.0d
		}
		{
			dependencies: ["7244FA69157727AF"]
			description: [
				"硅岩合金对制作多方块结构和机器部件至关重要."
				""
				"我们需要大量Naq合金来制造旋转炉床所需的所有Naq合金框架."
			]
			id: "57D12E37E980FDBB"
			rewards: [{
				count: 8
				id: "6243FFE5B0489563"
				item: "gtceu:naquadah_alloy_dust"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "这就是你要找的Naq合金"
			tasks: [{
				id: "1984C87F86501555"
				item: "gtceu:naquadah_alloy_dust"
				type: "item"
			}]
			x: -10.5d
			y: -0.5d
		}
		{
			dependencies: ["603ABA13788E2216"]
			description: ["终于有能力制作下一级电压线圈用于能量舱口,以及ZPM级外壳所需的ZPM马达!"]
			id: "49262C7C4E9EF712"
			rewards: [{
				count: 16
				id: "662F4646FD708262"
				item: "gtceu:fine_europium_wire"
				random_bonus: 32
				type: "item"
			}]
			subtitle: "超级稀有细线"
			tasks: [{
				id: "4731362FCD25A516"
				item: "gtceu:fine_europium_wire"
				type: "item"
			}]
			x: -4.5d
			y: 2.5d
		}
		{
			dependencies: ["74510B6C9C16A628"]
			description: ["现在我们有了固态铕,可以进一步加工成机器和多方块结构的组件!"]
			id: "603ABA13788E2216"
			rewards: [{
				count: 6
				id: "361E56D1AA154B38"
				item: "gtceu:europium_ingot"
				random_bonus: 12
				type: "item"
			}]
			subtitle: "最终倒计时!"
			tasks: [{
				id: "3F8CA5C8FDF15BCB"
				item: "gtceu:europium_ingot"
				type: "item"
			}]
			x: -1.0d
			y: 2.5000000000000004d
		}
		{
			dependencies: ["278252472B5B94D4"]
			description: [
				"这些是活性变压器的管道."
				""
				"再次强调,它们必须染色才能工作,且不能转弯.需要更多活性变压器来改变激光方向."
			]
			id: "7F2D4C5BEFE3DDD3"
			optional: true
			rewards: [{
				count: 16
				id: "6676F747CC517749"
				item: "gtceu:normal_laser_pipe"
				random_bonus: 16
				type: "item"
			}]
			subtitle: "又是管道"
			tasks: [{
				id: "1251249FA97FD527"
				item: "gtceu:normal_laser_pipe"
				type: "item"
			}]
			x: -8.0d
			y: -2.0d
		}
		{
			dependencies: ["7F2D4C5BEFE3DDD3"]
			description: [
				"如果觉得256安培很大,请三思.至少在活性变压器面前."
				""
				"这是电流量最低级的激光源舱口,最高可达4096A舱口.这才叫电力!"
			]
			id: "1194FF35ADAA9957"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "6AD4747FBF3B08E8"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "激光鲨鱼？"
			tasks: [{
				id: "630A6BAE13801D9A"
				item: "gtceu:zpm_256a_laser_source_hatch"
				type: "item"
			}]
			x: -8.0d
			y: -3.0d
		}
		{
			dependencies: ["7F2D4C5BEFE3DDD3"]
			description: [
				"这就是为什么活性变压器是GT架构中最优秀的EU传输方式."
				""
				"其传输超大电流的能力无与伦比."
			]
			id: "72FA90C181139957"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "4AA38C20471912F2"
				table_id: 7041264405549027492L
				type: "loot"
			}]
			subtitle: "这才叫电力!"
			tasks: [{
				id: "5E8AFBD479A7F707"
				item: "gtceu:luv_256a_laser_source_hatch"
				type: "item"
			}]
			x: -9.0d
			y: -2.0d
		}
		{
			dependencies: [
				"238CC42F61006CE9"
				"29706E3681616E41"
			]
			description: ["这些LuV级超导体锭将用于制作超导线材和细线."]
			id: "7E697FE6A6C8B4EE"
			rewards: [{
				count: 6
				id: "46860F5306D9FCE3"
				item: "gtceu:indium_tin_barium_titanium_cuprate_ingot"
				random_bonus: 6
				type: "item"
			}]
			subtitle: "锭块在手选择我有"
			tasks: [{
				id: "2DF19D9748DD24BB"
				item: "gtceu:indium_tin_barium_titanium_cuprate_ingot"
				type: "item"
			}]
			x: -3.5d
			y: -3.0d
		}
		{
			description: [
				"在电流损耗方面,LuV级导线的选择并不理想."
				""
				"因此推荐使用超导电缆,它无需橡胶包裹且零电流损耗."
			]
			id: "29706E3681616E41"
			rewards: [{
				count: 4
				id: "02851C5158880EF1"
				item: "gtceu:indium_tin_barium_titanium_cuprate_dust"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "真是五花八门的粉尘啊!"
			tasks: [{
				id: "01F6E99509DFC141"
				item: "gtceu:indium_tin_barium_titanium_cuprate_dust"
				type: "item"
			}]
			x: -3.5d
			y: -4.0d
		}
		{
			dependencies: ["2036ED4A823C1456"]
			description: ["现在我们可以制作超导体、&a硅岩合金&f、镥锭和铕锭了!"]
			id: "238CC42F61006CE9"
			rewards: [{
				exclude_from_claim_all: true
				id: "61D0294B38B0E710"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "我们需要更多热量!"
			tasks: [{
				count: 16L
				id: "78F54B8FDB45EA7D"
				item: "gtceu:naquadah_coil_block"
				type: "item"
			}]
			x: -9.0d
			y: -4.0d
		}
		{
			dependencies: ["7D972F334DCE5626"]
			description: [
				"我们需要更多——大量的更多Naquadah矿."
				""
				"至少现在可以启动Naquadah线圈,帮助电爆破炉处理更多金属了!"
			]
			id: "2036ED4A823C1456"
			rewards: [{
				count: 12
				id: "050633E74A894C31"
				item: "gtceu:naquadah_ingot"
				random_bonus: 24
				type: "item"
			}]
			subtitle: "我们需要更多Naq矿"
			tasks: [{
				id: "6DE235DDD37EA552"
				item: "gtceu:naquadah_ingot"
				type: "item"
			}]
			x: -11.0d
			y: -4.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"此任务默认隐藏,若你看到此提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "31F4481E80FD8907"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "6C6FC09CAD77E251"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "11C667F5FB2FB386"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -14.5d
			y: 4.0d
		}
		{
			dependencies: [
				"7852B54DBBFB8CDC"
				"6AFA62A63FC44CCF"
				"615C8496F7CCD9A4"
			]
			description: [
				"&eHPCA&r通过向&e研究站&r提供&d计算工作单元&r(CWU)来进行研究.\\n\\n"
				"若未妥善&b冷却&r,HPCA组件会&c过热&r.过热后组件将报废,务必避免这种情况!\\n\\n"
				"该多方块结构可通过组件高度定制.当前任务要求的16 CWU/t已足够UV级电路需求,后续章节我们会进一步升级!\\n\\n"
				"用光纤电缆连接HPCA与研究&a空间站&f的&a计算数据舱口&r."
			]
			icon: "gtceu:high_performance_computation_array"
			id: "586266B2096D27A7"
			rewards: [{
				exclude_from_claim_all: true
				id: "22F0029DA71D0DEA"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "这才是超级计算机"
			tasks: [
				{
					id: "2249BAA5F2562F86"
					item: "gtceu:high_performance_computation_array"
					type: "item"
				}
				{
					count: 4L
					id: "3E134A4F5574CA02"
					item: "gtceu:hpca_computation_component"
					type: "item"
				}
				{
					count: 4L
					id: "40F709808938B91C"
					item: { Count: 4, id: "gtceu:hpca_active_cooler_component" }
					type: "item"
				}
				{
					id: "4BE344E5A6537C8D"
					item: "gtceu:lv_input_hatch"
					type: "item"
				}
				{
					count: 7L
					id: "59DE9EA2736F99F9"
					item: "gtceu:computer_casing"
					type: "item"
				}
				{
					count: 13L
					id: "6CF9D5B65398BAF9"
					item: "gtceu:advanced_computer_casing"
					type: "item"
				}
				{
					count: 2L
					id: "7E5B703013212E71"
					item: { Count: 2, id: "gtceu:luv_energy_input_hatch" }
					type: "item"
				}
				{
					id: "643012B95334FF0C"
					item: "gtceu:computation_transmitter_hatch"
					type: "item"
				}
				{
					count: 15L
					id: "142A86055AF34D27"
					item: { Count: 15, id: "gtceu:computer_heat_vent" }
					type: "item"
				}
				{
					id: "62701679DC0E9913"
					item: "gtceu:hpca_empty_component"
					type: "item"
				}
				{
					id: "1E09B5C26F87D124"
					item: "gtceu:auto_maintenance_hatch"
					type: "item"
				}
			]
			x: 8.5d
			y: -2.0d
		}
		{
			description: ["当指令量超出单个&a闪存&r容量时,就需要用到&d数据球&r了!"]
			id: "38686579F22BDBC5"
			rewards: [{
				count: 2
				id: "794F0866A8AA3E5D"
				item: {
					Count: 1
					id: "gtceu:data_orb"
					tag: { }
				}
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "13437F166310CCA6"
				item: {
					Count: 1
					id: "gtceu:data_orb"
					tag: { }
				}
				type: "item"
			}]
			x: 7.5d
			y: -3.0d
		}
		{
			dependencies: ["3518ACB5BA656974"]
			description: ["此前我们很少用到场发生器,但从现在开始它们会频繁登场!"]
			id: "551CD9E0FA2E62A7"
			rewards: [{
				count: 4
				id: "017345C325D6B7BB"
				item: "gtceu:quantum_star"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "203B8812E85B89E3"
				item: "gtceu:iv_field_generator"
				type: "item"
			}]
			x: -2.0d
			y: -2.0d
		}
		{
			dependencies: ["551CD9E0FA2E62A7"]
			description: [
				"制作下一套研究系统需要这个组件!\\n\\n"
				"我说过,场发生器在后续配方中会越来越常见."
			]
			hide_dependency_lines: true
			id: "6AFA62A63FC44CCF"
			rewards: [{
				exclude_from_claim_all: true
				id: "3019DE7F2FE5EABC"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			tasks: [{
				id: "47F4041DFE6764E1"
				item: "gtceu:luv_field_generator"
				type: "item"
			}]
			x: 8.5d
			y: -1.0d
		}
		{
			dependencies: ["402FE874C854840C"]
			description: ["因剧毒性,这种物质在多数国家被禁用.但作为优质冷却剂,我们仍将在此使用!"]
			id: "615C8496F7CCD9A4"
			rewards: [{
				id: "4B2327EEB38B2C67"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:pcb_coolant"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:pcb_coolant"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "6642A8D5A1868059"
				item: "gtceu:pcb_coolant_bucket"
				type: "item"
			}]
			x: 8.5d
			y: -3.0d
		}
		{
			dependencies: ["42F7E5E834C63270"]
			description: ["只需添加&3氯气&r!"]
			id: "402FE874C854840C"
			rewards: [{
				id: "296F69D19F7E545E"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:polychlorinated_biphenyl"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:polychlorinated_biphenyl"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "70A00D9EFF328AE1"
				item: "gtceu:polychlorinated_biphenyl_bucket"
				type: "item"
			}]
			x: 8.5d
			y: -4.0d
		}
		{
			description: ["希望你的石油加工没落下进度!"]
			id: "42F7E5E834C63270"
			rewards: [{
				count: 8
				id: "7BBA327AF8876E1A"
				item: "gtceu:biphenyl_dust"
				random_bonus: 8
				type: "item"
			}]
			tasks: [{
				id: "707665E01725F18B"
				item: "gtceu:biphenyl_dust"
				type: "item"
			}]
			x: 6.5d
			y: -4.0d
		}
		{
			description: ["&d硼硅玻璃&r所需的&d硼&r可通过&e矿石处理&r直接加工&a锂云母&r获得,或通过电解&a盐&r与&a岩盐&r的副产品&a硼砂&r获取.无论如何,现在是囤积硼元素的时候了!"]
			id: "0BD28AAC069C14F4"
			rewards: [{
				count: 8
				id: "5A33FA0E55EA4841"
				item: "gtceu:normal_optical_pipe"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "高速运转"
			tasks: [{
				id: "649F1D3655ED615D"
				item: "gtceu:normal_optical_pipe"
				type: "item"
			}]
			x: 6.5d
			y: -3.0d
		}
		{
			dependencies: [
				"0BD28AAC069C14F4"
				"38686579F22BDBC5"
			]
			description: [
				"&b组装线&r上的&b数据存取舱&r空间不足了？厌倦了手动更换数据棒来运行配方？\\n\\n"
				"这个多方块机器能解决你的烦恼!它可配备多个数据存取舱,并通过&c光学&a数据传输&f舱口&r将所有数据传送至组装线的&2光学数据接收舱口&r.\\n\\n"
				"另外制作后续两个多方块结构还需要&d档案印制机&r作为材料."
			]
			id: "7852B54DBBFB8CDC"
			rewards: [{
				exclude_from_claim_all: true
				id: "0E29F26E227742B7"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			tasks: [{
				id: "66DA11873B439A7D"
				item: "gtceu:data_bank"
				type: "item"
			}]
			x: 6.5d
			y: -2.0d
		}
		{
			dependencies: ["2036ED4A823C1456"]
			description: ["终于,我们可以通过这个&e激光蚀刻机&r提高晶圆产量了!切割这些晶圆时甚至能获得更多基础晶圆!"]
			id: "6F82909CD6FFF606"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "64ABF923E9E3FE91"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			tasks: [{
				id: "7A0FCCBF1235257A"
				item: "gtceu:naquadah_boule"
				type: "item"
			}]
			x: -10.0d
			y: -5.0d
		}
		{
			description: ["这其实是&1IV型超导体&r,我们早就能制造了.之前需求不大,但现在可派上大用场了!"]
			id: "3518ACB5BA656974"
			rewards: [
				{
					count: 8
					id: "0BADF6933743ADE7"
					item: "gtceu:samarium_iron_arsenic_oxide_single_wire"
					random_bonus: 8
					type: "item"
				}
				{
					count: 6
					id: "6AB0F6EFB1688586"
					item: "gtceu:samarium_iron_arsenic_oxide_ingot"
					random_bonus: 6
					type: "item"
				}
				{
					count: 8
					id: "763AA5E0FD8B0659"
					item: "gtceu:samarium_iron_arsenic_oxide_dust"
					random_bonus: 8
					type: "item"
				}
			]
			tasks: [{
				id: "5725DF39AE448157"
				item: "gtceu:samarium_iron_arsenic_oxide_single_wire"
				type: "item"
			}]
			x: -2.0d
			y: -3.5d
		}
		{
			dependencies: ["7936FF3ED75DCA59"]
			description: ["你需要先制造&e聚变MK1反应堆&r并获取珍贵的&b铕元素&r,才能制作这台机器."]
			id: "664C2C33C22D372E"
			rewards: [{
				exclude_from_claim_all: true
				id: "71FD8E46A6C65220"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "&a近在咫尺&f却又遥不可及"
			tasks: [{
				id: "2E07D7A9B8F8F5AE"
				item: "gtceu:zpm_assembler"
				type: "item"
			}]
			x: -9.0d
			y: 1.5d
		}
		{
			dependencies: [
				"7852B54DBBFB8CDC"
				"6AFA62A63FC44CCF"
				"586266B2096D27A7"
			]
			description: ["终于可以告别那些单方块结构的&e扫描仪&r,升级到正经的&d研究站&r工作了."]
			icon: "gtceu:research_station"
			id: "18DF12E242210030"
			rewards: [{
				exclude_from_claim_all: true
				id: "644BCB192181CBCA"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "快穿上实验服戴上护目镜"
			tasks: [
				{
					id: "32C523676A4C2F59"
					item: "gtceu:research_station"
					type: "item"
				}
				{
					id: "6B607B6BA2661445"
					item: "gtceu:object_holder"
					type: "item"
				}
				{
					id: "7DBD65C55C67DE25"
					item: "gtceu:computation_receiver_hatch"
					type: "item"
				}
				{
					id: "523CA422CE9E6E6D"
					item: "gtceu:auto_maintenance_hatch"
					type: "item"
				}
				{
					count: 2L
					id: "0F61B77B02EE6BEE"
					item: { Count: 2, id: "gtceu:luv_energy_input_hatch" }
					type: "item"
				}
				{
					count: 57L
					id: "21DC67FA60EBA0AB"
					item: { Count: 57, id: "gtceu:computer_casing" }
					type: "item"
				}
				{
					count: 23L
					id: "6E0ED8B253085727"
					item: { Count: 23, id: "gtceu:advanced_computer_casing" }
					type: "item"
				}
				{
					count: 14L
					id: "42BA5C0B038B2069"
					item: { Count: 14, id: "gtceu:computer_heat_vent" }
					type: "item"
				}
			]
			x: 7.5d
			y: -1.0d
		}
		{
			dependencies: ["2F4258088CBFC399"]
			description: ["MK1型&a湮灭反应堆&f还能用于制造铬、镥、钚、&a铀-235&f、耐钢、铀、锇以及&a氦等离子体&f.\\n\\n我们很快就会重新用到耐钢和镥元素!"]
			id: "47683246D430D937"
			rewards: [{
				id: "5171A341666266CC"
				type: "xp"
				xp: 1000
			}]
			tasks: [{
				id: "60236232AD664FC4"
				type: "checkmark"
			}]
			title: "其他MK1产物"
			x: -3.0d
			y: 1.0d
		}
		{
			dependencies: [
				"7A22E94DD83B12AE"
				"74510B6C9C16A628"
			]
			description: ["说真的,后续会大量用到这些材料,务必保持晶体芯片和芯片零件的库存充足."]
			id: "02C726A758C76630"
			rewards: [
				{
					count: 6
					id: "789DAAB0B1E6ABCF"
					item: "gtceu:raw_crystal_chip"
					random_bonus: 6
					type: "item"
				}
				{
					count: 6
					id: "6179BE7017BFA471"
					item: "gtceu:raw_crystal_chip_parts"
					random_bonus: 6
					type: "item"
				}
			]
			subtitle: "千万别断货"
			tasks: [{
				count: 9L
				id: "457AD9D105D6979E"
				item: { Count: 9, id: "gtceu:raw_crystal_chip" }
				type: "item"
			}]
			x: 2.0d
			y: 1.0d
		}
	]
	title: "零点能源模块"
}
