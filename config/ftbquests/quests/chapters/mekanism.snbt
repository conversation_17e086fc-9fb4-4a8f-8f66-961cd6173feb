{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "mekanism"
	group: "2B51AC12041E3F89"
	icon: "mekanism:steel_casing"
	id: "23983F4DC524B14B"
	images: [{
		height: 2.0d
		image: "ftbquests:block/barrier_open"
		rotation: 45.0d
		width: 2.0d
		x: 9.0d
		y: -5.5d
	}]
	order_index: 7
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["0650996C7818ADB5"]
			description: [
				"&a冶金灌注机&f用于制作&d通用机械&f中的核心合成组件."
				""
				"这台机器的工作原理是将材料(中间左侧槽位)与\"灌注物\"(最左侧槽位)进行融合."
				""
				"这也是你获取&a钢锭&f的方式."
			]
			id: "162CE44400A63575"
			rewards: [
				{
					count: 2
					id: "52FEA6D9C1258DA8"
					item: "minecraft:redstone"
					random_bonus: 2
					type: "item"
				}
				{
					id: "59927C95618B2AB5"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "52912755CBA30A4D"
					type: "xp"
					xp: 10
				}
			]
			shape: "gear"
			size: 1.5d
			subtitle: "初始机器"
			tasks: [{
				id: "28277BA9F319240D"
				item: "mekanism:metallurgic_infuser"
				type: "item"
			}]
			x: -7.0d
			y: -2.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"这台机器的工作原理类似于研磨机或粉碎机,可以将矿石分解成粉末."
				""
				"它能将3个原矿分解为4份矿粉."
				""
				"这是你矿石加工厂的起点.它还可以升级为工厂机器,增加使用槽位."
			]
			id: "08DDE018A804BFE7"
			rewards: [
				{
					count: 3
					id: "4DF8BB733E3987AA"
					item: "minecraft:raw_iron"
					type: "item"
				}
				{
					id: "58769DAE976C11FA"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0C4A930404601471"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "将&a粗矿&f分解成矿粉"
			tasks: [{
				id: "1AE233B4CCFE99F7"
				item: "mekanism:enrichment_chamber"
				type: "item"
			}]
			x: -4.0d
			y: -2.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"这台机器能制造两种非常坚固的锭:&a强化荧石&f和&a强化黑曜石&f."
				""
				"它通过将物品与锇融合来制造更强大的金属锭."
			]
			id: "195729280394ABFB"
			rewards: [
				{
					id: "7ECD36EFDEC929A1"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "268A8495184348CE"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "基本上只适合做两件事"
			tasks: [{
				id: "6934C61AFEB0443D"
				item: "mekanism:osmium_compressor"
				type: "item"
			}]
			x: -8.0d
			y: -0.5d
		}
		{
			dependencies: ["6C25D42C36175BF4"]
			description: ["这台机器将矿石粉碎成它们的\"脏矿粉\"形态.这对于将矿团转化为脏矿粉很有用,然后可以通过&a富集仓&f处理得到纯净矿粉,最终熔炼成金属锭."]
			id: "7AE502EDB73BD57A"
			rewards: [
				{
					count: 4
					id: "62B555EE0C1159EA"
					item: "mekanism:dirty_dust_iron"
					type: "item"
				}
				{
					id: "282F549823B95E69"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0E0B58D60884D6AD"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "粉碎专家"
			tasks: [{
				id: "431D7329CFCAF880"
				item: "mekanism:crusher"
				type: "item"
			}]
			x: 1.0d
			y: -2.0d
		}
		{
			dependencies: ["4BDE773C3359D584"]
			description: [
				"这台机器可以替你采矿!"
				""
				"它完全可配置,甚至可以用圆石或你提供的任何方块替换被开采的区块!"
			]
			id: "7EA6B942D1294ED6"
			rewards: [
				{
					id: "24EF84E5344DB715"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "42A4D32A98E77574"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "6901B097A0EE1749"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "一个更大的机器人伙伴"
			tasks: [{
				id: "36FE95268A543F7C"
				item: "mekanism:digital_miner"
				type: "item"
			}]
			x: -7.75d
			y: 3.75d
		}
		{
			dependencies: ["58B125BD4876054C"]
			description: [
				"这个物品用于储存液体."
				""
				"它具有桶模式,可以切换用来舀取液体.在游戏初期收集岩浆时特别有用!"
			]
			hide_dependency_lines: true
			id: "6DB1AAAD926486BC"
			rewards: [
				{
					id: "2DF1E2F8B6BF2589"
					item: "minecraft:bucket"
					type: "item"
				}
				{
					id: "13F85F8598002FFE"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "它有桶模式!"
			tasks: [{
				id: "54BEF9EA9F1D4374"
				item: "mekanism:basic_fluid_tank"
				type: "item"
			}]
			x: 3.5d
			y: 7.0d
		}
		{
			dependencies: [
				"1FC7E9DBF92BE6AA"
				"4204702AA6FBF40B"
			]
			hide_dependency_lines: false
			id: "4BDE773C3359D584"
			progression_mode: "linear"
			rewards: [
				{
					id: "3405FDFD99DF7D9E"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "733BA4015C409DBE"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "陪伴你旅行的迷你机器人"
			tasks: [{
				id: "171E6682D1578C63"
				item: "mekanism:robit"
				type: "item"
			}]
			x: -7.75d
			y: 2.5d
		}
		{
			dependencies: ["162CE44400A63575"]
			hide_dependency_lines: true
			id: "37D4E5ACB35D8BF1"
			progression_mode: "linear"
			rewards: [
				{
					id: "2556E1661DDDC8CA"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "1D7E982934705158"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "需要氢气才能运作!"
			tasks: [{
				id: "71E922FC5BBF154C"
				item: "mekanism:jetpack"
				type: "item"
			}]
			x: -6.5d
			y: 3.75d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: ["此物品可将基础机器升级为基本工厂级机器."]
			hide_dependency_lines: true
			id: "07AD45DCF9EE3C2E"
			rewards: [
				{
					id: "7E9D506DF3FE5B8C"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "7A154DCDEA611E27"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "升级至工厂"
			tasks: [{
				id: "28A769E7676E358F"
				item: "mekanism:basic_tier_installer"
				type: "item"
			}]
			x: -0.5d
			y: 7.0d
		}
		{
			dependencies: ["07AD45DCF9EE3C2E"]
			hide_dependency_lines: true
			id: "493FAE3A6088518E"
			rewards: [
				{
					id: "0EF4D25B2F271C43"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "49D731775D72A88D"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "646785A06E01D173"
				item: "mekanism:advanced_tier_installer"
				type: "item"
			}]
			x: 0.0d
			y: 7.75d
		}
		{
			dependencies: ["493FAE3A6088518E"]
			id: "3E30EC9CA875A1F9"
			rewards: [
				{
					id: "654AEFEEC17DCABC"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "34A7A08FE327ABAD"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3AA211725EA97D18"
				item: "mekanism:elite_tier_installer"
				type: "item"
			}]
			x: -0.5d
			y: 8.25d
		}
		{
			dependencies: ["3E30EC9CA875A1F9"]
			id: "220C38510116BF36"
			rewards: [
				{
					id: "2BAB3B39820EF572"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "7191632FDBC16F4D"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "56A8A51990C7F0A1"
				item: "mekanism:ultimate_tier_installer"
				type: "item"
			}]
			x: -1.0d
			y: 7.75d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"此物品可实现无线传输功能"
				""
				"可自定义频道名称,传输任意指定内容"
			]
			hide_dependency_lines: true
			id: "7CC49360D07086B8"
			progression_mode: "linear"
			rewards: [
				{
					id: "18DB85CED00A8626"
					item: "mekanism:teleportation_core"
					type: "item"
				}
				{
					id: "623AC526E793CF87"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "5EA61C4244976347"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "无线传输能源/气体/流体/万物"
			tasks: [{
				id: "0526878F9506FB48"
				item: "mekanism:quantum_entangloporter"
				type: "item"
			}]
			x: -7.75d
			y: 5.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: ["该方块用于存储能源,也可为物品充电"]
			hide_dependency_lines: true
			id: "09408C6DCAC90318"
			rewards: [
				{
					id: "002E07C16DFB3445"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0CD8FBAF40FDD1E5"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "能源存储"
			tasks: [{
				id: "19C3A9D5E656FAA5"
				item: "mekanism:basic_energy_cube"
				type: "item"
			}]
			x: -2.5000000000000004d
			y: 7.0d
		}
		{
			dependencies: ["09408C6DCAC90318"]
			hide_dependency_lines: true
			id: "10909A87C1953F7C"
			rewards: [
				{
					id: "4FA5186D1822B6A4"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "24AB492929A83BB7"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3939A98E3276095B"
				item: "mekanism:advanced_energy_cube"
				type: "item"
			}]
			x: -2.0d
			y: 7.800000000000001d
		}
		{
			dependencies: ["10909A87C1953F7C"]
			id: "1DA058C68CF437DC"
			rewards: [
				{
					id: "75FE23DFE2FA6B3B"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "7C97CFC3809180E9"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "0485F28A3916A974"
				item: "mekanism:elite_energy_cube"
				type: "item"
			}]
			x: -2.5000000000000004d
			y: 8.3d
		}
		{
			dependencies: ["1DA058C68CF437DC"]
			id: "7AF982B6D8FD6C03"
			rewards: [
				{
					id: "032D64DAE54703D1"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "375F0416298634FC"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "50A250703B09FA5A"
				item: "mekanism:ultimate_energy_cube"
				type: "item"
			}]
			x: -3.0000000000000004d
			y: 7.800000000000001d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: ["在&a冶金灌注机&f中将铁与红石混合可获得此物品."]
			hide_dependency_lines: true
			id: "166971866A9234C7"
			rewards: [
				{
					id: "553CF82B4CDE5085"
					item: "minecraft:redstone"
					type: "item"
				}
				{
					id: "7A2076948F93E6D6"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			subtitle: "合成物品的基础合金"
			tasks: [{
				id: "72CE838EFDDE6479"
				item: "mekanism:alloy_infused"
				type: "item"
			}]
			x: -7.5d
			y: -3.5d
		}
		{
			dependencies: ["166971866A9234C7"]
			id: "7940E814260C556F"
			rewards: [
				{
					id: "5FA097D4BF2A2654"
					item: "mekanism:enriched_diamond"
					type: "item"
				}
				{
					id: "73EF7D1E94F0925B"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "2FE06FDA0A3A66C7"
				item: "mekanism:alloy_reinforced"
				type: "item"
			}]
			x: -7.5d
			y: -4.5d
		}
		{
			dependencies: ["7940E814260C556F"]
			id: "019D5A05A2134C7E"
			rewards: [
				{
					id: "6C480517028750E3"
					item: "mekanism:dust_refined_obsidian"
					type: "item"
				}
				{
					id: "1E437DC12A456C83"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "0DD9B2119BB50C80"
				item: "mekanism:alloy_atomic"
				type: "item"
			}]
			x: -7.5d
			y: -5.5d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: ["在&a冶金灌注机&f中将锇与红石混合可获得此物品."]
			hide_dependency_lines: true
			id: "0498A578D0EC3254"
			rewards: [
				{
					id: "44A42C932205CED3"
					item: "minecraft:redstone"
					type: "item"
				}
				{
					id: "36DA06D55E5BB49C"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			subtitle: "基础&a电路板&f"
			tasks: [{
				id: "5E51F5B17F77573E"
				item: "mekanism:basic_control_circuit"
				type: "item"
			}]
			x: -6.5d
			y: -3.5d
		}
		{
			dependencies: [
				"0498A578D0EC3254"
				"166971866A9234C7"
			]
			id: "6D7CABCFB50D8B0D"
			rewards: [
				{
					id: "29E77041082DD2E9"
					item: "mekanism:enriched_redstone"
					type: "item"
				}
				{
					id: "4C4A490E290BB38C"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "05F2EEE4DE21E2ED"
				item: "mekanism:advanced_control_circuit"
				type: "item"
			}]
			x: -6.5d
			y: -4.5d
		}
		{
			dependencies: [
				"6D7CABCFB50D8B0D"
				"7940E814260C556F"
			]
			id: "347C16F0F7CFAACF"
			rewards: [
				{
					id: "5A285211E7D37512"
					item: "mekanism:alloy_reinforced"
					type: "item"
				}
				{
					id: "117AA7D11A587024"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "48AC2274973C4509"
				item: "mekanism:elite_control_circuit"
				type: "item"
			}]
			x: -6.5d
			y: -5.5d
		}
		{
			dependencies: [
				"347C16F0F7CFAACF"
				"019D5A05A2134C7E"
			]
			id: "4AA150A009E904DA"
			rewards: [
				{
					id: "2EAE017B367B43AF"
					item: "mekanism:alloy_atomic"
					type: "item"
				}
				{
					id: "3C70B4A824D438D9"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "7E9FCA38BA758B41"
				item: "mekanism:ultimate_control_circuit"
				type: "item"
			}]
			x: -7.0d
			y: -6.5d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"在&a冶金灌注机&f中将铁与煤炭/木炭混合可得&a富集铁&f"
				""
				"将&a富集铁&f与更多煤炭/木炭混合可得&a钢粉&f,可熔炼成钢锭"
				""
				"这是&d通用机械&f众多配方中的重要合成材料"
			]
			hide_dependency_lines: true
			id: "1B6DDF50D00CBB31"
			rewards: [
				{
					id: "2B4CF26B63BD01A1"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "62B5B4DCE3CD982F"
					type: "xp"
					xp: 10
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				icon: "alltheores:steel_ingot"
				id: "6B88F1F9DF50C1E4"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:ingots/steel"
					}
				}
				title: "任意#forge:钢锭"
				type: "item"
			}]
			title: "钢"
			x: 0.5d
			y: 2.0d
		}
		{
			dependencies: ["1B6DDF50D00CBB31"]
			description: [""]
			id: "23F165DEAD225B10"
			rewards: [
				{
					count: 4
					id: "33E6BC2DA88E92EE"
					item: "mekanism:basic_universal_cable"
					type: "item"
				}
				{
					id: "65D3C338D866F6C0"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "能源传输专用"
			tasks: [{
				id: "56F8A49135418FD8"
				item: "mekanism:basic_universal_cable"
				type: "item"
			}]
			x: -3.5d
			y: 4.0d
		}
		{
			dependencies: ["23F165DEAD225B10"]
			hide_dependency_lines: true
			id: "618ECDB3FB534A8A"
			rewards: [
				{
					id: "29B9E7D9B73A99F0"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0FEFDBFB0971C811"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "445F3D8AA18A518A"
				item: "mekanism:advanced_universal_cable"
				type: "item"
			}]
			x: -3.0d
			y: 5.0d
		}
		{
			dependencies: ["618ECDB3FB534A8A"]
			id: "7553689BC9202E14"
			rewards: [
				{
					id: "42EDDE2A027669CF"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "71DC1FAB9D749E6D"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "6EC6D1463C65EBF2"
				item: "mekanism:elite_universal_cable"
				type: "item"
			}]
			x: -3.5d
			y: 5.5d
		}
		{
			dependencies: ["7553689BC9202E14"]
			id: "32E093F004E8CAC6"
			rewards: [
				{
					id: "1DA3EC185CA8BE9F"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "4992789167D656CD"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "1ECDAF49CF1BC952"
				item: "mekanism:ultimate_universal_cable"
				type: "item"
			}]
			x: -4.0d
			y: 5.0d
		}
		{
			dependencies: ["1B6DDF50D00CBB31"]
			id: "2EF4DD5CC254CC80"
			rewards: [
				{
					count: 4
					id: "011785CCEDC7688E"
					item: "mekanism:basic_mechanical_pipe"
					type: "item"
				}
				{
					id: "6FA14D9A12111586"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "流体传输专用"
			tasks: [{
				id: "45B151086147D98D"
				item: "mekanism:basic_mechanical_pipe"
				type: "item"
			}]
			x: -1.5d
			y: 4.0d
		}
		{
			dependencies: ["2EF4DD5CC254CC80"]
			hide_dependency_lines: true
			id: "1A72F7DA24E1BB09"
			rewards: [
				{
					id: "28A0F6D1DD5ABC8C"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "43AF0D7C4057994C"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "131392325AEB228E"
				item: "mekanism:advanced_mechanical_pipe"
				type: "item"
			}]
			x: -1.0d
			y: 5.0d
		}
		{
			dependencies: ["1A72F7DA24E1BB09"]
			id: "5B3FDF651D845DF1"
			rewards: [
				{
					id: "62E318265FA22D19"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "290BF7F4A217F813"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "78C482BF65B20673"
				item: "mekanism:elite_mechanical_pipe"
				type: "item"
			}]
			x: -1.5d
			y: 5.5d
		}
		{
			dependencies: ["5B3FDF651D845DF1"]
			id: "01C3B23461807007"
			rewards: [
				{
					id: "174F4154411CE222"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "45500FB3AC79CFE4"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "25D5626BBA7615F0"
				item: "mekanism:ultimate_mechanical_pipe"
				type: "item"
			}]
			x: -2.0d
			y: 5.0d
		}
		{
			dependencies: ["1B6DDF50D00CBB31"]
			id: "4434D7B66521D69A"
			rewards: [
				{
					count: 4
					id: "43C7CBD7952BAF44"
					item: "mekanism:basic_pressurized_tube"
					type: "item"
				}
				{
					id: "0C073BA4ADC6439C"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "气体传输专用"
			tasks: [{
				id: "685BFFC7BDA40FC3"
				item: "mekanism:basic_pressurized_tube"
				type: "item"
			}]
			x: 0.5d
			y: 4.0d
		}
		{
			dependencies: ["1B6DDF50D00CBB31"]
			id: "5B681BC43371CC5C"
			rewards: [
				{
					count: 4
					id: "75E2FFA83AB6A12A"
					item: "mekanism:basic_logistical_transporter"
					type: "item"
				}
				{
					id: "0E81EDFBC2D49452"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "物品传输专用"
			tasks: [{
				id: "3F972BE9DFCA1EB5"
				item: "mekanism:basic_logistical_transporter"
				type: "item"
			}]
			x: 2.5d
			y: 4.0d
		}
		{
			dependencies: ["1B6DDF50D00CBB31"]
			id: "531E3FF1F2865C67"
			rewards: [
				{
					count: 4
					id: "434BA009CC22D188"
					item: "mekanism:basic_thermodynamic_conductor"
					type: "item"
				}
				{
					id: "02D46DE414B9402B"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "热量传输专用"
			tasks: [{
				id: "5F4FEC0FD7C5CC33"
				item: "mekanism:basic_thermodynamic_conductor"
				type: "item"
			}]
			x: 4.5d
			y: 4.0d
		}
		{
			dependencies: ["4434D7B66521D69A"]
			hide_dependency_lines: true
			id: "768F9EBD3E115CA6"
			rewards: [
				{
					id: "2BC755C4DEF323CD"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "3CA544203F49B1DD"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "2E82ABE762A77F41"
				item: "mekanism:advanced_pressurized_tube"
				type: "item"
			}]
			x: 1.0d
			y: 5.0d
		}
		{
			dependencies: ["768F9EBD3E115CA6"]
			id: "6424D99CBA76895B"
			rewards: [
				{
					id: "622A2FCCC3BFDD77"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "4B2C848A94AB89DF"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "460D2FF854258953"
				item: "mekanism:elite_pressurized_tube"
				type: "item"
			}]
			x: 0.5d
			y: 5.5d
		}
		{
			dependencies: ["6424D99CBA76895B"]
			id: "546F7FF099D2696E"
			rewards: [
				{
					id: "69161884ABE804F2"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "189D64FDDDFC6AA3"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "1E066CC63A95B288"
				item: "mekanism:ultimate_pressurized_tube"
				type: "item"
			}]
			x: 0.0d
			y: 5.0d
		}
		{
			dependencies: ["5B681BC43371CC5C"]
			hide_dependency_lines: true
			id: "30B3469DAA8D5A0A"
			rewards: [
				{
					id: "7AEB19AB9872A9F0"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "6BBCBD12ECF90A70"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "631FDADA57127F2E"
				item: "mekanism:advanced_logistical_transporter"
				type: "item"
			}]
			x: 3.0d
			y: 5.0d
		}
		{
			dependencies: ["30B3469DAA8D5A0A"]
			id: "46AB23E922C51517"
			rewards: [
				{
					id: "55B3DD4C6EF41873"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "77ACF6D58FB6571F"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "1F7689D1D5DB5E8C"
				item: "mekanism:elite_logistical_transporter"
				type: "item"
			}]
			x: 2.5d
			y: 5.5d
		}
		{
			dependencies: ["46AB23E922C51517"]
			id: "6C8431C216A66C1F"
			rewards: [
				{
					id: "6BEC988B43431E0E"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "32495EA2EB9D601C"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "62A00D9DD3A8CA8E"
				item: "mekanism:ultimate_logistical_transporter"
				type: "item"
			}]
			x: 2.0d
			y: 5.0d
		}
		{
			dependencies: ["531E3FF1F2865C67"]
			hide_dependency_lines: true
			id: "7522F2DC9038ED92"
			rewards: [
				{
					id: "7CE8542B0820BF47"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "1DAAB1C491A45FA6"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "7B1C318ACC88ED60"
				item: "mekanism:advanced_thermodynamic_conductor"
				type: "item"
			}]
			x: 5.0d
			y: 5.0d
		}
		{
			dependencies: ["7522F2DC9038ED92"]
			id: "55E2F8C6A71E7328"
			rewards: [
				{
					id: "25196B84EC5AF157"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "4662DD54B15AF7FF"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "72F178B9B6C32B15"
				item: "mekanism:elite_thermodynamic_conductor"
				type: "item"
			}]
			x: 4.5d
			y: 5.5d
		}
		{
			dependencies: ["55E2F8C6A71E7328"]
			id: "5E2F9E27E6B4DF74"
			rewards: [
				{
					id: "1498DEB1CFBCFF1C"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "3589979E95DB7C1B"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "744EA1745278F562"
				item: "mekanism:ultimate_thermodynamic_conductor"
				type: "item"
			}]
			x: 4.0d
			y: 5.0d
		}
		{
			dependencies: ["195729280394ABFB"]
			id: "31B73D16C0199785"
			rewards: [
				{
					id: "66D918CA2A6FE21B"
					item: "mekanism:enriched_refined_obsidian"
					type: "item"
				}
				{
					id: "4003D6DCC41BFE6C"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0E6DCEACCB6074DF"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "69CEEFADCFA28675"
				item: "mekanism:ingot_refined_obsidian"
				type: "item"
			}]
			x: -7.5d
			y: 0.5d
		}
		{
			dependencies: ["195729280394ABFB"]
			id: "58445E5B3957ACC8"
			rewards: [
				{
					count: 2
					id: "356543506AA2B6B0"
					item: "minecraft:glowstone_dust"
					type: "item"
				}
				{
					id: "7B3AD733695E004D"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "532EE85D4748D50D"
				item: "mekanism:ingot_refined_glowstone"
				type: "item"
			}]
			x: -8.5d
			y: 0.5d
		}
		{
			dependencies: [
				"18D88932916C7A98"
				"407EFAF528871014"
			]
			description: [
				"该机器可\"提纯\"矿石.将1个原矿转化为2个\"矿簇\",经粉碎机处理成粗粉,再通过&a富集仓&f获得纯净矿粉,最后熔炼成锭"
				""
				"使金属锭产量翻倍"
				""
				"需消耗&a氧气&r运作,氧气由电解水产生(使用&e&a电解分离器&f&r)"
			]
			id: "6C25D42C36175BF4"
			rewards: [
				{
					count: 4
					id: "258304336DF472BB"
					item: "mekanism:clump_iron"
					type: "item"
				}
				{
					id: "20D0EFFC09ECB28D"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "2AF72C7C24D94674"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "矿簇制造机"
			tasks: [{
				id: "4983AE3B954BF2FE"
				item: "mekanism:purification_chamber"
				type: "item"
			}]
			x: -0.5d
			y: -2.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"该机器用于熔炼物品"
				""
				"可升级为熔炼工厂,最大熔炼槽数量可达9个"
			]
			id: "488DBE69595F38F8"
			optional: true
			rewards: [
				{
					count: 2
					id: "43FE96E4A4A00D5C"
					item: "minecraft:raw_iron"
					type: "item"
				}
				{
					id: "699241EB28E47F5E"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "40AB668F74703AC6"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "&d通用机械&f的&a动力炉&f"
			tasks: [{
				id: "5A081977DB9407EA"
				item: "mekanism:energized_smelter"
				type: "item"
			}]
			x: -6.0d
			y: -0.5d
		}
		{
			dependencies: ["08DDE018A804BFE7"]
			description: [
				"&a电解分离器&f(高端名称)用于从特定流体/气体中分离化学成分"
				""
				"若计划建造矿石&a处理&f工厂需大量配置"
				""
				"首先制作本机器,我们将分解最基础的流体:水"
			]
			id: "18D88932916C7A98"
			rewards: [
				{
					id: "58B361AC94AEB36D"
					item: "mekanism:alloy_infused"
					random_bonus: 1
					type: "item"
				}
				{
					id: "7863B65A90948AAA"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0C296148AF479F71"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "开始分解作业"
			tasks: [{
				id: "054CC6FB5173F8A3"
				item: "mekanism:electrolytic_separator"
				type: "item"
			}]
			x: -2.0d
			y: -2.0d
		}
		{
			dependencies: ["407EFAF528871014"]
			description: [
				"该发电机可燃烧氢气和乙烯来发电."
				""
				"注意:燃烧氢气产生的能量不会超过运行&a电解分离器&f所需的成本.建议改用乙烯燃烧."
			]
			id: "3EC9D0DA61B45328"
			rewards: [
				{
					count: 2
					id: "5E48D3BDD3BDAF4C"
					item: "mekanism:alloy_infused"
					type: "item"
				}
				{
					id: "58BEEEC6C7661895"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "56B7014FCB396F47"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "1BD5BF3301C59FE5"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			subtitle: "将气体转化为能源!"
			tasks: [{
				id: "2C13E8B67BC61E34"
				item: "mekanismgenerators:gas_burning_generator"
				type: "item"
			}]
			x: -2.0d
			y: -4.0d
		}
		{
			dependencies: ["263220DCCDB90E29"]
			description: [
				"这台机器是矿石&a处理&f工厂的下一阶段设备.将其放置在&a提纯仓&f的&a左侧&f,可扩展您现有的矿石工厂!"
				""
				"该机器需要&a氯化氢&f才能运作.要了解如何制造&a氯化氢&f,请完成上方任务!"
			]
			id: "4F1C04C0F6769825"
			rewards: [
				{
					count: 2
					id: "241F3906AE9BC520"
					item: "mekanism:alloy_infused"
					type: "item"
				}
				{
					id: "566771B41B345645"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "2216852A9C647509"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "将3个原矿转化为8个矿石碎片"
			tasks: [{
				id: "443DCB7E2E45F3DD"
				item: "mekanism:chemical_injection_chamber"
				type: "item"
			}]
			x: 5.0d
			y: -2.0d
		}
		{
			dependencies: ["603877AB96321F1A"]
			description: [
				"这台机器通过混合两种气体来制造新气体."
				""
				"您需要用它将氯气与氢气结合制成&a氯化氢&f,该气体随后可用于我们的&a化学压射室&f."
			]
			id: "04E2D539E33B7B0F"
			rewards: [
				{
					id: "3C71942DE8376D3A"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "576766449D68D6D4"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "&a化学化合机&f"
			tasks: [{
				id: "31D203EC92BEA66F"
				item: "mekanism:chemical_infuser"
				type: "item"
			}]
			x: 6.5d
			y: -3.5d
		}
		{
			dependencies: ["4F1C04C0F6769825"]
			description: [
				"要为机器获取&a氯化氢&f,我们需要先制备&a盐水&r."
				""
				"这需要&a热力蒸馏塔&f,这是个多方块结构."
				""
				"首先搭建4x3x4的中空结构(仅底座实心),可用&a控制器&f或阀门替换任意侧面方块"
				""
				"至少需要1个控制器和2个阀门"
				""
				"注入水后开始收集盐水,将其泵入&a&a电解分离器&f&r可从气体中提取&6氯气&r"
				"{image:atm:textures/questpics/mek/thermalplant.png width:100 height:100 align:1}"
			]
			id: "603877AB96321F1A"
			min_width: 300
			rewards: [
				{
					count: 2
					id: "7C0FF0123BCFA127"
					item: "alltheores:steel_ingot"
					random_bonus: 2
					type: "item"
				}
				{
					id: "3BC1C8A0556253D3"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "2A58C351E36E9DD3"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "水变卤水!"
			tasks: [
				{
					id: "716D483EFC35F6F3"
					item: "mekanism:thermal_evaporation_controller"
					type: "item"
				}
				{
					count: 37L
					id: "33D7942176AD257D"
					item: "mekanism:thermal_evaporation_block"
					type: "item"
				}
				{
					count: 2L
					id: "4BAD2AADFE32215A"
					item: "mekanism:thermal_evaporation_valve"
					type: "item"
				}
			]
			title: "&a热力蒸馏塔&f"
			x: 5.0d
			y: -3.5d
		}
		{
			dependencies: ["1112E4E2CCEB2467"]
			description: [
				"我们需要在当前配置中再添加3台机器."
				""
				"流程的第一部分是&a化学溶解室&f."
				""
				"这台机器需要&a硫酸&f将原矿分解成\"矿浆\"."
				""
				"要获取&a硫酸&f,我们需要新建一套装置,具体说明详见上方任务."
			]
			id: "4F436770D30D8520"
			rewards: [
				{
					count: 2
					id: "168E7AFB6769C922"
					item: "mekanism:ingot_refined_obsidian"
					random_bonus: 2
					type: "item"
				}
				{
					id: "455096107C4695CC"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0A42ABE6D34462D5"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "1FE5AA0FF1F9BB51"
				item: "mekanism:chemical_dissolution_chamber"
				type: "item"
			}]
			title: "四级矿石工厂的启动"
			x: 9.0d
			y: -2.0d
		}
		{
			dependencies: ["4F436770D30D8520"]
			description: [
				"制作&a硫酸&f需要先制备&a水蒸气&f."
				""
				"向这台机器泵入水即可转化为蒸汽."
			]
			id: "4236B9F071BE18F3"
			rewards: [
				{
					id: "2AAB70B739306C6C"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "31A8BB8FAFD364E7"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "50D9504BFFB97A27"
				item: "mekanism:rotary_condensentrator"
				type: "item"
			}]
			x: 8.5d
			y: -3.5d
		}
		{
			dependencies: ["4F436770D30D8520"]
			description: [
				"首先从制备&a二氧化硫&f开始."
				""
				"将火药放入单独的&a化学压射室&f与&a氯化氢&f反应生成&a硫粉&f.或者你也可以直接粉碎&d热力系列&f中的硫磺获得&a硫粉&f,任君选择."
				""
				"将&a硫粉&f送入这台&a化学氧化机&f即可生成&a二氧化硫&f.接下来需要制备&a三氧化硫&f."
				""
				"把&a二氧化硫&f通入&a化学灌注器&f与氧气结合生成&a三氧化硫&f.再将三氧化物送入另一个&a化学灌注器&f与&a水蒸气&f反应生成&a硫酸&f."
				""
				"流程相当复杂."
			]
			id: "2E274BEEF2B0B8C7"
			rewards: [
				{
					id: "352AE8CE9CFD6D38"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "2F7DD21804D3F74C"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "21BFDF262D2A9DB3"
					item: "mekanism:chemical_oxidizer"
					type: "item"
				}
				{
					id: "3B5A283601BEADA0"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:dusts/sulfur"
						}
					}
					title: "任意&a硫粉&f"
					type: "item"
				}
			]
			x: 9.5d
			y: -3.5d
		}
		{
			dependencies: ["603877AB96321F1A"]
			description: [
				"&a热力蒸馏塔&f多方块结构的盐水产量取决于内部温度."
				""
				"有多种升温方式,包括直接在沙漠建造!"
				""
				"&a木料加热器&f通过燃烧熔岩桶产热,可通过导热管道输送热量."
				""
				"&a电阻型加热器&f利用RF/FE能量产热,可自由设置能耗功率."
			]
			id: "027084AE2DF5EBA6"
			optional: true
			rewards: [
				{
					id: "3EF65B888EC38888"
					item: "mekanism:thermal_evaporation_valve"
					type: "item"
				}
				{
					id: "12B2CD9579419F81"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "热盐水现制现卖,新鲜热盐水"
			tasks: [{
				id: "6E75525A5D88EF23"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:fuelwood_heater"
							}
							{
								Count: 1b
								id: "mekanism:resistive_heater"
							}
						]
					}
				}
				title: "加热器"
				type: "item"
			}]
			title: "提升盐水生产温度"
			x: 5.0d
			y: -5.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"创建自定义传送门需先放置传送器方块并接通能源."
				""
				"以传送器方块为基底搭建\"&a传送门框架&f\"."
				""
				"最终成品为4x3的传送门结构,中间两个方块构成传送门本体."
			]
			hide_dependency_lines: true
			id: "7B0DFA55B4D8B16D"
			rewards: [
				{
					id: "1BF6EFE93A37AF5C"
					item: "mekanism:teleportation_core"
					type: "item"
				}
				{
					id: "4893EC372720D401"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "7638B4BA27EA012E"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "传送艺术的巅峰之作"
			tasks: [
				{
					count: 9L
					id: "2C1730C4F7CB8377"
					item: "mekanism:teleporter_frame"
					type: "item"
				}
				{
					id: "68FE61A81A5C1390"
					item: "mekanism:teleporter"
					type: "item"
				}
			]
			title: "&a自定义传送门&f!"
			x: -9.0d
			y: 3.75d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"该物品用于存储能量,功能类似便携电池组."
				""
				"同时也是&d通用机械&f中的重要合成材料."
			]
			hide_dependency_lines: true
			id: "1FC7E9DBF92BE6AA"
			rewards: [
				{
					id: "71757B9EB777A90A"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "118BA15EE6BE785F"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "便携式&a电池组&f"
			tasks: [{
				id: "79EF9001D76FD91D"
				item: "mekanism:energy_tablet"
				type: "item"
			}]
			x: -9.0d
			y: 2.5d
		}
		{
			dependencies: ["166971866A9234C7"]
			hide_dependency_lines: true
			id: "3C8D9278B81BB37A"
			rewards: [
				{
					id: "7EB456E52AAA2685"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "05078555F0B50C46"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "46C5C0922839BDCA"
				item: "mekanism:upgrade_speed"
				type: "item"
			}]
			x: 0.5d
			y: 9.5d
		}
		{
			dependencies: ["166971866A9234C7"]
			hide_dependency_lines: true
			id: "0ACE573560A19309"
			rewards: [
				{
					id: "1CD0AB579EAB2073"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0B54660C3D8B7D52"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "1B5CF4B0B6D3F6F9"
				item: "mekanism:upgrade_energy"
				type: "item"
			}]
			x: 1.0d
			y: 10.0d
		}
		{
			dependencies: ["166971866A9234C7"]
			hide_dependency_lines: true
			id: "763FB27929E053BE"
			rewards: [
				{
					id: "103C389B08EA61DA"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "063BFCFDD1528E5F"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "43E4874F23459DB1"
				item: "mekanism:upgrade_filter"
				type: "item"
			}]
			x: -0.5d
			y: 9.5d
		}
		{
			dependencies: ["166971866A9234C7"]
			hide_dependency_lines: true
			id: "001DE8028CAF0A08"
			rewards: [
				{
					id: "0A099579F90657E4"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "4D74CDF6AE6E40C3"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			subtitle: "嘘...太吵了...."
			tasks: [{
				id: "126FB3CA8F2CCF11"
				item: "mekanism:upgrade_muffling"
				type: "item"
			}]
			x: 0.0d
			y: 10.0d
		}
		{
			dependencies: ["166971866A9234C7"]
			hide_dependency_lines: true
			id: "09830BB2A23E94B4"
			rewards: [
				{
					id: "37BB7B57D72C0E8C"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "4D45C6C905944505"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "70A4BDDF6392DC9D"
				item: "mekanism:upgrade_gas"
				type: "item"
			}]
			x: 0.5d
			y: 10.5d
		}
		{
			dependencies: ["166971866A9234C7"]
			hide_dependency_lines: true
			id: "515A60B89ED5440D"
			rewards: [
				{
					id: "38FD0131ED5FEF8B"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "4D2F9BA2D7981111"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				id: "0872B5A950703C8A"
				item: "mekanism:upgrade_stone_generator"
				type: "item"
			}]
			x: 1.5d
			y: 9.5d
		}
		{
			dependencies: ["1FC7E9DBF92BE6AA"]
			description: ["该工具用于配置&d通用机械&f中诸多设备,从切换管道\"抽取/推送\"模式到旋转机器方向均可操作."]
			hide_dependency_lines: true
			id: "5E116409DC7D30BB"
			progression_mode: "linear"
			rewards: [
				{
					id: "38057234ABE98B7A"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "7B99F69BA1AB0098"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "&d通用机械&f的扳手"
			tasks: [{
				id: "2612CACBDDF9AD1A"
				item: "mekanism:configurator"
				type: "item"
			}]
			x: -9.0d
			y: 5.0d
		}
		{
			dependencies: ["18D88932916C7A98"]
			description: [
				"分解水需要先建立水源.&a厨房水槽&f提供无限水源,可轻松连接水泵输出."
				""
				"当然你也可以使用&d通用机械&f的泵搭配经典无限水源."
				""
				"将水泵入&a电解分离器&f即可分解出氢气和氧气."
			]
			id: "407EFAF528871014"
			rewards: [
				{
					id: "5F4E37BD22336518"
					item: "minecraft:water_bucket"
					type: "item"
				}
				{
					id: "661BF73705DAFB31"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "万能的无限水源"
			tasks: [{
				id: "16DAFC5C5846015A"
				item: "cookingforblockheads:sink"
				type: "item"
			}]
			title: "水源"
			x: -2.0d
			y: -3.0d
		}
		{
			dependencies: ["6DB1AAAD926486BC"]
			hide_dependency_lines: true
			id: "30F8BA43B1BB9035"
			rewards: [
				{
					id: "68FCEAF6FC113984"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "4E84D5C93A175BAE"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5FFEF5D6055A67B1"
				item: "mekanism:advanced_fluid_tank"
				type: "item"
			}]
			x: 4.0d
			y: 7.800000000000004d
		}
		{
			dependencies: ["30F8BA43B1BB9035"]
			id: "5F94483E05D2F528"
			rewards: [
				{
					id: "34D281AF21304592"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "310591995AE450E0"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "05059B48A8F56BC5"
				item: "mekanism:elite_fluid_tank"
				type: "item"
			}]
			x: 3.5d
			y: 8.300000000000004d
		}
		{
			dependencies: ["5F94483E05D2F528"]
			id: "712CB147B5873121"
			rewards: [
				{
					id: "11A26AE87671CC86"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "00B48685B5FE781C"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "33F9C615B944909A"
				item: "mekanism:ultimate_fluid_tank"
				type: "item"
			}]
			x: 3.0d
			y: 7.800000000000004d
		}
		{
			dependencies: ["58B125BD4876054C"]
			description: ["该方块用于储存气体."]
			hide_dependency_lines: true
			id: "41EB0C570FC54F43"
			rewards: [
				{
					id: "2BA7618ACAED912E"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "6A2363D77EC727F8"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			size: 1.5d
			subtitle: "储存所有气体"
			tasks: [{
				id: "665B9818855C0028"
				item: "mekanism:basic_chemical_tank"
				type: "item"
			}]
			x: 1.5000000000000053d
			y: 7.0d
		}
		{
			dependencies: ["41EB0C570FC54F43"]
			hide_dependency_lines: true
			id: "1162544BA1B2B0F3"
			rewards: [
				{
					id: "11600E65D29E4987"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "05ED665CB2C849D2"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "78CFB8D559DA92AE"
				item: "mekanism:advanced_chemical_tank"
				type: "item"
			}]
			x: 2.0d
			y: 7.800000000000004d
		}
		{
			dependencies: ["1162544BA1B2B0F3"]
			id: "4C4B1602E2AFC314"
			rewards: [
				{
					id: "1866147DD8145A9F"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "2CF135C8D597A4A4"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "1D2AE44FA5391B71"
				item: "mekanism:elite_chemical_tank"
				type: "item"
			}]
			x: 1.5d
			y: 8.300000000000004d
		}
		{
			dependencies: ["4C4B1602E2AFC314"]
			id: "0C0D0B80B3FA26F2"
			rewards: [
				{
					id: "35D89D94AE8BCBD3"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "77A4A2D6E72F8CE5"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5FD0078940E64A4C"
				item: "mekanism:ultimate_chemical_tank"
				type: "item"
			}]
			x: 1.0d
			y: 7.800000000000004d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: ["利用阳光发电!"]
			hide_dependency_lines: true
			id: "74200A48498DD7F8"
			rewards: [
				{
					id: "1E2B1E4B956092D9"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "74FCF5135C24D033"
					type: "xp"
					xp: 100
				}
			]
			shape: "octagon"
			size: 1.25d
			subtitle: "产能约17.6FE/秒"
			tasks: [{
				id: "22ACFEB806D68385"
				item: "mekanismgenerators:solar_generator"
				type: "item"
			}]
			x: -1.0d
			y: 1.0d
		}
		{
			dependencies: ["6F62B5510FA881CD"]
			description: [
				"&a热力发电机&f有两种发电模式:"
				""
				"&9被动模式:&r用熔岩源或流动方块包围发电机,通过产生热量来被动发电.在顶部放置一个熔岩源方块,让其从侧面流下.记得先连接管道!"
				""
				"&9主动模式:&r将煤炭或木材等可燃材料放入发电机,通过燃烧燃料来发电."
			]
			id: "0650996C7818ADB5"
			rewards: [
				{
					id: "7072D079156C2A44"
					item: "alltheores:osmium_ingot"
					type: "item"
				}
				{
					id: "6D2DD795F34A4177"
					type: "xp"
					xp: 10
				}
			]
			shape: "octagon"
			size: 1.25d
			subtitle: "基础发电设备"
			tasks: [{
				id: "7BA12BAB8271E170"
				item: "mekanismgenerators:heat_generator"
				type: "item"
			}]
			x: -8.875d
			y: -1.975d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: ["该发电机可将&a生物燃料&f转化为能量,产能约140FE/t."]
			hide_dependency_lines: true
			id: "6CD1720B76F47806"
			rewards: [
				{
					id: "1301C1670E4241EF"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "45C7E780DC5681CF"
					type: "xp"
					xp: 100
				}
			]
			shape: "octagon"
			size: 1.25d
			tasks: [{
				id: "36B12984060A90FD"
				item: "mekanismgenerators:bio_generator"
				type: "item"
			}]
			x: 3.0d
			y: 2.0d
		}
		{
			dependencies: ["74200A48498DD7F8"]
			description: [
				"若您犹豫是否值得升级,答案是肯定的."
				""
				"此版本产能105.6FE/t,还可用于为&a热力蒸馏塔&f提供额外热量."
			]
			id: "4EDD96EB60EF5814"
			rewards: [
				{
					id: "7C9221EEC576875E"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "0736D51EE3F58FA4"
					type: "xp"
					xp: 100
				}
			]
			shape: "octagon"
			size: 1.25d
			tasks: [{
				id: "4F0B3CA66BE1AEE1"
				item: "mekanismgenerators:advanced_solar_generator"
				type: "item"
			}]
			x: -2.0d
			y: 2.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"这是绝佳的发电选择."
				""
				"基础产能约40FE/t,且随高度提升而增加.&aY坐标&f越高,发电量越大!"
			]
			hide_dependency_lines: true
			id: "7778937DF377C1B4"
			rewards: [
				{
					id: "31ABFC11245A8E49"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "32DF83533E5D7416"
					type: "xp"
					xp: 100
				}
			]
			shape: "octagon"
			size: 1.25d
			subtitle: "风力发电"
			tasks: [{
				id: "50624A39AD15A8A6"
				item: "mekanismgenerators:wind_generator"
				type: "item"
			}]
			x: 2.0d
			y: 1.0d
		}
		{
			description: [
				"&d通用机械&f是能改变游戏体验的科技模组."
				""
				"&a化工&f系统专注于分解物质的化学成分,充分提取每种材料的价值."
				""
				"本模组包含氢动力喷气背包、迷你机器人伙伴、反应堆、自动化采矿的&a数字型采矿机&f等丰富内容."
			]
			icon: {
				Count: 1
				id: "mekanism:creative_energy_cube"
				tag: {
					mekData: {
						EnergyContainers: [{
							Container: 0b
							stored: "18446744073709551615.9999"
						}]
					}
				}
			}
			id: "58B125BD4876054C"
			rewards: [
				{
					id: "7D519C36EA3CF356"
					item: "alltheores:raw_osmium"
					type: "item"
				}
				{
					id: "1C5A7C0402C237DD"
					type: "xp"
					xp: 10
				}
			]
			shape: "square"
			size: 2.0d
			subtitle: "打造专属工厂"
			tasks: [{
				id: "10CE7D11B912F418"
				item: "alltheores:raw_osmium"
				type: "item"
			}]
			title: "&d通用机械&f"
			x: -13.0d
			y: -2.0d
		}
		{
			dependencies: ["58B125BD4876054C"]
			id: "6F62B5510FA881CD"
			rewards: [
				{
					id: "52CF295425D3C2B9"
					item: "alltheores:raw_osmium"
					random_bonus: 1
					type: "item"
				}
				{
					id: "75AA096A6B99BFA3"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "基础合成材料"
			tasks: [{
				id: "52967F83338A3AC3"
				item: "alltheores:osmium_ingot"
				type: "item"
			}]
			title: "锇锭"
			x: -10.5d
			y: -2.0d
		}
		{
			dependencies: ["7AE502EDB73BD57A"]
			description: [
				"当前阶段要实现\"双倍\"锭产量,设备应如下配置:"
				""
				"原矿输入&a提纯仓&f,由&e电解分离器&f供应&a氧气&r."
				""
				"产物随后进入&a破碎机&r,将矿团转化为\"粗矿粉\".这些\"粗矿粉\"输入&a富集仓&f后,会转化为对应的\"&a精矿粉&f\"."
				""
				"最后&a富集仓&f将产物输送至熔炉.明白了吗？"
			]
			id: "263220DCCDB90E29"
			rewards: [
				{
					count: 2
					id: "07F4FEA9CD91FB7B"
					item: "mekanism:alloy_infused"
					type: "item"
				}
				{
					id: "12132365C99DF4EB"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			size: 1.5d
			subtitle: "二级矿石工厂"
			tasks: [{
				id: "46C4C1AD9FA5BEE2"
				title: "当前装置布局"
				type: "checkmark"
			}]
			x: 3.0d
			y: -2.0d
		}
		{
			dependencies: [
				"4F1C04C0F6769825"
				"04E2D539E33B7B0F"
			]
			description: [
				"现在您应有5台设备准备处理矿石.准备好迎接更复杂的流程了吗？"
				""
				"生产线应为:&a化学压射室&f > 提纯仓 > 破碎机 > &a富集仓&f > 熔炉."
				""
				"目前还算简单对吧？"
				""
				"系好安全带,接下来会更疯狂."
			]
			id: "1112E4E2CCEB2467"
			rewards: [{
				id: "0B129E30AA37E379"
				type: "xp"
				xp: 10
			}]
			subtitle: "三级矿石工厂"
			tasks: [{
				id: "57F40CFA03BD36EF"
				title: "&a困难&f部分"
				type: "checkmark"
			}]
			x: 6.5d
			y: -2.0d
		}
		{
			dependencies: ["08DDE018A804BFE7"]
			description: [
				"将气体、固体与液体结合,生成主产物与副产品."
				""
				"该机器用于制作终局材料与装备."
			]
			id: "587A19FC348387C5"
			rewards: [
				{
					count: 2
					id: "7F4F10CD3B125E3A"
					item: "mekanism:basic_control_circuit"
					type: "item"
				}
				{
					id: "583CAD36267241C2"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "6153A09A9D197270"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "1C162E39F2ACA72F"
				item: "mekanism:pressurized_reaction_chamber"
				type: "item"
			}]
			x: -4.0d
			y: -3.5d
		}
		{
			dependencies: ["7AE502EDB73BD57A"]
			description: ["&a磨粉机&f还能将天然物质分解为&a生物燃料&f!"]
			id: "3B43DB1A6B0A7B44"
			rewards: [
				{
					id: "174A1D24134BB9DC"
					item: "mekanism:bio_fuel"
					type: "item"
				}
				{
					id: "72D588EA49839008"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				count: 2L
				id: "05138E2E906020F3"
				item: "mekanism:bio_fuel"
				type: "item"
			}]
			title: "&a生物燃料&f"
			x: 1.0d
			y: -3.0d
		}
		{
			dependencies: ["3B43DB1A6B0A7B44"]
			description: [
				"在&a加压反应室&f中将&a生物燃料&f与水、氢结合,可生成基质并副产乙烯."
				""
				"这是制造HDPE颗粒的原料,用于终局装备如MEKA套装的制作."
			]
			id: "5047792C6EF6D2AD"
			rewards: [
				{
					id: "4501F6A232456167"
					item: "mekanism:substrate"
					type: "item"
				}
				{
					id: "1979E89900B74A27"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				count: 3L
				id: "1F6A5B58DF94730B"
				item: "mekanism:substrate"
				type: "item"
			}]
			title: "基质"
			x: 1.0d
			y: -4.0d
		}
		{
			dependencies: ["5047792C6EF6D2AD"]
			description: ["在&a加压反应室&f中混合氧气、乙烯和基质,可制成&a高密度聚乙烯丸&f."]
			id: "76A38CCA5816CDAD"
			rewards: [
				{
					count: 2
					id: "16F49D5355A06C1B"
					item: "mekanism:substrate"
					type: "item"
				}
				{
					id: "1138396810238D01"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				count: 3L
				id: "62E8F3CC847BBD3D"
				item: "mekanism:hdpe_pellet"
				type: "item"
			}]
			title: "&a高密度聚乙烯丸&f"
			x: 1.0d
			y: -5.0d
		}
		{
			dependencies: ["76A38CCA5816CDAD"]
			description: ["将3个&a高密度聚乙烯丸&f放入&a富集仓&f,可制成&a高密度聚乙烯片&f."]
			id: "47106CE1937C4340"
			rewards: [
				{
					count: 3
					id: "78A06CC539C1B6AE"
					item: "mekanism:hdpe_pellet"
					type: "item"
				}
				{
					id: "6222BDC7235A0FBC"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "6EE6F07986BAFC3B"
				item: "mekanism:hdpe_sheet"
				type: "item"
			}]
			x: 1.0d
			y: -6.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			hide_dependency_lines: true
			id: "49675EA8CBCA1388"
			progression_mode: "linear"
			rewards: [
				{
					id: "1E9683F66A5FE752"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "76A57DE86B17E93A"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "6B3298C898E9C59A"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "hexagon"
			size: 1.25d
			subtitle: "&d通用机械&f全能工具"
			tasks: [{
				id: "79F38A9428A0A23D"
				item: "mekanism:atomic_disassembler"
				type: "item"
			}]
			x: -6.5d
			y: 5.0d
		}
		{
			dependencies: ["08DDE018A804BFE7"]
			description: [
				"使用&a富集仓&f,您可以将物品富集转化为其富集变种."
				""
				"这些\"富集\"物品在&a冶金灌注机&f中可产出8倍容量的mb."
				""
				"若计划批量生产钢锭,请先富集您的木炭!"
			]
			id: "0F326EEEC2EBE4E5"
			rewards: [
				{
					count: 2
					id: "1B7E6CC9019F43AB"
					item: "mekanism:enriched_carbon"
					type: "item"
				}
				{
					id: "04127BD3A4706FE4"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "优先富集物品!"
			tasks: [{
				id: "4B60ACBCC3B46D1D"
				item: "mekanism:enriched_carbon"
				type: "item"
			}]
			title: "物品富集"
			x: -4.0d
			y: -0.5d
		}
		{
			dependencies: ["4F436770D30D8520"]
			description: [
				"本机器需水运作,通过水流清洗\"矿浆\"为\"纯净矿浆\"."
				""
				"这是您四级矿石&a处理&f工厂的第二阶段设施."
			]
			id: "4AFF81D3D0E78255"
			rewards: [
				{
					id: "3CE007719AA0DDD8"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "18B6402C4049FD4C"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "给矿石洗个澡"
			tasks: [{
				id: "1DA159AA61B37545"
				item: "mekanism:chemical_washer"
				type: "item"
			}]
			x: 8.5d
			y: -0.5d
		}
		{
			dependencies: ["4F436770D30D8520"]
			description: [
				"本机器是四级矿石&a处理&f工厂的第三环节."
				""
				"它将&a化学清洗机&f产出的纯净矿浆转化为晶体,供后续&a化学压射室&f加工."
			]
			id: "3999760881C855FA"
			rewards: [
				{
					id: "2B37585059AAE0D6"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "75C11279E936ADAA"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "将矿浆转化为晶体"
			tasks: [{
				id: "6F2B3A4206C129B0"
				item: "mekanism:chemical_crystallizer"
				type: "item"
			}]
			x: 9.5d
			y: -0.5d
		}
		{
			dependencies: [
				"3999760881C855FA"
				"4AFF81D3D0E78255"
			]
			description: [
				"如果你和我一样,可能在最后几步迷路55次——这确实是个复杂系统"
				""
				"工厂基础布局应为:"
				""
				"原矿进入&a化学溶解室&f > 将气体泵入&a化学清洗机&f > 再泵入&a化学结晶器&f > 晶体输出至&a化学压射室&f > 碎片进入&a提纯仓&f > 团块进入粉碎机 > 粗粉进入&a富集仓&f > 精粉送入熔炉"
				""
				"{image:atm:textures/questpics/mek/mekanism_flowchart.png width:300 height:150 align:1 fit:true}"
			]
			id: "3C49F2EEDCCAF1DF"
			min_width: 300
			rewards: [{
				id: "3417F669ABC16584"
				type: "xp"
				xp: 100
			}]
			shape: "gear"
			size: 1.5d
			subtitle: "内容很多,我知道."
			tasks: [{
				id: "3B3B58C438FD4397"
				title: "四级矿石&a处理&f工厂概览"
				type: "checkmark"
			}]
			x: 9.0d
			y: 1.0d
		}
		{
			dependencies: ["162CE44400A63575"]
			description: [
				"站立在此物品上可为任意模组的电力设备充能."
				""
				"这也是制造机械仆从的必备组件."
			]
			hide_dependency_lines: true
			id: "4204702AA6FBF40B"
			rewards: [
				{
					id: "72909BB3BAF1758D"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "18F7E9FF427F660D"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "6190BE8B67130AB5"
				item: "mekanism:chargepad"
				type: "item"
			}]
			x: -6.5d
			y: 2.5d
		}
		{
			dependencies: [
				"4236B9F071BE18F3"
				"2E274BEEF2B0B8C7"
			]
			description: [
				"在掌握高级机械操作后,现在开始制作&d&a反物质靶丸&f&r."
				""
				"这些能合成多种&5终局&r物品,包括&e&aATM之星&f&r.欲了解反应堆等完整知识,请前往&a&d通用机械&f&r:&d反应堆&r任务线!"
			]
			icon: "mekanism:pellet_antimatter"
			id: "7E4A95B6443F23BC"
			rewards: [{
				id: "1E16D1CE01AD7052"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 2.0d
			subtitle: "通往反应堆的&a铺路石&f"
			tasks: [{
				id: "60BF6E0420C91050"
				title: "&d高级&d通用机械&f"
				type: "checkmark"
			}]
			x: 9.0d
			y: -5.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"此任务默认隐藏,若您看到本提示,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "1B98350325FA52CD"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "16F845475F9E5089"
					title: "全模组任务集"
					type: "checkmark"
				}
				{
					id: "22A1ED0E7CF3404B"
					title: "全模组任务集"
					type: "checkmark"
				}
			]
			x: -13.0d
			y: -4.0d
		}
	]
	title: "&d通用机械&f"
}
