{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "apotheosis_2"
	group: "6614EE2378B8AFB9"
	icon: "minecraft:spawner"
	id: "0E81CBCD6B1D1895"
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["7C3968AF39557751"]
			description: ["&d神化&f对部分物品的改动包括铁砧和剪刀的附魔.剪刀现在可兼容常规附魔(包括时运),还新增了&a生长血清&f、色差效应和劳工剥削等新魔咒(具体效果待您探索).铁砧现在可附加耐久系列及分裂/湮灭等新魔咒——在铁砧上使用后,需通过附魔铁砧将其砸向&a附魔书&f生效."]
			id: "5B653A45093C56F0"
			rewards: [{
				id: "551D3CE25E4DD3EB"
				item: {
					Count: 1
					id: "minecraft:enchanted_book"
					tag: {
						StoredEnchantments: [{
							id: "minecraft:unbreaking"
							lvl: 3s
						}]
					}
				}
				type: "item"
			}]
			tasks: [
				{
					id: "2A0D3BDA4C874A43"
					item: "minecraft:anvil"
					type: "item"
				}
				{
					id: "5A94111976A96FCC"
					item: {
						Count: 1
						id: "minecraft:shears"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "铁砧与剪刀"
			x: -2.0d
			y: -1.0d
		}
		{
			dependencies: ["7C3968AF39557751"]
			description: ["厌倦了仙人掌、竹子和甘蔗的标准高度？渴望看到竹子摩天大楼？&d神化&f为您实现作物参天的梦想!现在仙人掌、竹子和甘蔗的生长上限已提升!具体多高？谁知道呢,世界高度限制是多少来着？"]
			id: "6184B40362A7B190"
			rewards: [{
				count: 32
				id: "26405F74A6300E73"
				item: "minecraft:bone_meal"
				type: "item"
			}]
			tasks: [
				{
					id: "01F155EEAC1A8404"
					item: "minecraft:sugar_cane"
					type: "item"
				}
				{
					id: "3FE68BCA484CF4BB"
					item: "minecraft:cactus"
					type: "item"
				}
				{
					id: "0756DD96D4A479FD"
					item: "minecraft:bamboo"
					type: "item"
				}
			]
			title: "高大的&a绿色染料作物&f现在更高了"
			x: -2.5d
			y: 0.0d
		}
		{
			dependencies: ["7C3968AF39557751"]
			description: ["自1.14版本就存在的&a制箭台&f至今仍是村民专属工作点!玩家权益何在？!现在您无需在工作台制作药箭,直接使用&a制箭台&f——用专业设备才更酷炫."]
			id: "00588B2FDB99874D"
			rewards: [{
				count: 8
				id: "25153229ED21F95C"
				item: "minecraft:arrow"
				type: "item"
			}]
			tasks: [{
				id: "6CB3873E7BCE06F2"
				item: "minecraft:fletching_table"
				type: "item"
			}]
			title: "&a制箭台&f现在有用了!"
			x: -2.0d
			y: 1.0d
		}
		{
			dependencies: ["07D875C6BBFF3264"]
			description: ["&d神化&f另一项变革针对刷怪箱.还记得用&a精准采集&f挖取刷怪箱的时光吗？现在这个功能通过&d神化&f回归了!您还可以通过&a右键点击&f各类物品为刷怪箱添加多种新型改造模块."]
			icon: {
				Count: 1
				id: "minecraft:spawner"
				tag: {
					BlockEntityTag: {
						Delay: 159s
						ForgeCaps: { }
						MaxNearbyEntities: 6s
						MaxSpawnDelay: 800s
						MinSpawnDelay: 200s
						RequiredPlayerRange: 16s
						SpawnCount: 4s
						SpawnData: {
							entity: {
								id: "minecraft:axolotl"
							}
						}
						SpawnPotentials: [{
							data: {
								entity: {
									id: "minecraft:axolotl"
								}
							}
							weight: 1
						}]
						SpawnRange: 4s
						baby: 0b
						ignore_conditions: 0b
						ignore_light: 0b
						ignore_players: 0b
						no_ai: 0b
						redstone_control: 0b
						silent: 0b
					}
				}
			}
			id: "310969B8FE0A94DE"
			tasks: [{
				advancement: "apotheosis:spawner/root"
				criterion: ""
				id: "256BA7F05A23110F"
				type: "advancement"
			}]
			title: "&a刷怪箱&f"
			x: 1.0d
			y: 0.0d
		}
		{
			description: ["鉴于&d神化&f体量庞大,我将任务分为三个章节:常规游戏改动、&a附魔学&f和词缀系统将独立成章."]
			id: "07D875C6BBFF3264"
			tasks: [{
				id: "6E24BC7D56597911"
				title: "&d神化&f"
				type: "checkmark"
			}]
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["07D875C6BBFF3264"]
			description: ["&d神化&f为Minecraft添加了一些小改动,别担心,都是有益且有帮助的!"]
			id: "7C3968AF39557751"
			tasks: [{
				id: "1F3314E76D27E430"
				title: "基础游戏改动"
				type: "checkmark"
			}]
			x: -1.0d
			y: 0.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["某些生物(主要是怪物)需要特定的光照等级才能生成.敌对生物需要较低的光照等级,而被动生物需要较高的光照等级.使用&a灵魂灯笼&f后,你就不用再担心光照等级了,因为它会忽略光照!这并不会忽略其他生成条件,比如牲畜动物需要草地.&a龙蛋&f也会忽略这一点."]
			id: "0F89BFD4A3F63A48"
			rewards: [{
				id: "6C85E2CE330363D6"
				item: "torchmaster:dreadlamp"
				type: "item"
			}]
			tasks: [{
				id: "5F16431DBEAF97B0"
				item: "minecraft:soul_lantern"
				type: "item"
			}]
			title: "忽略光照"
			x: 2.0d
			y: 0.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["&a猪巫妖之心&f由猪巫妖掉落.它可以用来增加刷怪笼能够生成的生物数量.生成的&a生物&f是随机的,最大数量由&a猪巫妖之心&f决定.每颗心会使数量增减1,最大为8."]
			id: "6A497B063CF32A5C"
			rewards: [{
				id: "3F1149656E6D078F"
				item: "allthemodium:piglich_heart"
				type: "item"
			}]
			tasks: [{
				id: "426CC475B062FC85"
				item: "allthemodium:piglich_heart"
				type: "item"
			}]
			title: "&a生成数量&f"
			x: 1.5d
			y: 1.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["最大实体数量是指刷怪笼能够生成并保留的生物数量.如果最大实体数量为6,当已经有6个生物生成时,除非它们死亡或被移走,否则不会再生成更多生物.每个&a恶魂之泪&f会使数量增减2.最大实体数量为16,最小为1."]
			id: "4D07B0A4A2E77CDA"
			rewards: [{
				count: 10
				id: "125102EC635E5209"
				item: "minecraft:ghast_tear"
				type: "item"
			}]
			tasks: [{
				id: "4DA7AE7F61B1FC05"
				item: "minecraft:ghast_tear"
				type: "item"
			}]
			title: "最大实体数量"
			x: 2.5d
			y: -2.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["在刷怪笼上使用&a金苹果&f后,你将吸走即将生成的生物的灵魂,只留下它们曾经的躯壳.&a生物&f将失去所有AI,基本上会像盔甲架一样行动.它们无法攻击你,无法传送,无法自主移动.它们只会呆在那里等着被杀死,多么刺激!"]
			id: "42D7C8CD8E6F5CD7"
			rewards: [{
				count: 3
				id: "1B960A1E13DB5302"
				item: "minecraft:golden_apple"
				type: "item"
			}]
			tasks: [{
				id: "7CA847C78EB1B215"
				item: "minecraft:golden_apple"
				type: "item"
			}]
			title: "无AI"
			x: 2.5d
			y: 2.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["这个功能对于从早期版本回归的玩家来说可能是新的.在刷怪笼上使用海龟蛋后,它只会生成其中的幼年生物.这只适用于原版的幼年生物,不适用于模组中的生物."]
			id: "6F71FD826C29C31A"
			rewards: [{
				id: "1532FF56C99CAB89"
				item: "aquaculture:box_turtle"
				type: "item"
			}]
			tasks: [{
				id: "65FF566B862C7211"
				item: "minecraft:turtle_egg"
				type: "item"
			}]
			title: "幼年"
			x: 3.5d
			y: 2.5d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["&a生成范围&f是生物能够生成的范围.范围越大,它们生成的空间就越大.范围越小,工厂的成本就越低."]
			id: "186593EBCE3FE8D8"
			rewards: [{
				count: 8
				id: "0CFDDB194610C1FE"
				item: "minecraft:blaze_rod"
				type: "item"
			}]
			tasks: [{
				id: "5E1D4330184C7E38"
				item: "minecraft:blaze_rod"
				type: "item"
			}]
			title: "&a生成范围&f"
			x: 4.5d
			y: 2.5d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["红石激活为你的刷怪笼提供了一个开关.没有红石信号时,它不会生成生物."]
			id: "0E02CE4469FCA4C9"
			rewards: [{
				count: 16
				id: "0E37557EFACD595E"
				item: "minecraft:redstone"
				type: "item"
			}]
			tasks: [{
				id: "39E60F2CA2EFAB9E"
				item: "minecraft:comparator"
				type: "item"
			}]
			title: "红石激活"
			x: 3.0d
			y: 0.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["&a激活范围&f是指玩家(你)必须离刷怪笼多近才能使其工作.最低可以是1格,最高可以是48格.它们以刷怪笼为中心形成一个圆形区域,而不是直接的距离.海灵核心和&a龙蛋&f会忽略这一点."]
			id: "3E2A411FF5B4B0E7"
			rewards: [{
				count: 14
				id: "1CFFBC0A625D0737"
				item: "minecraft:prismarine_crystals"
				type: "item"
			}]
			tasks: [{
				id: "75FE61AB74C39108"
				item: "minecraft:prismarine_crystals"
				type: "item"
			}]
			title: "&a激活范围&f"
			x: 1.5d
			y: -1.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["刷怪笼会随机选择一个在最大和最小生成延迟之间的数字来决定何时生成生物.最小生成延迟可以低至100,高至32,767.每个锭会使数值增减10."]
			id: "7A9AE63998BB41FF"
			rewards: [{
				id: "42D8FF9FCD7A2050"
				item: "allthemodium:allthemodium_ingot"
				type: "item"
			}]
			tasks: [{
				id: "5E1EDF7E617E5853"
				item: "allthemodium:allthemodium_ingot"
				type: "item"
			}]
			title: "最小生成延迟"
			x: 3.5d
			y: -2.5d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["最大生成延迟是指生成生物可能花费的最长时间.与最小生成延迟一样,它可以低至100,高至32,767.每个锭会使数值增减10.这些数字是Minecraft的刻,20刻等于&a1秒&f.因此,最快的生成延迟是每5秒生成一次."]
			id: "0D6D45DBA64E612D"
			rewards: [{
				id: "36AC1DA2655AD3D3"
				item: "allthemodium:unobtainium_ingot"
				type: "item"
			}]
			tasks: [{
				id: "1FA39F402B7D0B8B"
				item: "allthemodium:unobtainium_ingot"
				type: "item"
			}]
			title: "最大生成延迟"
			x: 4.5d
			y: -2.5d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["海灵核心将执行海晶碎片的功能,但效果更好.玩家不再需要靠近刷怪笼.只要区块被加载,它就会生成生物."]
			id: "30EB438C66324213"
			rewards: [{
				id: "4961AE3E3A38253D"
				item: "minecraft:conduit"
				type: "item"
			}]
			tasks: [{
				id: "211FBEB0E4FF1FD6"
				item: "minecraft:conduit"
				type: "item"
			}]
			title: "忽略玩家"
			x: 4.0d
			y: 0.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["刷怪笼必备神器.所谓'忽略所有条件'其实是指大多数条件——包括光照等级、生成所需方块及生物群系.但空间条件仍需满足,比如史莱姆需要3x3空间生成,玩家也仍需在附近."]
			id: "40096ED0B04C3EC5"
			rewards: [{
				count: 4
				id: "4D40145162EB9BEC"
				item: "mysticalagradditions:dragon_scale"
				type: "item"
			}]
			tasks: [{
				id: "74B1D74F1C52D05C"
				item: "minecraft:dragon_egg"
				type: "item"
			}]
			title: "忽略所有生成条件"
			x: 5.0d
			y: 0.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["副手持&a石英&f时,主手的&a刷怪箱修改&f物品会产生相反效果.例如石英配&a烈焰棒&f会缩小&a生成范围&f而非扩大,配&a恶魂之泪&f则会减少最大实体数量."]
			id: "167E1474644C9908"
			rewards: [{
				count: 32
				id: "0D2D3CC89ECD4C53"
				item: "minecraft:quartz"
				type: "item"
			}]
			tasks: [{
				id: "20D72B07EE11DDCB"
				item: "minecraft:quartz"
				type: "item"
			}]
			title: "反向日"
			x: 3.0d
			y: -1.0d
		}
		{
			dependencies: ["310969B8FE0A94DE"]
			description: ["羊毛对刷怪笼的唯一作用就是消音.厌倦了刷怪笼的嘈杂声？任何颜色/织数的羊毛都能让它闭嘴!"]
			icon: "minecraft:white_wool"
			id: "1AD87CB3226ED224"
			tasks: [{
				id: "39702880D9133E3E"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "minecraft:wool"
					}
				}
				title: "任意#minecraft:wool"
				type: "item"
			}]
			title: "安静点,你会吵醒怪物的!"
			x: 3.0d
			y: 1.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"根据&eAllTheMods&r整合包&e保留所有权利&r的许可协议,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"此任务默认隐藏,若你看到本提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "7784F866BF65D0C7"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "6EFFD1AF56A5BD02"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "3DD56437ABF3608E"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: -1.0d
		}
	]
	title: "&d神化&f"
}
