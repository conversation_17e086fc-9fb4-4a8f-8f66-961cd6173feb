{
	default_hide_dependency_lines: false
	default_quest_shape: "hexagon"
	filename: "basic_logistics"
	group: "35A88CA0DDED1092"
	icon: "pipez:wrench"
	id: "3DEB33F78398EAD6"
	images: [
		{
			height: 1.5d
			image: "atm:textures/questpics/logistics/basic.png"
			rotation: 0.0d
			width: 5.3300970873786415d
			x: 1.0d
			y: 3.0d
		}
		{
			height: 1.5d
			image: "atm:textures/questpics/logistics/logistics.png"
			rotation: 0.0d
			width: 8.941747572815533d
			x: 10.0d
			y: 3.0d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/logistics/pipez.png"
			rotation: 0.0d
			width: 3.4761904761904763d
			x: 0.0d
			y: -3.0d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/logistics/mekanism.png"
			rotation: 0.0d
			width: 5.642857142857143d
			x: 10.0d
			y: -3.0d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/logistics/integrated-.png"
			rotation: 0.0d
			width: 6.755813953488372d
			x: 4.5d
			y: -2.5d
		}
		{
			height: 1.5d
			image: "atm:textures/questpics/logistics/laserio.png"
			rotation: 0.0d
			width: 7.232142857142857d
			x: 4.5d
			y: 10.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/logistics/pipez_item.png"
			rotation: 90.0d
			width: 4.461077844311378d
			x: -3.5d
			y: -0.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/logistics/pipez_fluid.png"
			rotation: 90.0d
			width: 4.578313253012048d
			x: -2.5d
			y: -0.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/logistics/pipez_energy.png"
			rotation: 90.0d
			width: 4.578313253012048d
			x: -1.5d
			y: -0.5d
		}
		{
			height: 0.9d
			image: "atm:textures/questpics/logistics/meka_transporter.png"
			rotation: 90.0d
			width: 4.75d
			x: 12.0d
			y: -0.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/logistics/meka_cable.png"
			rotation: 90.0d
			width: 4.75d
			x: 10.0d
			y: -0.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/logistics/meka_pipe.png"
			rotation: 90.0d
			width: 4.75d
			x: 11.0d
			y: -0.5d
		}
	]
	order_index: 0
	quest_links: [ ]
	quests: [
		{
			description: ["&e&l管道大师&r是最简洁的&f&l物流&r模组.简洁到整个模组说明都能放在这个任务页面里,还能留出空间介绍其他模组!\\n\\n将特定管道连接到需要运输物品的位置,用管道扳手配置哪边输入、输出或不连接.\\n\\n其他扳手也能与&e&l管道大师&r兼容!\\n\\n(大量使用时可能卡顿,但比&5&l通用机械&r管道好多了)"]
			id: "2329AA0AFBEF1E13"
			rewards: [{
				count: 3
				id: "71FDE5AB12145B69"
				item: "minecraft:iron_block"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "43D2013855BF5F17"
				item: "pipez:wrench"
				type: "item"
			}]
			title: "&l&e管道大师"
			x: 1.0d
			y: 1.0d
		}
		{
			dependencies: ["2329AA0AFBEF1E13"]
			description: [
				"&a基础版&f的&e&l管道&r中最简易的成员——&e物品管道&r.\\n\\n它能将物品从一个容器搬运到另一个容器.可以是箱子间传输,从农场到系统,甚至从采矿机到熔炉.\\n\\n操作非常简单:将&e管道&r连接到需要搬运物品的装置,用扳手设置提取物品的方位即可."
				""
				"{image:atm:textures/questpics/logistics/pipez_item.png width:200 height:50 align:center}"
			]
			id: "36836AD948F96B9F"
			rewards: [{
				count: 16
				id: "22360F382F0BA4CE"
				item: "pipez:item_pipe"
				type: "item"
			}]
			size: 1.1d
			tasks: [{
				id: "04CFDEC2D4C671F2"
				item: "pipez:item_pipe"
				type: "item"
			}]
			title: "&e物品管道系统"
			x: 0.0d
			y: -0.5d
		}
		{
			dependencies: ["2329AA0AFBEF1E13"]
			description: [
				"&b水&r=&b蓝色&r &b水&r=&b流体&r 因此&b蓝色&r=&b流体&r\\n\\n众多模组都有专属&b流体&r,连原版 Minecraft 也拥有&b水&r和&c岩浆&r.你很可能需要运输这些&b流体&r,而&b&a流体管道&f&r正是理想选择.\\n\\n连接无限水源后,就能传输取之不尽的&b水&r!"
				""
				"{image:atm:textures/questpics/logistics/pipez_fluid.png width:200 height:50 align:center}"
			]
			id: "33433A443B65D252"
			min_width: 250
			rewards: [{
				count: 16
				id: "78309F82E7E4AE69"
				item: "pipez:fluid_pipe"
				type: "item"
			}]
			size: 1.1d
			tasks: [{
				id: "2E1254C3F9F6079C"
				item: "pipez:fluid_pipe"
				type: "item"
			}]
			title: "&b&a流体传输管道&fz"
			x: 1.0d
			y: -0.5d
		}
		{
			dependencies: ["2329AA0AFBEF1E13"]
			description: [
				"所有科技模组都需要&c能源&r,无论是FE、EU、RF、OP还是其他缩写!&c&a能量管道&fz&r来者不拒,只管传输!\\n\\n部分机器的&c能源&r输入输出端口有严格限制,使用时请务必注意!"
				""
				"{image:atm:textures/questpics/logistics/pipez_energy.png width:200 height:50 align:center}"
			]
			id: "53EC96FA0E7C4ED4"
			rewards: [{
				count: 16
				id: "6BFD87108A2B3FE8"
				item: "pipez:energy_pipe"
				type: "item"
			}]
			size: 1.1d
			tasks: [{
				id: "17BE12E1047A68A4"
				item: "pipez:energy_pipe"
				type: "item"
			}]
			title: "&c&a能量管道&fz"
			x: 2.0d
			y: -0.5d
		}
		{
			dependencies: [
				"33433A443B65D252"
				"36836AD948F96B9F"
				"53EC96FA0E7C4ED4"
			]
			description: [
				"何必纠结传输内容？&5&a通用管道&fz&r比&c&a能量管道&fz&r更全能,可同时运输&e物品&r、&b流体&r和&c能源&r."
				""
				"{image:atm:textures/questpics/logistics/pipez_universal.png width:200 height:50 align:center}"
			]
			id: "4FBAB3A0FBF8CE2F"
			rewards: [{
				count: 16
				id: "02B1975FE7F3C29A"
				item: "pipez:universal_pipe"
				type: "item"
			}]
			size: 1.1d
			tasks: [{
				id: "1E0BB9A36487B272"
				item: "pipez:universal_pipe"
				type: "item"
			}]
			title: "&5&a通用管道&fz"
			x: 1.0d
			y: -1.5d
		}
		{
			dependencies: ["2329AA0AFBEF1E13"]
			description: [
				"这些&e&l管道系统&r主要源自&5&l&d通用机械&f&r.该模组以&a气体&r形态引入了物质的第四态.其他&e&l管道&r无法运输&a气体&r,若需运输氢气或&a锂&f,必须使用&a&a气体管道&fz&r.\\n\\n(我常难以分辨&5&l&d通用机械&f&r的物品属于&a气体&r、&b流体&r还是&a灌注类型&f.机器界面有时会标注&a气体&r或&b流体&r提示,若无标注可查阅JEI相关配方确认!)"
				""
				"{image:atm:textures/questpics/logistics/pipez_gas.png width:200 height:50 align:center}"
			]
			id: "2F4458E9921DEB86"
			rewards: [{
				count: 16
				id: "51371416132329E3"
				item: "pipez:gas_pipe"
				type: "item"
			}]
			size: 1.1d
			tasks: [{
				id: "7E1C1FB56CFCADF6"
				item: "pipez:gas_pipe"
				type: "item"
			}]
			title: "&a&a气体管道&fz"
			x: -0.5d
			y: 1.0d
		}
		{
			dependencies: ["2329AA0AFBEF1E13"]
			description: ["&e&l管道大师&r有独特的物品过滤系统,但需要至少高级升级模块.放入高级升级后点击添加按钮即可设置过滤器.\\n可添加:物品、标签或NBT作为过滤条件.\\n\\n还能设置目标过滤器.按住Shift键&a右键点击&f容器即可设定.\\n\\n需选择白名单模式(仅过滤物品)或黑名单模式(过滤除外物品)."]
			id: "2A976D41F79C34B1"
			rewards: [{
				id: "11D2714AB566A00B"
				item: "pipez:advanced_upgrade"
				type: "item"
			}]
			shape: "rsquare"
			size: 0.9d
			tasks: [{
				id: "5F9D69AF08F576AB"
				item: "pipez:filter_destination_tool"
				type: "item"
			}]
			x: 2.5d
			y: 1.5d
		}
		{
			dependencies: ["2329AA0AFBEF1E13"]
			description: ["&e&l管道系统&r传输量提升!\\n\\n所有升级都会增加&e&l管道系统&r的传输量,同时提供更多功能选项.\\n\\n基础升级允许通过红石信号控制&e&l管道系统&r.\\n改进升级可设置传输优先级:最近优先、最远优先、轮询或随机.\\n高级升级支持添加过滤器.\\n终极升级虽无新功能,但大幅提升传输速度!"]
			id: "6A962535CD68713E"
			rewards: [{
				id: "4DDD2CDC825A9BE3"
				item: "pipez:advanced_upgrade"
				type: "item"
			}]
			shape: "rsquare"
			size: 0.9d
			tasks: [{
				id: "48CF7A7622423DEA"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "pipez:basic_upgrade"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "pipez:improved_upgrade"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "pipez:ultimate_upgrade"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				type: "item"
			}]
			title: "升级!"
			x: 2.5d
			y: 0.5d
		}
		{
			description: ["&5&l&d通用机械&f&r作为知名大型科技模组,自然配备完善的&l物流系统&r.\\n\\n不同机器对配置要求严格,需通过GUI界面调整才能正常传输物品,随后即可使用&5&l机械管道&r进行运输!\\n\\n注意:这些管道不仅适用于&5&l&d通用机械&f&r,还能配合其他多种机器使用,只需用扳手设置推拉模式即可.\\n\\n(大量使用可能导致卡顿,请谨慎部署)"]
			id: "34CFE1439262AA72"
			rewards: [{
				count: 3
				id: "06BBE2C3833797ED"
				item: "alltheores:steel_block"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "418E333710652BFA"
				item: "mekanism:configurator"
				type: "item"
			}]
			title: "&5&l&d通用机械&f"
			x: 8.0d
			y: 1.0d
		}
		{
			description: ["&3&l&d动态联合/集成动力&f&r的核心就是&f &l物流系统&r!堪称模组物流的典范.\\n\\n专注于物品传输、管理与存储,设计如此精良甚至可能不需要扳手!但建议还是备一个.\\n\\n(这是最不卡顿的管道解决方案,强烈推荐!)"]
			id: "6E05B62A40D5A891"
			rewards: [{
				count: 3
				id: "1F763DED812265B7"
				item: "integrateddynamics:crystalized_menril_block"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "7CAEF421E14B6F2A"
				item: "integrateddynamics:wrench"
				type: "item"
			}]
			title: "&3&l&d动态联合/集成动力&f"
			x: 4.5d
			y: 1.0d
		}
		{
			dependencies: ["34CFE1439262AA72"]
			description: [
				"机械管道是&5&l通用机械&r的流体传输方案.\\n\\n该模组中部分机器需要特定流体,例如&a电解分离器&f需要用水,蒸发厂则用于制取锂."
				""
				"{image:atm:textures/questpics/logistics/meka_pipe.png width:200 height:50 align:center}"
			]
			id: "1834B5C1F8435D7A"
			rewards: [{
				count: 16
				id: "779C78573F83BFFE"
				item: "mekanism:advanced_mechanical_pipe"
				type: "item"
			}]
			size: 1.1d
			subtitle: "流体传输"
			tasks: [{
				id: "42A8569B1AC36DE7"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:basic_mechanical_pipe"
							}
							{
								Count: 1b
								id: "mekanism:advanced_mechanical_pipe"
							}
							{
								Count: 1b
								id: "mekanism:elite_mechanical_pipe"
							}
							{
								Count: 1b
								id: "mekanism:ultimate_mechanical_pipe"
							}
						]
					}
				}
				title: "机械管道"
				type: "item"
			}]
			x: 8.0d
			y: -0.5d
		}
		{
			dependencies: ["34CFE1439262AA72"]
			description: [
				"&5&l&d通用机械&f&r作为科技模组...你居然惊讶它的机器需要能源？"
				""
				"{image:atm:textures/questpics/logistics/meka_cable.png width:200 height:50 align:center}"
			]
			icon_scale: 1.3d
			id: "493D04D954E4FBA0"
			rewards: [{
				count: 16
				id: "6E44439DE3A2B59C"
				item: "mekanism:advanced_universal_cable"
				type: "item"
			}]
			size: 1.1d
			subtitle: "能量系统"
			tasks: [{
				id: "4448DE84ABA737BE"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:basic_universal_cable"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:advanced_universal_cable"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:elite_universal_cable"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:ultimate_universal_cable"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "通用电缆"
				type: "item"
			}]
			x: 9.0d
			y: -0.5d
		}
		{
			dependencies: ["34CFE1439262AA72"]
			description: [
				"逻辑运输器是&5&l通用机械&r远距离传输物品与方块的解决方案.\\n\\n需配置为从箱子/抽屉等容器中提取或推送物品."
				""
				"{image:atm:textures/questpics/logistics/meka_transporter.png width:200 height:50 align:center}"
			]
			id: "30DE4F5BC3EDB555"
			rewards: [{
				count: 16
				id: "67D4792D7C7FF358"
				item: "mekanism:advanced_logistical_transporter"
				type: "item"
			}]
			size: 1.1d
			subtitle: "方块与物品"
			tasks: [{
				id: "337725ECA2C04090"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:basic_logistical_transporter"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:advanced_logistical_transporter"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:elite_logistical_transporter"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:ultimate_logistical_transporter"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "逻辑传送装置"
				type: "item"
			}]
			x: 7.0d
			y: -0.5d
		}
		{
			dependencies: ["34CFE1439262AA72"]
			description: [
				"热量是&5&l&d通用机械&f&r的另一核心要素,为众多机器和多方块结构提供动力.&a热力蒸馏塔&f和&a湮灭反应堆&f都依赖热能运作,而导体正是传输热量的媒介!"
				""
				"{image:atm:textures/questpics/logistics/meka_conductor.png width:200 height:50 align:center}"
			]
			icon_scale: 1.3d
			id: "316B513FE568EF57"
			rewards: [{
				count: 16
				id: "3E64DF44CA2F9908"
				item: "mekanism:advanced_thermodynamic_conductor"
				type: "item"
			}]
			size: 1.1d
			subtitle: "热能系统"
			tasks: [{
				id: "79E961F671475850"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:basic_thermodynamic_conductor"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:advanced_thermodynamic_conductor"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:elite_thermodynamic_conductor"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanism:ultimate_thermodynamic_conductor"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "热力传导器"
				type: "item"
			}]
			x: 8.5d
			y: -1.5d
		}
		{
			dependencies: ["34CFE1439262AA72"]
			description: [
				"显然原版&2&lMinecraft&r的物品/方块/流体还不够,加上热能与能源仍不满足.\\n\\n&5&l&d通用机械&f&r其余内容通过加压管道运输:气体是主要运输对象(如氢/氧)；&a灌注类型&f类似红石灌注/煤炭灌注；矿浆用于矿石加工；染料色素则是改变物品颜色的本质要素."
				""
				"{image:atm:textures/questpics/logistics/meka_tube.png width:200 height:50 align:center}"
			]
			icon_scale: 1.3d
			id: "4BC2D7FCDD792DBA"
			rewards: [{
				count: 16
				id: "3217F5BBC9DAF0EC"
				item: "mekanism:advanced_pressurized_tube"
				type: "item"
			}]
			size: 1.1d
			subtitle: "气体及其他"
			tasks: [{
				id: "62228D4E878D28AE"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:basic_pressurized_tube"
							}
							{
								Count: 1b
								id: "mekanism:advanced_pressurized_tube"
							}
							{
								Count: 1b
								id: "mekanism:elite_pressurized_tube"
							}
							{
								Count: 1b
								id: "mekanism:ultimate_pressurized_tube"
							}
						]
					}
				}
				title: "加压管道"
				type: "item"
			}]
			x: 7.5d
			y: -1.5d
		}
		{
			dependencies: ["6E05B62A40D5A891"]
			description: ["逻辑电缆与其他&e&l管道系统&r或&5&l机械管道&r不同.普通管道类似漏斗,物品需经过管道中转,而逻辑电缆直接连接容器,大幅减少卡顿.\\n\\n仅用于连接输入口与接口."]
			icon_scale: 1.5d
			id: "6112956E19017D2D"
			rewards: [{
				count: 16
				id: "673E97D0F77771B4"
				item: "integrateddynamics:cable"
				type: "item"
			}]
			shape: "circle"
			tasks: [{
				id: "5F7BDDC673BAA500"
				item: "integrateddynamics:cable"
				type: "item"
			}]
			x: 3.5d
			y: 0.0d
		}
		{
			description: ["&n&f&l物流系统&r:\\n指物品或资源的运输、管理与存储体系.\\n\\n在模组版Minecraft中同样如此!当需要运输数以千计(追求&e&l稳定&r时甚至百万级)的物品时,必须建立从工厂、机器、农场到箱子的运输网络——这正是&l&f物流系统&r的用武之地."]
			id: "6CD7D99AA102A5C3"
			rewards: [{
				count: 2
				id: "5BC324F4449F0732"
				item: "minecraft:chest"
				type: "item"
			}]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "48A30B08A090BD75"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:tools/wrench"
					}
				}
				title: "扳手工具"
				type: "item"
			}]
			title: "&l物流系统"
			x: 4.5d
			y: 3.0d
		}
		{
			dependencies: ["6E05B62A40D5A891"]
			description: ["变量卡是启动物品输入等交互的基础.将其放入输入器的卡槽即可激活功能.\\n\\n通过+按钮可编辑所有参数:输入量、物品槽位、频道等.\\n\\n若只需基础运输功能,放入卡片后简单调整传输上限即可."]
			id: "03C40D6A5D722543"
			rewards: [{
				count: 4
				id: "112EF88F5897BC33"
				item: "integrateddynamics:variable_transformer_input"
				type: "item"
			}]
			shape: "rsquare"
			size: 0.9d
			tasks: [{
				id: "5789E685C4B54C1B"
				item: "integrateddynamics:variable"
				type: "item"
			}]
			x: 5.5d
			y: 0.0d
		}
		{
			dependencies: [
				"6112956E19017D2D"
				"03C40D6A5D722543"
			]
			description: [
				"要让物品导入器工作,请将其放置在需要抽取物品的容器上.接着将接口放置在第二个容器上,并用逻辑电缆连接两者.最后将变量卡插入导入器并根据需要调整设置.\\n\\n可设置为以整数上限(超过20亿)导入物品."
				""
				"{image:atm:textures/questpics/logistics/id_item.png width:200 height:50 align:center}"
			]
			icon_scale: 1.5d
			id: "6F152402756DA35E"
			rewards: [
				{
					count: 3
					id: "510F6E6940CEFB39"
					item: "integratedtunnels:part_interface_item"
					type: "item"
				}
				{
					count: 3
					id: "47F3BA6276A2EA34"
					item: "integratedtunnels:part_importer_item"
					type: "item"
				}
			]
			size: 1.1d
			tasks: [
				{
					id: "6479D685217CC64D"
					item: "integratedtunnels:part_importer_item"
					type: "item"
				}
				{
					id: "7A8D6779B7C6B99D"
					item: "integratedtunnels:part_interface_item"
					type: "item"
				}
			]
			title: "&f物品输入系统"
			x: 3.5d
			y: -1.0d
		}
		{
			dependencies: [
				"6112956E19017D2D"
				"03C40D6A5D722543"
			]
			description: [
				"操作方式与物品导入器类似:将&e流体导入器&r安装在存有液体的储罐上,再将&e流体接口&r安装在目标位置并用逻辑电缆连接.最后像之前一样插入变量卡即可!\\n\\n同样能以整数上限抽取液体.\\n\\n注意:该装置不会在世界中放置或移除流体源,仅能在储罐间转移流体."
				""
				"{image:atm:textures/questpics/logistics/id_fluid.png width:200 height:50 align:center}"
			]
			icon_scale: 1.5d
			id: "034F2CDF0830254B"
			rewards: [
				{
					count: 3
					id: "44416071DD70A203"
					item: "integratedtunnels:part_interface_fluid"
					type: "item"
				}
				{
					count: 3
					id: "7F9CF03DD0B1E2D3"
					item: "integratedtunnels:part_importer_fluid"
					type: "item"
				}
			]
			size: 1.1d
			tasks: [
				{
					id: "38AB1D04DBB036B9"
					item: "integratedtunnels:part_importer_fluid"
					type: "item"
				}
				{
					id: "1E13BEB52F3D087C"
					item: "integratedtunnels:part_interface_fluid"
					type: "item"
				}
			]
			title: "&e流体输入系统"
			x: 4.5d
			y: -1.0d
		}
		{
			dependencies: [
				"6112956E19017D2D"
				"03C40D6A5D722543"
			]
			description: [
				"只需将&3能量导入器&r安装在&a移动能源&f设备上,再用逻辑电缆连接至&3能量接口&r!插入并编辑变量卡后,你就能像专家一样传输能量了!\\n\\n需要提醒的是,该装置在与&a通量接入点&f进行导入/导出时可能出现问题,使用时请注意."
				""
				"{image:atm:textures/questpics/logistics/id_energy.png width:200 height:50 align:center}"
			]
			icon_scale: 1.5d
			id: "76CECFB244F39F18"
			rewards: [
				{
					count: 3
					id: "47C5525DAF15361A"
					item: "integratedtunnels:part_interface_energy"
					type: "item"
				}
				{
					count: 3
					id: "7792BBAEC038F0C6"
					item: "integratedtunnels:part_importer_energy"
					type: "item"
				}
			]
			size: 1.1d
			tasks: [
				{
					id: "4618703B49BDB077"
					item: "integratedtunnels:part_importer_energy"
					type: "item"
				}
				{
					id: "11F2F4CB9E93471A"
					item: "integratedtunnels:part_interface_energy"
					type: "item"
				}
			]
			title: "&3能量输入系统"
			x: 5.5d
			y: -1.0d
		}
		{
			description: ["&c&l激光IO&r是DireWolf对&a&l&d末影接口&f&r&l物流系统&r的延续.\\n\\n通过&c激光&r传输物品——谁能拒绝&c激光&r的诱惑呢？!\\n\\n一切从&a逻辑芯片&f开始运作."]
			id: "5F1218CF8EFC607B"
			rewards: [{
				count: 4
				id: "7D27118147D79F00"
				item: "laserio:logic_chip"
				type: "item"
			}]
			shape: "octagon"
			size: 1.5d
			tasks: [{
				id: "652F3AA1A8877897"
				item: "laserio:logic_chip"
				type: "item"
			}]
			title: "&c&l激光IO系统"
			x: 4.5d
			y: 6.5d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["用于连接&c&a激光节点&f&r与连接器.虽非核心部件,但为保持扳手主题的一致性而存在.\\n\\n也可作为普通扳手进行其他配置!"]
			id: "7BC8F50A89A3BE1A"
			rewards: [{
				id: "4665F92118ECF273"
				item: "laserio:laser_node"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "0D82B98F49ECDE84"
				item: "laserio:laser_wrench"
				type: "item"
			}]
			x: 4.5d
			y: 5.0d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["这才是真正的&c&l激光IO&r核心!&c&a激光节点&f&r是实际放置功能卡片的装置.\\n\\n它们能从上下左右四个基本方向的相邻容器中搬运物品.若要连接远端容器,需要第二个&c节点&r,用扳手按住Shift键&a右击&f第一个节点再&a右击&f第二个节点进行连接.\\n\\n将卡片放入对应方向的插槽,还可安装&a节点超频器&f来提升运作速度."]
			icon_scale: 2.0d
			id: "5C0F6B1C93A52113"
			rewards: [{
				id: "1CDCDE301AFE1521"
				item: "laserio:laser_node"
				type: "item"
			}]
			tasks: [{
				id: "132F43079A756548"
				item: "laserio:laser_node"
				type: "item"
			}]
			x: 3.0d
			y: 6.5d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["基础过滤器的使用...非常简单!放入物品即可建立过滤规则.\\n\\n可切换白名单(仅允许指定物品)或黑名单(仅拒绝指定物品)模式.\\n\\n还能设置是否匹配&aNBT数据&f.若启用匹配,将检测附魔、耐久度甚至生物容器内的实体等NBT信息；若不匹配则无视NBT处理同类物品."]
			icon_scale: 1.5d
			id: "47A2BEFB11F4D581"
			rewards: [{
				count: 2
				id: "1D06FA229ABEDA31"
				item: "laserio:logic_chip"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "1D0BCF8F310F9F31"
				item: "laserio:filter_basic"
				type: "item"
			}]
			x: 6.0d
			y: 6.5d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["使用&c&l激光IO&r运输&2物品&r需要&2物品卡片&r.\\n\\n将抽取卡置于&c&a激光节点&f&r的输入面,插入卡置于输出面.\\n\\n抽取卡有多重设置:当存在多个输入源时,&a轮询&f模式会按顺序抽取物品,而非默认的最近容器优先."]
			icon_scale: 1.5d
			id: "4C41FD926F31180B"
			rewards: [{
				count: 2
				id: "19AE8583E62E2168"
				item: "laserio:card_item"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "3E4FC405FD55ED51"
				item: "laserio:card_item"
				type: "item"
			}]
			title: "&2物品卡片"
			x: 4.0d
			y: 8.0d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["功能与&2物品卡片&r完全相同,需要抽取卡和插入卡配合使用.不过这次是从储存&9流体&r的罐体间运输.注意:无法在世界中放置或移除&9流体&r源方块."]
			icon_scale: 1.5d
			id: "1C70D739CE464E25"
			rewards: [{
				count: 2
				id: "48537D0800E28E28"
				item: "laserio:card_fluid"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "65A892F50FB762AA"
				item: "laserio:card_fluid"
				type: "item"
			}]
			title: "&9&a流体卡片&f"
			x: 3.0d
			y: 8.0d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["总得想办法传输&e能量&r,最好能精准送达.其运作方式与运输&2物品&r、&9流体&r和&5化学品&r完全一致!"]
			icon_scale: 1.5d
			id: "52F85CED20C8B1E9"
			rewards: [{
				count: 2
				id: "28B75342412D614B"
				item: "laserio:card_energy"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "346CFB97F9BFCFC2"
				item: "laserio:card_energy"
				type: "item"
			}]
			title: "&e&a能量卡&f"
			x: 5.0d
			y: 8.0d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["厌倦了到处铺设&4红石&r线路？既然能无线传输FE能量,为何不能无线传输&4红石&r信号？&c&l激光IO&r让这成为现实.用&4输入卡&r接收&4&a红石信号&f&r,再通过&4输出卡&r将&4信号&r传递到别处!"]
			icon_scale: 1.5d
			id: "47768B1FFD57D56E"
			rewards: [{
				count: 2
				id: "778135590CF0FE97"
				item: "laserio:card_redstone"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "333634D36B6549F5"
				item: "laserio:card_redstone"
				type: "item"
			}]
			title: "&4&a红石卡&f"
			x: 6.0d
			y: 8.0d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["当运用&c&l激光IO&r进行&l物流管理&r时,大量卡片会占用背包空间.这时就需要&a物流卡暂存器&f!\\n\\n它能集中存放多张卡片,打开&a激光节点&f时会同步显示暂存器界面,操作非常便捷!"]
			id: "617695A9CA45D88F"
			optional: true
			rewards: [{
				count: 2
				id: "713279F21DD21446"
				item: "laserio:logic_chip"
				type: "item"
			}]
			tasks: [{
				id: "035E7A7567049C78"
				item: "laserio:card_holder"
				type: "item"
			}]
			x: 5.0d
			y: 9.0d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["每张卡片都需要单独设置参数？太麻烦了!直接用克隆器复制吧!\\n\\n用克隆器&a左击&f源卡片复制数据,再&a右击&f目标卡片即可粘贴配置!"]
			id: "308935C85364B04D"
			optional: true
			rewards: [{
				count: 2
				id: "71AB05CB39E60E9B"
				item: "laserio:logic_chip"
				type: "item"
			}]
			shape: "hexagon"
			tasks: [{
				id: "4637853BB5E04D77"
				item: "laserio:card_cloner"
				type: "item"
			}]
			x: 6.0d
			y: 9.0d
		}
		{
			dependencies: ["5C0F6B1C93A52113"]
			description: ["需要远距离连接&c&a激光节点&f&r？试试连接器!用扳手按常规方式操作即可."]
			icon_scale: 1.5d
			id: "7EE27C3908008E20"
			optional: true
			rewards: [{
				id: "4839A441EAD03919"
				item: "laserio:laser_connector"
				type: "item"
			}]
			tasks: [{
				id: "09AA5520AC0CCC24"
				item: "laserio:laser_connector"
				type: "item"
			}]
			x: 2.0d
			y: 6.5d
		}
		{
			dependencies: ["47A2BEFB11F4D581"]
			description: ["1... 2... 这里还有个4.\\n\\n设置数量时先放入物品,然后可以左键或右键&a点击切换&f数值.按住Shift键操作可以以10为单位增减.设定好数量后将其存入卡片!\\n\\n卡片会等待物品达到设定数量才开始提取,并始终在第一个容器中保留该数量物品."]
			icon_scale: 1.5d
			id: "06767BA0AFE8C1EE"
			rewards: [{
				count: 2
				id: "7CD74A2417E9F801"
				item: "laserio:logic_chip"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "298C94E654593532"
				item: "laserio:filter_count"
				type: "item"
			}]
			x: 7.0d
			y: 6.5d
		}
		{
			dependencies: ["47A2BEFB11F4D581"]
			description: ["标签(Tags)是Minecraft中物品分类的重要机制,我们自然可以利用它来制作过滤器!\\n\\n放入物品后选择标签,或直接输入已知的标签名称.\\n\\n例如通过工具标签从系统中提取装备就非常实用.\\n\\n(输入/kubejs hand可查看手中物品的所属标签)"]
			icon_scale: 1.5d
			id: "3065EA195D0A36BA"
			rewards: [{
				count: 2
				id: "018435A9D4B7DA05"
				item: "laserio:logic_chip"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "0BF7D47F1BF18EA5"
				item: "laserio:filter_tag"
				type: "item"
			}]
			x: 7.0d
			y: 7.5d
		}
		{
			dependencies: ["47A2BEFB11F4D581"]
			description: ["&a模组过滤器&f是基础过滤器之一.放入物品后通过白名单/黑名单模式运作,但匹配的是整个模组的所有物品而非单一物品.\\n\\n例如&d神化&f宝石过滤器只允许&d神化&f模组的物品,&d通用机械&f合金过滤器仅接受&d通用机械&f模组物品."]
			icon_scale: 1.5d
			id: "2A6900E87DE8BAE0"
			rewards: [{
				count: 2
				id: "36921E4FDCF84830"
				item: "laserio:logic_chip"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "1D8C41C20C557A21"
				item: "laserio:filter_mod"
				type: "item"
			}]
			x: 7.0d
			y: 5.5d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["卡片存在传输限制:物品提取卡每次最多转移8个物品,流体提取卡仅&a5桶&f,化工与能源卡的限额更是玄学.\\n\\n但在模组化&2&l我的世界&r中,我们需要超频突破这些限制!\\n\\n将超频元件放入卡片右上角插槽即可提升传输上限!\\n\\n最多可安装4个."]
			id: "046C417B2ADF3AA7"
			optional: true
			rewards: [{
				count: 3
				id: "28A66D357ACC9E3E"
				item: "laserio:overclocker_card"
				type: "item"
			}]
			tasks: [{
				id: "5983560F1C762FAC"
				item: "laserio:overclocker_card"
				type: "item"
			}]
			x: 4.0d
			y: 9.0d
		}
		{
			dependencies: ["5F1218CF8EFC607B"]
			description: ["不仅卡片有限制,节点本身也能超频!\\n\\n将超频元件放入&c节点&r右下角插槽可提升其操作速度.\\n\\n最多可安装8个."]
			id: "0BB562FB44B0AAA7"
			optional: true
			rewards: [{
				count: 3
				id: "47DD8A893C7F9E35"
				item: "laserio:overclocker_node"
				type: "item"
			}]
			tasks: [{
				id: "6EF656AC41CC4D7E"
				item: "laserio:overclocker_node"
				type: "item"
			}]
			x: 3.0d
			y: 9.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r制作,专用于AllTheMods整合包."
				"根据&eAllTheMods&r系列整合包的&e保留所有权利&r许可协议,未经明确授权不得在其他公开整合包中使用本任务."
				""
				""
				""
				"此为隐藏任务,若你看到此提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "0C2FB631B750646A"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "6AED5128FB4ACF75"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "0C7B081EDB19B519"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: 3.0d
			y: 5.0d
		}
	]
	title: "基础物流"
}
