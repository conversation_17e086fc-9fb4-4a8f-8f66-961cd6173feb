{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "extreme_voltage"
	group: "1DA67E79B40AB130"
	icon: "gtceu:micro_processor_computer"
	id: "489F28A71282B3E7"
	order_index: 5
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"我们就当一切正常"
				""
				"在&5超高压&r环境下,我们处理的是超过1000伏特的电压!本质上我们就是电力抢修工,但请千万别在家尝试"
			]
			id: "5241693278027FFD"
			rewards: [
				{
					count: 4
					id: "2C401089E546A2F9"
					item: "gtceu:ram_chip"
					random_bonus: 8
					type: "item"
				}
				{
					count: 4
					id: "7406D49AC20EEC56"
					item: "gtceu:diode"
					random_bonus: 8
					type: "item"
				}
				{
					count: 2
					id: "0ED1F7FD56E7CF43"
					item: "gtceu:plastic_printed_circuit_board"
					random_bonus: 4
					type: "item"
				}
			]
			size: 1.5d
			subtitle: "一切正常,&5超高压&r下都正常"
			tasks: [{
				id: "17E7D25D83B98E80"
				item: "gtceu:micro_processor_computer"
				type: "item"
			}]
			x: -2.5d
			y: 1.0d
		}
		{
			dependencies: ["6805CC8AD6010F33"]
			description: [
				"&c&l停!禁止通过!也别想收取200美元!"
				""
				"你已经完成本章所有其他任务了吗？"
				""
				"哦,完成了？"
				""
				"干得好,那就继续前进吧!"
			]
			id: "2E47C92E3E8D826A"
			rewards: [{
				exclude_from_claim_all: true
				id: "17353B52C1E760AD"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			size: 1.5d
			subtitle: "向IV级进发!"
			tasks: [{
				id: "23FFAB356FE1CA42"
				item: "gtceu:micro_processor_mainframe"
				type: "item"
			}]
			x: 8.0d
			y: 1.0d
		}
		{
			dependencies: [
				"71D4B261AF487062"
				"7ACC4E777A75E043"
			]
			id: "0262E0D49A12F817"
			rewards: [{
				count: 2
				id: "1E0A51D6BF3DF098"
				item: "gtceu:gallium_ingot"
				random_bonus: 2
				type: "item"
			}]
			subtitle: "升级!"
			tasks: [{
				id: "4CB14D90753F710B"
				item: "gtceu:smd_transistor"
				type: "item"
			}]
			x: 5.0d
			y: 6.0d
		}
		{
			dependencies: [
				"71D4B261AF487062"
				"7ACC4E777A75E043"
			]
			id: "6445DEC19DD55A34"
			rewards: [{
				count: 2
				id: "1DCF548413B4AAB7"
				item: "gtceu:tantalum_ingot"
				random_bonus: 2
				type: "item"
			}]
			subtitle: "合成配方"
			tasks: [{
				id: "466FF2FF107AFDFA"
				item: "gtceu:smd_resistor"
				type: "item"
			}]
			x: 5.0d
			y: 5.0d
		}
		{
			dependencies: [
				"71D4B261AF487062"
				"7ACC4E777A75E043"
			]
			id: "3D9B7855B5616DCE"
			rewards: [{
				count: 3
				id: "4AC575760AE4F1BF"
				item: "gtceu:polyvinyl_chloride_foil"
				random_bonus: 3
				type: "item"
			}]
			subtitle: "电路"
			tasks: [{
				id: "0774AA33B0CA149F"
				item: "gtceu:smd_capacitor"
				type: "item"
			}]
			x: 5.0d
			y: 4.0d
		}
		{
			dependencies: [
				"71D4B261AF487062"
				"7C134F7838C23059"
			]
			id: "0D9D1462C676C050"
			rewards: [
				{
					count: 8
					id: "7A8C4DEF04A1497F"
					item: "gtceu:fine_platinum_wire"
					random_bonus: 8
					type: "item"
				}
				{
					count: 2
					id: "07FD547CFCD64325"
					item: "gtceu:small_gallium_arsenide_dust"
					random_bonus: 2
					type: "item"
				}
			]
			subtitle: "时间"
			tasks: [{
				id: "23F7124635FE5FC8"
				item: "gtceu:smd_diode"
				type: "item"
			}]
			x: 5.0d
			y: 2.0d
		}
		{
			dependencies: [
				"71D4B261AF487062"
				"7ACC4E777A75E043"
			]
			id: "11AAA1DCB452DFFC"
			rewards: [{
				count: 2
				id: "3ED7AAC94A1DF8F8"
				item: "gtceu:tantalum_ingot"
				random_bonus: 2
				type: "item"
			}]
			subtitle: "用于"
			tasks: [{
				id: "210478A03F540A6B"
				item: "gtceu:smd_inductor"
				type: "item"
			}]
			x: 5.0d
			y: 3.0d
		}
		{
			dependencies: [
				"6D082AE4CF9A56DC"
				"7B27B87A520E38B0"
				"5241693278027FFD"
			]
			description: [
				"在&a&a电力高炉&f&r中将少量&d镁粉&r与你的&a四氯化钛&f混合,会得到一块&l滚烫&r的金属锭"
				""
				"镁可通过&e矿物处理&r多种途径获取,但我个人最喜欢&e电解&r黑曜石粉"
				""
				"你可以通过&e电解&r获得的&a氯化镁&f来回收镁和氯"
				""
				"在使用前,需将金属锭放入&a&a真空冷冻机&f&r冷却"
				""
				"&l&e注意:&r&r请务必仔细核对金属锭配方所需的温度条件,这个配方需要比铜镍线圈更高级的&b坎塔尔合金线圈&r"
			]
			icon: "gtceu:titanium_ingot"
			id: "78A295D4D1A21BCA"
			min_width: 300
			rewards: [{
				count: 4
				id: "12436A8F60A5F9E1"
				item: "gtceu:magnesium_dust"
				random_bonus: 4
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "7AB26B5B4167E5EC"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:ingots/titanium"
					}
				}
				title: "&a钛锭&f"
				type: "item"
			}]
			x: -2.5d
			y: -2.75d
		}
		{
			description: ["&d铂金线™&r我们稍后会详细介绍,现在先庆幸铂金储量丰富吧"]
			icon: "alltheores:platinum_ingot"
			id: "7C134F7838C23059"
			shape: "square"
			tasks: [{
				id: "5C915E839949FED6"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:ingots/platinum"
					}
				}
				title: "&a铂锭&f"
				type: "item"
			}]
			x: 7.0d
			y: 2.0d
		}
		{
			dependencies: ["33DAA602B385A55D"]
			description: [
				"遇到电压问题了？这种导线能帮上忙!"
				""
				"这种导线具有&3超导&r特性,无论传输距离多远都不会损耗电压"
			]
			id: "0F6FB959B352121D"
			rewards: [{
				exclude_from_claim_all: true
				id: "039842C09E329415"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [{
				id: "5F3BEAA5602BA0CD"
				item: "gtceu:mercury_barium_calcium_cuprate_single_wire"
				type: "item"
			}]
			x: -0.5d
			y: -4.0d
		}
		{
			description: [
				"&e电解&r&b钽铁矿粉&r可获得&d钽粉&r"
				""
				"钽铁矿处理过程中也会产生该副产品!"
			]
			id: "7ACC4E777A75E043"
			rewards: [{
				count: 8
				id: "7A19A486962CBAB5"
				item: "gtceu:raw_tantalite"
				random_bonus: 4
				type: "item"
			}]
			shape: "square"
			tasks: [{
				icon: "gtceu:tantalum_dust"
				id: "309F610CA713BB20"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:dusts/tantalum"
					}
				}
				title: "&a钽粉&f"
				type: "item"
			}]
			x: 7.0d
			y: 3.5d
		}
		{
			dependencies: ["60736D37F7705B39"]
			description: [
				"又要升级&a电力高炉&r？没错!"
				""
				"这将允许我们熔炼更高级的金属锭,比如HV超导金属!"
			]
			id: "5500BDFA7A5D04EB"
			rewards: [{
				exclude_from_claim_all: true
				id: "7DDBA3FEF3F6198A"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [{
				count: 16L
				id: "261D5EF68A314309"
				item: "gtceu:nichrome_coil_block"
				type: "item"
			}]
			x: 1.2000000000000002d
			y: -2.5d
		}
		{
			dependencies: [
				"5241693278027FFD"
				"0D573979B25FAC48"
			]
			description: [
				"&a&a蒸馏塔&f&r是&d&a原油处理&f&r的基础设施,能将石油转化为更多有用形态"
				""
				"建造蒸馏塔时,需确保其高度为&c1层+流体输出口数量&r才能正确处理配方"
				""
				"&e例如&r:若配方产出5种流体,则蒸馏塔至少需要6层高并配备5个输出仓"
				""
				"最大结构尺寸为3x3x13"
				""
				"&b注意:&r关于多方块结构建造方法,请查看JEI中多方块控制器的&3多方块信息&r页面"
				""
				"获取原油的方式由你决定!以下任务列出了几种可选方案"
			]
			icon: "gtceu:distillation_tower"
			id: "3BEDF19CD79D53D5"
			min_width: 300
			rewards: [{
				exclude_from_claim_all: true
				id: "221D254BFC0257F7"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			subtitle: "啊终于,真正的流体处理"
			tasks: [{
				id: "2A5387EF0991FC1D"
				item: "gtceu:distillation_tower"
				type: "item"
			}]
			x: -2.5d
			y: 3.5d
		}
		{
			dependencies: ["5241693278027FFD"]
			description: [
				"当金属锭在&c1800K&r以上高温加热后,会因过热而无法用普通冷却槽处理"
				""
				"这时就需要&e&a真空冷冻机&f&r来满足所有冷却需求!"
				""
				"该机器不仅能冷却炽热金属锭,还能将多种气体转化为液体"
			]
			icon: "gtceu:vacuum_freezer"
			id: "6D082AE4CF9A56DC"
			rewards: [{
				exclude_from_claim_all: true
				id: "108EAAF040BA7990"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [
				{
					id: "74FCADB757C762B9"
					item: "gtceu:vacuum_freezer"
					type: "item"
				}
				{
					id: "5F18E278ABC418FE"
					item: "gtceu:auto_maintenance_hatch"
					optional_task: true
					type: "item"
				}
			]
			x: -0.5d
			y: -1.0d
		}
		{
			dependencies: [
				"0911814AFFFCF885"
				"6D082AE4CF9A56DC"
			]
			description: [
				"这块金属锭可真烫!快放进&a&a真空冷冻机&f冷却"
				""
				"需在&a电弧炉&f上安装Kanthal线圈方块"
			]
			id: "60736D37F7705B39"
			tasks: [{
				id: "0EEE381F78288115"
				item: "gtceu:nichrome_ingot"
				type: "item"
			}]
			x: 1.2000000000000002d
			y: -1.0d
		}
		{
			description: ["4份镍粉与1份铬粉在&e搅拌机&r中混合即可制成镍铬合金!"]
			id: "0911814AFFFCF885"
			rewards: [{
				id: "2DF63FCEAFE5F977"
				item: "gtceu:chromium_dust"
				random_bonus: 2
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "4127FE4279C4DF22"
				item: "gtceu:nichrome_dust"
				type: "item"
			}]
			x: 1.2000000000000002d
			y: 0.10000000000000003d
		}
		{
			dependencies: ["5241693278027FFD"]
			description: [
				"这将解锁唯一配方——&1IV级电路&r!"
				""
				"不过别急着制作电路以为能跳过这个阶段"
			]
			id: "6805CC8AD6010F33"
			rewards: [{
				exclude_from_claim_all: true
				id: "3B422B9AD9E7D9FE"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [{
				id: "271929662CF5AC0D"
				item: "gtceu:hv_circuit_assembler"
				type: "item"
			}]
			x: 3.0d
			y: 1.0d
		}
		{
			dependencies: ["5241693278027FFD"]
			description: [
				"升级&e组装机&r后可制造表面贴装器件(简称&bSMD&r),能降低电路元件成本!"
				""
				"同时解锁更高阶的能量仓,是时候升级多方块机器了!"
			]
			id: "71D4B261AF487062"
			rewards: [{
				exclude_from_claim_all: true
				id: "5F111E8360C6F822"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [{
				id: "443C9F78D6B49249"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:hv_assembler"
							}
							{
								Count: 1b
								id: "gtceu:ev_assembler"
							}
						]
					}
				}
				title: "高压或极压组装机"
				type: "item"
			}]
			x: 3.0d
			y: 3.5d
		}
		{
			id: "40DC4C8DF9616F97"
			shape: "square"
			tasks: [{
				id: "3A68557D0E516D4E"
				item: "gtceu:black_bronze_dust"
				type: "item"
			}]
			x: -1.0d
			y: 5.5d
		}
		{
			description: [
				"在&e化学反应&r中将钠粉与钾粉结合可得...钠钾合金"
				""
				"&b钾粉&r可通过在&e&a化学反应器&f&r中用盐水和恶魂之泪制作微小颗粒获得"
			]
			id: "1E8D496B1CC4F69B"
			shape: "square"
			tasks: [{
				id: "2C4A50B1A9AF4A84"
				item: "gtceu:sodium_potassium_bucket"
				type: "item"
			}]
			x: 3.0d
			y: 5.5d
		}
		{
			dependencies: ["40DC4C8DF9616F97"]
			id: "61A16C4CF0B7A9B2"
			rewards: [{
				count: 3
				id: "06CB661C0124B64B"
				item: "alltheores:steel_dust"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "0E66CBEB7C8E7539"
				item: "gtceu:black_steel_ingot"
				type: "item"
			}]
			x: 0.0d
			y: 4.5d
		}
		{
			dependencies: [
				"61A16C4CF0B7A9B2"
				"1E8D496B1CC4F69B"
				"71D4B261AF487062"
				"5D58E5CB4F4BDAD5"
			]
			description: [
				"请注意:每个能量仓可接收2安培电流"
				""
				"许多机器支持两个(或更多)能量仓,意味着可输入4安培电流以解锁更高电压阶层的加工!"
			]
			id: "0D573979B25FAC48"
			rewards: [{
				exclude_from_claim_all: true
				id: "2D0E700C7A93D217"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [{
				id: "5E6E95015D19F4FE"
				item: "gtceu:hv_energy_input_hatch"
				type: "item"
			}]
			x: 1.0d
			y: 3.5d
		}
		{
			dependencies: ["7D10AA6EF07087B5"]
			description: ["为你的收藏再添一枚透镜!如果之前每个透镜都配了激光刻录机的话"]
			id: "5D58E5CB4F4BDAD5"
			rewards: [{
				count: 2
				id: "3FE559E167225891"
				item: "gtceu:silicon_wafer"
				random_bonus: 2
				type: "item"
			}]
			shape: "circle"
			tasks: [
				{
					id: "3459093D8C9EAE43"
					item: "gtceu:orange_glass_lens"
					type: "item"
				}
				{
					id: "48B9848EBF024C1A"
					item: "gtceu:lpic_wafer"
					type: "item"
				}
				{
					id: "20363BBDFECB5221"
					item: "gtceu:lpic_chip"
					type: "item"
				}
			]
			title: "&a低能耗&f集成芯片"
			x: 1.0d
			y: 4.5d
		}
		{
			dependencies: [
				"5500BDFA7A5D04EB"
				"7220835BD1F8EBA7"
				"6D082AE4CF9A56DC"
			]
			description: ["这确实是块超高温锭!需在&a电弧炉&f上安装&b镍铬合金线圈方块&r才能加工!"]
			id: "33DAA602B385A55D"
			tasks: [{
				id: "1E5B05165E5A0236"
				item: "gtceu:mercury_barium_calcium_cuprate_ingot"
				type: "item"
			}]
			x: -0.5d
			y: -2.5d
		}
		{
			description: [
				"&d钡粉&r可通过&e电解&r&b重晶石粉&r获得"
				""
				"&d水银&r来自&e离心&r红石粉或朱砂粉"
				""
				"急需&d钙粉&r？随时可以&e电解&r骨粉!"
			]
			id: "7220835BD1F8EBA7"
			rewards: [{
				count: 3
				id: "39F1DE4C40F09307"
				item: "gtceu:barium_dust"
				random_bonus: 2
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "04D48A6EC98F1CAD"
				item: "gtceu:mercury_barium_calcium_cuprate_dust"
				type: "item"
			}]
			x: 1.2000000000000002d
			y: -4.0d
		}
		{
			description: [
				"希望你有持续进行矿物采集与加工!"
				""
				"获取&d&a金红石粉&f&r的多种途径:"
				"&e电解&r15份铝土矿粉"
				"[ \"\", { \"text\": \"EBF \", \"color\": \"green\", \"hoverEvent\": { \"action\": \"show_text\", \"contents\": { \"text\": \"Electric Blast Furnace\" } } }, { \"text\": \"10 Ilmenite and 4 Carbon together\" }]"
				"&6矿物处理&r钛铁矿或铝土矿概率产出"
				"&e&a化学浸洗器&f&r中用&3&a过硫酸钠&f&r处理铝锭可提高产出概率"
				""
				"&c注意:&r机器等级越高,概率产出率越大"
			]
			id: "03B5D467E76C5B8A"
			min_width: 300
			rewards: [
				{
					count: 4
					id: "491962ED7FE39994"
					item: "gtceu:raw_bauxite"
					random_bonus: 4
					type: "item"
				}
				{
					count: 4
					id: "3B0A0D8407FA285B"
					item: "gtceu:raw_ilmenite"
					random_bonus: 4
					type: "item"
				}
			]
			shape: "square"
			subtitle: "更像是无用粉尘"
			tasks: [{
				id: "2B956FA18DAF1BD8"
				item: "gtceu:rutile_dust"
				type: "item"
			}]
			x: -2.5d
			y: -6.0d
		}
		{
			dependencies: ["03B5D467E76C5B8A"]
			description: ["你需要用&e高压&a化学反应器&f&r处理氯气、碳粉和金红石粉来制作这个"]
			id: "7B27B87A520E38B0"
			rewards: [
				{
					count: 2
					id: "0522455DB01E3725"
					item: "gtceu:rutile_dust"
					random_bonus: 2
					type: "item"
				}
				{
					id: "44AA9F0AC065A628"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 16000
								FluidName: "gtceu:chlorine"
								capacity: 16000
							}
						}
						id: "evilcraft:dark_tank"
						tag: {
							Fluid: {
								Amount: 16000
								FluidName: "gtceu:chlorine"
							}
							capacity: 16000
						}
					}
					type: "item"
				}
			]
			tasks: [{
				id: "01CFC3E97122E03C"
				item: "gtceu:titanium_tetrachloride_bucket"
				type: "item"
			}]
			x: -2.5d
			y: -4.5d
		}
		{
			dependencies: ["3BEDF19CD79D53D5"]
			description: ["在&b程序3&r模式下燃烧16块原木可产出&a重油&f"]
			icon: "gtceu:pyrolyse_oven"
			id: "130DDACA0E38A8E8"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "23F2A4B5063BA3AB"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "72661F65A697879A"
				item: "gtceu:pyrolyse_oven"
				type: "item"
			}]
			x: -2.0d
			y: 5.5d
		}
		{
			dependencies: ["3BEDF19CD79D53D5"]
			description: ["&a裂解机&r虽不能直接产油,但能更高效处理副产品!"]
			icon: "gtceu:cracker"
			id: "3762F8137BFD5A74"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "3708022293D107D8"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "5BACD43DF65C2FA9"
				item: "gtceu:cracker"
				type: "item"
			}]
			x: -3.0d
			y: 5.5d
		}
		{
			dependencies: ["3BEDF19CD79D53D5"]
			description: [
				"放置一台这样的设备,开始钻探吧!它能从基岩层下方开采出远古石油"
				""
				"使用这台设备主要能获取&e&a原油&f&r,同时也有几率发现&a天然气&f及其他石油变种!你可以使用&e高压勘探仪&r的&b流体模式&r来探测地下资源分布"
				""
				"请注意,区块内的石油会随时间枯竭,因此需要定期移动&a流体钻机&f"
			]
			id: "450B3C0520D6B2BB"
			min_width: 300
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "27E92BF29DF6FBD4"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "12A161B884B5653F"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:mv_fluid_drilling_rig"
							}
							{
								Count: 1b
								id: "gtceu:hv_fluid_drilling_rig"
							}
						]
					}
				}
				title: "&a流体钻机&f"
				type: "item"
			}]
			x: -2.5d
			y: 5.0d
		}
		{
			dependencies: ["3B4F326D72E794D3"]
			description: ["将&e轻质蒸汽&a裂化石脑油&f&r进行&a蒸馏&r可获得&0苯"]
			id: "7D6AC042FFD0B6D6"
			rewards: [{
				id: "4123B08AF0AC72D8"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4CFDA0E86F6528A5"
				item: "gtceu:benzene_bucket"
				type: "item"
			}]
			x: -7.0d
			y: 2.5d
		}
		{
			dependencies: ["3B4F326D72E794D3"]
			description: ["将&e轻质蒸汽&a裂化石脑油&f&r进行&a蒸馏&r可获得&e丁二烯"]
			id: "3619CD5AAD5E3691"
			rewards: [{
				id: "2435330DB710A010"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "672E91EAB979F7AF"
				item: "gtceu:butadiene_bucket"
				type: "item"
			}]
			x: -8.5d
			y: 2.5d
		}
		{
			dependencies: ["7D6AC042FFD0B6D6"]
			description: ["在&e&a化学反应器&f&r中将苯与乙烯反应可得到&b苯乙烯"]
			id: "46F1A93B071CD980"
			rewards: [{
				id: "1C74698CCB18BA0B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "56A08496E54BC1A4"
				item: "gtceu:styrene_bucket"
				type: "item"
			}]
			x: -7.0d
			y: 1.0d
		}
		{
			dependencies: [
				"46F1A93B071CD980"
				"3619CD5AAD5E3691"
			]
			description: [
				"在&e&a化学反应器&f&r中使苯乙烯、丁二烯与氧气/空气反应,可获得最高等级橡胶的原始粉末"
				""
				"建议使用纯氧气而非空气,这样能获得最大产量"
			]
			id: "2DDA8AC73C2D50B4"
			rewards: [{
				id: "4FA7BFC6C564DEBD"
				item: "gtceu:butadiene_bucket"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "72F5202F8FDF0E53"
				item: "gtceu:raw_styrene_butadiene_rubber_dust"
				type: "item"
			}]
			x: -8.5d
			y: 1.0d
		}
		{
			dependencies: ["2DDA8AC73C2D50B4"]
			description: [
				"最终产物——最高等级的&d丁苯橡胶&r"
				""
				"只需少量即可包覆导线,在制作高阶传送带模块时也将大有用处"
			]
			id: "511FB322F176D88C"
			rewards: [{
				exclude_from_claim_all: true
				id: "31DF63BA959C435B"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [{
				id: "1F18BEE43B4DC9A4"
				item: "gtceu:styrene_butadiene_rubber_bucket"
				type: "item"
			}]
			x: -8.5d
			y: 0.0d
		}
		{
			dependencies: [
				"7A0A7DC1C2655EA6"
				"228D1C880563CCBB"
			]
			description: [
				"这时&a裂解装置&r就派上用场了,它在裂解时不会损失珍贵的石脑油"
				""
				"替代方案是使用&e&a化学反应器&f&r,但会损失一半石脑油!"
				""
				"建议另外建造&a&a蒸馏塔&f&r来处理这些产物"
			]
			id: "3B4F326D72E794D3"
			rewards: [{
				id: "444110B4CC50435E"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "155D71F4D5E47CF7"
				item: "gtceu:lightly_steam_cracked_naphtha_bucket"
				type: "item"
			}]
			x: -7.0d
			y: 3.5d
		}
		{
			dependencies: ["6805E4C7F6FBAC77"]
			description: ["在&e化学反应器&r中用&9氢气&r处理&e&a含硫石脑油&f&r以脱除硫分"]
			id: "7A0A7DC1C2655EA6"
			rewards: [{
				id: "66D525B2A26063DE"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "09220F4C339567B8"
				item: "gtceu:naphtha_bucket"
				type: "item"
			}]
			x: -5.5d
			y: 4.0d
		}
		{
			dependencies: ["3BEDF19CD79D53D5"]
			description: ["蒸馏&b&a原油&f&r可获得大量&e&a含硫石脑油&f&r,其他油类中也能提取"]
			id: "6805E4C7F6FBAC77"
			rewards: [{
				id: "46BA643BC3DEF2E9"
				item: "gtceu:oil_medium_bucket"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "3DA49DF52B6D150A"
				item: "gtceu:sulfuric_naphtha_bucket"
				type: "item"
			}]
			x: -4.0d
			y: 4.0d
		}
		{
			dependencies: ["0FFF764AA07D6DFC"]
			description: [
				"啊,这让我回想起蒸汽时代的往事"
				""
				"不过它产生的蒸汽量可比老式锅炉多得多"
			]
			id: "228D1C880563CCBB"
			rewards: [{
				id: "556A321FB75D9F76"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "7B7128EECA64BD03"
				item: "gtceu:steam_bucket"
				type: "item"
			}]
			x: -5.5d
			y: 5.0d
		}
		{
			description: ["在&a程序1&r模式下加入少量&9水&r即可产生大量&7蒸汽"]
			id: "0FFF764AA07D6DFC"
			rewards: [{
				exclude_from_claim_all: true
				id: "19EC7F834936C81F"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			shape: "square"
			tasks: [{
				id: "3AC5F6DC531CCE3D"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:lv_fluid_heater"
							}
							{
								Count: 1b
								id: "gtceu:mv_fluid_heater"
							}
							{
								Count: 1b
								id: "gtceu:hv_fluid_heater"
							}
							{
								Count: 1b
								id: "gtceu:ev_fluid_heater"
							}
						]
					}
				}
				title: "Any &a流体加热器&f"
				type: "item"
			}]
			x: -4.0d
			y: 5.0d
		}
		{
			dependencies: ["3406646DF9585AB8"]
			description: [
				"中阶橡胶!既然已掌握顶级橡胶制法,这个可能用不上"
				""
				"但后续某些配方专门需要&9&a硅橡胶&f&r,因此仍需制作"
			]
			id: "6338544F8530118F"
			rewards: [{
				exclude_from_claim_all: true
				id: "29369FEABD94841B"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			tasks: [{
				id: "3E090FF6659CAE97"
				item: "gtceu:silicone_rubber_bucket"
				type: "item"
			}]
			x: -5.5d
			y: 1.0d
		}
		{
			dependencies: ["6A54E73E212BB1AF"]
			description: ["在&e&a化学反应器&f&r的&a程序2&r模式下,将硅粉、水、甲烷和氯气混合制成"]
			id: "3406646DF9585AB8"
			rewards: [{
				count: 3
				id: "053DB92E1E479427"
				item: "gtceu:polydimethylsiloxane_dust"
				random_bonus: 3
				type: "item"
			}]
			subtitle: "试试快速说&a可用3次&f"
			tasks: [{
				id: "1717E9A6D23CF49C"
				item: "gtceu:polydimethylsiloxane_dust"
				type: "item"
			}]
			x: -5.5d
			y: 2.5d
		}
		{
			dependencies: ["3BEDF19CD79D53D5"]
			description: [
				"通过&a石油蒸馏&r获得的&e&a含硫炼油气&f&r可提纯为&7&a炼油气&f"
				""
				"&a炼油气&f可进一步&a裂解&r为不同形态,具体取决于你想蒸馏的成分"
				""
				"&3轻烃裂解气&r是获取&d甲烷&r和氢气的优质来源!"
				""
				"替代方案(较慢)包括&e离心&r蘑菇或蒸馏&a发酵生物质&f"
			]
			id: "6A54E73E212BB1AF"
			rewards: [{
				id: "3D51C447126057E3"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6AEF501966EBB44B"
				item: "gtceu:methane_bucket"
				type: "item"
			}]
			x: -4.0d
			y: 3.0d
		}
		{
			id: "7D10AA6EF07087B5"
			rewards: [{
				exclude_from_claim_all: true
				id: "4EC35F01615CB33B"
				table_id: 5304546381530089504L
				type: "loot"
			}]
			shape: "square"
			tasks: [{
				icon: "gtceu:hv_cutter"
				id: "5685DB13CE250DBA"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:hv_cutter"
							}
							{
								Count: 1b
								id: "gtceu:ev_cutter"
							}
						]
					}
				}
				title: "高压或极压切割机"
				type: "item"
			}]
			x: 1.0d
			y: 5.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方团队&r或&2社区贡献者&r为AllTheMods整合包编写"
				"根据&eAllTheMods&r系列整合包采用的&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用本任务"
				""
				""
				""
				"该任务默认隐藏,若你看到此说明,说明正处于编辑模式"
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "6464CF4B189D3DC7"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "5976F3A604271467"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "0BC2552079B4ED2A"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: -4.0d
			y: 1.0d
		}
	]
	title: "极限电压"
}
