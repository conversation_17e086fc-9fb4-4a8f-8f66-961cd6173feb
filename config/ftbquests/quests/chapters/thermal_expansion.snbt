{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "thermal_expansion"
	group: "2B51AC12041E3F89"
	icon: "thermal:machine_frame"
	id: "658721DF03EC997D"
	images: [{
		height: 0.3d
		image: "allthetweaks:item/atm_star"
		rotation: 0.0d
		width: 0.3d
		x: 4.2d
		y: 3.2d
	}]
	order_index: 12
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["&d热力系列&f是一组模块化模组,为您的Minecraft体验增添了魔法与科技交融的丰富内容!"]
			icon: "thermal:upgrade_augment_3"
			id: "2C50B0E024C3D92E"
			rewards: [
				{
					id: "184363B38B8B2CBA"
					type: "xp"
					xp: 10
				}
				{
					id: "009C332DA938512C"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "thermal:guidebook"
						}
					}
					type: "item"
				}
			]
			shape: "square"
			size: 1.5d
			tasks: [{
				id: "37547F63C72EED17"
				item: "alltheores:raw_tin"
				type: "item"
			}]
			title: "欢迎来到&9&d热力系列&f&r!"
			x: -4.5d
			y: 0.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: [
				"&a红石熔炉&f使用RF/FE能量而非煤炭来熔炼物品."
				""
				"与&d热力系列&f中所有机器相同,该设备可通过安装强化部件来提升各工序速度."
			]
			id: "22BC123D486CC3E3"
			rewards: [{
				exclude_from_claim_all: true
				id: "42CC76583E352106"
				table_id: 7377440633892994587L
				type: "random"
			}]
			subtitle: "&a动力炉&f"
			tasks: [{
				id: "288B38C43A7C6D48"
				item: "thermal:machine_furnace"
				type: "item"
			}]
			title: "&a红石熔炉&f"
			x: 1.5d
			y: 0.0d
		}
		{
			dependencies: ["22BC123D486CC3E3"]
			description: ["&a粉碎机&f能将原始矿石粉碎成矿粉,并有25%%概率额外产出矿粉."]
			id: "55C8DD9A754545BD"
			rewards: [{
				exclude_from_claim_all: true
				id: "50CC03971F8EC6BD"
				table_id: 7377440633892994587L
				type: "random"
			}]
			subtitle: "将矿石粉碎成矿粉"
			tasks: [{
				id: "06665E87CB134F3C"
				item: "thermal:machine_pulverizer"
				type: "item"
			}]
			x: 3.5d
			y: 0.0d
		}
		{
			dependencies: ["55C8DD9A754545BD"]
			description: [
				"&a感应炉&f可将材料融合成新型合金."
				""
				"在将&a远古残骸&f熔炼为&a下界合金碎片&f时尤为实用."
			]
			id: "452F51995AD0461C"
			rewards: [{
				exclude_from_claim_all: true
				id: "0D91F03C2111A35D"
				table_id: 7377440633892994587L
				type: "random"
			}]
			subtitle: "合金制造机"
			tasks: [{
				id: "63C10CF0EF19F2C8"
				item: "thermal:machine_smelter"
				type: "item"
			}]
			x: 5.5d
			y: 0.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["制作&d热力系列&f各类机器均需使用&a机器框架&f."]
			id: "5F385CBA98795C62"
			rewards: [
				{
					id: "157563CE4EFA237B"
					type: "xp"
					xp: 10
				}
				{
					id: "676677234F8E6F37"
					item: "thermal:tin_gear"
					type: "item"
				}
			]
			shape: "hexagon"
			subtitle: "机器基础框架"
			tasks: [{
				id: "3EC446E752907C94"
				item: "thermal:machine_frame"
				type: "item"
			}]
			x: -0.5d
			y: 0.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "3DA93308D19BA85F"
			rewards: [
				{
					id: "108A20AE0FED5D27"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "498980CF804D6A24"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "通过燃烧物品发电!"
			tasks: [{
				id: "4BEE939AC38768ED"
				item: "thermal:dynamo_stirling"
				type: "item"
			}]
			x: 7.5d
			y: -1.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["注:可接受&a树油&f、&a杂酚油&f和&a精炼油&f作为燃料."]
			hide_dependency_lines: true
			id: "7FE2EED58AB791E8"
			rewards: [
				{
					id: "7B21F4A9F2C52F51"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "08C9A20AA454154E"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "使用液态燃料发电!"
			tasks: [{
				id: "6E5C2E9D729210C9"
				item: "thermal:dynamo_compression"
				type: "item"
			}]
			x: 7.5d
			y: 0.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "2F71FCE4E576C977"
			rewards: [
				{
					id: "317ED7FF0734E5F1"
					item: "minecraft:lava_bucket"
					type: "item"
				}
				{
					id: "79A13EA08A164B86"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "0ACEAB1F063A2DFB"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "利用岩浆发电!"
			tasks: [{
				id: "52A52D9AC73D57A6"
				item: "thermal:dynamo_magmatic"
				type: "item"
			}]
			x: 8.5d
			y: -1.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "72C1C2CE02DCBDFF"
			rewards: [
				{
					count: 2
					id: "2D68111DA1CB4560"
					item: "minecraft:lapis_lazuli"
					random_bonus: 2
					type: "item"
				}
				{
					id: "48E74944FEA0ECC1"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "385B47382DCFF266"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "使用宝石发电!"
			tasks: [{
				id: "7B973B2B2EED7921"
				item: "thermal:dynamo_lapidary"
				type: "item"
			}]
			x: 8.5d
			y: 1.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: [""]
			hide_dependency_lines: true
			id: "2EAE9EDE6EFA59F0"
			rewards: [
				{
					id: "7A632E03F9CD6324"
					item: "minecraft:book"
					type: "item"
				}
				{
					id: "47096C6969AB1279"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "21595AE7A363FF01"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "通过附魔物品发电!"
			tasks: [{
				id: "0E3CDD1130A56248"
				item: "thermal:dynamo_disenchantment"
				type: "item"
			}]
			x: 7.5d
			y: 1.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "40ADAB71DB70EF32"
			rewards: [
				{
					count: 4
					id: "557845C485F475BB"
					item: "minecraft:cooked_beef"
					type: "item"
				}
				{
					id: "7C366B7A8CE82E4B"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "1EDF12F7A2AF8F34"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "居然能用食物发电？"
			tasks: [{
				id: "795A2D642A7B7D50"
				item: "thermal:dynamo_gourmand"
				type: "item"
			}]
			x: 8.5d
			y: 0.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: [
				"这是所有机器和物品的基础升级部件."
				""
				"注:虽然机器可安装多个基础升级,但仅最高等级的升级会生效."
			]
			hide_dependency_lines: true
			id: "76084BE1BBCF941F"
			rewards: [
				{
					id: "7CD91CF01EAA7BCD"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4CC0F0C5CD540477"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "一级基础升级"
			tasks: [{
				id: "3EE6189C5B2FDD2F"
				item: "thermal:upgrade_augment_1"
				type: "item"
			}]
			x: 0.5d
			y: 2.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: [
				"这是&d热力系列&f物品和机器的二级升级部件."
				""
				"注:虽然机器可安装多个基础升级,但仅最高等级的升级会生效."
			]
			hide_dependency_lines: true
			id: "246CD1925FD6761C"
			rewards: [
				{
					id: "723FE016CAA6566D"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "29640479A9973A7C"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "二级基础升级"
			tasks: [{
				id: "6AD321AC8D6BFDAD"
				item: "thermal:upgrade_augment_2"
				type: "item"
			}]
			x: 1.5d
			y: 2.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["注:虽然机器可安装多个基础升级,但仅最高等级的升级会生效."]
			hide_dependency_lines: true
			id: "034FC4BCCCD7D154"
			rewards: [
				{
					id: "6724D7DE6CC92091"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2C33A1AF6AE00419"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "三级基础升级"
			tasks: [{
				id: "5237B4381DA7BE1B"
				item: "thermal:upgrade_augment_3"
				type: "item"
			}]
			x: 2.5d
			y: 2.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["当放置在两个水源方块之间时,可创造无限水源."]
			hide_dependency_lines: true
			id: "213FFA67A680E534"
			rewards: [
				{
					id: "46E591F83A20EB99"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "29A65831B83F2BC5"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "07C7BA8E13F85930"
				item: "thermal:device_water_gen"
				type: "item"
			}]
			x: -5.0d
			y: 2.5d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["该机器可为放入其中的物品充能."]
			hide_dependency_lines: true
			id: "5FDEAA78891874FD"
			rewards: [
				{
					id: "16C939074FA98D0B"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "549B018803BBB23F"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "物品充能器"
			tasks: [{
				id: "2BB7C4355B61F638"
				item: "thermal:charge_bench"
				type: "item"
			}]
			x: -4.5d
			y: 3.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: [
				"该机器可生产多种石材."
				""
				"在一侧放置岩浆源方块,另一侧放置水源方块,即可生成圆石.查看配方可了解其他可生产的石材类型!"
			]
			hide_dependency_lines: false
			id: "4EA8BA9753D0DD81"
			optional: true
			rewards: [
				{
					id: "600EF049B3363CCC"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "40A5A606A14519FA"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "&a造石机&f"
			tasks: [{
				id: "2720B59BB163F73B"
				item: "thermal:device_rock_gen"
				type: "item"
			}]
			x: -4.5d
			y: 2.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: [
				"可将特定方块转化为液体."
				""
				"适用于将圆石、下界岩等转化为岩浆."
			]
			id: "0897F7A3203E45AF"
			rewards: [
				{
					id: "4C62A0183D243C27"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "564837E9A2526BDB"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3D2DFF6062AED26E"
				item: "thermal:machine_crucible"
				type: "item"
			}]
			x: -2.0d
			y: -2.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: [
				"该机器功能类似&a植物盆&f、&a园艺玻璃罩&f等设备."
				""
				"当提供水和种子时,会在机器内部培育作物,并自动将产物输出至机器内."
			]
			id: "648B483B128A32F5"
			rewards: [
				{
					id: "265FD226E965D7B3"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "356E519785E2B5E2"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "61287BF539F0C5FC"
				item: "thermal:machine_insolator"
				type: "item"
			}]
			x: -1.0d
			y: -2.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			hide_dependency_lines: true
			id: "66321E1F01C36567"
			rewards: [
				{
					id: "374BE54F4405BC0B"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "77F606B6D81F4211"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "7A3BCB9C631D8FAD"
				item: "thermal:machine_sawmill"
				type: "item"
			}]
			x: -1.5d
			y: -2.5d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "1B04B7EA5220D275"
			rewards: [
				{
					id: "7F764F32D74976B4"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4DD15CDD438523D6"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "物品吸取装置"
			tasks: [{
				id: "3819DBE6E95E998E"
				item: "thermal:device_collector"
				type: "item"
			}]
			x: -4.0d
			y: 2.5d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["向区域内扩散&a药水效果&f."]
			hide_dependency_lines: true
			id: "66858700C3DDCB9E"
			rewards: [
				{
					id: "479CC02BC1343DBE"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "3C36326CDB42B5FF"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "590869F3AE44A956"
				item: "thermal:device_potion_diffuser"
				type: "item"
			}]
			x: -4.5d
			y: -3.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["&a可用于&f为物品充能、&a组件机器&f或向物品注入液体."]
			id: "74F524F4F0231A78"
			rewards: [
				{
					id: "4E1086FC2DA044FC"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "6DD2055E48636114"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5835951863555C2E"
				item: "thermal:tinker_bench"
				type: "item"
			}]
			x: -4.5d
			y: -2.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["小技巧:你可以用&9容量&r附魔来增加存储空间!"]
			hide_dependency_lines: true
			id: "037E566ACC83FE07"
			rewards: [
				{
					id: "54DBA686738A0538"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "1FD0DB21844211B8"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "64186CC4330A70D8"
				item: {
					Count: 1
					id: "thermal:energy_cell"
					tag: {
						BlockEntityTag: {
							Energy: 0
							EnergyMax: 1000000
							EnergyRecv: 1000
							EnergySend: 1000
						}
					}
				}
				type: "item"
			}]
			title: "能量存储"
			x: -1.25d
			y: 1.9499999999999997d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "4389E906A2A74867"
			rewards: [
				{
					id: "4AE007FFAF1003F8"
					item: "minecraft:bucket"
					type: "item"
				}
				{
					id: "43B8F1F2FBA0D4EA"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "671A9B5208396DBE"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "694DB377E094D28E"
				item: {
					Count: 1
					id: "thermal:fluid_cell"
					tag: {
						BlockEntityTag: {
							TankInv: [{
								Amount: 0
								Capacity: 32000
								FluidName: "minecraft:empty"
								Tank: 0b
							}]
						}
					}
				}
				type: "item"
			}]
			title: "&a流体储存&f"
			x: 5.25d
			y: 1.9499999999999997d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "483C1F4D099369A2"
			rewards: [{
				exclude_from_claim_all: true
				id: "3ADCE21ED735AA1D"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "diamond"
			subtitle: "允许存储经验值"
			tasks: [{
				id: "66656B02B957573F"
				item: "thermal:xp_storage_augment"
				type: "item"
			}]
			x: 1.0d
			y: 5.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "0837E35C9C6881B4"
			rewards: [
				{
					id: "3990D47351D43E1C"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "5051167761B10575"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "提升&aRF容量&f和&a传输速率&f"
			tasks: [{
				id: "2F608F433D9A3363"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:rf_coil_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_augment_4"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_augment_5"
							}
						]
					}
				}
				title: "扩展RF线圈"
				type: "item"
			}]
			title: "扩展型RF线圈"
			x: 1.5d
			y: 3.1999999999999993d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "3320ADFD7DC4CA00"
			rewards: [
				{
					id: "204DB02FD7E9A4F6"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "51FFB9970FE6B8E0"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "提升&aRF容量&f,并略微增加&aRF传输&f"
			tasks: [{
				id: "7B5C9FA866C0588A"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:rf_coil_storage_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_storage_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_storage_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_storage_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_storage_augment_4"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_storage_augment_5"
							}
						]
					}
				}
				title: "稳定化RF线圈"
				type: "item"
			}]
			x: 2.5d
			y: 3.1999999999999993d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "79366EC1EE27ED4B"
			rewards: [
				{
					id: "591FD4F323E3FF7C"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "2DCE94B84105A51C"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "提升&aRF传输&f,并略微增加容量"
			tasks: [{
				id: "775A7E11D20688CD"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:rf_coil_xfer_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_xfer_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_xfer_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_xfer_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_xfer_augment_4"
							}
							{
								Count: 1b
								id: "thermal_extra:rf_coil_xfer_augment_5"
							}
						]
					}
				}
				title: "高通量RF线圈"
				type: "item"
			}]
			x: 3.5d
			y: 3.1999999999999993d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "6DF4B859ACBCD408"
			rewards: [{
				exclude_from_claim_all: true
				id: "0509DF46312C4883"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "diamond"
			subtitle: "提升&a储罐容量&f"
			tasks: [{
				id: "5DFD1C0334466FF2"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:fluid_tank_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:fluid_tank_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:fluid_tank_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:fluid_tank_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:fluid_tank_augment_4"
							}
							{
								Count: 1b
								id: "thermal_extra:fluid_tank_augment_5"
							}
							{
								Count: 1b
								id: "thermal_extra:fluid_tank_augment_6"
							}
						]
					}
				}
				title: "扩展储罐结构"
				type: "item"
			}]
			x: 2.0d
			y: 5.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "6D35E56FC874C841"
			rewards: [{
				exclude_from_claim_all: true
				id: "368D5522438399DD"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "1726C6AB09496E0C"
				item: "thermal:item_filter_augment"
				type: "item"
			}]
			x: 1.5d
			y: 5.5d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: [""]
			hide_dependency_lines: true
			id: "74DD4F8A13EAD3ED"
			rewards: [
				{
					id: "10EE19CDB35235F4"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "0BCA35766BDB9415"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "提升&a处理&f速度,但降低效率"
			tasks: [{
				id: "46E665F97A2BECB6"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:machine_speed_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_speed_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_speed_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_speed_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_speed_augment_4"
							}
						]
					}
				}
				title: "通量联动增幅器"
				type: "item"
			}]
			x: 0.5d
			y: 3.1999999999999993d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "61E2FC5D363A5CA4"
			rewards: [
				{
					id: "5534C32BABF19B6D"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "56B2183163132B51"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "以速度为代价提升&a效率&f"
			tasks: [{
				id: "1E0593F1AA073CFD"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:machine_efficiency_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_efficiency_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_efficiency_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_efficiency_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_efficiency_augment_4"
							}
						]
					}
				}
				title: "通量效率"
				type: "item"
			}]
			x: 1.0d
			y: 3.6999999999999993d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "58C6BAC128155B4E"
			rewards: [
				{
					id: "1672462285E4696D"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "5CF0B3C1A24BF1DF"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "增加&a副产物&f"
			tasks: [{
				id: "497485048E0AD20D"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:machine_output_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_output_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_output_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_output_augment_3"
							}
						]
					}
				}
				title: "辅助筛分工艺"
				type: "item"
			}]
			x: 2.0d
			y: 3.6999999999999993d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "7D49A41E4D63A596"
			rewards: [
				{
					count: 4
					id: "0410D3AC01336E89"
					item: "minecraft:redstone"
					type: "item"
				}
				{
					id: "40E9A3AF6C1A87BC"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "0EC6C17E0FF3A29F"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "减少&a催化剂用量&f"
			tasks: [{
				id: "6C996D5E63879519"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:machine_catalyst_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_catalyst_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_catalyst_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:machine_catalyst_augment_3"
							}
						]
					}
				}
				title: "催化回收室"
				type: "item"
			}]
			x: 3.0d
			y: 3.6999999999999993d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "234CD79746FCAA18"
			rewards: [{
				exclude_from_claim_all: true
				id: "5F186575AE3ED67D"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "diamond"
			subtitle: "消除副产物"
			tasks: [{
				id: "784D4494897AF202"
				item: "thermal:machine_null_augment"
				type: "item"
			}]
			x: 2.5d
			y: 5.5d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "7C83735C2D746162"
			rewards: [
				{
					id: "4B8F25D9433225BF"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "68A241917208B66A"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "以效率为代价提升&a产率&f"
			tasks: [{
				id: "3861678346D376C1"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:dynamo_output_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_output_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_output_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_output_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_output_augment_4"
							}
						]
					}
				}
				title: "辅助 &a反应锅炉&f"
				type: "item"
			}]
			x: 8.0d
			y: 2.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "467CDD14AE21A850"
			rewards: [
				{
					id: "6D0FE4CFD7575A75"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "677183781F1344D9"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "hexagon"
			subtitle: "提升发电机&a燃料效率&f"
			tasks: [{
				id: "3D6A9C7EE22C2ADF"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:dynamo_fuel_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_fuel_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_fuel_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_fuel_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:dynamo_fuel_augment_4"
							}
						]
					}
				}
				title: "多循环喷射器"
				type: "item"
			}]
			x: 8.0d
			y: -2.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "00C24A7DFEAEE956"
			rewards: [{
				exclude_from_claim_all: true
				id: "45D73ED4CEBD7BCC"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "diamond"
			subtitle: "增加范围效果"
			tasks: [{
				id: "705AE21001A4E9C7"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "thermal:area_radius_augment"
							}
							{
								Count: 1b
								id: "thermal_extra:area_radius_augment_1"
							}
							{
								Count: 1b
								id: "thermal_extra:area_radius_augment_2"
							}
							{
								Count: 1b
								id: "thermal_extra:area_radius_augment_3"
							}
							{
								Count: 1b
								id: "thermal_extra:area_radius_augment_4"
							}
						]
					}
				}
				title: "径向增强"
				type: "item"
			}]
			x: 3.0d
			y: 5.0d
		}
		{
			dependencies: ["66858700C3DDCB9E"]
			id: "22A1C68078EFB38B"
			rewards: [
				{
					id: "16143BA782E3D869"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "138DAAFCD3B6FA02"
					table_id: 7377440633892994587L
					type: "random"
				}
				{
					exclude_from_claim_all: true
					id: "6C8CBB408A5B7F2E"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "增强药水效果"
			tasks: [{
				id: "08ED05EBDFF0B4D9"
				item: "thermal:potion_amplifier_augment"
				type: "item"
			}]
			x: -5.0d
			y: -2.5d
		}
		{
			dependencies: ["66858700C3DDCB9E"]
			id: "1714E1048F01E1AA"
			rewards: [
				{
					id: "065A80C12CFDB394"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "51031CBB70E1E32C"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "延长药水效果持续时间"
			tasks: [{
				id: "501A3B2548F6DB3E"
				item: "thermal:potion_duration_augment"
				type: "item"
			}]
			x: -4.0d
			y: -2.5d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: ["该机器主要用于从花朵中提取染料,或将矿石混合物分离回原始成分."]
			id: "3475E12711B6BB98"
			rewards: [{
				exclude_from_claim_all: true
				id: "31F0296EE8936FFA"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "diamond"
			subtitle: "将物品分离为&a组分&f"
			tasks: [{
				id: "2183800CED355EEB"
				item: "thermal:machine_centrifuge"
				type: "item"
			}]
			x: 0.0d
			y: -2.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: [
				"使用模具制造\"压制品\"."
				""
				"例如板材、齿轮等."
			]
			hide_dependency_lines: true
			id: "5963FBEB78A79668"
			rewards: [
				{
					id: "650B53A376632EC3"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "5B2A3F1733C09B27"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "0C6725EA57E7D9EE"
				item: "thermal:machine_press"
				type: "item"
			}]
			x: 0.5d
			y: -2.5d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: [
				"将液体转化为固体物品,部分需要模具."
				""
				"还能制作蜜蜂刷怪蛋."
			]
			id: "469443A3BA0C3BEE"
			rewards: [{
				exclude_from_claim_all: true
				id: "046E00AC462A9536"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "66AF5C07727A5B39"
				item: "thermal:machine_chiller"
				type: "item"
			}]
			x: 1.0d
			y: -2.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: ["可将液体转化为物品或其他有用液体."]
			hide_dependency_lines: true
			id: "627D6FDC3D8C42F6"
			rewards: [
				{
					id: "7D725FF8CB44785C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "3BB52A90042D3DC8"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3B188F7D7009093C"
				item: "thermal:machine_refinery"
				type: "item"
			}]
			x: 0.0d
			y: -3.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: [
				"工作原理类似&a焦炉&f,但更简化."
				""
				"放入\"燃料\"(如煤炭)可生产&a焦煤&f和副产物."
			]
			hide_dependency_lines: true
			id: "5ECC93FB8F676E3F"
			rewards: [
				{
					id: "5AA3B772E203E40C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2CA387680118884A"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "76A0C32FB86A089D"
				item: "thermal:machine_pyrolyzer"
				type: "item"
			}]
			x: -2.0d
			y: -3.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: ["将液体与物品结合"]
			hide_dependency_lines: true
			id: "469663FE3DA932EF"
			rewards: [
				{
					id: "05CAA4581B7D1435"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "1176D77246ED412B"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "05867D444D20EABE"
				item: "thermal:machine_bottler"
				type: "item"
			}]
			x: -1.0d
			y: -3.0d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			description: ["可制作\"液体药水\",并能装瓶成药水."]
			hide_dependency_lines: true
			id: "1BCE8D02CDD13838"
			rewards: [
				{
					id: "46E350F851A4013C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "555654D47E430E04"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "70EF981620DADB32"
				item: "thermal:machine_brewer"
				type: "item"
			}]
			x: -0.5d
			y: -2.5d
		}
		{
			dependencies: ["5F385CBA98795C62"]
			hide_dependency_lines: true
			id: "7AAEFA2A349D3F82"
			rewards: [
				{
					id: "4FE6677655F3B4DD"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2274A69E0B4C36D1"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "自动合成台!"
			tasks: [{
				id: "72EC640A6F6C69C1"
				item: "thermal:machine_crafter"
				type: "item"
			}]
			x: 1.0d
			y: -3.0d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			description: ["这更像是使用RF/FE能量的镐子."]
			hide_dependency_lines: true
			id: "5257468DC6C11851"
			rewards: [
				{
					id: "7029BD256EF4EEBD"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "498091B0B90B28EF"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "octagon"
			subtitle: "前期采矿工具"
			tasks: [{
				id: "38EE8C011F7E3FEC"
				item: {
					Count: 1
					id: "thermal:flux_drill"
					tag: { }
				}
				type: "item"
			}]
			x: 3.5d
			y: -2.5d
		}
		{
			dependencies: ["2C50B0E024C3D92E"]
			hide_dependency_lines: true
			id: "6BF6B00BC21CA547"
			rewards: [
				{
					id: "539EF1C8332A468C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "25DEC0A5098BD973"
					table_id: 7377440633892994587L
					type: "random"
				}
			]
			shape: "octagon"
			subtitle: "RF动力手锯!"
			tasks: [{
				id: "504ABB4FCF4AA14E"
				item: {
					Count: 1
					id: "thermal:flux_saw"
					tag: { }
				}
				type: "item"
			}]
			x: 4.5d
			y: -2.5d
		}
		{
			dependencies: ["034FC4BCCCD7D154"]
			hide_dependency_lines: true
			id: "76BCB8C0448EFE50"
			rewards: [{
				exclude_from_claim_all: true
				id: "1D3B3D6557AD6C69"
				table_id: 7377440633892994587L
				type: "random"
			}]
			shape: "hexagon"
			subtitle: "四级基地升级"
			tasks: [{
				id: "41B789CFB591439D"
				item: "thermal_extra:upgrade_augment"
				type: "item"
			}]
			x: 3.5d
			y: 2.0d
		}
		{
			can_repeat: false
			description: [
				"该任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包编写."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"该任务默认隐藏,若你看到此提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "40053027D054EAB4"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "2D270CF1DED60859"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "2980A563E185D60D"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -6.5d
			y: 0.0d
		}
	]
	title: "&d热力系列&f"
}
