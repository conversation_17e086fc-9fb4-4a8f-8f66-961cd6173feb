{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "medium_voltage"
	group: "1DA67E79B40AB130"
	icon: "gtceu:good_electronic_circuit"
	id: "574AC3A76DC03364"
	order_index: 3
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"记住:超频会使配方处理速度翻倍,但功耗会增至四倍"
				""
				"当你开始替换机器时,可以将旧机器放入粉碎机以回收部分制造材料"
			]
			id: "262AE37765B139BE"
			rewards: [{
				count: 2
				id: "73FAB82D3EDC2227"
				item: "gtceu:basic_electronic_circuit"
				type: "item"
			}]
			size: 1.5d
			subtitle: "欢迎来到&b中压时代&f"
			tasks: [{
				id: "7DECEDB572639CFF"
				item: "gtceu:good_electronic_circuit"
				type: "item"
			}]
			x: -7.5d
			y: 0.19999999999999996d
		}
		{
			dependencies: [
				"57F1B60DEA622275"
				"2B9BA85662BF637C"
				"246882DD125868EC"
				"0E603F1FE596DB2A"
				"4E1D5EC061A4AE55"
			]
			description: [
				"恭喜!"
				""
				"通过制造高级&a集成电路&f,你已成功进入&a高压时代&f!"
				""
				"现在可以淘汰那些基础&a电路板&f和&a优质电子电路板&f的老配方,用它们的集成版本替代"
			]
			icon: "gtceu:advanced_integrated_circuit"
			id: "2C28217E1131A63A"
			rewards: [
				{
					count: 2
					id: "502F2B7FE7D13923"
					item: "gtceu:good_integrated_circuit"
					random_bonus: 2
					type: "item"
				}
				{
					count: 4
					id: "3FFB6FD2118C84F0"
					item: "gtceu:transistor"
					random_bonus: 4
					type: "item"
				}
			]
			size: 1.5d
			subtitle: "新时代来临"
			tasks: [
				{
					id: "3948D4991F8810F6"
					item: "gtceu:basic_integrated_circuit"
					type: "item"
				}
				{
					id: "2E681E532E087237"
					item: "gtceu:good_integrated_circuit"
					type: "item"
				}
				{
					id: "74F24FDAA9C6153D"
					item: "gtceu:advanced_integrated_circuit"
					type: "item"
				}
			]
			title: "高级&a集成电路&f"
			x: 7.050000000000001d
			y: 3.1499999999999995d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"我知道这是中压时代的低压机器,但相信我,它值得消耗中压电路来制造"
				""
				"使用&a电路组装机&f制造电路会更便宜,而你将需要大量电路"
			]
			id: "246882DD125868EC"
			rewards: [{
				exclude_from_claim_all: true
				id: "42AECA3A8E8FE1E2"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "更便宜的电路!"
			tasks: [{
				id: "2BA5756D55310A03"
				item: "gtceu:lv_circuit_assembler"
				type: "item"
			}]
			x: -0.5d
			y: -4.5d
		}
		{
			dependencies: [
				"063F5023D56CA6B1"
				"6A82827978D3483B"
				"06BBD7B736C564C1"
			]
			description: [
				"晶体管堪称现代奇迹,正是它推动了电子时代的蓬勃发展"
				""
				"通过信号放大和开关功能,晶体管使得更复杂的电子设备成为可能,并引入了逻辑编程能力!"
				""
				"在我们的案例中,它们让我们能够制造&a集成电路&f!"
			]
			id: "0E603F1FE596DB2A"
			rewards: [{
				count: 4
				id: "0477033EDE72D79B"
				item: "gtceu:fine_tin_wire"
				random_bonus: 6
				type: "item"
			}]
			tasks: [{
				id: "4EFE2448BBA41309"
				item: "gtceu:transistor"
				type: "item"
			}]
			x: 2.0d
			y: 1.0d
		}
		{
			dependencies: ["100ADA8508F6502A"]
			description: ["现在需要将晶圆切割成芯片,所以把它们放回切割机吧"]
			id: "57F1B60DEA622275"
			rewards: [{
				id: "2884EBF95CB39508"
				item: "gtceu:silicon_wafer"
				random_bonus: 1
				type: "item"
			}]
			tasks: [{
				id: "5805B28FB0547910"
				item: "gtceu:ilc_chip"
				type: "item"
			}]
			x: 5.5d
			y: -1.4999999999999984d
		}
		{
			dependencies: ["7EFA4F2C0A895502"]
			description: ["刻蚀后的晶圆需要切割成适当尺寸,所以我们要再次使用切割机!"]
			id: "2B9BA85662BF637C"
			rewards: [{
				id: "4B04D1AC6BF7DCB3"
				item: "gtceu:silicon_wafer"
				random_bonus: 1
				type: "item"
			}]
			tasks: [{
				id: "2900399703D087CC"
				item: "gtceu:ram_chip"
				type: "item"
			}]
			x: 8.5d
			y: -1.4999999999999984d
		}
		{
			dependencies: ["2AB457E29360E3B8"]
			description: ["冷却后我们就可以将锭加工成更有用的材料"]
			id: "662E0A84D755064F"
			rewards: [
				{
					count: 2
					id: "0E5BD83CE089A46E"
					item: "gtceu:carbon_dust"
					random_bonus: 2
					type: "item"
				}
				{
					id: "3418DD325855E266"
					item: "gtceu:silicon_dioxide_dust"
					random_bonus: 2
					type: "item"
				}
			]
			tasks: [{
				id: "1B2434CAA46B1B5E"
				item: "gtceu:silicon_ingot"
				type: "item"
			}]
			x: 1.5d
			y: -0.1d
		}
		{
			dependencies: ["5E90EF7FF530C477"]
			description: [
				"乙烯与更多氧气反应会生成聚乙烯"
				""
				"注意这个配方需要使用&e程序1&r"
				""
				"可以用空气代替氧气,但产出的聚乙烯会减少"
				""
				"这种材料用途广泛,我们会大量使用,所以务必批量生产,或者更好的方式是自动化生产"
			]
			id: "6A82827978D3483B"
			rewards: [{
				id: "0E430B94CAD6455B"
				item: "gtceu:ethylene_bucket"
				type: "item"
			}]
			subtitle: "更多氧气"
			tasks: [{
				id: "6A3EF1D7987AA6BB"
				item: "gtceu:polyethylene_bucket"
				type: "item"
			}]
			x: 1.0d
			y: 1.5d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: ["当聚乙烯生产线就绪后,可以在组装机中切换生产机器外壳以节省材料"]
			id: "063F5023D56CA6B1"
			rewards: [{
				exclude_from_claim_all: true
				id: "266B8E9D3694AAA1"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "格里格斯,组装!"
			tasks: [{
				id: "37E2BCA62B82CFF5"
				item: "gtceu:mv_assembler"
				type: "item"
			}]
			x: -1.5d
			y: 0.5d
		}
		{
			dependencies: ["707EBA5717938515"]
			description: [
				"用这个小技巧把方块变成板!"
				""
				"对制造硅晶圆和晶圆芯片也非常有用,我们很快就会用到"
			]
			id: "0D1A6B32FEB51FAD"
			rewards: [{
				exclude_from_claim_all: true
				id: "71B383C6E1AE918A"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "尖端技术"
			tasks: [{
				id: "7BE3C637D0A738EF"
				item: "gtceu:mv_cutter"
				type: "item"
			}]
			x: 7.0d
			y: 0.5000000000000004d
		}
		{
			dependencies: [
				"4DD7F3508B757EF0"
				"1F92F7314DF3C3E2"
			]
			description: ["在车床中加工一个红宝石板可以得到红宝石透镜"]
			id: "26004F997C758011"
			rewards: [{
				id: "7742DCB74500D3F7"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6719992674D5E9F1"
				item: "gtceu:ruby_lens"
				type: "item"
			}]
			x: 4.0d
			y: -3.5000000000000018d
		}
		{
			dependencies: [
				"0D1A6B32FEB51FAD"
				"26D1F1ECF66194E6"
			]
			description: [
				"&a军用级切割机&f能快速处理晶锭,将其转化为16个晶圆"
				""
				"这些晶圆还能用来制造更便宜的二极管——是时候升级那个配方了!"
			]
			id: "688AFF76CF5E599A"
			rewards: [{
				count: 2
				id: "76BF16038E87E38E"
				item: "gtceu:silicon_wafer"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "5205FCBDC094E2F2"
				item: "gtceu:silicon_wafer"
				type: "item"
			}]
			x: 7.0d
			y: -0.9999999999999989d
		}
		{
			description: [
				"&e32份&a硅粉&f&r加上&a一小堆砷化镓粉&f,在&b程序2&r模式下可以制造这个"
				""
				"或许值得为此新建一个专用电弧炉,专门处理程序1的配方"
			]
			id: "26D1F1ECF66194E6"
			rewards: [{
				count: 8
				id: "2E36296F10252C53"
				item: "gtceu:silicon_dust"
				random_bonus: 8
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "6A276D195CB7BAD1"
				item: "gtceu:silicon_boule"
				type: "item"
			}]
			x: 7.0d
			y: -2.4999999999999973d
		}
		{
			dependencies: [
				"545959C8C28C6F2B"
				"26004F997C758011"
				"644B16B9618B41BE"
			]
			description: [
				"使用激光和特定透镜在晶圆上刻蚀不同图案"
				""
				"建议为我们制作的每种透镜都配备一台,这样在自动化时就不用手动更换透镜了"
			]
			id: "11F7D3DDF5683EB3"
			rewards: [{
				exclude_from_claim_all: true
				id: "50742DF1F39731AC"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			tasks: [{
				id: "699D8FAFFE29593C"
				item: "gtceu:mv_laser_engraver"
				type: "item"
			}]
			x: 7.0d
			y: -3.5000000000000018d
		}
		{
			dependencies: [
				"1ACD6672F72664D6"
				"1F92F7314DF3C3E2"
			]
			description: ["在车床中加工绿宝石板可以得到绿宝石透镜!"]
			id: "545959C8C28C6F2B"
			rewards: [{
				id: "16950710D10185F1"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "26EADF8B321DFAA6"
				item: "gtceu:emerald_lens"
				type: "item"
			}]
			x: 10.0d
			y: -3.5000000000000018d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"放入宝石板,得到宝石透镜"
				""
				"仍然可以制造棒材!"
			]
			hide_dependency_lines: true
			id: "1F92F7314DF3C3E2"
			rewards: [{
				exclude_from_claim_all: true
				id: "49C7CE1D4F1883BC"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			tasks: [{
				id: "79464C2A1BC4AF5A"
				item: "gtceu:mv_lathe"
				type: "item"
			}]
			x: 7.0d
			y: -5.500000000000011d
		}
		{
			dependencies: ["0D1A6B32FEB51FAD"]
			description: ["用切割机处理绿宝石方块可以得到绿宝石板"]
			id: "1ACD6672F72664D6"
			rewards: [{
				id: "4165234EDBAE1A23"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "08D29D56F156C82E"
				item: "gtceu:emerald_plate"
				type: "item"
			}]
			x: 10.0d
			y: -1.4999999999999984d
		}
		{
			dependencies: ["0D1A6B32FEB51FAD"]
			description: ["将红宝石方块放入切割机可以得到九个红宝石板"]
			id: "4DD7F3508B757EF0"
			rewards: [{
				id: "16A756955B6CB9B8"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "56729AE1F5992DB7"
				item: "gtceu:ruby_plate"
				type: "item"
			}]
			x: 4.0d
			y: -1.4999999999999984d
		}
		{
			dependencies: ["0D1A6B32FEB51FAD"]
			description: [
				"制作润滑剂有多种方法"
				""
				"我建议的一种方法是从鱼类中提取鱼油,然后将其蒸馏成润滑剂"
				""
				"润滑剂配合切割机非常实用,因为使用它的配方速度会快很多,&a比如&f相比用水"
			]
			id: "42FD79CEB3426861"
			rewards: [{
				count: 8
				id: "782AFBC0CD345E5F"
				item: "minecraft:tropical_fish"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "4DA40BBA92BE3271"
				item: "gtceu:lubricant_bucket"
				type: "item"
			}]
			x: 7.0d
			y: 1.5000000000000013d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"这通常用于矿石&a处理&f产线,通过在汞或过硫酸钠中洗涤碎矿来获取副产品"
				""
				"不过在这里,它也可以用来冷却炽热的硅锭"
			]
			id: "54A164C737660C4E"
			rewards: [{
				exclude_from_claim_all: true
				id: "77EA140702841EC6"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			tasks: [{
				id: "67DE2F5E9646F51B"
				item: "gtceu:mv_chemical_bath"
				type: "item"
			}]
			x: -1.5d
			y: -1.3000000000000003d
		}
		{
			dependencies: [
				"589F47DE51213920"
				"72E5439299E957A8"
			]
			description: [
				"将我们刚制作的这些粉末放入电爆炉,就能得到炽热的硅锭!"
				""
				"手持炽热锭会对你造成伤害,但为了完成这个任务你必须这么做——因为我有点邪恶"
				""
				"你需要冷却它,这里要用化学浴槽来处理"
			]
			id: "2AB457E29360E3B8"
			rewards: [
				{
					count: 2
					id: "08C5A61C36B2D91B"
					item: "gtceu:coal_dust"
					random_bonus: 2
					type: "item"
				}
				{
					id: "0588356A9AC8BE93"
					item: "gtceu:glass_dust"
					random_bonus: 1
					type: "item"
				}
			]
			subtitle: "&a烫手山芋&f"
			tasks: [{
				id: "00676E5631ED2A66"
				item: "gtceu:hot_silicon_ingot"
				type: "item"
			}]
			x: 1.5d
			y: -1.3000000000000003d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"你可以在&a末地&f找到原生铝矿,而粗铝几乎随处可见!"
				""
				"或者你也可以通过电解处理铝土矿等多种原料来获取铝"
				""
				"我们还能在黏土产线流程中被动生成铝"
			]
			id: "1C1802ABF3EE3120"
			rewards: [{
				count: 8
				id: "350821400A1DBB6F"
				item: "alltheores:raw_aluminum"
				random_bonus: 4
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			subtitle: "是你吗铝？"
			tasks: [{
				icon: "alltheores:aluminum_ingot"
				id: "7B46FA23AF26BF76"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:ingots/aluminum"
					}
				}
				title: "&a铝锭&f"
				type: "item"
			}]
			x: -7.5d
			y: -3.0d
		}
		{
			dependencies: [
				"11F7D3DDF5683EB3"
				"688AFF76CF5E599A"
			]
			description: ["&a玻璃透镜&f(绿色)也能制作这个,但彩色玻璃透镜属于高压配方"]
			id: "7EFA4F2C0A895502"
			rewards: [{
				id: "096F7065F33494AC"
				item: "gtceu:silicon_wafer"
				random_bonus: 1
				type: "item"
			}]
			tasks: [{
				id: "412F8874629333ED"
				item: "gtceu:ram_wafer"
				type: "item"
			}]
			x: 8.5d
			y: -2.4999999999999973d
		}
		{
			dependencies: [
				"11F7D3DDF5683EB3"
				"688AFF76CF5E599A"
			]
			description: ["&a玻璃透镜&f(红色)同样能制作这个,不过玻璃透镜属于高压配方"]
			id: "100ADA8508F6502A"
			rewards: [{
				id: "1D619C5D74EE82D7"
				item: "gtceu:silicon_wafer"
				random_bonus: 1
				type: "item"
			}]
			tasks: [{
				id: "0B29B51417A0773F"
				item: "gtceu:ilc_wafer"
				type: "item"
			}]
			x: 5.5d
			y: -2.4999999999999973d
		}
		{
			dependencies: [
				"629DC8DED0F6B578"
				"579A41570D610B07"
			]
			description: [
				"乙醇+&a硫酸&f在&e&a化学反应器&f&r中可制取乙烯"
				""
				"当然还有其他方法,但那些涉及石油化工的内容我们暂时不深入探讨"
			]
			id: "5E90EF7FF530C477"
			rewards: [{
				id: "2A6A82A57A3FBD42"
				item: "gtceu:sulfuric_acid_bucket"
				type: "item"
			}]
			tasks: [{
				id: "1D9C137DC559E76F"
				item: "gtceu:ethylene_bucket"
				type: "item"
			}]
			x: 1.5d
			y: 2.5d
		}
		{
			dependencies: [
				"307F92868E0F4EB3"
				"22AC248B6BB88486"
			]
			description: ["蒸馏生物质能得到乙醇——也就是酒精,但别告诉别人是我说的"]
			id: "629DC8DED0F6B578"
			rewards: [{
				id: "09DBB95EA63243FB"
				item: "gtceu:biomass_bucket"
				type: "item"
			}]
			subtitle: "非饮用酒"
			tasks: [{
				id: "70555BDDBADF02A3"
				item: "gtceu:ethanol_bucket"
				type: "item"
			}]
			x: 0.5d
			y: 3.5d
		}
		{
			description: [
				"用&a研钵&r研磨煤炭获得煤粉"
				""
				"将煤粉放入&e离心机&r得到碳粉"
				""
				"再次使用离心机,这次加入玻璃粉,就能得到二氧化硅粉"
			]
			id: "589F47DE51213920"
			rewards: [
				{
					count: 4
					id: "2FF0451D94263F24"
					item: "minecraft:coal"
					random_bonus: 4
					type: "item"
				}
				{
					count: 4
					id: "29731F158AA059AB"
					item: "minecraft:glass"
					random_bonus: 4
					type: "item"
				}
			]
			shape: "square"
			tasks: [
				{
					count: 2L
					id: "7FFE2CFE2C3F5E41"
					item: "gtceu:carbon_dust"
					type: "item"
				}
				{
					id: "7C28E6CC97ACE6A3"
					item: "gtceu:silicon_dioxide_dust"
					type: "item"
				}
			]
			title: "硅原料制备"
			x: 0.5d
			y: -2.2d
		}
		{
			dependencies: ["662E0A84D755064F"]
			description: ["这块&a硅板&f将让我们能制作晶体管!全新的电子元件,好耶!"]
			id: "06BBD7B736C564C1"
			rewards: [{
				id: "7154B2B74B4F5FAC"
				item: "gtceu:silicon_ingot"
				type: "item"
			}]
			tasks: [{
				id: "76730D2C8D492CAA"
				item: "gtceu:silicon_plate"
				type: "item"
			}]
			x: 2.5d
			y: -0.1d
		}
		{
			dependencies: [
				"492386DF6CA892BD"
				"53AF056139DAACDB"
			]
			description: ["生物质用途广泛,可用于生产乙醇和甲醇等"]
			id: "307F92868E0F4EB3"
			rewards: [{
				count: 2
				id: "6993315CE1780B9F"
				item: "gtceu:bio_chaff"
				type: "item"
			}]
			tasks: [{
				id: "17A555336A386EDA"
				item: "gtceu:biomass_bucket"
				type: "item"
			}]
			x: -0.5d
			y: 3.5d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"将化合物蒸馏成其他物质——注意查看可用配方对应的编程电路设置"
				""
				"虽然存在&a蒸馏塔&f,不过这个我们稍后再介绍"
			]
			id: "22AC248B6BB88486"
			rewards: [{
				exclude_from_claim_all: true
				id: "6E623304DC884B9A"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			tasks: [{
				id: "6DA7AD27CA056906"
				item: "gtceu:mv_distillery"
				type: "item"
			}]
			x: -1.5d
			y: 5.5d
		}
		{
			dependencies: ["262AE37765B139BE"]
			id: "492386DF6CA892BD"
			rewards: [{
				exclude_from_claim_all: true
				id: "498F5EA3939A2A64"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "不是那种酿酒厂"
			tasks: [{
				id: "47A9907066F31663"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:lv_brewery"
							}
							{
								Count: 1b
								id: "gtceu:mv_brewery"
							}
						]
					}
				}
				title: "酿造厂"
				type: "item"
			}]
			x: -1.5d
			y: 2.5d
		}
		{
			dependencies: ["6300791605A757B0"]
			description: [
				"粉碎那些植物球来制作生物碎料"
				""
				"自动化生产时,请确保设置输出仅为1份生物碎料而非4份.否则概率性产出会干扰自动合成系统"
			]
			id: "53AF056139DAACDB"
			rewards: [{
				count: 2
				id: "54C08D8193833F66"
				item: "gtceu:plant_ball"
				type: "item"
			}]
			tasks: [{
				id: "646E2D0A688D28AD"
				item: "gtceu:bio_chaff"
				type: "item"
			}]
			x: -1.5d
			y: 4.5d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: ["在世界中往&e碎石机&r左侧注水右侧灌入熔岩,然后放入一块闪长岩,就能看到它为你复制出更多闪长岩"]
			icon: "gtceu:mv_rock_crusher"
			id: "6275C90E5890C1E4"
			optional: true
			rewards: [
				{
					id: "32792BF84A213FBF"
					item: "minecraft:diorite"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "294610D7D543A42E"
					table_id: 7083859357644513434L
					type: "loot"
				}
			]
			shape: "diamond"
			size: 1.25d
			subtitle: "被动产铝"
			tasks: [{
				id: "2D0B938BFFE7D76C"
				item: "gtceu:mv_rock_crusher"
				type: "item"
			}]
			title: "黏土产线"
			x: -8.975000000000001d
			y: 2.9749999999999996d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: ["仅消耗能量就能制造磁性铁棒——节省你的红石吧!"]
			id: "1BD5B25B80EC0F97"
			rewards: [{
				exclude_from_claim_all: true
				id: "3CB7824290DDD795"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "磁化装置!"
			tasks: [{
				id: "4C32A748A4630A95"
				item: "gtceu:mv_polarizer"
				type: "item"
			}]
			x: -6.0d
			y: -3.0d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"挤出机通过使用模具将锭压制成各种形状"
				""
				"用挤出机制作转子等合成部件通常更划算"
			]
			id: "0A2675CF16B6443B"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "1922767923C7B993"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "0997C42AD73E0890"
				item: "gtceu:mv_extruder"
				type: "item"
			}]
			x: -6.0d
			y: 3.0d
		}
		{
			dependencies: ["0A2675CF16B6443B"]
			id: "53E1B892386DD42F"
			optional: true
			rewards: [{
				count: 2
				id: "1C768CA0AE2851DA"
				item: "alltheores:steel_plate"
				random_bonus: 2
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "1724B18A1A47C232"
				item: "gtceu:rotor_extruder_mold"
				type: "item"
			}]
			x: -6.5d
			y: 3.5d
		}
		{
			dependencies: ["0A2675CF16B6443B"]
			id: "54A619012A5FD814"
			optional: true
			rewards: [{
				count: 2
				id: "1B73202C86C5ADAE"
				item: "alltheores:steel_plate"
				random_bonus: 2
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "74B66E8EEC327F1D"
				item: "gtceu:normal_pipe_extruder_mold"
				type: "item"
			}]
			x: -6.0d
			y: 4.0d
		}
		{
			dependencies: ["0A2675CF16B6443B"]
			id: "1E5032F8420016AB"
			optional: true
			rewards: [{
				count: 2
				id: "53CDC4546DC8F088"
				item: "alltheores:steel_plate"
				random_bonus: 2
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "4366765C95E0F2DA"
				item: "gtceu:bolt_extruder_mold"
				type: "item"
			}]
			x: -5.5d
			y: 3.5d
		}
		{
			dependencies: ["1BD5B25B80EC0F97"]
			description: ["制作MV机器组件&a电动马达&f时会用到这些材料"]
			id: "497028C92F886AE0"
			rewards: [{
				id: "7A94DDAC0325A87A"
				item: "gtceu:magnetic_steel_rod"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "638FE467700933F2"
				item: "gtceu:magnetic_steel_rod"
				type: "item"
			}]
			x: -5.0d
			y: -3.5d
		}
		{
			dependencies: ["72DACA35906D7E5B"]
			description: ["终于到了电解黏土粉尘的时候了,获取那甜美的铝粉"]
			id: "41FCD7F5B7E6E261"
			optional: true
			rewards: [{
				count: 3
				id: "59E36712DBB6484F"
				item: "gtceu:silicon_dust"
				random_bonus: 2
				type: "item"
			}]
			shape: "diamond"
			subtitle: "硅元素的优质来源"
			tasks: [{
				id: "1AF82AB8FAD6E515"
				item: "gtceu:mv_electrolyzer"
				type: "item"
			}]
			x: -9.000000000000002d
			y: 5.5d
		}
		{
			dependencies: [
				"6E9126ECA080E725"
				"72DACA35906D7E5B"
			]
			description: [
				"我们的首个覆盖板!覆盖板能以多种方式改变机器行为(不过这里不展开说明)"
				""
				"&a机械臂&r覆盖板默认允许你导出物品,也可设置为向机器导入物品.配合之前的缓冲箱/桶,可专门导入闪长岩粉尘"
				""
				"为什么MV阶段使用LV覆盖板？因为造价更低,且覆盖板不会因电压差异而爆炸"
			]
			icon: "gtceu:lv_robot_arm"
			id: "2341D611975C58AF"
			optional: true
			rewards: [{
				count: 2
				id: "5B1A93B5FB22A8D6"
				item: "alltheores:steel_rod"
				random_bonus: 2
				type: "item"
			}]
			shape: "diamond"
			subtitle: "自动导入？太棒了"
			tasks: [{
				id: "4A4FD0588A9C3448"
				item: "gtceu:lv_robot_arm"
				type: "item"
			}]
			x: -8.300000000000002d
			y: 4.800000000000001d
		}
		{
			dependencies: ["6275C90E5890C1E4"]
			description: [
				"将闪长岩研磨成闪长岩粉尘!同时有小概率产出石粉需注意处理"
				""
				"建议设置自动输出至缓冲箱/木桶,并将石粉直接丢弃"
			]
			id: "6E9126ECA080E725"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "50081BD1D618A429"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "026C2FC05C707BFF"
				item: "gtceu:mv_macerator"
				type: "item"
			}]
			x: -9.000000000000002d
			y: 4.100000000000001d
		}
		{
			dependencies: ["6E9126ECA080E725"]
			description: [
				"离心分离闪长岩粉尘获取黏土粉尘与芒硝粉"
				""
				"芒硝粉可留存后续处理"
				""
				"下一步骤仅需黏土粉尘,需自行处理芒硝粉"
			]
			id: "72DACA35906D7E5B"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "27269BEDB3689DA1"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "2F87D396311C6602"
				item: "gtceu:mv_centrifuge"
				type: "item"
			}]
			x: -9.700000000000001d
			y: 4.800000000000001d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: ["该工序消耗更多蒸汽!可能需要再建一台锅炉"]
			id: "2AEDB489A017AAF9"
			optional: true
			rewards: [
				{
					count: 6
					id: "30E05DCEB338719C"
					item: "alltheores:steel_plate"
					random_bonus: 6
					type: "item"
				}
				{
					count: 3
					id: "744C49134916734F"
					item: "gtceu:aluminium_plate"
					random_bonus: 2
					type: "item"
				}
			]
			subtitle: "蒸汽全开!"
			tasks: [{
				id: "28BC15EC9BCAB1B6"
				item: "gtceu:mv_steam_turbine"
				type: "item"
			}]
			x: -9.5d
			y: -1.0d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: ["为多台机器供电时,注意线损问题!"]
			id: "428C034C78923B6C"
			optional: true
			rewards: [
				{
					count: 2
					id: "4E427896A544A91E"
					item: "gtceu:red_alloy_single_wire"
					random_bonus: 4
					type: "item"
				}
				{
					count: 4
					id: "0B1328663DAC2B0E"
					item: "gtceu:copper_single_wire"
					random_bonus: 8
					type: "item"
				}
				{
					count: 3
					id: "09A0EA5857D5AE4D"
					item: "gtceu:aluminium_plate"
					random_bonus: 2
					type: "item"
				}
			]
			tasks: [{
				id: "71567A9437A86C2F"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:mv_1a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:mv_4a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:mv_8a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:mv_16a_energy_converter"
							}
						]
					}
				}
				title: "任意MV能量转换器"
				type: "item"
			}]
			title: "MV &a能源转换&f"
			x: -9.5d
			y: 1.5d
		}
		{
			description: [
				"压缩机处理八个植物可合成植物球"
				""
				"离心制作胶水时也有概率产出"
			]
			id: "6300791605A757B0"
			rewards: [{
				count: 8
				id: "7E0D1BD5A8DFD24A"
				item: "minecraft:potato"
				type: "item"
			}]
			shape: "square"
			subtitle: "压缩植物物质"
			tasks: [{
				id: "577EEBAF4FACDCA1"
				item: "gtceu:plant_ball"
				type: "item"
			}]
			x: -2.5d
			y: 3.5d
		}
		{
			dependencies: ["24E3CB198F73524A"]
			description: ["在电弧炉中用铜锭与63mB氧气可制得退火铜锭"]
			id: "4E1D5EC061A4AE55"
			rewards: [{
				count: 2
				id: "34166C7A9BD6EC7C"
				item: "gtceu:annealed_copper_bolt"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "5CB8068643C87602"
				item: "gtceu:annealed_copper_ingot"
				type: "item"
			}]
			x: -0.5d
			y: -5.5d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"少量氧气加电能即可实现铜材退火"
				""
				"此法还能将旧机器分解为锭形态(不同于粉碎机的粉尘产出)"
			]
			icon: "gtceu:mv_arc_furnace"
			id: "24E3CB198F73524A"
			rewards: [{
				exclude_from_claim_all: true
				id: "617745FCC8063446"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "电弧闪耀!"
			tasks: [{
				id: "77EF6FE6C54C104A"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:lv_arc_furnace"
							}
							{
								Count: 1b
								id: "gtceu:mv_arc_furnace"
							}
						]
					}
				}
				title: "&a电弧炉&f"
				type: "item"
			}]
			x: -1.5d
			y: -5.5d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"该机器使用频率极高,升级设备可大幅提升处理速度"
				""
				"建议批量建造以实现并行处理"
			]
			id: "579A41570D610B07"
			rewards: [{
				exclude_from_claim_all: true
				id: "7C10D5F463B33C47"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "实验服穿好了吗？"
			tasks: [{
				id: "3165B02F2BFFEDDA"
				item: "gtceu:mv_chemical_reactor"
				title: "&a化学反应器&f"
				type: "item"
			}]
			x: -1.5d
			y: 1.5d
		}
		{
			dependencies: ["4E1D5EC061A4AE55"]
			description: [
				"还在工作台合成电阻？"
				""
				"将退火铜制成细导线,加入胶水和碳粉,即可在组装机中一次性产出4个电阻"
				""
				"这才是配方升级!"
			]
			id: "4AE05E80142C12A6"
			optional: true
			rewards: [{
				count: 2
				id: "0F34F1836D2E1927"
				item: "gtceu:resistor"
				random_bonus: 2
				type: "item"
			}]
			tasks: [
				{
					count: 4L
					id: "2F26EFD73DE0E8A1"
					item: "gtceu:fine_annealed_copper_wire"
					type: "item"
				}
				{
					id: "427138127BEBAEAF"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "gtceu:carbon_dust"
								}
								{
									Count: 1b
									id: "chemlib:carbon_dust"
								}
								{
									Count: 1b
									id: "gtceu:charcoal_dust"
								}
								{
									Count: 1b
									id: "railcraft:charcoal_dust"
								}
								{
									Count: 1b
									id: "gtceu:coal_dust"
								}
								{
									Count: 1b
									id: "chemlib:coal_dust"
								}
								{
									Count: 1b
									id: "enderio:powdered_coal"
								}
								{
									Count: 1b
									id: "railcraft:coal_dust"
								}
							]
						}
					}
					title: "碳粉"
					type: "item"
				}
			]
			title: "电阻再进化"
			x: 0.5d
			y: -5.5d
		}
		{
			description: [
				"全新的矿石处理装置!"
				""
				"[ \"\", { \"text\": \"The \" }, { \"text\": \"Sifter \", \"color\":\"yellow\" }, { \"text\": \"replaces the Thermal Centrifuge and 2nd Macerator in the \" }, { \"text\":\"矿石&a处理&f装置\", \"color\":\"gold\", \"clickEvent\": { \"action\":\"change_page\", \"value\":\"25DBFE887B041E94\" }, \"underlined\":\"true\", \"hoverEvent\": { \"action\":\"show_text\", \"contents\": { \"text\":\"&a点击此处&f查看提示\" } } } ]"
				""
				"如果你能巧妙运用过滤器和物品流系统,不必专门为此新建装置——不过新建一套可能反而更省事"
			]
			id: "51DB667E991E51F0"
			rewards: [{
				exclude_from_claim_all: true
				id: "311B689EC83C4652"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			tasks: [{
				id: "0ED34925D7A494BF"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:lv_sifter"
							}
							{
								Count: 1b
								id: "gtceu:mv_sifter"
							}
						]
					}
				}
				title: "筛矿机"
				type: "item"
			}]
			x: 7.699999999999999d
			y: -4.300000000000001d
		}
		{
			dependencies: ["51DB667E991E51F0"]
			description: [
				"将&a原始绿宝石&r或精准采集的&a下界绿宝石矿石&r通过粉碎机处理,经&a洗矿厂&f提纯后,开始&e筛矿&r获取&b精良&r或&b无瑕&r宝石"
				""
				"必须使用&d格雷科技&f专属绿宝石矿石(普通精准采集绿宝石矿无效)"
				""
				"&e&l提示:&r寻找&d格雷科技&f™绿宝石时,注意&a挖矿维度&f&a下界层&r的&d铍矿石&r!"
			]
			id: "644B16B9618B41BE"
			min_width: 250
			rewards: [{
				count: 2
				id: "4361FB8B464B2750"
				item: "gtceu:flawless_emerald_gem"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "4DB8770CF9F924C5"
				item: "gtceu:flawless_emerald_gem"
				type: "item"
			}]
			x: 6.4d
			y: -4.300000000000001d
		}
		{
			dependencies: [
				"63621F0189B6EB1B"
				"7963008B930D84BB"
			]
			description: ["&bMV切割机&r的必要组件"]
			hide_dependent_lines: true
			id: "707EBA5717938515"
			rewards: [
				{
					count: 2
					id: "049FA0E13A9C9EB8"
					item: "gtceu:chromium_dust"
					random_bonus: 2
					type: "item"
				}
				{
					count: 2
					id: "20B01BB434069711"
					item: "gtceu:vanadium_dust"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "circle"
			tasks: [{
				id: "72380DD47A0B98BD"
				item: "gtceu:vanadium_steel_ingot"
				type: "item"
			}]
			x: -0.5d
			y: -3.6d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: ["&b钒钢粉&r属于MV级混合机配方,是时候升级设备了!"]
			id: "63621F0189B6EB1B"
			rewards: [{
				exclude_from_claim_all: true
				id: "43720EC35D614D1C"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			tasks: [{
				id: "72EB03DB951E83D4"
				item: "gtceu:mv_mixer"
				type: "item"
			}]
			x: -1.5d
			y: -2.2d
		}
		{
			dependencies: ["262AE37765B139BE"]
			description: [
				"这台机器对于获取&d铬粉&r非常有用,我们需要用它来制作&b不锈钢粉&r和&b钒钢粉"
				""
				"你可以连续处理&a红石粉&f获得红宝石粉,然后通过&e电解&r得到&d&a铬粉&f"
			]
			id: "7963008B930D84BB"
			rewards: [{
				exclude_from_claim_all: true
				id: "5DF8C2DC3E30CD77"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			tasks: [{
				id: "0AEB920BEAD1C447"
				item: "gtceu:mv_electrolyzer"
				type: "item"
			}]
			x: -1.5d
			y: -3.1d
		}
		{
			dependencies: ["63621F0189B6EB1B"]
			description: ["将铝粉、铁粉和铬粉一起放入&e混合机&r中,观察它们融合的过程!"]
			id: "1812BF72305CFFCF"
			rewards: [{
				count: 4
				id: "74819F7F2D53028B"
				item: "gtceu:chromium_dust"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "2CF35B15AD75E179"
				item: "gtceu:kanthal_dust"
				type: "item"
			}]
			x: -0.5d
			y: -2.2d
		}
		{
			dependencies: [
				"1812BF72305CFFCF"
				"54A164C737660C4E"
				"3DCCEDC5A817EBEB"
			]
			description: ["在&a电力高炉&r中熔炼这些粉末,然后在&e&a化学浸洗器&f中冷却"]
			id: "358706CA93DC2A9C"
			rewards: [{
				count: 4
				id: "71E792F83E0444B2"
				item: "gtceu:chromium_dust"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "4E1E15185D672109"
				item: "gtceu:kanthal_ingot"
				type: "item"
			}]
			x: -0.5d
			y: -1.3000000000000003d
		}
		{
			dependencies: ["358706CA93DC2A9C"]
			description: [
				"使用&e提取机&r将铜转化为液态"
				""
				"用&e线缆切割机&r切割坎塔尔锭"
				""
				"&e弯曲&r那些铝材"
				""
				"然后在你的&e装配机&r中将所有部件组装起来!"
				""
				"用这些材料替换你&a电力高炉&r上的铜镍合金线圈"
			]
			id: "72E5439299E957A8"
			rewards: [{
				exclude_from_claim_all: true
				id: "086F84F7B79F7B8B"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			shape: "gear"
			tasks: [{
				count: 16L
				id: "6C8DCC0E471D0163"
				item: "gtceu:kanthal_coil_block"
				type: "item"
			}]
			x: 0.5d
			y: -1.3000000000000003d
		}
		{
			dependencies: ["1F92F7314DF3C3E2"]
			description: [
				"按照制作绿宝石/红宝石透镜的相同步骤制作&9蓝宝石透镜"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"4DD7F3508B757EF0\"}, \"text\": \"How to make the gem plate\", \"color\": \"green\", \"hoverEvent\": { \"action\":\"show_text\", \"contents\": { \"text\":\"&a点击此处&f查看提示\" } }}"
				""
				"{\"clickEvent\": {\"action\": \"change_page\", \"value\": \"26004F997C758011\"}, \"text\": \"How to make the gem lens\", \"color\": \"yellow\", \"hoverEvent\": { \"action\":\"show_text\", \"contents\": { \"text\":\"&a点击此处&f查看提示\" } }}"
			]
			hide_dependency_lines: true
			id: "20A0A574F51EEF74"
			tasks: [{
				id: "71285AC928BBD40E"
				item: "gtceu:sapphire_lens"
				type: "item"
			}]
			x: -3.5d
			y: -0.20000000000000004d
		}
		{
			dependencies: ["20A0A574F51EEF74"]
			description: ["没错,又一个&e激光雕刻&r配方...这些任务正在变得非线性化"]
			id: "5B92DA55541B168B"
			rewards: [{
				count: 2
				id: "0B4A34429F4F377A"
				item: "gtceu:silicon_wafer"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "0B0A35D87D18E3A1"
				item: "gtceu:ulpic_wafer"
				type: "item"
			}]
			x: -2.5d
			y: -0.4d
		}
		{
			dependencies: ["5B92DA55541B168B"]
			description: ["回到&e切割机&r继续加工!"]
			id: "4E5FE40373675AED"
			rewards: [{
				id: "27DFB41D8F75BF3B"
				type: "xp"
				xp: 250
			}]
			tasks: [{
				id: "1C973641E5F60EB4"
				item: "gtceu:ulpic_chip"
				type: "item"
			}]
			x: -1.5d
			y: -0.4d
		}
		{
			dependencies: ["4E5FE40373675AED"]
			description: [
				"你可以使用GT组装机或普通合成台制作这个.由于图案空间很宝贵,特别是对于GT组装机来说,或许&a分子装配室&f更适合处理这种组装工作"
				""
				"将你&e&a电力高炉&f&r上的LV能源仓替换为这些升级版,现在你的EBF可以处理&eHV&r级配方了!"
				""
				"记得也要升级你的能源供应!"
			]
			id: "3DCCEDC5A817EBEB"
			min_width: 250
			rewards: [{
				exclude_from_claim_all: true
				id: "5FF28AC1A353565C"
				table_id: 7083859357644513434L
				type: "loot"
			}]
			subtitle: "电力高炉升级"
			tasks: [{
				count: 2L
				id: "3E686316F9E6B29E"
				item: "gtceu:mv_energy_input_hatch"
				type: "item"
			}]
			x: -0.5d
			y: -0.20000000000000004d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包都采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"这个任务被故意隐藏,如果你看到这个提示,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "3578F6AB3A810572"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "196DF8FE38484BD9"
					title: "全模组任务集"
					type: "checkmark"
				}
				{
					id: "7FC8CE068042076A"
					title: "全模组任务集"
					type: "checkmark"
				}
			]
			x: -7.5d
			y: 2.0d
		}
	]
	title: "中压电"
}
