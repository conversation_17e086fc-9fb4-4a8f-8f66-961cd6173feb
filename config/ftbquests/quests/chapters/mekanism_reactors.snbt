{
	default_hide_dependency_lines: false
	default_min_width: 200
	default_quest_shape: ""
	filename: "mekanism_reactors"
	group: "2B51AC12041E3F89"
	icon: "mekanism:supercharged_coil"
	id: "0A093D8C4429B627"
	images: [
		{
			height: 3.0d
			image: "ftbquests:tasks/input_only"
			rotation: 45.0d
			width: 3.0d
			x: 17.0d
			y: -7.0d
		}
		{
			height: 3.0d
			image: "ftbquests:tasks/input_only"
			rotation: 0.0d
			width: 3.0d
			x: 17.0d
			y: -7.0d
		}
		{
			height: 3.0d
			image: "ftbquests:block/barrier_open"
			rotation: 0.0d
			width: 3.0d
			x: 2.0d
			y: -7.0d
		}
		{
			height: 3.0d
			image: "ftbquests:block/barrier_open"
			rotation: 45.0d
			width: 3.0d
			x: 2.0d
			y: -7.0d
		}
		{
			height: 1.0d
			image: "mekanism:item/hazmat_mask"
			rotation: 0.0d
			width: 1.0d
			x: -1.0d
			y: 0.75d
		}
		{
			height: 1.0d
			image: "mekanism:item/hazmat_gown"
			rotation: 0.0d
			width: 1.0d
			x: -1.0d
			y: 1.25d
		}
		{
			height: 1.0d
			image: "mekanism:item/hazmat_pants"
			rotation: 0.0d
			width: 1.0d
			x: -1.0d
			y: 1.75d
		}
		{
			height: 1.0d
			image: "mekanism:item/hazmat_boots"
			rotation: 0.0d
			width: 1.0d
			x: -1.0d
			y: 2.25d
		}
		{
			height: 1.0d
			image: "mekanism:item/fluorite_gem"
			rotation: 0.0d
			width: 1.0d
			x: 8.0d
			y: 1.5d
		}
		{
			height: 1.0d
			image: "mekanism:item/yellow_cake_uranium"
			rotation: 0.0d
			width: 1.0d
			x: 8.0d
			y: 3.5d
		}
		{
			height: 11.0d
			hover: ["atm9.quest.mekanismReactors.fusionReactor"]
			image: "atm:textures/questpics/mek/fusion_cutout.png"
			rotation: 0.0d
			width: 10.627118644067796d
			x: 17.0d
			y: -13.0d
		}
		{
			height: 12.0d
			hover: ["atm9.quest.mekanismReactors.industrialTurbine"]
			image: "atm:textures/questpics/mek/turbine_cutout.png"
			rotation: 0.0d
			width: 10.536144578313253d
			x: 20.0d
			y: 0.0d
		}
		{
			height: 10.0d
			hover: ["atm9.quest.mekanismReactors.fissionReactor"]
			image: "atm:textures/questpics/mek/fission_cutout.png"
			rotation: 0.0d
			width: 9.661016949152541d
			x: 2.0d
			y: 9.5d
		}
		{
			height: 10.0d
			hover: ["atm9.quest.mekanismReactors.supercriticalPhaseShifter"]
			image: "atm:textures/questpics/mek/sps_cutout.png"
			rotation: 0.0d
			width: 12.687687687687689d
			x: -4.5d
			y: -7.0d
		}
		{
			alpha: 150
			height: 1.5d
			image: "ae2:block/controller_column_lights"
			rotation: 90.0d
			width: 1.5d
			x: 4.0d
			y: -1.0d
		}
		{
			height: 1.0d
			image: "mob_grinding_utils:block/fan_front_on"
			rotation: -45.0d
			width: 1.0d
			x: 11.0d
			y: -0.5d
		}
		{
			height: 1.0d
			image: "mob_grinding_utils:block/fan_front_on"
			rotation: -45.0d
			width: 1.0d
			x: 11.0d
			y: 1.5d
		}
		{
			height: 1.0d
			image: "ftbquests:block/screen_side"
			order: -1
			rotation: 0.0d
			width: 5.0d
			x: 12.5d
			y: -6.0d
		}
		{
			height: 5.0d
			image: "ars_nouveau:textures/particle/laser.png"
			rotation: 90.0d
			width: 0.5d
			x: 12.5d
			y: -6.0d
		}
		{
			height: 1.0d
			image: "securitycraft:block/ani_laser"
			rotation: 0.0d
			width: 0.021739130434782608d
			x: 12.0d
			y: -4.5d
		}
		{
			height: 1.0d
			image: "securitycraft:block/ani_laser"
			rotation: 0.0d
			width: 0.021739130434782608d
			x: 13.0d
			y: -4.0d
		}
		{
			height: 0.3d
			hover: ["atm9.quest.ae2.img.star"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 2.0d
			y: -9.0d
		}
		{
			height: 0.3d
			hover: ["atm9.quest.ae2.img.star"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 4.0d
			y: 0.15d
		}
	]
	order_index: 8
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["24174700F7FB771C"]
			description: [
				"开启&d通用机械&f反应堆世界之旅前,我们先建造&a&a裂变反应堆&f&r.这种多方块结构通过燃烧&3&a裂变燃料&f&r产生巨量热能.该反应堆本身不发电,但其产生的热能可加热&b冷却剂&r,用于驱动&a&a工业涡轮&f&r发电."
				""
				"&a裂变反应堆&f极其危险——堆芯熔毁将引发&c爆炸&r,并导致&2辐射&r扩散至5区块范围,持续数游戏周."
				""
				"但我们将做好准备.先制作一套&a防化服&f以防万一...对吧？"
			]
			id: "36B1995B495AA674"
			min_width: 350
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1A6ABA2C6D742CE0"
					table_id: 7197061527498167330L
					type: "random"
				}
				{
					id: "6D8B557D90A02403"
					type: "xp"
					xp: 10
				}
			]
			shape: "gear"
			subtitle: "橙色=&a辐射防护&f"
			tasks: [
				{
					id: "14BF9ADCB8F43ADC"
					item: "mekanism:hazmat_mask"
					type: "item"
				}
				{
					id: "0B06DCC5BA15895E"
					item: "mekanism:hazmat_gown"
					type: "item"
				}
				{
					id: "1F43C3AE8239B716"
					item: "mekanism:hazmat_pants"
					type: "item"
				}
				{
					id: "68B050A8CDCDCD10"
					item: "mekanism:hazmat_boots"
					type: "item"
				}
			]
			title: "反应堆防护准备"
			x: 0.0d
			y: 0.5d
		}
		{
			dependencies: ["36B1995B495AA674"]
			description: [
				"现在收集反应堆建造材料."
				""
				"与多数&d通用机械&f多方块结构相同,反应堆尺寸可自定义.最小外框尺寸为3宽×4高×3深,最大18×18×18.&a我们建议初始建造5×5规格&r."
				""
				"外围框架&b必须&r使用&a&a裂变反应堆外壳&f&r,立面可选用外壳、&b&a反应堆玻璃&f&r、&a反应堆端口&f或&a反应堆逻辑适配器&f(后续详解)."
				""
				"现在开始建造基础5×5×5的&a裂变反应堆&f吧!"
				""
				"需要建造指导？手持&dw&r键悬停查看&a裂变反应堆外壳&f,将显示辅助建造的交互演示."
			]
			hide_until_deps_visible: false
			id: "1482F2D45E8F761D"
			min_width: 300
			rewards: [
				{
					id: "0792BEFF2D7C604C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "251D1877952196CD"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "square"
			subtitle: "外壳与玻璃"
			tasks: [
				{
					count: 44L
					id: "19447D5D621A8D89"
					item: "mekanismgenerators:fission_reactor_casing"
					type: "item"
				}
				{
					count: 50L
					id: "0765AB709CC3C6E2"
					item: "mekanismgenerators:reactor_glass"
					type: "item"
				}
			]
			title: "&a裂变反应堆&f建造基础"
			x: 0.0d
			y: 2.5d
		}
		{
			dependencies: ["1482F2D45E8F761D"]
			description: [
				"要用&a裂变反应堆&f输入输出物品,需要&a&a裂变反应堆端口&f&r.使用&9配置器&r可设置输入/输出模式"
				""
				"每个&a裂变反应堆&f&a至少需要4个端口&r:"
				""
				"1个冷却剂输入口"
				"1个冷却剂输出口"
				"1个&a裂变燃料&f输入口"
				"1个废料输出口"
				""
				"初始建造时建议如图每面放置端口,务必按输入输出列表配置!"
				""
				"{image:atm:textures/questpics/mek/port_example.png width:200 height:200 align:1}"
			]
			id: "0696B725E840B996"
			min_width: 400
			rewards: [
				{
					id: "11FABC06766669F5"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "08C5A528A0456C24"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "rsquare"
			tasks: [{
				count: 4L
				id: "057CDA5BC0C5F774"
				item: "mekanismgenerators:fission_reactor_port"
				type: "item"
			}]
			title: "与&a裂变反应堆&f交互"
			x: 1.5d
			y: 2.5d
		}
		{
			dependencies: ["1482F2D45E8F761D"]
			description: [
				"害怕&a裂变反应堆&f爆炸导致辐射泄漏？别担心,我们都怕.&o*此处应有可怕回忆闪回*"
				""
				"如何预防这种灾难？用&d&a裂变反应堆逻辑适配器&f&r制作红石&a&a断路器&f&r.虽不能100%%防爆,但绝对值得配备"
				""
				"这些适配器让我们能用&c红石&r控制反应堆.单个适配器配合拉杆即可启停反应堆,但我们将用于损害管控"
				""
				"还可设置为根据反应堆内状态(如&c&a损伤临界值&f&r或&8燃料不足&r)发出&a红石信号&f,便于设置断路器在异常时关闭反应堆"
				""
				"{image:atm:textures/questpics/mek/logic_adapter.png width:150 height:125 align:1}"
			]
			id: "5FDB48511EC1C580"
			min_width: 300
			optional: true
			rewards: [{
				id: "2E5FE4E46429A82D"
				type: "xp"
				xp: 50
			}]
			shape: "hexagon"
			subtitle: "因为我们都炸过反应堆"
			tasks: [{
				id: "16B10EA105CCC1D3"
				item: "mekanismgenerators:fission_reactor_logic_adapter"
				type: "item"
			}]
			title: "反应堆安全机制"
			x: 1.5d
			y: 3.5d
		}
		{
			dependencies: ["1482F2D45E8F761D"]
			description: [
				"反应堆内部由多个&a&a裂变燃料组件&f&r方块构成的柱子组成,每个柱子顶部放置一个&a&a控制棒组件&f&r.这些柱子的高度可以从1到15个方块不等,具体取决于反应堆的大小."
				""
				"在这个建造中,我们将在多方块结构的中心放置2个裂变燃料组件,然后在顶部放置&a控制棒组件&f."
				""
				"{image:atm:textures/questpics/mek/rod_example.png width:250 height:200 align:1}"
			]
			id: "3F5010269469EBE0"
			min_width: 250
			rewards: [
				{
					id: "6819DA9F73BA383C"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "1BF0FCC1C88A003D"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "rsquare"
			tasks: [
				{
					count: 2L
					id: "032045BB026F44B3"
					item: "mekanismgenerators:fission_fuel_assembly"
					type: "item"
				}
				{
					id: "0875D821930A348A"
					item: "mekanismgenerators:control_rod_assembly"
					type: "item"
				}
			]
			title: "反应堆内部:燃料控制"
			x: 1.5d
			y: 1.5d
		}
		{
			dependencies: ["5FDB48511EC1C580"]
			description: [
				"利用一些原版机制和2个&a&a裂变反应堆逻辑适配器&f&r,我们可以创建一个简单的断路器,在&o&e情况变得有点疯狂&r时跳闸并关闭反应堆."
				""
				"为此,我们需要一块红石、一个活塞、一个沙子或沙砾方块,以及一个观察者."
				""
				"在其中一面上,我们先放置一个逻辑适配器,然后在其上方隔一个方块放置另一个适配器.将顶部的适配器设置为&9\"激活\"&r,底部的适配器设置为&c\"&a损伤临界值&f\"&r."
				""
				"接下来,在底部适配器的前方和下方放置任意建筑方块,并在其上放置一块红石,从适配器引出.然后在前方的红石处放置一个朝上的活塞,并在活塞上放置我们的沙子或沙砾."
				""
				"对于断路器的最后部分,放置观察者,使其面朝&b外部的活塞设置&r.&9这很重要&r!"
				""
				"当反应堆达到临界损伤时,它将激活底部适配器,导致红石接收到信号,从而激活活塞并推动沙砾/沙子.这将激活观察者,然后关闭反应堆."
				""
				"{image:atm:textures/questpics/mek/example_circuit.png width:200 height:250 align:1}"
			]
			icon: "minecraft:observer"
			id: "7B0764DDE94E73D0"
			min_width: 500
			optional: true
			rewards: [
				{
					id: "102A352650624E7C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "070E292041FDB840"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "hexagon"
			subtitle: "需要图示？观看Ponder动画!"
			tasks: [
				{
					count: 2L
					id: "25FB07BDEC8EE4EF"
					item: "mekanismgenerators:fission_reactor_logic_adapter"
					type: "item"
				}
				{
					id: "6F57410F914C4D07"
					item: "minecraft:redstone"
					type: "item"
				}
				{
					id: "5BC88AE46F95774D"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "minecraft:gravel"
								}
								{
									Count: 1b
									id: "minecraft:sand"
								}
							]
						}
					}
					title: "沙砾或沙子"
					type: "item"
				}
				{
					id: "584B8A392FBCD199"
					item: "minecraft:piston"
					type: "item"
				}
				{
					id: "139EDEDEA3DAD312"
					item: "minecraft:observer"
					type: "item"
				}
			]
			title: "示例&a断路器&f"
			x: 1.5d
			y: 4.5d
		}
		{
			dependencies: ["1DE8B0C9A7195720"]
			description: [
				"当燃烧燃料时,&a裂变反应堆&f会产生大量热量.为防止反应堆转化为TNT,我们需要确保其得到适当冷却."
				""
				"最简单的方法是通过&a水槽&f向反应堆输入&9水&r.&a水槽&f是无限水源,&o对于这种情况来说非常理想&r."
				""
				"将水泵入反应堆设置为&a输入&r的端口,使反应堆充满水.这些水在反应堆运行时会受热转化为&b蒸汽&r,可用于在&9&a工业涡轮&f&r中发电."
				""
				"&e钠&r也可用作更高效的冷却剂,能实现更高的燃烧率和更低的核心温度."
			]
			id: "2B76B2F18C2C47D2"
			min_width: 300
			progression_mode: "linear"
			rewards: [
				{
					id: "1E2809FAFCF0A292"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "573EC47D2235DA05"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "rsquare"
			subtitle: "高品质H₂O"
			tasks: [{
				id: "7E8281C007094843"
				item: "cookingforblockheads:sink"
				type: "item"
			}]
			title: "冷却我们的反应堆"
			x: 3.0d
			y: 4.0d
		}
		{
			dependencies: [
				"0696B725E840B996"
				"3F5010269469EBE0"
			]
			description: [
				"一旦你完成了放置所有建造反应堆所需的方块,它应该会发出红色粒子来显示它已经完成."
				""
				"&a右击&f反应堆的任何地方将打开&a界面&r.这里将包含你正确运行反应堆所需的所有信息,以及两个用于开启和关闭反应堆的按钮."
				""
				"在左侧,你有2个储罐:一个用于&b冷却剂&r,一个用于&3&a裂变燃料&f&r.在右侧,你有一个用于&8&a核废料&f&r,一个用于&b加热的冷却剂&r,这很可能是&b蒸汽&r."
				""
				"&c温度&r条将显示反应堆的热度.在达到一定温度后,反应堆将开始承受&4损伤&r,最终导致反应堆爆炸."
				""
				"要调整&a裂变燃料&f的&c燃烧速率&r并查看更多统计信息,点击左侧的(I)标签.在这里,你可以调整速率限制,它控制反应堆每刻燃烧多少燃料."
				""
				"{image:atm:textures/questpics/mek/reactor_interface_1.png width:200 height:150 align:1}"
				"{image:atm:textures/questpics/mek/reactor_interface_2.png width:200 height:150 align:1}"
			]
			icon: "mekanism:geiger_counter"
			id: "1DE8B0C9A7195720"
			min_width: 500
			progression_mode: "linear"
			rewards: [
				{
					id: "08BFEE4DB82A488D"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "24535A9B5B195CDB"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "square"
			size: 1.5d
			subtitle: "这些绝对是数字"
			tasks: [{
				id: "7231A50B09F6218A"
				item: "mekanismgenerators:fission_reactor_casing"
				type: "item"
			}]
			title: "&a裂变反应堆&f界面"
			x: 3.0d
			y: 2.5d
		}
		{
			dependencies: ["3B81800EE6E77EF2"]
			description: [
				"每个反应堆都需要铀作为燃料,对吧？"
				""
				"首先收集些&a&a铀锭&f&r,需要通过&9&a富集仓&f&r加工成&e&a铀黄饼&f&r."
			]
			icon: "mekanism:yellow_cake_uranium"
			id: "7E17AB5A4492929E"
			rewards: [
				{
					id: "48CC9D36035B2625"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "6F9EDF210644BE81"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "rsquare"
			subtitle: "当然要用铀"
			tasks: [
				{
					id: "5641530385DCA6A7"
					item: "alltheores:uranium_ingot"
					type: "item"
				}
				{
					count: 2L
					id: "1C72C0C59D65B1D0"
					item: "mekanism:yellow_cake_uranium"
					type: "item"
				}
			]
			title: "&a铀"
			x: 5.5d
			y: 3.5d
		}
		{
			dependencies: ["7E17AB5A4492929E"]
			description: ["获得&e&a铀黄饼&f&r后,可将其送入&a&a化学氧化机&f&r生成气体&2&a氧化铀&f&r."]
			id: "3AF4E5D4839CEF8B"
			progression_mode: "linear"
			rewards: [
				{
					id: "147637FB74512198"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "38EBF2DB27CB0D4B"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [
				{
					id: "772EA844FE7C1B74"
					item: "mekanism:chemical_oxidizer"
					type: "item"
				}
				{
					icon: "mekanism:basic_chemical_tank"
					id: "29D6CA9A77ACACC6"
					title: "在机器中观察&a氧化铀&f"
					type: "checkmark"
				}
			]
			title: "&e&a氧化铀&f"
			x: 7.0d
			y: 3.5d
		}
		{
			dependencies: ["7E4A95B6443F23BC"]
			description: [
				"回顾一下,我们已使用多种气体和机械建立了高级矿石&a处理&f设施."
				""
				"本章将运用这些技术建造强大的多方块&a反应堆&r,以及先进的发电储能方式.最终可制作&d&a反物质靶丸&f&r,用于合成&e&aATM之星&f&r和本整合包中最强的工具武器."
			]
			icon: "mekanism:antiprotonic_nucleosynthesizer"
			id: "24174700F7FB771C"
			min_width: 300
			rewards: [{
				id: "73F5CE911AC4474C"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 2.0d
			subtitle: "&c需完成&d通用机械&f章节中的\"高级&d通用机械&f\"任务"
			tasks: [{
				id: "3153288DCE1C4FEF"
				title: "§a§d通用机械§f§r:§d高级"
				type: "checkmark"
			}]
			x: 1.5d
			y: -1.0d
		}
		{
			dependencies: ["1DE8B0C9A7195720"]
			description: [
				"&a裂变反应堆&f需要&3&a裂变燃料&f&r运行.若现在查看JEI中的&a裂变燃料&f配方,你可能会被复杂的制作流程吓到.别担心,我们一步步来."
				""
				"核心在于制备&e&a六氟化铀&f&r,重点需要合成两种气体:&b&a氢氟酸&f&r和&e&a氧化铀&f&r."
			]
			icon: "mekanism:reprocessed_fissile_fragment"
			id: "3B81800EE6E77EF2"
			rewards: [{
				id: "21A507B60A7721B0"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "382818E289EC83A6"
				title: "&3&a裂变燃料&f生产"
				type: "checkmark"
			}]
			x: 4.5d
			y: 2.5d
		}
		{
			dependencies: ["3B81800EE6E77EF2"]
			description: [
				"你应该已在四级矿石&a处理&f设施中制作过&2&a硫酸&f&r,这里再复习下制备方法."
				""
				"获取&e&a硫粉&f&r的方式:粉碎热力膨胀的硫磺,或在&a化学压射室&f中将&b&a氯化氢&f&r与&3火药&r混合."
				""
				"将&a硫粉&f通过&9&a化学氧化机&f&r制成&e&a二氧化硫&f&r,再于&a化学灌注器&f中与&b氧气&r结合生成&e&a三氧化硫&f&r."
				""
				"最后将&b&a水蒸气&f&r与&a三氧化硫&f反应即可得到&2&a硫酸&f&r."
			]
			icon: "gtceu:sulfur_dust"
			id: "31EEE2875595F315"
			progression_mode: "linear"
			rewards: [
				{
					id: "414A6E5BDC1A3B1E"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "20492956C6AD544F"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			subtitle: "快速回顾"
			tasks: [{
				id: "14A09EAF150777A3"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:dusts/sulfur"
					}
				}
				title: "&a硫粉&f"
				type: "item"
			}]
			title: "&2&a硫酸&f"
			x: 5.5d
			y: 1.5d
		}
		{
			dependencies: ["31EEE2875595F315"]
			description: [
				"在&9&a化学溶解室&f&r中将&2&a硫酸&f&r与&b萤石&r结合,制成&b&a氢氟酸&f&r."
				""
				"我们快成功了!"
			]
			id: "187A3F5B41D1C923"
			progression_mode: "linear"
			rewards: [
				{
					id: "5AEF7913046DC6FB"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "2CB5555573A7DE48"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [
				{
					id: "452FBF76102210BD"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:gems/fluorite"
						}
					}
					title: "任意#forge:萤石"
					type: "item"
				}
				{
					id: "4F5A28143EFF4944"
					item: "mekanism:chemical_dissolution_chamber"
					type: "item"
				}
			]
			title: "&b&a氢氟酸&f"
			x: 7.0d
			y: 1.5d
		}
		{
			dependencies: [
				"187A3F5B41D1C923"
				"3AF4E5D4839CEF8B"
			]
			description: ["使用另一个&9&a化学灌注器&f&r,将&b&a氢氟酸&f&r与&e&a氧化铀&f&r结合生成&2&a六氟化铀&f&r."]
			id: "3663E93E169EF8E3"
			progression_mode: "linear"
			rewards: [
				{
					id: "1AA24CF536807D83"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "34ED8F5C16ACBE63"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "73C5F4AC41EE5123"
				item: "mekanism:chemical_infuser"
				type: "item"
			}]
			title: "&2&a六氟化铀&f"
			x: 8.0d
			y: 2.5d
		}
		{
			dependencies: ["3663E93E169EF8E3"]
			description: [
				"拥有&9&a同位素离心机&f&r后,可将&2&a六氟化铀&f&r离心处理得到&3&a裂变燃料&f&r!"
				""
				"看,也没那么难对吧？"
			]
			icon: "mekanism:isotopic_centrifuge"
			id: "16A6C47E35F3B9D0"
			progression_mode: "linear"
			rewards: [
				{
					id: "04AA88C4D9B33E34"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "0F0F1DC9C68D607E"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "3F2371FD486D4F0E"
				item: "mekanism:isotopic_centrifuge"
				type: "item"
			}]
			title: "&3&a裂变燃料&f"
			x: 9.5d
			y: 2.5d
		}
		{
			dependencies: ["3FFF9018DA2A2763"]
			description: [
				"让我们再检查一遍清单 &one more time&r,确保在启动前一切准备就绪:"
				""
				"1. 穿戴好 &a防化服&f(安全第一)"
				"2. 确保冷却水/冷却剂正泵入输入端口"
				"3. &a裂变燃料&f 正泵入输入端口"
				"4. 设置输出端口将加热冷却剂导出至垃圾桶或 &a工业涡轮机&f"
				"5. 设置 &a核废料&f 输出端口连接至 &a放射性废料桶&f 或处理设备,或两者兼备!"
				""
				"准备就绪后,按下 &e激活&r 按钮!您还可以调整 &3燃烧速率&r 来产生更多 &a核废料&f,但建议从低速开始."
			]
			icon: "chipped:hazard_red_concrete"
			id: "3591EAA8E397F992"
			min_width: 300
			rewards: [{
				id: "5AC458A8C3D99B74"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			size: 1.5d
			tasks: [{
				id: "27709080B4B77B09"
				title: "准备就绪!"
				type: "checkmark"
			}]
			title: "启动 &a反应堆&f"
			x: 9.5d
			y: -1.5d
		}
		{
			dependencies: ["16A6C47E35F3B9D0"]
			description: [
				"当反应堆开始消耗 &a裂变燃料&f 时,会产生加热的 &b冷却剂&r 和 &8&a核废料&f&r."
				""
				"这时就会产生辐射.只要废料 &o安全存放在容器或设备中&r,就不会发生泄漏...对吧？"
				""
				"储存放射性物质的最佳方式是使用 &2&a放射性废料桶&f&r.它们能安全储存废料,同时让气体缓慢 &a衰变&f 而不会造成辐射泄漏.不要让 &a核废料&f 堆积在反应堆内,否则会导致过热,因此请设置废料输出端口并将其输送至废料桶!"
				""
				"&9重要提示&r:破坏任何装有放射性气体的机器、废料桶、管道或物品 &c都会导致辐射泄漏&r,包括 &a核废料&f 的产物(如钋或钚)."
			]
			id: "3FFF9018DA2A2763"
			min_width: 300
			rewards: [
				{
					id: "1EEAAB174D95E4B6"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "4278904667FC1D86"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				count: 3L
				id: "45EC7D8BA0CCFB07"
				item: "mekanism:radioactive_waste_barrel"
				type: "item"
			}]
			title: "处理 &8&a核废料&f"
			x: 9.5d
			y: 0.5d
		}
		{
			dependencies: ["3591EAA8E397F992"]
			description: [
				"可将 &8&a核废料&f&r 输入 &a同位素离心机&f 以生产 &9钚&r."
				""
				"将钚与水和 &7&a氟石粉&f&r 一起放入 &a加压反应室&f,即可制成 &9&a钚靶丸&f&r.这些是用于制造终局材料的!"
				""
				"注意:此过程还会产生副产品 &7乏 &a核废料&f&r,需要将其泵入废料桶储存."
			]
			id: "2E9FC7DC2AC6FD8E"
			rewards: [
				{
					id: "353657D187263DAA"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "5654C27CDA3AC9C5"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "6B40312EE7C8875D"
				item: "mekanism:pellet_plutonium"
				type: "item"
			}]
			x: 7.5d
			y: -1.5d
		}
		{
			dependencies: ["3591EAA8E397F992"]
			description: [
				"将 &a核废料&f 泵入 &9&a太阳能中子活化器&f&r 可获得 &d钋&r."
				""
				"将钋与 &a氟石粉&f 一起放入 &a加压反应室&f,即可制成 &9&a钋靶丸&f&r.未来您将需要大量此类靶丸."
				""
				"注意:此过程还会产生副产品 &7乏 &a核废料&f&r,需要将其泵入废料桶储存."
			]
			id: "1FAAF8216CDC3AC6"
			rewards: [
				{
					id: "316FC7C98CC22E11"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2D0F32B47DB74908"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "38A344B697C93B3B"
				item: "mekanism:pellet_polonium"
				type: "item"
			}]
			x: 8.5d
			y: -3.5d
		}
		{
			dependencies: [
				"2E9FC7DC2AC6FD8E"
				"1FAAF8216CDC3AC6"
			]
			description: [
				"&d&a超临界移相器&f&r(简称SPS)是另一种多方块结构,用于向钋注入大量能量以产生 &d反物质&r 气体,该气体可结晶为靶丸."
				""
				"建造SPS需要制作72个 &9&a超临界移相器外壳&f&r(60个用于建造,12个用于制作端口)、3个 &9&a超临界移相器端口&f&r、122个 &a结构玻璃&f 以及下个任务中将提到的另一物品.让我们先准备材料吧!"
			]
			id: "068728DE3B9B13C3"
			progression_mode: "linear"
			rewards: [
				{
					id: "34DCA3CB7F03E0E4"
					item: "mekanism:sps_port"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "4EF29B7A9C4A9A5A"
					table_id: 8364958827326577211L
					type: "loot"
				}
				{
					id: "1356A6FBD70699CA"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [
				{
					count: 60L
					id: "63C9D54A0F55BA9B"
					item: "mekanism:sps_casing"
					type: "item"
				}
				{
					count: 122L
					id: "6236D6215419A2EF"
					item: "mekanism:structural_glass"
					type: "item"
				}
				{
					count: 3L
					id: "769C396EE20CD89D"
					item: "mekanism:sps_port"
					type: "item"
				}
			]
			title: "&d&a超临界移相器&f"
			x: 6.0d
			y: -3.5d
		}
		{
			dependencies: ["1FAAF8216CDC3AC6"]
			description: [
				"你可能已经听到人们谈论&c&a湮灭反应堆&f&r是整合包中最好的能源.他们是对的."
				""
				"一旦激活,反应堆在正确的设置下可以输出高达200MRF/t的能量.它还可以用水冷却以产生蒸汽,这些蒸汽可以进入&9&a工业涡轮&f&r以产生更多的能量."
				""
				"要建造&a湮灭反应堆&f,我们需要遵循一个简单的模式.每一面看起来都像这样:"
				"{image:atm:textures/questpics/mek/fusion_pattern.png width:175 height:175 align:1}"
				""
				"顶部结构需要将中间方块替换为&a湮灭反应堆控制器&f."
				""
				"端口设置方面,可替换侧面任意&a反应堆玻璃&f.本配置需要两个输入口分别注入&c氘&r和&e氚&r,另设一个能量输出口."
				""
				"{image:atm:textures/questpics/mek/completed_fusion_reactor.png width:175 height:150 align:1}"
			]
			id: "0152C49AB74B9D32"
			min_width: 500
			progression_mode: "linear"
			rewards: [
				{
					id: "73F01F26D46493C6"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "5E28FD20ACEBC0F3"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "rsquare"
			size: 2.0d
			subtitle: "最佳&a移动能源&f&a打包&f方案"
			tasks: [
				{
					id: "4005EDF7A97FD97D"
					item: "mekanismgenerators:fusion_reactor_controller"
					type: "item"
				}
				{
					count: 36L
					id: "6E680D259F45AF80"
					item: "mekanismgenerators:fusion_reactor_frame"
					type: "item"
				}
				{
					count: 3L
					id: "2369D4585A3BDCCC"
					item: "mekanismgenerators:fusion_reactor_port"
					type: "item"
				}
				{
					count: 25L
					id: "554C67BA24832AB4"
					item: "mekanismgenerators:reactor_glass"
					type: "item"
				}
			]
			title: "The &c&a湮灭反应堆&f"
			x: 8.5d
			y: -7.0d
		}
		{
			dependencies: ["068728DE3B9B13C3"]
			description: [
				"&9超导线圈&r应安装在两个侧面的&a超临界移相器端口&f中心位置(如下图所示).通电后可将钋转化为反物质.基础配置需1个,也可安装2个提升效率."
				""
				"每生成1mb反物质需消耗400MRF.若尚未建造&c&a湮灭反应堆&f&r,现在正是最佳时机!"
				""
				"{image:atm:textures/questpics/mek/sps_coils.png width:200 height:125 align:1}"
			]
			id: "2331FCDD2F2B709A"
			min_width: 300
			rewards: [
				{
					id: "7BC5630B6210C074"
					item: "mekanism:supercharged_coil"
					type: "item"
				}
				{
					id: "3C4D31DF8C934DC9"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "1DFAC25097BB1E80"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "48A70F1D73DE5E37"
				item: "mekanism:supercharged_coil"
				type: "item"
			}]
			title: "&a增强&f线圈"
			x: 6.0d
			y: -5.5d
		}
		{
			dependencies: ["2331FCDD2F2B709A"]
			description: [
				"集齐所有材料后即可开始建造.下文为文字指引,也可通过Ponder动画查看建造过程."
				""
				"建成后的SPS结构呈7x7规格(非立方体),其建造模板如下图所示:"
				""
				"{image:atm:textures/questpics/mek/basic_sps_shape.png width:250 height:225 align:1}"
				""
				"每个侧面(含顶面/底面)均需遵循此模板:&a增强型&f线圈需对称安装在侧面中心位置,剩余两个端口分别用于输入钋和输出反物质气体."
				""
				"完整结构示意图如下:"
				""
				"{image:atm:textures/questpics/mek/sps_complete.png width:300 height:250 align:1}"
			]
			id: "2B3F2F470E06BC40"
			min_width: 350
			progression_mode: "linear"
			rewards: [
				{
					id: "7D3868B320D82382"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "41C9347CE3D3D8AE"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "3CD7CD472E76B554"
				item: "mekanism:sps_casing"
				type: "item"
			}]
			title: "建造SPS!"
			x: 6.0d
			y: -7.0d
		}
		{
			dependencies: ["2B3F2F470E06BC40"]
			description: [
				"在SPS中消耗数万亿能量后,我们终于可以制造 &d&a反物质靶丸&f&r."
				""
				"将SPS产生的反物质气体输入 &a化学结晶器&f,每生产1,000mb气体可获得1个 &a反物质靶丸&f."
			]
			id: "00EFC7B327E79076"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "18DC85F7E60E6427"
					table_id: 5196609362437981520L
					type: "loot"
				}
				{
					id: "377C6F58639E8469"
					type: "xp"
					xp: 1000
				}
			]
			shape: "gear"
			size: 3.0d
			tasks: [{
				id: "3B83170BC4E75158"
				item: "mekanism:pellet_antimatter"
				type: "item"
			}]
			x: 2.0d
			y: -7.0d
		}
		{
			dependencies: ["79757F66DF263FA0"]
			description: [
				"查看可视化演示:将光标悬停在&a激光聚焦矩阵&f上时按住&aW&r键启动Ponder功能."
				""
				"需将所有激光束对准&a激光增幅器&f.单个增幅器效率较低,建议建造多个."
				""
				"下图展示标准配置方案:可将激光器直接连接能量立方等电源,或通过管道/线缆供电.注意激光器与&a激光增幅器&f之间需保留1格空隙."
				""
				"&a激光增幅器&f表面有红色标记点,该面应对准&a激光聚焦矩阵&f."
				""
				"请确保关闭&a激光增幅器&f(或启用&a红石信号控制&f),待其储存&c至少400MRF&r能量后方可启动."
				""
				"{image:atm:textures/questpics/mek/laser_example.png width:200 height:150 align:1} "
			]
			id: "320CC038A64A3195"
			min_width: 400
			rewards: [
				{
					id: "1D08E38CECF7C7E8"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4F0CE5F24DACE759"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [
				{
					id: "1F82E1E037CBDC34"
					item: "mekanism:laser_amplifier"
					type: "item"
				}
				{
					count: 3L
					id: "64C66199BBFDF908"
					item: "mekanism:laser"
					type: "item"
				}
			]
			title: "启动方法:§d§a激光聚焦§f阵列"
			x: 14.5d
			y: -6.0d
		}
		{
			dependencies: ["12D271359D48210E"]
			description: ["此时您应该已经在生产 &a锂&r.将其泵入 &a太阳能中子活化器&f 即可制造 &e氚&r."]
			id: "57534FA0E09C4975"
			rewards: [
				{
					id: "464CDFABB1935067"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "5B4E2F6B1A4ABA2D"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "6C40D1088C211675"
				item: "mekanism:solar_neutron_activator"
				type: "item"
			}]
			title: "为 &a湮灭反应堆&f 供能:&e氚"
			x: 12.5d
			y: -7.5d
		}
		{
			dependencies: ["12D271359D48210E"]
			description: [
				"要为我们的&a湮灭反应堆&f供能,我们需要制造两种不同的气体,其中一种是&c氘气&r."
				""
				"制作氘气需要先制造一些电动泵并为其安装过滤升级.将它们放置在水源方块上方,接通电源后,它们会抽出&5&a重水&f&r."
				""
				"将&a重水&f泵入&a电解分离器&f即可获得&c氘气&r."
			]
			icon: "mekanism:electric_pump"
			id: "4ABF0727AA569DD9"
			rewards: [
				{
					id: "5A40C3DB56EDA0E3"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "24B785D6329E4565"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [
				{
					id: "34228001011C4524"
					item: "mekanism:electric_pump"
					type: "item"
				}
				{
					id: "1C627F3894C50E4E"
					item: "mekanism:upgrade_filter"
					type: "item"
				}
			]
			title: "为反应堆供能:§c氘气"
			x: 12.5d
			y: -8.5d
		}
		{
			dependencies: [
				"57534FA0E09C4975"
				"4ABF0727AA569DD9"
			]
			description: [
				"要启动&a湮灭反应堆&f,我们需要快速注入D-T燃料.这种燃料是通过在&a化学灌注器&f中将&c氘气&r与&e氚气&r混合制成的."
				""
				"首先制作一个&4霍尔容器&r,将其放入灌注器(标有加号的位置)并注入D-T燃料.现在反应堆就可以准备启动了!"
			]
			id: "3593D955361B0C6D"
			rewards: [
				{
					id: "2FBAF9E2B19C1A6F"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "29186BFA372DE715"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "789B4C8031BCDBDA"
				item: "mekanismgenerators:hohlraum"
				type: "item"
			}]
			title: "&a燃料&f注入器"
			x: 14.5d
			y: -8.0d
		}
		{
			dependencies: ["0152C49AB74B9D32"]
			description: [
				"要激活反应堆的反应,我们需要注入海量能量.&o真的是海量能量&r."
				""
				"这需要你搭建多个&9激光器&r并全部供能,然后将400MFE能量直接射入&a激光聚焦矩阵&f."
				""
				"&a激光聚焦矩阵&f需安装在&a湮灭反应堆&f某一面的中心位置.接下来我们将建造激光器."
			]
			icon: "mekanism:laser"
			id: "79757F66DF263FA0"
			min_width: 300
			rewards: [
				{
					id: "27476AD1A921D6BC"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "5B24AFD5AA909662"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "5799CEC4031D454E"
				item: "mekanismgenerators:laser_focus_matrix"
				type: "item"
			}]
			title: "超强&a激光束&f"
			x: 10.5d
			y: -6.0d
		}
		{
			dependencies: ["0152C49AB74B9D32"]
			description: [
				"&a湮灭反应堆&f需要特殊燃料:确切地说是&d&a氘氚燃料&f&r."
				""
				"有两种供能方式:以1,000mb/t的速率直接泵入&a氘氚燃料&f,或者分别控制两种燃料的泵入速率."
				""
				"作为入门,我们先采用分别泵入的方式.可能还需要了解它们的制作方法."
			]
			icon: "mekanismgenerators:fusion_fuel_bucket"
			id: "12D271359D48210E"
			rewards: [{
				id: "771198694F018CFC"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "2A6E713A062510B6"
				title: "为反应堆加注燃料"
				type: "checkmark"
			}]
			title: "为&d&a湮灭反应堆&f供能"
			x: 10.5d
			y: -8.0d
		}
		{
			dependencies: [
				"320CC038A64A3195"
				"3593D955361B0C6D"
			]
			description: [
				"已填充&a氘氚燃料&f的霍尔raum容器？&o完成!"
				""
				"氘与氚燃料已准备就绪可注入反应堆？&o完成!"
				""
				"&a激光增幅器&f已储备至少400MRF能量可轰击&a激光聚焦矩阵&f？&o完成!"
				""
				"准备就绪后:将&5霍尔raum&r放入&a&a湮灭反应堆控制器&f&r,注入燃料并激活&a激光增幅器&f."
				""
				"操作正确时&n反应堆将成功激活!"
				""
				""
				"{image:atm:textures/questpics/mek/fusion_activated.png width:225 height:150 align:1}"
			]
			icon: "mekanismgenerators:fusion_reactor_controller"
			id: "54D8B9CB3F98040F"
			min_width: 300
			progression_mode: "linear"
			rewards: [
				{
					id: "66CC79AB269C93AC"
					type: "xp"
					xp: 500
				}
				{
					exclude_from_claim_all: true
					id: "12724465924DCF00"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 3.0d
			tasks: [{
				id: "4B63979C49A34565"
				item: {
					Count: 1
					id: "mekanismgenerators:hohlraum"
					tag: {
						mekData: {
							GasTanks: [{
								Tank: 0b
								stored: {
									amount: 10L
									gasName: "mekanismgenerators:fusion_fuel"
								}
							}]
						}
					}
				}
				type: "item"
			}]
			title: "&d我想我们准备好了"
			x: 17.0d
			y: -7.0d
		}
		{
			dependencies: ["54D8B9CB3F98040F"]
			description: [
				"&d&a湮灭反应堆&f&r单机最大输出200MRF/t,但需先了解其运作机制."
				""
				"基础供能方案:分别注入氘/氚燃料后,通过&c燃料标签页&r中的&a&a注射速率&f&r控制燃烧效率."
				""
				"该数值必须为偶数且最大不超过98,因为它会与反应堆内部的D-T燃料混合使用.每种燃料的消耗量等于&a注射速率&f &a每刻&f的一半,即最大值时每种燃料消耗49mb/t."
				""
				"不过,你可以直接注入&d&a氘氚燃料&f&r,但无法控制&a注射速率&f.这将产生大量&a每刻&f能量,但燃料消耗率会大幅提升至每种燃料500mb/t."
				""
				"{image:atm:textures/questpics/mek/fusion_fuelui1.png width:175 height:150 align:1}"
			]
			id: "26D306418545A2D6"
			min_width: 300
			rewards: [
				{
					id: "3D24F967DBB74D71"
					item: "mekanism:ultimate_induction_cell"
					type: "item"
				}
				{
					id: "5FAD66DBACDEA6ED"
					type: "xp"
					xp: 1000
				}
				{
					exclude_from_claim_all: true
					id: "4EF01D10EB944CC6"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			tasks: [{
				id: "1EEEE30BA51757D6"
				max_input: 1000L
				type: "forge_energy"
				value: 50000000L
			}]
			title: "&d&a终极&f游戏&a移动能源&f"
			x: 20.0d
			y: -7.0d
		}
		{
			dependencies: ["3FFF9018DA2A2763"]
			description: [
				"&9&a工业涡轮&f&r是用于将&c加热冷却液&r转化为能量的巨型多方块结构.最小尺寸为5x5x5,最大尺寸可达17x17x18."
				""
				"建造涡轮机需要若干方块,让我们从基础材料开始准备."
				""
				"与大多数&d通用机械&f多方块结构相同,框架必须使用&e&a涡轮机外壳&f&r构成.但各面可以使用&b&a结构玻璃&f&r或机壳替代&a反应堆玻璃&f."
				""
				"我们将建造这台涡轮机,任务要求准备精确数量的所需材料."
				""
				"{image:atm:textures/questpics/mek/turbine_completed.png width:200 height:225 align:1}"
			]
			id: "6313B18820445882"
			min_width: 300
			rewards: [
				{
					id: "0963F21E8369E149"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "6427E149BC34AD20"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "gear"
			subtitle: "构建框架"
			tasks: [
				{
					count: 52L
					id: "0C8EF4390F02EBC4"
					item: "mekanismgenerators:turbine_casing"
					type: "item"
				}
				{
					count: 52L
					id: "3CD4768A3FCA49FF"
					item: "mekanism:structural_glass"
					type: "item"
				}
			]
			title: "&9&a工业涡轮&f"
			x: 12.5d
			y: 0.5d
		}
		{
			dependencies: ["6313B18820445882"]
			description: [
				"&9&a涡轮阀门&f&r用于泵入&b蒸汽&r,同时输出涡轮机产生的能量."
				""
				"&8&a涡轮排气口&f&r在使用&a&a饱和冷凝器&f&r时能排出多余水分.此外还能提升涡轮机内蒸汽的整体流速.排气口总数也会限制&a蒸汽处理&f速率.虽然排气口可安装在涡轮机顶面,但本次建造我们仅在外侧使用."
				""
				"&a&a饱和冷凝器&f&r用于将&b蒸汽&r转化回水,需安装在电磁线圈层&a或以上&f位置."
			]
			id: "233E438357CD89F6"
			min_width: 300
			rewards: [
				{
					id: "0A4A1734D68DC205"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "1FA4B60F847A1D52"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [
				{
					count: 2L
					id: "3E72FC25B884DFFC"
					item: "mekanismgenerators:turbine_valve"
					type: "item"
				}
				{
					count: 24L
					id: "4D32BE4920464ED0"
					item: "mekanismgenerators:turbine_vent"
					type: "item"
				}
				{
					count: 8L
					id: "2C56599158C13CDF"
					item: "mekanismgenerators:saturating_condenser"
					type: "item"
				}
			]
			title: "&a端口装置"
			x: 14.0d
			y: -0.5d
		}
		{
			dependencies: ["6313B18820445882"]
			description: [
				"&9&a涡轮转子&f&r安装在涡轮机中心.每个&a涡轮转子&f需要2片&a&a涡轮叶片&f&r.本次我们将使用3个转子."
				""
				"选中转子时&a右键点击&f&a&a涡轮叶片&f&r可直接安装.转子越高,叶片越长.本次建造共使用6片叶片.若要建造更大涡轮机,需根据叶片数量相应增加涡轮机宽度."
				""
				"&9&a复杂旋钮装置&f&r必须安装在&a涡轮转子&f顶部,周围需环绕&e&a分压元件&f&r."
				""
				"&a分压元件数量&f必须铺满&a复杂旋钮装置&f所在整个层面."
			]
			id: "05D20B506213A449"
			min_width: 300
			rewards: [
				{
					id: "5FDADDDF8428A238"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "5EF02DD923B9AECC"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [
				{
					count: 3L
					id: "69CE83F6561383E5"
					item: "mekanismgenerators:turbine_rotor"
					type: "item"
				}
				{
					id: "2902E36FD3AC8E68"
					item: "mekanismgenerators:rotational_complex"
					type: "item"
				}
				{
					count: 8L
					id: "1E9FDE2560C1AB7C"
					item: "mekanism:pressure_disperser"
					type: "item"
				}
				{
					count: 6L
					id: "526F17523CE9BDF5"
					item: "mekanismgenerators:turbine_blade"
					type: "item"
				}
			]
			title: "&a转子系统"
			x: 14.0d
			y: 0.5d
		}
		{
			dependencies: ["6313B18820445882"]
			description: [
				"&9电磁线圈&r直接安装在&a&a复杂旋钮装置&f&r上方,用于将动能转化为电能."
				""
				"最多可使用7个线圈(对应28片叶片的涡轮机).每个线圈必须与另一个线圈或&a复杂旋钮装置&f接触."
			]
			id: "7C61906C6C87C97D"
			rewards: [
				{
					id: "18AE58687D78BB0F"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "78B5258851C77EEF"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "4C0CC372A8597375"
				item: "mekanismgenerators:electromagnetic_coil"
				type: "item"
			}]
			x: 14.0d
			y: 1.5d
		}
		{
			dependencies: [
				"233E438357CD89F6"
				"7C61906C6C87C97D"
				"05D20B506213A449"
			]
			description: [
				"如果涡轮机建造正确,你会看到结构周围出现红色粒子效果.&a右键点击&f涡轮机可打开操作界面."
				""
				"界面会显示所有关键信息,包括总&a蒸汽处理速率&f以及涡轮机内部的蒸汽总量."
				""
				"右侧能量条显示涡轮机储存的电力.能量满格时涡轮机会自动停机,除非你设置了溢流排放."
				""
				"让我们启动它吧!"
				""
				"当你的&a裂变反应堆&f完全运作后,将&b蒸汽&r直接泵入涡轮机的&a涡轮阀门&f.由于本装置使用&a饱和冷凝器&f,你还可以通过&a涡轮排气口&f将水循环泵回反应堆."
			]
			id: "4189BC3DFB551F4C"
			min_width: 300
			progression_mode: "linear"
			rewards: [
				{
					id: "3B59F78B29ABD642"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "28236D14DE2BFD97"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "16269C92EF34FB14"
				item: "mekanismgenerators:turbine_casing"
				type: "item"
			}]
			title: "利用涡轮机发电!"
			x: 15.5d
			y: 0.5d
		}
		{
			dependencies: ["24174700F7FB771C"]
			description: [
				"要处理巨量电力,我们需要更强大的存储方案,普通能量立方体已无法满足需求."
				""
				"我们将建造可定制的多方块结构来存储海量电力,但首先需要制备一些&a锂粉&f!"
				""
				"你应该已经通过&a热力蒸馏塔&f生产了盐水.将&e盐水&r送入另一个&a热力蒸馏塔&f提取锂,再通过&9化学结晶器&f即可获得&a锂粉&f."
			]
			icon: "mekanism:upgrade_energy"
			id: "0FF852DE33E41C90"
			min_width: 250
			rewards: [
				{
					id: "6FBC38ABC17E2BFF"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4F4019E53A3BC0CF"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "31A1425AA09C33F8"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:dusts/lithium"
					}
				}
				title: "&a锂粉&f"
				type: "item"
			}]
			title: "高级&a能量存储&f"
			x: 3.0d
			y: -1.0d
		}
		{
			dependencies: ["0FF852DE33E41C90"]
			description: [
				"几乎所有&d通用机械&f多方块结构都采用相同建造方式."
				""
				"你需要构建长方体结构.边缘必须使用&8&a输导外壳&f&r,各面可使用机壳、&a&a结构玻璃&f&r或&c&a输导端口&f&r.建议配置2个端口:一个输入,一个输出.这些设置可通过&e配置器&r调整."
				""
				"本次我们将建造5x5x5结构.本任务要求准备精确数量的建造材料.需要帮助？&n查看深度解析!"
				""
				"{image:atm:textures/questpics/mek/induction_matrix.png width:200 height:200 align:1}"
			]
			hide_until_deps_visible: false
			id: "673FCBF8685D0EEE"
			min_width: 300
			rewards: [
				{
					id: "47EF9E0FC6A57445"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "7B24AA0C25D709FA"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "diamond"
			subtitle: "&a能量&f立方体之父"
			tasks: [
				{
					count: 44L
					id: "6D0EB8C587E34699"
					item: "mekanism:induction_casing"
					type: "item"
				}
				{
					count: 2L
					id: "6468D6A69F683E6D"
					item: "mekanism:induction_port"
					type: "item"
				}
				{
					count: 55L
					id: "5586FBB0561715AD"
					item: "mekanism:structural_glass"
					type: "item"
				}
			]
			title: "建造&9&a输导矩阵&f"
			x: 4.0d
			y: -1.5d
		}
		{
			dependencies: ["0FF852DE33E41C90"]
			description: [
				"&a输导矩阵&f允许你通过添加能量单元和供应器来定制多方块结构的能量存储与传输能力."
				""
				"感应单元可提升矩阵的总能量存储容量."
				""
				"感应供应器可提高矩阵的能量输入/输出传输速率."
				""
				"你可以自由配置内部组件数量,但至少需要各安装一个.更高阶的组件还能进一步提升存储和传输能力."
				""
				"{image:atm:textures/questpics/mek/induction_inside.png width:300 height:200 align:1}"
			]
			hide_until_deps_visible: false
			id: "14D772808D1BEAE2"
			min_width: 350
			rewards: [
				{
					id: "3CC428CF1A7974C7"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "72F41F8D5D6CCBB6"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "4063C1B5F935A026"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "mekanism:basic_induction_cell"
								}
								{
									Count: 1b
									id: "mekanism:advanced_induction_cell"
								}
								{
									Count: 1b
									id: "mekanism:elite_induction_cell"
								}
								{
									Count: 1b
									id: "mekanism:ultimate_induction_cell"
								}
							]
						}
					}
					title: "感应仓"
					type: "item"
				}
				{
					id: "2E3FF129C3668DD8"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "mekanism:basic_induction_provider"
								}
								{
									Count: 1b
									id: "mekanism:advanced_induction_provider"
								}
								{
									Count: 1b
									id: "mekanism:elite_induction_provider"
								}
								{
									Count: 1b
									id: "mekanism:ultimate_induction_provider"
								}
							]
						}
					}
					title: "感应供给器"
					type: "item"
				}
			]
			title: "&a自定义&r&9能量上限"
			x: 4.0d
			y: -0.5d
		}
		{
			dependencies: [
				"673FCBF8685D0EEE"
				"14D772808D1BEAE2"
			]
			description: [
				"当你建成首个&9充能&a输导矩阵&f时,结构周围会出现红色粒子效果表示建造完成."
				""
				"别忘了用更高级的能源单元和供应器升级!需要更多空间？&a输导矩阵&f的最大尺寸可达18x18x18."
			]
			icon: "mekanism:ultimate_induction_cell"
			id: "07ECC87DFF2D3991"
			progression_mode: "linear"
			rewards: [
				{
					id: "19F7348CFE7A14C9"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "75D4F410B2974D08"
					table_id: 8364958827326577211L
					type: "loot"
				}
			]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "271A066A74F1995C"
				item: "mekanism:induction_casing"
				type: "item"
			}]
			title: "&a完成&r&9能量矩阵"
			x: 5.0d
			y: -1.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若你看到本提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "67423D709305AD2D"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "43556EEA05422924"
					title: "全模组任务集"
					type: "checkmark"
				}
				{
					id: "314BB5005E524D0A"
					title: "全模组任务集"
					type: "checkmark"
				}
			]
			x: 1.5d
			y: -2.5d
		}
	]
	title: "§d通用机械§f:§d高级"
}
