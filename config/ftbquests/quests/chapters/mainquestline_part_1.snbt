{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "mainquestline_part_1"
	group: "2084F3F6FB861C5B"
	icon: "minecraft:crafting_table"
	id: "732824E03978A934"
	images: [
		{
			height: 1.0d
			image: "waystones:item/warp_stone"
			rotation: 0.0d
			width: 1.0d
			x: 0.97d
			y: 9.0d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/gettingstarted/titleimage1.png"
			order: -1
			rotation: 0.0d
			width: 13.671d
			x: 7.5d
			y: -3.0d
		}
		{
			height: 0.01d
			image: "ftbquests:textures/shapes/square/outline.png"
			rotation: 0.0d
			width: 15.4d
			x: 6.5d
			y: -1.5d
		}
		{
			height: 2.0d
			image: "integrateddynamics:aspect/read/list/inventory/itemstacks"
			rotation: 0.0d
			width: 2.0d
			x: -3.0d
			y: -2.5d
		}
		{
			height: 1.5d
			image: "farmersdelight:item/hamburger"
			rotation: 0.0d
			width: 1.5d
			x: -3.0d
			y: 9.0d
		}
		{
			height: 1.0d
			image: "tombstone:item/bag_of_seeds"
			order: -1
			rotation: 0.0d
			width: 1.0d
			x: -2.5d
			y: 9.23d
		}
		{
			height: 1.0d
			image: "tombstone:item/bag_of_seeds"
			order: -1
			rotation: 0.0d
			width: 1.0d
			x: -3.573d
			y: 9.23d
		}
		{
			height: 1.0d
			image: "mysticalagriculture:item/awakened_supremium_essence"
			order: -1
			rotation: 0.0d
			width: 1.0d
			x: -4.5d
			y: 6.0d
		}
		{
			height: 1.0d
			image: "sophisticatedbackpacks:item/advanced_magnet_upgrade"
			rotation: 0.0d
			width: 1.0d
			x: -4.5d
			y: 0.0d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/gettingstarted/sniffer3.png"
			rotation: 0.0d
			width: 4.0d
			x: 2.5d
			y: 12.5d
		}
		{
			height: 1.0d
			image: "minecraft:block/torchflower"
			rotation: 0.0d
			width: 1.0d
			x: 2.5d
			y: 14.5d
		}
		{
			height: 1.0d
			image: "minecraft:block/torchflower"
			rotation: 0.0d
			width: 1.0d
			x: 0.5d
			y: 13.0d
		}
		{
			height: 1.0d
			image: "minecraft:block/torchflower"
			rotation: 0.0d
			width: 1.0d
			x: 3.6845d
			y: 13.8523d
		}
		{
			height: 1.0d
			image: "ftbquests:tasks/input_only"
			order: -1
			rotation: 45.0d
			width: 1.0d
			x: 1.0d
			y: 9.0d
		}
		{
			height: 0.75d
			image: "apotheosis:items/gem_dust"
			rotation: 0.0d
			width: 0.75d
			x: -0.5d
			y: 7.0d
		}
		{
			height: 0.5d
			image: "irons_spellbooks:item/rotten_spell_book"
			rotation: 0.0d
			width: 0.5d
			x: 2.5d
			y: 7.0d
		}
		{
			height: 1.0d
			image: "irons_spellbooks:item/upgrade_orb_fire"
			order: -1
			rotation: 0.0d
			width: 1.0d
			x: 2.5d
			y: 7.0d
		}
		{
			height: 0.75d
			image: "ironfurnaces:block/iron_furnace_front_on_smoke"
			rotation: 0.0d
			width: 0.75d
			x: 5.5d
			y: 1.0d
		}
		{
			height: 0.5d
			image: "alltheores:item/iron_dust"
			rotation: 0.0d
			width: 0.5d
			x: 6.25d
			y: 2.0d
		}
		{
			height: 0.5d
			image: "alltheores:item/iron_dust"
			rotation: 0.0d
			width: 0.5d
			x: 4.75d
			y: 2.0d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/gettingstarted/wither2.png"
			rotation: 0.0d
			width: 5.0d
			x: 26.5d
			y: 7.5d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/gettingstarted/enderdragon.png"
			rotation: 0.0d
			width: 5.0d
			x: 21.5d
			y: 2.0d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/gettingstarted/warden_roar.png"
			rotation: 0.0d
			width: 5.0d
			x: 21.5d
			y: 12.5d
		}
		{
			height: 1.0d
			image: "minecraft:block/torchflower"
			rotation: 0.0d
			width: 1.0d
			x: 1.0d
			y: 14.0d
		}
		{
			alpha: 200
			height: 1.25d
			image: "allthemodium:block/fluid/atm_molten_still"
			rotation: 45.0d
			width: 1.25d
			x: 13.5d
			y: 9.0d
		}
		{
			alpha: 200
			height: 1.25d
			image: "allthemodium:block/fluid/atm_molten_still"
			rotation: 0.0d
			width: 1.25d
			x: 13.5d
			y: 9.0d
		}
		{
			height: 1.5d
			image: "chipped:block/deepslate/ctm/cut_deepslate_column_ctm/0"
			order: -1
			rotation: 0.0d
			width: 1.5d
			x: 13.5d
			y: 9.0d
		}
		{
			height: 1.5d
			image: "chipped:block/deepslate/ctm/cut_deepslate_column_ctm/0"
			order: -1
			rotation: 45.0d
			width: 1.5d
			x: 13.5d
			y: 9.0d
		}
		{
			height: 3.0d
			image: "gtceu:block/overlay/machine/overlay_pipe_out"
			rotation: 0.0d
			width: 3.0d
			x: 16.0d
			y: 1.5d
		}
		{
			height: 8.0d
			image: "atm:textures/questpics/gettingstarted/confused1.png"
			rotation: 0.0d
			width: 8.0d
			x: -9.0d
			y: 3.5d
		}
		{
			alpha: 200
			height: 1.5d
			image: "ars_nouveau:block/source_still"
			rotation: 0.0d
			width: 1.5d
			x: 8.0d
			y: 3.0d
		}
		{
			alpha: 200
			height: 1.5d
			image: "sophisticatedstorage:block/gold_barrel_bottom"
			rotation: 0.0d
			width: 1.5d
			x: 8.0d
			y: 3.0d
		}
		{
			height: 1.5d
			image: "naturesaura:block/blast_furnace_booster_top"
			rotation: 0.0d
			width: 1.5d
			x: 3.0d
			y: 3.0d
		}
	]
	order_index: 0
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"ATM9是款&a厨房水槽式&r整合包,意味着你可以自由探索并按自己喜欢的方式游玩!\\n\\n不过我们设置了终极目标:制作&e&aATM之星&f&r!\\n\\n无论你是Minecraft萌新还是ATM系列老玩家,这个任务线都将指引你完成所有流程,包括最终之星的合成.\\n\\n和绝大多数整合包一样,先从收集木材开始吧!"
				""
				""
			]
			id: "5CF320E9C4C1B1E1"
			rewards: [
				{
					count: 8
					id: "64451D3624C2085E"
					item: "minecraft:torch"
					type: "item"
				}
				{
					id: "1902B7E64709494A"
					type: "xp"
					xp: 10
				}
				{
					count: 8
					id: "5C8109AE8C1BAD29"
					item: "minecraft:cooked_beef"
					type: "item"
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				icon: "minecraft:oak_log"
				id: "285A53DB9D54595D"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "minecraft:logs"
					}
				}
				title: "收集木材"
				type: "item"
			}]
			title: "&e欢迎来到入门章节&r!"
			x: -3.0d
			y: 3.0d
		}
		{
			dependencies: ["5CF320E9C4C1B1E1"]
			description: ["前期游戏中最令人头疼的莫过于物品存储问题.\\n\\n本整合包提供了海量优化方案!想了解更多存储技巧？快去查看&a基础存储&r任务线!"]
			hide_dependency_lines: true
			id: "0752E680F9DE4039"
			rewards: [{
				id: "7D6D1B0CB60B90B8"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			size: 1.5d
			tasks: [{
				icon: "minecraft:chest"
				id: "1AFA45D4934626C6"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:chests/wooden"
					}
				}
				title: "箱子"
				type: "item"
			}]
			title: "&a存储系统"
			x: -3.0d
			y: 0.0d
		}
		{
			dependencies: ["5CF320E9C4C1B1E1"]
			description: ["冒险途中最重要的物资之一就是&2食物&r!\\n\\n新增了许多可建造农场的植物类型,快去寻找种子开始耕作吧!"]
			hide_dependency_lines: true
			id: "681E89DB12A21A09"
			rewards: [{
				id: "688CFEFDE7244290"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			size: 1.5d
			tasks: [{
				icon: "minecraft:apple"
				id: "3D4DC59D56174F6D"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:crops"
					}
				}
				title: "食物"
				type: "item"
			}]
			title: "&2食物与农业"
			x: -3.0d
			y: 6.0d
		}
		{
			dependencies: ["0752E680F9DE4039"]
			description: ["&a功能性存储&r模组新增了可容纳多组物品的&2抽屉&r,还能通过升级扩展容量!\\n\\n特别适合储存圆石、种子等大量获得的物品!"]
			icon: "functionalstorage:oak_1"
			id: "5EE945A1185E8336"
			optional: true
			rewards: [
				{
					id: "2F26494429A38E0C"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "3650502ECF1ACC45"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "636C3AC34EFAEF52"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3B1F1A7CAC8B7BC7"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "functionalstorage:drawer"
					}
				}
				title: "任意功能性存储抽屉"
				type: "item"
			}]
			title: "&a功能性存储"
			x: -2.0d
			y: 1.0d
		}
		{
			dependencies: ["0752E680F9DE4039"]
			description: ["&a&a精妙背包&f&r为游戏添加了最实用的背包系统!\\n\\n这些背包可通过升级提升品质,还能装配特定升级组件来增强整体功能!"]
			id: "00BD90363CA2D893"
			optional: true
			rewards: [
				{
					id: "2A88291C5526A88C"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "32F7C36CDCDE8984"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "7D1E29C1E57C456E"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "63C2682DA7129BE1"
				item: {
					Count: 1
					id: "sophisticatedbackpacks:backpack"
					tag: {
						inventorySlots: 27
						upgradeSlots: 1
					}
				}
				match_nbt: false
				type: "item"
			}]
			title: "&a&a精妙背包&f"
			x: -4.0d
			y: 1.0d
		}
		{
			dependencies: ["0752E680F9DE4039"]
			description: ["&2&d精妙存储&f&r新增了可升级的箱子、木桶和潜影盒,通过升级能获得更大容量,还能装配功能升级组件!\\n\\n注意:获得铁锭后,你可以直接将原版箱子升级为铁质存储箱,无需从基础款做起."]
			id: "7A0602AC493D8356"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7DCF0C367757E68E"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "48485C13A7991D52"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "69A5DCFFD9CE2C9A"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "sophisticatedstorage:basic_tier_upgrade"
							}
							{
								Count: 1b
								id: "sophisticatedstorage:basic_to_iron_tier_upgrade"
							}
						]
					}
				}
				title: "箱子升级"
				type: "item"
			}]
			title: "&2&d精妙存储&f"
			x: -4.0d
			y: -1.0d
		}
		{
			dependencies: ["681E89DB12A21A09"]
			description: ["&a&a植物盆&f&r 提供了一种自动种植作物和树苗的方式.只需放入一些泥土和种子或树苗,就能看着植物在1格空间内随时间生长!\\n\\n可升级为&e&a漏式植物盆&f&r实现作物自动化种植,它会自动将产物输出到下方的容器中."]
			id: "709344CCB273856F"
			rewards: [
				{
					id: "0FA5EFB87AE17336"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "7A19E9653246A939"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				icon: "botanypots:terracotta_botany_pot"
				id: "34E3533D501B867B"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "botanypots:all_botany_pots"
					}
				}
				title: "&a植物盆&f&r"
				type: "item"
			}]
			title: "室内农场"
			x: -2.0d
			y: 5.0d
		}
		{
			dependencies: ["5CF320E9C4C1B1E1"]
			description: ["虽然可以用物品栏的2x2合成格进行基础合成,但这远远不够.我们需要制作一个工作台来升级合成能力!"]
			id: "282CE3A088AE5CFB"
			rewards: [{
				id: "7A8E604C248E9E0B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "746C44D800F4E4FB"
				item: "minecraft:crafting_table"
				type: "item"
			}]
			title: "我的世界中的合成艺术"
			x: 0.0d
			y: 3.0d
		}
		{
			dependencies: ["0752E680F9DE4039"]
			description: ["&c储物罐&r是外出采矿时保持物品栏整洁的绝佳选择.\\n\\n基础储物罐可存放数组物品,并能设置为自动拾取特定物品.使用&ao&r键可切换拾取模式!"]
			id: "5F2FC6FAF9E8BF0E"
			optional: true
			rewards: [
				{
					id: "6605AE7CCB68D0C9"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "087D260DF4F1DC51"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "50A3B9089650D222"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "7FF8E7BFE4AB1B90"
				item: "dankstorage:dank_1"
				match_nbt: false
				type: "item"
			}]
			title: "&c储物罐"
			x: -2.0d
			y: -1.0d
		}
		{
			dependencies: ["282CE3A088AE5CFB"]
			description: ["无论你是模组老手还是MC萌新,人人都要制作&2木镐&r.\\n\\n镐子能挖掘大多数石头和金属方块.这把木镐不耐用,记得用它采集些&3圆石&r!"]
			id: "7975C7145572C438"
			rewards: [
				{
					id: "1551FD138B9B3813"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "48D4CB09DCF1E607"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "5CA7453233441165"
				item: {
					Count: 1
					id: "minecraft:wooden_pickaxe"
					tag: {
						Damage: 0
					}
				}
				match_nbt: false
				type: "item"
			}]
			title: "第一把镐"
			x: 1.0d
			y: 4.5d
		}
		{
			dependencies: ["282CE3A088AE5CFB"]
			description: ["个人很讨厌必须跑到方块旁才能合成,这时&2&a手持工作台&f&r就派上用场了!\\n\\n这个物品相当于&a&a便携式工作台&f&r!\\n\\n提示:可放入&a饰品栏&r并设置&b快捷键&r快速打开!"]
			id: "378BF828DC931F0C"
			optional: true
			rewards: [{
				id: "78B689AE8463620A"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			tasks: [{
				id: "4756A216A6E6860C"
				item: "crafting_on_a_stick:crafting_table"
				type: "item"
			}]
			title: "便携式合成"
			x: 0.0d
			y: 1.5d
		}
		{
			dependencies: ["7975C7145572C438"]
			description: ["有了木镐,开采石头不成问题!\\n\\n用&3圆石&r制作熔炉来冶炼矿石是下一步发展关键.\\n\\n还需将镐子升级为石镐来开采&b铁矿石&r等高级矿物!"]
			icon: "minecraft:furnace"
			id: "7D38BC3DB3406F51"
			rewards: [{
				id: "36A9FD7873103E63"
				type: "xp"
				xp: 10
			}]
			tasks: [
				{
					count: 16L
					icon: "minecraft:cobblestone"
					id: "10DFD79E9CE6DB61"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:cobblestone"
						}
					}
					title: "任意圆石"
					type: "item"
				}
				{
					id: "4701BCBA5EA024F2"
					item: "minecraft:furnace"
					type: "item"
				}
			]
			title: "石器时代"
			x: 3.0d
			y: 4.5d
		}
		{
			dependencies: ["051E0C85E7B71CE0"]
			description: ["获得新金属后,可用&a&d更多熔炉&f&r模组升级熔炉!\\n\\n该模组的熔炉(不仅是铁制版)可添加&a强化组件&r提升速度、改变功能等!\\n\\n通过界面左侧配置实现自动输入输出,让自动化更便捷."]
			id: "2AEBE3F28996A6ED"
			rewards: [{
				id: "668CE42B421B64D8"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "7C88F80D05E62AC7"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "ironfurnaces:copper_furnace"
							}
							{
								Count: 1b
								id: "ironfurnaces:iron_furnace"
							}
							{
								Count: 1b
								id: "ironfurnaces:upgrade_copper"
							}
							{
								Count: 1b
								id: "ironfurnaces:upgrade_iron"
							}
						]
					}
				}
				title: "熔炉升级"
				type: "item"
			}]
			title: "&a熔炉升级&r!"
			x: 5.5d
			y: 6.5d
		}
		{
			dependencies: ["7D38BC3DB3406F51"]
			description: ["煤炭虽是优质燃料但消耗极快.\\n\\n与其不断采矿,不如熔炼&2原木&r制成木炭!这种可再生资源效果相同.\\n\\n想更高效？将木炭分解成&3&a小块木炭&f&r,每块正好冶炼1个物品,绝不浪费!"]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:block/furnace_front_on"
				}
			}
			id: "18F88DE24EFBA7A7"
			rewards: [{
				id: "19BC35D05B4369D3"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "73F491DD08E2D06C"
					item: "minecraft:charcoal"
					type: "item"
				}
				{
					id: "01BFB9EB89580A42"
					item: "utilitix:tiny_charcoal"
					type: "item"
				}
			]
			title: "熔炉燃料"
			x: 3.0d
			y: 3.0d
		}
		{
			dependencies: ["7D38BC3DB3406F51"]
			description: ["我猜你一直在挖矿对吧？毕竟这是《我的世界》嘛.\\n\\n你会发现许多令人困惑的新矿石,但初期可以先用原版材料过渡!\\n\\n铜矿储量丰富,能制作&a矿石粉碎锤&r或&e抽屉升级件&r等实用物品,记得多采集些!\\n\\n铁矿石可能是每次遇到都必挖的重要资源,模组世界的运转几乎都离不开铁."]
			id: "051E0C85E7B71CE0"
			rewards: [
				{
					id: "3A97D5B2AE8AEF50"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "1332B608BF4AE9FB"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "octagon"
			size: 1.5d
			tasks: [
				{
					id: "38BA6B3FF1EC9185"
					item: "minecraft:iron_ingot"
					type: "item"
				}
				{
					id: "2E116AC5F25650BE"
					item: "minecraft:copper_ingot"
					type: "item"
				}
			]
			title: "&9金属&r时代"
			x: 5.5d
			y: 4.5d
		}
		{
			dependencies: ["051E0C85E7B71CE0"]
			description: ["进阶发展的下一步是制作&a铁镐&f.\\n\\n这把镐子能开采游戏中的稀有矿石,包括钻石!"]
			id: "698A959C9E449592"
			rewards: [
				{
					id: "77D1205EEAD47367"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "40D1A85DBA8BF5DC"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			tasks: [{
				id: "03005DEF00AB48F3"
				item: {
					Count: 1
					id: "minecraft:iron_pickaxe"
					tag: {
						Damage: 0
					}
				}
				match_nbt: false
				type: "item"
			}]
			title: "一把&a铁镐&r"
			x: 8.0d
			y: 4.5d
		}
		{
			dependencies: ["698A959C9E449592"]
			description: ["有了基础铁器后,你可以通过&e&d新生魔艺&f&r模组开启&d魔法&r新篇章.\\n\\n制作这本&2新手法术书&r就能释放入门法术,比如投掷物采矿或攻击生物.\\n\\n想深入了解该模组？快去完成&e&d新生魔艺&f&r的任务线吧!"]
			id: "40D9387C5AC664E0"
			rewards: [
				{
					id: "61A817F82F1C6DFD"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "761C021A73C2F48F"
					table_id: 7708276966210401484L
					type: "loot"
				}
			]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "6A1F976746D85288"
				item: "ars_nouveau:novice_spell_book"
				type: "item"
			}]
			title: "&a初探&r&d魔法&r"
			x: 8.0d
			y: 3.0d
		}
		{
			dependencies: ["698A959C9E449592"]
			description: ["小技巧:将原版工具放回工作台可制成&a&d寂静装备&f工具&r.\\n\\n这些工具可升级且耐久归零不会损坏,只需用&2&a修理工具包&f&r修复!\\n\\n想了解更多？查看&9&d寂静装备&f&r任务线!"]
			id: "680F2CB6905F08AD"
			rewards: [{
				id: "2C52336368ACAE77"
				type: "xp"
				xp: 10
			}]
			shape: "gear"
			size: 1.25d
			tasks: [{
				id: "629F4296DDD32604"
				item: {
					Count: 1
					id: "silentgear:pickaxe"
					tag: {
						Damage: 0
						SGear_Data: {
							Construction: {
								Parts: [
									{
										ID: "silentgear:pickaxe_head"
										Item: {
											Count: 1b
											id: "silentgear:pickaxe_head"
											tag: {
												Damage: 0
												Materials: [{
													ID: "silentgear:iron"
												}]
											}
										}
									}
									{
										ID: "silentgear:rod"
										Item: {
											Count: 1b
											id: "silentgear:rod"
											tag: {
												Materials: [{
													ID: "silentgear:wood"
												}]
											}
										}
									}
								]
							}
							Properties: {
								LockStats: 0b
								ModVersion: "3.5.0"
								Stats: {
									"silentgear:attack_reach": 3.0f
									"silentgear:attack_speed": -2.8f
									"silentgear:charging_value": 0.7f
									"silentgear:durability": 250.0f
									"silentgear:enchantment_value": 14.0f
									"silentgear:harvest_level": 2.0f
									"silentgear:harvest_speed": 6.0f
									"silentgear:magic_damage": 1.0f
									"silentgear:melee_damage": 3.0f
									"silentgear:rarity": 20.0f
									"silentgear:repair_efficiency": 1.0f
								}
								Traits: [
									{
										Level: 3b
										Name: "silentgear:malleable"
									}
									{
										Level: 1b
										Name: "silentgear:magnetic"
									}
									{
										Level: 2b
										Name: "silentgear:flexible"
									}
								]
							}
							Rendering: {
								Model: 3
								ModelKey: "pickaxe:pickaxe_head{iron},rod{wood},"
							}
						}
						SGear_UUID: [I;
							2053226576
							840254061
							-1565882294
							1308565543
						]
					}
				}
				match_nbt: false
				type: "item"
			}]
			title: "&a&d寂静装备&f工具"
			x: 8.0d
			y: 6.0d
		}
		{
			dependencies: ["698A959C9E449592"]
			description: ["拥有铁镐或更高级镐子后,就能开采&c红石&r.\\n\\n红石是开启科技模组的钥匙,能发电或制造精巧装置.\\n\\n这可是需求量极大的资源!"]
			hide_dependency_lines: false
			hide_until_deps_visible: false
			id: "4DE1158931F84F22"
			rewards: [
				{
					id: "2C46B4017AC5F3AE"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "18A505CF00AA26D6"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "octagon"
			tasks: [{
				count: 4L
				id: "73F9C130378195B9"
				item: "minecraft:redstone"
				type: "item"
			}]
			title: "&c红石"
			x: 10.5d
			y: 1.5d
		}
		{
			dependencies: ["698A959C9E449592"]
			description: ["钻石不仅是制作工具的最佳材料,还能开启&a下界&f等新维度!"]
			id: "76406EFFF8CBA6B4"
			rewards: [
				{
					id: "5631B3158B9642FB"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "197F4FAA40D360D9"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "17B9A643AFD7EB39"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:gems/diamond"
					}
				}
				title: "任意#forge:gems/diamond钻石"
				type: "item"
			}]
			title: "我们挖到&b钻石&r啦!"
			x: 10.5d
			y: 4.5d
		}
		{
			dependencies: ["76406EFFF8CBA6B4"]
			description: [
				"使用钻石级镐子即可开采&d黑曜石&r.黑曜石可用于建造通往&c下界&r的传送门.\\n\\n传送门框架可以有多种尺寸,但最常见的是基本'门型'结构——用至少10块黑曜石搭建的4x5空心框架.角落无需使用黑曜石,只需确保门框本身完整!"
				""
				"{image:atm:textures/questpics/gettingstarted/example_netherportal.png width:175 height:201 align:1}"
			]
			id: "0F3D51FFD2FE8DCB"
			min_width: 300
			rewards: [
				{
					id: "196D91EFBD3EBC2F"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "55D84560988395F7"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "square"
			size: 1.0d
			tasks: [
				{
					count: 10L
					id: "6206DC761D4CDDFE"
					item: { Count: 10, id: "minecraft:obsidian" }
					type: "item"
				}
				{
					advancement: "minecraft:story/enter_the_nether"
					criterion: ""
					id: "6FF63A559722919C"
					title: "进入&a下界&f"
					type: "advancement"
				}
			]
			title: "&c&a向下界进发&f&r!"
			x: 13.5d
			y: 4.5d
		}
		{
			dependencies: ["4DE1158931F84F22"]
			description: ["收集红石后就能开始发电啦!不同模组有不同能源类型,但多数使用RF或FE作为单位.\\n\\n初期发电设备有多种选择:\\n\\n- &a发电机增强件&r:将&a铁熔炉&f模组的熔炉改造为焚烧发电,JEI可查物品发电量\\n\\n- &aRFTool&r的&a燃煤发电机&f:直接燃烧煤炭等物品发电,可自动为相邻机器供能\\n\\n- &9Powah&r熔能炉:该模组有多种发电方案,熔能炉作为入门机型可燃烧木材或煤炭"]
			hide_dependent_lines: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "industrialforegoing:block/generators/generator_side_magmatic"
				}
			}
			id: "2D879A34A5788CCC"
			min_width: 400
			rewards: [
				{
					id: "2C9E98C328385885"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "5E36C0A090FD73E0"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "7C383FCA495D1CAB"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "ironfurnaces:augment_generator"
							}
							{
								Count: 1b
								id: "rftoolspower:coalgenerator"
							}
							{
								Count: 1b
								id: "powah:furnator_starter"
							}
						]
					}
				}
				title: "初始能源方案"
				type: "item"
			}]
			title: "&c初级能源"
			x: 16.0d
			y: 1.5d
		}
		{
			dependencies: ["051E0C85E7B71CE0"]
			description: ["初期熔炼原矿是必要的,但你会错过额外资源!\\n\\n有几种方法可以使每块原矿产出翻倍,其中最简单的方式是制作并使用&e矿锤&r.\\n\\n它能将1块原矿分解成2份矿粉,熔炼后获得锭,实现产量翻倍!\\n\\n想要更高效率？试试&5&d神秘时代&f&r模组!"]
			id: "4E5238F00CEED8B2"
			rewards: [
				{
					id: "5C1B39677C047DEA"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "70212285EF3ED4FB"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "square"
			size: 1.25d
			tasks: [{
				id: "1869893A4F8E9E9C"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "alltheores:ore_hammers"
					}
				}
				title: "矿石锤"
				type: "item"
			}]
			title: "&e基础 &a双倍矿物&f"
			x: 5.5d
			y: 2.0d
		}
		{
			dependencies: ["2D879A34A5788CCC"]
			description: ["想飞行又不想去&a末地&f找鞘翅？做个&a喷气背包&r吧!\\n\\n基础款是&2木质&r,可升级加速/扩容,最终能消除缓降效果.\\n\\n别忘了准备充电装置!"]
			id: "4EDD67D5C6823344"
			rewards: [
				{
					id: "58BD4F4CDF1B551D"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "042A4ECF7974E406"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "55FE958734110229"
				item: {
					Count: 1
					id: "ironjetpacks:jetpack"
					tag: {
						Id: "ironjetpacks:wood"
						Throttle: 1.0d
					}
				}
				match_nbt: false
				type: "item"
			}]
			title: "&a游戏初期&f 飞行"
			x: 17.0d
			y: 0.5d
		}
		{
			dependencies: ["4E9229FBA875C0BE"]
			description: ["除了有冷却的/home和/rtp指令外,许多模组都添加了传送方式!\\n\\n在村庄寻找&e路标石&r,可收集并搭建传送网络.\\n\\n小技巧:还能合成&d&a传送石&f&r随时传送到路标网络!"]
			id: "79494986A7957292"
			rewards: [
				{
					id: "1422464FC1F1F0DC"
					item: "waystones:waystone"
					type: "item"
				}
				{
					id: "2E9E9049F5B2CACE"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "4B2155D7B3E916DD"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				icon: "waystones:waystone"
				id: "7B33B2A7945A2C9D"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "waystones:waystone"
							}
							{
								Count: 1b
								id: "waystones:mossy_waystone"
							}
							{
								Count: 1b
								id: "waystones:sandy_waystone"
							}
						]
					}
				}
				title: "路标石"
				type: "item"
			}]
			title: "&d传送&a系统"
			x: 0.0d
			y: 8.0d
		}
		{
			dependencies: ["2D879A34A5788CCC"]
			description: ["导线传输距离有限,无线输电是终极方案.\\n\\n使用&9Powah&r模组的&a&a玩家充电器&f&r可随时充电.想深入了解？完成&9Powah&r任务线!\\n\\n搭建无线电网推荐&a&d通量网络&f&r模组,&e&a通量控制器&f&r支持跨维度充电,&a&a通量接入点&f&r+&a&a通量接出点&f&r实现全局供电."]
			id: "7D12B3ECC3E3AC7B"
			min_width: 350
			rewards: [
				{
					id: "5EAF138F285E39E3"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "48CD5113435D21D5"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "74AD72BE7CA6FEA0"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "fluxnetworks:flux_plug"
							}
							{
								Count: 1b
								id: "fluxnetworks:flux_point"
							}
							{
								Count: 1b
								id: "powah:player_transmitter_starter"
							}
							{
								Count: 1b
								id: "fluxnetworks:flux_controller"
							}
						]
					}
				}
				title: "无线能源方案"
				type: "item"
			}]
			title: "无线能源"
			x: 17.0d
			y: 2.5d
		}
		{
			dependencies: ["2D879A34A5788CCC"]
			description: ["多种储能方案可选!\\n\\n&9Powah&r的&a&a能量单元&f&r可升级扩容,或选择&a&dRF工具箱&f&r的&a能量核心&r多方块结构.\\n\\n注:只需制作其中任意一种即可完成任务."]
			id: "672134A1A620EEAB"
			rewards: [
				{
					id: "18EEB670636B9671"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "2061A5D3182DE1CE"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "3C95552D03C16F3D"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "rftoolspower:dimensionalcell_simple"
							}
							{
								Count: 1b
								id: "enderio:basic_capacitor_bank"
							}
							{
								Count: 1b
								id: "rftoolspower:cell1"
							}
							{
								Count: 1b
								id: "powah:energy_cell_starter"
							}
							{
								Count: 1b
								id: "mekanism:basic_energy_cube"
							}
						]
					}
				}
				title: "&a能量存储&f方案"
				type: "item"
			}]
			title: "&a能源存储&f"
			x: 15.0d
			y: 0.5d
		}
		{
			dependencies: ["681E89DB12A21A09"]
			description: ["以超强特性闻名的&2&d神秘农业&f&r能种植游戏内几乎所有资源.想种钻石？培育&a钻石种子&f吧!\\n\\n查看&2&d神秘农业&f&r任务线深入了解!"]
			id: "427E7112ED0978FB"
			rewards: [
				{
					id: "4C1E6FBDD27DF4EF"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "7E1C0978EB897124"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "26A2FFCD2BC34175"
				item: "mysticalagriculture:inferium_essence"
				type: "item"
			}]
			title: "&2&d神秘农业&f"
			x: -4.0d
			y: 5.0d
		}
		{
			dependencies: ["681E89DB12A21A09"]
			description: ["想知道个小秘密？\\n\\n把面包放进熔炉会变成&2烤面包&r,是前期绝佳食物!\\n\\n现在不是秘密啦."]
			id: "2114BABF547A0E2A"
			rewards: [
				{
					id: "6BC6FF38041DD59C"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "194D8E694665B35A"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "08D68241A798A476"
				item: "croptopia:toast"
				type: "item"
			}]
			title: "烤面包"
			x: -2.0d
			y: 7.0d
		}
		{
			dependencies: ["4E9229FBA875C0BE"]
			description: [
				"&a强化&f &d神化&f&r对Minecraft中大量物品和系统进行了全面革新,增强RPG体验.\\n\\n首先你会注意到部分物品具有&d稀有度&r,这会赋予其不同属性或特殊能力.它们可能还带有&b&a镶嵌槽&f&r,可通过&a锻造台&f嵌入旅途获得的&6宝石&r.\\n\\n更多关于&d神化&f的内容,请查阅&6&a阴影编年史&f&r指南书."
				""
				""
				"{image:atm:textures/questpics/gettingstarted/apoth_exampleitem.png width:170 height:86 align:1}"
			]
			id: "780DE5A24ED53F60"
			rewards: [
				{
					id: "27BF0B80ADD17159"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "apotheosis:apoth_chronicle"
						}
					}
					type: "item"
				}
				{
					id: "35C3A016DF3C9389"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "2B68EE62B54DDB37"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			subtitle: "&d神化&f"
			tasks: [{
				advancement: "apotheosis:affix/root"
				criterion: ""
				icon: {
					Count: 1
					id: "ftbquests:custom_icon"
					tag: {
						Icon: "apotheosis:items/mythic_material"
					}
				}
				id: "1FBEC3A36CAAA8EA"
				title: "找到&d神化&f&r附魔物品"
				type: "advancement"
			}]
			title: "找到&d神化&f&r附魔物品"
			x: 0.0d
			y: 6.0d
		}
		{
			dependencies: ["4E9229FBA875C0BE"]
			description: [
				"&d铁魔咒与法术书&r为Minecraft增添了经典RPG施法体系!\\n\\n你将迎战&3死灵法师&r等危险生物,收集全新资源,最重要的是:&6施展强力法术&r!\\n\\n初期需要通过战利品随机获取&b&a法术卷轴&f&r.这些一次性施法物品可放入&d法术书&r,通过&2&a抄写台&f&r转化为无限施放版本!\\n\\n当然,前提是你有足够的&9魔力值&r."
				""
				""
				""
				""
				"{image:atm:textures/questpics/gettingstarted/ironspells_example.png width:199 height:125 align:1}"
			]
			id: "58541EEDCB2C7CEE"
			min_width: 350
			rewards: [
				{
					id: "7C07DA5E6B18DF1F"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "4A14755142DBFA79"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5A21FAE40A7E734E"
				item: "irons_spellbooks:scroll"
				match_nbt: false
				title: "铁质&a施法卷轴&f"
				type: "item"
			}]
			title: "&d法术&r &a卷轴"
			x: 2.0d
			y: 6.0d
		}
		{
			dependencies: ["7975C7145572C438"]
			description: ["探索是&eATM&r整合包的核心!\\n\\n众多冒险模组带来新结构、生物、Boss,最重要的是:&c&l战&e&l利&a&l品&9&l!"]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:textures/entity_icon/horse/horse_chestnut.png"
				}
			}
			id: "4E9229FBA875C0BE"
			rewards: [{
				id: "7C5F26262FE9B032"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "03BCD8CA1FF84420"
				title: "冒险时刻!"
				type: "checkmark"
			}]
			title: "&9探索时间到!"
			x: 1.0d
			y: 7.0d
		}
		{
			dependencies: ["681E89DB12A21A09"]
			description: ["在原版游戏中,村民系统常常令人头疼.交易轮换机制不易操作,职业系统有时也难以掌控.\\n\\n&a&a简易村民&f&r模组让这一切变得简单!首先,你可以通过潜行+&a右键点击&f来拾取村民.随后可以轻松地将他们放置在世界中,或是放入本模组特有的容器里!\\n\\n在JEI中搜索&d@EasyVillagers&r即可查看模组提供的所有方块!"]
			id: "28E60192912BEBAD"
			rewards: [
				{
					id: "6502F8AB0033CC29"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "7CCFFBC576CC4D3E"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "7F265D92FDA70DFA"
				item: "easy_villagers:villager"
				match_nbt: false
				type: "item"
			}]
			title: "&a&a简易村民&f"
			x: -3.0d
			y: 7.5d
		}
		{
			dependencies: ["2D879A34A5788CCC"]
			description: ["将发电机产生的能量导出是个好主意,但具体该如何操作？\\n\\n你可以使用来自Pipez&r模组的&c&a能量管道&f&r,若已安装&9Powah&r模组,也可使用其提供的&c&a能量管道&f&r.\\n\\n本整合包还保留了经典模组&e&d末影接口&f&r,可以使用其中的&e能量导管&r."]
			id: "4869C413646CC4CC"
			rewards: [
				{
					id: "583B550C6F1EDACC"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "140701E3E5D975AF"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "1960B985B54B9FBD"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "enderio:energy_conduit"
							}
							{
								Count: 1b
								id: "pipez:energy_pipe"
							}
							{
								Count: 1b
								id: "powah:energy_cable_starter"
							}
							{
								Count: 1b
								id: "mekanism:basic_universal_cable"
							}
						]
					}
				}
				title: "基础能源线缆"
				type: "item"
			}]
			title: "能量管道"
			x: 15.0d
			y: 2.5d
		}
		{
			dependencies: ["0F3D51FFD2FE8DCB"]
			description: ["当收集足够的&a末影珍珠&f和&a烈焰粉&f后,就可以前往主世界的&a末地传送门&f.\\n\\n合成若干&d末影之眼&r后&a右键点击&f抛向空中,它们会指引最近的&a末地传送门&f方位.\\n\\n找到要塞中的&a末地传送门&f框架,用末影之眼激活即可开启通往&d&a末地&f&r的传送门."]
			hide_until_deps_visible: false
			icon: "minecraft:end_portal_frame"
			id: "61C49BC2B384FB80"
			rewards: [{
				exclude_from_claim_all: true
				id: "3E92832FCABBF64B"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			shape: "hexagon"
			size: 1.25d
			tasks: [
				{
					count: 10L
					id: "2BF525CB7074AF2A"
					item: "minecraft:ender_eye"
					type: "item"
				}
				{
					advancement: "minecraft:story/follow_ender_eye"
					criterion: ""
					icon: "minecraft:end_portal_frame"
					id: "2014E320BFF51D62"
					title: "&d寻找&a要塞&r"
					type: "advancement"
				}
			]
			title: "&a寻找&r &d末地&f"
			x: 16.0d
			y: 4.5d
		}
		{
			dependencies: ["61C49BC2B384FB80"]
			description: ["在&d&a末地&f&r中,你将面对原版 Minecraft 的终极Boss:&5&a末影龙&f&r.\\n\\n每根黑曜石柱顶端都有为巨龙治疗的&a末影水晶&f,务必优先破坏!注意保持距离,它们会造成巨额伤害.\\n\\n击败末影龙后,出口传送门上方会出现&d&a龙蛋&f&r.它无法像普通方块那样采集,但会受到重力影响."]
			hide_until_deps_visible: false
			icon: "minecraft:dragon_head"
			id: "6EE5BE5693E8ACE4"
			rewards: [
				{
					id: "56A277BC5DE71CA9"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "08DE61A4378573A1"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "gear"
			size: 2.0d
			tasks: [
				{
					advancement: "minecraft:story/enter_the_end"
					criterion: ""
					id: "4E47E0E49057195E"
					title: "&d前往&a终末之地&f&r"
					type: "advancement"
				}
				{
					advancement: "minecraft:end/root"
					criterion: ""
					icon: "minecraft:dragon_head"
					id: "2F96969419E7C767"
					title: "&5末地&f&r"
					type: "advancement"
				}
				{
					id: "3EC9DF0765694A7E"
					item: "minecraft:dragon_egg"
					type: "item"
				}
			]
			title: "&5末地&f"
			x: 19.0d
			y: 4.5d
		}
		{
			dependencies: ["76406EFFF8CBA6B4"]
			description: ["本任务只需制作一件钻石工具或防具,但凑齐整套装备会更好!\\n\\n钻石工具耐久出众,防具能提供全面保护.\\n\\n游戏中更高级的装备都需要以钻石装备作为基础材料!"]
			id: "2EC9668ED4EA47CB"
			rewards: [
				{
					id: "22A34BE659C7BC6D"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "7B5B59A44117F6E4"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "3209E46C9B05C163"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "minecraft:diamond_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_axe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_shovel"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_helmet"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_chestplate"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_leggings"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:diamond_boots"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&b钻石&r &a装备&r"
				type: "item"
			}]
			title: "&9&a全副武装&f"
			x: 10.5d
			y: 6.0d
		}
		{
			dependencies: ["0F3D51FFD2FE8DCB"]
			description: ["1.20版本更新了下界合金装备的合成方式.需要在堡垒遗迹的宝箱中寻找&e下界合金 &a锻造模板&f&r.\\n\\n将其与&c下界合金锭&r及钻石装备放入&a&a锻造台&f&r即可升级为&c下界合金&r装备.\\n\\n提示:别一次性用完所有模板!可通过合成配方复制更多模板."]
			id: "1E1EB7DC19DDCFB7"
			rewards: [
				{
					id: "59B9C8F5AB1E300A"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4338BE89D5F22F9B"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "363806ECF99CBE88"
				item: "minecraft:netherite_upgrade_smithing_template"
				type: "item"
			}]
			title: "&e下界合金模板"
			x: 12.0d
			y: 6.0d
		}
		{
			dependencies: [
				"2EC9668ED4EA47CB"
				"1E1EB7DC19DDCFB7"
				"5E799B92358A8732"
			]
			description: ["&d下界合金&r装备性能全面超越钻石装备,且不会被熔岩烧毁!(但穿戴者仍会受伤,请勿尝试)\\n\\n这些材料能打造游戏中最顶级的工具和防具."]
			id: "0F026D5A17CCCF51"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "29C76CDC2D4EE385"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "45F5DBB95E35764C"
					type: "xp"
					xp: 50
				}
			]
			shape: "square"
			tasks: [{
				id: "0AFA64F1025569A2"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "minecraft:netherite_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_shovel"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_axe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_helmet"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_chestplate"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_leggings"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "minecraft:netherite_boots"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d下界合金&r &a装备&r"
				type: "item"
			}]
			title: "&d下界合金&r &a装备&r"
			x: 10.5d
			y: 7.5d
		}
		{
			dependencies: ["051E0C85E7B71CE0"]
			description: ["&2考古学&r是MC v1.20的全新功能,玩家可以在&a可疑的&r方块(如沙子和沙砾)中发现物品.\\n\\n这些方块可以在各种结构中找到,如温暖的&a海底废墟&f、&a沙漠神殿&f、沙漠水井以及全新的&e古迹废墟&r.\\n\\n要从可疑方块中发掘物品,你需要制作一把&a刷子&r来刷掉方块以揭示物品."]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "3ADCAD65EA6900BA"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1E0E59B27422F16E"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "122D5ED8BE887D6A"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "1993EF1788F53759"
				item: {
					Count: 1
					id: "minecraft:brush"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&2考古学&r!"
			x: 6.0d
			y: 10.5d
		}
		{
			dependencies: ["0F3D51FFD2FE8DCB"]
			description: ["在&c下界&r中,你会遇到&e&a远古残骸&f&r.它可以熔炼成碎片,与金锭结合制成&e&a下界合金锭&f&r,这是用于制作游戏中最强工具和盔甲的终局金属."]
			id: "5E799B92358A8732"
			rewards: [{
				exclude_from_claim_all: true
				id: "3AA95C89CCD4545E"
				table_id: 4196188979167302596L
				type: "loot"
			}]
			shape: "square"
			tasks: [
				{
					id: "43762D6B09E48291"
					item: "minecraft:ancient_debris"
					type: "item"
				}
				{
					id: "4346DAC18183627E"
					item: "minecraft:netherite_ingot"
					type: "item"
				}
			]
			title: "&d&a古代金属&f"
			x: 13.5d
			y: 7.5d
		}
		{
			dependencies: ["5CF320E9C4C1B1E1"]
			description: ["这个生物群系是在荒野更新中引入的!\\n\\n小心!你可能会意外召唤出一个新朋友.\\n\\n附注:它并不友好."]
			hide_dependency_lines: true
			id: "6EDAB29FBD3C60A3"
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				biome: "minecraft:deep_dark"
				icon: {
					Count: 1
					id: "ftbquests:custom_icon"
					tag: {
						Icon: "minecraft:block/sculk_shrieker_can_summon_inner_top"
					}
				}
				id: "1D48B6FC70F5C745"
				title: "&6造访&r &d深暗之域&f&r!"
				type: "biome"
			}]
			title: "&e探访&r &d&a深暗之域&f&r!"
			x: 16.0d
			y: 10.5d
		}
		{
			dependencies: [
				"0F026D5A17CCCF51"
				"78DDD2CA8E7E2507"
				"1FD4C32B3937E1C7"
			]
			description: ["&e全金属&r装备将开启你的OP之旅!\\n\\n首先,所有工具和盔甲都是&d不可破坏的&r,意味着无需担心它们会损坏或需要修复!\\n\\n这些工具速度极快,并且基础伤害很高."]
			id: "4E178CCAC7F85F54"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5FA13C02E33371F1"
					table_id: 7025454341029952768L
					type: "loot"
				}
				{
					id: "69F8DF0EE9D6D751"
					type: "xp"
					xp: 250
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "0CE818D1D451E7C4"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:allthemodium_boots"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_leggings"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_chestplate"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_helmet"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_sword"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_pickaxe"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_axe"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_shovel"
							}
						]
					}
				}
				title: "&6全模组工具与盔甲&r"
				type: "item"
			}]
			title: "&e全金属&r &d工具和盔甲"
			x: 10.5d
			y: 9.0d
		}
		{
			dependencies: ["3ADCAD65EA6900BA"]
			description: ["要制作&e全金属&r工具和盔甲,你需要找到&e全金属 &a锻造模板&f&r.\\n\\n可以在&d远古城市&r中刷&a可疑的黏土&r来找到它."]
			id: "78DDD2CA8E7E2507"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5E97ECDECA567F5A"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "004934D0BB0CA78C"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "19266B8811F0A712"
				item: "allthemodium:allthemodium_upgrade_smithing_template"
				type: "item"
			}]
			title: "&e全金属 &a锻造模板&f"
			x: 8.0d
			y: 9.0d
		}
		{
			dependencies: ["3ADCAD65EA6900BA"]
			description: ["&a&a锻造模板&f&r是全新物品,用于为盔甲添加装饰!这可以在&a锻造台&f中完成.\\n\\n虽然它们有合成配方,但大多数装饰模板是从战利品箱或考古挖掘中获得的稀有物品.一旦找到一个,你可以使用配方复制模板来制作更多!"]
			id: "7741905EA8380B25"
			rewards: [
				{
					id: "78458A2A58EEEAAF"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2B9B88680EA3D6F4"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "gear"
			tasks: [{
				id: "1693B07767081FB3"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "minecraft:trim_templates"
					}
				}
				title: "盔甲纹饰模板"
				type: "item"
			}]
			title: "&d&a护甲夹板&f"
			x: 4.5d
			y: 10.5d
		}
		{
			dependencies: ["3ADCAD65EA6900BA"]
			description: ["要找到这个蛋,你需要在温暖的&a海底废墟&f中刷掉&a可疑的沙子&r.\\n\\n你可以将蛋放置在世界中,它会慢慢孵化成一只&d小嗅探兽&r.通常需要约20分钟孵化,但放在苔藓块上可以加倍孵化速度,只需10分钟.\\n\\n小嗅探兽最终会长成成年&d嗅探兽&r,当它们在可挖掘的方块上徘徊时,可以嗅出物品如&2火把花种子&r或&2瓶子草荚果&r.\\n\\n你可以用火把花繁殖两只成年嗅探兽."]
			id: "72989212DD45DC10"
			rewards: [
				{
					id: "2071EEC4C3305EC6"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "4A8D767945327347"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "heart"
			subtitle: "Scruff McGruff的远亲"
			tasks: [{
				id: "070A5475DB3F6E6C"
				item: "minecraft:sniffer_egg"
				type: "item"
			}]
			title: "&a&r &5嗅探兽"
			x: 6.0d
			y: 12.0d
		}
		{
			dependencies: ["6EDAB29FBD3C60A3"]
			description: ["&d监守者&r可以通过触发靠近&3&a幽匿尖啸体&f&r的&5&a潜声传感器&f&r至少&a3次&f来召唤.\\n\\n有趣的事实:&a看守兽&f是盲的!它只能通过听觉找到你,所以要么飞行要么潜行!\\n\\n提示:无论你使用远程还是近战攻击都无所谓,因为它会找到你.而且它会攻击你.如果你跑得太远,它会钻入地下并在你旁边出现!祝你好运 :D"]
			hide_until_deps_visible: false
			id: "6D09511D64DDC282"
			rewards: [
				{
					id: "7949A3E032C67EB5"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "31A8DCBFC72CA085"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "gear"
			size: 2.0d
			tasks: [{
				entity: "minecraft:warden"
				icon: {
					Count: 1
					id: "ftbquests:custom_icon"
					tag: {
						Icon: "ftbchunks:textures/faces/minecraft/warden.png"
					}
				}
				id: "6CAD0E4CF3577BEB"
				title: "&5击杀&a监守者&f&r"
				type: "kill"
				value: 1L
			}]
			title: "&5击杀 &a看守兽&f"
			x: 19.0d
			y: 10.5d
		}
		{
			dependencies: [
				"6EDAB29FBD3C60A3"
				"5E799B92358A8732"
			]
			description: ["&e全钶矿&r是ATM模组中最坚硬的金属之一,但其矿石在主世界极为罕见.\\n\\n你只能在&d&a深暗之域&f&r生物群的墙壁和天花板上找到它.开采这种矿石需要下界合金镐或更高级的工具.\\n\\n提示:在&a挖矿维度&f中,这种矿石会常见得多."]
			id: "1FD4C32B3937E1C7"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2CD0D7A5833ACDD9"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "01F45EB4D3DB2A93"
					type: "xp"
					xp: 100
				}
			]
			shape: "octagon"
			size: 1.5d
			tasks: [{
				id: "4F5479637B945F88"
				item: "allthemodium:raw_allthemodium"
				type: "item"
			}]
			title: "&aATM矿石&f"
			x: 13.5d
			y: 9.0d
		}
		{
			dependencies: ["4A4C71C43519D5FE"]
			description: ["&5凋灵&r是《我的世界》中最难对付的Boss之一.主要因为它会飞行、会爆炸、移动速度还快——你应该明白有多棘手了.\\n\\n召唤方式:用4个&a灵魂沙&f摆成T字形,在顶部放置3个&a凋灵骷髅头颅&f即可召唤&5凋灵&r.\\n\\n警告:它会频繁爆炸.千万别在基地附近召唤!"]
			hide_dependency_lines: false
			hide_until_deps_visible: false
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:textures/entity_icon/wither/wither_invulnerable.png"
				}
			}
			id: "69A517AB8A801939"
			rewards: [
				{
					id: "1C1320B4266DCB86"
					type: "xp"
					xp: 250
				}
				{
					exclude_from_claim_all: true
					id: "06D66F00886663C4"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "gear"
			size: 2.0d
			tasks: [{
				entity: "minecraft:wither"
				icon: {
					Count: 1
					id: "ftbquests:custom_icon"
					tag: {
						Icon: "minecraft:textures/entity_icon/wither/wither_invulnerable.png"
					}
				}
				id: "76C41881A02F2098"
				title: "&6击杀&r &5凋灵&r"
				type: "kill"
				value: 1L
			}]
			title: "&e击杀&r &5凋灵"
			x: 19.0d
			y: 7.5d
		}
		{
			dependencies: ["2AEBE3F28996A6ED"]
			description: ["&a强化组件&r是可以改变或提升熔炉功能的升级模块.\\n\\n- 高炉模式:将熔炉转换为仅能使用高炉配方\\n\\n- 烟熏模式:将熔炉转换为仅能使用烟熏配方\\n\\n- 工厂模式:将熔炉改为使用能源驱动(而非燃料),升级为可同时熔炼多个物品的工厂.具体数量取决于熔炉等级.\\n \\n - 速度强化:熔炼速度翻倍,但燃料消耗也翻倍\\n\\n- &a燃料效率&f:每单位燃料可熔炼物品数量翻倍,但速度降低25%%."]
			id: "7EC01E7DB045DB05"
			min_width: 350
			optional: true
			rewards: [{
				id: "535A39E4C6E7F4AD"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			tasks: [{
				id: "3BEA6C6A41D5ABEA"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "ironfurnaces:augment_blasting"
							}
							{
								Count: 1b
								id: "ironfurnaces:augment_smoking"
							}
							{
								Count: 1b
								id: "ironfurnaces:augment_factory"
							}
							{
								Count: 1b
								id: "ironfurnaces:augment_speed"
							}
							{
								Count: 1b
								id: "ironfurnaces:augment_fuel"
							}
						]
					}
				}
				title: "熔炉&a强化装置&r"
				type: "item"
			}]
			title: "熔炉&a强化组件"
			x: 5.5d
			y: 8.0d
		}
		{
			dependencies: ["681E89DB12A21A09"]
			description: ["如果觉得用种子种钻石不够过瘾,&e&d资源蜜蜂&f&r能为你提供各种资源采集的新途径.\\n\\n外出冒险时可能会遇到它们!想要体验这个模组,记得完成&eProductive Bee&r的任务线!"]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "productivebees:textures/item/all_bees.png"
				}
			}
			id: "086A3E80E57D46BE"
			rewards: [
				{
					id: "48D2664438A581AB"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "206DD38C65ABF884"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			size: 1.0d
			tasks: [
				{
					id: "60FF61DC3AA3A12E"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "minecraft:honeycomb"
								}
								{
									Count: 1b
									id: "minecraft:honey_bottle"
								}
							]
						}
					}
					title: "蜂蜜"
					type: "item"
				}
				{
					id: "2DE41EE12F6432B5"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:hives"
						}
					}
					title: "蜂箱"
					type: "item"
				}
			]
			title: "&e&d资源蜜蜂&f"
			x: -4.0d
			y: 7.0d
		}
		{
			dependencies: ["2D879A34A5788CCC"]
			description: ["钻石还能通过模组&a&d建筑小帮手&f&r制作酷炫的建筑工具.\\n\\n无论是扩建基地还是用&a破坏小帮手&f清理空间,这些工具都超级实用."]
			id: "7A514E27E1A7FE32"
			optional: true
			rewards: [
				{
					id: "71D231672C274C17"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "6D44B1DE7B009082"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "47A1F59A538E3C4B"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "buildinggadgets2:gadget_building"
							}
							{
								Count: 1b
								id: "buildinggadgets2:gadget_exchanging"
							}
							{
								Count: 1b
								id: "buildinggadgets2:gadget_copy_paste"
								tag: { }
							}
							{
								Count: 1b
								id: "buildinggadgets2:gadget_cut_paste"
								tag: {
									pastereplace: 1b
								}
							}
							{
								Count: 1b
								id: "buildinggadgets2:gadget_destruction"
							}
						]
					}
				}
				match_nbt: false
				title: "&d建筑小帮手&f"
				type: "item"
			}]
			title: "&a&d建筑小帮手&f"
			x: 16.0d
			y: 0.0d
		}
		{
			dependencies: ["0F3D51FFD2FE8DCB"]
			description: ["要召唤&a下界&f的终极Boss,我们需要先收集些材料.\\n\\n至少需要3个&5&a凋灵骷髅头颅&f&r.可通过击杀&7&a凋灵骷髅&f&r低概率掉落,或用更常见的掉落物&3&a凋灵骷髅头颅碎片&f&r合成.\\n\\n另外还需要4块&a灵魂沙&f,当然还得准备套像样的盔甲."]
			hide_until_deps_visible: false
			icon: "minecraft:wither_skeleton_skull"
			id: "4A4C71C43519D5FE"
			shape: "hexagon"
			size: 1.25d
			tasks: [
				{
					count: 3L
					id: "178C4C3288503A66"
					item: "minecraft:wither_skeleton_skull"
					type: "item"
				}
				{
					count: 4L
					id: "14C0350713AC4214"
					item: "minecraft:soul_sand"
					type: "item"
				}
			]
			title: "&5凋灵骷髅头颅"
			x: 16.0d
			y: 7.5d
		}
		{
			dependencies: ["4E9229FBA875C0BE"]
			description: ["战利品总是令人兴奋,而&dLootr箱子&r让这个过程更加完美.\\n\\n这些箱子采用玩家独立掉落机制,你和好友可以各自获取战利品而不会互相影响!\\n\\n还有机会开出名为&e&d奇异饰品&f&r的稀有物品,可放置在&bCurios&r饰品栏位获得特殊效果!\\n\\n技巧:潜行状态下挖掘可直接带走Lootr箱子!"]
			icon: "lootr:lootr_chest"
			id: "5B95C5B5B3A9CB2E"
			rewards: [
				{
					id: "29ECF9DB6CC61534"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "2AC7559ACB3E63E0"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "7DD48BFCA238430A"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "artifacts:artifacts"
					}
				}
				title: "&d奇异饰品&f"
				type: "item"
			}]
			title: "&d&a战利品箱子&f"
			x: 2.0d
			y: 8.0d
		}
		{
			dependencies: ["76406EFFF8CBA6B4"]
			description: [
				"既然已获得&b钻石&r,现在可以建造通往&d暮色森林&f&r维度的传送门!\\n\\n建造2x2的水池,边缘用鲜花围住.完成后投入钻石即可激活传送门!\\n\\n该模组详情请参阅&d暮色森林&f&r任务线!"
				""
				"{image:atm:textures/questpics/gettingstarted/twilight_portal.png width:241 height:180 align:1}"
			]
			id: "23CA641A3D3BE22F"
			min_width: 300
			rewards: [{
				id: "61AB892E58B3E5AE"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			tasks: [{
				dimension: "twilightforest:twilight_forest"
				id: "082FD4018EA92767"
				title: "&d暮色森林&f&r"
				type: "dimension"
			}]
			title: "&d暮色森林&f"
			x: 10.5d
			y: 3.0d
		}
		{
			dependencies: ["0F3D51FFD2FE8DCB"]
			description: [
				"当你探索过&a下界&f,取得&a烈焰棒&f和少量&a末影珍珠&f后,便可制作&d矿石透视药水&r.\\n\\n这种药水能让你获得特定矿石的X光视觉!首先需要制作&a研钵&f,将&a末影珍珠&f研磨成&a末影粉尘&f.\\n\\n将&a研钵和研杵&f配合&a末影粉尘&f与目标金属锭混合,可制成可熔炼的粉末,最终用于酿造矿石透视药水."
				""
				"{@pagebreak}"
				""
				"这是制作&a铁粉&f的示例."
				""
				"{image:atm:textures/questpics/gettingstarted/iron_powder.png width:217 height:69 align:1}"
				""
				"将煅烧粉末&a酿造&f成平凡药水以制作矿石视野药水."
				""
				"{image:atm:textures/questpics/gettingstarted/iron_potions.png width:217 height:105 align:1}"
			]
			icon: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "potionsmaster:allthemodium_sight"
				}
			}
			id: "732A45CE74B8971C"
			min_width: 300
			rewards: [
				{
					id: "126C7766C2476F51"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "46C6CE411021BEEC"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "gear"
			tasks: [
				{
					id: "28D8E7A8C2004419"
					item: "potionsmaster:tile_mortar"
					type: "item"
				}
				{
					id: "294687FFA242DA9C"
					item: "potionsmaster:pestle"
					type: "item"
				}
				{
					id: "7CD4EE36E1614FEC"
					item: "potionsmaster:ender_powder"
					type: "item"
				}
				{
					id: "07E0A5D3F4546624"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "potionsmaster:coal_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:diamond_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:emerald_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:gold_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:iron_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:lapis_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:redstone_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:uranium_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:platinum_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:allthemodium_powder"
								}
								{
									Count: 1b
									id: "potionsmaster:netherite_powder"
								}
							]
						}
					}
					title: "矿石粉末"
					type: "item"
				}
			]
			title: "&d矿石透视药水"
			x: 13.5d
			y: 3.0d
		}
		{
			dependencies: ["1FD4C32B3937E1C7"]
			description: ["&e全金属&r可用来制作&d&a传送垫&f&r,用于前往三个新维度:&a挖矿维度&f、异界和彼岸.\\n\\n要前往&a挖矿维度&f,请在主世界放置&a传送垫&f并潜行状态下&a右键点击&f(需双手空手).\\n\\n要前往异界,请在&a下界&f放置&a传送垫&f并潜行状态下&a空手右键点击&f.\\n\\n要前往彼岸,请在&a末地&f放置&a传送垫&f并潜行状态下&a空手右键点击&f."]
			id: "7B74D36B9C69B63E"
			rewards: [
				{
					id: "4E0E7E5434500C8F"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "7F283B11A18641D8"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "square"
			tasks: [{
				id: "016C79FEE71EC9FE"
				item: "allthemodium:teleport_pad"
				type: "item"
			}]
			title: "&e&a传送垫&f"
			x: 13.5d
			y: 11.0d
		}
		{
			dependencies: ["7B74D36B9C69B63E"]
			description: ["&a挖矿维度&r是绝佳的采矿场所.没想到吧？\\n\\n这是个平坦的多层世界,主世界、下界和末地的矿石在这里一应俱全.\\n\\n注意:振金和叵得矿无法在此获取."]
			icon: "allthemodium:alloy_pick"
			id: "3FDB414F277C8BC1"
			rewards: [{
				id: "08BF439E888E659B"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				dimension: "allthemodium:mining"
				id: "3632A1D25278EEC4"
				title: "&a造访&r &d采矿&r &a维度&r"
				type: "dimension"
			}]
			title: "&a挖矿&r &d维度&r"
			x: 14.5d
			y: 12.5d
		}
		{
			dependencies: ["7B74D36B9C69B63E"]
			description: ["&d异界&r遍布着装满战利品和刷怪笼的地牢,ATM模组的终极BOSS——&5猪灵魔像&r也栖息于此.祝你好运击败它!"]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "allthemodium:item/piglich_heart"
				}
			}
			id: "515E7703BA1F55FB"
			rewards: [{
				id: "7F3E7C67AB056EAA"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				dimension: "allthemodium:the_other"
				id: "3472C376356CADEE"
				type: "dimension"
			}]
			title: "&a异&r &d界&r"
			x: 12.5d
			y: 12.5d
		}
		{
			dependencies: ["7B74D36B9C69B63E"]
			description: ["位于&a末地&f边缘之外的彼岸，是为建筑玩家提供的广阔纯净空间。\\n\\n类似主世界与下界的关系，&a末地&f到彼岸存在1:50的方块比例换算。"]
			icon: "voidtotem:totem_of_void_undying"
			id: "4D160570DC0C8898"
			rewards: [{
				id: "4D320FE983E58193"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				dimension: "allthemodium:the_beyond"
				id: "3F76760927159DF0"
				type: "dimension"
			}]
			title: "&a彼&r &d岸&r"
			x: 13.5d
			y: 13.5d
		}
		{
			dependencies: ["4E178CCAC7F85F54"]
			description: ["获得&e全金属&r后,下一阶段需要&d振金&r.\\n\\n这种稀有矿石生成于&a下界&fY64以上的天花板上,或在异界Y0-Y40的洞窟墙壁中.\\n\\n注意:&a矿石&f仅会暴露在空气中生成!"]
			id: "56A5C102BDD74ED8"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2F60D92333BD6A57"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "7D418AF91CB716B9"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "6F222A2906776DFE"
				item: "allthemodium:raw_vibranium"
				type: "item"
			}]
			title: "&a振金矿石&f"
			x: 9.5d
			y: 11.0d
		}
		{
			dependencies: ["4E178CCAC7F85F54"]
			description: ["振金装备虽强,但仍有提升空间.\\n\\n终极材料&d叵得矿&r是仅存在于&a末地&f高地群系的珍稀矿石."]
			id: "17FCF0E1AA82E296"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4BF82D303CC74EE1"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "1286386E3AF87098"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "2D04277650830C69"
				item: "allthemodium:raw_unobtainium"
				type: "item"
			}]
			title: "&d&a叵得矿石&f"
			x: 10.5d
			y: 11.0d
		}
		{
			dependencies: ["4E178CCAC7F85F54"]
			description: ["升级&e全金属&r装备需要获取对应的&a锻造模板&f.\\n\\n&d振金&r模板可在&a下界&f&5堡垒遗迹&r的&a可疑灵魂沙&f中用刷子获取.\\n\\n&d叵得矿&r模板需在异界地牢图书馆的战利品箱中概率获得."]
			id: "4C2E9478545AAB38"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "17826C2D2F6A4488"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "3DC03EDFFE7D7305"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "15A688893DFADCBC"
					item: "allthemodium:vibranium_upgrade_smithing_template"
					type: "item"
				}
				{
					id: "534DF8CF9B8A0012"
					item: "allthemodium:unobtainium_upgrade_smithing_template"
					type: "item"
				}
			]
			title: "&d全金属装备升级"
			x: 11.5d
			y: 11.0d
		}
		{
			dependencies: [
				"69A517AB8A801939"
				"6D09511D64DDC282"
				"6EE5BE5693E8ACE4"
				"2D879A34A5788CCC"
			]
			description: ["当你击败MC主要BOSS、建立基础能源系统并获取&e全金属&r后,新的征程即将开启."]
			icon: "allthetweaks:atm_star"
			id: "585502BC014E420F"
			rewards: [{
				id: "50EF2FE79AC5FB74"
				type: "xp"
				xp: 10
			}]
			shape: "pentagon"
			size: 3.0d
			subtitle: "解锁第二章"
			tasks: [{
				id: "4EBF6B68ED8C9B40"
				title: "&6迈向&aATM之星&f&r!"
				type: "checkmark"
			}]
			title: "&e迈向&aATM之星&f&r!"
			x: 22.5d
			y: 7.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方&r或&2社区贡献者&r为整合包创作."
				"根据&eAll Rights Reserved&r许可协议,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的整合包中使用本任务."
				""
				""
				""
				"该任务默认隐藏,若你看到此提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "48EAD15A211C3087"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "20D6D248995ED9EB"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "493C6AAD521D5A1E"
					title: "全模组任务集"
					type: "checkmark"
				}
			]
			x: -5.0d
			y: 3.0d
		}
	]
	title: "§a第一章§r:§b开端"
}
