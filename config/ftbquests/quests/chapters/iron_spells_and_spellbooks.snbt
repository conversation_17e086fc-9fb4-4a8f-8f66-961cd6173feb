{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "iron_spells_and_spellbooks"
	group: "02FE661031A105D8"
	icon: "irons_spellbooks:scroll"
	id: "76DF5FAA7961E5E7"
	images: [
		{
			height: 3.0d
			image: "atm:textures/questpics/iron_spells/spells_title.png"
			rotation: 0.0d
			width: 9.38265306122449d
			x: 3.5d
			y: -1.5d
		}
		{
			height: 3.0d
			hover: ["烈焰法师"]
			image: "atm:textures/questpics/iron_spells/spells_pyromancer.png"
			rotation: 0.0d
			width: 1.5506216696269983d
			x: 5.5d
			y: 1.5d
		}
		{
			height: 3.0d
			hover: ["寒冰法师"]
			image: "atm:textures/questpics/iron_spells/spells_cryomancer.png"
			rotation: 0.0d
			width: 1.738703339882122d
			x: 0.5d
			y: 1.5d
		}
		{
			height: 3.0d
			hover: ["&a编写桌&f"]
			image: "atm:textures/questpics/iron_spells/spells_table.png"
			rotation: 0.0d
			width: 5.4288407163053725d
			x: -6.5d
			y: 0.5d
		}
		{
			height: 2.0d
			hover: ["炼金大锅"]
			image: "atm:textures/questpics/iron_spells/spells_cauldron.png"
			rotation: 0.0d
			width: 2.106776180698152d
			x: -8.5d
			y: 3.0d
		}
		{
			height: 2.0d
			hover: ["&a奥术砧&f"]
			image: "atm:textures/questpics/iron_spells/spells_anvil.png"
			rotation: 0.0d
			width: 1.9232409381663114d
			x: -6.5d
			y: 3.0d
		}
		{
			height: 2.0d
			hover: ["卷轴锻造台"]
			image: "atm:textures/questpics/iron_spells/spells_scroll.png"
			rotation: 0.0d
			width: 2.3376623376623376d
			x: -4.5d
			y: 3.0d
		}
		{
			height: 4.0d
			hover: ["&a死树木&f之王"]
			image: "atm:textures/questpics/iron_spells/spells_king.png"
			rotation: 0.0d
			width: 2.3363914373088686d
			x: 12.0d
			y: 6.0d
		}
		{
			height: 3.0d
			hover: ["死灵法师"]
			image: "atm:textures/questpics/iron_spells/spells_necromancer.png"
			rotation: 0.0d
			width: 1.7410714285714288d
			x: 16.0d
			y: 6.5d
		}
		{
			height: 3.0d
			hover: ["远古骑士"]
			image: "atm:textures/questpics/iron_spells/spells_knight.png"
			rotation: 0.0d
			width: 2.070567986230637d
			x: 14.0d
			y: 6.5d
		}
		{
			height: 2.5d
			hover: ["山岳高塔"]
			image: "atm:textures/questpics/iron_spells/spells_mountaintower.png"
			rotation: 0.0d
			width: 4.444444444444445d
			x: -0.5d
			y: -4.5d
		}
		{
			height: 2.5d
			hover: ["烈焰法师塔"]
			image: "atm:textures/questpics/iron_spells/spells_pyromancertower.png"
			rotation: 0.0d
			width: 4.444444444444445d
			x: 6.5d
			y: -4.5d
		}
		{
			height: 2.5d
			hover: ["红树林小屋"]
			image: "atm:textures/questpics/iron_spells/spells_magrovehut.png"
			rotation: 0.0d
			width: 4.444444444444445d
			x: 10.5d
			y: -4.5d
		}
		{
			height: 2.5d
			hover: ["唤魔者堡垒"]
			image: "atm:textures/questpics/iron_spells/spells_evokerfort.png"
			rotation: 0.0d
			width: 4.444444444444445d
			x: -4.5d
			y: -4.5d
		}
	]
	order_index: 8
	quest_links: [ ]
	quests: [
		{
			description: ["&e法术书&r!不然还能把&3法术&r放哪呢？\\n\\n你可以将&3卷轴&r和&e法术书&r放入&a编写桌&f来组合.法术书有容量限制,但再也不用担心稀有度问题了.\\n\\n当&3卷轴&r被添加到&e法术书&r时不会被消耗,而是消耗法力值."]
			hide_dependent_lines: true
			id: "615C3E012B2DECD3"
			rewards: [{
				count: 3
				id: "231B5B9731B07E53"
				item: "minecraft:paper"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "6502D0124314BF25"
				item: "minecraft:paper"
				type: "item"
			}]
			title: "&e法术书&r!"
			x: 7.0d
			y: -6.5d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["可合成的&e&a法术书&f&r...没错制作难度很高.\\n\\n最难获取的是破损典籍,可以在&9远古城市&r的箱子中找到.&4&a血瓶&f&r可通过将生物(甚至你自己)放入营火上的炼药锅或&a炼金术大锅&f获得.&5雷电瓶&r需对闪电苦力怕使用玻璃瓶.\\n\\n集齐材料后即可合成&a古代法典&f.它12格的&3法术&r槽位非常实用!"]
			id: "7408CCF998D9CD37"
			rewards: [{
				id: "4A55C808BAC88ECE"
				item: "irons_spellbooks:legendary_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "6F0B2D8E0F4A8F27"
				item: {
					Count: 1
					id: "irons_spellbooks:netherite_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 12
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&a古代法典&f"
			x: 2.0d
			y: -7.0d
		}
		{
			dependencies: ["7408CCF998D9CD37"]
			description: ["在&a锻造台&f将&a古代法典&f与&aATM锭&r及模板合成.\\n\\n模板可通过在&9远古城市&r刷可疑的黏土获得."]
			id: "445C21949ADA1FE3"
			rewards: [{
				id: "5016E225DFFE4510"
				item: "allthemodium:allthemodium_upgrade_smithing_template"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "12B17D5093655F19"
				item: {
					Count: 1
					id: "allthewizardgear:allthemodium_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 13
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&e全矿物&r &a法术书&f"
			x: 1.0d
			y: -6.5d
		}
		{
			dependencies: ["445C21949ADA1FE3"]
			description: ["在&a锻造台&f将全矿物&e&a法术书&f&r与&a振金锭&r及模板合成.\\n\\n该模板可通过在堡垒遗迹刷可疑的&a灵魂沙&f获得."]
			id: "3DCD38634176BD92"
			rewards: [{
				id: "7799ADC405111DE6"
				item: "allthemodium:vibranium_upgrade_smithing_template"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "6BA92364768B8CD4"
				item: {
					Count: 1
					id: "allthewizardgear:vibranium_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 14
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&3振金&r &a法术书&f"
			x: 0.0d
			y: -6.5d
		}
		{
			dependencies: ["3DCD38634176BD92"]
			description: ["你能想象的最强&e&a法术书&f&r!15个&3法术&r槽位!真的有15种&3法术&r吗？!哦等等确实有15种...虽然也不算很多\\n\\n合成方式同上:前阶法术书+锭+模板."]
			id: "27CF1A2587321A2C"
			rewards: [{
				id: "7A6B5A61207C8054"
				item: "allthemodium:unobtainium_upgrade_smithing_template"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "7DDFBD15D58978B9"
				item: {
					Count: 1
					id: "allthewizardgear:unobtainium_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 15
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&5无法获得&r &a法术书&f"
			x: -1.0d
			y: -6.5d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["最基础的&e法术书&r,因此造价也最便宜!\\n\\n仅能容纳5个&3法术&r."]
			id: "67A587F4BAD76C70"
			rewards: [{
				id: "0181CA7B3448C53C"
				item: "irons_spellbooks:common_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "6077B2E430D01066"
				item: {
					Count: 1
					id: "irons_spellbooks:copper_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 5
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "脆弱典籍"
			x: 6.0d
			y: -6.5d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["比脆弱典籍强不了多少...\\n\\n比脆弱典籍多1个&3法术&r槽位!这意味着能多准备一个强力&3法术&r!"]
			id: "6CE7115DA2B23776"
			rewards: [{
				id: "4B016E7BB94B93F9"
				item: "irons_spellbooks:uncommon_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "34EBDAAB54DEC7D7"
				item: {
					Count: 1
					id: "irons_spellbooks:iron_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 6
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "铁装典籍"
			x: 5.0d
			y: -6.5d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["另一种可合成的&e法术书&r,这个造价稍高!需要从疣猪兽掉落的猪皮.\\n\\n可容纳8个&3法术&r,虽然容量仍偏小但已经有所提升!"]
			id: "66762A4743104157"
			rewards: [{
				id: "7CF77C5B3E6D55F7"
				item: "irons_spellbooks:rare_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "4A1F6C4CC2624031"
				item: {
					Count: 1
					id: "irons_spellbooks:gold_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 8
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "学徒&a法术书&f"
			x: 4.0d
			y: -6.0d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["另一本可制作的&e&a法术书&f&r,按照这个模式制作的话这本会更昂贵.它需要奥术布、金锭和一本&a附魔书&f.它对附魔书的种类不挑剔,只要是附魔过的就行!\\n\\n它有10个&3法术&r槽位,并提供巨大的&a魔力提升&f!"]
			id: "4407BD7B5F033765"
			rewards: [{
				id: "17E703890863C286"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "7040BF1C496C2185"
				item: {
					Count: 1
					id: "irons_spellbooks:diamond_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 10
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "附魔 &a法术书&f"
			x: 3.0d
			y: -7.0d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["它在这儿!它在那儿!它无处不在而且已经腐朽了!你可以在大多数箱子中找到它们.\\n\\n它们可以容纳8个&3法术&r,但提供的法术抗性较低."]
			hide_dependent_lines: true
			id: "1CAEC4C273EDDB99"
			rewards: [{
				id: "7BA21F76F1D1563F"
				item: "irons_spellbooks:rare_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "463614EE1B9E0590"
				item: {
					Count: 1
					id: "irons_spellbooks:rotten_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 8
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "腐朽的 &a法术书&f"
			x: 4.0d
			y: -7.0d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["&a唤魔者&r是Minecraft中强大而致命的迷你Boss.所以当然&l铁傀儡&r添加了一个更强大更致命的版本!是的,这本&e书&r是两者的稀有掉落物!\\n\\n它有10个法术槽位,但其中3个已被占用.&a尖牙攻击&r、&a尖牙护盾&r和&a&a召唤恼鬼群&f&r都随&e书&r自带且无法移除.\\n\\n同时还能提升&a&l唤魔&r效果!"]
			id: "4E3355FDCB65BB63"
			rewards: [{
				id: "158AAA21F5E6260C"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "2C2307F605F18979"
				item: {
					Count: 1
					id: "irons_spellbooks:evoker_spell_book"
					tag: {
						ISB_Spells: {
							data: [
								{
									id: "irons_spellbooks:fang_strike"
									index: 0
									level: 6
									locked: 1b
								}
								{
									id: "irons_spellbooks:fang_ward"
									index: 1
									level: 4
									locked: 1b
								}
								{
									id: "irons_spellbooks:summon_vex"
									index: 2
									level: 4
									locked: 1b
								}
							]
							maxSpells: 10
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&a唤魔者法典"
			x: 3.0d
			y: -8.0d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["要获得这本特殊的&a法术书&f,你需要击败&4&l死亡之王&r!\\n\\n它有10个&3法术&r槽位,但其中4个已被占用.&4血刃斩&r、&4血步&r、&4虹吸射线&r和&4烈焰风暴&r已自带且无法移除."]
			id: "48C68664319B0294"
			rewards: [{
				id: "3980F859732CFA37"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "63FB8946440CCEDB"
				item: {
					Count: 1
					id: "irons_spellbooks:necronomicon_spell_book"
					tag: {
						ISB_Spells: {
							data: [
								{
									id: "irons_spellbooks:blood_slash"
									index: 0
									level: 5
									locked: 1b
								}
								{
									id: "irons_spellbooks:blood_step"
									index: 1
									level: 5
									locked: 1b
								}
								{
									id: "irons_spellbooks:ray_of_siphoning"
									index: 2
									level: 5
									locked: 1b
								}
								{
									id: "irons_spellbooks:blaze_storm"
									index: 3
									level: 5
									locked: 1b
								}
							]
							maxSpells: 10
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "死灵之书"
			x: 3.0d
			y: -6.0d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["这本&e&a法术书&f&r是&c烈焰人&r的稀有掉落物.我记得这一点是因为当我从我的&c烈焰人&r农场得到一个时我很困惑.\\n\\n它能提升&c火焰法术&r效果,并有10个&3法术&r槽位."]
			id: "1A0368CB49B8CBFF"
			rewards: [{
				id: "017C87162F328470"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "093FE39726FF986E"
				item: {
					Count: 1
					id: "irons_spellbooks:blaze_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 10
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&c烈焰教导手册"
			x: 3.0d
			y: -9.0d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["这个需要花点时间解释,所以请耐心听.\\n\\n首先,找到一个&a唤魔者要塞&r.\\n然后,找到藏在其中的一本&a研究手册&f.接着,让被囚禁在&a要塞&r中的商人翻译它.(就像我们的人质任务翻译员一样!)\\n翻译完成后,把它带到&e&l牧师&r所在的&e村庄&r,用它交换&e&a法术书&f&r!\\n\\n它能提升&e神圣法术&r效果,别担心,被囚禁的商人非常非常稀有,继续寻找吧!"]
			id: "73D6B980D35082A0"
			rewards: [{
				id: "3BA65ACBDE632A92"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "0C83BA739BAA08DA"
				item: {
					Count: 1
					id: "irons_spellbooks:villager_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 10
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&e村民圣经"
			x: 3.0d
			y: -5.0d
		}
		{
			dependencies: ["1CAEC4C273EDDB99"]
			description: ["&2&l自然&r的&e&a法术书&f&r.\\n\\n它有10个&3法术&r槽位,并能提升&2自然法术&r效果!\\n\\n不过你需要一本腐朽的&a法术书&f来制作它..."]
			id: "78AA7BFABBFB9973"
			rewards: [{
				id: "35475D285361ED90"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "3861A10D8E2EF77B"
				item: {
					Count: 1
					id: "irons_spellbooks:druidic_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 10
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&2德鲁伊典籍"
			x: 3.0d
			y: -4.0d
		}
		{
			dependencies: ["615C3E012B2DECD3"]
			description: ["另一本需要破损的书来制作的物品.你可以在&9远古城市&r的箱子中找到破损的书.\\n\\n你还需要龙皮,它由&d&a末影龙&f&r掉落!\\n\\n它将拥有12个&3法术&r槽位,并能提升&d末地法术&r效果!"]
			id: "652A14E17DDE97E6"
			rewards: [{
				id: "7BB7AECDA4A62E3D"
				item: "irons_spellbooks:legendary_ink"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "5D6DCB8FCAC5B1C6"
				item: {
					Count: 1
					id: "irons_spellbooks:dragonskin_spell_book"
					tag: {
						ISB_Spells: {
							data: [ ]
							maxSpells: 12
							mustEquip: 1b
							spellWheel: 1b
						}
					}
				}
				type: "item"
			}]
			title: "&d龙皮 &a法术书&f"
			x: 2.0d
			y: -6.0d
		}
		{
			description: [
				"这是个非常重要且常见的方块!可通过合成或在多数&l铁魔法&r结构中找到.\\n\\n用于在&e&a法术书&f&r中添加或移除&3卷轴&r!将&e&a法术书&f&r放入书籍槽,下方放置&3卷轴&r.若想&a移除&f&3卷轴&r,点击后从右侧取出放入背包."
				""
				"{image:atm:textures/questpics/iron_spells/spells_table_gui.png width:150 height:100 align:center}"
			]
			id: "2A787B99A8B0C767"
			rewards: [{
				count: 3
				id: "70AB901751BE0F43"
				item: "minecraft:paper"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "6EAC7D1EB04B2686"
				item: "irons_spellbooks:inscription_table"
				type: "item"
			}]
			x: -2.75d
			y: 0.5d
		}
		{
			description: [
				"&a奥术砧&f是&l铁魔法&r中用途最广的方块.\\n\\n可用同色墨水合并&3法术&r进行升级.\\n能将&3卷轴&r合成可装备武器(如剑).\\n还能使用升级宝珠为戒指设置属性,或为武器装备添加升级宝珠."
				""
				"{image:atm:textures/questpics/iron_spells/spells_anvil_gui.png width:100 height:100 align:center}"
			]
			id: "551A4916F032ACCF"
			rewards: [{
				count: 3
				id: "12BF12853B302708"
				item: "irons_spellbooks:upgrade_orb"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "7A0BAF69F0AEE0AE"
				item: "irons_spellbooks:arcane_anvil"
				type: "item"
			}]
			x: -2.0d
			y: -0.25d
		}
		{
			description: [
				"卷轴锻造台,顾名思义!\\n\\n用于制作&3卷轴&r!\\n\\n添加墨水设定稀有度,用纸制作&3卷轴&r本体,再放入对应&3法术&r类型的物品(&c&a烈焰棒&f&r对应&c&l火焰&r,&2毒马铃薯&r对应&2&l自然&r等),最后选择&3法术&r即可生成!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_scroll_gui.png width:150 height:100 align:center}"
			]
			id: "69838E3F12218D68"
			rewards: [{
				count: 3
				id: "20EDABAC9A96258A"
				item: "irons_spellbooks:rare_ink"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "6FC286A3EC91DE9F"
				item: "irons_spellbooks:scroll_forge"
				type: "item"
			}]
			x: -1.25d
			y: 0.5d
		}
		{
			description: [
				"绝对比普通炼药锅强!\\n\\n&a炼金术士&f炼药锅可以:\\n1. 通过烹煮生物或玩家获取&4血液&r\\n2. 制作升级药水\\n3. 有50%概率从卷轴提取墨水\\n4. 将4个同稀有度墨水合成更高稀有度"
				""
				""
				"{image:atm:textures/questpics/iron_spells/spells_cauldron.png width:100 height:100 align:center}"
			]
			id: "3E8077AB45C79E6A"
			rewards: [
				{
					id: "4065C90A9423D9A9"
					item: "irons_spellbooks:oakskin_elixir"
					type: "item"
				}
				{
					id: "54B576CCD81B30BF"
					item: "irons_spellbooks:invisibility_elixir"
					type: "item"
				}
				{
					id: "047CA47141F8B998"
					item: "irons_spellbooks:evasion_elixir"
					type: "item"
				}
			]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "7DE683FE83AE2C19"
				item: "irons_spellbooks:alchemist_cauldron"
				type: "item"
			}]
			x: -2.0d
			y: 1.25d
		}
		{
			id: "541446863A72B96C"
			rewards: [{
				count: 2
				id: "18ABCBE38C93164A"
				item: "irons_spellbooks:common_ink"
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "3508E0BECB5DD919"
				item: "irons_spellbooks:common_ink"
				type: "item"
			}]
			title: "&7普通&r墨水"
			x: -3.0d
			y: 3.0d
		}
		{
			id: "637C87D8968CD51A"
			rewards: [{
				count: 2
				id: "5D16DACA6D6E8EA2"
				item: "irons_spellbooks:uncommon_ink"
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "718C6E669D17F0C0"
				item: "irons_spellbooks:uncommon_ink"
				type: "item"
			}]
			title: "&a罕见&r墨水"
			x: -2.5d
			y: 2.5d
		}
		{
			id: "60FAD8BF235E381B"
			rewards: [{
				count: 2
				id: "0DF72CE9CA1693CD"
				item: "irons_spellbooks:rare_ink"
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "38691751D6156791"
				item: "irons_spellbooks:rare_ink"
				type: "item"
			}]
			title: "&b稀有&r墨水"
			x: -2.0d
			y: 3.0d
		}
		{
			id: "06F8EDF7513F8611"
			rewards: [{
				count: 2
				id: "1EEABD789960AC05"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "1C60DB6E320883DF"
				item: "irons_spellbooks:epic_ink"
				type: "item"
			}]
			title: "&b史诗&r墨水"
			x: -1.5d
			y: 2.5d
		}
		{
			id: "67D40A1A9332C03D"
			rewards: [{
				count: 2
				id: "2AC73A86B874938C"
				item: "irons_spellbooks:legendary_ink"
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				id: "4948A54224513853"
				item: "irons_spellbooks:legendary_ink"
				type: "item"
			}]
			title: "&b传说&r墨水"
			x: -1.0d
			y: 3.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: ["驾驭&5闪电&r的力量!&5&l闪电&r系专注于运用电能击杀敌人并强化自身.部分攻击甚至能召唤真实的&5闪电&r!\\n\\n&5&l闪电&r系的核心材料是&5闪电瓶&r,可通过对闪电苦力怪使用&a玻璃瓶&f获取.你需要用&5闪电&r来制造更多&5闪电&r!\\n\\n其&5卷轴&r为&7灰色&r底色配&b&a淡蓝色&f&r文字."]
			id: "36F87173D8CD1D68"
			rewards: [{
				count: 4
				id: "3B60CA662CF4E48E"
				item: "irons_spellbooks:lightning_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "0DCF8CD129263225"
				item: "irons_spellbooks:lightning_rune"
				type: "item"
			}]
			title: "&5&l闪电"
			x: -1.5d
			y: 6.0d
		}
		{
			description: ["&7符文&r是构筑职业体系的核心组件.获取&7&a空白符文&f&r的方式包括击败&4&l亡灵君王&r,或使用净化石清除已铭刻符文.\\n\\n现有8种职业(含1个隐藏职业),每个职业都有专属的&3卷轴&r、核心材料、升级路线及外观套装."]
			id: "639531AB27DCD267"
			rewards: [{
				count: 3
				id: "22A13BA6CFA84533"
				item: "irons_spellbooks:blank_rune"
				type: "item"
			}]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				id: "3D0E50F79BBFB8E2"
				item: "irons_spellbooks:blank_rune"
				type: "item"
			}]
			title: "&l职业系统"
			x: 3.0d
			y: 4.5d
		}
		{
			dependencies: ["36F87173D8CD1D68"]
			id: "05DBDD86FCFF3BA8"
			rewards: [{
				id: "6E3D432F89004AF6"
				item: "irons_spellbooks:lightning_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "659F1A7D0E67F23E"
				item: "irons_spellbooks:lightning_upgrade_orb"
				type: "item"
			}]
			title: "&5闪电&r升级宝珠"
			x: -2.0d
			y: 7.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: ["&c&l火焰&r系自然与&c火&r元素相关,多数卷轴效果都涉及用&c火焰&r灼烧敌人.\\n\\n&c&l火焰&r系核心材料是&c&a烈焰棒&f&r,可通过合成&c火焰符文&r或击杀&c&l炎术士&r获取.\\n\\n其&c卷轴&r为&c浅红色&r底色配&8灰色&r文字."]
			id: "69763C2E2F454A73"
			rewards: [{
				count: 4
				id: "4D17EFD179403504"
				item: "irons_spellbooks:fire_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "634698E023224876"
				item: "irons_spellbooks:fire_rune"
				type: "item"
			}]
			title: "&c&l烈焰&r"
			x: -1.5d
			y: 9.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: ["&b&l寒冰&r系专注冬季魔法,运用&b冰霜&r与&b雪&r之力冻结伤害敌人.\\n\\n核心材料&b&a冻骨&f&r由&b流浪者&r掉落,可用于合成&b寒冰符文&r或通过击杀&b&l霜术士&r获取.\\n\\n其&b卷轴&r呈&b&a淡蓝色&f&r底色配&9深蓝&r文字."]
			id: "7C79BCE482A06199"
			rewards: [{
				count: 4
				id: "47BE12E5770D5DDF"
				item: "irons_spellbooks:ice_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "54D2968A39335B94"
				item: "irons_spellbooks:ice_rune"
				type: "item"
			}]
			title: "&b&l寒冰"
			x: 1.5d
			y: 9.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: ["&d&l末影&r系汲取&d末影人&r与&d&a末影龙&f&r的力量化为己用,包含&d瞬移&r、&d火球&r及&d&a龙息&f&r等特色能力.\\n\\n核心材料为&d&a末影珍珠&f&r.\\n\\n其&d卷轴&r以&5紫色&r为底配&d粉红&r文字."]
			id: "0EED5D755D2C866A"
			rewards: [{
				count: 4
				id: "395F8BF373AC047E"
				item: "irons_spellbooks:ender_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "4C392FED43CE3A79"
				item: "irons_spellbooks:ender_rune"
				type: "item"
			}]
			title: "&d&l末影"
			x: 1.5d
			y: 6.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: ["&e&l神圣&r系专精治疗辅助,主打&a愈合&f与团队支援.\\n\\n核心材料&e神圣珍珠&r需合成获取,可通过制作符文或击杀&e&l祭司&r获得&e神圣符文&r.\\n\\n&e神圣卷轴&r为&f白色&r底色配&e金色&r文字."]
			id: "7270A37E31A70C91"
			rewards: [{
				count: 4
				id: "332785208A140301"
				item: "irons_spellbooks:holy_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "76FE9CD7C301AD3B"
				item: "irons_spellbooks:holy_rune"
				type: "item"
			}]
			title: "&e&l神圣"
			x: 4.5d
			y: 6.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: ["该体系直接将游戏评级从E10+提升至Teen级.&4&l鲜血&r系通过杀戮敌人恢复自身&c生命值&r,多数攻击都带吸血效果.\\n\\n核心材料&4&a血瓶&f&r需用大锅烹煮生物或玩家获取.\\n\\n其&4卷轴&r为棕色(无棕色代码)配&4&a暗红&f&r文字."]
			id: "24150DE600C2D761"
			rewards: [{
				count: 4
				id: "547550452D865205"
				item: "irons_spellbooks:blood_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "46FE75ED07A456F7"
				item: "irons_spellbooks:blood_rune"
				type: "item"
			}]
			title: "&4&l鲜血"
			x: 4.5d
			y: 9.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: ["&n&a唤魔定义:&r\\n召唤灵体或神明的仪式.\\n\\n&a&l唤魔&r系复现&a唤魔者&r的能力,主打召唤术——无论是&a尖牙&r还是&a恼鬼&r,只要是召唤物都属此系.\\n\\n核心材料为&a绿宝石&r,可通过合成符文或击败&a&l大唤魔师&r获取.\\n\\n&a唤魔卷轴&r为&f白色&r底色配&0黑色&r文字."]
			id: "1BE69992D2C5085B"
			rewards: [{
				count: 4
				id: "41C19F9FA3187DBA"
				item: "irons_spellbooks:evocation_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "79C97582AC678CBF"
				item: "irons_spellbooks:evocation_rune"
				type: "item"
			}]
			title: "&a&l唤魔"
			x: 7.5d
			y: 6.0d
		}
		{
			dependencies: ["639531AB27DCD267"]
			description: [
				"&2&l自然&r模组通过&2毒害&r和&2削弱&r敌人来利用地球力量——就像大企业对地球所做的那样!"
				"\\n&2&l自然&r的核心材料是&2毒马铃薯&r."
				"\\n\\n该模组的&2卷轴&r呈&a淡绿色&f&r,文字为&2碧绿色&f&r."
			]
			id: "000C1ECD781F3F81"
			rewards: [{
				count: 4
				id: "545B0278B6FE8509"
				item: "irons_spellbooks:nature_rune"
				type: "item"
			}]
			shape: "pentagon"
			size: 1.3d
			tasks: [{
				id: "638387342D93E886"
				item: "irons_spellbooks:nature_rune"
				type: "item"
			}]
			title: "&2&l自然"
			x: 7.5d
			y: 9.0d
		}
		{
			dependencies: ["69763C2E2F454A73"]
			id: "32D2CE292E088939"
			rewards: [{
				id: "128A56F15DBD0BBC"
				item: "irons_spellbooks:fire_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "17BDD679028814A2"
				item: "irons_spellbooks:fire_upgrade_orb"
				type: "item"
			}]
			title: "&c火焰&r升级宝珠"
			x: -2.0d
			y: 10.0d
		}
		{
			dependencies: ["7C79BCE482A06199"]
			id: "76558D23A70AFF78"
			rewards: [{
				id: "2926EE1DDA1EAE25"
				item: "irons_spellbooks:ice_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "1363286BCB4D1B2F"
				item: "irons_spellbooks:ice_upgrade_orb"
				type: "item"
			}]
			title: "&b寒冰&r升级宝珠"
			x: 1.0d
			y: 10.0d
		}
		{
			dependencies: ["0EED5D755D2C866A"]
			description: ["为什么是湿的？难道是从湖里捞出来的？"]
			id: "2BB910B217DF3A12"
			rewards: [{
				id: "760BB6C069D24785"
				item: "irons_spellbooks:ender_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "739EFE9E68204C8E"
				item: "irons_spellbooks:ender_upgrade_orb"
				type: "item"
			}]
			title: "&d末地&r升级宝珠"
			x: 1.0d
			y: 7.0d
		}
		{
			dependencies: ["7270A37E31A70C91"]
			id: "401BB95B740385B7"
			rewards: [{
				id: "04F75F97A70D4F92"
				item: "irons_spellbooks:holy_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "5C06B792E65671B7"
				item: "irons_spellbooks:holy_upgrade_orb"
				type: "item"
			}]
			title: "&e神圣&r升级宝珠"
			x: 4.0d
			y: 7.0d
		}
		{
			dependencies: ["24150DE600C2D761"]
			id: "2E6F1C8718EB8C66"
			rewards: [{
				id: "6883DD602681DE95"
				item: "irons_spellbooks:blood_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "51A955F8266388C0"
				item: "irons_spellbooks:blood_upgrade_orb"
				type: "item"
			}]
			title: "&4鲜血&r升级宝珠"
			x: 4.0d
			y: 10.0d
		}
		{
			dependencies: ["1BE69992D2C5085B"]
			id: "0CE1F6DE5FD6A6D9"
			rewards: [{
				id: "61833CFDCEF0E599"
				item: "irons_spellbooks:evocation_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "445AFFA43D663E2E"
				item: "irons_spellbooks:evocation_upgrade_orb"
				type: "item"
			}]
			title: "&a唤魔&r升级宝珠"
			x: 7.0d
			y: 7.0d
		}
		{
			dependencies: ["000C1ECD781F3F81"]
			id: "4104ABDC0E577350"
			rewards: [{
				id: "64D833B66910C00A"
				item: "irons_spellbooks:nature_upgrade_orb"
				type: "item"
			}]
			tasks: [{
				id: "2827F04FDE7E2EFF"
				item: "irons_spellbooks:nature_upgrade_orb"
				type: "item"
			}]
			title: "&2自然&r升级宝珠"
			x: 7.0d
			y: 10.0d
		}
		{
			dependencies: ["69763C2E2F454A73"]
			description: [
				"希望你不是从&c&l纵火者&r那里偷来的,他可是个好人!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_outfit_pyromancer.png width:50 height:100 align:center}"
			]
			id: "06F2C42E6408149E"
			rewards: [{
				count: 10
				id: "4FB4692B0380ECAF"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "15A5A3E9B5E7408D"
					item: {
						Count: 1
						id: "irons_spellbooks:pyromancer_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "16B174CC92560C1D"
					item: {
						Count: 1
						id: "irons_spellbooks:pyromancer_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "773A7DAC74D1129E"
					item: {
						Count: 1
						id: "irons_spellbooks:pyromancer_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "3DD0EAF67B234245"
					item: {
						Count: 1
						id: "irons_spellbooks:pyromancer_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&c烈焰法师套装"
			x: -1.0d
			y: 10.0d
		}
		{
			dependencies: ["7C79BCE482A06199"]
			description: [
				"反正他很讨厌,偷就偷吧我不在乎."
				""
				"{image:atm:textures/questpics/iron_spells/spells_outfit_cryomancer.png width:50 height:100 align:center}"
			]
			id: "489833A2A6C39151"
			rewards: [{
				count: 10
				id: "7A8AA6AD4583AD31"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "074C34798C780717"
					item: {
						Count: 1
						id: "irons_spellbooks:cryomancer_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "16E1EA352F3AE603"
					item: {
						Count: 1
						id: "irons_spellbooks:cryomancer_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "6902B4D88C5D1D9E"
					item: {
						Count: 1
						id: "irons_spellbooks:cryomancer_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "02A8F765C44A7CD6"
					item: {
						Count: 1
						id: "irons_spellbooks:cryomancer_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&b寒冰法师套装"
			x: 2.0d
			y: 10.0d
		}
		{
			dependencies: ["36F87173D8CD1D68"]
			description: [
				"这个什么时候会加入模组？"
				""
				"{image:atm:textures/questpics/iron_spells/spells_outfit_electromancer.png width:50 height:100 align:center}"
			]
			id: "4CCA1E2AAA9AB9D9"
			rewards: [{
				count: 10
				id: "05611EF761E22078"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "648C0C5589A1EBF8"
					item: {
						Count: 1
						id: "irons_spellbooks:electromancer_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "64556EDCE8DFDC01"
					item: {
						Count: 1
						id: "irons_spellbooks:electromancer_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "17B2E0E42C9E7FD4"
					item: {
						Count: 1
						id: "irons_spellbooks:electromancer_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "5E5E043729B00A2F"
					item: {
						Count: 1
						id: "irons_spellbooks:electromancer_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&5雷电法师套装"
			x: -1.0d
			y: 7.0d
		}
		{
			dependencies: ["0EED5D755D2C866A"]
			description: ["{image:atm:textures/questpics/iron_spells/spells_outfit_shadow.png width:60 height:100 align:center}"]
			id: "19542103E042BDB5"
			rewards: [{
				count: 10
				id: "556A75A230A7FB7F"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "45EA8BAA78E35A59"
					item: {
						Count: 1
						id: "irons_spellbooks:shadowwalker_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "3390FC17DC27AE44"
					item: {
						Count: 1
						id: "irons_spellbooks:shadowwalker_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "6F42DE6255FA2EC5"
					item: {
						Count: 1
						id: "irons_spellbooks:shadowwalker_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "79BA11CC61AFF789"
					item: {
						Count: 1
						id: "irons_spellbooks:shadowwalker_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&d暗影行者套装"
			x: 2.0d
			y: 7.0d
		}
		{
			dependencies: ["7270A37E31A70C91"]
			description: [
				"听说现在上网课就能考取这个职业资格."
				""
				"{image:atm:textures/questpics/iron_spells/spells_outfit_priest.png width:60 height:100 align:center}"
			]
			id: "24BAC1C0392E950E"
			rewards: [{
				count: 10
				id: "3234598909E89E39"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "180047D663C0E520"
					item: {
						Count: 1
						id: "irons_spellbooks:priest_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "7A7FA15862290EB5"
					item: {
						Count: 1
						id: "irons_spellbooks:priest_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "4666D04D6F72D4EF"
					item: {
						Count: 1
						id: "irons_spellbooks:priest_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "42915369150F71AC"
					item: {
						Count: 1
						id: "irons_spellbooks:priest_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&6祭司套装"
			x: 5.0d
			y: 7.0d
		}
		{
			dependencies: ["24150DE600C2D761"]
			description: ["{image:atm:textures/questpics/iron_spells/spells_outfit_cultist.png width:60 height:100 align:center}"]
			id: "5DE863C3FB6492BE"
			rewards: [{
				count: 10
				id: "7E0AB4E3643616E9"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "31E9928D9D8E56B1"
					item: {
						Count: 1
						id: "irons_spellbooks:cultist_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "3B58AA5A37681F95"
					item: {
						Count: 1
						id: "irons_spellbooks:cultist_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "0DE64C8F5E57D530"
					item: {
						Count: 1
						id: "irons_spellbooks:cultist_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "51AF0872134A9438"
					item: {
						Count: 1
						id: "irons_spellbooks:cultist_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&4邪教徒套装"
			x: 5.0d
			y: 10.0d
		}
		{
			dependencies: ["1BE69992D2C5085B"]
			description: [
				"他或许邪恶,但依旧时尚."
				""
				"{image:atm:textures/questpics/iron_spells/spells_outfit_archevoker.png width:60 height:100 align:center}"
			]
			id: "0E594BC58CC7476F"
			rewards: [{
				count: 10
				id: "030656069DF2B656"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "4C54C4B0BEA37C43"
					item: {
						Count: 1
						id: "irons_spellbooks:archevoker_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "103B9A33981DA57E"
					item: {
						Count: 1
						id: "irons_spellbooks:archevoker_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "12462F1FBEF40EC7"
					item: {
						Count: 1
						id: "irons_spellbooks:archevoker_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "6DD1859E412EDD24"
					item: {
						Count: 1
						id: "irons_spellbooks:archevoker_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&a高阶唤魔师套装"
			x: 8.0d
			y: 7.0d
		}
		{
			dependencies: ["000C1ECD781F3F81"]
			description: [
				"万圣节期间特别受欢迎!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_outfit_plagued.png width:60 height:100 align:center}"
			]
			id: "6D232F6D8E8DA546"
			rewards: [{
				count: 10
				id: "1C626523F443307E"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [
				{
					id: "5F3591F891E97AE9"
					item: {
						Count: 1
						id: "irons_spellbooks:plagued_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "12A9B2F2883A2B64"
					item: {
						Count: 1
						id: "irons_spellbooks:plagued_chestplate"
						tag: {
							Damage: 0
							ISB_Spells: {
								data: [ ]
								maxSpells: 1
								mustEquip: 1b
								spellWheel: 1b
							}
						}
					}
					type: "item"
				}
				{
					id: "2E4C5DDFBAF2E7F1"
					item: {
						Count: 1
						id: "irons_spellbooks:plagued_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "795A76F0EDCF55DD"
					item: {
						Count: 1
						id: "irons_spellbooks:plagued_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&2瘟疫使者套装"
			x: 8.0d
			y: 10.0d
		}
		{
			dependencies: ["3009018DEC1EC952"]
			description: [
				"&l铁魔法&r中最大最难的建筑.\\n\\n这些深埋地下的结构类似试炼大厅,也可用迷途指南针协助寻找!\\n\\n内含大量刷怪笼、&a试炼刷怪笼&f及战利品.\\n\\n最重要的是王座厅,抵达时&4&l亡者之王&r的骸骨会复活!(若未触发可能需要点击他)"
				""
				"{image:atm:textures/questpics/iron_spells/spells_catacombs.png width:200 height:100 align:center}"
			]
			icon: "minecraft:stone_bricks"
			id: "6DB5732177AABB87"
			rewards: [{
				count: 8
				id: "5393F61EE3C9DEF3"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				id: "58BDC26BCCADEC22"
				structure: "irons_spellbooks:catacombs"
				type: "structure"
			}]
			title: "&4地下墓穴"
			x: 9.0d
			y: -0.5d
		}
		{
			dependencies: ["6DB5732177AABB87"]
			description: [
				"王座上安放着昔日&4&l国王&r的遗骸.但当你靠近触碰时,它们将重新苏醒——这位&4&l国王&r不再长眠!唯有杀戮能延续他的生命...而此刻最近的活物正是你.\\n\\n&4&l&a死木&f之王&r是最强Boss,拥有400点&c生命值&r,也是唯一拥有正式Boss血条(及专属音乐)的存在.他能施展各流派的&3法术&r,从&2自然系&r的&2毒箭&r到&6神圣系&r的&6枯萎术&r,甚至&a咒法系&r的&a尖牙&r!还能瞬移、召唤亡者,并用&4血之权杖&r攻击你.\\n\\n击败他将掉落各类&3卷轴&r、&7&a空白符文&f&r,并有概率获得&4血之权杖&r与死灵之书."
				""
				"{image:atm:textures/questpics/iron_spells/spells_king.png width:100 height:200 align:center}"
			]
			icon: "minecraft:wither_skeleton_skull"
			id: "0436DF3308681913"
			rewards: [{
				count: 5
				id: "03C23C77EF25C4C0"
				item: "irons_spellbooks:blank_rune"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				entity: "irons_spellbooks:dead_king"
				id: "0578D51FAF53123E"
				type: "kill"
				value: 1L
			}]
			title: "&4&l&a枯木&f之王"
			x: 8.5d
			y: -1.5d
		}
		{
			dependencies: ["3009018DEC1EC952"]
			description: [
				"重兵把守的&a要塞&r是&a&l大唤魔者&r及其追随者的巢穴.\\n当你看见高耸的&a云杉木墙&f和内部巨石建造的&a要塞&r时,便知已抵达此地.\\n\\n卫道士、&a唤魔者&r和劫掠兽在城墙与&a要塞&r内巡逻.突破防线后,你可沿阶梯上行,通过跑酷抵达屋顶,在顶端会见&a&l要塞首领&r.\\n\\n这座严防死守的&a要塞&r内自然藏有大量战利品."
				""
				"{image:atm:textures/questpics/iron_spells/spells_evokerfort.png width:200 height:100 align:center}"
			]
			icon: "minecraft:dark_oak_log"
			id: "641888DC7BC40AA9"
			rewards: [{
				count: 8
				id: "0A507F7F42EA3B4A"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				id: "15B0574265D66F03"
				structure: "irons_spellbooks:evoker_fort"
				type: "structure"
			}]
			title: "&a唤魔者要塞"
			x: 8.5d
			y: 1.0d
		}
		{
			dependencies: ["641888DC7BC40AA9"]
			description: [
				"&a要塞&r顶端盘踞着&a&l大唤魔者&r,他只对玩家及攻击者抱有敌意.\\n\\n他拥有60点生命值,除常规&a唤魔者&r攻击外,还能施展&a烟花&r、&a护盾&r和&a狂风冲击&r!\\n\\n击败后可获得奥术精华、墨水、&a咒法符文&r、&a咒法卷轴&r,并有几率掉落珍稀的&a咒法魔典&r!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_evoker.png width:100 height:200 align:center}"
			]
			icon: "minecraft:totem_of_undying"
			id: "68B44B9E939F4228"
			rewards: [{
				count: 3
				id: "0984DAB8E3603A83"
				item: "irons_spellbooks:evocation_rune"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				entity: "irons_spellbooks:archevoker"
				id: "057705B366878174"
				type: "kill"
				value: 1L
			}]
			title: "&a&l高阶唤魔师"
			x: 7.5d
			y: 1.0d
		}
		{
			dependencies: ["3009018DEC1EC952"]
			description: [
				"在幽深的&a红树林沼泽&f淤泥中,隐居着名为&2&l炼药隐士&r的生物.\\n\\n他远离尘嚣,周遭只摆放着炼制药水所需的器具.\\n\\n你可在其&2茅屋&r中找到这位&2&l炼药隐士&r.\\n\\n屋内还备有酿造台与装满药水的炼药锅!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_magrovehut.png width:200 height:100 align:center}"
			]
			icon: "minecraft:mangrove_log"
			id: "6963EC8A71D66AE3"
			rewards: [{
				count: 8
				id: "1AD3F48A00B9856F"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				id: "60894C5B3A6E9DC1"
				structure: "irons_spellbooks:mangrove_hut"
				type: "structure"
			}]
			title: "&2红树林小屋"
			x: 11.5d
			y: 1.0d
		}
		{
			dependencies: ["6963EC8A71D66AE3"]
			description: [
				"正如其酷似的猪灵族,&2&l炼药隐士&r也属于中立生物.不过他对&e金锭&r毫无兴趣,只反击攻击者.\\n\\n可用绿宝石或药水材料与他交易,换取成品药水、药材或&2自然卷轴&r.\\n\\n若执意战斗,他拥有60点生命值,会用药水和&2自然法术&r反击.受伤时会用药水自愈(看来他确实痴迷药水).\\n\\n击败后掉落奥术精华、墨水、&2自然符文&r及&2自然卷轴&r!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_apotheocarist.png width:100 height:200 align:center}"
			]
			icon: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "healing"
				}
			}
			id: "548F85EE6B6F1811"
			rewards: [{
				count: 3
				id: "4EBAE4CBE310C358"
				item: "irons_spellbooks:nature_rune"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				entity: "irons_spellbooks:apothecarist"
				id: "24CB88A2AB1614D2"
				type: "kill"
				value: 1L
			}]
			title: "&2&l炼金师"
			x: 12.5d
			y: 1.0d
		}
		{
			dependencies: ["3009018DEC1EC952"]
			description: [
				"&c纵火者&r的&c焚毁高塔&r.传说当你望见&c高塔&r轮廓时,便能感受到它散发的灼热.\\n\\n这座&c纵火者之塔&r主要由石料构成,内部采用云杉与橡木建造,附属建筑紧邻主塔.\\n\\n顶层弥漫着烟气的源头处,还能发现&5盔甲堆&r.\\n\\n若要寻找&c&l纵火者&r,需冒险进入地下室!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_pyromancertower.png width:200 height:100 align:center}"
			]
			icon: "minecraft:blackstone"
			id: "30B7AF815D9D7553"
			rewards: [{
				count: 8
				id: "2AA4DDD3E70D6C51"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				id: "461BA0C4C9389E1D"
				structure: "irons_spellbooks:pyromancer_tower"
				type: "structure"
			}]
			title: "&c烈焰法师之塔"
			x: 11.0d
			y: 2.5d
		}
		{
			dependencies: ["30B7AF815D9D7553"]
			description: [
				"&c&l纵火者&r(与派浪漫者不同)是&lIron's Spells&r新增的中立生物.他可能会给附近的生物点上&c火焰&r...\\n\\n你可以用所有&c火系&r物品、墨水和&c火焰卷轴&r与他交易.\\n\\n若想冒着全身毛发被烧光的风险挑战他,请注意——他拥有60点生命值.他会用&c火焰魔法&r让你&c着火&r,这点毋庸置疑.\\n\\n被击败后会掉落奥术精华、墨水、&c火焰符文&r和&c火焰卷轴&r!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_pyromancer.png width:100 height:200 align:center}"
			]
			icon: "minecraft:fire_charge"
			id: "59E68086A8B99EA7"
			rewards: [{
				count: 3
				id: "7FEA8FCAF3452395"
				item: "irons_spellbooks:fire_rune"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				entity: "irons_spellbooks:pyromancer"
				id: "7954F2F744B999C4"
				type: "kill"
				value: 1L
			}]
			title: "&c&l烈焰法师"
			x: 11.5d
			y: 3.5d
		}
		{
			dependencies: ["3009018DEC1EC952"]
			description: [
				"在群山之巅,云海之上矗立着另一座&b高塔&r.这座塔让你脊背发凉.\\n\\n&b高塔&r由石头、&a深色橡木&f和深板岩构成,侧翼延伸出第二座塔楼.外部楼梯直达一层,内部楼梯可通往卧室或侧塔.\\n\\n你还可以顺着梯子下到地下室,&b&l霜冻法师&r很可能就在那里.\\n\\n塔内能找到&a书写台&f、紫水晶和&a战利品箱&f!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_mountaintower.png width:200 height:100 align:center}"
			]
			icon: "minecraft:packed_ice"
			id: "13F77C38AC015E9F"
			rewards: [{
				count: 8
				id: "68D4B581ECCF20B7"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				id: "2E3B052C7DDEC133"
				structure: "irons_spellbooks:mountain_tower"
				type: "structure"
			}]
			title: "&b雪山高塔"
			x: 9.0d
			y: 2.5d
		}
		{
			dependencies: ["13F77C38AC015E9F"]
			description: [
				"你不该闯入&b高塔&r——&b&l霜冻法师&r敌视玩家.\\n\\n他会立即用&b寒冰魔法&r攻击你,甚至驱使动物作战!为取胜不惜牺牲动物,真是冷酷无情!\\n\\n击败后掉落奥术精华、墨水、&b寒冰符文&r和&b寒冰卷轴&r."
				""
				"{image:atm:textures/questpics/iron_spells/spells_cryomancer.png width:100 height:200 align:center}"
			]
			icon: "minecraft:snowball"
			id: "555ED1E39131D91B"
			rewards: [{
				count: 3
				id: "6DA1755FA3A0E77F"
				item: "irons_spellbooks:ice_rune"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				entity: "irons_spellbooks:cryomancer"
				id: "7B5566E59E84B1FE"
				type: "kill"
				value: 1L
			}]
			title: "&b&l寒冰法师"
			x: 8.5d
			y: 3.5d
		}
		{
			dependencies: ["72AB70FD8D8FABBF"]
			description: [
				"&5远古战场&r只是往昔的碎片.\\n\\n无人知晓当年发生了什么.\\n\\n无人知晓谁与谁交战.如今只剩残垣断壁和这些&5盔甲堆&r,不妨用镐子挖走它们."
				""
				"{image:atm:textures/questpics/iron_spells/spells_battleground.png width:200 height:100 align:center}"
			]
			icon: "minecraft:deepslate_tiles"
			id: "7FC54AF87CBDD222"
			rewards: [{
				id: "68D7E0E5F683B522"
				item: {
					Count: 1
					id: "minecraft:diamond_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				id: "5663C7A9B9DB0F11"
				structure: "irons_spellbooks:ancient_battleground"
				type: "structure"
			}]
			title: "&5远古战场"
			x: 14.0d
			y: 1.0d
		}
		{
			dependencies: ["7FC54AF87CBDD222"]
			description: [
				"试图采集&5盔甲堆&r时,它们仿佛被无形躯体穿戴般复活了.\\n\\n这些60点生命值的特殊Boss不使用魔法,更像是魔法造物.它们会用剑进行劈砍或&a蓄力突刺&f.\\n\\n据观察,它们会攻击视野内除其他&5&l骑士&r外的一切目标!\\n\\n击败后掉落&a下界合金碎片&f和灰烬精华,绝对是好东西!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_knight.png width:100 height:200 align:center}"
			]
			icon: "irons_spellbooks:armor_pile"
			id: "395A33977B18B9AD"
			rewards: [{
				count: 5
				id: "09AA886DDCA0ECD2"
				item: "irons_spellbooks:cinder_essence"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				entity: "irons_spellbooks:citadel_keeper"
				id: "02CDD36D03E62071"
				type: "kill"
				value: 1L
			}]
			title: "&5&l远古骑士"
			x: 14.0d
			y: 2.5d
		}
		{
			description: [
				"&l铁魔&r为我们带来了精彩有趣的魔法模组——&l铁魔法的咒语与魔典&r!"
				"\\n\\n本模组核心是制作和使用&3法术&r!每个&3法术&r都有稀有度、等级和派系,且都能收录进法术书."
				"\\n\\n别想光靠&7铁锭&r和&c红石&r就能为所欲为,你得离开舒适区去冒险战斗!"
			]
			id: "300F2E45D185A9A1"
			rewards: [{
				count: 10
				id: "5BEC54791A7352FB"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "gear"
			size: 3.0d
			tasks: [{
				id: "4E7FDA9DE2071EE5"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			title: "&l铁魔法的咒语与魔典"
			x: 3.0d
			y: 1.5d
		}
		{
			dependencies: ["3009018DEC1EC952"]
			description: [
				"几乎每个&6村庄&r都可能存在&6&l祭司&r,包括模组村庄.\\n\\n他们通常在&6村庄&r中心活动,比如集市附近,但并非所有村庄都有."
				""
				"{image:atm:textures/questpics/iron_spells/spells_village.png width:200 height:100 align:center}"
			]
			icon: "minecraft:oak_log"
			id: "01119871B1D5C576"
			rewards: [{
				count: 8
				id: "21F4BD3CC3708E34"
				item: "irons_spellbooks:arcane_essence"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				id: "1758F9A1E399448B"
				structure: "#minecraft:village"
				tags: ["village"]
				type: "structure"
			}]
			title: "&6村庄"
			x: 11.0d
			y: -0.5d
		}
		{
			dependencies: ["01119871B1D5C576"]
			description: [
				"&6村庄&r的&a中立&f守护者.他会攻击所有&a敌对生物&f,类似&a铁傀儡&f,但样貌莫名眼熟...难道是熟人假扮的？\\n\\n可用&a治疗药水&f和绿宝石交易「卷曲地图」(该物品将用于对抗&a唤魔者要塞&r).\\n\\n若攻击他,会遭到&6神圣魔法&r连续攻击,生命值低下时他还会自我治疗.\\n\\n击败后掉落奥术精华、墨水、&6神圣符文&r和&6神圣卷轴&r."
				""
				"{image:atm:textures/questpics/iron_spells/spells_priest.png width:100 height:200 align:center}"
			]
			icon: "minecraft:golden_apple"
			id: "7F85669FEA41CD97"
			rewards: [{
				count: 3
				id: "061AE7083D11867A"
				item: "irons_spellbooks:holy_rune"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				entity: "irons_spellbooks:priest"
				id: "25BF2D52BEB024B3"
				type: "kill"
				value: 1L
			}]
			title: "&6&l祭司"
			x: 11.5d
			y: -1.5d
		}
		{
			description: ["主世界&2(Overworld)&r有6个会生成Boss的建筑结构!(还有第7个但目前没有Boss)"]
			icon: "minecraft:grass_block"
			id: "3009018DEC1EC952"
			rewards: [{
				id: "2D116FDCEA070CBA"
				item: "minecraft:map"
				type: "item"
			}]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				dimension: "minecraft:overworld"
				id: "08BA6518F2BBDD85"
				type: "dimension"
			}]
			title: "&l冒险时刻!"
			x: 10.0d
			y: 1.0d
		}
		{
			description: ["&c&a地狱&f&r!"]
			icon: "minecraft:netherrack"
			id: "72AB70FD8D8FABBF"
			shape: "octagon"
			size: 1.2d
			tasks: [{
				dimension: "minecraft:the_nether"
				id: "10B368D0D52F377B"
				type: "dimension"
			}]
			title: "通往...的高速公路"
			x: 14.0d
			y: -0.5d
		}
		{
			description: [
				"这些生物几乎能在任何&2原版&r&a敌对生物&f生成的地方出现.它们并非't首领级生物,只是更强的敌人!\\n\\n它们拥有25点&c生命值&r,并具备多种攻击方式:像&a唤魔者&r那样从地面召唤&a尖牙&r,还能发射&b雪球&r攻击你.\\n\\n它们还能召唤僵尸和骷髅协助作战.这些召唤物不会掉落任何物品,且生成时就自带从地面获取的盔甲.\\n\\n死亡时会掉落奥术精华,有时还会掉落&3卷轴&r!"
				""
				"{image:atm:textures/questpics/iron_spells/spells_necromancer.png width:100 height:200 align:center}"
			]
			icon: {
				Count: 1
				id: "irons_spellbooks:tarnished_helmet"
				tag: {
					Damage: 0
				}
			}
			id: "204B969DA081056A"
			rewards: [{
				id: "008A3E406FB09885"
				item: {
					Count: 1
					id: "irons_spellbooks:tarnished_helmet"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			shape: "diamond"
			tasks: [{
				entity: "irons_spellbooks:necromancer"
				id: "1CC0E97BB8DC4F35"
				type: "kill"
				value: 5L
			}]
			title: "死灵法师"
			x: 15.5d
			y: 1.0d
		}
		{
			description: [
				"还有最后一种&3法术&r派系我忘了提及.实际上关于这些&9法术&r的知识都已失传,不只我记不清."
				"\\n\\n要找回记忆,我们需要安静地漫步在回忆长廊——也就是&9远古城市&r."
			]
			icon: "minecraft:sculk_sensor"
			id: "5ED368C926E5F32C"
			rewards: [{
				id: "37DB5B8BEB5F094E"
				item: {
					Count: 1
					id: "minecraft:enchanted_book"
					tag: {
						StoredEnchantments: [{
							id: "minecraft:swift_sneak"
							lvl: 3s
						}]
					}
				}
				type: "item"
			}]
			shape: "octagon"
			size: 1.5d
			tasks: [{
				biome: "minecraft:deep_dark"
				id: "6D7DD99A02A8F451"
				type: "biome"
			}]
			title: "&a被遗忘的&f派系..."
			x: 10.0d
			y: 6.0d
		}
		{
			dependencies: ["5ED368C926E5F32C"]
			description: [
				"你可以在&9远古城市&r的箱子里找到散落的&a古代知识&f碎片."
				"\\n\\n还需要&9&a回响碎片&f&r来:1.拼合碎片学习&9法术&r 2.实际制作&9法术&r"
			]
			hide_details_until_startable: true
			id: "5B013A3A37D6902B"
			rewards: [{
				count: 3
				id: "218DA9B1217C1CBE"
				item: "irons_spellbooks:ancient_knowledge_fragment"
				type: "item"
			}]
			shape: "diamond"
			size: 1.2d
			tasks: [{
				id: "45C1F9BF5C0EAE0A"
				item: "irons_spellbooks:ancient_knowledge_fragment"
				type: "item"
			}]
			title: "深入远古城市"
			x: 10.0d
			y: 8.0d
		}
		{
			dependencies: ["5B013A3A37D6902B"]
			description: [
				"当你最终拼合&a古代知识&f碎片后,将获得...&9玄秘手稿&r!这就是最后的派系!"
				"\\n\\n&9玄秘&r是最强力的终极法术派系,需用&9&a回响碎片&f&r制作,且必须通过&9手稿&r学习.每份&9手稿&r仅能使用一次."
			]
			hide_details_until_startable: true
			id: "2B901ECF97FFA181"
			rewards: [{
				id: "0B7A3F07EDA98210"
				item: "minecraft:echo_shard"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "3D0B1A6B945A87B5"
				item: "irons_spellbooks:eldritch_manuscript"
				type: "item"
			}]
			title: "&9&l玄秘"
			x: 10.0d
			y: 10.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"该任务默认隐藏,若你看到此说明,说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "68A3904EDA48FF76"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "748281267C747571"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "086FD18B34727A13"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: 5.5d
			y: 3.5d
		}
	]
	title: "铁匠法术与法术书"
}
