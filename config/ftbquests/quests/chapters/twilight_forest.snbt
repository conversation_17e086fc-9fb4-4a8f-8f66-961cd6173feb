{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "twilight_forest"
	group: "752CDE464613A1ED"
	icon: "twilightforest:twilight_portal_miniature_structure"
	id: "7732CF7AAA63DB3A"
	order_index: 4
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"欢迎来到&9&d暮色森林&f&r!"
				""
				"要创建通往&d暮色森林&f的传送门,请挖掘2x2的水池并注满水.用花朵环绕水池边缘,然后投入一颗钻石."
				""
				"若操作正确,雷神会给予你提示."
			]
			id: "4193303999597249"
			rewards: [
				{
					id: "1C0B624437947A5B"
					type: "xp"
					xp: 100
				}
				{
					id: "5453ED799AE6ED00"
					item: {
						Count: 1
						id: "minecraft:player_head"
						tag: {
							SkullOwner: {
								Id: [I;
									1223057599
									1414090141
									-1109509241
									1019046394
								]
								Name: "ZephyrWindSpirit"
								Properties: {
									textures: [{
										Signature: "bszJX3BvT6r4197un7fCTLWwbmYvucp9bjel1xHRqbEgw6BUuXWSjvUi2rSORpzP9fiT1fXL1AbDsu9L1/JkTVsqhBdsOZjlTEQDjTLE4WPy1nUobhYGT+mlNJjcQV9mQtOm8TYVr/lRPIu/uKjJPk1Ot/1W7oY0s9wEUxjDCzdBjDfDybaH9x8VQZR2cSgTPo8NXB/zNV+DG/6ylgh3z6FxvP8m7PwFUveouivcMZZGB8mV0Z3cRms4vymPrD+TLGQNLwQ4C4RYgMka4SKDKT2E2JLC3JXXUY/94zG9vsU92FwQ6kfz1h7p9pPQSJdVbdji0qVnAR8tX0KswksRYdY4vnwIFPzpMXfNEm6SroEFodp9FXHZKvPnlDgyVixQU/oIkp+UALXByvSPnFod7TCqhLMW8nSn+96gOTm/TnV95YvY/d7dRIAuNS/hu0dh/ITasE9M0HJ2/OaPH23vVENs4YVh9vdjl9kM6E0LlxgzvTa3H9uCvzt0zh67I8B/hQqyQrGgrTMIE0+B7tYI+e9ZZIfeRJx/NlB6i11CtNZ3iSaycUQ0uUOmZnqhkyvr6tUf0sDTnXvf9aUfVaV4a8Mz4YruQDGmHbJQIwGZQxpt8yp3qqjHx2u+Uh6JdpwXfi4P+qccovq360nfeMVHa3Omy8f8QJuffSIPzIZrdg0="
										Value: "ewogICJ0aW1lc3RhbXAiIDogMTY1Nzk0MjM3Mzc5MiwKICAicHJvZmlsZUlkIiA6ICI0OGU2NjBiZjU0NDk0ZDlkYmRkZTNiODczY2JkNjlmYSIsCiAgInByb2ZpbGVOYW1lIiA6ICJaZXBoeXJXaW5kU3Bpcml0IiwKICAic2lnbmF0dXJlUmVxdWlyZWQiIDogdHJ1ZSwKICAidGV4dHVyZXMiIDogewogICAgIlNLSU4iIDogewogICAgICAidXJsIiA6ICJodHRwOi8vdGV4dHVyZXMubWluZWNyYWZ0Lm5ldC90ZXh0dXJlLzRmNzYyNTNjMTVjMTg1ZTg1NzVjY2FiMDgwN2Y3MWNiZmFlMWJhZGZlOWMwOTM2MGUwNDgxODc2MzZkZGNjNGQiCiAgICB9LAogICAgIkNBUEUiIDogewogICAgICAidXJsIiA6ICJodHRwOi8vdGV4dHVyZXMubWluZWNyYWZ0Lm5ldC90ZXh0dXJlLzIzNDBjMGUwM2RkMjRhMTFiMTVhOGIzM2MyYTdlOWUzMmFiYjIwNTFiMjQ4MWQwYmE3ZGVmZDYzNWNhN2E5MzMiCiAgICB9CiAgfQp9"
									}]
								}
							}
						}
					}
					type: "item"
				}
			]
			shape: "octagon"
			size: 1.5d
			subtitle: "创建传送门"
			tasks: [{
				advancement: "twilightforest:root"
				criterion: ""
				icon: "twilightforest:twilight_portal_miniature_structure"
				id: "695EA135D2B5FDC8"
				type: "advancement"
			}]
			title: "&d暮色森林&f"
			x: -7.5d
			y: 0.0d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"在&d暮色森林&f中,有许多新生物等待发现."
				""
				"其中最烦人的是蝉.建议击杀它来完成成就,不过击杀任何&d暮色森林&f生物都有效."
			]
			hide_until_deps_visible: true
			id: "575E405B270BBCBC"
			rewards: [{
				id: "2D7EEE6D9AB073A0"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				advancement: "twilightforest:twilight_hunter"
				criterion: ""
				icon: "twilightforest:cicada"
				id: "49C77D4CDAE03481"
				title: "&a森林之静&f"
				type: "advancement"
			}]
			x: -7.5d
			y: -1.5d
		}
		{
			dependencies: ["2951B1D7080C5EF9"]
			dependency_requirement: "all_started"
			description: [
				"....尚未实装."
				""
				"作为替代,请前往&a终焉高原&f的巨型城堡获取门方块!"
				""
				"虽然会生成作为\"占位符\"的狗头人,但击杀它不会获得任何物品."
			]
			icon: "twilightforest:castle_brick"
			id: "420158B1736A1354"
			rewards: [{
				id: "3E889B970E03E480"
				type: "xp"
				xp: 100
			}]
			shape: "rsquare"
			tasks: [
				{
					biome: "twilightforest:final_plateau"
					icon: "twilightforest:castle_brick"
					id: "086AAFD8D9F5EDFA"
					title: "进入&a终焉高原&f"
					type: "biome"
				}
				{
					count: 12L
					id: "0BB84A1074D7D214"
					item: "twilightforest:pink_castle_door"
					type: "item"
				}
				{
					count: 12L
					id: "02D55A14580DBDE9"
					item: "twilightforest:yellow_castle_door"
					type: "item"
				}
				{
					count: 12L
					id: "24BB0DB1D0CEE3AE"
					item: "twilightforest:blue_castle_door"
					type: "item"
				}
				{
					count: 12L
					id: "3011B112DB273913"
					item: "twilightforest:violet_castle_door"
					type: "item"
				}
			]
			title: "&a鳍足&f首领"
			x: 9.0d
			y: 5.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"&d暮色森林&f中有多种特色食物可供制作!"
				""
				"尝试所有配方吧!"
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			icon: "minecraft:bowl"
			id: "604F2B68B27885BF"
			optional: true
			rewards: [
				{
					count: 4
					id: "1985FEC75D5FCD1A"
					item: "allthemodium:allthemodium_apple"
					random_bonus: 4
					type: "item"
				}
				{
					id: "2661628F6222794D"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "5D1D9C6EE805BD76"
					item: "twilightforest:raw_venison"
					type: "item"
				}
				{
					id: "70A3CED85106362F"
					item: "twilightforest:cooked_venison"
					type: "item"
				}
				{
					id: "6E028B1374EB57AB"
					item: "twilightforest:maze_wafer"
					type: "item"
				}
				{
					id: "07581F27FE61BBB0"
					item: "twilightforest:cooked_meef"
					type: "item"
				}
				{
					id: "45A70662D54D46F7"
					item: "twilightforest:experiment_115"
					type: "item"
				}
				{
					id: "3039460B9A037CFD"
					item: "twilightforest:hydra_chop"
					type: "item"
				}
				{
					advancement: "twilightforest:twilight_dinner"
					criterion: ""
					id: "01531CEF33729679"
					type: "advancement"
				}
				{
					id: "74B7BA7AA89EEECD"
					item: "twilightforest:torchberries"
					type: "item"
				}
			]
			title: "精致美食"
			x: 6.5d
			y: 3.0d
		}
		{
			dependencies: ["575E405B270BBCBC"]
			dependency_requirement: "one_completed"
			description: [
				"在森林中极易迷路.探险时会遇到黑曜石柱."
				""
				"石柱周围栖息着渡鸦.击杀它们获取羽毛,可用来制作&d暮色森林&f地图!"
			]
			icon: "twilightforest:raven_feather"
			id: "57940981E8DE55D4"
			rewards: [{
				id: "64746E59EAEAAFC2"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6CDB5FB3000FB0AE"
				item: "twilightforest:raven_feather"
				type: "item"
			}]
			title: "&a渡鸦羽毛&f"
			x: -6.5d
			y: -2.5d
		}
		{
			dependencies: ["420158B1736A1354"]
			description: [
				"&d暮色森林&f中的战利品箱藏有稀有树苗."
				""
				"收集全部品种!"
			]
			icon: "twilightforest:time_sapling"
			id: "0ED7B25DC1AA767B"
			rewards: [
				{
					id: "26B95D542E90E46C"
					item: "twilightforest:mining_sapling"
					type: "item"
				}
				{
					id: "083BCDC9A825C003"
					type: "xp"
					xp: 1000
				}
				{
					id: "307ADDA3F89CF02E"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			shape: "gear"
			size: 3.0d
			subtitle: "培育树木"
			tasks: [
				{
					id: "51F100B03CBC2ACF"
					item: "twilightforest:time_sapling"
					type: "item"
				}
				{
					id: "0E97645EAF951F5A"
					item: "twilightforest:sorting_sapling"
					type: "item"
				}
				{
					id: "753E61230A8790AF"
					item: "twilightforest:mining_sapling"
					type: "item"
				}
				{
					id: "63ABA495C733F036"
					item: "twilightforest:transformation_sapling"
					type: "item"
				}
			]
			title: "&a雷亚尔币&f终极首领"
			x: 6.5d
			y: 5.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"暮色冒险的第一个首领位于庭院中."
				""
				"击杀娜迦将解锁下个首领——巫妖的领域."
			]
			hide_until_deps_visible: true
			id: "3531B28F14CF72A2"
			rewards: [
				{
					count: 3
					id: "31C7455838E9B507"
					item: "twilightforest:naga_scale"
					random_bonus: 3
					type: "item"
				}
				{
					id: "742B9B4AA603C30C"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "5D567A4631D5D3A2"
				item: "twilightforest:naga_trophy"
				type: "item"
			}]
			title: "平衡天秤之时"
			x: -6.0d
			y: 0.0d
		}
		{
			dependencies: ["3531B28F14CF72A2"]
			description: [
				"高塔中栖息着强大的巫妖."
				""
				"这是场三阶段战斗,但仅第一阶段具独特性."
				""
				"阶段一:&a巫妖&f用护盾保护自身,并发射&a末影珍珠&f弹幕(类似恶魂火球).将珍珠反弹回去可破坏护盾!随着护盾削弱,他会召唤分身干扰."
				""
				"阶段二:&a巫妖&f切换法杖召唤僵尸助战.此时防御薄弱,可进行近战攻击!"
				""
				"阶段三:当法杖能量耗尽,他将持&a黄金之剑&f进入狂暴状态.速战速决!"
			]
			id: "0107D516E038E0DB"
			rewards: [
				{
					id: "7AFE97BA7DD6FAD1"
					item: "minecraft:golden_apple"
					random_bonus: 2
					type: "item"
				}
				{
					id: "18E6D57791488EB4"
					type: "xp"
					xp: 100
				}
				{
					id: "5C86EA40DE0D0702"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [{
				id: "4FB6BB3F61AD8D6B"
				item: "twilightforest:lich_trophy"
				type: "item"
			}]
			title: "亡灵序曲"
			x: -4.5d
			y: 1.0d
		}
		{
			dependencies: ["4B95D48D7525FFAD"]
			description: [
				"迷宫沼泽深处栖息着巨型蘑菇牛."
				""
				"击败后会掉落&a牛头人沙拉酱肉&f.食用后方可解锁下一区域."
			]
			icon: "twilightforest:minoshroom_trophy"
			id: "04440BB2EFFD6DD9"
			rewards: [
				{
					id: "7DDF1FEFAE5F311F"
					type: "xp"
					xp: 100
				}
				{
					id: "2348B79F8ADCFD49"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [
				{
					id: "2CD3DF9A97F31897"
					item: "twilightforest:minoshroom_trophy"
					type: "item"
				}
				{
					id: "15B1513233BE274B"
					item: "twilightforest:meef_stroganoff"
					type: "item"
				}
			]
			title: "巨力斯特罗加诺夫"
			x: 1.0d
			y: 1.0d
		}
		{
			dependencies: ["04440BB2EFFD6DD9"]
			description: [
				"源自希腊神话的著名多头怪物."
				""
				"远程攻击效果不佳,需要近身作战."
				""
				"击败后可前往&a黑森林&f寻找下个首领."
			]
			id: "7026E46FD8B3A81D"
			rewards: [
				{
					id: "185A446AAFA6CAD6"
					type: "xp"
					xp: 100
				}
				{
					id: "55F90DB2EEEEE064"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			subtitle: "烈焰沼泽"
			tasks: [{
				id: "6D9D134621F8FA36"
				item: "twilightforest:hydra_trophy"
				type: "item"
			}]
			title: "&a铲除祸害&f"
			x: 2.5d
			y: -0.5d
		}
		{
			dependencies: ["7026E46FD8B3A81D"]
			description: [
				"在&a黑森林&f深处,你会发现通往地下的建筑结构."
				""
				"要进入其中,你需要将获得的战利品放置在附近的基座上."
				""
				"在第三层,你会遭遇&a幻影骑士&f.击败这些骑士将&a解锁&f下一个首领."
			]
			id: "3DCF26B53AE1EBF6"
			rewards: [
				{
					id: "2FDFDEDC63DBFACE"
					type: "xp"
					xp: 100
				}
				{
					id: "0432C721D444184B"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			subtitle: "&a黑木&f森林之宴"
			tasks: [{
				id: "4E03E4FCB1B4DE05"
				item: "twilightforest:knight_phantom_trophy"
				type: "item"
			}]
			title: "&a进入&f黑森林&f"
			x: 4.0d
			y: -2.0d
		}
		{
			dependencies: ["3DCF26B53AE1EBF6"]
			description: [
				"在&a黑森林&f中,你会找到&a幽冥高塔&f."
				""
				"要进入高塔,需寻找底部周期性出现的方块.穿过迷宫直达顶层与远古恶魂战斗."
				""
				"建议使用远程武器击杀&a暮初恶魂&f.首领层设有4个恶魂陷阱,可用来削弱远古恶魂."
				""
				"这些陷阱通过击杀恶魂幼体充能,用红石激活.虽非必须使用,但能提供战术优势."
			]
			id: "688C911ECFB2F134"
			rewards: [
				{
					id: "18258B0937EC3D75"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "30312A819C8EB06B"
					item: "twilightforest:carminite"
					random_bonus: 2
					type: "item"
				}
				{
					id: "436197AB331DD880"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			subtitle: "暗黑卡莫尼特高塔"
			tasks: [{
				id: "64B931AE43EA1E92"
				item: "twilightforest:ur_ghast_trophy"
				type: "item"
			}]
			title: "&a炽厄之泪&f"
			x: 6.5d
			y: -2.0d
		}
		{
			dependencies: ["688C911ECFB2F134"]
			description: [
				"击败远古恶魂后,将解锁&a积雪森林&f生态群系."
				""
				"这里有许多生物需要战斗,但要推进进度必须击杀&a雪怪首领&f."
				""
				"你可以在巨大的雪人洞穴中找到&a雪怪首领&f,击败它以继续冒险."
				""
			]
			id: "31BB7EB95CE73C1A"
			rewards: [
				{
					id: "4A0F85570A9BB08F"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "77A0EDF91EEB2786"
					item: "twilightforest:alpha_yeti_fur"
					random_bonus: 2
					type: "item"
				}
				{
					id: "23174C24D051B80E"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [{
				id: "45C67776B86B2301"
				item: "twilightforest:alpha_yeti_fur"
				type: "item"
			}]
			title: "前往雪原生态群系!"
			x: 9.0d
			y: -2.0d
		}
		{
			dependencies: ["31BB7EB95CE73C1A"]
			description: [
				"击败&a雪怪首领&f将解锁冰川生态群系.这里栖息着可爱的企鹅和&a冰雪女王&f."
				""
				"在&a极光宫殿&f顶端,&a冰雪女王&f会召唤冰晶保护自己."
				""
				"她还会砸落冰方块破坏地面并造成巨额伤害."
				""
				"由于下半身被冰方块保护,你只能攻击她的上半身."
				""
				"击败&a冰雪女王&f后,将解锁高地地区的访问权限."
			]
			id: "6FD41DF7704466A4"
			rewards: [
				{
					id: "7C737743505C71EA"
					type: "xp"
					xp: 100
				}
				{
					id: "21B903DE9EB6367F"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [{
				id: "1997CE8D805B6EB6"
				item: "twilightforest:snow_queen_trophy"
				type: "item"
			}]
			title: "&a召唤晴空&f"
			x: 10.5d
			y: -0.5d
		}
		{
			dependencies: ["20436AFCC7E6855D"]
			description: [
				"获得巨型镐后,你需要返回&a巨魔洞穴&f寻找&a巨型黑曜石&f."
				""
				"用巨型镐开采它会获得战利品.你需要取得&a余烬之灯&f才能继续前进."
				""
				""
			]
			icon: {
				Count: 1
				id: "twilightforest:lamp_of_cinders"
				tag: {
					Damage: 0
				}
			}
			id: "5CBA8C89FE717B9C"
			rewards: [
				{
					id: "297971829C1A7D58"
					item: {
						Count: 1
						id: "minecraft:potion"
						tag: {
							Potion: "minecraft:healing"
						}
					}
					type: "item"
				}
				{
					id: "022835C754F54A11"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			tasks: [{
				id: "54FEB8B303070725"
				item: {
					Count: 1
					id: "twilightforest:lamp_of_cinders"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 12.0d
			y: 4.5d
		}
		{
			dependencies: ["58BD1063A19777DC"]
			description: [
				"带着你的&a魔豆&f和&a肥沃泥土&f,在高地生态群系寻找大型云朵."
				""
				"在土壤中种植魔豆会长出通天藤蔓.攀爬至云端后,你将见到巨人族."
				""
				"必须击杀&a矿工巨人&f并获取他们的镐才能继续旅程."
			]
			icon: {
				Count: 1
				id: "twilightforest:giant_pickaxe"
				tag: {
					Damage: 0
				}
			}
			id: "20436AFCC7E6855D"
			rewards: [
				{
					id: "57BD859AC0E3617B"
					type: "xp"
					xp: 100
				}
				{
					id: "10163664BE8E155F"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			shape: "pentagon"
			subtitle: "那些巨人形似于我,却与我截然不同"
			tasks: [{
				id: "671EEE3BD0D051FD"
				item: {
					Count: 1
					id: "twilightforest:giant_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 12.0d
			y: 2.6d
		}
		{
			dependencies: ["3531B28F14CF72A2"]
			description: ["用娜迦鳞片可以制作这套护甲.虽防御力一般,但外观精美."]
			id: "4D4AB60B3B1CD437"
			rewards: [
				{
					id: "481E680F0F18B148"
					type: "xp"
					xp: 100
				}
				{
					id: "7AFB735476E84420"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [
				{
					id: "529D27B9675CBD7F"
					item: {
						Count: 1
						id: "twilightforest:naga_chestplate"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:fire_protection"
								lvl: 3s
							}]
						}
					}
					type: "item"
				}
				{
					id: "3B252125FD56FC27"
					item: {
						Count: 1
						id: "twilightforest:naga_leggings"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:protection"
								lvl: 3s
							}]
						}
					}
					type: "item"
				}
			]
			title: "娜迦&a鳞板装甲&f"
			x: -6.0d
			y: -1.0d
		}
		{
			dependencies: ["0107D516E038E0DB"]
			description: [
				"想像巫妖一样发射末影爆弹吗？这根权杖能满足你."
				""
				"在工作台中与&a末影珍珠&f合成即可充能."
			]
			id: "212EC1F41227184D"
			rewards: [
				{
					id: "45E2D1CD5953608A"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "20567937363CD1F1"
					item: "minecraft:ender_pearl"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "54F8AB4DDD68C94A"
				item: {
					Count: 1
					id: "twilightforest:twilight_scepter"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -5.0d
			y: 2.5d
		}
		{
			dependencies: ["0107D516E038E0DB"]
			description: [
				"使用这根权杖可以汲取敌人的生命力!"
				""
				"在工作台中与发酵蛛眼合成即可为权杖充能."
			]
			id: "6CB1BFBA10DF24E4"
			rewards: [
				{
					id: "0AB5BCBB96B2F409"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "3970CC74E6AA5566"
					item: "minecraft:fermented_spider_eye"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "4FBA38F3FB4B7C28"
				item: {
					Count: 1
					id: "twilightforest:lifedrain_scepter"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -5.5d
			y: 2.0d
		}
		{
			dependencies: ["0107D516E038E0DB"]
			description: [
				"谁不想召唤属于自己的僵尸大军呢？"
				""
				"在工作台中与腐肉合成即可充能."
			]
			id: "3908F7C80154D9CA"
			rewards: [
				{
					id: "65B006EC088F5773"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "02A569F306882648"
					item: "minecraft:rotten_flesh"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3146C0D222FADF31"
				item: {
					Count: 1
					id: "twilightforest:zombie_scepter"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -3.5d
			y: 2.0d
		}
		{
			dependencies: ["0107D516E038E0DB"]
			description: [
				"这根权杖能召唤护盾环绕保护使用者."
				""
				"在工作台中与&a金苹果&f合成即可充能."
			]
			id: "3371570F189DF994"
			rewards: [
				{
					id: "37272146953406F6"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "5557BEAAA052774B"
					item: "minecraft:golden_apple"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "3CCC28F4BEAAC162"
				item: {
					Count: 1
					id: "twilightforest:fortification_scepter"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -4.0d
			y: 2.5d
		}
		{
			dependencies: ["0107D516E038E0DB"]
			description: [
				"是时候前往沼泽了!在沼泽中,你会发现一座顶部有入口的古怪山丘.这就是蘑菇牛迷宫!"
				""
				"在迷宫里,你将迎战数种新敌人,它们会掉落&a迷宫地图核心&f.这是制作&a迷宫地图&f的必要材料."
				""
				"这张特殊地图能为你标注蘑菇牛迷宫的路径.在这里,你的小地图模组将失去作用."
				""
				"你还能发现多个藏宝室,里面放着专为迷宫准备的特殊战利品."
			]
			icon: "twilightforest:maze_map"
			id: "4B95D48D7525FFAD"
			rewards: [{
				id: "0EBC5DE2494689BB"
				type: "xp"
				xp: 100
			}]
			subtitle: "不知道能不能遇见史莱克"
			tasks: [
				{
					id: "7AA9D1C39C51F20F"
					item: "twilightforest:maze_map"
					type: "item"
				}
				{
					count: 3L
					id: "1779F067AD7CDA50"
					item: "twilightforest:raw_meef"
					type: "item"
				}
			]
			title: "前往沼泽!"
			x: -1.5d
			y: 1.0d
		}
		{
			dependencies: ["4F66DF6B494BEFF3"]
			dependency_requirement: "one_completed"
			description: [
				"这张地图是探索&d暮色森林&f的必备物品."
				""
				"使用&a空白魔法地图&f可以生成显示附近BOSS和建筑图标的地图."
			]
			id: "0990D6CEE042F44E"
			optional: true
			rewards: [{
				id: "6D7C21A48CD96B58"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "0EBD4A2BF8DE117D"
				item: "twilightforest:magic_map"
				type: "item"
			}]
			x: -4.0d
			y: -1.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"这套护甲比娜迦护甲稍强,且自带全套附魔."
				""
				"通过在&d暮色森林&f中寻找&e钢叶&r即可制作."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "2BEBF66D7EA594FA"
			rewards: [
				{
					id: "5DF56C2A6538CF49"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "4DC6E4E7C6FFB69D"
					item: "twilightforest:steeleaf_ingot"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "41286D909D0A6867"
					item: {
						Count: 1
						id: "twilightforest:steeleaf_helmet"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:projectile_protection"
								lvl: 2s
							}]
						}
					}
					type: "item"
				}
				{
					id: "569238D13FE4BEDE"
					item: {
						Count: 1
						id: "twilightforest:steeleaf_chestplate"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:blast_protection"
								lvl: 2s
							}]
						}
					}
					type: "item"
				}
				{
					id: "465948D752178FD8"
					item: {
						Count: 1
						id: "twilightforest:steeleaf_leggings"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:fire_protection"
								lvl: 2s
							}]
						}
					}
					type: "item"
				}
				{
					id: "29B138097231BC9F"
					item: {
						Count: 1
						id: "twilightforest:steeleaf_boots"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:feather_falling"
								lvl: 2s
							}]
						}
					}
					type: "item"
				}
			]
			title: "钢叶护甲"
			x: 8.0d
			y: 1.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"使用&9铁木&r即可打造这套护甲."
				""
				"该护甲同样自带自动附魔效果."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "40258842B0359A2D"
			rewards: [
				{
					id: "3F15327EC89501A3"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "58261ADFF3E4DB51"
					item: "twilightforest:raw_ironwood"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "5B86279AE9E62F55"
					item: {
						Count: 1
						id: "twilightforest:ironwood_helmet"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:aqua_affinity"
								lvl: 1s
							}]
						}
					}
					type: "item"
				}
				{
					id: "60033D60F21A2145"
					item: {
						Count: 1
						id: "twilightforest:ironwood_chestplate"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:protection"
								lvl: 1s
							}]
						}
					}
					type: "item"
				}
				{
					id: "48D0CAE437234AC6"
					item: {
						Count: 1
						id: "twilightforest:ironwood_leggings"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:protection"
								lvl: 1s
							}]
						}
					}
					type: "item"
				}
				{
					id: "1D94DFBBCD46D84B"
					item: {
						Count: 1
						id: "twilightforest:ironwood_boots"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:feather_falling"
								lvl: 1s
							}]
						}
					}
					type: "item"
				}
			]
			title: "铁木护甲"
			x: 5.0d
			y: 1.5d
		}
		{
			dependencies: ["7026E46FD8B3A81D"]
			description: [
				"九头蛇掉落的炽焰之血可用于打造炽焰护甲."
				""
				"当穿戴全套时,攻击你的敌人将被点燃10秒."
			]
			id: "1FF5906DF721D091"
			rewards: [
				{
					id: "645C3DCEFD53C822"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "107B2D81CF1B63E2"
					item: "twilightforest:fiery_ingot"
					random_bonus: 2
					type: "item"
				}
				{
					id: "3BB1CF44BD9DF7B4"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [
				{
					id: "48F5F5BE01C792CE"
					item: {
						Count: 1
						id: "twilightforest:fiery_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "761CF4D721CB22DF"
					item: {
						Count: 1
						id: "twilightforest:fiery_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "384D339F85C2F6CC"
					item: {
						Count: 1
						id: "twilightforest:fiery_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "1559426B94209449"
					item: {
						Count: 1
						id: "twilightforest:fiery_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "炽焰护甲"
			x: 1.5d
			y: -0.5d
		}
		{
			dependencies: ["3DCF26B53AE1EBF6"]
			description: ["可能在幻影骑士的宝箱中找到."]
			id: "0A207A437AF153AA"
			rewards: [
				{
					id: "1BA06461A6CFA2A8"
					type: "xp"
					xp: 100
				}
				{
					id: "269E12B24D6C62AD"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "2429F7C568231ED7"
					item: {
						Count: 1
						id: "twilightforest:phantom_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "0C2C788F3061A7D3"
					item: {
						Count: 1
						id: "twilightforest:phantom_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			x: 4.0d
			y: -3.5d
		}
		{
			dependencies: ["3DCF26B53AE1EBF6"]
			description: ["可合成或在幻影骑士的宝箱中发现."]
			id: "25906B43A198B72F"
			rewards: [
				{
					id: "73E3E17874A35218"
					type: "xp"
					xp: 100
				}
				{
					id: "7F25EEC7FA00F6A8"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "095092712EA93AD4"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "44E7089F08F757D4"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "1A137AF836AFC3C0"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "33789178B086D262"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "骑士金属护甲"
			x: 3.5d
			y: -3.0d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"寻找&a谜题羊&f,它会向献上&a彩虹羊毛&f(16种颜色)的玩家赐予财富."
				""
				"提示:在谜题羊所在的遗迹中,你头顶上方有个发射器,或许能派上用场."
			]
			hide_until_deps_visible: true
			id: "4DA0725E089D7C91"
			optional: true
			rewards: [{
				id: "1BF594BADCE267FA"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "0DC327E6F70EE1C1"
				item: "twilightforest:quest_ram_trophy"
				type: "item"
			}]
			title: "谜题羊"
			x: -7.5d
			y: 1.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"这个一次性物品能避免死亡.当濒死时,护符会被消耗并给予短暂的生命恢复效果."
				""
				"可在战利品箱中找到."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "6F67079A453DAABE"
			rewards: [
				{
					id: "0109DCAA3BD0CA82"
					type: "xp"
					xp: 100
				}
				{
					id: "2C507B67069584C5"
					item: "twilightforest:charm_of_life_1"
					type: "item"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5F7D02D164E85469"
				item: "twilightforest:charm_of_life_1"
				type: "item"
			}]
			x: 6.5d
			y: 0.0d
		}
		{
			dependencies: ["6F67079A453DAABE"]
			description: ["与&a生命符咒 I&f类似,此物品通过消耗来避免死亡.使用时将恢复全部生命值,并获得30秒的恢复IV、抗性提升和&a火焰保护&f效果."]
			hide_until_deps_visible: true
			id: "15006CF73F8CAB7C"
			rewards: [
				{
					id: "3D2DA73A93E0D143"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 2
					id: "3030EE09DC532C90"
					item: "twilightforest:charm_of_life_1"
					type: "item"
				}
			]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "1AF2A8D21E2C64FE"
				item: "twilightforest:charm_of_life_2"
				type: "item"
			}]
			x: 6.5d
			y: 1.5d
		}
		{
			dependencies: ["4B95D48D7525FFAD"]
			description: ["此物品能防止你死亡时丢失主副手物品及护甲."]
			id: "610F9E9D0B5131C7"
			rewards: [{
				id: "4CA93BA5E85DAA73"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "69F49433EDA8F189"
				item: "twilightforest:charm_of_keeping_1"
				type: "item"
			}]
			x: -2.0d
			y: 0.10000000000000003d
		}
		{
			dependencies: ["610F9E9D0B5131C7"]
			description: ["此物品能让你死亡时保留护甲和快捷栏物品."]
			id: "4665E6FD0AAED164"
			rewards: [{
				id: "5BAC92C56DB3B58E"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "146FE418E4077B1D"
				item: "twilightforest:charm_of_keeping_2"
				type: "item"
			}]
			x: -1.0d
			y: 0.10000000000000003d
		}
		{
			dependencies: ["4665E6FD0AAED164"]
			description: ["此物品能让你死亡时保留背包内所有物品."]
			id: "3A3ED88027331A6C"
			rewards: [
				{
					id: "757D67F1402CEA6E"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					id: "62FB99FCB11C009F"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			shape: "diamond"
			size: 1.25d
			tasks: [{
				id: "2FB00B8498F8E8E3"
				item: "twilightforest:charm_of_keeping_3"
				type: "item"
			}]
			x: -1.5d
			y: -0.5d
		}
		{
			dependencies: ["4193303999597249"]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "1464D45C474464DB"
			rewards: [{
				id: "25991402B38AFA2A"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			subtitle: "豪华版吹叶机"
			tasks: [{
				id: "13C31B864EC1DE9B"
				item: {
					Count: 1
					id: "twilightforest:peacock_feather_fan"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 6.0d
			y: 2.5d
		}
		{
			dependencies: ["31BB7EB95CE73C1A"]
			description: ["用&a雪怪首领毛皮&f打造."]
			id: "3C8724C3A9459507"
			rewards: [
				{
					id: "5BA6C83E9E634D86"
					type: "xp"
					xp: 100
				}
				{
					count: 2
					id: "30EEAB06F29DF219"
					item: "twilightforest:alpha_yeti_fur"
					random_bonus: 2
					type: "item"
				}
				{
					id: "0151BD8A11801463"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [
				{
					id: "45D0FCAC69264C9B"
					item: {
						Count: 1
						id: "twilightforest:yeti_helmet"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:protection"
								lvl: 2s
							}]
						}
					}
					type: "item"
				}
				{
					id: "4F6C2CBFE11B14D2"
					item: {
						Count: 1
						id: "twilightforest:yeti_chestplate"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:protection"
								lvl: 2s
							}]
						}
					}
					type: "item"
				}
				{
					id: "1D7E23359B11FB50"
					item: {
						Count: 1
						id: "twilightforest:yeti_leggings"
						tag: {
							Damage: 0
							Enchantments: [{
								id: "minecraft:protection"
								lvl: 2s
							}]
						}
					}
					type: "item"
				}
				{
					id: "34E0A02434E3B41C"
					item: {
						Count: 1
						id: "twilightforest:yeti_boots"
						tag: {
							Damage: 0
							Enchantments: [
								{
									id: "minecraft:protection"
									lvl: 2s
								}
								{
									id: "minecraft:feather_falling"
									lvl: 4s
								}
							]
						}
					}
					type: "item"
				}
			]
			title: "雪怪护甲"
			x: 9.5d
			y: -3.0d
		}
		{
			dependencies: ["31BB7EB95CE73C1A"]
			description: ["小雪怪和寒冬狼掉落的毛皮可制作此护甲."]
			id: "2A0B3C91D72E8B75"
			rewards: [
				{
					id: "1561C0CA37248A7C"
					type: "xp"
					xp: 100
				}
				{
					id: "3DE875692CDA73EC"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			subtitle: "可染色!"
			tasks: [
				{
					id: "74DE768958A1DF0E"
					item: {
						Count: 1
						id: "twilightforest:arctic_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "28805490ED9B650D"
					item: {
						Count: 1
						id: "twilightforest:arctic_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "7E45C2C500C51BCB"
					item: {
						Count: 1
						id: "twilightforest:arctic_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "09422C5CD2A93FDD"
					item: {
						Count: 1
						id: "twilightforest:arctic_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "极地护甲"
			x: 8.5d
			y: -3.0d
		}
		{
			dependencies: ["4B95D48D7525FFAD"]
			description: [
				"迷宫中罕见掉落的特殊镐子."
				""
				"破坏迷宫墙壁时仅消耗1点耐久,而其他镐子会消耗16点!"
			]
			id: "51BC981AB4CFAD95"
			rewards: [{
				id: "43613B9B3AED5AEC"
				type: "xp"
				xp: 100
			}]
			shape: "hexagon"
			subtitle: "密室陷阱,禁止入内？"
			tasks: [{
				id: "37FB4455E15C55FF"
				item: {
					Count: 1
					id: "twilightforest:mazebreaker_pickaxe"
					tag: {
						Damage: 0
						Enchantments: [
							{
								id: "minecraft:efficiency"
								lvl: 4s
							}
							{
								id: "minecraft:unbreaking"
								lvl: 3s
							}
							{
								id: "minecraft:fortune"
								lvl: 2s
							}
						]
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 2.0d
		}
		{
			dependencies: ["6FD41DF7704466A4"]
			description: ["由&a冰雪女王&f掉落,这把弓能同时射出3支箭,但只消耗1支箭."]
			id: "53A79338994088FD"
			rewards: [{
				id: "4998E36C6161772B"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "64D192E45B9E8E6C"
				item: {
					Count: 1
					id: "twilightforest:triple_bow"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 11.5d
			y: -0.5d
		}
		{
			dependencies: ["6FD41DF7704466A4"]
			description: ["由&a冰雪女王&f掉落,这把弓射出的箭会自动追踪目标,百发百中!"]
			id: "7509E4093010EA4C"
			rewards: [{
				id: "61308BB9F304D0E4"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "5423C2517A5D133B"
				item: {
					Count: 1
					id: "twilightforest:seeker_bow"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 10.5d
			y: -1.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: ["随机出现在战利品箱中,这把弓命中后会施加10秒的缓慢III效果."]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "49EE6D2A3684A510"
			rewards: [{
				id: "6BCB7D180D92A1AB"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "52E28B85293DE45A"
				item: {
					Count: 1
					id: "twilightforest:ice_bow"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 7.5d
			y: 1.0d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"这把弓罕见地出现在&a极光宫殿&f中."
				""
				"当用此弓击中敌人时,你们会互换位置.注意别把天上的东西射下来!"
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "3D29EF7F150F5334"
			rewards: [{
				id: "3051ABF9B41E8CDA"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "37A1612D95C34DF2"
				item: {
					Count: 1
					id: "twilightforest:ender_bow"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 5.5d
			y: 1.0d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"能吸引所有名称含'矿石'物品的磁铁(煤炭除外)."
				""
				"可在空心山宝箱中找到."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "5FE4DAE8F41B1437"
			rewards: [{
				id: "73C722B92E712713"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "7B5C5A02D22359AF"
				item: {
					Count: 1
					id: "twilightforest:ore_magnet"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 5.5d
			y: 2.0d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"造成40颗心伤害的剑,但仅有1点耐久度."
				""
				"若想长期使用,可将其设为不可破坏."
				""
				"这些罕见地出现在&a极光宫殿&f的战利品箱中."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "38ADDF7FF4E4892D"
			rewards: [{
				id: "0FFBA24EB9CD17E3"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "68A6B72FD1DF6A75"
				item: {
					Count: 1
					id: "twilightforest:glass_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 6.0d
			y: 0.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: ["在&a极光宫殿&f发现的这把剑,攻击时会使敌人获得10秒冰冻效果."]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "78CE2ECEF0B651DA"
			rewards: [{
				id: "1EDFE99AD36463BB"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "2E37E73B5297E0F7"
				item: {
					Count: 1
					id: "twilightforest:ice_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 7.0d
			y: 0.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"此物品类似火炬发射器.向目标方块发射&e月光蠕虫&r,其照明效果与火把相似."
				""
				"可在部分空心山和&a巫妖怪塔&f的宝箱中找到."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "60FC2DAEA954A849"
			rewards: [{
				id: "13A5729B3DF5AEAA"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "0BE7DD735B1FB717"
				item: {
					Count: 1
					id: "twilightforest:moonworm_queen"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 7.0d
			y: 2.5d
		}
		{
			dependencies: ["4193303999597249"]
			description: [
				"想让主世界更像&d暮色森林&f吗？"
				""
				"将此粉末用于主世界生物,可将其转化为&d暮色森林&f变种."
				""
				"可在&d暮色森林&f地牢宝箱中找到."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "0E9DE6293DF611E1"
			rewards: [{
				id: "585C6465B69B2781"
				type: "xp"
				xp: 100
			}]
			shape: "diamond"
			tasks: [{
				id: "220F1E09B54E7ECA"
				item: "twilightforest:transformation_powder"
				type: "item"
			}]
			x: 7.5d
			y: 2.0d
		}
		{
			dependencies: ["4B95D48D7525FFAD"]
			id: "6F957D07AA74F16E"
			optional: true
			shape: "hexagon"
			tasks: [{
				id: "19F17291B47DC7B0"
				item: "twilightforest:ore_map"
				type: "item"
			}]
			x: -2.0d
			y: 2.0d
		}
		{
			dependencies: ["57940981E8DE55D4"]
			description: ["将&a渡鸦羽毛&f与火炬浆果、荧石组合可制成&a魔法地图核心&f."]
			icon: "twilightforest:magic_map_focus"
			id: "4F66DF6B494BEFF3"
			rewards: [
				{
					count: 4
					id: "104344E0925D3B1D"
					item: "twilightforest:torchberries"
					type: "item"
				}
				{
					id: "6B25E3FA48BA4307"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "6C3B980F96579633"
				item: "twilightforest:magic_map_focus"
				type: "item"
			}]
			x: -5.0d
			y: -2.5d
		}
		{
			dependencies: ["7026E46FD8B3A81D"]
			description: [
				"使用九头蛇掉落的炽焰之血可制作多种工具."
				""
				"打造的&a炽焰剑&f自带&a火焰附加&f II效果."
				""
				"&a炽焰镐&f具备自动熔炼功能."
			]
			id: "111F2EE85FB0A455"
			rewards: [
				{
					count: 2
					id: "22A4DADBE90045AA"
					item: "twilightforest:fiery_ingot"
					random_bonus: 2
					type: "item"
				}
				{
					id: "00F7086DDD7126F7"
					type: "xp"
					xp: 100
				}
				{
					id: "7A5E43387F7110E9"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			tasks: [
				{
					id: "1ACA91C37E113066"
					item: {
						Count: 1
						id: "twilightforest:fiery_sword"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "5FA41AF43B32B189"
					item: {
						Count: 1
						id: "twilightforest:fiery_pickaxe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			x: 2.5d
			y: -1.5d
		}
		{
			dependencies: ["04440BB2EFFD6DD9"]
			description: ["这是蘑菇牛的掉落物.冲刺时能造成更高伤害."]
			id: "730AF9210F00018E"
			rewards: [{
				id: "5D4F5E0EFB90BB72"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				id: "63527819961CF742"
				item: {
					Count: 1
					id: "twilightforest:diamond_minotaur_axe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.0d
			y: 2.0d
		}
		{
			dependencies: ["3DCF26B53AE1EBF6"]
			id: "607D592CE102C82E"
			rewards: [
				{
					count: 4
					id: "56325FCB249895FD"
					item: "twilightforest:knightmetal_ingot"
					random_bonus: 4
					type: "item"
				}
				{
					id: "343843C0B976060B"
					type: "xp"
					xp: 100
				}
				{
					id: "13FA9172DE9942D6"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "036BA11CFAF4A837"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_sword"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "48F928D65370BC9B"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_pickaxe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "2CC995B62A954D96"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_axe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "141CFE131162447F"
					item: {
						Count: 1
						id: "twilightforest:block_and_chain"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "2C4EE242D93E2F75"
					item: {
						Count: 1
						id: "twilightforest:knightmetal_shield"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "骑士金属工具"
			x: 4.5d
			y: -3.0d
		}
		{
			dependencies: ["688C911ECFB2F134"]
			description: [
				"&9重现方块&r如同炫酷的门.右键点击后会短暂消失."
				""
				"&e消逝方块&r被点击后会永久消失."
			]
			id: "01748C2CD9C97523"
			rewards: [
				{
					count: 4
					id: "49D390979C866DBB"
					item: "twilightforest:reappearing_block"
					type: "item"
				}
				{
					count: 4
					id: "568FFBEBF78BF845"
					item: "twilightforest:vanishing_block"
					type: "item"
				}
				{
					id: "1FEC203D4CA7DBC9"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "6F4BFD3CF4F593DE"
					item: "twilightforest:reappearing_block"
					type: "item"
				}
				{
					id: "543D6787030477B0"
					item: "twilightforest:vanishing_block"
					type: "item"
				}
			]
			x: 7.0d
			y: -3.0d
		}
		{
			dependencies: ["688C911ECFB2F134"]
			description: [
				"&9&a砷铅铁建造器&f&r在接收红石信号时,会沿信号方向生成临时方块."
				""
				"&e&a砷铅铁反应堆&f&r能将附近的黑曜石和下界岩转化为&a伪金块&f和&a伪钻石块&f.短暂运行后会吸收周边方块并爆炸,生成卡米尼特恶魂幼体."
			]
			id: "7B4A687EB505C2FF"
			rewards: [
				{
					count: 2
					id: "7C7EBDD4A84D118C"
					item: "twilightforest:carminite"
					random_bonus: 2
					type: "item"
				}
				{
					id: "52FC60BE94B415E3"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "56287EDCE7111CDA"
					item: "twilightforest:carminite_builder"
					type: "item"
				}
				{
					id: "3777E5BC42A44D9A"
					item: "twilightforest:carminite_reactor"
					type: "item"
				}
			]
			x: 6.0d
			y: -3.0d
		}
		{
			dependencies: ["6FD41DF7704466A4"]
			description: [
				"解锁高地生态域后,前往击杀洞穴巨人."
				""
				"它们可能掉落&9&a魔豆&f&r.还能找到装有乌伯鲁斯土壤的宝箱,这是种植魔豆的必需品."
			]
			id: "58BD1063A19777DC"
			rewards: [
				{
					count: 2
					id: "7DBCA6F6A6C2F6CD"
					item: "twilightforest:uberous_soil"
					type: "item"
				}
				{
					id: "46F74B4005D736B3"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "03833E1286B5C7BC"
					item: "twilightforest:magic_beans"
					type: "item"
				}
				{
					id: "21A2013F7DDAE1B6"
					item: "twilightforest:uberous_soil"
					type: "item"
				}
			]
			title: "拜访巨人"
			x: 12.0d
			y: 1.0d
		}
		{
			dependencies: ["5CBA8C89FE717B9C"]
			description: [
				"使用&a余烬之灯&f可破坏荆棘之地生态域的荆棘丛."
				""
				"收集&a荆棘玫瑰&f以进入&a终焉高原&f."
			]
			id: "2951B1D7080C5EF9"
			rewards: [
				{
					id: "52EB72698EB29FF9"
					item: "minecraft:diamond_block"
					type: "item"
				}
				{
					id: "3A33D234FEAA59C6"
					type: "xp"
					xp: 100
				}
				{
					id: "0A2372BA1C0E172A"
					table_id: 5351477636770726245L
					type: "random"
				}
			]
			shape: "heart"
			tasks: [{
				count: 12L
				id: "5F0A10AAC2220CFF"
				item: "twilightforest:thorn_rose"
				type: "item"
			}]
			title: "带刺的玫瑰"
			x: 10.5d
			y: 5.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"根据&eAllTheMods&r整合包采用'保留所有权利'的授权协议,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若您看到此提示,说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "1A2AE8A2F20D2FA6"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "06FC3AA435F90AF6"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "2F0F73982BB388C5"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -9.0d
			y: 0.0d
		}
	]
	title: "&d暮色森林&f"
}
