{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "welcome"
	group: ""
	icon: "allthetweaks:atm_star"
	id: "5B00676D79306EA2"
	images: [
		{
			corner: true
			height: 5.0d
			image: "packmenu:textures/gui/logo.png"
			rotation: 8.0d
			width: 5.0d
			x: 0.0d
			y: -4.5d
		}
		{
			click: "https://discord.gg/3paFjuRfz9"
			height: 1.0d
			hover: ["加入Discord社区!"]
			image: "ftbchunks:textures/waypoint_default.png"
			rotation: 0.0d
			width: 1.0d
			x: 0.0d
			y: 1.5d
		}
	]
	order_index: 0
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"ATM9是开放式整合包,让您以自己方式探索模组世界!"
				""
				"如有疑问欢迎加入ATM官方Discord!"
			]
			icon: "allthetweaks:atm_star"
			id: "3BC0A50886A3222B"
			rewards: [{
				id: "4956CB79E44FB523"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			size: 3.0d
			tasks: [{
				id: "5A1784C5676CDC62"
				title: "&d欢迎来到All&a模组&f9!"
				type: "checkmark"
			}]
			x: 0.0d
			y: -0.5d
		}
		{
			description: [
				"在本整合包中,任务系统为可选内容.模组内容不会因未完成任务而受限!"
				""
				"主线任务外的独立任务线旨在作为模组指南.若想遵循发展路线,请务必查看主线任务!"
			]
			icon: "ftbquests:book"
			id: "3708A4780ACEB34E"
			min_width: 250
			rewards: [{
				id: "4A56DF6E0204666C"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "52BB142F044075B4"
				title: "任务系统"
				type: "checkmark"
			}]
			x: 2.5d
			y: -0.5d
		}
		{
			description: ["ATM整合包内包含许多实用指令,以下是部分示例:\\n\\n&e/sethome&r (家名称) | 设置可通过/home (名称)传送的据点.例如:/sethome farm → 用/home farm传送\\n\\n&e/spawn&r | 传送至主世界出生点\\n\\n&e/rtp&r | '&a随机传送&f'会将你传送到世界随机位置\\n\\n注意:这些指令存在冷却时间和使用限制.如需修改可编辑配置文件:\\n\\n- 单人模式 | &osaves/(存档名)/serverconfig/ftbessentials.snbt&r\\n\\n- &a服务器端&f | &oworld/serverconfig/ftbessentials.snbt"]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "ftbteams:textures/settings.png"
				}
			}
			id: "7658C1C663394E85"
			min_width: 350
			rewards: [{
				id: "5007C73E170C4FF6"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "10A16F89D4AD238D"
				title: "实用指令"
				type: "checkmark"
			}]
			x: -2.5d
			y: -0.5d
		}
		{
			description: [
				"领地声明操作:按&eM&r键打开地图,点击左上角&a领地地图&r图标"
				""
				"声明区块方式:左键拖动选择区域"
				""
				"强制加载区块:按住Shift+左键点击区块.操作成功时区块会显示网格线"
			]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "ftbchunks:textures/waypoint_home.png"
				}
			}
			id: "0C93D7A607AB8B83"
			rewards: [{
				id: "1B044C35466AFAEF"
				type: "xp"
				xp: 10
			}]
			size: 1.5d
			tasks: [{
				id: "103C42C743E2A2DB"
				title: "区块认领"
				type: "checkmark"
			}]
			x: 4.5d
			y: -0.5d
		}
		{
			description: ["若想创建团队与好友共同游戏,使用指令&a/ftbteams party create (团队名称)&r"]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "ftbteams:textures/teams.png"
				}
			}
			id: "5AC1BE754210429E"
			rewards: [{
				id: "101D2FD24AB845AC"
				type: "xp"
				xp: 10
			}]
			size: 1.5d
			tasks: [{
				id: "4F13A02FB0055A62"
				title: "创建团队"
				type: "checkmark"
			}]
			x: -4.5d
			y: -0.5d
		}
		{
			description: [
				"本包任务由以下志愿者完成翻译"
				"&l翻译者名单&r (截至v0.3.6版本)"
				"&9西班牙语&r 译者 &2radzratz&r, &2abel102389&r, &2arivio&r 和 &2metabodiru&r"
				"&9挪威语&r (书面挪威语) 译者 &2Permest&r"
				"&9葡萄牙语&r 译者 &2oruiva&r"
				"&9日语&r 译者 &2fishlife1 (flll)&r"
				"&9韩语&r 译者 &2J-YonKen&r"
				"&9德语&r 译者 &2Aquamatik&r"
				"&9荷兰语&r 译者 &2NeofastFTL&r"
				"&9意大利语&r 译者 &2Il-mostro-di-gioacchinopoli&r"
			]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "ad_astra:textures/environment/earth.png"
				}
			}
			icon_scale: 1.1d
			id: "70D4EA125CF11137"
			rewards: [{
				id: "6935874719DCABBC"
				type: "xp"
				xp: 100
			}]
			shape: "rsquare"
			size: 1.8d
			tasks: [{
				id: "083129895DA3B3B4"
				title: "全&a翻译&f"
				type: "checkmark"
			}]
			x: 0.0d
			y: 3.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方团队&r或&2社区贡献者&r为ATM整合包创作"
				"根据&eAllTheMods&r系列整合包采用&e保留所有权利&r的授权协议,未经官方明确许可,禁止在任何非ATM团队发布的公开整合包中使用本任务"
				""
				""
				""
				"此任务默认隐藏,若你看到此提示说明正处于编辑模式"
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "4F020912336BCC47"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "7EBE873AF3D96C0C"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "0CD6D7231BCDC4E3"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: 5.5d
		}
	]
	title: "欢迎"
}
