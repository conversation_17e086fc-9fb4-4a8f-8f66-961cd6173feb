{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "evilcraft"
	group: "02FE661031A105D8"
	icon: "evilcraft:blood_orb_filled"
	id: "3456E0C530C0038E"
	images: [
		{
			height: 2.0d
			image: "evilcraft:block/blood_flow"
			rotation: 45.0d
			width: 2.0d
			x: 0.0d
			y: 0.0d
		}
		{
			height: 2.0d
			image: "evilcraft:block/blood_flow"
			rotation: 90.0d
			width: 2.0d
			x: 0.0d
			y: 0.0d
		}
		{
			alpha: 100
			color: 0
			height: 3.0d
			image: "ftbquests:textures/shapes/diamond/outline.png"
			order: -2
			rotation: 45.0d
			width: 3.0d
			x: 0.0d
			y: 0.0d
		}
		{
			alpha: 100
			color: 0
			height: 3.0d
			image: "ftbquests:textures/shapes/diamond/outline.png"
			order: -1
			rotation: 0.0d
			width: 3.0d
			x: 0.0d
			y: 0.0d
		}
		{
			alpha: 100
			height: 1.0d
			image: "createaddition:block/tesla_coil/lightning_new"
			rotation: 45.0d
			width: 1.0d
			x: -0.5d
			y: -3.0d
		}
		{
			alpha: 150
			color: 11393254
			height: 2.0d
			image: "ftblibrary:icons/toggle_rain"
			rotation: 0.0d
			width: 2.0d
			x: 0.5d
			y: -4.0d
		}
		{
			height: 1.0d
			image: "evilcraft:block/blood_stain_1"
			rotation: 0.0d
			width: 1.0d
			x: -1.0d
			y: 4.5d
		}
		{
			height: 1.0d
			image: "evilcraft:block/blood_stain_0"
			rotation: 0.0d
			width: 1.0d
			x: -1.0d
			y: 5.0d
		}
		{
			height: 1.0d
			image: "evilcraft:block/blood_stain_1"
			rotation: 45.0d
			width: 1.0d
			x: -1.5d
			y: 4.5d
		}
		{
			height: 2.0d
			image: "evilcraft:block/spirit_portal"
			rotation: 0.0d
			width: 2.0d
			x: 4.5d
			y: -1.0d
		}
		{
			alpha: 100
			height: 2.0d
			image: "ftbquests:block/barrier"
			order: -1
			rotation: 45.0d
			width: 2.0d
			x: 4.5d
			y: -1.0d
		}
		{
			color: 9109504
			height: 2.5d
			image: "evilcraft:item/bowl_of_promises_active_overlay"
			rotation: 0.0d
			width: 2.5d
			x: 8.0d
			y: 9.5d
		}
		{
			height: 2.0d
			image: "evilcraft:block/blood_infuser_north_off"
			rotation: 0.0d
			width: 2.0d
			x: 8.0d
			y: 8.5d
		}
		{
			height: 1.5d
			image: "evilcraft:item/vengeance_essence"
			rotation: 0.0d
			width: 1.5d
			x: 3.0d
			y: 0.0d
		}
		{
			height: 1.5d
			image: "evilcraft:item/vengeance_essence"
			rotation: 0.0d
			width: 1.5d
			x: 6.0d
			y: 0.0d
		}
		{
			height: 1.5d
			image: "evilcraft:item/blook"
			rotation: 0.0d
			width: 1.5d
			x: 13.5d
			y: 1.5d
		}
		{
			height: 1.5d
			image: "evilcraft:item/blook"
			rotation: 0.0d
			width: 1.5d
			x: 10.5d
			y: 1.5d
		}
		{
			height: 2.5d
			image: "evilcraft:block/spirit_furnace_up_on"
			rotation: 45.0d
			width: 2.5d
			x: 12.0d
			y: 0.0d
		}
		{
			height: 2.5d
			image: "evilcraft:block/spirit_furnace_up_on"
			rotation: -135.0d
			width: 2.5d
			x: 12.0d
			y: 3.0d
		}
		{
			alpha: 150
			height: 2.0d
			hover: ["合成&aATM之星&f所需"]
			image: "minecraft:textures/painting/skull_and_roses.png"
			rotation: 0.0d
			width: 2.0d
			x: 0.0d
			y: 9.0d
		}
		{
			height: 0.3d
			hover: ["合成&aATM之星&f所需"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 12.0d
			y: 9.8d
		}
	]
	order_index: 6
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"&c&d邪恶工艺&f&r是以黑暗力量为主题的魔法模组.收集敌人鲜血,驾驭远古邪恶之源的力量!"
				""
				"模组所有内容均可在指南书&a《黑暗起源》&f&r中查阅."
				""
				"首先请寻找一些&9暗影宝石&r."
			]
			id: "31FD9EA513E0D010"
			rewards: [
				{
					id: "7E586B43C8F5CF6F"
					item: "evilcraft:origins_of_darkness"
					type: "item"
				}
				{
					id: "27EAEB9369B92D43"
					type: "xp"
					xp: 25
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "6467C991A6D45E36"
				item: "evilcraft:dark_gem"
				type: "item"
			}]
			title: "&a欢迎来到&r&c&d邪恶工艺&f&r!"
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["31FD9EA513E0D010"]
			description: [
				"要收集鲜血,需先制作&c鲜血萃取器&r."
				""
				"只要携带此物品,击杀生物时就会自动收集鲜血.&c鲜血&r是&d邪恶工艺&f中的重要资源."
				""
				"需要扩容？只需制作新萃取器,在工作台中合并即可!"
				""
				"该设备还能从地面&c血渍&r中提取鲜血(可自行制造)."
				""
				"最后,当储存足够鲜血时,可潜行状态下&a右键点击&f地面放置血桶."
			]
			id: "7E79F52147B606F9"
			min_width: 250
			rewards: [
				{
					id: "0B31270AAD83F04B"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 0
								FluidName: "minecraft:empty"
								capacity: 5000
							}
						}
						id: "evilcraft:blood_extractor"
						tag: {
							Fluid: {
								Amount: 0
								FluidName: "minecraft:empty"
							}
							capacity: 5000
						}
					}
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "1B1AC214CAAC35D0"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "47C92745264ADA72"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "3DCF9B950A836935"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 5000
						}
					}
					id: "evilcraft:blood_extractor"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 5000
					}
				}
				type: "item"
			}]
			title: "&a收集&r&c鲜血"
			x: 0.0d
			y: 2.5d
		}
		{
			dependencies: ["31FD9EA513E0D010"]
			description: [
				"这些建筑极易辨认——中央矗立着巨型光柱."
				""
				"&9黑暗神殿&r中央设有&a环境蓄能器&r."
				""
				"可用其充能并制造多种关键物品,包括&d闪电炸弹&r!"
			]
			id: "2CB69634F6A6E53E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "274FD83268B97D3F"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "1D2A6BD57767B921"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			tasks: [
				{
					icon: "evilcraft:environmental_accumulator"
					id: "52D6F14FEB974305"
					structure: "evilcraft:dark_temple"
					title: "探访黑暗神殿"
					type: "structure"
				}
				{
					id: "22E342DF7C9197A7"
					item: {
						Count: 1
						id: "evilcraft:weather_container"
						tag: {
							weather: "EMPTY"
						}
					}
					match_nbt: false
					type: "item"
				}
			]
			title: "&9黑暗神殿"
			x: 0.0d
			y: -2.0d
		}
		{
			dependencies: ["7E79F52147B606F9"]
			description: [
				"需制作首颗&5黑暗能量宝石&r推进进度."
				""
				"需在&a鲜血萃取器&r中储存至少5桶鲜血.挖出5格深坑注满鲜血,"
				""
				"形成血池后将&9暗影宝石&r投入其中进行灌注."
			]
			id: "64EC182B81BD8B86"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "68FD17A0DF088BA3"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "64E447CC5A543294"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "1B4C9F78177AE9B5"
				item: "evilcraft:dark_power_gem"
				type: "item"
			}]
			title: "&d用&r&c鲜血&d灌注宝石"
			x: 0.5d
			y: 4.0d
		}
		{
			dependencies: ["7E79F52147B606F9"]
			description: [
				"血桶放置过久会凝固成&c硬化血块&r."
				""
				"虽极不卫生,但我们需要各种形态的鲜血推进进度——包括干涸的血液."
				""
				"遇雨或用普通工具破坏可还原为鲜血.若用&9&a打火石&f&r破坏,则会获得&d血晶碎片&r."
			]
			id: "1E3471513C75CC54"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3A1E48D068175183"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "0459B93C052F519B"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				count: 8L
				id: "3EB33B838372C035"
				item: "evilcraft:hardened_blood_shard"
				type: "item"
			}]
			title: "凝固的&c鲜血？"
			x: 1.5d
			y: 3.0d
		}
		{
			dependencies: ["68318811CCC28320"]
			description: [
				"今后无需再手动制造血池来制作黑暗能量宝石."
				""
				"可制作&9鲜血灌注机&r自动处理,实现物品直接灌注!"
				""
				"使用&e承诺之证&r可升级该设备.这是推进进度的核心机器之一!"
			]
			id: "62A262A706CFCAF0"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "34ED8C3B8CC630CA"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "173EF0031734B431"
					type: "xp"
					xp: 50
				}
			]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "0BE59456FB7744CE"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 10000
						}
					}
					id: "evilcraft:blood_infuser"
					tag: { }
				}
				type: "item"
			}]
			title: "&a鲜血灌注机"
			x: 3.5d
			y: 5.5d
		}
		{
			dependencies: [
				"64EC182B81BD8B86"
				"1E3471513C75CC54"
			]
			description: [
				"将&a硬化血晶&r与&9黑暗能量宝石&r结合可制作&a鲜血灌注&f核心."
				""
				"&9&a鲜血灌注&f核心&r是&d邪恶工艺&f中多种机器的核心合成材料."
			]
			id: "68318811CCC28320"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "11CC009B068D104B"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "7E28A39E1447CF3A"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			size: 1.25d
			tasks: [{
				id: "734B129022B49A38"
				item: "evilcraft:blood_infusion_core"
				type: "item"
			}]
			title: "&a灌注核心"
			x: 2.0d
			y: 4.5d
		}
		{
			dependencies: ["62A262A706CFCAF0"]
			description: [
				"使用鲜血灌注机制作&c亡灵树苗&r,可生长为&d亡灵树木&r."
				""
				"其&a原木&f与木板可用于制作&d邪恶工艺&f中的多种工具物品."
			]
			id: "40888A2C17D8FFF6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4B6E919276C8F1A2"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "352AA0893B127276"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "2379362CB6DAE025"
					item: "evilcraft:undead_sapling"
					type: "item"
				}
				{
					id: "758C54EFB1301C68"
					item: "evilcraft:undead_planks"
					type: "item"
				}
			]
			title: "亡灵树木"
			x: 3.5d
			y: 7.5d
		}
		{
			dependencies: ["62A262A706CFCAF0"]
			description: [
				"&d邪恶工艺&f机器可以通过使用&e承诺&r进行升级.每种承诺会产生不同效果,但首先我们要升级血祭台&a解锁&f更多配方."
				""
				"为此需要制作&e坚韧承诺:一级&r.通常这只会增加机器的存储容量.但对血祭台而言,这将解锁更多配方!"
			]
			icon: "evilcraft:promise_tier_1"
			id: "62CE0FFAF6352287"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "482322CBBA7FE9F3"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "448851E30BF3A2D9"
					type: "xp"
					xp: 25
				}
			]
			shape: "square"
			tasks: [
				{
					id: "689026A3ED7A863A"
					item: "evilcraft:bowl_of_promises_tier0"
					type: "item"
				}
				{
					id: "68A9C99DC3DFD901"
					item: "evilcraft:promise_tier_1"
					type: "item"
				}
			]
			title: "&a升级我们的机器"
			x: 6.0d
			y: 5.5d
		}
		{
			dependencies: ["68318811CCC28320"]
			description: [
				"&c&a血木箱子&f&r可用&c血液&r修复物品."
				""
				"但修复后的物品可能会变成&d诅咒&r状态..."
			]
			id: "74B0308336F5E017"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3C0C54B87A03F1D2"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "253E9653A75A538B"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "45053D11D2454483"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 10000
						}
					}
					id: "evilcraft:blood_chest"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 10000
					}
				}
				type: "item"
			}]
			title: "用&c血液&r修复工具"
			x: 1.0d
			y: 5.5d
		}
		{
			dependencies: ["62CE0FFAF6352287"]
			description: [
				"需要&a移除&f装备上的&d附魔&r？或是想&a解除&f诅咒？可通过&c净化装置&r实现."
				""
				"操作时先向&a净化者&f注入至少3桶血液,然后投入需要&a清除&f附魔的物品."
				""
				"接着放入&c血之书&r,它会吸收物品上的一个附魔并将其转化为对应附魔书."
			]
			id: "5A8F4CA0F09F4842"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "31A60FDFF8B5C6A0"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "0692DC80214837E5"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "106290C6011C5430"
					type: "xp"
					xp: 25
				}
			]
			tasks: [
				{
					id: "5C748C6F62DC11FF"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 0
								FluidName: "minecraft:empty"
								capacity: 3000
							}
						}
						id: "evilcraft:purifier"
						tag: {
							Fluid: {
								Amount: 0
								FluidName: "minecraft:empty"
							}
							capacity: 3000
						}
					}
					type: "item"
				}
				{
					id: "5E175E8BE4C2D8F2"
					item: "evilcraft:blook"
					type: "item"
				}
			]
			title: "移除&d附魔&r与&d诅咒"
			x: 6.0d
			y: 7.0d
		}
		{
			dependencies: ["62CE0FFAF6352287"]
			id: "1D794DE95C9FF6C7"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "362801F7A9EDD76D"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "3F5DB1C3D8F4468D"
					type: "xp"
					xp: 25
				}
			]
			subtitle: "提升&d邪恶工艺&f机器速度"
			tasks: [{
				id: "776844C7221C165C"
				item: "evilcraft:promise_speed_0"
				type: "item"
			}]
			x: 5.0d
			y: 4.0d
		}
		{
			dependencies: ["62CE0FFAF6352287"]
			id: "12F3A604329EC604"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "77395930D140E807"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "021809D0577370B6"
					type: "xp"
					xp: 25
				}
			]
			subtitle: "增强&d邪恶工艺&f机器&a效率&f"
			tasks: [{
				id: "0B55C293EACD60B9"
				item: "evilcraft:promise_efficiency_0"
				type: "item"
			}]
			x: 7.0d
			y: 4.0d
		}
		{
			dependencies: ["7E79F52147B606F9"]
			description: [
				"击杀生物时有时会生成&d复仇怨灵&r."
				""
				"它们的&d精华&r可用于制作&d邪恶工艺&f中的高级物品."
				""
				"怨灵太少？制作&9复仇指环&r并在战斗中启用可吸引更多怨灵."
			]
			id: "51B24CC5E9332C1E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5ADA78F1C2C772ED"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "32E1BD15DDDD99B6"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "048E4473A97AD33E"
					item: "evilcraft:vengeance_essence"
					type: "item"
				}
				{
					id: "1F78BC1E71207D66"
					item: "evilcraft:vengeance_ring"
					type: "item"
				}
			]
			title: "&d怨灵&r的复仇"
			x: 1.5d
			y: 1.5d
		}
		{
			dependencies: ["62CE0FFAF6352287"]
			icon: "evilcraft:promise_tier_2"
			id: "4978A9B616362CCE"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "726B4CB73514DAD2"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "13F444F987948EFB"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "26B65922BE81B24E"
					type: "xp"
					xp: 50
				}
			]
			shape: "rsquare"
			size: 1.25d
			tasks: [
				{
					id: "48A4C8390243406C"
					item: "evilcraft:bowl_of_promises_tier1"
					type: "item"
				}
				{
					id: "703639D39EF4884C"
					item: "evilcraft:promise_tier_2"
					type: "item"
				}
			]
			title: "§a二级§r:更多§c血液"
			x: 8.0d
			y: 5.5d
		}
		{
			dependencies: ["4978A9B616362CCE"]
			id: "4EBED8ABC8C863A4"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7822EA12EF81C4EA"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "4D73C84CCF51B761"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "79AC26FC2012ADA2"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [
				{
					id: "1812768537DE49D7"
					item: "evilcraft:bowl_of_promises_tier2"
					type: "item"
				}
				{
					id: "3B0932C1D1C8734F"
					item: "evilcraft:promise_tier_3"
					type: "item"
				}
			]
			title: "§a三级§r:海量§c血液"
			x: 10.0d
			y: 5.5d
		}
		{
			dependencies: [
				"1D794DE95C9FF6C7"
				"12F3A604329EC604"
			]
			description: ["提供速度提升+台阶跨越能力!"]
			id: "4D7B6842B9F53459"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "597BBA8C06982126"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "2DE3C44CE6807BA9"
					type: "xp"
					xp: 25
				}
			]
			shape: "octagon"
			size: 1.25d
			tasks: [{
				id: "6F7CD7DB5A883579"
				item: "evilcraft:effortless_ring"
				type: "item"
			}]
			x: 6.0d
			y: 2.5d
		}
		{
			dependencies: ["7E79F52147B606F9"]
			description: [
				"利用&9暗影宝石&r可制作&a暗影储罐&r."
				""
				"可储存16桶任意液体——不过你主要会用来装&c血液&r."
				""
				"需要扩容？在合成网格中与另一个暗影储罐组合即可."
			]
			id: "75CF9EAB75C3907E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "21261E43154D9972"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "3D3EAE032673B96C"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [{
				id: "7FAA4F7F27A63395"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			title: "&a储存&r&c流体"
			x: -1.5d
			y: 2.0d
		}
		{
			dependencies: ["40888A2C17D8FFF6"]
			description: [
				"这是把时运V的镐子.仅此而已."
				""
				"挖矿时&o绝对&r不会召唤怨灵.&o绝对不&r."
			]
			id: "10EF30B919EBA5C6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "30CA54B7D68A0FEC"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "3EB65F6BBCFCDEF3"
					type: "xp"
					xp: 25
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "56C5BE8015C28FBC"
				item: {
					Count: 1
					id: "evilcraft:vengeance_pickaxe"
					tag: {
						Damage: 0
						Enchantments: [
							{
								id: "evilcraft:vengeance"
								lvl: 3s
							}
							{
								id: "minecraft:fortune"
								lvl: 5s
							}
						]
					}
				}
				type: "item"
			}]
			x: 4.5d
			y: 9.0d
		}
		{
			dependencies: [
				"4978A9B616362CCE"
				"066438B01655D866"
			]
			description: [
				"&c&d邪恶工艺&f&r拥有专属的&a怪物农场&f!"
				""
				"首先需要合成至少33个&c暗血砖&r,用它们建造足以禁锢召唤灵魂的坚固结构."
				""
				"还需将灵魂囚禁于&9永恒禁锢之匣&r中,这将决定农场产出的物品."
				""
				"建造此结构时,你需要搭建一个能让生物生成的立方体空间.最小尺寸为3x4x3,这个大小足以生成僵尸等生物.记得在其中一面放置&9灵魂熔炉&r,这样你才能与结构互动."
				""
				"若想生成更大型的生物,你需要建造更大的结构空间."
				""
				"{image:atm:textures/questpics/evilcraft/evilcraft_spiritfurnace.png width:125 height:150 align:1}"
			]
			id: "1DA0A87C471A38AC"
			min_width: 400
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0E86747E5E65C608"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "3862E3F9117A91DF"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "6689C397E5960B91"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			tasks: [
				{
					count: 33L
					id: "69D96EE6DC2179D1"
					item: "evilcraft:dark_blood_brick"
					type: "item"
				}
				{
					id: "3CAAC4AF810BF534"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 0
								FluidName: "minecraft:empty"
								capacity: 10000
							}
						}
						id: "evilcraft:spirit_furnace"
						tag: { }
					}
					type: "item"
				}
			]
			title: "使用&c血魔法&r建造&a怪物农场&f"
			x: 8.0d
			y: 1.5d
		}
		{
			dependencies: ["51B24CC5E9332C1E"]
			description: [
				"除了获取&d精华&r,我们还能囚禁怨灵备用.听起来很邪恶对吧？"
				""
				"要成为怨灵猎手,需先制作&d怨灵聚焦器&r用于&a定住怨灵&r,然后在附近放置&9永恒禁锢盒&r吸收怨灵."
			]
			icon: "evilcraft:box_of_eternal_closure"
			id: "066438B01655D866"
			min_width: 250
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5E0530F6FB4A5A7B"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "4E246CAB1B584FB1"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			size: 1.5d
			tasks: [
				{
					id: "2161414994EAB2A9"
					item: "evilcraft:box_of_eternal_closure"
					type: "item"
				}
				{
					id: "2228CC59A99F49E9"
					item: "evilcraft:vengeance_focus"
					type: "item"
				}
			]
			title: "捕捉&d怨灵"
			x: 4.5d
			y: 1.5d
		}
		{
			dependencies: ["74B0308336F5E017"]
			description: [
				"觉得&c&a血木箱子&f&r运作不够快？有太多待修复物品堆积？你可以制作&c巨型&a血木箱子&f&r来解决这些问题."
				""
				"制作时需准备25块&9强化亡灵木板&r.用这些材料搭建3x&a3x3空心&f立方体框架,最后用&c巨型&a血木箱子&f&r方块封顶完成多方块结构.正确组装后,你将获得可使用的超大容量&c&a血木箱子&f&r——不妨称之为庞然巨箱."
				""
				"该结构还支持通过&6承诺&r进行升级."
				""
				""
				"{image:atm:textures/questpics/evilcraft/bloodchest.png width:250 height:200 align:1}"
			]
			id: "0104C2E2E30B966B"
			min_width: 250
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4EA306D7DB8079AB"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "7ECD4A2652185AB7"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "2F653206D21841F4"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			size: 1.25d
			subtitle: "Papa &a血木箱子&f"
			tasks: [
				{
					id: "685A188F7C39BE43"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 0
								FluidName: "minecraft:empty"
								capacity: 10000
							}
						}
						id: "evilcraft:colossal_blood_chest"
						tag: {
							Fluid: {
								Amount: 0
								FluidName: "minecraft:empty"
							}
							capacity: 10000
						}
					}
					match_nbt: false
					type: "item"
				}
				{
					count: 25L
					id: "0B3D5A92B01017A5"
					item: "evilcraft:reinforced_undead_planks"
					type: "item"
				}
			]
			title: "&a巨构修复工程"
			x: 0.0d
			y: 6.5d
		}
		{
			dependencies: ["1DA0A87C471A38AC"]
			description: [
				"想把禁锢盒里的怨灵做成生物蛋？"
				""
				"&9怨灵复苏仪&r就能实现.注入大量&c血液&r和空白蛋,配合装有目标怨灵的&9永恒禁锢盒&r即可尝试制作."
				""
				"注:部分生物无法制作成蛋."
			]
			id: "59036A2741E7A8AA"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3D1C925F4CB7567F"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "1FB5ED3DF7B6AC7B"
					type: "xp"
					xp: 25
				}
			]
			shape: "octagon"
			size: 1.25d
			tasks: [{
				id: "4D297E4145C19440"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 10000
						}
					}
					id: "evilcraft:spirit_reanimator"
					tag: { }
				}
				type: "item"
			}]
			title: "&a制造&r&9生物蛋"
			x: 8.0d
			y: -0.125d
		}
		{
			dependencies: ["31FD9EA513E0D010"]
			hide_dependency_lines: true
			id: "1F8BC2A4CF055038"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2CCF3AB81B8231CE"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "396530A6F7A571DA"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			subtitle: "&a可重用末影珍珠&f"
			tasks: [{
				id: "20878EDDDF0C77DA"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 1000
						}
					}
					id: "evilcraft:blood_pearl_of_teleportation"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 1000
					}
				}
				type: "item"
			}]
			x: 12.0d
			y: 3.5d
		}
		{
			dependencies: ["2CB69634F6A6E53E"]
			description: ["按住&a右键&f可蓄力发动&c血液&r范围攻击.潜行+&a右键&f调节威力等级.&a等级越高&f伤害越大,但消耗血液也越多."]
			hide_dependency_lines: true
			id: "56FB82DEB944F758"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1F1C3F029C5673D4"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "06E13CEE3DF1FC56"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [{
				id: "40BB2C59D2A857BF"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 4000
						}
					}
					id: "evilcraft:mace_of_distortion"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 4000
					}
				}
				type: "item"
			}]
			x: 12.0d
			y: -0.5d
		}
		{
			dependencies: ["62CE0FFAF6352287"]
			description: [
				"&9动能吸引器&r就像物品和经验值的磁铁."
				""
				"而&e反向动能吸引器&r则会排斥物品和经验值."
			]
			hide_dependency_lines: true
			id: "77B5B23A2C01ED7E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "364516D496C2023E"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "70FFB8883248C8E3"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			subtitle: "磁力装置"
			tasks: [
				{
					id: "7E84245939CD98B9"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 0
								FluidName: "minecraft:empty"
								capacity: 1000
							}
						}
						id: "evilcraft:kineticator"
						tag: {
							Fluid: {
								Amount: 0
								FluidName: "minecraft:empty"
							}
							capacity: 1000
						}
					}
					type: "item"
				}
				{
					id: "7974BA01706F695D"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 0
								FluidName: "minecraft:empty"
								capacity: 1000
							}
						}
						id: "evilcraft:kineticator_repelling"
						tag: {
							Fluid: {
								Amount: 0
								FluidName: "minecraft:empty"
							}
							capacity: 1000
						}
					}
					type: "item"
				}
			]
			x: 12.0d
			y: 2.5d
		}
		{
			dependencies: ["2CB69634F6A6E53E"]
			hide_dependency_lines: true
			id: "0EAF01037BB20BCB"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "055B27F27A45871E"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "7800D8E4E13B6AA0"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			subtitle: "召唤僵尸"
			tasks: [{
				id: "73E9DB8434DF6236"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 10000
						}
					}
					id: "evilcraft:necromancer_staff"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 10000
					}
				}
				type: "item"
			}]
			x: 12.0d
			y: 0.5d
		}
		{
			dependencies: ["68318811CCC28320"]
			hide_dependency_lines: true
			id: "199E7C89584C3DB1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "749C4081C46FB569"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "0F6D96198298DBF3"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			subtitle: "移除负面&a药水效果&f"
			tasks: [{
				id: "40CA543BAAC1182D"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 5000
						}
					}
					id: "evilcraft:invigorating_pendant"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 5000
					}
				}
				type: "item"
			}]
			x: 11.5d
			y: 3.0d
		}
		{
			dependencies: ["62CE0FFAF6352287"]
			hide_dependency_lines: true
			id: "6CA1D90CCE17097D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2B7A21A84A12700F"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "2B585A6FB25F377D"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			subtitle: "一次性闪电权杖"
			tasks: [{
				id: "26ADA98532E2CDB7"
				item: "evilcraft:sceptre_of_thunder"
				type: "item"
			}]
			x: 12.5d
			y: 0.0d
		}
		{
			dependencies: [
				"026A71F98A52E3A5"
				"753B3EF7CC94A6DD"
			]
			dependency_requirement: "one_completed"
			description: ["使用&a玩家&r或&d狼人&r的肉,你可以通过加尔蒙波西亚之力创造&a无限食物源&r!"]
			id: "600F66B13B29708B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0253575B6D6C1E28"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "1853D8E78E2A1682"
					type: "xp"
					xp: 100
				}
			]
			shape: "octagon"
			size: 1.5d
			tasks: [{
				id: "5DA025E8807292CC"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 10000
						}
					}
					id: "evilcraft:flesh_rejuvenated"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 10000
					}
				}
				type: "item"
			}]
			x: 12.0d
			y: 9.0d
		}
		{
			dependencies: ["35FA55BE8DF49EE8"]
			description: ["要注入指定药水,只需手持&2充能护符&r时&a右键点击&f来&a打开注射槽&f."]
			hide_dependency_lines: true
			id: "4DF7E2149F4BD8CC"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "68CA2A1606C9E043"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "64ACEFD0F2270714"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			subtitle: "施加&a药水效果&f"
			tasks: [{
				id: "02AD6938B2B14996"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 5000
						}
					}
					id: "evilcraft:primed_pendant"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 5000
						inventoryItem: [ ]
					}
				}
				type: "item"
			}]
			x: 12.5d
			y: 3.0d
		}
		{
			dependencies: ["35FA55BE8DF49EE8"]
			description: ["功能类似&a诡异之锤&r,但会造成范围爆炸效果."]
			hide_dependency_lines: true
			id: "290FAB3DE8FD04E7"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4160886ED7D22DC5"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "2D6B2B067E478156"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [{
				id: "2789A057B859F93B"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 4000
						}
					}
					id: "evilcraft:mace_of_destruction"
					tag: {
						Fluid: {
							Amount: 0
							FluidName: "minecraft:empty"
						}
						capacity: 4000
					}
				}
				type: "item"
			}]
			x: 11.5d
			y: 0.0d
		}
		{
			dependencies: ["7E79F52147B606F9"]
			description: [
				"是否想过用&a锹&f攻击生物还能造成伤害？虽然想问为什么,但这就是&d邪恶工艺&f."
				""
				"现在满足你!这既是武器,也是挖掘松软方块的工具!"
			]
			id: "0B9F2B443813F43C"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6061CB7F7078B276"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "066EF2005D17430C"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [{
				id: "743506EBB76A0B12"
				item: {
					Count: 1
					id: "evilcraft:spikey_claws"
					tag: {
						Damage: 0
					}
				}
				match_nbt: false
				type: "item"
			}]
			x: -2.0d
			y: 2.5d
		}
		{
			dependencies: ["4EBED8ABC8C863A4"]
			description: [
				"&e纠缠圣杯&r类似&a末影蓄水槽&f."
				""
				"放置在世界中时可抽取血液.在物品栏激活时,会尝试补充以&c血液&r为资源的物品."
				""
				"要制作同网络的其他圣杯,只需在合成配方中用圣杯替代金锭."
			]
			hide_dependency_lines: true
			id: "0BB0DF36B079558F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5A20416F816F49A5"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "420CF9D7184466D4"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "4A3BF64ABA8BF90A"
					type: "xp"
					xp: 25
				}
			]
			shape: "gear"
			tasks: [{
				count: 2L
				id: "3C8168A55ACA86A3"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 0
							FluidName: "minecraft:empty"
							capacity: 4000
						}
					}
					id: "evilcraft:entangled_chalice"
					tag: {
						capacity: 4000
						tankID: "creative"
					}
				}
				match_nbt: false
				type: "item"
			}]
			title: "&d纠缠圣杯"
			x: 12.0d
			y: 1.5d
		}
		{
			dependencies: ["4EBED8ABC8C863A4"]
			description: [
				"利用被杀的&9复仇之魂&r力量,可通过血液灌注机制作&d加尔蒙波西亚&r."
				""
				"这是实体化的&d痛苦&r与&d悲伤&r,也是&d邪恶工艺&f中强力工具和物品的高级合成材料."
			]
			id: "35FA55BE8DF49EE8"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "23AB023224276D8B"
					table_id: 7482740998888138375L
					type: "random"
				}
				{
					exclude_from_claim_all: true
					id: "2153675CF7021E26"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "737E46F6AB10F512"
					type: "xp"
					xp: 250
				}
			]
			shape: "hexagon"
			size: 2.0d
			tasks: [{
				id: "25714C3E4C8266CD"
				item: "evilcraft:garmonbozia"
				type: "item"
			}]
			title: "&d加尔蒙波西亚"
			x: 12.0d
			y: 5.5d
		}
		{
			dependencies: ["35FA55BE8DF49EE8"]
			description: ["现在使用&d加尔蒙波西亚&r可以制作专属的&a环境收集器&r.说实话这个名字太长了."]
			id: "63AE4568375DD1BF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5FCB9312466F3997"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "05E294959C8F918F"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "58D7956E86F91083"
				item: "evilcraft:sanguinary_environmental_accumulator"
				type: "item"
			}]
			title: "专属天气收集器"
			x: 14.0d
			y: 5.5d
		}
		{
			dependencies: ["2CB69634F6A6E53E"]
			description: [
				"下雨时投入&a天气容器&r可汲取雨水之力."
				""
				"借此可制作&9无限&a水桶&f&r或&9无限水源方块&r,都是极其实用的物品!"
			]
			id: "7A93F07BE2E6EC97"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "117278C807CC151D"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "464BD039DFC85653"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "747D85AFC2E37B49"
					item: {
						Count: 1
						id: "evilcraft:weather_container"
						tag: {
							weather: "RAIN"
						}
					}
					match_nbt: true
					type: "item"
				}
				{
					id: "005992A335555BA4"
					item: "evilcraft:bucket_eternal_water"
					type: "item"
				}
				{
					id: "576692A5D2EBAD4C"
					item: "evilcraft:eternal_water"
					type: "item"
				}
			]
			title: "&a召唤&r &9降雨"
			x: 0.5d
			y: -3.0d
		}
		{
			dependencies: ["2CB69634F6A6E53E"]
			description: [
				"雷暴天气时将&a天气容器&r投入&a环境收集器&r可汲取风暴之力."
				""
				"由此可制作随心操控闪电的物品."
			]
			id: "674E2690D66ECD6E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4A4E483F09FCCBFF"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "1E29E392E6983202"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "45941C8F87F92114"
					item: {
						Count: 1
						id: "evilcraft:weather_container"
						tag: {
							weather: "LIGHTNING"
						}
					}
					match_nbt: true
					type: "item"
				}
				{
					id: "2A0FF6158E8316FC"
					item: "evilcraft:lightning_grenade"
					type: "item"
				}
				{
					id: "00A16E31DAB13327"
					item: "evilcraft:lightning_bomb"
					type: "item"
				}
			]
			title: "&a你被&r &9雷霆击中"
			x: -0.5d
			y: -3.0d
		}
		{
			dependencies: ["7E79F52147B606F9"]
			description: [
				"有生物摔得太狠导致&c血液&r四溅？真糟糕!"
				""
				"总之."
				""
				"可用&c血祭台&r吸收珍贵的&c血液&r备用!"
				""
				"想自动化采血？在祭台上放置&9尖刺板&r,让生物站在上面即可."
			]
			id: "525517F1625A9BCB"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "19D85646C2FCF95A"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "288BC70DF47B76C4"
					type: "xp"
					xp: 25
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "529AF6004FE7819C"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									ForgeCaps: {
										Parent: {
											Amount: 0
											FluidName: "minecraft:empty"
											capacity: 10000
										}
									}
									id: "evilcraft:sanguinary_pedestal_0"
									tag: {
										Fluid: {
											Amount: 0
											FluidName: "minecraft:empty"
										}
										capacity: 10000
									}
								}
								{
									Count: 1b
									ForgeCaps: {
										Parent: {
											Amount: 0
											FluidName: "minecraft:empty"
											capacity: 10000
										}
									}
									id: "evilcraft:sanguinary_pedestal_1"
									tag: {
										Fluid: {
											Amount: 0
											FluidName: "minecraft:empty"
										}
										capacity: 10000
									}
								}
							]
						}
					}
					title: "血祭基座"
					type: "item"
				}
				{
					id: "3FC22C6B8659C136"
					item: "evilcraft:spiked_plate"
					type: "item"
				}
			]
			title: "&a收集&r &c血迹"
			x: -1.5d
			y: 3.0d
		}
		{
			dependencies: ["35FA55BE8DF49EE8"]
			description: [
				"这是&a玩家&r的独特掉落物,包括你死亡时!"
				""
				"你可以通过反复死亡或参与PvP获取——反正都一样!"
			]
			id: "753B3EF7CC94A6DD"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "137B83481CEFB57E"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "0354885167881B05"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "19C973A3F42D631C"
				item: "evilcraft:flesh_humanoid"
				match_nbt: false
				type: "item"
			}]
			x: 11.0d
			y: 7.5d
		}
		{
			dependencies: ["35FA55BE8DF49EE8"]
			description: [
				"村庄里有些&o特别&r的村民,他们碰巧是&d狼人&r."
				""
				"击杀这些野兽可获得&d狼人肉&r."
			]
			id: "026A71F98A52E3A5"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5502D363E7C68301"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "128F76AD2008EC5E"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				id: "7217F680E0AA4389"
				item: "evilcraft:flesh_werewolf"
				type: "item"
			}]
			x: 13.0d
			y: 7.5d
		}
		{
			dependencies: ["40888A2C17D8FFF6"]
			description: [
				"需要从敌人身上获取更多鲜血？当然你需要!"
				""
				"&d血刃剑&r让你能收集更多鲜血,同时附带抢夺效果还能获得更多战利品!"
			]
			id: "7B524DAD8A33BF85"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "07375BCFE91437B3"
					table_id: 7482740998888138375L
					type: "loot"
				}
				{
					id: "7131BA97015DC647"
					type: "xp"
					xp: 25
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "19AAADDD210A2AF9"
				item: {
					Count: 1
					id: "evilcraft:vein_sword"
					tag: {
						Damage: 0
						Enchantments: [{
							id: "minecraft:looting"
							lvl: 2s
						}]
					}
				}
				type: "item"
			}]
			x: 2.5d
			y: 9.0d
		}
		{
			dependencies: ["40888A2C17D8FFF6"]
			description: [
				"&d扫帚&r可通过扫帚部件制作.每把扫帚由3个部件组成:帚柄、帚帽和帚毛."
				""
				"每个部件可通过将基础部件与特定物品合成来赋予特殊修饰属性.因修饰种类过多,请务必查阅指南书&a了解更多信息&f!"
				""
				"合成扫帚时,只需将各部件放入工作台.&a扫帚&f需要鲜血才能正常运作."
			]
			icon: {
				Count: 1
				ForgeCaps: {
					Parent: {
						Amount: 10000
						FluidName: "evilcraft:blood"
						capacity: 10000
					}
				}
				id: "evilcraft:broom"
				tag: {
					Fluid: {
						Amount: 10000
						FluidName: "evilcraft:blood"
					}
					capacity: 10000
				}
			}
			id: "28BF66D1B8CD4D44"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "621377873D26BE59"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "7882A83FC533B42E"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			size: 1.5d
			subtitle: "魔法扫帚"
			tasks: [
				{
					id: "73427CBDD811273E"
					item: {
						Count: 1
						id: "evilcraft:broom_part"
						tag: {
							broom_parts_tag: ["evilcraft:rod_bare"]
						}
					}
					match_nbt: true
					type: "item"
				}
				{
					id: "499D24D1F87BF794"
					item: {
						Count: 1
						id: "evilcraft:broom_part"
						tag: {
							broom_parts_tag: ["evilcraft:brush_bare"]
						}
					}
					match_nbt: true
					type: "item"
				}
				{
					id: "2148DCC201648C0A"
					item: {
						Count: 1
						id: "evilcraft:broom_part"
						tag: {
							broom_parts_tag: ["evilcraft:cap_bare"]
						}
					}
					match_nbt: true
					type: "item"
				}
				{
					id: "1E0F142EBB4085FF"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 0
								FluidName: "minecraft:empty"
								capacity: 10000
							}
						}
						id: "evilcraft:broom"
						tag: {
							Fluid: {
								Amount: 0
								FluidName: "minecraft:empty"
							}
							broom_modifiers_tag: [
								{
									id: "evilcraft:acceleration"
									value: 0.0f
								}
								{
									id: "evilcraft:modifier_count"
									value: 0.0f
								}
								{
									id: "evilcraft:maneuverability"
									value: 0.0f
								}
								{
									id: "evilcraft:speed"
									value: 0.0f
								}
								{
									id: "evilcraft:levitation"
									value: 0.0f
								}
							]
							broom_parts_tag: [
								"evilcraft:brush_bare"
								"evilcraft:cap_bare"
								"evilcraft:rod_bare"
							]
							capacity: 10000
						}
					}
					match_nbt: false
					type: "item"
				}
			]
			title: "&c鲜血&r &d扫帚"
			x: 3.5d
			y: 10.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r创作,专为AllTheMods整合包设计."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"此任务默认隐藏,若你看到此提示,说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "00D851126F2E90FA"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "50DCADFCCAD7BB24"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
				{
					id: "40C84C03F35EE9FA"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
			]
			x: -2.0d
			y: 0.0d
		}
	]
	title: "&d邪恶工艺&f"
}
