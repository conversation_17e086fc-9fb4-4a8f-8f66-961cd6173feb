{
	default_hide_dependency_lines: true
	default_quest_shape: ""
	filename: "allthemodium"
	group: ""
	icon: "allthemodium:allthemodium_ingot"
	id: "1B175B2C955D8395"
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["731686C758AD9A99"]
			dependency_requirement: "one_completed"
			description: [
				"这种珍贵矿石将开启您成为顶尖强者的旅程!"
				""
				"它生成于&a深暗之域&f生物群系的洞顶和墙壁,或在&a挖矿维度&f的深板岩层中."
			]
			hide_dependency_lines: false
			hide_until_deps_visible: false
			id: "5BDBE666E604FCAC"
			rewards: [
				{
					id: "35D9FCB5040C66D9"
					item: "allthemodium:allthemodium_ore"
					type: "item"
				}
				{
					id: "0205D27EF9929F30"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2139CB369B6057CD"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "759E7BC43EFF648C"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:raw_allthemodium"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_ingot"
							}
						]
					}
				}
				title: "&aATM矿石&f"
				type: "item"
			}]
			x: -1.5d
			y: 0.5d
		}
		{
			dependencies: ["5BDBE666E604FCAC"]
			dependency_requirement: "one_started"
			description: [
				"通往(近乎)无敌境界的下一步."
				""
				"在&a下界&fY64以上的任何生物群系洞顶和墙壁寻找这种稀有矿石."
				""
				"您也可在Other维度的Y0至Y40区间,沿洞穴墙壁和顶部发现该矿石."
				""
				"注意:&a矿石&f仅会暴露在空气中生成!"
			]
			hide_dependency_lines: false
			id: "2DF64CB9298E91EA"
			rewards: [
				{
					id: "5D2EBAC7AC7945CD"
					item: "allthemodium:vibranium_ore"
					type: "item"
				}
				{
					id: "459B4DDA26AC0FFC"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "3E63128D811B9171"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "53CAF4DF6BD09600"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:raw_vibranium"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_ingot"
							}
						]
					}
				}
				title: "&a振金矿石&f"
				type: "item"
			}]
			x: 0.0d
			y: 1.0d
		}
		{
			dependencies: ["2DF64CB9298E91EA"]
			description: ["一种极其稀有的矿石,仅能在&a末地&f高地生物群系中找到."]
			hide_dependency_lines: false
			id: "4F6E6AF1D9E74CB7"
			rewards: [
				{
					id: "2AA15BC1812E1F77"
					item: "allthemodium:unobtainium_ore"
					type: "item"
				}
				{
					id: "2DC06917102E6563"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "0E328B594DAC6713"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "06B707D1437D36FC"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:raw_unobtainium"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_ingot"
							}
						]
					}
				}
				title: "&a叵得矿石&f"
				type: "item"
			}]
			x: 1.5d
			y: 0.5d
		}
		{
			description: ["&e全金属锭&r可用于制作超高速熔炉!"]
			hide_dependency_lines: false
			id: "2CC97CF32D9C017B"
			rewards: [{
				exclude_from_claim_all: true
				id: "3C0169930F748A7F"
				table_id: 5564196992594175882L
				type: "random"
			}]
			shape: "diamond"
			size: 1.25d
			tasks: [{
				id: "54B58D4300C033A6"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "ironfurnaces:allthemodium_furnace"
							}
							{
								Count: 1b
								id: "ironfurnaces:vibranium_furnace"
							}
							{
								Count: 1b
								id: "ironfurnaces:unobtainium_furnace"
							}
						]
					}
				}
				title: "ATM熔炉"
				type: "item"
			}]
			title: "&d高速熔炉"
			x: -3.0d
			y: 3.0d
		}
		{
			dependencies: ["2BF9B347D1FC037A"]
			dependency_requirement: "one_completed"
			hide_dependency_lines: false
			id: "29637BD992599915"
			rewards: [
				{
					id: "28AB1CC409C9BADB"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "008EBE6E9E5262F3"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "square"
			size: 1.0d
			tasks: [{
				id: "48DD4D998FEBB09B"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthearcanistgear:allthemodium_boots"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 4
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:allthemodium_leggings"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 4
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:allthemodium_robes"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 4
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:allthemodium_hat"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 4
									}
								}
							}
							{
								Count: 1b
								id: "allthewizardgear:allthemodium_mage_chestplate"
								tag: {
									ISB_Spells: {
										data: [ ]
										maxSpells: 1
										mustEquip: 1b
										spellWheel: 1b
									}
								}
							}
							{
								Count: 1b
								id: "allthewizardgear:allthemodium_mage_helmet"
							}
							{
								Count: 1b
								id: "allthewizardgear:allthemodium_mage_leggings"
							}
							{
								Count: 1b
								id: "allthewizardgear:allthemodium_mage_boots"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_helmet"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_chestplate"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_leggings"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_boots"
							}
						]
					}
				}
				title: "全金属护甲"
				type: "item"
			}]
			title: "&e全金属护甲"
			x: -1.5d
			y: 4.0d
		}
		{
			description: [
				"&d全金属&r是AllTheMods整合包的核心模组.该模组为世界添加了终极矿石,将您的模组体验提升至全新高度."
				""
				"您可在&9全金属&r手册中获取更多关于该模组的信息."
			]
			icon: {
				Count: 1
				id: "patchouli:guide_book"
				tag: {
					"patchouli:book": "allthemodium:allthemodium_book"
				}
			}
			id: "731686C758AD9A99"
			rewards: [
				{
					id: "4D04379836E29120"
					type: "xp"
					xp: 10
				}
				{
					id: "0D061D49519CE0B4"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "allthemodium:allthemodium_book"
						}
					}
					type: "item"
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				dimension: "minecraft:overworld"
				id: "159872B988A173AA"
				type: "dimension"
			}]
			title: "全金属入门指南"
			x: 0.0d
			y: -1.0d
		}
		{
			hide_dependency_lines: true
			id: "7F3B96033AB7A21E"
			rewards: [
				{
					id: "51777CA9A13AAD35"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2FD4422A73C37850"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "heart"
			tasks: [{
				id: "6C79D005D95BAB61"
				item: "allthemodium:allthemodium_apple"
				type: "item"
			}]
			x: 3.0d
			y: 4.0d
		}
		{
			hide_dependency_lines: true
			id: "15D56588634665FA"
			rewards: [
				{
					id: "40F8666A439FDC16"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "0186D353D38596DC"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "heart"
			tasks: [{
				id: "32629A7C461C48F7"
				item: "allthemodium:allthemodium_carrot"
				type: "item"
			}]
			x: -3.0d
			y: 4.0d
		}
		{
			dependencies: ["5BDBE666E604FCAC"]
			description: ["注意:虽然制作基础工具不需要&a升级模板&r,但使用它将为您节省大量&e&aATM锭&f&r!"]
			hide_dependency_lines: true
			hide_until_deps_visible: false
			id: "30E6C6825D78B5F1"
			rewards: [
				{
					id: "407C0224BB0CF2C7"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "3C3FBA11E9666529"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.0d
			tasks: [{
				id: "37CD942230304016"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:allthemodium_sword"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_pickaxe"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_axe"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_shovel"
							}
							{
								Count: 1b
								id: "allthemodium:allthemodium_hoe"
							}
						]
					}
				}
				title: "全金属工具"
				type: "item"
			}]
			title: "&e全金属工具"
			x: -1.5d
			y: 5.5d
		}
		{
			description: [
				"&a传送&f平台用于传送到ATM整合包新增的3个维度."
				""
				"在主世界放置后,通过潜行&a右键点击&f空手可前往&a&a挖矿维度&f&r."
				""
				"要前往&cOthe&r维度,请在&a下界&f执行相同操作."
				""
				"要抵达&5Beyond&r维度,请在&a末地&f使用&a传送平台&f."
			]
			hide_dependency_lines: true
			hide_until_deps_visible: true
			id: "3C322474D2F2BA99"
			rewards: [
				{
					id: "0B4E3EEE5A9DB68C"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "57F29BA9DE0EB0FB"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "square"
			size: 1.5d
			tasks: [{
				id: "004AE063EA883019"
				item: "allthemodium:teleport_pad"
				type: "item"
			}]
			title: "全金属维度"
			x: 0.0d
			y: 7.5d
		}
		{
			dependencies: ["3C322474D2F2BA99"]
			description: [
				"您将在异界发现海量矿石.这个维度充满惊人的矿脉分布,还有&a古代森林&f."
				""
				"在这些森林中,您可以找到赋予&a夜视&f效果的远古浆果."
				""
				"这也是唯一存在&a灵魂熔岩&f和猪灵奇兽的维度!"
			]
			hide_dependency_lines: false
			icon: "allthemodium:piglich_heart"
			id: "58E3D29E2E034BA2"
			rewards: [
				{
					count: 16
					id: "26A3F549CA3338F4"
					item: "allthemodium:ancient_soulberries"
					type: "item"
				}
				{
					id: "74B5009A2A31A73B"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				dimension: "allthemodium:the_other"
				id: "075FBA0F83548F6A"
				type: "dimension"
			}]
			title: "异界维度"
			x: 1.5d
			y: 7.0d
		}
		{
			dependencies: ["3C322474D2F2BA99"]
			description: [
				"&9&a挖矿维度&f&r拥有多个层级可供开采矿石!"
				""
				"该维度包含主世界常规的&3石头&r和&3深板岩&r层,还有用于开采下界矿石的&c下界岩&r层,以及最后用于开采末地矿石的&e末地石&r层."
			]
			hide_dependency_lines: false
			icon: {
				Count: 1
				id: "minecraft:stone_pickaxe"
				tag: {
					Damage: 0
				}
			}
			id: "7E8FE99A3C448413"
			rewards: [{
				id: "7B03C181BB8EB227"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				dimension: "allthemodium:mining"
				id: "7C1AC35E98DA4470"
				type: "dimension"
			}]
			title: "&a挖矿维度&f"
			x: -1.5d
			y: 7.0d
		}
		{
			description: ["需要更多&eATM金属&r？来养些蜜蜂吧!"]
			hide_dependency_lines: false
			id: "5D8A3491889F2C4E"
			rewards: [
				{
					id: "15475C2EF8192338"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "736E172147AD8566"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "diamond"
			size: 1.25d
			tasks: [{
				id: "1E0783DED2164C8D"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "productivebees:configurable_honeycomb"
								tag: {
									EntityTag: {
										type: "productivebees:allthemodium"
									}
								}
							}
							{
								Count: 1b
								id: "productivebees:configurable_honeycomb"
								tag: {
									EntityTag: {
										type: "productivebees:vibranium"
									}
								}
							}
							{
								Count: 1b
								id: "productivebees:configurable_honeycomb"
								tag: {
									EntityTag: {
										type: "productivebees:unobtainium"
									}
								}
							}
						]
					}
				}
				title: "ATM蜂巢"
				type: "item"
			}]
			title: "&e高效ATM蜜蜂"
			x: 3.0d
			y: 3.0d
		}
		{
			hide_until_deps_visible: true
			id: "2DB81CE6F647D08A"
			rewards: [{
				exclude_from_claim_all: true
				id: "4E13E4065EE46FBC"
				table_id: 5564196992594175882L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "7402ED40B70EE397"
				item: "allthemodium:unobtainium_allthemodium_alloy_ingot"
				type: "item"
			}]
			title: "&a稀有金属-ATM合金&f"
			x: 0.0d
			y: 10.5d
		}
		{
			hide_until_deps_visible: true
			id: "3E0A6D2FAEEF22A8"
			rewards: [{
				exclude_from_claim_all: true
				id: "0B5B60F08F952B31"
				table_id: 5564196992594175882L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "48EDC0316BE2986A"
				item: "allthemodium:unobtainium_vibranium_alloy_ingot"
				type: "item"
			}]
			title: "&a稀有金属-振金合金&f"
			x: 1.5d
			y: 10.0d
		}
		{
			hide_until_deps_visible: true
			id: "38135FFD9ED64395"
			rewards: [{
				exclude_from_claim_all: true
				id: "3EFE94A1B3D54CCA"
				table_id: 5564196992594175882L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "09214F39B42692F3"
				item: "allthemodium:vibranium_allthemodium_alloy_ingot"
				type: "item"
			}]
			title: "&a振金-ATM合金&f"
			x: -1.5d
			y: 10.0d
		}
		{
			dependencies: [
				"2DB81CE6F647D08A"
				"38135FFD9ED64395"
				"3E0A6D2FAEEF22A8"
			]
			hide_dependency_lines: false
			id: "7D3648FF86B0EB85"
			rewards: [{
				exclude_from_claim_all: true
				id: "57239B6424179212"
				table_id: 7025454341029952768L
				type: "random"
			}]
			shape: "pentagon"
			tasks: [{
				id: "1BD4860E0CC120FC"
				item: "allthemodium:alloy_sword"
				type: "item"
			}]
			title: "合金剑"
			x: -0.7000000000000001d
			y: 13.5d
		}
		{
			dependencies: [
				"2DB81CE6F647D08A"
				"38135FFD9ED64395"
				"3E0A6D2FAEEF22A8"
			]
			hide_dependency_lines: false
			id: "4881ABF8877BA572"
			rewards: [{
				exclude_from_claim_all: true
				id: "699606E4614B1E28"
				table_id: 7025454341029952768L
				type: "random"
			}]
			shape: "pentagon"
			tasks: [{
				id: "7585EE207A816B28"
				item: "allthemodium:alloy_axe"
				type: "item"
			}]
			title: "&a合金斧&f"
			x: 0.7000000000000001d
			y: 13.5d
		}
		{
			dependencies: [
				"2DB81CE6F647D08A"
				"38135FFD9ED64395"
				"3E0A6D2FAEEF22A8"
			]
			hide_dependency_lines: false
			id: "4F84C91128C9DCED"
			rewards: [{
				exclude_from_claim_all: true
				id: "3D768F8F20884784"
				table_id: 7025454341029952768L
				type: "random"
			}]
			shape: "pentagon"
			tasks: [{
				id: "0068F0000541A6E9"
				item: "allthemodium:alloy_pick"
				type: "item"
			}]
			title: "&a合金镐&f"
			x: -2.0d
			y: 13.5d
		}
		{
			dependencies: [
				"2DB81CE6F647D08A"
				"38135FFD9ED64395"
				"3E0A6D2FAEEF22A8"
			]
			hide_dependency_lines: false
			id: "2BD4E8494F2F43E9"
			rewards: [{
				exclude_from_claim_all: true
				id: "43087FBBFEB79B36"
				table_id: 7025454341029952768L
				type: "random"
			}]
			shape: "pentagon"
			tasks: [{
				id: "4B44E545FE264B84"
				item: "allthemodium:alloy_shovel"
				type: "item"
			}]
			title: "&a合金锹&f"
			x: 2.0d
			y: 13.5d
		}
		{
			dependencies: [
				"4881ABF8877BA572"
				"4F84C91128C9DCED"
				"2BD4E8494F2F43E9"
				"7D3648FF86B0EB85"
			]
			hide_dependency_lines: false
			id: "4AD2F0AC870672DB"
			rewards: [{
				exclude_from_claim_all: true
				id: "7E68266B0C71E310"
				table_id: 7175652334583451871L
				type: "random"
			}]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				id: "0E1B0C621A467BE0"
				item: "allthemodium:alloy_paxel"
				type: "item"
			}]
			title: "合金万能工具"
			x: 0.0d
			y: 12.000000000000002d
		}
		{
			dependencies: ["5BDBE666E604FCAC"]
			description: ["可通过在&d远古城市&r中&2刷洗&r&a可疑的黏土&r获得."]
			hide_dependency_lines: false
			id: "2BF9B347D1FC037A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "60583F6BAD10AF9A"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "6D990F0555B74E30"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.0d
			tasks: [{
				id: "1F249850998F505E"
				item: "allthemodium:allthemodium_upgrade_smithing_template"
				type: "item"
			}]
			title: "&e难得素升级"
			x: -1.5d
			y: 2.5d
		}
		{
			dependencies: ["2DF64CB9298E91EA"]
			description: ["可在&c下界&r的&d堡垒遗迹&r中,通过&2刷洗&r&a可疑的灵魂沙&f&r获得."]
			hide_dependency_lines: false
			id: "0C1EC499EB16C604"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2550EE645E0608DF"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "7FACE78C8932328C"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.0d
			tasks: [{
				id: "641B47453E890867"
				item: "allthemodium:vibranium_upgrade_smithing_template"
				type: "item"
			}]
			title: "&b振金升级"
			x: 0.0d
			y: 3.0d
		}
		{
			dependencies: ["4F6E6AF1D9E74CB7"]
			description: ["该物品可在其他&a地牢&r的图书馆战利品中找到."]
			hide_dependency_lines: false
			id: "1E92D4FEB8E96BBF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "288A32017FE80F19"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "5735B4806162ACBA"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.0d
			tasks: [{
				id: "0043CEE91EF992F7"
				item: "allthemodium:unobtainium_upgrade_smithing_template"
				type: "item"
			}]
			title: "&d无法获得素升级"
			x: 1.5d
			y: 2.5d
		}
		{
			dependencies: ["0C1EC499EB16C604"]
			hide_dependency_lines: false
			id: "28260B53A3F9E57D"
			rewards: [
				{
					id: "47EA865134159DDB"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "3924B2C4B6CDDFDA"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "square"
			size: 1.0d
			tasks: [{
				id: "7E5429C59943C661"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthearcanistgear:vibranium_boots"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 5
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:vibranium_robes"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 5
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:vibranium_leggings"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 5
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:vibranium_hat"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 5
									}
								}
							}
							{
								Count: 1b
								id: "allthewizardgear:vibranium_mage_helmet"
							}
							{
								Count: 1b
								id: "allthewizardgear:vibranium_mage_chestplate"
								tag: {
									ISB_Spells: {
										data: [ ]
										maxSpells: 1
										mustEquip: 1b
										spellWheel: 1b
									}
								}
							}
							{
								Count: 1b
								id: "allthewizardgear:vibranium_mage_leggings"
							}
							{
								Count: 1b
								id: "allthewizardgear:vibranium_mage_boots"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_leggings"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_boots"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_chestplate"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_helmet"
							}
						]
					}
				}
				title: "振金护甲"
				type: "item"
			}]
			title: "&b振金盔甲"
			x: 0.0d
			y: 4.5d
		}
		{
			dependencies: ["1E92D4FEB8E96BBF"]
			hide_dependency_lines: false
			id: "777B6100B321DAA6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4BA5947D1FD02BC6"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "03FE1794BB96A38B"
					type: "xp"
					xp: 250
				}
			]
			shape: "square"
			size: 1.0d
			tasks: [{
				id: "5457F63FC60BA231"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthearcanistgear:unobtainium_boots"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 6
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:unobtainium_leggings"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 6
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:unobtainium_robes"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 6
									}
								}
							}
							{
								Count: 1b
								id: "allthearcanistgear:unobtainium_hat"
								tag: {
									an_stack_perks: {
										color: ""
										perks: [ ]
										tier: 6
									}
								}
							}
							{
								Count: 1b
								id: "allthewizardgear:unobtainium_mage_leggings"
							}
							{
								Count: 1b
								id: "allthewizardgear:unobtainium_mage_boots"
							}
							{
								Count: 1b
								id: "allthewizardgear:unobtainium_mage_chestplate"
								tag: {
									ISB_Spells: {
										data: [ ]
										maxSpells: 1
										mustEquip: 1b
										spellWheel: 1b
									}
								}
							}
							{
								Count: 1b
								id: "allthewizardgear:unobtainium_mage_helmet"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_helmet"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_chestplate"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_leggings"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_boots"
							}
						]
					}
				}
				title: "难获金属护甲"
				type: "item"
			}]
			title: "&d无法获得素盔甲"
			x: 1.5d
			y: 4.0d
		}
		{
			dependencies: ["30E6C6825D78B5F1"]
			hide_dependency_lines: false
			id: "553DD7CBD4351A71"
			rewards: [
				{
					id: "45B79CB58D2BE6BB"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "7A1B7905F069FDE2"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			shape: "pentagon"
			size: 1.0d
			tasks: [{
				id: "2DB3FE929A70B1C9"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:vibranium_sword"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_pickaxe"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_axe"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_shovel"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_hoe"
							}
						]
					}
				}
				title: "振金工具"
				type: "item"
			}]
			title: "&b振金工具"
			x: 0.0d
			y: 6.0d
		}
		{
			dependencies: ["553DD7CBD4351A71"]
			hide_dependency_lines: false
			id: "37ACDA018D07A4DF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "45C747E322F5EA3E"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "2BA2A9B5741FA8B0"
					type: "xp"
					xp: 250
				}
			]
			shape: "pentagon"
			size: 1.0d
			tasks: [{
				id: "6AC1F79015239A46"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:unobtainium_sword"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_pickaxe"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_axe"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_shovel"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_hoe"
							}
						]
					}
				}
				title: "难获金属工具"
				type: "item"
			}]
			title: "&d无法获得素工具"
			x: 1.5d
			y: 5.5d
		}
		{
			dependencies: ["3C322474D2F2BA99"]
			description: [
				"位于&a末地&f边缘之外,是建造者们需要大量开阔工作区域时的绝佳选择."
				""
				"类似于主世界->下界，&a末地&f->超越之地的方块比例为1:50"
			]
			hide_dependency_lines: false
			icon: "voidtotem:totem_of_void_undying"
			id: "53DD784E75965947"
			optional: true
			rewards: [{
				id: "5E779E57482952D9"
				type: "xp"
				xp: 100
			}]
			tasks: [{
				dimension: "allthemodium:the_beyond"
				id: "5B673FC6B4C064ED"
				type: "dimension"
			}]
			title: "超越之地"
			x: 0.0d
			y: 9.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务被故意隐藏,若您能看到此内容,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "532048F1D6759FF6"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "2B5A232033859FBC"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "782BF0528469C296"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: -2.5d
		}
	]
	title: "全钯矿"
}
