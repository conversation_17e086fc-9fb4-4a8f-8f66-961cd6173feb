{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "extreme_reactors"
	group: "2B51AC12041E3F89"
	icon: "bigreactors:wrench"
	id: "3C78926E5D301BA0"
	images: [
		{
			height: 0.5d
			hover: ["合成&aATM之星&f所需"]
			image: "allthetweaks:item/atm_star"
			order: 1
			rotation: 0.0d
			width: 0.5d
			x: -2.0d
			y: 16.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/extremereactors/title2.png"
			rotation: 0.0d
			width: 10.156626506024097d
			x: -12.5d
			y: 12.0d
		}
		{
			height: 8.0d
			image: "atm:textures/questpics/extremereactors/titleimage2.png"
			order: -1
			rotation: 0.0d
			width: 10.4903078677309d
			x: -12.5d
			y: 9.0d
		}
		{
			height: 1.75d
			image: "ftbquests:tasks/input_only"
			rotation: 90.0d
			width: 1.75d
			x: -5.5d
			y: 8.0d
		}
		{
			color: 14679808
			height: 1.75d
			image: "ftbquests:tasks/input_only"
			rotation: 45.0d
			width: 1.75d
			x: -5.5d
			y: 8.0d
		}
		{
			height: 2.25d
			image: "forbidden_arcanus:block/clibano_combustion/soul_fire/clibano_center_front"
			rotation: 180.0d
			width: 2.25d
			x: -2.0d
			y: 15.5d
		}
		{
			height: 2.0d
			image: "bigreactors:fluid/fluid.fuelcolumn.flowing"
			rotation: 0.0d
			width: 2.0d
			x: -2.0d
			y: 15.5d
		}
		{
			height: 1.5d
			image: "bloodmagic:block/liquid_doubt_flowing"
			rotation: 0.0d
			width: 1.5d
			x: -0.5d
			y: 9.5d
		}
		{
			height: 2.0d
			image: "ftbquests:block/base_input"
			order: -1
			rotation: 0.0d
			width: 2.0d
			x: -0.5d
			y: 9.5d
		}
		{
			height: 2.0d
			image: "ftbquests:block/base_input"
			order: -1
			rotation: 45.0d
			width: 2.0d
			x: -0.5d
			y: 0.5d
		}
		{
			height: 1.5d
			image: "mob_grinding_utils:block/fan_front_on"
			order: -1
			rotation: 45.0d
			width: 1.5d
			x: -0.5d
			y: 0.5d
		}
	]
	order_index: 4
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"基于原版模组&e大型反应堆&r,&a&d极限反应堆&f&r允许你建造可定制的多方块反应堆!"
				""
				"核心元素当然是铀矿.你需要大量铀矿、煤炭和铁锭才能开始建造."
				""
				"若在过程中遇到困难,可参考&a&a极端&f手册&r获取帮助!"
			]
			id: "7C4E4793DA887DE4"
			rewards: [
				{
					id: "5D196EE2BB1E921E"
					type: "xp"
					xp: 10
				}
				{
					id: "1C213B4FE894781D"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "bigreactors:erguide"
						}
					}
					type: "item"
				}
			]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "7ECE44526077F3C9"
				item: "alltheores:uranium_ingot"
				type: "item"
			}]
			title: "欢迎来到&9&d极限反应堆&f&r!"
			x: -10.0d
			y: 4.0d
		}
		{
			dependencies: ["7C4E4793DA887DE4"]
			description: [
				"在建造反应堆前,我们需要先熔炼煤炭(或木炭)制作&9&a石墨锭&f&r."
				""
				"石墨与铁锭都是建造反应堆的核心材料."
			]
			id: "4FA6BEA4E646B742"
			rewards: [
				{
					id: "07500C1CA8341D60"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "6E406BC76180F481"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			subtitle: "硬化碳材"
			tasks: [{
				count: 16L
				id: "4B83A0D1C2C3C226"
				item: "bigreactors:graphite_ingot"
				type: "item"
			}]
			title: "外壳用石墨"
			x: -7.5d
			y: 4.0d
		}
		{
			dependencies: ["75AD0CEBC1335915"]
			description: [
				"我们将建造最小型的被动反应堆——&93x3x3&r规格.任务要求的材料数量正是建造所需的最低配置."
				""
				"首先用反应堆外壳搭建3x3x3框架,底层中心可继续使用&a反应堆外壳&f.每个外立面需安装&9反应堆组件&r,例如活性能量栓或&a固体访问端口&f."
				""
				"&a显示下一页&f!"
				""
				"{@pagebreak}"
				"所有反应堆都必须包含1个&6&a反应堆控制器&f&r(通常置于前壁中央).接着在多方块结构中心放置1根&a&a燃料棒&f&r,并在顶部对应位置安装1个&e&a控制杆&f&r."
				""
				"需配置废料进出口,使用&9反应堆&a固体访问端口&f&r实现.本建造方案中,请在左右两侧各安装1个."
				""
				"能量输出端需在后壁中央安装&c活性能量栓&r.放置完成后反应堆即可激活!现在可以&a右键点击&f&a控制器&f打开界面启动它."
				""
				"注:使用&a基础反应堆部件&r最大可建造5x5x5反应堆.若要建造更大规格,需使用&e强化反应堆部件&r."
				""
				"{@pagebreak}"
				"这是3x3x3反应堆的成品示意图!"
				""
				"{image:atm:textures/questpics/extremereactors/3x3sample.png width:150 height:150 align:1}"
			]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "bigreactors:block/reactor/reinforced/controller_off"
				}
			}
			id: "4AD8363D7359A072"
			min_width: 300
			rewards: [
				{
					id: "3FEA5D1C2E8907D8"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "0C32A80527EB8A8F"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "hexagon"
			size: 1.5d
			subtitle: "基础原理学习"
			tasks: [
				{
					count: 21L
					id: "70D705C81989D87C"
					item: "bigreactors:basic_reactorcasing"
					type: "item"
				}
				{
					id: "1D03F9FD56B01515"
					item: "bigreactors:basic_reactorcontroller"
					type: "item"
				}
				{
					count: 2L
					id: "2ADB404DAFBD7AC5"
					item: "bigreactors:basic_reactorsolidaccessport"
					type: "item"
				}
				{
					id: "6E7930A4B00563C9"
					item: "bigreactors:basic_reactorpowertapfe_active"
					type: "item"
				}
				{
					id: "1C89B070388F3ADF"
					item: "bigreactors:basic_reactorcontrolrod"
					type: "item"
				}
				{
					id: "368FDD7ECB8C06CD"
					item: "bigreactors:basic_reactorfuelrod"
					type: "item"
				}
			]
			title: "&d我们的第一座反应堆"
			x: -5.5d
			y: 8.0d
		}
		{
			dependencies: ["4FA6BEA4E646B742"]
			description: [
				"建造反应堆首先需要制作&e&a反应堆外壳&f&r."
				""
				"这些外壳构成反应堆的框架与墙体,也可用&9&a反应堆玻璃&f&r替代墙体以便观察内部."
				""
				"请注意:标注\"基础型\"的部件只能与其他基础部件搭配使用,且受尺寸限制仅能建造小型反应堆."
			]
			id: "4B9E9497E44D0096"
			rewards: [
				{
					id: "0303247B6A6C3F08"
					type: "xp"
					xp: 10
				}
				{
					count: 4
					id: "19C423870DAAA0DB"
					item: "bigreactors:basic_reactorcasing"
					random_bonus: 4
					type: "item"
				}
			]
			shape: "gear"
			size: 1.5d
			tasks: [
				{
					count: 4L
					id: "76E0779D896F146B"
					item: "bigreactors:basic_reactorcasing"
					type: "item"
				}
				{
					id: "4EA3FF4654F9D9FF"
					item: "bigreactors:basic_reactorglass"
					type: "item"
				}
			]
			title: "反应堆建筑组件"
			x: -5.5d
			y: 4.0d
		}
		{
			dependencies: ["4B9E9497E44D0096"]
			description: [
				"需要通过这些&c必备&r方块来实现反应堆的能量输出、物品输送及燃料注入."
				""
				"&c能源接口&r可从&9被动型&r反应堆\"导出\"能量,通过管道或电缆连接即可传输电力."
				""
				"每个反应堆都必须配备&a存取端口&r,既能注入燃料又可排出废料.建议每个反应堆配置两个端口各司其职."
			]
			id: "2A20000FAEC2E16A"
			rewards: [
				{
					count: 2
					id: "772EB41C198591C5"
					item: "bigreactors:basic_reactorcasing"
					random_bonus: 2
					type: "item"
				}
				{
					id: "1FA7A1D6E690613A"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "565CF24C6904CC2A"
					item: "bigreactors:basic_reactorpowertapfe_active"
					type: "item"
				}
				{
					id: "0649EBB6E6516B4C"
					item: "bigreactors:basic_reactorsolidaccessport"
					type: "item"
				}
			]
			title: "反应堆交互组件"
			x: -5.0d
			y: 5.5d
		}
		{
			dependencies: ["4B9E9497E44D0096"]
			description: [
				"&d&a反应堆控制器&f&r是整个反应堆的核心.当反应堆组装完成后,右键点击终端即可打开反应堆控制界面."
				""
				"根据反应堆类型是&9被动冷却型&r还是&e主动冷却型&r,界面会有所不同.被动冷却型反应堆通过直接燃烧燃料产生能量,而主动冷却型反应堆则利用产生的热量汽化冷却剂,随后将蒸汽输送至涡轮机发电."
				""
				"在被动冷却反应堆界面中,您可以查看并切换运行状态与废料排出设置.界面还会显示温度参数、当前FE/t产能以及反应堆&a每刻&f消耗的燃料量."
			]
			id: "75AD0CEBC1335915"
			min_width: 300
			rewards: [
				{
					count: 2
					id: "2975B436F7D91A8A"
					item: "bigreactors:basic_reactorcasing"
					random_bonus: 2
					type: "item"
				}
				{
					id: "38C141F047926833"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "5508199460B01082"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			subtitle: "反应堆的&a心脏&f"
			tasks: [{
				id: "62A0FE4CFAB9621B"
				item: "bigreactors:basic_reactorcontroller"
				type: "item"
			}]
			x: -5.5d
			y: 6.0d
		}
		{
			dependencies: ["4B9E9497E44D0096"]
			description: [
				"每个反应堆都需要配备&9&a反应堆控制棒&f&r和&9&a燃料棒&f&r,这些组件控制着燃料如何注入反应堆."
				""
				"&a控制杆&f需安装在反应堆顶部.单个反应堆可配置多根控制杆,但至少需要1根.通常控制杆数量越多,反应堆燃烧燃料的速率就越高——这意味着总产能提升,但具体效果取决于反应堆的整体配置."
				""
				"每根&a控制杆&f都需要配备足够数量的&a燃料棒&f延伸至反应堆底部.例如5格高的反应堆,每根控制杆需要连接3根&a燃料棒&f."
				""
				"右键点击&a控制杆&f可通过调节燃料棒伸缩幅度来控制燃料燃烧速率:燃料棒伸出越长,燃料消耗速率越低."
			]
			id: "7B4AAC741F0A6073"
			min_width: 300
			rewards: [
				{
					count: 2
					id: "3165C37A9C6F4AA4"
					item: "bigreactors:basic_reactorcasing"
					random_bonus: 2
					type: "item"
				}
				{
					id: "332E2A363D91F6E9"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "1FC2120A22A7BAEC"
					item: "bigreactors:basic_reactorcontrolrod"
					type: "item"
				}
				{
					id: "14C67262D9F8A9A8"
					item: "bigreactors:basic_reactorfuelrod"
					type: "item"
				}
			]
			title: "&a反应堆控制棒&f"
			x: -6.0d
			y: 5.5d
		}
		{
			dependencies: ["4AD8363D7359A072"]
			description: [
				"向反应堆添加燃料时,需选择装有&9反应堆&a固体访问端口&f&r的侧面,从容器中泵入&e铀燃料&r."
				""
				"最简便的方法是使用&a&a存储抽屉&f&r或普通&a箱子&r,顶部连接&9物品管道&r(如下图所示)."
				""
				"{image:atm:textures/questpics/extremereactors/importexample.png width:150 height:150 align:1}"
			]
			id: "14E5349DD740D026"
			min_width: 400
			progression_mode: "linear"
			rewards: [
				{
					id: "55FBD6A7422569AB"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "29C27647CB5FC0F6"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				count: 4L
				id: "159251A23C881D83"
				item: "alltheores:uranium_ingot"
				type: "item"
			}]
			title: "为被动式反应堆供能"
			x: -7.5d
			y: 8.0d
		}
		{
			dependencies: ["4AD8363D7359A072"]
			description: ["反应堆燃烧燃料时会产生需要排出的&9废料&r或&d反应副产物&r,这正是另一个&a固体访问端口&f的用途!请确保将其设置为输出模式,并通过管道导入存储设备."]
			id: "4745152F6FF242B3"
			rewards: [
				{
					id: "3C59017024A58441"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "2A7EFCB386FBB78B"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			subtitle: "&a物尽其用&f 之类的..."
			tasks: [{
				id: "77F79D09A76CFF15"
				item: "bigreactors:cyanite_ingot"
				type: "item"
			}]
			title: "废料处理"
			x: -3.0d
			y: 8.0d
		}
		{
			dependencies: ["4745152F6FF242B3"]
			description: [
				"现在我们的小型反应堆已产出一些蓝晶石,接下来需要将其转化为有用物质."
				""
				"这需要建造另一个多方块结构:&a再处理机&r."
				""
				"该过程消耗大量蓝晶石,建议提前储备!您可能还需要升级更大规格的反应堆."
			]
			id: "354086C858E10154"
			rewards: [
				{
					id: "601D8AF8D45F9818"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "080EADDE6EB76EAC"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				id: "0577F2FA32B65E00"
				item: "bigreactors:reprocessorcasing"
				type: "item"
			}]
			title: "废料再处理"
			x: -0.5d
			y: 8.0d
		}
		{
			description: [
				"利用蓝晶石可以制造涡轮机的核心——&9涡轮控制器&r."
				""
				"涡轮机与反应堆同属多方块结构!它们通过处理&d主动冷却型&r反应堆产生的&7蒸汽&r等气态物质来产生海量能源.建造第一台涡轮机还需要其他若干组件."
				""
				"注:&a基本型涡轮机&f部件仅能建造最大5x5x10规格的涡轮机.若要建造更大规格,必须使用&a&a加强型涡轮机&f部件&r."
			]
			id: "4415C9F8DA2D7E68"
			rewards: [
				{
					count: 2
					id: "6EE855A0C663EDDF"
					item: "bigreactors:basic_turbinecasing"
					random_bonus: 2
					type: "item"
				}
				{
					id: "5C20A5831F6F1EE9"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "01FB731CE2FD9481"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "hexagon"
			tasks: [
				{
					count: 4L
					id: "3FD4E648D3560D1B"
					item: "bigreactors:basic_turbinecasing"
					type: "item"
				}
				{
					id: "738B3094737D9A6A"
					item: "bigreactors:basic_turbinecontroller"
					type: "item"
				}
			]
			title: "涡轮机制造"
			x: -0.5d
			y: 5.5d
		}
		{
			dependencies: ["4AD8363D7359A072"]
			description: [
				"反应堆&9慢化剂&r是建造时放置在反应堆&l内部&r的材料,能根据其物理特性改变反应堆性能."
				""
				"通常材料越稀有,慢化效果越好.&a保留&f反应堆内部空置意味着使用空气作为慢化剂,效果较差."
				""
				"前期推荐使用性价比高的&3&a石墨块&f&r作为慢化剂!"
			]
			id: "73362EDC984B8A0F"
			min_width: 300
			progression_mode: "linear"
			rewards: [
				{
					id: "56D7ED254FC3A540"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "6150F69F6D20EBFB"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			subtitle: "它们是慢化剂,不是冷却剂"
			tasks: [{
				id: "0D4AE8FBAA953732"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "minecraft:iron_block"
							}
							{
								Count: 1b
								id: "minecraft:gold_block"
							}
							{
								Count: 1b
								id: "minecraft:emerald_block"
							}
							{
								Count: 1b
								id: "minecraft:diamond_block"
							}
							{
								Count: 1b
								id: "minecraft:netherite_block"
							}
							{
								Count: 1b
								id: "minecraft:copper_block"
							}
							{
								Count: 1b
								id: "alltheores:platinum_block"
							}
							{
								Count: 1b
								id: "alltheores:osmium_block"
							}
							{
								Count: 1b
								id: "alltheores:nickel_block"
							}
							{
								Count: 1b
								id: "alltheores:lead_block"
							}
							{
								Count: 1b
								id: "alltheores:aluminum_block"
							}
							{
								Count: 1b
								id: "alltheores:silver_block"
							}
							{
								Count: 1b
								id: "alltheores:tin_block"
							}
							{
								Count: 1b
								id: "alltheores:zinc_block"
							}
							{
								Count: 1b
								id: "alltheores:steel_block"
							}
							{
								Count: 1b
								id: "alltheores:invar_block"
							}
							{
								Count: 1b
								id: "alltheores:electrum_block"
							}
							{
								Count: 1b
								id: "alltheores:bronze_block"
							}
							{
								Count: 1b
								id: "alltheores:enderium_block"
							}
							{
								Count: 1b
								id: "alltheores:lumium_block"
							}
							{
								Count: 1b
								id: "alltheores:signalum_block"
							}
							{
								Count: 1b
								id: "alltheores:brass_block"
							}
							{
								Count: 1b
								id: "botania:manasteel_block"
							}
							{
								Count: 1b
								id: "botania:terrasteel_block"
							}
							{
								Count: 1b
								id: "botania:elementium_block"
							}
							{
								Count: 1b
								id: "bigreactors:graphite_block"
							}
						]
					}
				}
				title: "示范性减速剂"
				type: "item"
			}]
			title: "反应堆慢化剂"
			x: -7.0d
			y: 9.0d
		}
		{
			dependencies: [
				"4415C9F8DA2D7E68"
				"4745152F6FF242B3"
			]
			description: [
				"反应堆还可用于加热&b冷却剂&r(例如水)以产生&b蒸汽&r,如&b高压蒸汽&r."
				""
				"为此,您需要建造一个强化反应堆.其建造方式与3x3x3反应堆相同,但所有部件都需替换为&a强化反应堆部件&r.建议尺寸大于3x3x3."
				""
				"输入冷却剂需要&9锻造厂&a流体端口&f&r.该端口可将水等流体导入反应堆,同时用于导出反应堆产生的蒸汽."
				""
				"如需转换流体类型,可建造&a&d通用机械&f &a流体端口&f&r,将流体蒸汽转化为&d通用机械&f气态蒸汽."
			]
			id: "476755275B948A5F"
			min_width: 300
			rewards: [
				{
					id: "450F59D9CB0FEF7A"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "44BF0BA518FE9E1D"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			subtitle: "蒸汽制造指南"
			tasks: [
				{
					id: "122E3BB7DB314F68"
					item: "bigreactors:reinforced_reactorcasing"
					type: "item"
				}
				{
					id: "3A52CAA0728D629A"
					item: "bigreactors:reinforced_reactorfluidport_forge_active"
					type: "item"
				}
			]
			title: "建造主动冷却反应堆"
			x: -3.0d
			y: 5.5d
		}
		{
			dependencies: ["4AD8363D7359A072"]
			description: [
				"反应堆为多方块结构,可自由定制尺寸!"
				""
				"使用&a基础反应堆部件&r时,最大可建造5x5x5反应堆."
				""
				"采用&e强化反应堆部件&r可建造&l巨型&r反应堆(32x32x48).反应堆输出受多重变量影响,请务必进行实验验证!"
				""
				"核心建造建议:"
				""
				"高度决定产能:更高的反应堆容纳更多燃料棒,提升总功率输出,但&c燃烧速率&r同步增加"
				""
				"宽度提升效率:在燃料棒数量不变前提下,更宽的反应堆结构能显著降低整体能耗."
			]
			id: "3F9D553C9FA64F2A"
			min_width: 300
			progression_mode: "linear"
			rewards: [
				{
					id: "2D71A6EF1CA59FA7"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "52181A03434A605B"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				count: 16L
				id: "52294DB4AE793F9C"
				item: "bigreactors:basic_reactorcasing"
				type: "item"
			}]
			title: "反应堆扩容方案"
			x: -7.0d
			y: 7.0d
		}
		{
			dependencies: ["4415C9F8DA2D7E68"]
			description: [
				"涡轮机运作需要配置多种&e功能端口&r."
				""
				"&9&a流体端口&f&r用于注入&b蒸汽&r等气态介质,或排出&9冷却水&r等废液,因此每个涡轮机需配备两个."
				""
				"&c能量输出端&r用于提取电力,是构成多方块结构的必备组件."
			]
			id: "186731580B14F9D2"
			rewards: [
				{
					count: 4
					id: "6F76A59F202AD944"
					item: "bigreactors:basic_turbinecasing"
					random_bonus: 4
					type: "item"
				}
				{
					id: "7BC028EFCCFAF39A"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [
				{
					count: 2L
					id: "5E9CEEEE330B1DE0"
					item: "bigreactors:basic_turbinefluidport_forge_active"
					type: "item"
				}
				{
					id: "18EB6570007F534A"
					item: "bigreactors:basic_turbinepowertapfe_active"
					type: "item"
				}
			]
			title: "涡轮机接口系统"
			x: -1.5d
			y: 3.0d
		}
		{
			dependencies: ["4415C9F8DA2D7E68"]
			description: [
				"要使涡轮机运转,这些&c必备&r组件缺一不可:"
				""
				"- &9转子轴承&r安装在涡轮轴端,可置于任意面但决定主轴延伸方向.通常安装在底面中心位置."
				""
				"- &e转子轴&r从转子轴承延伸至涡轮机另一侧,直达单个涡轮机外壳方块,构成涡轮机的传动轴."
				""
				"- &9转子叶片&r驱动转子旋转.这些叶片安装在转子轴上,长度可跨越多个方块.每片叶片能处理特定量的蒸汽,所需数量取决于反应堆'的产出速率."
				""
				"这是涡轮机垂直轴配置的示例,顶部装有铅制涡轮线圈."
				""
				"{image:atm:textures/questpics/extremereactors/maxbasicturbine.png width:100 height:150 align:1}"
			]
			id: "67AFCBCE7AAC3089"
			min_width: 300
			rewards: [
				{
					id: "433D5587FE499E95"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2EAE5391E041D455"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "diamond"
			tasks: [
				{
					id: "69497C419271A8F2"
					item: "bigreactors:basic_turbinerotorbearing"
					type: "item"
				}
				{
					count: 4L
					id: "05559BFC34BEBF4A"
					item: "bigreactors:basic_turbinerotorshaft"
					type: "item"
				}
				{
					count: 8L
					id: "42F16075D25E4A94"
					item: "bigreactors:basic_turbinerotorblade"
					type: "item"
				}
			]
			title: "制作&a涡轮机&f转轴"
			x: -0.5d
			y: 2.0d
		}
		{
			dependencies: ["4415C9F8DA2D7E68"]
			description: [
				"&d涡轮线圈&r需环绕涡轮轴末端(靠近外壳方块)布置,是将机械能转化为电能的核心部件.每个涡轮机最多配置3组线圈,支持不同型号线圈混搭."
				""
				"本任务要求至少安装一种合格线圈."
			]
			id: "3FC7FDAF84871963"
			progression_mode: "linear"
			rewards: [
				{
					id: "54346236C9443772"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "2719368F51041BAD"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "5D6A9AD111A612EE"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "minecraft:iron_block"
							}
							{
								Count: 1b
								id: "minecraft:gold_block"
							}
							{
								Count: 1b
								id: "minecraft:netherite_block"
							}
							{
								Count: 1b
								id: "minecraft:copper_block"
							}
							{
								Count: 1b
								id: "alltheores:platinum_block"
							}
							{
								Count: 1b
								id: "alltheores:osmium_block"
							}
							{
								Count: 1b
								id: "alltheores:nickel_block"
							}
							{
								Count: 1b
								id: "alltheores:lead_block"
							}
							{
								Count: 1b
								id: "alltheores:aluminum_block"
							}
							{
								Count: 1b
								id: "alltheores:silver_block"
							}
							{
								Count: 1b
								id: "alltheores:tin_block"
							}
							{
								Count: 1b
								id: "alltheores:zinc_block"
							}
							{
								Count: 1b
								id: "alltheores:steel_block"
							}
							{
								Count: 1b
								id: "alltheores:invar_block"
							}
							{
								Count: 1b
								id: "alltheores:electrum_block"
							}
							{
								Count: 1b
								id: "alltheores:bronze_block"
							}
							{
								Count: 1b
								id: "alltheores:enderium_block"
							}
							{
								Count: 1b
								id: "alltheores:lumium_block"
							}
							{
								Count: 1b
								id: "alltheores:signalum_block"
							}
							{
								Count: 1b
								id: "alltheores:brass_block"
							}
							{
								Count: 1b
								id: "botania:manasteel_block"
							}
							{
								Count: 1b
								id: "botania:terrasteel_block"
							}
							{
								Count: 1b
								id: "botania:elementium_block"
							}
							{
								Count: 1b
								id: "bigreactors:ludicrite_block"
							}
							{
								Count: 1b
								id: "bigreactors:ridiculite_block"
							}
							{
								Count: 1b
								id: "bigreactors:inanite_block"
							}
							{
								Count: 1b
								id: "bigreactors:insanite_block"
							}
						]
					}
				}
				title: "涡轮线圈组"
				type: "item"
			}]
			title: "涡轮线圈阵列"
			x: 0.5d
			y: 3.0d
		}
		{
			dependencies: [
				"3FC7FDAF84871963"
				"67AFCBCE7AAC3089"
				"186731580B14F9D2"
				"775D176081DD75F5"
			]
			description: [
				"现阶段不建议建造最小型号涡轮机,应根据&9反应堆&r规格进行定制化设计."
				""
				"初级建造原则:涡轮机的&9蒸汽处理能力&r需匹配反应堆产能.为获得最大输出功率,需将转速稳定维持在900RPM或1800RPM.这需要通过反复测试不同线圈组合、叶片数量及整体尺寸来实现!"
				""
				"任务提示:建造任意尺寸的&d&a目标显示器&f&r,右键屏幕选择本任务作为验证条件,向任务屏幕输入能量直至进度条填满即可完成."
			]
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "bigreactors:block/turbine/basic/controller_off"
				}
			}
			id: "4ED36AA3766E842B"
			min_width: 300
			rewards: [
				{
					id: "5AE542B84586D0BA"
					type: "xp"
					xp: 500
				}
				{
					exclude_from_claim_all: true
					id: "6612F39435CC428F"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "square"
			size: 1.5d
			tasks: [
				{
					id: "4A94CC9DA47A370C"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "ftbquests:screen_1"
								}
								{
									Count: 1b
									id: "ftbquests:screen_3"
								}
								{
									Count: 1b
									id: "ftbquests:screen_5"
								}
								{
									Count: 1b
									id: "ftbquests:screen_7"
								}
							]
						}
					}
					title: "Quest &a目标显示器&f"
					type: "item"
				}
				{
					id: "24146672439051F5"
					max_input: 1000000L
					type: "forge_energy"
					value: 1000000L
				}
			]
			title: "&d首台涡轮机组装"
			x: -0.5d
			y: 0.5d
		}
		{
			dependencies: ["4415C9F8DA2D7E68"]
			description: ["与反应堆类似,框架必须使用&d强化外壳&r建造,但侧壁可替换为&9&a涡轮机专用玻璃&f&r!"]
			id: "2D592669F4D41793"
			rewards: [
				{
					count: 2
					id: "01C09E75947CCBF5"
					item: "bigreactors:basic_turbineglass"
					random_bonus: 2
					type: "item"
				}
				{
					id: "46A906727C46868C"
					type: "xp"
					xp: 10
				}
			]
			shape: "diamond"
			tasks: [{
				count: 4L
				id: "2FC2A5929C2E0EA9"
				item: "bigreactors:basic_turbineglass"
				type: "item"
			}]
			title: "涡轮机构造规范"
			x: -0.5d
			y: 4.0d
		}
		{
			dependencies: ["4415C9F8DA2D7E68"]
			description: [
				"当涡轮机完全建造后,右键点击其&a控制器&f可显示涡轮机操作界面."
				""
				"此处可查看涡轮机的全部统计数据.将鼠标悬停在各项目上可获取详细信息."
				""
				"左下角有两个箭头用于调节&9流量速率&r,控制输入涡轮机的热蒸汽量.设定时请以反应堆'的&d蒸汽产出速率&r为基准参考."
				""
				"{image:atm:textures/questpics/extremereactors/turbineui.png width:200 height:150 align:1}"
			]
			id: "775D176081DD75F5"
			min_width: 400
			rewards: [{
				id: "7CD6A1F962D2C310"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "2905C4386A407A1E"
				title: "&a涡轮机&f控制界面"
				type: "checkmark"
			}]
			x: -0.5d
			y: 3.0d
		}
		{
			dependencies: ["354086C858E10154"]
			description: [
				"&a核废料再处理器&r为3x3x7多方块结构,需遵循特定建造规则."
				""
				"该结构的核心是&a主控制器&r,可安装在除框架外的任意垂直面上."
			]
			id: "2AF31F1769085641"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "61047858BC63EC82"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "5FAF55F5CF1BA345"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "2DBC9E20BFE7F058"
				item: "bigreactors:reprocessorcontroller"
				type: "item"
			}]
			x: -0.5d
			y: 9.5d
		}
		{
			dependencies: ["2AF31F1769085641"]
			description: [
				"建造&a再处理器&r框架需要大量外壳方块,这意味着需要大量蓝晶石."
				""
				"首先搭建3格宽、3格深、7格高的空心结构作为基础框架."
				""
				"正确建造后,上下表面中心应留有空位.垂直面可使用&a再处理器玻璃&r或必需部件如&a能量端口&f、控制器等."
				""
				"如需查看框架示意图,请翻阅下一页!"
				""
				"{@pagebreak}"
				"再处理器多方块结构框架示意图."
				""
				"{image:atm:textures/questpics/extremereactors/reprocessorframe.png width:100 height:175 align:1}"
				""
				"{@pagebreak}"
				"完整建造的再处理器."
				""
				""
				"{image:atm:textures/questpics/extremereactors/reprocessorfull.png width:100 height:150 align:1}"
			]
			id: "69642A3618E86DED"
			rewards: [
				{
					id: "5C7121F82848C273"
					type: "xp"
					xp: 50
				}
				{
					exclude_from_claim_all: true
					id: "6FBCFDF7DDF64A60"
					table_id: 5564196992594175882L
					type: "loot"
				}
			]
			tasks: [{
				count: 52L
				id: "5DF9AA03A22C4F77"
				item: "bigreactors:reprocessorcasing"
				type: "item"
			}]
			title: "建造&a支撑框架&f"
			x: -0.5d
			y: 11.0d
		}
		{
			dependencies: ["2AF31F1769085641"]
			description: [
				"建造&a再处理器&r时,你至少需要一个&e收集器&r和&9废物注入器&r."
				""
				"&e再处理器收集器&r必须放置在结构底面的中心位置."
				""
				"&9废物注入器&r需安装在顶面中心,此处用于输入或插入废物,例如&9&a蓝晶锭&f&r."
			]
			id: "2598273041353196"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "52893569CB2AE712"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "18F8A508E2A08534"
					type: "xp"
					xp: 10
				}
			]
			tasks: [
				{
					id: "72B74F6051CD53A1"
					item: "bigreactors:reprocessorcollector"
					type: "item"
				}
				{
					id: "221816504F2573A5"
					item: "bigreactors:reprocessorwasteinjector"
					type: "item"
				}
			]
			title: "废物导入"
			x: -2.0d
			y: 9.5d
		}
		{
			dependencies: ["2AF31F1769085641"]
			description: [
				"虽然其他&a再处理器部件&r在建造时有固定位置,但这三个部件只要不装在框架上,可以任意安装在垂直面上!"
				""
				"&c&a能量端口&f&r用于为多方块机器供电以处理废物."
				""
				"&9&a流体注入器&f端口&r用于注入所需液体,具体取决于废物类型.处理蓝晶石时需要水!"
				""
				"&a输出端口&r用于输出再处理后的材料.你可以&a右键点击&f手动取出,或通过管道实现自动化输出."
			]
			id: "3C3FE45CEF5E242B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6261A65DF856A6A7"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "6FE2DA9F00DF8EED"
					type: "xp"
					xp: 10
				}
			]
			tasks: [
				{
					id: "475BD29336FB98E8"
					item: "bigreactors:reprocessorpowerport"
					type: "item"
				}
				{
					id: "530111623A8C7C58"
					item: "bigreactors:reprocessorfluidinjector"
					type: "item"
				}
				{
					id: "41D67C2BF92A876A"
					item: "bigreactors:reprocessoroutputport"
					type: "item"
				}
			]
			x: 1.0d
			y: 9.5d
		}
		{
			dependencies: ["69642A3618E86DED"]
			description: [
				"建成功能完整的&a再处理器&r后,可输入电力、水和&9蓝晶石&r来生产&d钚铀合金&r."
				""
				"该产物可作为反应堆燃料,并会产生名为&9磁铁矿&r的副产物."
			]
			id: "7E07C5A6FA6B6B1F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7B170A7928434E99"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "1437F7CBCB818A17"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "37BB2B7853E319A3"
				item: "bigreactors:blutonium_ingot"
				type: "item"
			}]
			x: -0.5d
			y: 12.5d
		}
		{
			dependencies: ["4AD8363D7359A072"]
			description: [
				"既然已从反应堆收集到&9废物&r,进阶流程需要你将获得的锭块进行\"流体化\"处理.知道这意味着什么吗？"
				""
				"我们需要建造&a流体化装置&r!核心部件是&a流体化控制器&r.建成后右键点击可打开界面,查看内容物、当前电力等级,并控制开关."
			]
			id: "25D4406CB86C8CBB"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "79B399A941B2BAE4"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "0A9859E493DA20A2"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "4A123A454CDD7120"
				item: "bigreactors:fluidizercontroller"
				type: "item"
			}]
			title: "&a流体&f化装置"
			x: -4.0d
			y: 9.5d
		}
		{
			dependencies: ["25D4406CB86C8CBB"]
			description: ["&a流体化装置&r是可定制的3x3x3最小规格多方块结构.与其他多方块机器相同,框架需使用外壳,而表面可用玻璃构成."]
			id: "501C6B7580453410"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "31DAA5C4FB8A443F"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "739312C574871639"
					type: "xp"
					xp: 10
				}
			]
			tasks: [
				{
					count: 4L
					id: "1088144C68D87424"
					item: "bigreactors:fluidizercasing"
					type: "item"
				}
				{
					id: "36D35DE85B71D5EC"
					item: "bigreactors:fluidizerglass"
					type: "item"
				}
			]
			title: "流体化装置构造"
			x: -5.0d
			y: 11.5d
		}
		{
			dependencies: ["25D4406CB86C8CBB"]
			description: [
				"&a流体化装置&r有三种模式:固态转流体、双固态合成流体、双流体合成新流体.具体模式取决于使用的&a注入器&r类型."
				""
				"例如要将&d钚铀合金&r流体化,需使用1个&a固态注入器&r."
				""
				"若要融合两种固态物质,需配置2个&a固态注入器&r."
				""
				"融合两种流体则需2个&9&a流体注入器&f&r."
				""
				"虽然机制复杂,但对流程推进至关重要.例如需先在流体化装置将磁铁矿转化为流体,再与卢德克矿在&a再处理器&r中合成滑稽矿."
			]
			id: "5914D015D8543875"
			min_width: 400
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0A7A5CA3C15445E0"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "3858004802200429"
					type: "xp"
					xp: 25
				}
			]
			tasks: [
				{
					id: "730D5AE0E7A04CFB"
					item: "bigreactors:fluidizersolidinjector"
					type: "item"
				}
				{
					id: "4A714BB3605492B9"
					item: "bigreactors:fluidizerfluidinjector"
					type: "item"
				}
			]
			title: "运作模式"
			x: -3.0d
			y: 11.5d
		}
		{
			dependencies: ["25D4406CB86C8CBB"]
			description: [
				"若要获取&a流体化装置&r产物,需在任意面安装&a输出端口&r."
				""
				"该装置需电力驱动,因此必须配置&c&a能量端口&f&r才能完成结构."
			]
			id: "55DCA040C84DCEF3"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1AAD62FA59F57405"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "79AC0F7350F14F50"
					type: "xp"
					xp: 25
				}
			]
			tasks: [
				{
					id: "52C86777BD3D8867"
					item: "bigreactors:fluidizeroutputport"
					type: "item"
				}
				{
					id: "32703B3E70D87917"
					item: "bigreactors:fluidizerpowerport"
					type: "item"
				}
			]
			title: "必备端口"
			x: -4.0d
			y: 11.5d
		}
		{
			dependencies: ["7E07C5A6FA6B6B1F"]
			description: [
				"通过&a再处理器&r,我们可以组合现有材料制作新锭块."
				""
				"注意:此步骤可能需要&9流体化装置&r参与!"
			]
			id: "5A615BB74A5CD332"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "31C7AA56815F4583"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "0694F5807F48509A"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "184BDD80596398A0"
					item: "bigreactors:ludicrite_ingot"
					type: "item"
				}
				{
					id: "0C1C624F8D84DE63"
					item: "bigreactors:ridiculite_ingot"
					type: "item"
				}
			]
			x: -0.5d
			y: 14.0d
		}
		{
			dependencies: [
				"5914D015D8543875"
				"55DCA040C84DCEF3"
				"501C6B7580453410"
			]
			description: [
				"使用&a流体化装置&r,我们可以将&d蓝铀矿&r与&e黄铀矿&r结合制成&2绿铀矿&r."
				""
				"当&2绿铀矿&r作为反应堆燃料时,会产生&c罗西尼特&r作为反应产物.这正是我们需要的!"
				""
				"要将&2绿铀矿&r用作燃料,你需要为反应堆制作&c燃料注入端口&r."
				"注意:你可能需要清空反应堆现有燃料,或为此新建一个反应堆."
			]
			id: "7C4D8AA107780795"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3BD7DA0DA8DB5C19"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "7B3F79DA7988E2AC"
					type: "xp"
					xp: 25
				}
			]
			shape: "rsquare"
			size: 1.5d
			tasks: [{
				id: "52B5D97C5675BBA7"
				item: "bigreactors:reinforced_reactorfluidaccessport"
				type: "item"
			}]
			title: "罗西尼特"
			x: -4.0d
			y: 14.0d
		}
		{
			dependencies: [
				"7C4D8AA107780795"
				"5A615BB74A5CD332"
			]
			description: [
				"在&a再处理器&r中将&9滑稽矿锭&r与&c罗西尼特&r结合,可制成&d虚无矿锭&r."
				""
				"现在我们可以用这些材料制作&d虚无矿&r方块."
			]
			id: "6ADDFD55AD0DF7D4"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "111AA42FC55BC26C"
					table_id: 7025454341029952768L
					type: "loot"
				}
				{
					id: "7FD3F44901EA7315"
					type: "xp"
					xp: 1000
				}
			]
			tasks: [{
				id: "544C4964F5B857B1"
				item: "bigreactors:inanite_ingot"
				type: "item"
			}]
			x: -2.0d
			y: 14.0d
		}
		{
			dependencies: [
				"0B1DF8A040826D87"
				"6ADDFD55AD0DF7D4"
			]
			description: [
				"本模组中最难获取的材料之一!"
				""
				"这也是制作&e&aATM之星&f&r的必备材料!"
			]
			id: "5AD80D3242DD3F60"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1F7036222F535785"
					table_id: 7175652334583451871L
					type: "loot"
				}
				{
					id: "01A038407420AF20"
					type: "xp"
					xp: 1000
				}
			]
			shape: "pentagon"
			size: 2.0d
			tasks: [{
				id: "53B5B370425018EC"
				item: "bigreactors:insanite_block"
				type: "item"
			}]
			title: "&d疯狂矿方块"
			x: -2.0d
			y: 15.5d
		}
		{
			dependencies: ["7C4D8AA107780795"]
			description: [
				"现在你有了&c罗西尼特&r,可以将其与&a蓝锥矿&r混合制成&d疯狂矿锭&r."
				""
				"&a&a蓝锥矿&f&r可在&a下界&f中找到."
			]
			id: "0B1DF8A040826D87"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "13B2AA15441877E6"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "2065E13D53308BF3"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "22922E479FED9E43"
				item: "bigreactors:insanite_ingot"
				type: "item"
			}]
			x: -4.0d
			y: 15.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经明确授权,该任务不得用于&eAllTheMods团队&r未发布的任何公开整合包."
				""
				""
				""
				"该任务默认隐藏,若你看到此提示,说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "578D5EF99F7D0E5A"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "65880A531196005C"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
				{
					id: "1A5E18C3212E9B3F"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
			]
			x: -10.0d
			y: 2.5d
		}
	]
	title: "&d极限反应堆&f"
}
