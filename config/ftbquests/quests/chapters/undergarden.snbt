{
	autofocus_id: "1C232674CD04024D"
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "undergarden"
	group: "752CDE464613A1ED"
	icon: "undergarden:catalyst"
	id: "65A95C2312976E49"
	images: [
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_title.png"
			rotation: 0.0d
			width: 17.434343434343436d
			x: 2.0d
			y: -11.0d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/undergarden/undergarden_vegetation.png"
			rotation: 0.0d
			width: 6.052631578947368d
			x: 7.0d
			y: -9.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/undergarden/undergarden_biomes.png"
			rotation: 0.0d
			width: 3.6315789473684212d
			x: -4.0d
			y: -9.5d
		}
		{
			height: 1.5d
			image: "atm:textures/questpics/undergarden/undergarden_neutral.png"
			rotation: 0.0d
			width: 7.611940298507463d
			x: 2.0d
			y: -3.0d
		}
		{
			height: 1.5d
			image: "atm:textures/questpics/undergarden/undergarden_hostile.png"
			rotation: 0.0d
			width: 5.940789473684211d
			x: 9.0d
			y: -3.5d
		}
		{
			height: 1.5d
			image: "atm:textures/questpics/undergarden/undergarden_friendly.png"
			rotation: 0.0d
			width: 6.592592592592593d
			x: -4.5d
			y: -3.5d
		}
		{
			height: 1.0d
			image: "atm:textures/questpics/undergarden/undergarden_tools.png"
			rotation: 0.0d
			width: 10.257142857142858d
			x: 2.0d
			y: 2.5d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_gloomper.png"
			rotation: 0.0d
			width: 2.301980198019802d
			x: -1.0d
			y: -0.5d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_brute.png"
			rotation: 0.0d
			width: 2.0d
			x: 5.0d
			y: -0.5d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_rotling.png"
			rotation: 0.0d
			width: 1.5617715617715617d
			x: 7.5d
			y: 2.5d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/undergarden/undergarden_rotwalker.png"
			rotation: 0.0d
			width: 1.478527607361963d
			x: 9.0d
			y: 3.0d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/undergarden/undergarden_rotbeast.png"
			rotation: 0.0d
			width: 2.8649706457925634d
			x: 11.0d
			y: 3.0d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/undergarden/undergarden_guardian.png"
			rotation: 0.0d
			width: 2.0809248554913293d
			x: 13.0d
			y: 2.0d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_muncher.png"
			rotation: 0.0d
			width: 1.8853868194842407d
			x: 12.5d
			y: -2.0d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_stoneborn.png"
			rotation: 0.0d
			width: 1.0316091954022988d
			x: 2.0d
			y: 1.0d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_mog.png"
			rotation: 0.0d
			width: 1.5603112840466926d
			x: -2.0d
			y: 1.0d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_smog.png"
			rotation: 0.0d
			width: 1.8461538461538463d
			x: -4.5d
			y: 2.5d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_minion.png"
			rotation: 0.0d
			width: 2.1219512195121952d
			x: -7.5d
			y: -2.0d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/undergarden/undergarden_gronglet.png"
			rotation: 0.0d
			width: 2.185185185185185d
			x: -7.5d
			y: 1.5d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/undergarden/undergarden_grongle.png"
			rotation: 0.0d
			width: 5.4288407163053725d
			x: 12.5d
			y: -8.5d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/undergarden/undergarden_ink.png"
			rotation: 0.0d
			width: 5.4288407163053725d
			x: -9.0d
			y: -8.5d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/undergarden/undergarden_veil.png"
			rotation: 0.0d
			width: 5.4288407163053725d
			x: -9.0d
			y: -5.5d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/undergarden/undergarden_blood.png"
			rotation: 0.0d
			width: 5.4288407163053725d
			x: 12.5d
			y: -5.5d
		}
		{
			height: 6.0d
			image: "atm:textures/questpics/undergarden/undergarden_background.png"
			rotation: 0.0d
			width: 10.656d
			x: -5.5d
			y: 6.0d
		}
		{
			corner: true
			height: 6.0d
			image: "atm:textures/questpics/undergarden/undergarden_background.png"
			rotation: 0.0d
			width: 10.656d
			x: 10.0d
			y: 6.0d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/undergarden/undergarden_vine.png"
			rotation: 0.0d
			width: 6.542696629213483d
			x: -9.0d
			y: -5.5d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/undergarden/undergarden_vine.png"
			rotation: 0.0d
			width: 6.542696629213483d
			x: -9.0d
			y: -8.5d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/undergarden/undergarden_vine.png"
			rotation: 0.0d
			width: 6.542696629213483d
			x: 12.5d
			y: -8.5d
		}
		{
			height: 4.0d
			image: "atm:textures/questpics/undergarden/undergarden_vine.png"
			rotation: 0.0d
			width: 6.542696629213483d
			x: 12.5d
			y: -5.5d
		}
		{
			height: 6.0d
			image: "atm:textures/questpics/undergarden/undergarden_background.png"
			rotation: 0.0d
			width: 10.656d
			x: -9.5d
			y: 6.0d
		}
		{
			height: 6.0d
			image: "atm:textures/questpics/undergarden/undergarden_background.png"
			rotation: 0.0d
			width: 10.656d
			x: 14.0d
			y: 6.0d
		}
	]
	order_index: 3
	quest_links: [ ]
	quests: [
		{
			dependencies: ["6C4B93FE64BD418D"]
			description: ["&2&l深暗之园&r维度遍布着多样且恶心的生物群系,充满各种植被、友好或敌对的生物,当然还有矿石和物品.\\n\\n准备好战斗吧!"]
			hide_dependent_lines: true
			icon: "undergarden:goo_ball"
			id: "1C232674CD04024D"
			rewards: [{
				id: "4611E0242FAB9CF3"
				type: "xp"
				xp: 100
			}]
			shape: "gear"
			size: 3.0d
			tasks: [{
				dimension: "undergarden:undergarden"
				id: "0F2860523070BF25"
				type: "dimension"
			}]
			title: "欢迎来到&2&l深暗之园&r!"
			x: 2.0d
			y: -6.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["地下墓穴是由&a渊邃石砖&f构成的巨型地底建筑,有时会有部分结构露出地面.\\n\\n在这座巨大迷宫中潜藏着&7遗忘者&r、隐藏宝箱,以及首领:&a&a遗忆守卫&f&r.\\n\\n希望你已做好挑战准备!"]
			icon: "undergarden:cracked_depthrock_bricks"
			id: "00CB8D60E7831A57"
			rewards: [{
				count: 5
				id: "6756B0150D2C092D"
				item: "minecraft:golden_apple"
				type: "item"
			}]
			shape: "octagon"
			size: 1.3d
			tasks: [{
				id: "0A889EB88211B0ED"
				structure: "undergarden:catacombs"
				type: "structure"
			}]
			title: "地下墓穴"
			x: 13.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"迄今为止我最爱的生物群系!\\n\\n布满巨型格隆格罗巨树的绿色丛林.\\n\\n这些树上能找到&e格隆果&r!"
				"{image:atm:textures/questpics/undergarden/undergarden_grongle.png width:200 height:100 align:center}"
			]
			icon: "undergarden:grongle_sapling"
			id: "647A87E800C703C3"
			rewards: [{
				id: "2DBEE0D01E6A8995"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:gronglegrowth"
				icon: "undergarden:grongle_sapling"
				id: "7C9EF32C628CA0A8"
				type: "biome"
			}]
			title: "格隆格生长"
			x: -5.5d
			y: -6.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&e格隆果&r严格来说是方块而非生物.但我实在太喜欢它们了必须收录!\\n\\n它们生成在格隆格罗树上,会发出声音和光亮!绝对是完美的装饰品."
				"{image:atm:textures/questpics/undergarden/undergarden_gronglet.png width:100 height:100 align:center}"
			]
			id: "5FCA6F5FE97780B7"
			rewards: [{
				count: 7
				id: "64525F6365C40773"
				item: "undergarden:gronglet"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				id: "442B73DD312E5D42"
				item: "undergarden:gronglet"
				type: "item"
			}]
			title: "&e格隆格"
			x: -3.5d
			y: -2.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a远古之海&f是&2&l深暗之园&r中的开阔海域.\\n\\n尽管充满污染物,这里的水却神奇地可以饮用.\\n\\n&a水生生物&f还包括&b格薇鱼&r和&b格薇幼鱼&r.当然还有闪亮海藻!"
				"{image:atm:textures/questpics/undergarden/undergarden_ancient.png width:200 height:100 align:center}"
			]
			icon: "undergarden:glitterkelp"
			id: "353AEEC116A5DEEF"
			rewards: [{
				id: "5429CC01B4971AD3"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:ancient_sea"
				icon: "undergarden:glitterkelp"
				id: "518049B7B94DEA7A"
				type: "biome"
			}]
			title: "&a远古之海&f"
			x: -5.5d
			y: -8.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["闪光海藻生长在远古和&a霜冻之海&f中.\\n\\n它们除了发光没什么特别作用,但可以做成派!"]
			id: "43A1BC20BB054A14"
			rewards: [{
				count: 5
				id: "740845127F7ECC4D"
				item: "undergarden:glitterkelp"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "68A2E3EFD8C086B8"
				item: "undergarden:glitterkelp"
				type: "item"
			}]
			x: 6.5d
			y: -5.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a摇蜿森林&f和其他森林没什么不同.\\n\\n只不过这里的树喜欢随心所欲地摇摆."
				"{image:atm:textures/questpics/undergarden/undergarden_wiggle.png width:200 height:100 align:center}"
			]
			icon: "undergarden:wigglewood_sapling"
			id: "050D8480EE9D8E62"
			rewards: [{
				id: "6B757D1052B67B34"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:wigglewood_forest"
				icon: "undergarden:wigglewood_sapling"
				id: "7FFA960B8F3CC151"
				type: "biome"
			}]
			title: "&a摇蜿森林&f"
			x: -2.0d
			y: -5.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a遗忆之地&f是&2&l深暗之园&r的平原.\\n\\n这里没有树木,但有大量岩石、花朵和生物!\\n\\n不过不会生成&7遗忘者&r,抱歉打破了你的期待."
				"{image:atm:textures/questpics/undergarden/undergarden_field.png width:200 height:100 align:center}"
			]
			icon: "undergarden:depthrock_bricks"
			id: "4E3D548E5D8D7D26"
			rewards: [{
				id: "7E7389D366197863"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:forgotten_field"
				icon: "undergarden:depthrock_bricks"
				id: "12E13E7D8732BC65"
				type: "biome"
			}]
			title: "&a遗忆之地&f"
			x: -4.0d
			y: -7.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"我保证这是最后一个&a蘑菇塘沼&f!\\n这些蘑菇看起来很普通,但玄机在于它们的产物!\\n\\n菌丝面纱!绝对是实用的装饰方块."
				"{image:atm:textures/questpics/undergarden/undergarden_veil.png width:200 height:100 align:center}"
			]
			icon: "undergarden:veil_mushroom"
			id: "7E3DA4168D352B42"
			rewards: [{
				id: "02C06AB42788957F"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:veil_mushroom_bog"
				icon: "undergarden:veil_mushroom"
				id: "5F65CF932582E4D4"
				type: "biome"
			}]
			title: "Veil &a蘑菇塘沼&f"
			x: -3.0d
			y: -5.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&c腐化生物&r中最弱小的一种.\\n\\n拥有5颗心生命值.\\n\\n和其他&c腐化生物&r一样会攻击&2石裔&r和&7遗忘者&r,掉落&c&a御腐破片&f&r."
				"{image:atm:textures/questpics/undergarden/undergarden_rotling.png width:100 height:100 align:center}"
			]
			icon: "undergarden:rotling_spawn_egg"
			id: "24D38DA8E86DD897"
			rewards: [{
				count: 3
				id: "2BEA59F5963F6F21"
				item: "undergarden:utheric_shard"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:rotling"
				icon: "undergarden:rotling_spawn_egg"
				id: "1ECCD5B3DADC859A"
				type: "kill"
				value: 1L
			}]
			title: "&c腐化幼体"
			x: 6.5d
			y: 1.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"中等体型的&c腐化生物&r——&c腐行兽&r.\\n\\n拥有20颗心生命值.\\n\\n和其他&c腐化生物&r一样会攻击&2石裔&r和&7遗忘者&r,掉落&c&a御腐破片&f&r."
				"{image:atm:textures/questpics/undergarden/undergarden_rotwalker.png width:100 height:200 align:center}"
			]
			icon: "undergarden:rotwalker_spawn_egg"
			id: "380D3212D1CBA593"
			rewards: [{
				count: 6
				id: "1C3ABD092D7A3A57"
				item: "undergarden:utheric_shard"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:rotwalker"
				icon: "undergarden:rotwalker_spawn_egg"
				id: "453E3D10DCED3F28"
				type: "kill"
				value: 1L
			}]
			title: "&c腐化行者"
			x: 8.5d
			y: 1.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"最庞大危险的&c腐化生物&r.\\n\\n拥有80颗心生命值,堪称迷你Boss.\\n\\n和其他&c腐化生物&r一样会攻击&2石裔&r和&7遗忘者&r,掉落&c&a御腐破片&f&r."
				"{image:atm:textures/questpics/undergarden/undergarden_rotbeast.png width:100 height:100 align:center}"
			]
			icon: "undergarden:rotbeast_spawn_egg"
			id: "70FD8856BFCD9AEE"
			rewards: [{
				count: 3
				id: "1FFBDB345F444727"
				item: "undergarden:utherium_crystal"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:rotbeast"
				icon: "undergarden:rotbeast_spawn_egg"
				id: "08238D4E8D9CEB70"
				type: "kill"
				value: 1L
			}]
			title: "&c腐化巨兽"
			x: 10.5d
			y: 1.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&4吞噬兽&r与&2&l深暗之园&r其他&a敌对生物&f类似,但它的掉落物特别实用!\\n\\n&a巨嘴兽&f死亡时会掉落&7凝矿&r和&b&a霜钢粒&f&r."
				"{image:atm:textures/questpics/undergarden/undergarden_muncher.png width:100 height:100 align:center}"
			]
			icon: "undergarden:muncher_spawn_egg"
			id: "5D229FA3A5F92954"
			rewards: [{
				count: 4
				id: "4F9BB0E1415EF299"
				item: "undergarden:froststeel_nugget"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:muncher"
				icon: "undergarden:muncher_spawn_egg"
				id: "775FC64EEBCD72DB"
				type: "kill"
				value: 1L
			}]
			title: "&4咀嚼者"
			x: 10.5d
			y: -2.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&2石裔&r是村民与&a铁傀儡&f的完美结合.\\n\\n通过&a右键点击&f可进行交易.\\n\\n他们会主动攻击&a敌对生物&f(或攻击他们的玩家)."
				"{image:atm:textures/questpics/undergarden/undergarden_stoneborn.png width:100 height:150 align:center}"
			]
			icon: "undergarden:stoneborn_spawn_egg"
			id: "4D836B9D9E3B898F"
			rewards: [{
				count: 3
				id: "1F234CCAC2D4F7D9"
				item: "undergarden:regalium_crystal"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:stoneborn"
				icon: "undergarden:stoneborn_spawn_egg"
				id: "6B5ACC22375819D5"
				type: "kill"
				value: 1L
			}]
			title: "&2石裔"
			x: 2.0d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&5幽怨兽&r理论上是被动生物,但拥有防御机制,我视其中立!\\n\\n被攻击时会释放类似&a滞留药水&f的效果.\\n\\n死亡时掉落美味的腿部和皮革.\\n\\n可用幽怨南瓜繁殖."
				"{image:atm:textures/questpics/undergarden/undergarden_gloomper.png width:100 height:100 align:center}"
			]
			icon: "undergarden:gloomper_spawn_egg"
			id: "79C652E3FCB88550"
			rewards: [{
				count: 4
				id: "42738C69450F0A58"
				item: "undergarden:raw_gloomper_leg"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:gloomper"
				icon: "undergarden:gloomper_spawn_egg"
				id: "1FB5E933DD7FA6F9"
				type: "kill"
				value: 1L
			}]
			title: "&5幽暗爬行者"
			x: 0.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&d纳戈尔&r是生成于地下的凶残敌对生物.\\n\\n它们拥有7颗半心形生命值,会无情地试图杀死你.\\n\\n它们不会掉落任何物品,只是作为想要安静采矿者的障碍."
				"{image:atm:textures/questpics/undergarden/undergarden_nargoyle.png width:100 height:100 align:center}"
			]
			icon: "undergarden:nargoyle_spawn_egg"
			id: "0A1D6E233C0ECC8E"
			rewards: [{
				id: "39F72DD6F716983D"
				type: "xp"
				xp: 1
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:nargoyle"
				icon: "undergarden:nargoyle_spawn_egg"
				id: "241BF9881DEEAA19"
				type: "kill"
				value: 1L
			}]
			title: "&d纳迦石像鬼"
			x: 8.5d
			y: -2.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&7晶光蛞蝓&r是在&2&l深暗之园&r随处可见的无害小生物.\\n\\n它们的行为类似&a雪傀儡&f,会在移动时留下痕迹.只不过这些痕迹更加粘稠...\\n\\n死亡时会掉落&a晶光粘浆&f,但其实你可以直接从它们留下的痕迹中采集,何必伤害它们呢!"
				"{image:atm:textures/questpics/undergarden/undergarden_scintling.png width:150 height:100 align:center}"
			]
			icon: "undergarden:scintling_spawn_egg"
			id: "4F3B4990BE6C2A4A"
			rewards: [{
				count: 10
				id: "21A9651456278BCD"
				item: "undergarden:goo_ball"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:scintling"
				icon: "undergarden:scintling_spawn_egg"
				id: "01C8E18FB9140B34"
				type: "kill"
				value: 1L
			}]
			title: "&7闪烁体"
			x: -2.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&9喷吐怪&r生成概率很低,但一旦出现就注定要骚扰你.\\n\\n它们会向你喷射唾液(并发出恶心的吮吸声),但若受到攻击就会逃跑——类似幼年猪灵.\\n\\n死亡时仅会掉落&a渊邃卵石&f."
				"{image:atm:textures/questpics/undergarden/undergarden_sploogie.png width:125 height:100 align:center}"
			]
			icon: "undergarden:sploogie_spawn_egg"
			id: "67A2347534269528"
			rewards: [{
				id: "3293B6D906126951"
				type: "xp"
				xp: 1
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:sploogie"
				icon: "undergarden:sploogie_spawn_egg"
				id: "1DB0E7F8AF4D40A8"
				type: "kill"
				value: 1L
			}]
			title: "&9史普鲁吉"
			x: 6.5d
			y: -2.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&b格威布林鱼&r是海洋中的友好鱼类.\\n\\n它们比&b格威布&r体型更小,行为类似原版鱼类!"]
			icon: "undergarden:gwibling_spawn_egg"
			id: "53FDC6C536E50A1A"
			rewards: [{
				id: "4FA496EDE0E9607C"
				item: "undergarden:gwibling_bucket"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:gwibling"
				icon: "undergarden:gwibling_spawn_egg"
				id: "55DE528A1CC77248"
				type: "kill"
				value: 1L
			}]
			title: "&b格威布林鱼"
			x: -4.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&b巨涡怪&r是&b小涡怪&r的放大愤怒版.\\n\\n它们既无掉落物也无法被装进桶里,唯一能带给你的只有痛苦!"
				"{image:atm:textures/questpics/undergarden/undergarden_gwib.png width:100 height:100 align:center}"
			]
			icon: "undergarden:gwib_spawn_egg"
			id: "71E1454990DEC451"
			rewards: [{
				id: "3E0804953DAAC7E3"
				type: "xp"
				xp: 1
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:gwib"
				icon: "undergarden:gwib_spawn_egg"
				id: "28DB734A99F82FCE"
				type: "kill"
				value: 1L
			}]
			title: "&b格威布"
			x: 7.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"那块石头在动？等等,那不是石头,是&2摩格&r!\\n\\n&2摩格&r可以用&a渊邃卵石&f繁殖,死亡时可能掉落更多卵石或摩格苔藓!\\n\\n摩格苔藓可用于多种合成配方!"
				"{image:atm:textures/questpics/undergarden/undergarden_mog.png width:100 height:100 align:center}"
			]
			icon: "undergarden:mog_spawn_egg"
			id: "744DBE831C120B2F"
			rewards: [{
				count: 4
				id: "6FFDC804CABF16F8"
				item: "undergarden:mogmoss"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:mog"
				icon: "undergarden:mog_spawn_egg"
				id: "73FB056A56FE9B8D"
				type: "kill"
				value: 1L
			}]
			title: "&2摩格"
			x: -3.5d
			y: 1.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"那个烟雾在移动？不,那是&3烟摩格&r!&2摩格&r的近亲!\\n\\n行为模式与&2摩格&r完全相同,只是可能掉落蓝色摩格苔藓."
				"{image:atm:textures/questpics/undergarden/undergarden_smog.png width:100 height:100 align:center}"
			]
			icon: "undergarden:smog_mog_spawn_egg"
			id: "2E596B5A4EA2ABF4"
			rewards: [{
				count: 4
				id: "0DD7639423814FA5"
				item: "undergarden:blue_mogmoss"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:smog_mog"
				icon: "undergarden:smog_mog_spawn_egg"
				id: "365FA96C16AC95FF"
				type: "kill"
				value: 1L
			}]
			title: "&3S'摩格"
			x: -5.5d
			y: 1.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&7遗忘者&r是生成于地下墓穴结构的战士.\\n\\n他们穿着远古盔甲,手持克罗格鲁姆工具,这些装备在死亡时都可能掉落.\\n\\n他们还会主动攻击并杀死&c腐化生物&r."
				"{image:atm:textures/questpics/undergarden/undergarden_forgotten.png width:100 height:200 align:center}"
			]
			hide_dependent_lines: true
			icon: "undergarden:forgotten_spawn_egg"
			id: "141681FE9E524FB0"
			rewards: [{
				id: "723172D86161488C"
				item: {
					Count: 1
					id: "undergarden:cloggrum_battleaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:forgotten"
				icon: "undergarden:forgotten_spawn_egg"
				id: "0D6A262F278C9399"
				type: "kill"
				value: 3L
			}]
			title: "&7遗忘者"
			x: 9.5d
			y: -0.5d
		}
		{
			dependencies: ["00CB8D60E7831A57"]
			description: [
				"&a&a遗忆守卫&r是由&a&a遗忆锭&r构成的巨型傀儡.\\n\\n它会锁定玩家并不择手段地追杀目标.\\n\\n拥有80颗心的生命值,击杀后会掉落&a&a遗忆粒&r."
				"{image:atm:textures/questpics/undergarden/undergarden_guardian.png width:100 height:200 align:center}"
			]
			icon: "undergarden:forgotten_guardian_spawn_egg"
			id: "1054168AFD8520D4"
			rewards: [{
				count: 2
				id: "573A5DE60A3D35BF"
				item: "undergarden:forgotten_ingot"
				type: "item"
			}]
			size: 1.9d
			tasks: [{
				entity: "undergarden:forgotten_guardian"
				icon: "undergarden:forgotten_guardian_spawn_egg"
				id: "69BAF62F032AD940"
				type: "kill"
				value: 1L
			}]
			title: "&a&a遗忆守卫&f"
			x: 11.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&c乌瑟矿&r可以通过采矿或击杀&c腐化生物&r获得."]
			id: "30582B5FBED35FD1"
			rewards: [{
				id: "758D76C7755819CB"
				item: "undergarden:utherium_crystal"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "05F86B23702AD630"
				item: "undergarden:utherium_crystal"
				type: "item"
			}]
			title: "&c乌瑟矿"
			x: 4.0d
			y: 5.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&e帝王矿&r是&2&l深暗之园&r中的绿宝石等价物.\\n\\n虽然不能制作装备工具,但可以用来与&2石之子民&r交易其他物品."]
			id: "3AF7446592D19FA6"
			rewards: [{
				count: 5
				id: "2ACBFB0DDEE3BF54"
				item: "undergarden:regalium_crystal"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "6C5AD33B8B69BEEC"
				item: "undergarden:regalium_crystal"
				type: "item"
			}]
			title: "&e帝王矿"
			x: 6.0d
			y: 5.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&a&a遗忆锭&f&r必须通过地下墓穴获取——无论是搜刮宝箱还是击败&a&a遗忆守卫&f&r.\\n\\n你还需要在墓穴宝箱中找到&a锻造模板&f."]
			id: "4F5A2E2A5C7F4837"
			rewards: [{
				id: "228313ACC9720035"
				item: "undergarden:forgotten_ingot"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "145F7277A6E82495"
				item: "undergarden:forgotten_ingot"
				type: "item"
			}]
			title: "&a&a遗忆锭&f"
			x: 2.0d
			y: 5.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["霜钢生成于冰冻生物群系:霜原、寒冷的&a烟梗森林&f,可能还有冰封之海.\\n\\n它生成于颤岩中(这些群系中取代深层岩的矿石).&a寒栗石头&f特性类似冰,具有滑溜特性且会略微减速.\\n\\n你还能在咀嚼怪的肚子里找到些霜钢粒!"]
			id: "51E2728C5E35A152"
			rewards: [{
				count: 2
				id: "74FC609432923CAE"
				item: "undergarden:froststeel_ingot"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "2F2F12FD80DAE684"
				item: "undergarden:froststeel_ingot"
				type: "item"
			}]
			title: "&b霜钢"
			x: 0.0d
			y: 5.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&7Cloggrum&r是&2&l深暗之园&r中的铁矿石.\\n\\n它随处可见,可用于多种合成配方.\\n\\n甚至有些生物也会掉落它!"]
			id: "4141EF8B8C316118"
			rewards: [{
				count: 2
				id: "58A82FCE900550AA"
				item: "undergarden:cloggrum_ingot"
				type: "item"
			}]
			shape: "diamond"
			size: 1.5d
			tasks: [{
				id: "030173D7AE2E8AF7"
				item: "undergarden:cloggrum_ingot"
				type: "item"
			}]
			title: "&7克洛格姆"
			x: -2.0d
			y: 5.5d
		}
		{
			dependencies: ["4141EF8B8C316118"]
			description: ["&7Cloggrum&r护甲属性略逊于&a铁甲&f,但它几乎像树上长出来般易得,所以还算公平.\\n\\n&a靴子&f还能帮助穿越&a蚙娫黏浆&f!"]
			id: "130577AD1A1A8222"
			rewards: [{
				count: 4
				id: "142964A7354A4C51"
				item: "undergarden:cloggrum_ingot"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "7F976851A2831B88"
					item: {
						Count: 1
						id: "undergarden:cloggrum_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "560EBAD803282FF4"
					item: {
						Count: 1
						id: "undergarden:cloggrum_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "616F19EE1582B025"
					item: {
						Count: 1
						id: "undergarden:cloggrum_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "000C3B46CCF0F9C6"
					item: {
						Count: 1
						id: "undergarden:cloggrum_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&7克洛格姆&r护甲"
			x: -2.0d
			y: 7.0d
		}
		{
			dependencies: ["4141EF8B8C316118"]
			description: ["&7Cloggrum&r工具性能与铁制工具相当,只是外观更像泥巴而非钢铁!\\n\\n不过&a剑&f的伤害比&a铁剑&f更高."]
			id: "16A04B2FC3F0D902"
			rewards: [{
				count: 4
				id: "2D2BC753AF428A4B"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "5383BF974E6E6A96"
					item: {
						Count: 1
						id: "undergarden:cloggrum_pickaxe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "4C1CB1CD27DE1996"
					item: {
						Count: 1
						id: "undergarden:cloggrum_axe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "3182842487B1ADC0"
					item: {
						Count: 1
						id: "undergarden:cloggrum_shovel"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "3965E01BB5716714"
					item: {
						Count: 1
						id: "undergarden:cloggrum_hoe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "6C8D488FB86260B2"
					item: {
						Count: 1
						id: "undergarden:cloggrum_sword"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&7克洛格姆&r工具"
			x: -2.5d
			y: 4.0d
		}
		{
			dependencies: ["4141EF8B8C316118"]
			description: ["普通工具难以应对&2&l深暗之园&r中的危险生物.\\n\\n我们可以合成&7&a扼塞盾牌&f&r,功能与普通盾牌相同.\\n\\n但需要从&7遗忘者&r身上稀有掉落&7战斧&r."]
			id: "1E81B903137BB62A"
			rewards: [{
				count: 4
				id: "13F796F5FD4B30A0"
				item: "undergarden:cloggrum_ingot"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "229BDF9C5EFF2F68"
					item: {
						Count: 1
						id: "undergarden:cloggrum_battleaxe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "6FBB26ACF48BF73F"
					item: {
						Count: 1
						id: "undergarden:cloggrum_shield"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&7克洛格姆&r武器"
			x: -1.5d
			y: 4.0d
		}
		{
			dependencies: ["51E2728C5E35A152"]
			description: ["&b霜钢护甲&r略逊于钻石护甲,但提供强大的&a击退抗性&f.\\n\\n不过会附带减速效果,看来我们移动不了太快!"]
			id: "7617737FC179EA1A"
			rewards: [{
				count: 4
				id: "4CAED257664405F7"
				item: "undergarden:froststeel_ingot"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "1F56436CFE9E4829"
					item: {
						Count: 1
						id: "undergarden:froststeel_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "12D8CA0A44EF5EDD"
					item: {
						Count: 1
						id: "undergarden:froststeel_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "4F65FE9890B4283C"
					item: {
						Count: 1
						id: "undergarden:froststeel_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "75672283B8F0C3CB"
					item: {
						Count: 1
						id: "undergarden:froststeel_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&b霜钢&r 护甲"
			x: 0.0d
			y: 7.0d
		}
		{
			dependencies: ["51E2728C5E35A152"]
			description: ["类似铁制工具,但所有攻击都会使目标减速.\\n\\n当然锄头除外——不过谁会在意一把普通锄头呢？"]
			id: "46DCB235D3FEDA61"
			rewards: [{
				count: 4
				id: "3DA2915B3BB46AEC"
				item: "undergarden:froststeel_ingot"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "3B2ECE1F852193F6"
					item: {
						Count: 1
						id: "undergarden:froststeel_sword"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "51B4AD7164660ED5"
					item: {
						Count: 1
						id: "undergarden:froststeel_pickaxe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "0D63C10C5B742431"
					item: {
						Count: 1
						id: "undergarden:froststeel_axe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "1A9A616D3CA79C35"
					item: {
						Count: 1
						id: "undergarden:froststeel_shovel"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "20016A6E5AF25B3C"
					item: {
						Count: 1
						id: "undergarden:froststeel_hoe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&b霜钢&r 工具"
			x: 0.0d
			y: 4.0d
		}
		{
			dependencies: ["30582B5FBED35FD1"]
			description: ["&c乌瑟护甲&r属性与下界合金护甲相当!\\n\\n且不需要&a锻造模板&f!"]
			id: "01B13BBB41BBB460"
			rewards: [{
				count: 4
				id: "5B92118023C90ADD"
				item: "undergarden:utherium_crystal"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "5C577BF2008DEA1C"
					item: {
						Count: 1
						id: "undergarden:utherium_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "4DC0DCE680549B36"
					item: {
						Count: 1
						id: "undergarden:utherium_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "0DBD340646246BCD"
					item: {
						Count: 1
						id: "undergarden:utherium_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "192C931FBF39387C"
					item: {
						Count: 1
						id: "undergarden:utherium_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&c乌瑟矿&r 护甲"
			x: 4.0d
			y: 7.0d
		}
		{
			dependencies: ["30582B5FBED35FD1"]
			description: ["&c乌瑟工具&r类似钻石工具,但对&c腐朽生物&r更有效!\\n\\n用它们制造的武器反制其造物——这设定本可玩很多流行文化梗,但我一时想不起来..."]
			id: "1FCF946B7BE3C0BD"
			rewards: [{
				count: 4
				id: "024E32DD5FCCA5C8"
				item: "undergarden:utherium_crystal"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "749B1D631FC31987"
					item: {
						Count: 1
						id: "undergarden:utherium_sword"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "53F2440C55033418"
					item: {
						Count: 1
						id: "undergarden:utherium_pickaxe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "71B98C882B804619"
					item: {
						Count: 1
						id: "undergarden:utherium_axe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "656B4889C4C1A4F0"
					item: {
						Count: 1
						id: "undergarden:utherium_shovel"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "4542D7CA0F4097B8"
					item: {
						Count: 1
						id: "undergarden:utherium_hoe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&c乌瑟&r 工具"
			x: 4.0d
			y: 4.0d
		}
		{
			dependencies: ["4F5A2E2A5C7F4837"]
			description: ["用&a&a遗忆锭&f&r和&a锻造模板&f升级&7Cloggrum&r工具.\\n\\n这是你能获得的最佳工具,已自带针对&2&l深暗之园&r方块的&a效率&f附魔.\\n\\n剑和斧头会对所有&2&l深暗之园&r生物(除&c腐朽巨兽&r和&a&a遗忆守卫&f&r外)造成额外伤害."]
			id: "6124C0E9E419694B"
			rewards: [{
				id: "5D5504B9CD6842AA"
				item: "undergarden:forgotten_ingot"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "2C7066FECE927691"
					item: {
						Count: 1
						id: "undergarden:forgotten_pickaxe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "170BED42A6861142"
					item: {
						Count: 1
						id: "undergarden:forgotten_axe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "322FDB79D946CFC6"
					item: {
						Count: 1
						id: "undergarden:forgotten_shovel"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "0706A577127686BD"
					item: {
						Count: 1
						id: "undergarden:forgotten_hoe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "08F28AD831730B2D"
					item: {
						Count: 1
						id: "undergarden:forgotten_sword"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&a遗忘&r 工具"
			x: 2.5d
			y: 4.0d
		}
		{
			dependencies: ["4F5A2E2A5C7F4837"]
			description: ["升级你的&7Cloggrum战斧&r,打造&2&l深暗之园&r能给予的最强武器!"]
			id: "0D91A6B36BD2F526"
			rewards: [{
				id: "5C97016990826793"
				type: "xp_levels"
				xp_levels: 5
			}]
			shape: "square"
			size: 1.15d
			tasks: [{
				id: "5E0BED0B76D91B6F"
				item: {
					Count: 1
					id: "undergarden:forgotten_battleaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			title: "&a终极武器!"
			x: 1.5d
			y: 4.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a&a遗忆仆从&r并非自然生成,需要人工制造.将雕刻的幽暗南瓜放置在&a&a遗忆锭方块&r上方即可创造.\\n\\n行为类似&a雪傀儡&f,永远不会攻击你,而是会射击&a敌对生物&f.\\n\\n拥有10颗心的生命值且死亡无掉落...但有什么理由要杀死它们呢？"
				"{image:atm:textures/questpics/undergarden/undergarden_minion.png width:125 height:100 align:center}"
			]
			icon: "undergarden:forgotten_block"
			id: "5B4B5C148D63ACA1"
			rewards: [{
				id: "1B3D9B39502EA161"
				item: "undergarden:forgotten_block"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:minion"
				id: "26C44A9E20C98FF9"
				type: "kill"
				value: 1L
			}]
			title: "&a&a遗忆仆从&f"
			x: -5.5d
			y: -2.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"荒芜深渊正如其名,一片荒凉...这里只有&a敌对生物&f和卵石.\\n\\n我个人宁愿去其他地方寻找卵石..."
				"{image:atm:textures/questpics/undergarden/undergarden_barren.png width:200 height:100 align:center}"
			]
			icon: "undergarden:depthrock"
			id: "36389696767EFB4E"
			rewards: [{
				id: "4B4FBFCCB070EB30"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:barren_abyss"
				icon: "undergarden:depthrock"
				id: "7F8993963938570B"
				type: "biome"
			}]
			title: "荒芜深渊"
			x: -4.5d
			y: -8.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a浆血蘑菇&f沼泽是巨型&a浆血蘑菇&f的栖息地.\\n\\n血红色总让我不适,所以这个描述就简短些吧."
				"{image:atm:textures/questpics/undergarden/undergarden_blood.png width:200 height:100 align:center}"
			]
			icon: "undergarden:blood_mushroom"
			id: "06D13E319C26C949"
			rewards: [{
				id: "4A2FBB3EACE3F1BF"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:blood_mushroom_bog"
				icon: "undergarden:blood_mushroom"
				id: "19FD2A6FBA7368C4"
				type: "biome"
			}]
			title: "&a浆血蘑菇&f Bog"
			x: -3.5d
			y: -8.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"想象一下&a远古之海&f的景象.现在去掉所有海带,移除所有&b巨涡怪&r和&b小涡怪&r.\\n\\n这就是为你准备的&a荒芜礁海&f!"
				"{image:atm:textures/questpics/undergarden/undergarden_dead.png width:200 height:100 align:center}"
			]
			icon: "undergarden:sediment"
			id: "1BAE1519E44E40CE"
			rewards: [{
				id: "0214A47335438F7E"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:dead_sea"
				icon: "undergarden:sediment"
				id: "01BCE3270B3EEDA9"
				type: "biome"
			}]
			title: "&a荒芜礁海&f"
			x: -2.5d
			y: -8.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"有些人讨厌探索多个生物群系来收集不同树苗.\\n\\n显然&2&l深暗之园&r开发者也是,所以他把大多数树苗都放在了&a茂密森林&f!"
				"{image:atm:textures/questpics/undergarden/undergarden_dense.png width:200 height:100 align:center}"
			]
			icon: "undergarden:deepturf_block"
			id: "15A8F04BC10E619E"
			rewards: [{
				id: "12D1A6BD7DB7E1DE"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:dense_forest"
				icon: "undergarden:deepturf_block"
				id: "4AA288827E383289"
				type: "biome"
			}]
			title: "&a茂密森林&f"
			x: -5.0d
			y: -7.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"霜原(Frostfields)是&2&l深暗之园&r中的寒冷生物群系之一.从巨大的冰锥、细雪和&a霜冻邃草&f就能看出这里的严寒!\\n\\n不同于常见的地下深岩,这里遍布着颤栗石.\\n\\n颤栗石的特性更接近冰而非真正的石头——至少当你站在上面时是这样!"
				"{image:atm:textures/questpics/undergarden/undergarden_frosty.png width:200 height:100 align:center}"
			]
			icon: "minecraft:snow_block"
			id: "1E13D7CE5A9EFF74"
			rewards: [{
				id: "196AF64340C2D7B6"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:frostfields"
				icon: "minecraft:snow_block"
				id: "38C521FBA84E18B7"
				type: "biome"
			}]
			title: "霜原"
			x: -3.0d
			y: -7.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"霜原附近可能生成凛冽的&a烟梗森林&f.\\n\\n这里与霜原相似,只是多了烟梗树!"
				"{image:atm:textures/questpics/undergarden/undergarden_frostytrees.png width:200 height:100 align:center}"
			]
			icon: "undergarden:frozen_deepturf_block"
			id: "0C98503A2F20484D"
			rewards: [{
				id: "0957C1372A07536B"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:frosty_smogstem_forest"
				icon: "undergarden:frozen_deepturf_block"
				id: "7425F62E6014D327"
				type: "biome"
			}]
			title: "寒冷的&a烟梗森林&f"
			x: -2.0d
			y: -7.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"又一片海洋,只是这里更加寒冷刺骨."
				"{image:atm:textures/questpics/undergarden/undergarden_icey.png width:200 height:100 align:center}"
			]
			icon: "minecraft:packed_ice"
			id: "073BA8470EA37A00"
			rewards: [{
				id: "603CBB78679E2DE9"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:icy_sea"
				icon: "minecraft:packed_ice"
				id: "13998128201A3AD4"
				type: "biome"
			}]
			title: "&a霜冻之海&f"
			x: -4.5d
			y: -6.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a深蓝蘑菇&f沼泽比&a浆血蘑菇&f沼泽植被更茂密.\\n\\n除了标志性的&a深蓝蘑菇&f,这里还有迷你烟梗树和&a致毒混合物&f池塘——真好奇这些液体有什么效果..."
				"{image:atm:textures/questpics/undergarden/undergarden_indigo.png width:200 height:100 align:center}"
			]
			icon: "undergarden:indigo_mushroom"
			id: "0A0A991BAC99092C"
			rewards: [{
				id: "6D3D8B1115168801"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:indigo_mushroom_bog"
				icon: "undergarden:indigo_mushroom"
				id: "3CCDB2BDBD680158"
				type: "biome"
			}]
			title: "&a深蓝蘑菇&f Bog"
			x: -3.5d
			y: -6.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"紫色沼泽!\\n\\n遍地生长着&a墨黑蘑菇&f和&a致毒混合物&f!"
				"{image:atm:textures/questpics/undergarden/undergarden_ink.png width:200 height:100 align:center}"
			]
			icon: "undergarden:ink_mushroom"
			id: "278D34BBC13356DF"
			rewards: [{
				id: "404CE4FF92D35505"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:ink_mushroom_bog"
				icon: "undergarden:ink_mushroom"
				id: "2489A0C2542332E1"
				type: "biome"
			}]
			title: "&a墨黑蘑菇&f Bog"
			x: -2.5d
			y: -6.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a烟雾尖顶&f是...没错正如你所料!所有烟雾的发源地!当然不是全部,我们得承认&2摩格兽&r和&3斯摩格&r也有贡献.\\n\\n这里能找到水疱莓、&a粗糙邃土&f和&a烟雾孔&f——不过建议离那些孔洞远点..."
				"{image:atm:textures/questpics/undergarden/undergarden_smogspires.png width:200 height:100 align:center}"
			]
			icon: "undergarden:smog_vent"
			id: "511144EF336BFAF5"
			rewards: [{
				id: "5A53B26A5A61E77D"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:smog_spires"
				icon: "undergarden:smog_vent"
				id: "283BFDA10C3530EC"
				type: "biome"
			}]
			title: "&a烟雾尖顶&f"
			x: -5.0d
			y: -5.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&a烟梗森林&f与&a深蓝蘑菇&f沼泽相邻.\\n\\n这里同样呈现蓝色并生长着&a深蓝蘑菇&f,区别在于烟梗树更为高大!"
				"{image:atm:textures/questpics/undergarden/undergarden_smogstem.png width:200 height:100 align:center}"
			]
			icon: "undergarden:smogstem_sapling"
			id: "43FD4AEBB9548E57"
			rewards: [{
				id: "4EA712B69725CCF4"
				type: "xp_levels"
				xp_levels: 1
			}]
			shape: "hexagon"
			size: 1.2d
			tasks: [{
				biome: "undergarden:smogstem_forest"
				icon: "undergarden:smogstem_sapling"
				id: "44D8BAEAA4D0CA1D"
				type: "biome"
			}]
			title: "&a烟梗森林&f"
			x: -4.0d
			y: -5.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["摇蜿木的定义就是含有&a摇蜿木原木&f和树叶.它们能以任意方向、尺寸和形态生长.\\n\\n可见于&a摇蜿森林&f和&a茂密森林&f.\\n\\n它们拥有粉红树叶和类似云杉的褐色原木!"]
			id: "51C59FDF0BD11FDF"
			rewards: [{
				count: 3
				id: "31C1359A8B0505E6"
				item: "undergarden:wigglewood_sapling"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "15CC0FD07EFEBE55"
				item: "undergarden:wigglewood_sapling"
				type: "item"
			}]
			x: 8.0d
			y: -8.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&a纱障蘑菇&f仅生长于沼泽中.\\n\\n其褐色菌盖会在下方空气处生成&a蘑菇纱障&f,因此得名!"]
			id: "0FA77C28A57E06F1"
			rewards: [{
				count: 5
				id: "732E86ED89DD1668"
				item: "undergarden:veil_mushroom"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "608BB13CB5EC6DEE"
				item: "undergarden:veil_mushroom"
				type: "item"
			}]
			x: 8.5d
			y: -7.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["烟梗树底部异常粗壮,类似现实中的&a猴面包树&f!\\n\\n蓝色树叶搭配灰色原木.\\n分布于多个生物群系:&a烟梗森林&f、&a茂密森林&f,甚至&a深蓝蘑菇&f沼泽!"]
			id: "6D4FB6291CA8D1A6"
			rewards: [{
				count: 3
				id: "0B39C06B1CD2C478"
				item: "undergarden:smogstem_sapling"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "1B783C6110983107"
				item: "undergarden:smogstem_sapling"
				type: "item"
			}]
			x: 7.0d
			y: -8.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["疱莓会生成在&a烟雾尖顶&f区域.\\n\\n由于生长环境恶劣,部分会变成腐烂状态.\\n\\n不建议食用,或许它们另有用途!"]
			id: "6F416275B6F6D8F0"
			rewards: [
				{
					count: 8
					id: "5E09B2842A2D24D4"
					item: "undergarden:blisterberry"
					type: "item"
				}
				{
					count: 3
					id: "02BE2A1B60B71380"
					item: "undergarden:rotten_blisterberry"
					type: "item"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "1E225EF7E2F75A22"
				item: "undergarden:blisterberry"
				type: "item"
			}]
			x: 6.0d
			y: -6.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["没错!它们可以制作&a黑色染料&f!"]
			id: "739601B0C43B239B"
			rewards: [{
				count: 5
				id: "6BAB81EDF4C8BDF3"
				item: "undergarden:ink_mushroom"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "647B12D73B8AF0E8"
				item: "undergarden:ink_mushroom"
				type: "item"
			}]
			x: 7.5d
			y: -7.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&a深蓝蘑菇&f似乎是烟雾茎的最佳伙伴,主要伴随其生成!"]
			id: "4C5CFA45D9B4F6D3"
			rewards: [{
				count: 5
				id: "27ABA34900DB9865"
				item: "undergarden:indigo_mushroom"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "576DEAD84C6D8E82"
				item: "undergarden:indigo_mushroom"
				type: "item"
			}]
			x: 6.5d
			y: -7.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["巨榕树是类似丛林树木的高大乔木,仅生成于巨榕生态群系!\\n\\n这些树木也是&e小榕果&r的生成位置."]
			id: "2C5A7E4CD8B57E08"
			rewards: [
				{
					count: 3
					id: "492C35AF73FBB524"
					item: "undergarden:grongle_sapling"
					type: "item"
				}
				{
					id: "5637D555FCB587F6"
					item: "undergarden:gronglet"
					type: "item"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "1B1CE8A91C2F9545"
				item: "undergarden:grongle_sapling"
				type: "item"
			}]
			x: 6.0d
			y: -8.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["幽光南瓜的生成和种植方式与普通南瓜类似.\\n\\n同样可以制作南瓜派甚至南瓜灯!\\n\\n不同的是它们还能制成爆炸物..."]
			id: "22739522F0D33D7B"
			rewards: [{
				count: 3
				id: "497894879AAB71AC"
				item: "undergarden:gloomgourd_seeds"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "6E39966B6F48B054"
				item: "undergarden:gloomgourd"
				type: "item"
			}]
			x: 7.5d
			y: -5.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["生吃、烧烤、串成串.\\n\\n地底豆简直无所不能!"]
			id: "08D9C809CF4062E8"
			rewards: [{
				id: "42D4474823033895"
				item: {
					Count: 1
					id: "undergarden:underbean_on_a_stick"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "5F64479B146297BE"
				item: "undergarden:underbeans"
				type: "item"
			}]
			x: 8.0d
			y: -6.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["滴落果几乎能在任何生态群系生成——记得抬头看!\\n\\n它们生长在多数群系的顶部,类似发光浆果的生长方式."]
			id: "4568C2F465EDB960"
			rewards: [{
				count: 10
				id: "40EB047793AF98E2"
				item: "undergarden:droopvine_item"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "192B3F4E4A36C0B6"
				item: "undergarden:droopvine_item"
				type: "item"
			}]
			x: 7.0d
			y: -6.0d
		}
		{
			dependencies: [
				"1C232674CD04024D"
				"141681FE9E524FB0"
			]
			description: ["这是&7遗忘者&r穿戴的护甲,也是你获取它的来源.\\n\\n不过没有&a远古靴子&f,显然&7遗忘者&r没有脚."]
			id: "49EB9FB2133E3219"
			rewards: [{
				id: "6BC63C8EB24B2DED"
				type: "xp_levels"
				xp_levels: 5
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "7A0DABDDF01D5423"
					item: {
						Count: 1
						id: "undergarden:ancient_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "2C172CE16BBA49F2"
					item: {
						Count: 1
						id: "undergarden:ancient_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "29675CA8C6EACF13"
					item: {
						Count: 1
						id: "undergarden:ancient_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "远古盔甲"
			x: 6.0d
			y: 7.0d
		}
		{
			description: ["前往&2&l深暗之园&r的方式类似前往&c&l&a下界&f&r.你需要建造传送门.\\n\\n其形状尺寸与&a下界传送门&f相同,但要用&a石砖&f变种替代黑曜石,包括&a石砖&f、&a苔石砖&f或&a深板岩砖&f.\\n\\n建成后使用催化剂即可激活."]
			id: "6C4B93FE64BD418D"
			rewards: [{
				id: "5E5FBCE2B7C30700"
				item: "minecraft:diamond"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				id: "6582E66B9320B94B"
				item: "undergarden:catalyst"
				type: "item"
			}]
			title: "如何建造传送门"
			x: 2.0d
			y: -9.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&2穴居者&r是&2&l深暗之园&r的牛类生物.\\n它们被动温顺,死亡时会掉落皮革和穴居者肉.\\n\\n谢天谢地它们不能产奶——我可不想喝这些&2穴居者&r产的奶."
				"{image:atm:textures/questpics/undergarden/undergarden_dweller.png width:100 height:100 align:center}"
			]
			icon: "undergarden:dweller_spawn_egg"
			id: "17C2C8F64B4AE6EE"
			rewards: [{
				count: 5
				id: "7E5EEF5DF0AC1106"
				item: "undergarden:raw_dweller_meat"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:dweller"
				icon: "undergarden:dweller_spawn_egg"
				id: "4332BB29087E348A"
				type: "kill"
				value: 1L
			}]
			title: "&2穴居者"
			x: -6.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: [
				"&e蛮兵&r在部分生物群系生成且属于中立生物.\\n\\n若被攻击,区域内所有蛮兵都会反击,类似狼或&a僵尸猪灵&f.\\n\\n死亡时可能掉落獠牙,可作为骨头的替代品!"
				"{image:atm:textures/questpics/undergarden/undergarden_brute.png width:100 height:100 align:center}"
			]
			icon: "undergarden:brute_spawn_egg"
			id: "119242549BD21764"
			rewards: [{
				count: 2
				id: "3DDBB04BC978ACB5"
				item: "undergarden:brute_tusk"
				type: "item"
			}]
			size: 1.5d
			tasks: [{
				entity: "undergarden:brute"
				icon: "undergarden:brute_spawn_egg"
				id: "0E0BF08E84588327"
				type: "kill"
				value: 1L
			}]
			title: "&e蛮兵"
			x: 3.5d
			y: -0.5d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["弹弓是&2&l深暗之园&r新增的趣味武器,有4种弹药:\\n\\n&a史莱姆粘浆&f命中生物会造成粘滑效果\\n\\n深岩块是普通弹丸\\n\\n腐烂疱莓会爆炸\\n\\n而&e小榕果&r只会发出声响毫无杀伤力"]
			id: "1190BB433EFA3945"
			rewards: [{
				count: 5
				id: "250BD4197B2AB64B"
				item: "undergarden:rotten_blisterberry"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [{
				id: "5C5E12080C612D23"
				item: {
					Count: 1
					id: "undergarden:slingshot"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 5.5d
			y: 4.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["腐烂疱莓无法投掷,但制成疱莓炸弹后即可!\\n\\n还能进阶制作爆裂南瓜,效果类似会四处喷射疱莓的TNT."]
			id: "5E63C78CDC44FF95"
			rewards: [{
				count: 5
				id: "47A34F25F9C3DE31"
				item: "undergarden:blisterbomb"
				type: "item"
			}]
			shape: "square"
			size: 1.15d
			tasks: [
				{
					id: "245333BAF2116325"
					item: "undergarden:blisterbomb"
					type: "item"
				}
				{
					id: "027921BB67B819C7"
					item: "undergarden:boomgourd"
					type: "item"
				}
			]
			title: "爆炸物!!!"
			x: 6.5d
			y: 4.0d
		}
		{
			dependencies: ["1C232674CD04024D"]
			description: ["&a浆血蘑菇&f生长在它们的沼泽中.\\n\\n外形类似&a深色橡木&f,通体白色仅部分菌盖带有血斑!"]
			id: "6751F2651063B3A5"
			rewards: [{
				count: 5
				id: "479B006B22A2A066"
				item: "undergarden:blood_mushroom"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "6AB0706B07F12408"
				item: "undergarden:blood_mushroom"
				type: "item"
			}]
			x: 5.5d
			y: -7.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"此任务默认隐藏,若您看到本提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "5FA5ABAD6EF3BA0A"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "5BE7CFE79CC4D358"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "26117E1B78EEE1D7"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: 2.0d
			y: -4.5d
		}
	]
	title: "&d深暗之园&f"
}
