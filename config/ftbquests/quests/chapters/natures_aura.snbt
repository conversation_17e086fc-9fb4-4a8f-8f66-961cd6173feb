{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "natures_aura"
	group: "02FE661031A105D8"
	icon: "naturesaura:gold_leaf"
	id: "7A72E12C3839D165"
	images: [
		{
			height: 2.0d
			image: "naturesaura:item/gold_leaf"
			rotation: 0.0d
			width: 2.0d
			x: 11.0d
			y: -3.5d
		}
		{
			height: 2.0d
			image: "naturesaura:item/vacuum_bottle"
			rotation: 0.0d
			width: 2.0d
			x: 2.5d
			y: -1.5d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/natures_aura/natures_aura_title.png"
			rotation: 0.0d
			width: 12.1d
			x: 5.0d
			y: -3.5d
		}
		{
			height: 1.5d
			image: "naturesaura:item/eye_improved"
			rotation: 0.0d
			width: 1.5d
			x: 2.5d
			y: 1.5d
		}
		{
			height: 5.0d
			image: "irons_spellbooks:textures/particle/acid_bubble_3.png"
			rotation: 0.0d
			width: 5.0d
			x: -0.14d
			y: 0.14d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/natures_aura/natural_altar.png"
			rotation: 0.0d
			width: 9.859550561797754d
			x: 11.0d
			y: 0.0d
		}
	]
	order_index: 9
	quest_links: [ ]
	quests: [
		{
			dependencies: ["7DAED14CEC4CDA51"]
			description: ["破坏&e&a金树叶&f&r有较高概率掉落&e&a金树叶&f&r,这是制作&e&a金粉&f&r的必要材料."]
			id: "4F9D6109EEA5D26A"
			rewards: [
				{
					count: 2
					id: "5B02A6C891227D29"
					item: "naturesaura:gold_leaf"
					random_bonus: 2
					type: "item"
				}
				{
					id: "6CAA8129F43AE29B"
					type: "xp"
					xp: 50
				}
			]
			size: 1.25d
			tasks: [{
				count: 4L
				id: "46CB54014AC0447C"
				item: "naturesaura:gold_leaf"
				type: "item"
			}]
			title: "&e金树叶"
			x: 2.5d
			y: 0.0d
		}
		{
			dependencies: ["4F9D6109EEA5D26A"]
			description: [
				"&a&a森林仪式&f&r可通过搭建多方块结构完成:使用8个&a木墩&f、16份&e&a金粉&f&r,并根据配方选择橡树或&a丛林树苗&f.\\n\\n启动仪式时,将配方所需物品摆放在&a木墩&f上,并在中央催生对应的树苗.&e&a金粉&f&r会在仪式中消耗,因此需要大量储备."
				"{image:atm:textures/questpics/natures_aura/forest_ritual.png width:295 height:150 align:center}"
			]
			icon: "naturesaura:gold_powder"
			icon_scale: 1.1d
			id: "497AE687B7E95DE8"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "263B58408756F0D7"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "78FBBD798532E0C7"
					type: "xp"
					xp: 100
				}
			]
			shape: "rsquare"
			size: 1.4d
			tasks: [
				{
					id: "55628B1EECBB5E94"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "minecraft:oak_sapling"
								}
								{
									Count: 1b
									id: "minecraft:jungle_sapling"
								}
							]
						}
					}
					type: "item"
				}
				{
					count: 8L
					id: "531BA7E45499F20D"
					item: "naturesaura:wood_stand"
					type: "item"
				}
				{
					count: 16L
					id: "172E24F739EEA426"
					item: "naturesaura:gold_powder"
					type: "item"
				}
			]
			title: "&a&a森林仪式&f"
			x: 4.5d
			y: 0.0d
		}
		{
			dependencies: ["497AE687B7E95DE8"]
			description: ["&b&a环境之眼&f&r可通过&a&a森林仪式&f&r制作,可装备在饰品栏中,用于在屏幕左上角监控灵气浓度."]
			id: "12D3C7FC4B3DAB23"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0538EE90F51EC299"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "60D55CC4EC5F4DED"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "环境监测仪"
			tasks: [{
				id: "5C146B5DA9342F90"
				item: "naturesaura:eye"
				type: "item"
			}]
			x: 4.0d
			y: -1.5d
		}
		{
			dependencies: ["497AE687B7E95DE8"]
			description: ["&d&a远古树苗&f&r需通过&a&a森林仪式&f&r培育.成长后的树木会尝试维持正常灵气水平(既不会&a过高&f也不会过低).\\n\\n若灵气下降,树叶将开始枯萎并逆转生长过程."]
			hide_dependent_lines: true
			id: "22ADCF96AE3E609B"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "659656A2281281F0"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "64E310D44F8EE569"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "非普通&a远古树苗"
			tasks: [{
				id: "457413B665C31128"
				item: "naturesaura:ancient_sapling"
				type: "item"
			}]
			title: "&a远古树木"
			x: 5.0d
			y: -1.5d
		}
		{
			dependencies: ["497AE687B7E95DE8"]
			description: ["&b&a欢乐徽章&f&r是您应优先制作的代币,这些代币是&a&d自然灵气&f&r模组中物品的重要合成材料."]
			id: "05E668CE6687AC64"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3AB60B3BA5F7F8ED"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "0BBC6C584FC0AA3B"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "250CD873FCE7202F"
				item: "naturesaura:token_joy"
				type: "item"
			}]
			title: "&b&a欢乐徽章"
			x: 6.0d
			y: 0.0d
		}
		{
			dependencies: ["497AE687B7E95DE8"]
			description: ["手持&a带塞玻璃瓶&f在空中右键可收集灵气.不同维度采集的灵气形态各异.\\n\\n主世界获得&a日光灵气&r,&a下界&f获得&4幽灵灵气&r,&a末地&f获得&0暗影灵气&r.\\n\\n若使用&a带塞玻璃瓶&f的区域灵气严重匮乏,则会得到&0真空瓶&r."]
			id: "5A011CE8EEA3F9D8"
			rewards: [
				{
					id: "671E4F61F1B3699D"
					item: "naturesaura:vacuum_bottle"
					random_bonus: 1
					type: "item"
				}
				{
					id: "57755CC6A9E0B376"
					type: "xp"
					xp: 50
				}
			]
			subtitle: "灵气封装术"
			tasks: [
				{
					id: "2C08B46593A9B329"
					item: {
						Count: 1
						id: "naturesaura:aura_bottle"
						tag: {
							stored_type: "naturesaura:overworld"
						}
					}
					type: "item"
				}
				{
					id: "5FD10BCA425E745A"
					item: {
						Count: 1
						id: "naturesaura:aura_bottle"
						tag: {
							stored_type: "naturesaura:nether"
						}
					}
					type: "item"
				}
				{
					id: "6217C3256485AF70"
					item: {
						Count: 1
						id: "naturesaura:aura_bottle"
						tag: {
							stored_type: "naturesaura:end"
						}
					}
					type: "item"
				}
			]
			title: "灵气瓶"
			x: 4.5d
			y: 1.5d
		}
		{
			dependencies: [
				"5A011CE8EEA3F9D8"
				"3C2F5AB815A6C949"
			]
			description: ["&2&a树冠减光器&f&r与&2&a植物吸收器&f&r都是简单易用的灵气生成装置."]
			id: "71B6F013C8A60EB6"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4D8960CA9C40A84B"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "235D556AD4C2FDF8"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "更多灵气!"
			tasks: [
				{
					id: "7E80C1A891CB854B"
					item: "naturesaura:oak_generator"
					type: "item"
				}
				{
					id: "58E562C058F72AB0"
					item: "naturesaura:flower_generator"
					type: "item"
				}
			]
			title: "灵气生成"
			x: 6.0d
			y: 1.5d
		}
		{
			dependencies: ["3C2F5AB815A6C949"]
			description: ["&9&a灵气缓存器&f&r可用于储存&a灵气&r!"]
			id: "0B6D35F5B3A11FD2"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3F0A4F212A355533"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "487CF239ABD63AE8"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "灵气存储中"
			tasks: [{
				id: "1715E8DAFB3D3838"
				item: "naturesaura:aura_cache"
				type: "item"
			}]
			title: "&9&a灵气缓存器"
			x: 6.5d
			y: 3.0d
		}
		{
			dependencies: [
				"0B6D35F5B3A11FD2"
				"1D39634260A47C9A"
			]
			description: ["&b&a灵气宝库&f&r是&9&a灵气缓存器&f&r的升级版本."]
			id: "24599D45F5D1606E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "584BCEA94028B3F9"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "0EB4EAE31C361DAB"
					type: "xp"
					xp: 150
				}
			]
			subtitle: "灵气宝藏!"
			tasks: [{
				id: "0785B90A3359EF21"
				item: "naturesaura:aura_trove"
				type: "item"
			}]
			title: "&b&a灵气宝库"
			x: 6.5d
			y: 4.5d
		}
		{
			dependencies: ["05E668CE6687AC64"]
			description: ["&2&a自然祭坛核心&f&r是多方块结构,需1个&2&a核心&f、20块木板、16块&a石砖&f、8块&e&a金纹石砖&f&r和4块&a雕纹石砖&f组成.\\n\\n每个配方都会消耗灵气,该祭坛是模组进程的关键设备.\\n\\n任务&a右侧&f图示展示了多方块搭建方式."]
			icon: "naturesaura:nature_altar"
			id: "20F9EF925DB1CE05"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6236EF6A50E178BC"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "35A1A9938FEF4A1C"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [
				{
					id: "1C97A13C673C872C"
					item: "naturesaura:nature_altar"
					type: "item"
				}
				{
					count: 20L
					id: "0CA0B32D52395A39"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "minecraft:planks"
						}
					}
					type: "item"
				}
				{
					count: 16L
					id: "52874F5B79ECAE65"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "minecraft:stone_bricks"
						}
					}
					type: "item"
				}
				{
					count: 8L
					id: "005FC74EBECBBD39"
					item: "naturesaura:gold_brick"
					type: "item"
				}
				{
					count: 4L
					id: "1523CDF0F19A3162"
					item: "minecraft:chiseled_stone_bricks"
					type: "item"
				}
			]
			title: "&2自然祭坛"
			x: 8.0d
			y: 0.0d
		}
		{
			dependencies: ["5A011CE8EEA3F9D8"]
			description: ["&a灾厄村民之眼&f、&a烈焰之眼&f和&a潜影之眼&f分别用于定位特定结构."]
			id: "3F8A5CBEAE024748"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4388EAE6C8A04973"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "6F0FEC625987F600"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "结构追踪仪"
			tasks: [
				{
					id: "78363A5BFED389F3"
					item: "naturesaura:outpost_finder"
					type: "item"
				}
				{
					id: "39F677AFED023C6F"
					item: "naturesaura:fortress_finder"
					type: "item"
				}
				{
					id: "55C491B1083C46FB"
					item: "naturesaura:end_city_finder"
					type: "item"
				}
			]
			title: "结构定位器"
			x: 5.0d
			y: 3.0d
		}
		{
			dependencies: ["20F9EF925DB1CE05"]
			description: ["将&a铁锭&f置于&2&a自然祭坛核心&f&r上可制成&2&a灌注铁锭&f&r.\\n\\n此过程比制作&e污秽金锭&r耗时更长."]
			id: "3C2F5AB815A6C949"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1DEBE51A7FA6FDDB"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "0D7D898031CA25A1"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "687BAA9990A67C2E"
				item: "naturesaura:infused_iron"
				type: "item"
			}]
			title: "&2&a灌注铁锭"
			x: 7.5d
			y: 1.5d
		}
		{
			dependencies: ["20F9EF925DB1CE05"]
			description: ["将&e&a金锭&f&r置于&2&a自然祭坛核心&f&r上可制成&e&a污秽金锭&f&r.\\n\\n此过程比制作&2&a灌注铁锭&f&r耗时更长."]
			id: "3EC90ADA4E322B14"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "5968EAE4FAE64169"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "3EBAE5C8A4C59485"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "黄金污染术"
			tasks: [{
				id: "076D4BEB697AA55A"
				item: "naturesaura:tainted_gold"
				type: "item"
			}]
			title: "&e污秽金锭"
			x: 8.5d
			y: 1.5d
		}
		{
			dependencies: [
				"3C2F5AB815A6C949"
				"3EC90ADA4E322B14"
				"0EFD0A4AE82153E8"
			]
			description: [
				"&a献给众神&f是由1个&a供桌&f和36朵花组成的多方块结构.进行献祭需要召唤之灵.\\n"
				"{image:atm:textures/questpics/natures_aura/offering.png width:295 height:150 align:center}"
			]
			icon: "naturesaura:offering_table"
			id: "5C990F7E5D345A6F"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3AE65EA024BC5847"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "47D651BF2DE75C46"
					type: "xp"
					xp: 100
				}
			]
			shape: "pentagon"
			size: 1.25d
			subtitle: "在花林中向神明献上鸟浴池"
			tasks: [
				{
					id: "5FCFEA59AF1636DE"
					item: "naturesaura:offering_table"
					type: "item"
				}
				{
					count: 36L
					id: "2E97226C40818239"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "minecraft:flowers"
						}
					}
					type: "item"
				}
				{
					count: 3L
					id: "5CFDF0F9971BE465"
					item: "naturesaura:calling_spirit"
					type: "item"
				}
			]
			x: 8.0d
			y: 3.0d
		}
		{
			dependencies: ["5C990F7E5D345A6F"]
			description: ["若将&2&a灌注铁锭&f&r或&e&a污秽金锭&f&r置于&a供桌&f上,可获得&b天穹锭&r."]
			id: "1D39634260A47C9A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3B03F12214704259"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "4512677DDC4711A2"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "32333C6D1AD96106"
				item: "naturesaura:sky_ingot"
				type: "item"
			}]
			title: "&b天穹锭"
			x: 8.0d
			y: 4.5d
		}
		{
			dependencies: [
				"5C990F7E5D345A6F"
				"0EFD0A4AE82153E8"
			]
			description: ["在&a供桌&f上使用代币可对其进行升级.这些升级后的代币是制作更高级合成配方所必需的."]
			id: "4AD4FDF70B9BFCC9"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "6753674290A2752E"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "2DA2593ACF033861"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "18E1687150A37F37"
					item: "naturesaura:token_euphoria"
					type: "item"
				}
				{
					id: "03ED1F9B3A7E3CCD"
					item: "naturesaura:token_terror"
					type: "item"
				}
				{
					id: "7CEE6E6829D2BC96"
					item: "naturesaura:token_rage"
					type: "item"
				}
				{
					id: "5362B4DA434BDFF2"
					item: "naturesaura:token_grief"
					type: "item"
				}
			]
			title: "&a增强&f代币"
			x: 9.5d
			y: 3.0d
		}
		{
			dependencies: [
				"20F9EF925DB1CE05"
				"0EFD0A4AE82153E8"
			]
			description: ["这些催化剂可作为柱顶装饰添加到你的&2&a自然祭坛核心&f&r上.它们能让你将更多物品转化为其他物品或进行分解.\\n\\n&a粉碎催化器&f能使每个&a金叶&f分解为4份&a金粉&f,而非原本的2份!"]
			id: "54F7AD64A403C9BF"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "351DD42840C93A33"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "16F5C8E972132872"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "12FF35B444D5F641"
					item: "naturesaura:crushing_catalyst"
					type: "item"
				}
				{
					id: "67583A940E7BA925"
					item: "naturesaura:conversion_catalyst"
					type: "item"
				}
			]
			title: "催化剂"
			x: 8.0d
			y: -1.5d
		}
		{
			dependencies: [
				"4AD4FDF70B9BFCC9"
				"1D39634260A47C9A"
			]
			description: ["通过&2&a生育祭坛&f&r可大量生成原版生物.在灵气充沛的区域繁殖动物会生成&d生育之魂&r."]
			id: "0FD26FD88E801C0E"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0111441CEC9ECE8C"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "65E7041617B2E85A"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "魔法造娃术"
			tasks: [
				{
					id: "14BDAD1D8EF6D754"
					item: "naturesaura:animal_spawner"
					type: "item"
				}
				{
					id: "48828EAA097984AD"
					item: "naturesaura:birth_spirit"
					type: "item"
				}
			]
			title: "&2&a生育祭坛&f"
			x: 9.5d
			y: 4.5d
		}
		{
			description: ["&a&d自然灵气&f&r是一个关于运用与补充&a灵气&r的魔法模组.这些任务将带你了解基础知识.\\n\\n首先制作&e&a闪耀纤维&f&r,需要&e&a金粒&f&r、&a树叶&r和&a草&r.手持&e&a闪耀纤维&f&r右键点击树叶可将其转化为&e&a金叶&f&r.\\n\\n务必阅读&a&a自然灵气之书&f&r,书中还展示了&a&d自然灵气&f&r中多方块结构的搭建方法."]
			id: "7DAED14CEC4CDA51"
			rewards: [
				{
					id: "534E243D88E50AC6"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "naturesaura:book"
						}
					}
					type: "item"
				}
				{
					id: "1F5E315D07B2A65B"
					type: "xp"
					xp: 10
				}
			]
			shape: "hexagon"
			size: 2.0d
			subtitle: "萤火灵气"
			tasks: [{
				count: 16L
				id: "06D5CD358CFB4D2D"
				item: "naturesaura:gold_fiber"
				type: "item"
			}]
			title: "&e欢迎来到&a&d自然灵气&f"
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["05E668CE6687AC64"]
			description: ["制作其他物品时同样需要这些代币."]
			hide_dependent_lines: true
			id: "0EFD0A4AE82153E8"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "093DB2A3C28FF57F"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "3AE41DBD10734ED3"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "6B46BB7EF0CA76B4"
					item: "naturesaura:token_fear"
					type: "item"
				}
				{
					id: "7C59F509A0D8CEB0"
					item: "naturesaura:token_anger"
					type: "item"
				}
				{
					id: "72C1E0F2F9F923BD"
					item: "naturesaura:token_sorrow"
					type: "item"
				}
			]
			title: "其他代币"
			x: 6.0d
			y: -1.5d
		}
		{
			dependencies: ["1D39634260A47C9A"]
			description: ["在合成网格中将2个&b天穹锭&r、2个&e&a污染黄金&f&r和1个&5&a下界合金碎片&f&r组合可制成&d深渊锭&r."]
			id: "2B4D4E741D855B5A"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2D1AC5C000589417"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "75B109A8430AAA7B"
					type: "xp"
					xp: 250
				}
			]
			tasks: [{
				id: "55F7A32C63A206B2"
				item: "naturesaura:depth_ingot"
				type: "item"
			}]
			title: "&d深渊锭"
			x: 8.5d
			y: 6.0d
		}
		{
			dependencies: [
				"1D39634260A47C9A"
				"22ADCF96AE3E609B"
			]
			description: ["&a远古知识之杖&f会消耗灵气高亮显示附近的&5&a远古残骸&f&r."]
			id: "34ABEF75AE1E5A0F"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "01AEE39CBB7624D8"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "2625AC546F7332B8"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "下界合金探测器"
			tasks: [{
				id: "159513977921D09B"
				item: "naturesaura:netherite_finder"
				type: "item"
			}]
			x: 7.5d
			y: 6.0d
		}
		{
			dependencies: ["5A011CE8EEA3F9D8"]
			description: ["&5&a末影板条箱&f&r是可运输物品的&5&a末影箱子&f&r.\\n\\n&5&a末影目镜&f&r是&5&a末影板条箱&f&r的手持便携版."]
			id: "1ABEC7DE47BD6286"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2FD9E95B39CF60B3"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "40699ABA2D6B77A6"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "穿梭自如!"
			tasks: [
				{
					id: "4D6D4F663B472B77"
					item: "naturesaura:ender_crate"
					type: "item"
				}
				{
					id: "49E7547CEA050C39"
					item: "naturesaura:ender_access"
					type: "item"
				}
			]
			title: "&5&a末影板条箱&f与末影目镜"
			x: 4.0d
			y: 3.0d
		}
		{
			dependencies: [
				"4AD4FDF70B9BFCC9"
				"22ADCF96AE3E609B"
			]
			description: ["&b&a财富之杖&f&r会消耗灵气高亮显示64格范围内所有容器约&a1分钟&f.\\n\\n在寻找战利品箱或矿洞中存放的箱子时极为实用."]
			id: "4F03B152479C4AC0"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "69BAA9DC96AAD6CC"
					table_id: 901783880121977398L
					type: "random"
				}
				{
					id: "010E0886F2156704"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				id: "7610B30E5D604349"
				item: "naturesaura:loot_finder"
				type: "item"
			}]
			title: "&b&a财富之杖&f"
			x: 11.0d
			y: 3.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若你看到本提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "3AEA0FD1D5730CF2"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "7581F4AEFE77AE2E"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "7D2F9E36EFB00065"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: 2.0d
		}
	]
	title: "&d自然灵气&f"
}
