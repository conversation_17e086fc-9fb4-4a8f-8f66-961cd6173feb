{
	default_hide_dependency_lines: false
	default_quest_shape: "hexagon"
	filename: "creative"
	group: "2084F3F6FB861C5B"
	icon: "functionalstorage:creative_vending_upgrade"
	id: "16956970FF49BB4D"
	images: [
		{
			height: 4.0d
			hover: ["&aATM之星&f有何用途？"]
			image: "atm:textures/questpics/creative.png"
			rotation: 0.0d
			width: 16.0d
			x: 0.0d
			y: -4.0d
		}
		{
			height: 2.0d
			image: "allthetweaks:textures/item/atm_star.png"
			rotation: 20.0d
			width: 2.0d
			x: 4.0d
			y: -3.5d
		}
		{
			height: 2.0d
			image: "allthetweaks:textures/item/atm_star.png"
			rotation: -20.0d
			width: 2.0d
			x: -4.0d
			y: -3.5d
		}
	]
	order_index: 2
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["464D0C17601E8A2B"]
			description: ["若你制作9颗星星,或足够合成ATM&a流星块&f,就能培育&d&a星空蜜蜂&f&r.这种蜜蜂会从蜂巢中产出&e&aATM之星&f碎片&r.\\n\\n哦对了,它们还需要另一个ATM&a流星块&f作为花源!很简单对吧？"]
			hide_dependency_lines: true
			id: "6E6FDF551EA4FF1A"
			rewards: [
				{
					id: "13C7B1760109C468"
					item: {
						Count: 1
						id: "allthemodium:alloy_axe"
						tag: {
							affix_data: {
								affixes: {
									"apotheosis:durable": 0.8f
									"apotheosis:heavy_weapon/attribute/annihilating": 0.20009226f
									"apotheosis:heavy_weapon/attribute/berserking": 0.34490293f
									"apotheosis:heavy_weapon/attribute/giant_slaying": 0.80212396f
									"apotheosis:heavy_weapon/attribute/murderous": 0.9636011f
									"apotheosis:heavy_weapon/attribute/shredding": 0.52882904f
									"apotheosis:heavy_weapon/special/cleaving": 0.57667243f
									"apotheosis:heavy_weapon/special/executing": 0.42035472f
									"apotheosis:sword/mob_effect/elusive": 0.27058095f
								}
								name: "{\"color\":\"rainbow\",\"translate\":\"%2$s\",\"with\":[\"\",\"\"]}"
								rarity: "apotheosis:ancient"
								sockets: 5
								uuids: [[I;
									458049447
									-427996311
									-1781331551
									1681014930
								]]
							}
							display: {
								Name: "{\"text\":\"AlfredGG的终极战斧\"}"
							}
						}
					}
					type: "item"
				}
				{
					id: "5AC6632FD554B8F2"
					item: "reliquary:pedestals/passive/white_passive_pedestal"
					type: "item"
				}
				{
					id: "1F4E8436E25D423D"
					type: "xp_levels"
					xp_levels: 1000
				}
			]
			shape: "hexagon"
			size: 3.0d
			subtitle: "足够制作整合包所有物品？"
			tasks: [
				{
					count: 2L
					id: "529AEED3E1A07228"
					item: "allthetweaks:atm_star_block"
					type: "item"
				}
				{
					id: "5C995984E37F6C5A"
					item: {
						Count: 1
						id: "productivebees:configurable_honeycomb"
						tag: {
							EntityTag: {
								type: "productivebees:starry"
							}
						}
					}
					type: "item"
				}
			]
			title: "终身保障"
			x: 0.0d
			y: -0.5d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			hide_dependency_lines: true
			id: "3F833B656A0DBB0E"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "64982F4157A67B93"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			shape: "hexagon"
			tasks: [{
				id: "5973E65E0C940E27"
				item: "ars_nouveau:creative_spell_book"
				type: "item"
			}]
			title: "&a创造模式法术书&f"
			x: -3.0d
			y: 0.0d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			hide_dependency_lines: true
			id: "2CF11A70229000AB"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "1A639A768F7EF8FC"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [
				{
					id: "0CF133CEADDC504C"
					item: "create:creative_motor"
					type: "item"
				}
				{
					id: "72936F6095FF124A"
					item: "create:creative_blaze_cake"
					type: "item"
				}
			]
			title: "创意启程"
			x: 2.0d
			y: -1.5d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			hide_dependency_lines: true
			id: "5C7B81756CA58056"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "3B5F42272CCB9F9D"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "775BD503F830BB6C"
				item: "botania:creative_pool"
				type: "item"
			}]
			title: "无限魔力"
			x: -2.0d
			y: -1.5d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			id: "695C8159D28F16B7"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "2208E7A037EAB0CE"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "2E44EAD876619015"
				item: "ars_nouveau:creative_source_jar"
				type: "item"
			}]
			title: "&a创造魔源罐&f"
			x: 2.0d
			y: 0.5d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			id: "653487501398DECA"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "743B3A79F4825FDD"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			size: 1.5d
			tasks: [{
				id: "5C777F84736455DA"
				item: {
					Count: 1
					id: "ironjetpacks:jetpack"
					tag: {
						Id: "ironjetpacks:creative"
						Throttle: 1.0d
					}
				}
				match_nbt: true
				type: "item"
			}]
			title: "&a创造喷气背包&f"
			x: -2.0d
			y: 0.5d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			id: "58095E9EBC6FF9B2"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "1DAEDC2E98F1482B"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			tasks: [{
				id: "317121010F937E4F"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "integrateddynamics:energy_battery_creative"
							}
							{
								Count: 1b
								id: "mekanism:creative_energy_cube"
								tag: {
									mekData: {
										EnergyContainers: [{
											Container: 0b
											stored: "18446744073709551615.9999"
										}]
									}
								}
							}
							{
								Count: 1b
								id: "ae2:creative_energy_cell"
							}
							{
								Count: 1b
								id: "createaddition:creative_energy"
							}
						]
					}
				}
				title: "创造模式能源选项"
				type: "item"
			}]
			title: "创造能量"
			x: 3.0d
			y: -1.0d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			id: "721EA7CB1CBBFD14"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "14CC40665D74FF8C"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			tasks: [{
				id: "19CB734FC0897E75"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "refinedstorage:creative_controller"
								tag: {
									Energy: 32000
								}
							}
							{
								Count: 1b
								id: "ae2:creative_energy_cell"
							}
						]
					}
				}
				title: "创造模式虚拟存储能量"
				type: "item"
			}]
			title: "创造虚拟存储能量"
			x: 3.0d
			y: 0.0d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			id: "6C706326381CE611"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "2578E2D5023264B9"
				table_id: 7175652334583451871L
				type: "choice"
			}]
			tasks: [{
				id: "7052974A3E7D3DF2"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "pneumaticcraft:creative_compressor"
							}
							{
								Count: 1b
								id: "pneumaticcraft:creative_compressed_iron_block"
							}
						]
					}
				}
				title: "创造模式压力"
				type: "item"
			}]
			title: "创造压力"
			x: -3.0d
			y: -1.0d
		}
		{
			dependencies: ["6E6FDF551EA4FF1A"]
			description: ["你觉得这足够制作格雷之星了吗？"]
			hide_dependency_lines: true
			id: "0019BAE826902B4A"
			optional: true
			rewards: [{
				id: "248CEF5CDCC4D98C"
				type: "xp_levels"
				xp_levels: 10000
			}]
			size: 3.0d
			tasks: [{
				id: "05EDE3C0706A89A7"
				item: "allthecompressed:atm_star_block_9x"
				type: "item"
			}]
			title: "但是...为什么？"
			x: -1.5d
			y: 3.0d
		}
		{
			dependencies: ["464D0C17601E8A2B"]
			id: "39BD43AAEFBD5609"
			optional: true
			rewards: [
				{
					id: "0F1BEDEC9A4A479D"
					type: "xp_levels"
					xp_levels: 5000
				}
				{
					id: "04566CE860FF3B61"
					item: {
						Count: 1
						id: "gtceu:polybenzimidazole_plunger"
						tag: {
							DisallowContainerItem: 0b
							GT.Behaviours: { }
							GT.Tool: {
								AttackDamage: 0.0f
								AttackSpeed: -2.4f
								Damage: 0
								MaxDamage: 127
							}
							HideFlags: 2
							display: {
								Name: "{\"text\":\"非中子物质活塞\"}"
							}
						}
					}
					type: "item"
				}
			]
			size: 3.0d
			subtitle: "格雷会感到骄傲吗？"
			tasks: [{
				id: "45119551D2C9EE55"
				item: "allthetweaks:greg_star"
				type: "item"
			}]
			title: "格雷之星"
			x: 1.5d
			y: 3.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经明确授权,本任务不得用于任何非&eAllTheMods团队&r发布的公开整合包."
				""
				""
				""
				"此任务默认隐藏,若你看到此提示,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "7B82BB23454C6D44"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "543A08791D27BC62"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
				{
					id: "71F7DA56C55F4676"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: 1.5d
		}
	]
	title: "§a第三章§r:§d创造模式"
}
