{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "bounty_board"
	group: ""
	icon: "minecraft:zombie_head"
	id: "18A429E7F56AF5A9"
	images: [{
		height: 3.0d
		image: "atm:textures/questpics/bounty.png"
		rotation: 0.0d
		width: 12.0d
		x: 3.0d
		y: -8.0d
	}]
	order_index: 2
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			hide_until_deps_visible: false
			id: "2B05A29C62676EB2"
			rewards: [
				{
					id: "0FE23E01BFCD22FA"
					type: "xp"
					xp: 10
				}
				{
					count: 5
					id: "4247F14C46042AFC"
					item: "minecraft:rotten_flesh"
					type: "item"
				}
			]
			subtitle: "击杀5只僵尸"
			tasks: [{
				entity: "minecraft:zombie"
				icon: "minecraft:zombie_head"
				id: "5457CD8C1ABA0B9E"
				title: "击杀5只僵尸"
				type: "kill"
				value: 5L
			}]
			title: "§l§9主世界悬赏:§r§e僵尸"
			x: -4.0d
			y: -0.5d
		}
		{
			dependencies: ["2B05A29C62676EB2"]
			hide_until_deps_visible: true
			id: "444ACE285311ECB4"
			rewards: [
				{
					id: "3ED7AA82E00DCFD5"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "6A111F9EEADA4BA2"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:zombie"
				icon: "minecraft:zombie_head"
				id: "3AD10C31CB6BA4C2"
				title: "击杀10只僵尸"
				type: "kill"
				value: 10L
			}]
			x: -4.0d
			y: -2.0d
		}
		{
			dependencies: ["444ACE285311ECB4"]
			id: "146232B8504789C1"
			rewards: [
				{
					id: "267B3DBA7DED8C3B"
					type: "xp"
					xp: 50
				}
				{
					count: 20
					id: "0030F09FB9D2DE22"
					item: "minecraft:rotten_flesh"
					type: "item"
				}
				{
					count: 10
					id: "03A2CFC2335A776A"
					item: "minecraft:iron_ingot"
					random_bonus: 10
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "3FDDD3EECB6D7A85"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:zombie"
				icon: "minecraft:zombie_head"
				id: "51471254BFEBDAA7"
				title: "击杀50只僵尸"
				type: "kill"
				value: 50L
			}]
			x: -4.0d
			y: -3.5d
		}
		{
			dependencies: ["146232B8504789C1"]
			id: "7D5B36BF3EC0C93F"
			rewards: [
				{
					id: "0A77CE5C75957CD7"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "180D2F31E833E6B5"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:zombie"
				icon: "minecraft:zombie_head"
				id: "5056DC37AEF523E5"
				title: "击杀100只僵尸"
				type: "kill"
				value: 100L
			}]
			x: -4.0d
			y: -5.0d
		}
		{
			id: "42822B1E8A53D051"
			rewards: [
				{
					id: "40AC6E73C9411610"
					type: "xp"
					xp: 10
				}
				{
					count: 5
					id: "47DAFBFECB3B54BE"
					item: "minecraft:bone"
					type: "item"
				}
			]
			subtitle: "击杀5只骷髅"
			tasks: [{
				entity: "minecraft:skeleton"
				icon: "minecraft:skeleton_skull"
				id: "5B35720DF93CE2DB"
				title: "击杀5只骷髅"
				type: "kill"
				value: 5L
			}]
			title: "§l§9主世界悬赏:§r§e骷髅"
			x: -2.0d
			y: -0.5d
		}
		{
			dependencies: ["42822B1E8A53D051"]
			hide_until_deps_visible: true
			id: "4748831E75A840BE"
			rewards: [
				{
					id: "0D2AAD3B11A32E90"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "056975CFED74BF64"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:skeleton"
				icon: "minecraft:skeleton_skull"
				id: "015CB0D172D0712A"
				title: "击杀10只骷髅"
				type: "kill"
				value: 10L
			}]
			x: -2.0d
			y: -2.0d
		}
		{
			dependencies: ["4748831E75A840BE"]
			id: "410E0DD607CB3469"
			rewards: [
				{
					id: "02E2F8ED5DB84B97"
					type: "xp"
					xp: 50
				}
				{
					count: 20
					id: "7A227BD20C66223A"
					item: "minecraft:bone"
					type: "item"
				}
				{
					count: 20
					id: "342AD2A33F43C0EE"
					item: "minecraft:arrow"
					random_bonus: 20
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "649295B3143B081D"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:skeleton"
				icon: "minecraft:skeleton_skull"
				id: "2E17443A1BF2A0DA"
				title: "击杀50只骷髅"
				type: "kill"
				value: 50L
			}]
			x: -2.0d
			y: -3.5d
		}
		{
			dependencies: ["410E0DD607CB3469"]
			id: "555957ED58ABF8F8"
			rewards: [
				{
					id: "3F6EFAED74D8B549"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4F90CF0B7D2FED9B"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:skeleton"
				icon: "minecraft:skeleton_skull"
				id: "00655AC734444E54"
				title: "击杀100只骷髅"
				type: "kill"
				value: 100L
			}]
			x: -2.0d
			y: -5.0d
		}
		{
			id: "77FC692AC94D2EEF"
			rewards: [
				{
					count: 5
					id: "311AAB7A9D64E946"
					item: "minecraft:gunpowder"
					type: "item"
				}
				{
					id: "02E39788C2347A3F"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "击杀5只苦力怕"
			tasks: [{
				entity: "minecraft:creeper"
				icon: "minecraft:creeper_head"
				id: "423AF6C6647B1626"
				title: "击杀5只苦力怕"
				type: "kill"
				value: 5L
			}]
			title: "§l§9主世界悬赏:§r§e苦力怕"
			x: 0.0d
			y: -0.5d
		}
		{
			dependencies: ["77FC692AC94D2EEF"]
			hide_until_deps_visible: true
			id: "65BBA5C0DAEEC31F"
			rewards: [
				{
					id: "687FF91B1E08FDFF"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "01F13A0B354BBBFD"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:creeper"
				icon: "minecraft:creeper_head"
				id: "1AB92CF7F0246D21"
				title: "击杀10只苦力怕"
				type: "kill"
				value: 10L
			}]
			x: 0.0d
			y: -2.0d
		}
		{
			dependencies: ["65BBA5C0DAEEC31F"]
			id: "76A29816F19E33A2"
			rewards: [
				{
					id: "22546D5D3D7EB48C"
					type: "xp"
					xp: 50
				}
				{
					count: 20
					id: "27F653E02AC93F25"
					item: "minecraft:gunpowder"
					type: "item"
				}
				{
					count: 5
					id: "4A85485A2BF26504"
					item: "supplementaries:bomb"
					random_bonus: 5
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "2C419A310D1686C7"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:creeper"
				icon: "minecraft:creeper_head"
				id: "24BA89BD9898759B"
				title: "击杀50只苦力怕"
				type: "kill"
				value: 50L
			}]
			x: 0.0d
			y: -3.5d
		}
		{
			dependencies: ["76A29816F19E33A2"]
			id: "5FCA4FF8C135435E"
			rewards: [
				{
					id: "4648D5CB942BAA4A"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "7A4B7D60F897D023"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:creeper"
				icon: "minecraft:creeper_head"
				id: "045D1087A2692360"
				title: "击杀100只苦力怕"
				type: "kill"
				value: 100L
			}]
			x: 0.0d
			y: -5.0d
		}
		{
			description: [
				"此处列出了通过击杀敌人可获得的所有奖励."
				""
				"本页面内容仍在完善中!"
			]
			id: "41C0948CD9D50322"
			rewards: [{
				id: "55CF29758364D4B2"
				type: "xp"
				xp: 10
			}]
			size: 2.0d
			subtitle: "击杀所有&a生物&f"
			tasks: [{
				id: "3C380961550177C2"
				title: "&a悬赏榜&f"
				type: "checkmark"
			}]
			x: 3.0d
			y: 2.0d
		}
		{
			id: "728BE1816DA23DC0"
			rewards: [
				{
					id: "73BAA6AD36E525E5"
					type: "xp"
					xp: 1000
				}
				{
					exclude_from_claim_all: true
					id: "72DC79DE53E77CAD"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			size: 1.5d
			tasks: [{
				entity: "minecraft:ender_dragon"
				icon: "minecraft:dragon_head"
				id: "1809F1F9A3043683"
				title: "&a击杀末影龙&f"
				type: "kill"
				value: 1L
			}]
			x: 1.5d
			y: 4.5d
		}
		{
			id: "09C82CDDA800D8C9"
			rewards: [
				{
					id: "47314637684743A3"
					type: "xp"
					xp: 1000
				}
				{
					exclude_from_claim_all: true
					id: "12C0D9B030D64A4B"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			size: 1.5d
			tasks: [{
				entity: "minecraft:wither"
				icon: "minecraft:wither_skeleton_skull"
				id: "171FD27057746E80"
				title: "击杀凋灵"
				type: "kill"
				value: 1L
			}]
			x: 3.0000000000000004d
			y: 4.5d
		}
		{
			id: "08D1CC753F6B4283"
			rewards: [
				{
					id: "67970FDC5848EF61"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4BB86DB9D806D3E4"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			size: 1.5d
			tasks: [{
				entity: "minecraft:elder_guardian"
				icon: "minecraft:elder_guardian_spawn_egg"
				id: "2F28B803C75DA3B2"
				title: "击杀远古守卫者"
				type: "kill"
				value: 1L
			}]
			x: 4.5d
			y: 4.5d
		}
		{
			id: "56DA46DC82F6665D"
			rewards: [
				{
					count: 10
					id: "4DFA285786102C2B"
					item: "ars_nouveau:wilden_spike"
					type: "item"
				}
				{
					id: "20AEE2C8F4472343"
					type: "xp"
					xp: 1000
				}
				{
					exclude_from_claim_all: true
					id: "380E221DF1F736A8"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			size: 1.5d
			subtitle: "这还不是我的最终形态"
			tasks: [{
				entity: "ars_nouveau:wilden_boss"
				icon: "ars_nouveau:wilden_tribute"
				id: "452B65E139D9E12D"
				title: "击杀&a荒野奇美拉&f"
				type: "kill"
				value: 1L
			}]
			x: 4.550000000000001d
			y: 6.0d
		}
		{
			description: ["'在制作ATM7任务线时,流浪商人觉得在任务界面不断推搡我很有趣.\\n\\n干掉他们.全部.'\\n\\n- AlfredGG"]
			id: "0F55D0B4D5094EDB"
			optional: true
			rewards: [
				{
					id: "26EF416511A49817"
					item: {
						Count: 1
						id: "minecraft:iron_sword"
						tag: {
							Damage: 0
							display: {
								Lore: ["[{\"text\":\"献给那些敢于屠戮强大又恼人的流浪商人的勇者\",\"italic\":false}]"]
								Name: "[{\"text\":\"阿尔弗雷德GG之剑\",\"italic\":false}]"
							}
						}
					}
					type: "item"
				}
				{
					id: "2A9810116395C984"
					type: "xp"
					xp: 100
				}
				{
					id: "739F0D5CD8C7BA5E"
					item: "supplementaries:pedestal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "66C49399D8CCD721"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			size: 1.5d
			subtitle: "'这是私人恩怨' - AlfredGG"
			tasks: [
				{
					entity: "minecraft:wandering_trader"
					icon: "minecraft:trader_llama_spawn_egg"
					id: "596ABB45C0612AAF"
					title: "击杀村民商人"
					type: "kill"
					value: 1L
				}
				{
					entity: "minecraft:trader_llama"
					icon: "minecraft:trader_llama_spawn_egg"
					id: "1D136D138EF82DA9"
					title: "击杀商贩羊驼"
					type: "kill"
					value: 2L
				}
			]
			title: "杀死流浪商人和他烦人的羊驼"
			x: 3.0500000000000003d
			y: 6.0d
		}
		{
			icon: "minecraft:ender_pearl"
			id: "17F3AC15ADCFB175"
			rewards: [
				{
					count: 5
					id: "203C5104D0EE9774"
					item: "minecraft:ender_pearl"
					type: "item"
				}
				{
					id: "36F858FD06683839"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "击杀5只末影人"
			tasks: [{
				entity: "minecraft:enderman"
				icon: "minecraft:ender_pearl"
				id: "0EA4B08DAAFA4287"
				title: "&l&9&a末地&f悬赏:&r&e末影人"
				type: "kill"
				value: 5L
			}]
			title: "§l§9§a末地§f悬赏:§r§e末影人"
			x: 10.0d
			y: -0.5d
		}
		{
			dependencies: ["17F3AC15ADCFB175"]
			hide_until_deps_visible: true
			icon: "minecraft:ender_pearl"
			id: "2916F873A4DA84E8"
			rewards: [
				{
					id: "1E180EED68717968"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "2666996F798768C7"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:enderman"
				icon: "minecraft:ender_pearl"
				id: "58BF5D3267CF02F8"
				title: "击杀10只末影人"
				type: "kill"
				value: 10L
			}]
			x: 10.0d
			y: -2.0d
		}
		{
			dependencies: ["2916F873A4DA84E8"]
			icon: "minecraft:ender_pearl"
			id: "2AAF57D1D8AB6BF8"
			rewards: [
				{
					id: "6A0CF631E02D49A1"
					type: "xp"
					xp: 50
				}
				{
					count: 8
					id: "05A65D5FBBCE30C9"
					item: "minecraft:ender_pearl"
					random_bonus: 8
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "46897BBCAF61CBCB"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:enderman"
				icon: "minecraft:ender_pearl"
				id: "6BA6847A172C8DD7"
				title: "击杀50只末影人"
				type: "kill"
				value: 50L
			}]
			x: 10.0d
			y: -3.5d
		}
		{
			dependencies: ["2AAF57D1D8AB6BF8"]
			id: "479A4E778D8D7317"
			rewards: [
				{
					id: "2BC1E9EBB5267A83"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "4EEDA3086FA8D846"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:enderman"
				icon: "minecraft:ender_eye"
				id: "53C23DB8B4D1CEE2"
				title: "击杀100只末影人"
				type: "kill"
				value: 100L
			}]
			x: 10.0d
			y: -5.0d
		}
		{
			icon: "minecraft:spider_eye"
			id: "1F18B64C84C8809D"
			rewards: [
				{
					count: 5
					id: "275D3078C224D84C"
					item: "minecraft:spider_eye"
					type: "item"
				}
				{
					id: "18E8AA34DB10D4AF"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "击杀5只蜘蛛"
			tasks: [{
				entity: "minecraft:spider"
				icon: "minecraft:spider_eye"
				id: "18644A08B73A4B12"
				type: "kill"
				value: 5L
			}]
			title: "§l§9主世界悬赏:§r§e蜘蛛"
			x: 2.0d
			y: -0.5d
		}
		{
			dependencies: ["1F18B64C84C8809D"]
			hide_until_deps_visible: true
			id: "283CA6DB77D5D2EB"
			rewards: [
				{
					id: "39C45D0CB4075128"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "4A6C2A97D769418B"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:spider"
				icon: "minecraft:spider_eye"
				id: "5801AF8000768808"
				title: "击杀10只蜘蛛"
				type: "kill"
				value: 10L
			}]
			x: 2.0d
			y: -2.0d
		}
		{
			dependencies: ["283CA6DB77D5D2EB"]
			id: "63A1F25DF658928A"
			rewards: [
				{
					id: "5E0D1C18A4137319"
					type: "xp"
					xp: 50
				}
				{
					count: 10
					id: "6D9B9C1582E05FFD"
					item: "minecraft:string"
					random_bonus: 10
					type: "item"
				}
				{
					count: 20
					id: "13133B21B6680DA4"
					item: "minecraft:spider_eye"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "319B63A612336BE5"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:spider"
				icon: "minecraft:spider_eye"
				id: "0F94F7427F99DAFE"
				title: "击杀50只蜘蛛"
				type: "kill"
				value: 50L
			}]
			x: 2.0d
			y: -3.5d
		}
		{
			dependencies: ["63A1F25DF658928A"]
			id: "2D7E335B9D780E70"
			rewards: [
				{
					id: "0673C1611D1BC1FD"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2B8E13883A6DEF5F"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:spider"
				icon: "minecraft:spider_eye"
				id: "0F8ACB07CFA8CB33"
				title: "击杀100只蜘蛛"
				type: "kill"
				value: 100L
			}]
			x: 2.0d
			y: -5.0d
		}
		{
			icon: "minecraft:blaze_powder"
			id: "6141DE779232C8AA"
			rewards: [
				{
					count: 5
					id: "651B5C4C1024BC53"
					item: "minecraft:blaze_rod"
					type: "item"
				}
				{
					id: "4FB5BA9D969A1025"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "击杀5只烈焰人"
			tasks: [{
				entity: "minecraft:blaze"
				icon: "minecraft:blaze_powder"
				id: "1718CA0F8978181C"
				title: "&l&c&a下界&f悬赏:&r&e烈焰人"
				type: "kill"
				value: 5L
			}]
			title: "§l§c§a下界§f悬赏:§r§e烈焰人"
			x: 6.0d
			y: -0.5d
		}
		{
			dependencies: ["6141DE779232C8AA"]
			hide_until_deps_visible: true
			id: "4F48A4839B549C92"
			rewards: [
				{
					id: "10A5CCDDA3FABD37"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "7E7B0138373F4D28"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:blaze"
				icon: "minecraft:blaze_rod"
				id: "00445193DEB7EAA0"
				title: "击杀10只烈焰人"
				type: "kill"
				value: 10L
			}]
			x: 6.0d
			y: -2.0d
		}
		{
			dependencies: ["4F48A4839B549C92"]
			id: "6C9BC3A699E57162"
			rewards: [
				{
					id: "06776533F6B5ABCA"
					type: "xp"
					xp: 50
				}
				{
					count: 20
					id: "3D8FB446CA3F27F4"
					item: "minecraft:blaze_rod"
					random_bonus: 10
					type: "item"
				}
				{
					count: 10
					id: "5A1F2CBC8D91C5BE"
					item: "minecraft:blaze_powder"
					random_bonus: 10
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "3DD5D5A5D39C411C"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:blaze"
				icon: "minecraft:blaze_rod"
				id: "404D3420276BD963"
				title: "击杀50只烈焰人"
				type: "kill"
				value: 50L
			}]
			x: 6.0d
			y: -3.5d
		}
		{
			dependencies: ["6C9BC3A699E57162"]
			id: "35CC1F1DA9530688"
			rewards: [
				{
					id: "117A52F30802CC99"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "0845C88CA6D644A3"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:blaze"
				icon: "minecraft:blaze_rod"
				id: "2038156A13C07BFC"
				title: "击杀100只烈焰人"
				type: "kill"
				value: 100L
			}]
			x: 6.0d
			y: -5.0d
		}
		{
			id: "5AC497F76A086A5C"
			rewards: [
				{
					count: 5
					id: "310682051A0C2291"
					item: "minecraft:glass_bottle"
					type: "item"
				}
				{
					id: "3545146C9E60CD9B"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "击杀5只女巫"
			tasks: [{
				entity: "minecraft:witch"
				icon: {
					Count: 1
					id: "minecraft:potion"
					tag: {
						Potion: "minecraft:invisibility"
					}
				}
				id: "7BAB5E17D2DE4E3E"
				title: "§l§9主世界悬赏:§r§e女巫"
				type: "kill"
				value: 5L
			}]
			title: "§l§9主世界悬赏:§r§e女巫"
			x: 4.0d
			y: -0.5d
		}
		{
			dependencies: ["5AC497F76A086A5C"]
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "minecraft:invisibility"
				}
			}
			id: "6E4FD0B568BEB3F9"
			rewards: [
				{
					id: "15E0578B1F9521EF"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "6CCE54980E8D2003"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:witch"
				icon: {
					Count: 1
					id: "minecraft:potion"
					tag: {
						Potion: "minecraft:leaping"
					}
				}
				id: "7127A76DEB4D1696"
				title: "击杀10只女巫"
				type: "kill"
				value: 10L
			}]
			x: 4.0d
			y: -2.0d
		}
		{
			dependencies: ["6E4FD0B568BEB3F9"]
			id: "2D136FCDA92C92AC"
			rewards: [
				{
					id: "0165B8CC821A943F"
					type: "xp"
					xp: 50
				}
				{
					count: 10
					id: "52854A638D3FE878"
					item: "minecraft:redstone"
					random_bonus: 10
					type: "item"
				}
				{
					count: 10
					id: "0049AE95E8647336"
					item: "minecraft:sugar"
					random_bonus: 10
					type: "item"
				}
				{
					count: 10
					id: "2A0CD06747FBE0D8"
					item: "minecraft:glowstone_dust"
					random_bonus: 10
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "3F4C40818D552881"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:witch"
				icon: {
					Count: 1
					id: "minecraft:potion"
					tag: {
						Potion: "minecraft:slowness"
					}
				}
				id: "78A782913B7E6353"
				title: "击杀50只女巫"
				type: "kill"
				value: 50L
			}]
			x: 4.0d
			y: -3.5d
		}
		{
			dependencies: ["2D136FCDA92C92AC"]
			id: "6F93A02E620C69FE"
			rewards: [
				{
					id: "7FF2D930AF11BB82"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2019E33C08375AA8"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:witch"
				icon: {
					Count: 1
					id: "minecraft:splash_potion"
					tag: {
						Potion: "ars_nouveau:shielding_potion_strong"
					}
				}
				id: "5A3D6416BA0C7A20"
				title: "击杀100只女巫"
				type: "kill"
				value: 100L
			}]
			x: 4.0d
			y: -5.0d
		}
		{
			id: "3371F9248D403664"
			rewards: [
				{
					id: "7EC75E59C315BB3C"
					type: "xp"
					xp: 10
				}
				{
					count: 6
					id: "3C294799544A5584"
					item: "minecraft:coal"
					type: "item"
				}
				{
					id: "7221550A96E0CD00"
					item: "minecraft:wither_skeleton_skull"
					type: "item"
				}
			]
			subtitle: "击杀5只凋灵骷髅"
			tasks: [{
				entity: "minecraft:wither_skeleton"
				icon: "minecraft:wither_skeleton_skull"
				id: "1016033CBB003413"
				title: "&l&c&a地狱&f 悬赏:&r&e 凋灵骷髅"
				type: "kill"
				value: 5L
			}]
			title: "§l§c§a下界§f悬赏:§r§e凋灵骷髅"
			x: 8.0d
			y: -0.5d
		}
		{
			dependencies: ["3371F9248D403664"]
			hide_until_deps_visible: true
			id: "738E94C4CFB05D11"
			rewards: [
				{
					id: "5A7979DA8E07631A"
					type: "xp"
					xp: 20
				}
				{
					exclude_from_claim_all: true
					id: "021EACDBD76A9D2E"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:wither_skeleton"
				icon: "minecraft:wither_skeleton_skull"
				id: "33107949548BC22D"
				title: "击杀10只凋灵骷髅"
				type: "kill"
				value: 10L
			}]
			x: 8.0d
			y: -2.0d
		}
		{
			dependencies: ["738E94C4CFB05D11"]
			id: "65CE9117B923C0DB"
			rewards: [
				{
					id: "7ADAFF31822653D1"
					type: "xp"
					xp: 50
				}
				{
					count: 3
					id: "578580004196BB13"
					item: "minecraft:wither_skeleton_skull"
					type: "item"
				}
				{
					count: 6
					id: "46FE70A8D25378BD"
					item: "minecraft:coal"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "24573A422CE166A4"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			tasks: [{
				entity: "minecraft:wither_skeleton"
				icon: "minecraft:wither_skeleton_skull"
				id: "29A605F8B896F3B9"
				title: "击杀50只凋灵骷髅"
				type: "kill"
				value: 50L
			}]
			x: 8.0d
			y: -3.5d
		}
		{
			dependencies: ["65CE9117B923C0DB"]
			id: "180B3AFF8C552F3F"
			rewards: [
				{
					count: 9
					id: "5AACC888295804E0"
					item: "minecraft:wither_skeleton_skull"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "4E3E6BB30F0AF436"
					table_id: 5564196992594175882L
					type: "random"
				}
			]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				entity: "minecraft:wither_skeleton"
				icon: "minecraft:wither_skeleton_skull"
				id: "727813FFC599C36D"
				title: "击杀100只凋灵骷髅"
				type: "kill"
				value: 100L
			}]
			x: 8.0d
			y: -5.0d
		}
		{
			icon: "minecraft:sculk_sensor"
			id: "0E20A9B79D1C6637"
			rewards: [
				{
					id: "1A20BD8574954FE2"
					type: "xp"
					xp: 1000
				}
				{
					id: "190AE302BBF17638"
					item: "allthemodium:allthemodium_ingot"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "77D3E366578B4BF0"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			size: 1.5d
			tasks: [{
				entity: "minecraft:warden"
				icon: "minecraft:sculk_catalyst"
				id: "69678CAC075C8EA5"
				title: "击杀监守者"
				type: "kill"
				value: 1L
			}]
			x: 1.5d
			y: 6.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,此任务不得用于任何非官方发布的公开整合包."
				""
				""
				""
				"此任务默认隐藏,若您能看到此说明,说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "764C6E8E0BBB5537"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "148550E42311F5A5"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "07FE5720E3BD9A7B"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: 3.0d
			y: 0.5d
		}
	]
	title: "悬赏板"
}
