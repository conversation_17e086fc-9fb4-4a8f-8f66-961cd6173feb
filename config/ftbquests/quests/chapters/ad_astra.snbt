{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "ad_astra"
	group: "752CDE464613A1ED"
	icon: {
		Count: 1
		id: "ad_astra:tier_1_rocket"
		tag: {
			BotariumData: { }
		}
	}
	id: "769974FDAD5DBEB1"
	images: [
		{
			height: 0.3d
			hover: ["合成&aATM之星&f所需材料"]
			image: "allthetweaks:item/atm_star"
			rotation: 0.0d
			width: 0.3d
			x: 4.1d
			y: 4.0d
		}
		{
			height: 2.5d
			image: "atm:textures/questpics/adastra/title.png"
			rotation: 0.0d
			width: 11.7875d
			x: -0.2d
			y: -2.0d
		}
		{
			height: 2.0d
			image: "ad_astra:textures/painting/earth.png"
			rotation: 0.0d
			width: 2.0d
			x: -5.5d
			y: 4.0d
		}
		{
			height: 2.0d
			image: "ad_astra:textures/painting/mars.png"
			rotation: 0.0d
			width: 2.0d
			x: -3.0d
			y: 8.5d
		}
		{
			height: 2.0d
			image: "ad_astra:textures/painting/venus.png"
			rotation: 0.0d
			width: 2.0d
			x: 3.0d
			y: 8.5d
		}
		{
			height: 2.0d
			image: "ad_astra:textures/painting/glacio.png"
			rotation: 0.0d
			width: 2.0d
			x: 5.5d
			y: 4.0d
		}
		{
			height: 2.0d
			image: "ad_astra:textures/painting/mercury.png"
			rotation: 0.0d
			width: 2.0d
			x: 0.0d
			y: 10.5d
		}
	]
	order_index: 0
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["438E233A9014DA73"]
			description: [
				"制作&a&a二阶火箭&f&r需要大量&c戴什矿&r."
				""
				"这枚火箭能带你直飞&c火星&r!记得多带些&b氧气&r和&e燃料&r用于返航."
			]
			id: "4EA0E385FF7E5FEB"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "1AC6ABC34B8C49A1"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "1FE8F2E61C92B975"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.25d
			tasks: [{
				id: "79AED4781590C86C"
				item: {
					Count: 1
					id: "ad_astra:tier_2_rocket"
					tag: {
						BotariumData: { }
					}
				}
				type: "item"
			}]
			x: -3.0d
			y: 5.0d
		}
		{
			dependencies: ["0EE652B280CB5F55"]
			description: ["&d&a四阶火箭&f&r是目前可制作的最高级火箭,能带我们飞出&a太阳系&f!"]
			id: "210A0DC6D5CAC236"
			tasks: [{
				id: "68DB1231F3A96C65"
				item: {
					Count: 1
					id: "ad_astra:tier_4_rocket"
					tag: {
						BotariumData: { }
					}
				}
				type: "item"
			}]
			x: 2.5d
			y: 4.0d
		}
		{
			dependencies: ["438E233A9014DA73"]
			description: [
				"在探索月球并收集足够戴什矿后,你就能在行星轨道建造&d&a空间站&f&r!"
				""
				"这些预制结构可作为星系中的迷你基地,算是你在太空中的第二个家."
			]
			icon: "ad_astra:space_painting"
			id: "3017721842588919"
			optional: true
			tasks: [
				{
					count: 32L
					id: "272ECBF4F0313233"
					item: "alltheores:steel_ingot"
					type: "item"
				}
				{
					count: 64L
					id: "2E38F53470201DC1"
					item: "alltheores:iron_plate"
					type: "item"
				}
				{
					count: 32L
					id: "534B726DC1747DEA"
					item: "ad_astra:desh_plate"
					type: "item"
				}
				{
					count: 32L
					id: "041DBD596163B83B"
					item: "ad_astra:desh_ingot"
					type: "item"
				}
			]
			title: "&a空间站&f"
			x: -4.0d
			y: 4.0d
		}
		{
			dependencies: ["58452F7D73C30E72"]
			description: [
				"着陆后潜行&a右键点击&f&aLande&r可回收火箭和&a发射台&f,没有它们你可回不来!"
				""
				"&a月球&f荒凉寂静,除了可能与你交易的&2村民&r生物外,还能开采到制作&a二阶火箭&f所需的&c戴什矿&r,或许还能造辆超酷的&a漫游车&r."
			]
			id: "438E233A9014DA73"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3692BBD01BE5B51F"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "0423147C3A278BE3"
					type: "xp"
					xp: 25
				}
			]
			tasks: [
				{
					count: 4L
					id: "627563413CA0416E"
					item: "ad_astra:raw_desh"
					type: "item"
				}
				{
					count: 4L
					id: "1376E9EEBB16D83E"
					item: { Count: 4, id: "ad_astra:desh_ingot" }
					type: "item"
				}
			]
			title: "&c戴什矿"
			x: -3.0d
			y: 3.0d
		}
		{
			dependencies: [
				"20DA5CA244B7ABBF"
				"313BDDDAF1E08965"
				"05B977269171EB06"
				"02057E81D8139BAE"
				"4785659E5022FEE7"
			]
			description: [
				"是时候进军太空了!"
				""
				"放置&a&a发射台&f&r并将&a&a一阶火箭&f&r置于中央.潜行&a右键点击&r火箭打开燃料槽,需装入3桶燃料用于往返(各3桶).&c务必额外携带&a发射台&f&r以防着陆丢失!"
				""
				"准备就绪后进入火箭按空格发射!进入轨道后选择&d&a太阳系&f&r→&2地球&r→月球!"
				""
				"降落时&a长按空格减速&r!左侧高度条显示距月表距离,小心别坠毁!"
				""
				"技巧:按F5切换第三人称视角"
				""
				"月球数据:"
				"重力:1.625 m/s²"
				"氧气:无"
				"温度:-160.0°C"
			]
			hide_dependency_lines: true
			icon: "ad_astra:moon_globe"
			id: "58452F7D73C30E72"
			min_width: 500
			rewards: [
				{
					exclude_from_claim_all: true
					id: "468AC8321A3FA808"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "54327A5B1E3DAA8C"
					type: "xp"
					xp: 100
				}
			]
			tasks: [{
				advancement: "ad_astra:moon"
				criterion: ""
				id: "39A95AE27E68114B"
				title: "登陆月球!"
				type: "advancement"
			}]
			title: "&a前往&r &b月球&r!"
			x: -3.0d
			y: 1.5d
		}
		{
			dependencies: ["4E8E49EB9C83188E"]
			description: [
				"接下来要开采的是&3奥斯特姆矿&r."
				""
				"用于制造新设备及抵御高温行星的&a航天服&f."
			]
			id: "2A279B011D09A9EE"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4C46F403352DA31B"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "0799CA8C28548FCE"
					type: "xp"
					xp: 25
				}
			]
			tasks: [
				{
					count: 4L
					id: "1B3FD86852E9F8FE"
					item: "ad_astra:raw_ostrum"
					type: "item"
				}
				{
					count: 4L
					id: "65AE7D0A17C3DB4E"
					item: { Count: 4, id: "ad_astra:ostrum_ingot" }
					type: "item"
				}
			]
			title: "奥斯特姆矿"
			x: -1.0d
			y: 6.5d
		}
		{
			dependencies: ["4EA0E385FF7E5FEB"]
			description: [
				"&c火星&r虽是寒冷行星,但仍可能发现新生命体,务必做好准备!"
				""
				"火星数据:"
				"重力:3.72076 m/s²"
				"氧气:无"
				"温度:-65.0°C"
			]
			icon: "ad_astra:mars_globe"
			id: "4E8E49EB9C83188E"
			rewards: [{
				exclude_from_claim_all: true
				id: "7AACBAD9546F2981"
				table_id: 6573526605066559568L
				type: "loot"
			}]
			tasks: [{
				advancement: "ad_astra:mars"
				criterion: ""
				id: "51EA8D3FA1966B84"
				type: "advancement"
			}]
			title: "探访火星"
			x: -3.0d
			y: 6.5d
		}
		{
			dependencies: ["7CA42B3CA84A21B5"]
			description: [
				"我们需要远征金星!"
				""
				"那里出产最强金属&d卡洛矿&r,可制作&5&a四阶火箭&f&r和穿越&a太阳系&f的&d&a喷气式航天服&f."
				""
				"金星数据:"
				"重力:8.87 m/s²"
				"氧气:无"
				"温度:464.0°C"
			]
			icon: "ad_astra:venus_globe"
			id: "0EE652B280CB5F55"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "109BEEE85D907550"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "4CFB61860572F560"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					advancement: "ad_astra:venus"
					criterion: ""
					id: "3A4B0885275DF89B"
					type: "advancement"
				}
				{
					count: 4L
					id: "5435730E77B1B16F"
					item: "ad_astra:raw_calorite"
					type: "item"
				}
			]
			title: "探访金星"
			x: 3.0d
			y: 6.5d
		}
		{
			dependencies: ["7CA42B3CA84A21B5"]
			description: [
				"遍布熔岩平原的炽热荒芜之地."
				""
				"水星数据:"
				"重力:3.7 m/s²"
				"氧气:无"
				"温度:167.0°C"
			]
			icon: "ad_astra:mercury_globe"
			id: "32738F324B799879"
			rewards: [
				{
					id: "35B81F3437892767"
					item: "ad_astra:mercury_globe"
					type: "item"
				}
				{
					id: "336D40C61B9D2159"
					type: "xp"
					xp: 25
				}
			]
			tasks: [{
				advancement: "ad_astra:mercury"
				criterion: ""
				id: "7302AF42EB62C1D2"
				type: "advancement"
			}]
			title: "探访水星"
			x: 1.0d
			y: 8.0d
		}
		{
			dependencies: [
				"210A0DC6D5CAC236"
				"7B2A7B2298DAE8EC"
			]
			description: [
				"当前科技能抵达的最远行星.&b冰霜星&r虽寒冷但存在可呼吸的氧气!"
				""
				"冰霜星数据:"
				"重力:3.721 m/s²"
				"氧气:有"
				"温度:-20.0°C"
			]
			icon: "ad_astra:glacio_globe"
			id: "0B407DE1771F3304"
			rewards: [{
				exclude_from_claim_all: true
				id: "5511510CAEB44560"
				table_id: 1160439751879588774L
				type: "loot"
			}]
			size: 1.0d
			tasks: [{
				advancement: "ad_astra:interstellar"
				criterion: ""
				id: "60ADF70F2398E2F8"
				type: "advancement"
			}]
			title: "探访冰霜星"
			x: 3.0d
			y: 1.5d
		}
		{
			description: [
				"欢迎来到&d星际旅行&r模组!"
				""
				"本模组将带你开启星际之旅,这意味着你可以打造自己的宇宙飞船!!!"
				""
				"要进入太空,你需要比铁更坚固的材料来建造飞船."
				""
				"如果想脱离地心引力,你需要大量&a钢&r作为起步资源!可通过多种方式获取,比如使用&e&d通用机械&f的&a冶金灌注机&f&r先制作&3&a钢粉&f&r,或直接在合成网格中用&a铁粉&f、4个煤和锤子制作钢粉."
			]
			icon: {
				Count: 1
				id: "patchouli:guide_book"
				tag: {
					"patchouli:book": "ad_astra:astrodux"
				}
			}
			id: "0D8AC4FB1F61B07A"
			rewards: [
				{
					id: "1856194492635B78"
					item: {
						Count: 1
						id: "patchouli:guide_book"
						tag: {
							"patchouli:book": "ad_astra:astrodux"
						}
					}
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "72B6835DDF9CD7BA"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "69FB518CD44E0ACA"
				item: "alltheores:steel_ingot"
				type: "item"
			}]
			title: "&d飞向星辰!"
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["0D8AC4FB1F61B07A"]
			description: [
				"这把&a锤子&r能将金属锭加工成&a金属板&r!虽然初期很实用,但最终建议制作&a压缩机&r来自动处理.注意需要电力驱动!"
				""
				"前往太空需要大量&a铁&r和&a&a钢板&f&r来制作设备,记得实现自动化生产!"
			]
			icon: {
				Count: 1
				id: "immersiveengineering:hammer"
				tag: {
					Damage: 0
				}
			}
			id: "0D4A85FBCE0015E1"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "18833B285F7249FB"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "038D44F906DD8DB5"
					type: "xp"
					xp: 10
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "18AECBDA4AC04E06"
					item: {
						Count: 1
						id: "immersiveengineering:hammer"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "031A8E0B1C909394"
					item: "ad_astra:compressor"
					type: "item"
				}
			]
			title: "制作&a金属板"
			x: 0.0d
			y: 2.0d
		}
		{
			dependencies: ["0D4A85FBCE0015E1"]
			description: [
				"制作火箭需要先搭建&d&aNASA工作台&f&r,这是专门用于组装火箭的工作台!"
				""
				"下一步是用工作台制作&a&a一阶火箭&f&r.集齐所有部件后放入工作台即可合成!"
			]
			icon: {
				Count: 1
				id: "ad_astra:tier_1_rocket"
				tag: { }
			}
			id: "313BDDDAF1E08965"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "481C5E83FF671C18"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "2EEB9D7EC3684AFB"
					type: "xp"
					xp: 100
				}
			]
			shape: "hexagon"
			size: 1.5d
			tasks: [
				{
					id: "05AC65380B8E5A22"
					item: "ad_astra:nasa_workbench"
					type: "item"
				}
				{
					id: "0AD62369D27109EF"
					item: {
						Count: 1
						id: "ad_astra:tier_1_rocket"
						tag: { }
					}
					match_nbt: false
					type: "item"
				}
			]
			title: "&a建造首枚&r&d火箭&r!"
			x: 0.0d
			y: 4.0d
		}
		{
			dependencies: ["0D4A85FBCE0015E1"]
			description: [
				"若想前往太空,你必须为这趟旅程准备全新的装备."
				""
				"首次登&b月球&r需要制作全套&a&a航天服&f&r.要知道月球既寒冷又缺氧,没有航天服可撑不了多久呢 :)"
			]
			hide_dependency_lines: true
			id: "02057E81D8139BAE"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "79CA3A7E1C7AB579"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "7135774DB2AFB447"
					type: "xp"
					xp: 100
				}
			]
			shape: "square"
			tasks: [
				{
					id: "58780A1586C59948"
					item: {
						Count: 1
						id: "ad_astra:space_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "045CAA9319170596"
					item: {
						Count: 1
						id: "ad_astra:space_suit"
						tag: {
							BotariumData: { }
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "68F83E076039DB23"
					item: {
						Count: 1
						id: "ad_astra:space_pants"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "096039479BF27E8C"
					item: {
						Count: 1
						id: "ad_astra:space_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&a穿戴装备"
			x: -1.0d
			y: 3.0d
		}
		{
			dependencies: ["0D4A85FBCE0015E1"]
			description: [
				"火箭和汽车一样,不能靠幻想驱动.那么如何获取燃料？"
				""
				"首先在主世界寻找&3原油&r.海洋中会有喷涌的油井,需要收集足够量来精炼!"
				""
				"这时就需要&a&a燃油精炼机&f&r登场了.它能将原油转化为火箭燃料."
				""
				"建议大量储备——单程消耗&e3桶燃料&r,往返需要6桶!"
			]
			hide_dependency_lines: true
			icon: "ad_astra:fuel_refinery"
			id: "4785659E5022FEE7"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "15F7CCCFDF146D6B"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "47D3C9724A4169F3"
					item: "ad_astra:fuel_bucket"
					type: "item"
				}
				{
					id: "262ED3093C8B3DF5"
					type: "xp"
					xp: 10
				}
			]
			shape: "square"
			tasks: [
				{
					id: "54E00D1DC618B0FB"
					item: "ad_astra:fuel_refinery"
					type: "item"
				}
				{
					count: 3L
					id: "6605CAE425FCB735"
					item: "ad_astra:fuel_bucket"
					type: "item"
				}
			]
			title: "&a燃料补给"
			x: 1.0d
			y: 5.0d
		}
		{
			dependencies: ["02057E81D8139BAE"]
			description: [
				"冷知识:月球上没有&b氧气&r就无法呼吸.实际上任何地方缺氧都不行,猜猜月球缺什么？"
				""
				"所以我们要自制氧气.先制作&a&a氧气装载机&f&r并注入水和电力,就能将水转化为可用&b氧气&r."
				""
				"收集方式:放置&b&a氧气罐&f&r、空桶或直接放入&a航天服&f."
				""
				"专业建议:随身多带些&b氧气&r总没错...以防万一."
			]
			hide_dependency_lines: true
			icon: "ad_astra:oxygen_loader"
			id: "20DA5CA244B7ABBF"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "76EC8777189BA475"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "4626A1AFEE0EDF3C"
					type: "xp"
					xp: 10
				}
			]
			shape: "square"
			tasks: [
				{
					id: "0E3F2AF37FBCE8D4"
					item: "ad_astra:oxygen_loader"
					type: "item"
				}
				{
					id: "0DE204C9101E0A72"
					item: {
						Count: 1
						id: "ad_astra:gas_tank"
						tag: {
							BotariumData: { }
						}
					}
					type: "item"
				}
			]
			title: "&a制备&r&b氧气"
			x: -1.0d
			y: 5.0d
		}
		{
			dependencies: ["0D4A85FBCE0015E1"]
			description: [
				"火箭需要发射基座,因此要制作&a&a发射台&f&r."
				""
				"将其放置在视野开阔处,再将组装好的火箭置于&a发射台&f中央即可发射."
			]
			hide_dependency_lines: true
			id: "05B977269171EB06"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0F8D50FF991778EE"
					table_id: 6573526605066559568L
					type: "loot"
				}
				{
					id: "3910492B757109B4"
					type: "xp"
					xp: 25
				}
			]
			shape: "square"
			tasks: [{
				id: "0DC1EA166270E3B6"
				item: "ad_astra:launch_pad"
				type: "item"
			}]
			title: "&e发射升空"
			x: 1.0d
			y: 3.0d
		}
		{
			dependencies: ["2A279B011D09A9EE"]
			description: [
				"要在炽热行星生存,需要升级版&a航天服&f."
				""
				"将&3下界合金&r与&3奥斯特姆合金&r结合,能制作抵御极端高温的防护服!"
				""
				"若想抵达那些高温行星,还需建造&d&a三阶火箭&f&r!"
			]
			icon: {
				Count: 1
				id: "ad_astra:tier_3_rocket"
				tag: { }
			}
			id: "7CA42B3CA84A21B5"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "456C9F69DC6AEADD"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "5B18D55E86CC7E76"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "6D967595A1292808"
					item: {
						Count: 1
						id: "ad_astra:netherite_space_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "259215BDAC3E1596"
					item: {
						Count: 1
						id: "ad_astra:netherite_space_suit"
						tag: {
							BotariumData: { }
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "5A3FB02C57DA9676"
					item: {
						Count: 1
						id: "ad_astra:netherite_space_pants"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "1F6D0F499BE3006A"
					item: {
						Count: 1
						id: "ad_astra:netherite_space_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "02455651602809E4"
					item: {
						Count: 1
						id: "ad_astra:tier_3_rocket"
						tag: { }
					}
					type: "item"
				}
			]
			title: "&c&a整装待发&f应对&a高温"
			x: 1.0d
			y: 6.5d
		}
		{
			dependencies: ["0EE652B280CB5F55"]
			description: [
				"这是你能制作的最好的&a航天服&f."
				""
				"它可以充能,实现鞘翅般的飞行!还能保护你免受&a酸雨&f伤害. :)"
			]
			id: "7B2A7B2298DAE8EC"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "67EB4DBD244D73C2"
					table_id: 5564196992594175882L
					type: "loot"
				}
				{
					id: "08C7099BC1802925"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					id: "5455B3737ED5982F"
					item: {
						Count: 1
						id: "ad_astra:jet_suit_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "6701DE855E03A3C9"
					item: {
						Count: 1
						id: "ad_astra:jet_suit"
						tag: {
							BotariumData: { }
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "7B8F89563D5AF057"
					item: {
						Count: 1
						id: "ad_astra:jet_suit_pants"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
				{
					id: "204F65AFCF6E7D66"
					item: {
						Count: 1
						id: "ad_astra:jet_suit_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			title: "&a喷气式航天服&f"
			x: 3.5d
			y: 4.0d
		}
		{
			dependencies: ["2A279B011D09A9EE"]
			description: [
				"每次星际旅行需要6桶&a燃料&f会快速消耗资源."
				""
				"利用采集的&3锇矿&f,我们可以制造&d低温凝冻机&f.通电后,这台机器能将&b冰&f、&a浮冰&f、&a蓝冰&f或&a寒冰碎块&f转化为&d&a凛冰燃料&f."
				""
				"现在每次发射只需1桶&d&a凛冰燃料&f,往返仅需2桶!"
				""
				"注:放置时会冻结周围水源,形成无限冰源!"
			]
			id: "088D685775ED92EE"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "532D16AE1EEA7796"
					table_id: 4196188979167302596L
					type: "loot"
				}
				{
					id: "542912F98807C223"
					type: "xp"
					xp: 50
				}
			]
			tasks: [{
				id: "3A719EE7EC22388F"
				item: "ad_astra:cryo_freezer"
				type: "item"
			}]
			title: "&a高效燃料&f"
			x: -1.0d
			y: 8.0d
		}
		{
			dependencies: ["58452F7D73C30E72"]
			description: [
				"若你更倾向使用原有护甲而非全天穿戴&a航天服&f,可用&d&a太空呼吸&f附魔强化头盔!"
				""
				"需在物品栏放置&b&a氧气储罐&f才能生效."
				""
				"注:&a氧气&f仅适用于寒冷星球,但可升级适配."
			]
			id: "59ADE76689E381AA"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "26602F2CD43D941D"
					table_id: 487623848494439020L
					type: "loot"
				}
				{
					id: "79C791780DD1726E"
					type: "xp"
					xp: 25
				}
			]
			tasks: [
				{
					id: "6F8BB68347B1143C"
					item: {
						Count: 1
						id: "ad_astra_giselle_addon:oxygen_can"
						tag: { }
					}
					type: "item"
				}
				{
					id: "789B41F56AB4EF00"
					item: {
						Count: 1
						id: "minecraft:enchanted_book"
						tag: {
							StoredEnchantments: [{
								id: "ad_astra_giselle_addon:space_breathing"
								lvl: 1
							}]
						}
					}
					type: "item"
					weak_nbt_match: true
				}
			]
			title: "不想穿&a航天服&f？"
			x: -4.0d
			y: 2.5d
		}
		{
			dependencies: ["3017721842588919"]
			description: [
				"如果你想在地球外建立基地,很可能需要获取&b氧气&r的方法."
				""
				"&d&a氧气分配器&f&r是一种能在&a密闭&r空间中分配氧气的机器.当提供水和电力时,它会自动将氧气输送到密闭空间."
				""
				"&9&a水泵&f&r用于从下方的无限水源向分配器抽水.你也可以使用水槽或&d永恒水方块&r来供水."
			]
			id: "72B3CDA595D08587"
			tasks: [{
				id: "51FB59D095E984FD"
				item: "ad_astra:oxygen_distributor"
				type: "item"
			}]
			title: "基地供氧系统!"
			x: -4.0d
			y: 5.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为整合包制作."
				"所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经明确授权不得用于非官方发布的公开整合包."
				""
				""
				""
				"此任务默认隐藏,若你看到此提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "43EF517BA64AA19C"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "63671C4CE146D8C4"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "3F68281087FDC3BE"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 2.0d
			y: 0.0d
		}
	]
	title: "飞向星空"
}
