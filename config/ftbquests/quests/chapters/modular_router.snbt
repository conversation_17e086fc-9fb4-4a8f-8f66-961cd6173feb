{
	default_hide_dependency_lines: true
	default_quest_shape: "square"
	filename: "modular_router"
	group: "35A88CA0DDED1092"
	icon: "modularrouters:modular_router"
	id: "0AB4F9AED808DE48"
	images: [
		{
			height: 1.0d
			image: "modularrouters:block/modular_router_front_active"
			rotation: 0.0d
			width: 1.0d
			x: 0.0d
			y: -1.5d
		}
		{
			height: 5.0d
			image: "modularrouters:item/augment_core"
			rotation: 0.0d
			width: 5.0d
			x: -3.5d
			y: -2.0d
		}
		{
			height: 5.0d
			image: "modularrouters:item/upgrade/upgrade_layer1"
			rotation: 0.0d
			width: 5.0d
			x: 3.0d
			y: -2.0d
		}
		{
			height: 5.0d
			image: "modularrouters:item/upgrade/upgrade_layer0"
			rotation: 0.0d
			width: 5.0d
			x: 3.0d
			y: -2.0d
		}
		{
			height: 8.0d
			image: "modularrouters:item/module/module_layer1"
			rotation: 0.0d
			width: 8.0d
			x: 0.0d
			y: 5.0d
		}
		{
			height: 8.0d
			image: "modularrouters:item/module/module_layer0"
			rotation: 0.0d
			width: 8.0d
			x: 0.0d
			y: 5.0d
		}
		{
			height: 1.0d
			image: "modularrouters:block/modular_router_front_active"
			rotation: 0.0d
			width: 1.0d
			x: 1.5d
			y: 1.0d
		}
		{
			height: 1.0d
			image: "modularrouters:block/modular_router_front_active"
			rotation: 0.0d
			width: 1.0d
			x: -1.5d
			y: 1.0d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/router/router_title.png"
			rotation: 0.0d
			width: 8.130252100840336d
			x: 0.0d
			y: -6.5d
		}
	]
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["&l&d模块化路由器&f&r是物流模组.\\n\\n一切始于路由器!快制作一个吧!\\n\\n缓冲槽是路由器内唯一的物品槽位,所有物品都经此流通.多数模块需要&a红石信号&f才能运作."]
			id: "63E4529AF8894018"
			rewards: [{
				count: 3
				id: "4100927F60249CD2"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "08D4CEAE1B60A8C4"
				item: "modularrouters:modular_router"
				type: "item"
			}]
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["63E4529AF8894018"]
			description: ["模块决定路由器的功能类型.\\n\\n它们能让路由器从箱子提取物品、存入物品,甚至同时执行!还有更多功能!\\n\\n模块直接安装在路由器内部,可同时装载多个.\\n\\n过滤器和升级组件需安装在模块内部."]
			icon_scale: 1.6d
			id: "233C7CAA573D6045"
			rewards: [{
				count: 3
				id: "6E463F41C3F9F434"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			size: 1.0d
			tasks: [{
				id: "3B06163129B8276F"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			x: 0.0d
			y: 2.5d
		}
		{
			dependencies: ["63E4529AF8894018"]
			description: ["升级组件直接装入路由器,最多可安装5个.升级会直接影响所有兼容模块,且效果通常可叠加!"]
			icon_scale: 1.6d
			id: "75FE0B514448865B"
			rewards: [{
				count: 3
				id: "43E28DEED4445B5D"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			size: 1.0d
			tasks: [{
				id: "012EA4B7B434EC45"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			x: 4.5d
			y: -3.5d
		}
		{
			dependencies: ["63E4529AF8894018"]
			description: ["强化组件与升级组件类似,但专用于单个模块的增强!"]
			icon_scale: 1.6d
			id: "6648BC7D14233006"
			rewards: [{
				count: 3
				id: "1B18FA07F532A5B6"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			size: 1.0d
			tasks: [{
				id: "06D427AC0C2C34A2"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			x: -3.5d
			y: -3.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["当路由器处于危险环境时(比如生成凋灵).\\n\\n需要安装防爆升级组件保护它.\\n\\nTNT炸不坏,凋灵也毁不掉,只有你能拆除它."]
			id: "20D4568857887D38"
			rewards: [{
				count: 3
				id: "67B59A005496AF95"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "23EBEF198A2C8DB6"
				item: "modularrouters:blast_upgrade"
				type: "item"
			}]
			x: 1.5d
			y: -3.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["该升级使路由器功能类似框架方块.\\n\\n潜行时&a右键点击&f方块可将其外观应用于路由器.\\n\\n千万别弄丢它!"]
			id: "09E25B1CD6CABD5A"
			rewards: [{
				count: 3
				id: "1A90A9FB7F039AE0"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "766CBD152FEDF577"
				item: "modularrouters:camouflage_upgrade"
				type: "item"
			}]
			x: 3.5d
			y: -2.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["搭配&a能量模块&f使用时能耗更高!"]
			id: "1069244EB72F64CA"
			rewards: [{
				count: 3
				id: "4A93A6C81F908D5F"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "1D52EFEDE64C4A40"
				item: "modularrouters:energy_upgrade"
				type: "item"
			}]
			x: 3.5d
			y: -1.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["流体不像物品可堆叠,因此其升级机制略有不同.仍能提升传输量,只是速率不同."]
			id: "4FA5192164E4A427"
			rewards: [{
				count: 3
				id: "2DAF8FC58615592C"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "30B31BFD457C85FB"
				item: "modularrouters:fluid_upgrade"
				type: "item"
			}]
			x: 4.5d
			y: -1.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["路由器可能产生噪音,当你有数百台时噪音会叠加.\\n\\n这时投资消音升级会是不错的选择!"]
			id: "2447208F1DC74FFE"
			rewards: [{
				count: 3
				id: "691B6EAFF4712617"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "423F8BB90E4EAF61"
				item: "modularrouters:muffler_upgrade"
				type: "item"
			}]
			x: 4.5d
			y: -2.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["我的物品,禁止触碰.\\n\\n安装此升级后,只有你能操作该路由器."]
			id: "10672B5991E5FC34"
			rewards: [{
				count: 3
				id: "6CEECD21ED98315E"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "7A44262F9ADCBF27"
				item: "modularrouters:security_upgrade"
				type: "item"
			}]
			x: 2.5d
			y: -2.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["路由器每次运作周期都会执行动作.\\n\\n默认20刻(1秒)一个周期.\\n\\n使用&a风驰升级&f可缩短运作间隔."]
			id: "309B6B8368E535A0"
			rewards: [{
				count: 3
				id: "36F172F588DB8847"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "2D6601DF1C229C1F"
				item: "modularrouters:speed_upgrade"
				type: "item"
			}]
			x: 2.5d
			y: -3.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["通常路由器每周期移动1个物品.通过堆叠升级可倍增效率!\\n\\n1级堆叠移动2物品,2级4物品,6级可达每秒64物品."]
			id: "4CC963F5AECF285A"
			rewards: [{
				count: 3
				id: "35AAFCB0899E5587"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "4D0FC01C8A201175"
				item: "modularrouters:stack_upgrade"
				type: "item"
			}]
			x: 3.5d
			y: -3.5d
		}
		{
			dependencies: ["75FE0B514448865B"]
			description: ["该升级独特之处在于可作用于多个路由器.\\n\\n若需多台路由器协同工作,每台都需安装同步升级.\\n\\n需在专属GUI中进行调校."]
			id: "553C44F85E508DE3"
			rewards: [{
				count: 3
				id: "54C3ABFCF1716CA5"
				item: "modularrouters:blank_upgrade"
				type: "item"
			}]
			tasks: [{
				id: "6AAF612711A3A7AA"
				item: "modularrouters:sync_upgrade"
				type: "item"
			}]
			x: 4.5d
			y: -0.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["何必等待？通常MC中掉落物需1秒冷却才能拾取.\\n\\n但何必等待？\\n\\n此升级让&a真空模块&f立即吸取物品.\\n\\n有效降低卡顿!"]
			id: "0CE236731DF15DFF"
			rewards: [{
				count: 3
				id: "2EEBA8AC6795159F"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "7324614BF105A6CC"
				item: "modularrouters:fast_pickup_augment"
				type: "item"
			}]
			x: -2.5d
			y: -0.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["我们见过过滤器和&a轮询功能&f,现在二者合一!特别适用于保持特定物品顺序,比如制作&d神秘农业&f种子时!"]
			id: "76062D375E4CC8DD"
			rewards: [{
				count: 3
				id: "57F328DE7702602B"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "6E394B94A54824DE"
				item: "modularrouters:filter_round_robin_augment"
				type: "item"
			}]
			x: -4.5d
			y: -0.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["配合MK2挤压机使用,挤压出的方块将模拟真实特性.&a红石砖&f会发出红石信号,荧石发光,草方块透明化.但需安装此强化模块!\\n对熔炉或&a富集仓&f等方块实体无效."]
			id: "2DD5F0693780B10A"
			rewards: [{
				count: 3
				id: "5A9D64240C49D62D"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "5536050510B029C8"
				item: "modularrouters:mimic_augment"
				type: "item"
			}]
			x: -2.5d
			y: -3.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["传输物品时路由器可能过于贪婪,会攫取所有能触及的物品!\\n\\n使用&a调节拓展&f可限制传输量."]
			id: "674F28BCA2E4A3C8"
			rewards: [{
				count: 3
				id: "474C8D97713CAB94"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "2835082179389E8E"
				item: "modularrouters:regulator_augment"
				type: "item"
			}]
			x: -3.5d
			y: -0.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["何必等待？哦...你有正当理由？比如防止物品消失.嗯,倒是没考虑这点.\\n\\n拾取延迟强化适用于发射器与&a投掷模块&f,可延长玩家拾取物品的冷却时间."]
			id: "177396B9C4F9B3E2"
			rewards: [{
				count: 3
				id: "0494C547C8F4852A"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "6F6DAB4EA217843B"
				item: "modularrouters:pickup_delay_augment"
				type: "item"
			}]
			x: -3.5d
			y: -1.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["&a金属挤压机&f能推动方块和实体,但何不再加把劲!\\n\\n仅对实体有效."]
			id: "5560F303C452E110"
			rewards: [{
				count: 3
				id: "72A300745B1BAD82"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "26F317C784B8EB41"
				item: "modularrouters:pushing_augment"
				type: "item"
			}]
			x: -4.5d
			y: -1.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["不想所有设备都大范围运作？希望真空模块给世界留些投放物品的空间？那你可以缩小作用范围."]
			id: "1A806C1A54B26EB5"
			rewards: [{
				count: 3
				id: "1AFD453B64517C1D"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "522EAFFE3BB0AEED"
				item: "modularrouters:range_down_augment"
				type: "item"
			}]
			x: -3.5d
			y: -2.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["够不到所需物品？试试范围增强!\\n\\n使用后所有操作都将延伸作用距离.吸物模块能收取更远处的物品,能量传输距离更远,投掷器投射力度更强!"]
			id: "4EAFE7B240AF0A4D"
			rewards: [{
				count: 3
				id: "0AA5D556A6981D2D"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "61990DF29765904E"
				item: "modularrouters:range_up_augment"
				type: "item"
			}]
			x: -2.5d
			y: -2.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["通常路由控制模块,但这个强化组件让模块造反了!嗷呜!彻底无政府状态!\\n\\n它将听从自身红石信号而非路由器的指令."]
			id: "3025081DA6D2B398"
			rewards: [{
				count: 3
				id: "79EAE6180557E5A9"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "6988516896A11B77"
				item: "modularrouters:redstone_augment"
				type: "item"
			}]
			x: -2.5d
			y: -1.5d
		}
		{
			dependencies: [
				"6648BC7D14233006"
				"4CC963F5AECF285A"
			]
			description: ["有时你不想让所有模块一次性移动多组物品,这个强化组件可指定单个模块实现该功能.注意:不可与升级组件叠加使用...实际上它会覆盖升级效果."]
			id: "5FB29F3F4DF17765"
			rewards: [{
				count: 3
				id: "1E374921446DDBC7"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "0F4A2E8C9A3BCEB2"
				item: "modularrouters:stack_augment"
				type: "item"
			}]
			x: -4.5d
			y: -3.5d
		}
		{
			dependencies: ["6648BC7D14233006"]
			description: ["现在更像&a吸收漏斗&f了!\\n\\n&a吸物模块&f现在能吸取&a经验球&f和物品.经验值怎么处理就随你便了!"]
			id: "6B747129A85AA768"
			rewards: [{
				count: 3
				id: "44FB4D5F8088B6B0"
				item: "modularrouters:augment_core"
				type: "item"
			}]
			tasks: [{
				id: "69A9229F1A9CC4AA"
				item: "modularrouters:xp_vacuum_augment"
				type: "item"
			}]
			x: -4.5d
			y: -2.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["厌倦了到处&a右键点击&f？从剪刀剪羊毛到桶装牛奶？21世纪该让机器人代劳了!至少让路由器来干.\\n\\n&a执行模块&f能模拟你的&a右键点击&f操作.\\n\\n可设置点击对象(生物/方块)、攻击或使用模式、是否潜行点击,以及模块的作用距离."]
			id: "71A1FD0772453B51"
			rewards: [{
				count: 3
				id: "161A8E3E464D8E92"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "08229003D4BAB8B9"
				item: "modularrouters:activator_module"
				type: "item"
			}]
			x: 1.0d
			y: 5.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["摆弄放置器过头需要拆除方块？\\n\\n其等级和附魔取决于合成时使用的镐子.\\n\\n设置好区域并加上过滤器即可运作!\\n\\n&a可配合&f放置器将矿石转化为物品等操作."]
			id: "53B58FC3958750E7"
			rewards: [{
				count: 3
				id: "43B563A65E8D362C"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "22FF211D541738D9"
				item: "modularrouters:breaker_module"
				type: "item"
			}]
			x: -2.0d
			y: 4.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["摆弄放置器过头需要拆除方块？\\n\\n其等级和附魔取决于合成时使用的镐子.\\n\\n设置好区域并加上过滤器即可运作!\\n\\n&a可配合&f放置器将矿石转化为物品等操作."]
			id: "01C5D8357D8B0D62"
			rewards: [{
				count: 3
				id: "03A278F3604FAB61"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "35176C32CFAE3BB7"
				item: "modularrouters:detector_module"
				type: "item"
			}]
			x: 1.0d
			y: 2.5d
		}
		{
			dependencies: [
				"233C7CAA573D6045"
				"38954551309F42A7"
			]
			description: ["分发器是发送模块的半同胞(确切说是MK3版本,继承MK2血统).\\n\\n可同时向多个选定容器发送物品.\\n\\n支持多种模式:&a轮询&f、随机、近处优先或远处优先.&a轮询&f像发牌员般平均分配,随机模式任选目标,其他模式按与路由器的距离决定."]
			id: "326C63F7DC713E71"
			rewards: [{
				count: 3
				id: "4E04C738333A4846"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "11838D5D25FA3EDD"
				item: "modularrouters:distributor_module"
				type: "item"
			}]
			x: 1.0d
			y: 4.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["投掷器能干啥？\\n大概就是丢东西...\\n\\n缓存区物品会被抛到地面.\\n\\n配合&d植物魔法&f使用效果更佳!"]
			id: "7F8DEDD0A54D1E11"
			rewards: [{
				count: 3
				id: "2A0049B7F81796AB"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "4E2834A0DF0E3577"
				item: "modularrouters:dropper_module"
				type: "item"
			}]
			x: 1.0d
			y: 6.5d
		}
		{
			dependencies: [
				"326C63F7DC713E71"
				"346B9339E3D66478"
			]
			description: ["何必限制能量传输范围？放任自由吧!\\n\\n如同释放蜂群,能量会瞬间覆盖周边(还没嗡嗡声).附近需要充能的方块将获得无线充能."]
			id: "2B60D123123A5E8E"
			rewards: [{
				count: 3
				id: "180857D9ED9ABC22"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "2DB6B64AB076D1E2"
				item: "modularrouters:energy_distributor_module"
				type: "item"
			}]
			x: 0.0d
			y: 6.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["能量是万物之源,你需要将它输送到各处.幸好&a能量模块&f能帮忙!\\n\\n&a能量模块&f会将输入能量传输到你设定的任何位置!"]
			id: "346B9339E3D66478"
			rewards: [{
				count: 3
				id: "21B90CA08ACC8A4F"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "12C3BA50791B4CB8"
				item: "modularrouters:energy_output_module"
				type: "item"
			}]
			x: 0.0d
			y: 5.5d
		}
		{
			dependencies: [
				"233C7CAA573D6045"
				"53B58FC3958750E7"
				"36DED7099E80ED51"
			]
			description: ["挤压是个古板的词.\\n\\n&a金属挤压机&f在收到&a红石信号&f时,会将路由器缓存中的方块呈直线排列放置在路由器前方.信号关闭时会破坏所有已放置的方块.\\n\\n非常适合制作自动门!"]
			id: "40788326E791845D"
			rewards: [{
				count: 3
				id: "41DB69BCD3E6419E"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "4EC73A189243D253"
				item: "modularrouters:extruder_module_1"
				type: "item"
			}]
			x: -1.0d
			y: 2.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["与MK1挤压机类似,但这个版本更先进!&a可配合&f模板使用,放置方块时会按照预设图案排列!"]
			id: "00E1F1279F97557B"
			rewards: [{
				count: 3
				id: "5FE8CB5E9F5649EB"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "001795B7BF11F948"
				item: "modularrouters:extruder_module_2"
				type: "item"
			}]
			x: -1.0d
			y: 3.5d
		}
		{
			dependencies: ["7F8DEDD0A54D1E11"]
			description: ["投掷器负责投掷,\\n发射器负责发射,\\n而弹射模块负责弹射!\\n\\n&a弹射模块&f不会温柔地放置缓存物品,而是将它们弹射出去？射程多远？由你决定!"]
			id: "3C54C36CE97A48DC"
			rewards: [{
				count: 3
				id: "5757B94578F9920F"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "6C0562A6317BB343"
				item: "modularrouters:flinger_module"
				type: "item"
			}]
			x: 2.0d
			y: 6.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["液体进.\\n液体出.\\n\\n&a流体模块&f能从路由器附近抽取液体源存入缓存罐,也能将缓存罐中的液体排放到前方.\\n\\n仅限与路由器相邻的方块,如需更大范围请使用..."]
			id: "247DAE5B4E1AC7F1"
			rewards: [{
				count: 3
				id: "1B695D87AA33273C"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "48DE8F4C4D2661A1"
				item: "modularrouters:fluid_module"
				type: "item"
			}]
			x: -1.0d
			y: 4.5d
		}
		{
			dependencies: ["247DAE5B4E1AC7F1"]
			description: ["&a流体模块&f MK2!\\n\\n可操控附近任意液体源,不再局限于相邻方块."]
			id: "73D60868024FF907"
			rewards: [{
				count: 3
				id: "189184BA4C1A13E7"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "1FF3F8B78EF0DCB2"
				item: "modularrouters:fluid_module_2"
				type: "item"
			}]
			x: -1.0d
			y: 5.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["顾名思义,这个模块负责放置方块.\\n\\n设置方块放置方向(建议配置过滤器),将模块和方块放入路由器!红石信号激活时就会自动放置!"]
			id: "36DED7099E80ED51"
			rewards: [{
				count: 3
				id: "088882B53F8B0CBB"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "76E678E73AEEEC59"
				item: "modularrouters:placer_module"
				type: "item"
			}]
			x: -2.0d
			y: 3.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["&a路由器&f能予能取.\\n\\n该模块对建筑工和矿工都很有用!可设置为将缓存物品输送至背包,或从背包收取物品存入缓存.\\n\\n露天采矿时尤其好用,让它自动将圆石和深板岩从背包转移到&a垃圾桶&f."]
			id: "0DC8B68CDF945348"
			rewards: [{
				count: 3
				id: "3F3C5DD0694B1E1A"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "6BD3CA765D912D6B"
				item: "modularrouters:player_module"
				type: "item"
			}]
			x: -2.0d
			y: 5.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["吸..!吸..!吸..!\\n\\n&a提取模块&f与输送模块相反,会将容器中的物品吸入路由器缓存.\\n\\n容器必须与路由器相邻."]
			id: "1AA81C41431FD145"
			rewards: [{
				count: 3
				id: "2C14105CC4A4E01D"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "419242F445660844"
				item: "modularrouters:puller_module_1"
				type: "item"
			}]
			x: -2.0d
			y: 6.5d
		}
		{
			dependencies: ["1AA81C41431FD145"]
			description: ["与MK1版同样具有提取功能!但不再限于相邻容器,可作用于附近所有容器!"]
			id: "475E1B94A61B9EB0"
			rewards: [{
				count: 3
				id: "3D86B1835F9D422F"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "36A18CBAE804CE6B"
				item: "modularrouters:puller_module_2"
				type: "item"
			}]
			x: -1.0d
			y: 6.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["&a输送模块&f会将路由器缓存槽位的物品转移至其他容器.\\n\\nMK1版仅限与路由器处于相同XYZ轴且视线无遮挡的容器.若有方块阻挡则无法输送."]
			id: "6063F4B7BE4D394C"
			rewards: [{
				count: 3
				id: "2B7F8E2761AE6D4D"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "7AD327057658EE62"
				item: "modularrouters:sender_module_1"
				type: "item"
			}]
			x: 0.0d
			y: 3.5d
		}
		{
			dependencies: ["6063F4B7BE4D394C"]
			description: ["MK2输送模块功能类似MK1但限制更少.\\n\\n无需视线无遮挡或同处XYZ轴.\\n\\n但必须在同一维度,除非你拥有..."]
			id: "38954551309F42A7"
			rewards: [{
				count: 3
				id: "1C035E94B0139C57"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "36B1EBB1CE94001B"
				item: "modularrouters:sender_module_2"
				type: "item"
			}]
			x: 1.0d
			y: 3.5d
		}
		{
			dependencies: ["38954551309F42A7"]
			description: ["MK3版无视XYZ轴、视线或维度限制.\\n\\n只要容器位于同个Minecraft世界即可输送物品.\\n\\n我甚至怀疑它能把物品送到Minecraft游戏之外的容器!"]
			id: "2405663C033CBDF1"
			rewards: [{
				count: 3
				id: "3E6116F9A969F345"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "724A52B910305B55"
				item: "modularrouters:sender_module_3"
				type: "item"
			}]
			x: 2.0d
			y: 3.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["这些模块会吸!字面意思的吸!\\n\\n&a真空模块&f会吸取设定区域内的所有物品到路由器缓存,类似&a吸收漏斗&f.\\n\\n还可配合经验增幅器使用!"]
			id: "417B88FEB1B8F44E"
			rewards: [{
				count: 3
				id: "647D161F1B234A44"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "0FFBCEC7273ED2C3"
				item: "modularrouters:vacuum_module"
				type: "item"
			}]
			x: 2.0d
			y: 5.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: [
				"若你正在使用路由器,希望你具备一定的游戏知识.\\n\\n以防万一说明:"
				"销毁物品"
				"即永久删除.\\n\\n任何进入路由器的物品都将永远消失!\\n\\n建议配合过滤器使用..."
				""
			]
			id: "185074907753AE77"
			rewards: [{
				count: 3
				id: "21C32588D339B8FF"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "3B2EF593E573C2B8"
				item: "modularrouters:void_module"
				type: "item"
			}]
			x: 2.0d
			y: 4.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["哈哈哈,任务里的创造模式物品吓到你了吧？\\n\\n别担心,完成这个任务页面并不需要那个物品,我就是想吓唬你.\\n\\n\\n偶尔找点乐子..."]
			icon: "modularrouters:creative_module"
			id: "71FDF8CD953E46FC"
			rewards: [{
				count: 3
				id: "166D6D4C252D5E89"
				item: "modularrouters:blank_module"
				type: "item"
			}]
			tasks: [{
				id: "41EEE559F6402D81"
				title: "&a创造模块&f"
				type: "checkmark"
			}]
			x: 0.0d
			y: 4.5d
		}
		{
			dependencies: ["233C7CAA573D6045"]
			description: ["模块的过滤物品槽位有限,若需要更多请使用这个——批量物品过滤器.\\n\\n可添加更多物品至&a白名单或黑名单&f,然后像普通过滤器那样放置!\\n\\n制作特殊过滤器时会需要它!"]
			id: "143AE6889BDA9EF5"
			tasks: [{
				id: "3057A10D6D3A75D2"
				item: "modularrouters:bulk_item_filter"
				type: "item"
			}]
			x: -2.0d
			y: 7.5d
		}
		{
			dependencies: ["143AE6889BDA9EF5"]
			description: ["耐久度、&a流体储量&f、&a能量储量&f、附魔等级和&a食物营养价值&f都可作为&a检测过滤器&f的筛选条件.\\n\\n然后选择大于/小于/等于数值,或组合这些条件.\\n\\n输入具体数值后点击加号按钮即可添加至过滤器.\\n\\n比如仅保留高耐久工具或高&a营养价值&f的食物."]
			id: "4A03A671757B2EBF"
			tasks: [{
				id: "107FE3495FC5C0E7"
				item: "modularrouters:inspection_filter"
				type: "item"
			}]
			x: -1.0d
			y: 7.5d
		}
		{
			dependencies: ["143AE6889BDA9EF5"]
			description: ["按模组过滤非常实用,尤其适用于&d神化&f模组.\\n\\n放入某模组的物品后显示模组名称,点击加号即可添加!"]
			id: "6E5954C257726F86"
			tasks: [{
				id: "5BBF911BE5066A17"
				item: "modularrouters:mod_filter"
				type: "item"
			}]
			x: 0.0d
			y: 7.5d
		}
		{
			dependencies: ["143AE6889BDA9EF5"]
			description: ["这是代码吗？我又不是程序员!!？？!我只是想设计任务!\\n\\n在&a搜索栏&f输入正则表达式,点击加号即可添加至过滤器.匹配该表达式的物品都会被过滤."]
			id: "72A71E1D6D8996FD"
			tasks: [{
				id: "6E646B9915327365"
				item: "modularrouters:regex_filter"
				type: "item"
			}]
			x: 1.0d
			y: 7.5d
		}
		{
			dependencies: ["143AE6889BDA9EF5"]
			description: ["标签是Minecraft的核心机制.\\n\\n作为分类工具,它能将同类物品归组.比如用任意木板制作工作台,或用任意斧头高效砍树.\\n\\n放入物品后选择标签组进行过滤,例如用&a铁矿石&f标签筛选所有矿石."]
			id: "37F2BE3C2BAF7C2D"
			tasks: [{
				id: "7264131AC34825C1"
				item: "modularrouters:tag_filter"
				type: "item"
			}]
			x: 2.0d
			y: 7.5d
		}
		{
			dependencies: ["63E4529AF8894018"]
			description: [
				"这里展示用&d模块化路由器&f建造超简易&a怪物农场&f的经典案例(不含击杀方式).\\n\\n以&a凋灵骷髅&f农场为例:\\n\\n首先用&a真空模块&f将物品吸入缓冲器,接着用&a销毁模块&f处理&a石剑&f.为确保不会误删,需设置仅过滤&a石剑&f且不匹配&a伤害值&f的白名单.最后用发送模块将战利品传输至推荐使用的抽屉系统.\\n\\n仅需这些步骤!"
				""
				"{image:atm:textures/questpics/router/router_mob.png width:200 height:75 align:center}"
			]
			id: "53A5F5E49EA8D5AF"
			shape: "rsquare"
			size: 1.3d
			tasks: [
				{
					id: "69CFBD2DF15EBA0D"
					item: "modularrouters:vacuum_module"
					type: "item"
				}
				{
					id: "4DF12505FADDD7CC"
					item: "modularrouters:void_module"
					type: "item"
				}
				{
					id: "631388151555159B"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "modularrouters:sender_module_1"
									tag: {
										Damage: 0
									}
								}
								{
									Count: 1b
									id: "modularrouters:sender_module_2"
									tag: {
										Damage: 0
									}
								}
								{
									Count: 1b
									id: "modularrouters:sender_module_3"
									tag: {
										Damage: 0
									}
								}
							]
						}
					}
					type: "item"
				}
			]
			x: 3.2500000000000004d
			y: 2.6d
		}
		{
			dependencies: ["63E4529AF8894018"]
			description: [
				"厌倦手动采矿？何不让路由器代劳？\\n\\n先用放置器部署矿石,再由附魔破碎机开采.注意:破碎机需使用预先附魔的镐头合成,模块本身无法直接附魔.\\n\\n你可能需要另配路由器收集掉落矿石."
				""
				"{image:atm:textures/questpics/router/router_break.png width:150 height:100 align:center}"
			]
			id: "067AB3E4C4492F0D"
			shape: "rsquare"
			size: 1.3d
			tasks: [
				{
					id: "57087F48FE77A610"
					item: "modularrouters:breaker_module"
					type: "item"
				}
				{
					id: "3EEBE2CA2BFBB571"
					item: "modularrouters:placer_module"
					type: "item"
				}
			]
			x: -3.2500000000000004d
			y: 2.6d
		}
		{
			dependencies: ["63E4529AF8894018"]
			description: [
				"路由器也能用于农耕!\\n首先从简单农场开始.然后你需要一个&a执行模块&f并将其对准作物.接着在路由器中放入&a真空模块&f来收集作物.最后将所有部件装入路由器,用红石供能,让它自动耕作吧!"
				""
				"{image:atm:textures/questpics/router/router_farm.png width:50 height:150 align:center}"
			]
			id: "0D5691403AEC25A8"
			shape: "rsquare"
			size: 1.3d
			tasks: [
				{
					id: "4BC6CEDE3846216D"
					item: "modularrouters:activator_module"
					type: "item"
				}
				{
					id: "7B42B98FBD1C3654"
					item: "modularrouters:vacuum_module"
					type: "item"
				}
			]
			x: 0.0d
			y: -3.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包制作."
				"所有&eAllTheMods&r整合包均遵循&e保留所有权利&r协议,未经&eAllTheMods团队&r明确许可,不得用于其他公开整合包."
				""
				""
				""
				"该任务默认隐藏,若你看到此提示说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "0B4A47150708D7A0"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "00108F057048935C"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "59ABF1A82597A34D"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: -4.0d
		}
	]
	title: "&d模块化路由器&f"
}
