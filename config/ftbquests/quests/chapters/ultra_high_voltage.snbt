{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "ultra_high_voltage"
	group: "1DA67E79B40AB130"
	icon: "gtceu:wetware_processor_mainframe"
	id: "60A9BBC993EB2FD2"
	order_index: 10
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"我知道以超级计算机结束上章节并开启本节很奇怪,但事出有因."
				""
				"&a湿件处理器&f主机需要本节涉及的组件."
			]
			id: "2EA74A823D55D472"
			rewards: [{
				id: "138C41B0779E8FD9"
				type: "xp"
				xp: 1000
			}]
			shape: "diamond"
			size: 1.5d
			subtitle: "主机在哪？"
			tasks: [{
				id: "393B796BC1144684"
				item: "gtceu:wetware_processor_computer"
				type: "item"
			}]
			x: -9.0d
			y: 6.0d
		}
		{
			dependencies: [
				"3D89F65537D7CA1E"
				"2555BA914C466B5C"
				"702CE73E39E4D4BD"
			]
			description: ["此前大量工作都是为建造&n&l&5&a星锻&f!&r"]
			icon: "gtceu:star_forge"
			id: "7AE6AF0B5D3390E7"
			rewards: [{
				exclude_from_claim_all: true
				id: "1B8BCAA279AE6AF3"
				table_id: 8781463007120195614L
				type: "loot"
			}]
			shape: "heart"
			size: 2.0d
			subtitle: "锻造星辰"
			tasks: [
				{
					id: "618354EF9636D820"
					item: "gtceu:star_forge"
					type: "item"
				}
				{
					count: 6L
					id: "454F7D74D75CD02E"
					item: "mekanism:supercharged_coil"
					type: "item"
				}
				{
					count: 38L
					id: "73DEF8CD08315211"
					item: "gtceu:superconducting_coil"
					type: "item"
				}
				{
					count: 64L
					id: "333856BA249BF370"
					item: "gtceu:trinium_coil_block"
					type: "item"
				}
				{
					count: 225L
					id: "5B999D1E4E018709"
					item: "gtceu:atomic_casing"
					type: "item"
				}
				{
					count: 224L
					id: "084BFE437F0F086D"
					item: "connectedglass:clear_glass_black"
					type: "item"
				}
				{
					id: "34D497643849738F"
					item: "allthetweaks:atm_star_block"
					type: "item"
				}
			]
			x: 8.0d
			y: 4.0d
		}
		{
			dependencies: [
				"3D89F65537D7CA1E"
				"2555BA914C466B5C"
				"054D2D3C20C2D32F"
				"6786C701B7C4980B"
			]
			description: [
				"没错.我们有Mk.I、Mk.II和Mk.III版&a湮灭反应堆&f,现在还有巨型&a湮灭反应堆&f."
				""
				"各司其职,都是前进的关键.行动起来,建造属于你的巨型&a湮灭反应堆&f."
			]
			icon: "gtceu:mega_fusion_reactor"
			id: "39CD35C91F07258C"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3F44AB57C50B81A8"
					table_id: 8781463007120195614L
					type: "loot"
				}
				{
					exclude_from_claim_all: true
					id: "221E07FA30C8438B"
					table_id: 341947171990021391L
					type: "loot"
				}
			]
			shape: "gear"
			size: 1.6d
			subtitle: "巨型&a湮灭反应堆&f"
			tasks: [
				{
					id: "6B99152931C28CF4"
					item: "gtceu:mega_fusion_reactor"
					type: "item"
				}
				{
					count: 32L
					id: "38001A0BC813A12F"
					item: "mekanism:supercharged_coil"
					type: "item"
				}
				{
					count: 20L
					id: "15645CCCFD36B3FD"
					item: "gtceu:fusion_glass"
					type: "item"
				}
				{
					count: 208L
					id: "1DF6ABF3DEA03113"
					item: "gtceu:fusion_casing_mk3"
					type: "item"
				}
				{
					count: 88L
					id: "1CEE1CF49EB683A2"
					item: "gtceu:fusion_coil"
					type: "item"
				}
				{
					count: 64L
					id: "2F1FE1A604A2BF82"
					item: "gtceu:tritanium_coil_block"
					type: "item"
				}
				{
					count: 169L
					id: "2952CCEFEE9245AB"
					item: "gtceu:atomic_casing"
					type: "item"
				}
				{
					count: 40L
					id: "11920FFB5235E181"
					item: "gtceu:heatproof_machine_casing"
					type: "item"
				}
			]
			x: 1.8000000000000007d
			y: -2.6999999999999993d
		}
		{
			dependencies: [
				"2B8E66760514BE77"
				"054D2D3C20C2D32F"
			]
			description: [
				"&a湮灭反应堆&f Mk.III.你已预见未来,明白这是必要环节."
				""
				"虽然未来如此,但Mk.III在其设计用途中仍极具价值."
				""
				"至少建造1台Mk.III反应堆,它将大有裨益."
			]
			icon: "gtceu:uv_fusion_reactor"
			id: "6E18951E41103391"
			rewards: [{
				exclude_from_claim_all: true
				id: "689AC54A19B9EE50"
				table_id: 341947171990021391L
				type: "loot"
			}]
			shape: "diamond"
			size: 1.5d
			subtitle: "Mk. III型"
			tasks: [
				{
					id: "1786D0D015C3884C"
					item: "gtceu:uv_fusion_reactor"
					type: "item"
				}
				{
					count: 16L
					id: "4E15B886A44A5574"
					item: "gtceu:uv_energy_input_hatch"
					type: "item"
				}
				{
					count: 48L
					id: "272F201D0A7BA036"
					item: "gtceu:fusion_casing_mk3"
					type: "item"
				}
				{
					count: 4L
					id: "4700909D84885E86"
					item: "gtceu:fusion_coil"
					type: "item"
				}
				{
					count: 31L
					id: "7FC436C406CF454A"
					item: { Count: 31, id: "gtceu:fusion_glass" }
					type: "item"
				}
			]
			x: -1.5d
			y: 0.29999999999999893d
		}
		{
			dependencies: [
				"054D2D3C20C2D32F"
				"702CE73E39E4D4BD"
			]
			description: ["不,我们其实不需要这个.现阶段大型电路组装设施可在UV级及以上能源下建造运行!"]
			id: "0CBF5A49066468DD"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "7D7A9F44A20C359E"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "需要这个吗？"
			tasks: [{
				id: "3C9575096E4E44E0"
				item: "gtceu:uv_circuit_assembler"
				type: "item"
			}]
			x: -0.20000000000000004d
			y: 3.4000000000000004d
		}
		{
			dependencies: [
				"054D2D3C20C2D32F"
				"337B492F974628A4"
				"702CE73E39E4D4BD"
			]
			description: [
				"UHV能源仓.我们终于抵达了能源仓的巅峰境界!"
				""
				"现在我们可以设置多方块结构,使其能够处理任何级别的电力需求!"
			]
			id: "5561EB1E3DD77EC5"
			rewards: [{
				exclude_from_claim_all: true
				id: "2B3038B536DADF24"
				table_id: 341947171990021391L
				type: "loot"
			}]
			shape: "gear"
			size: 1.5d
			subtitle: "登峰造极"
			tasks: [{
				id: "3009E94D530487D4"
				item: "gtceu:uhv_energy_input_hatch"
				type: "item"
			}]
			x: -2.5d
			y: 1.8000000000000007d
		}
		{
			dependencies: ["5561EB1E3DD77EC5"]
			description: ["虽然UHV能源仓是能量传输的巅峰之作,但我们还有4A UHV能源仓."]
			id: "6F8E58FF4D96C4BC"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "6E7C3514A700FF15"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "等等...不是说已经到顶了吗？"
			tasks: [{
				id: "5D50568CB2796302"
				item: "gtceu:uhv_energy_input_hatch_4a"
				type: "item"
			}]
			x: -3.0d
			y: 3.0d
		}
		{
			dependencies: [
				"454F2BF7F2E25D83"
				"054D2D3C20C2D32F"
				"3D89F65537D7CA1E"
			]
			description: [
				"其他电池公司在它面前都不值一提!"
				""
				"这是&a终极电池&f,属于UHV级别!满足你所有高耗能设备的需求(当然是UHV级别的)"
			]
			id: "0DB92C70D04725BE"
			rewards: [{
				exclude_from_claim_all: true
				id: "6D073546ECACAEB6"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "金霸王电池望尘莫及"
			tasks: [{
				id: "5103FF4986087F40"
				item: "gtceu:max_battery"
				type: "item"
			}]
			x: 5.5d
			y: 0.3000000000000007d
		}
		{
			dependencies: ["2EA74A823D55D472"]
			description: [
				"这些巨型线圈是线圈工艺的终极形态."
				""
				"用这些线圈我们现在可以处理&aUHV超导体&f,最终将它们铸造成锭!"
			]
			id: "44EE336BC265D21C"
			rewards: [{
				exclude_from_claim_all: true
				id: "48D1C1DAECEAAB94"
				table_id: 341947171990021391L
				type: "random"
			}]
			subtitle: "三钛合金线圈"
			tasks: [{
				id: "204D0E0D57E23E89"
				item: "gtceu:tritanium_coil_block"
				type: "item"
			}]
			x: -5.5d
			y: 6.0d
		}
		{
			dependencies: [
				"054D2D3C20C2D32F"
				"6786C701B7C4980B"
			]
			description: [
				"如果64并行处理还不够,256并行如何？这正是这个方块的功能.它允许你同时运行256个并行任务!"
				""
				"这是最高级别的并行控制仓!"
			]
			id: "0D59094D0C23C44F"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "28EC4958E4D86290"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "64个还不够？"
			tasks: [{
				id: "685632AB2A3AFA5C"
				item: "gtceu:uv_parallel_hatch"
				type: "item"
			}]
			x: 1.8000000000000007d
			y: -1.299999999999999d
		}
		{
			dependencies: ["6786C701B7C4980B"]
			description: [
				"UV力场发生器是你将建造的最后一种力场发生器."
				""
				"合理规划来制作足够数量的发生器."
			]
			hide_dependency_lines: true
			id: "3D89F65537D7CA1E"
			rewards: [{
				exclude_from_claim_all: true
				id: "379BAF8D63EAA251"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "终极力场发生器"
			tasks: [{
				id: "26F1CE1C23672A2A"
				item: "gtceu:uv_field_generator"
				type: "item"
			}]
			x: 5.5d
			y: -1.6999999999999993d
		}
		{
			dependencies: ["695E846B663EC185"]
			description: [
				"太好了!现在我们可以制作UHV外壳了."
				""
				"但似乎没有U&aHV机器&f.为什么需要这个外壳？当然是为了各种仓和总线!"
			]
			id: "337B492F974628A4"
			rewards: [{
				id: "2504A95AFC702921"
				item: "gtceu:uhv_machine_hull"
				type: "item"
			}]
			subtitle: "机器在哪？"
			tasks: [{
				id: "10FC27F148761FA1"
				item: "gtceu:uhv_machine_hull"
				type: "item"
			}]
			x: -4.0d
			y: 1.3000000000000007d
		}
		{
			dependencies: [
				"44EE336BC265D21C"
				"695E846B663EC185"
			]
			description: [
				"这是一种复杂的金属锭.我们将多种金属熔炼成这种合金,这是有充分理由的."
				""
				"用这种锭,我们现在可以制作一些高级组件!"
			]
			id: "702CE73E39E4D4BD"
			rewards: [{
				count: 4
				id: "1ED0731892366C24"
				item: "gtceu:ruthenium_trinium_americium_neutronate_ingot"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "复杂的工艺..."
			tasks: [{
				id: "2EDA6683F4114183"
				item: "gtceu:ruthenium_trinium_americium_neutronate_ingot"
				type: "item"
			}]
			x: -5.5d
			y: 4.0d
		}
		{
			dependencies: ["44EE336BC265D21C"]
			description: [
				"我们又见到另一种极其复杂的合金."
				""
				"但正如其他复杂合金所示,它们虽然制作复杂,但总能带来巨大收益.这个也不例外."
			]
			hide_dependent_lines: true
			id: "30954ADF34DB05A7"
			rewards: [{
				count: 8
				id: "09A65B9DDFB8CFF3"
				item: "gtceu:enriched_naquadah_trinium_europium_duranide_ingot"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "复合合金"
			tasks: [{
				id: "20552163E8FFCF03"
				item: "gtceu:enriched_naquadah_trinium_europium_duranide_ingot"
				type: "item"
			}]
			x: 0.5d
			y: 6.0d
		}
		{
			dependencies: ["702CE73E39E4D4BD"]
			description: [
				"我相信你在达到这个阶段时已经制作了很多电炉."
				""
				"还有更多挑战等着你.但现在,你可以制作旋转炉,并运行一些并行任务!让我们加速处理流程!"
			]
			icon: "gtceu:mega_blast_furnace"
			id: "625B5E3CDDAECFFD"
			rewards: [{
				exclude_from_claim_all: true
				id: "72E4BA4940848225"
				table_id: 8781463007120195614L
				type: "loot"
			}]
			shape: "gear"
			size: 1.0d
			subtitle: "巨型电炉!"
			tasks: [
				{
					id: "66D7D97C3FAA17D4"
					item: "gtceu:mega_blast_furnace"
					type: "item"
				}
				{
					count: 132L
					id: "0F5FC4C9058975BD"
					item: "gtceu:naquadah_alloy_frame"
					type: "item"
				}
				{
					count: 28L
					id: "7541ECC2507CE71C"
					item: "gtceu:tungstensteel_firebox_casing"
					type: "item"
				}
				{
					count: 40L
					id: "11597D8C8B61CF67"
					item: "gtceu:extreme_engine_intake_casing"
					type: "item"
				}
				{
					count: 96L
					id: "06C01CB26C01E667"
					item: "gtceu:cupronickel_coil_block"
					type: "item"
				}
				{
					count: 72L
					id: "7B2A32F3AA195AB7"
					item: "gtceu:tungstensteel_pipe_casing"
					type: "item"
				}
				{
					count: 20L
					id: "17C7595ADF6B7BAB"
					item: "gtceu:heat_vent"
					type: "item"
				}
				{
					count: 382L
					id: "727FA76459DBE141"
					item: "gtceu:high_temperature_smelting_casing"
					type: "item"
				}
				{
					count: 88L
					id: "30BE3BCD9E872FB8"
					item: "gtceu:robust_machine_casing"
					type: "item"
				}
			]
			x: -6.5d
			y: 3.5d
		}
		{
			dependencies: ["702CE73E39E4D4BD"]
			description: [
				"现在,随着旋转炉处理所有任务,你需要一个能跟上冷却速度的多方块结构来冷却金属锭."
				""
				"这就是批量&a急速冷冻机&f的用武之地!"
			]
			icon: "gtceu:mega_vacuum_freezer"
			id: "67AA17BCDE37DFAB"
			rewards: [{
				exclude_from_claim_all: true
				id: "23F4A6D4A8E87215"
				table_id: 8781463007120195614L
				type: "loot"
			}]
			shape: "gear"
			subtitle: "与旋转炉完美搭配"
			tasks: [
				{
					id: "28EEC6756486D379"
					item: "gtceu:mega_vacuum_freezer"
					type: "item"
				}
				{
					count: 74L
					id: "1237C1C3D4F81E76"
					item: "gtceu:tungstensteel_pipe_casing"
					type: "item"
				}
				{
					count: 26L
					id: "33A1A44710F78A0D"
					item: "gtceu:heat_vent"
					type: "item"
				}
				{
					count: 9L
					id: "21CC962B100848B4"
					item: "gtceu:tempered_glass"
					type: "item"
				}
				{
					count: 36L
					id: "613FCC5D29BA60B8"
					item: "gtceu:clean_machine_casing"
					type: "item"
				}
				{
					count: 154L
					id: "068B1FF9F58C3C18"
					item: "gtceu:frostproof_machine_casing"
					type: "item"
				}
			]
			x: -6.5d
			y: 4.5d
		}
		{
			dependencies: ["30954ADF34DB05A7"]
			description: [
				"现在我们有了&aUV超导体&f线缆!用于制作多种组件,其中一些是我们继续前进所必需的."
				""
				"更多线缆.... 太棒了."
			]
			id: "2B8E66760514BE77"
			rewards: [{
				count: 8
				id: "46ACF990FF292361"
				item: "gtceu:enriched_naquadah_trinium_europium_duranide_single_wire"
				random_bonus: 16
				type: "item"
			}]
			subtitle: "&aUV超导体&f"
			tasks: [{
				id: "6C4C914784FB64DC"
				item: "gtceu:enriched_naquadah_trinium_europium_duranide_single_wire"
				type: "item"
			}]
			x: 0.5d
			y: -0.6999999999999993d
		}
		{
			description: [
				"虽然这可能是最复杂的合成之一,跨越多个级别的制作流程,但它是必需品."
				""
				"是的,制作一个需要很多步骤,但试着优化合成路线.相信之后你会感谢这个建议."
			]
			id: "454F2BF7F2E25D83"
			rewards: [{
				exclude_from_claim_all: true
				id: "35C2A7CABA21D940"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "复合能源"
			tasks: [{
				id: "0B711F2A4A5DCE95"
				item: "gtceu:energy_cluster"
				type: "item"
			}]
			x: 4.5d
			y: -0.6999999999999993d
		}
		{
			dependencies: ["3D40D91D7D948714"]
			description: ["我相信你现在明白为什么这些板至关重要.但如你所知,它们将带来巨大收益."]
			id: "2555BA914C466B5C"
			rewards: [{
				exclude_from_claim_all: true
				id: "5C723C9FAE82468A"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "最佳外壳"
			tasks: [{
				id: "3DF016CBFB16E05C"
				item: "gtceu:atomic_casing"
				type: "item"
			}]
			x: 8.0d
			y: -2.6999999999999993d
		}
		{
			dependencies: ["7F5DAB3EDB6E9592"]
			description: ["这些板材的需求量将会很大.想办法为自己供应大量这类板材可能是个挑战,但绝对值得."]
			id: "3D40D91D7D948714"
			rewards: [{
				count: 4
				id: "3893B82B1CA8161B"
				item: "gtceu:trinaquadalloy_plate"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "餐盘很重要"
			tasks: [{
				id: "5137EB9F0E75A746"
				item: "gtceu:trinaquadalloy_plate"
				type: "item"
			}]
			x: 9.0d
			y: -1.5d
		}
		{
			dependencies: ["0CF6D11016BAF3D0"]
			description: [
				"将会生产大量特里纳夸合金."
				""
				"研究如何优化流程以提高速度和效率可能是必要的."
			]
			id: "7F5DAB3EDB6E9592"
			rewards: [{
				count: 4
				id: "7A29C802031CEB87"
				item: "gtceu:trinaquadalloy_ingot"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "冷却合金"
			tasks: [{
				id: "74258E10C48FAA93"
				item: "gtceu:trinaquadalloy_ingot"
				type: "item"
			}]
			x: 9.0d
			y: -0.5d
		}
		{
			description: ["现在我们开始生产复杂Naquadah合金.就像我们正在研发的其他复杂资源一样,这个将带来显著效益.\\n\\n它只能在合金&a高炉冶炼&f中制造."]
			id: "0CF6D11016BAF3D0"
			rewards: [{
				id: "7BB30A393756D2D3"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:trinaquadalloy"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:trinaquadalloy"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "复合Naquadah"
			tasks: [{
				id: "747C0465749AAB2C"
				item: "gtceu:molten_trinaquadalloy_bucket"
				type: "item"
			}]
			x: 9.0d
			y: 0.5d
		}
		{
			dependencies: ["6F8E58FF4D96C4BC"]
			description: ["你没看错.这是16A UHV能量舱口.其功率是4A能量舱口的4倍."]
			id: "0DB21D996607BD8D"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "2FDB3BAF2DB98AE3"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "16A UHV能量舱口？!"
			tasks: [{
				id: "1FE57FC0C5BBD345"
				item: "gtceu:uhv_energy_input_hatch_16a"
				type: "item"
			}]
			x: -2.0d
			y: 3.0d
		}
		{
			dependencies: [
				"1FEB1ECDDDD8AD0E"
				"418E02FFE241A734"
				"79E29CFE2399AE96"
				"5074BBBCE86D058E"
			]
			description: [
				"这才像话!"
				""
				"虽然我们不会立即直接使用它.但现在就设置好制作和收集系统会非常有益,能节省大量时间!"
			]
			id: "0A1BACA070EB5264"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "381F47984205C92E"
					table_id: 341947171990021391L
					type: "loot"
				}
				{
					id: "3A692A70D38C3533"
					item: {
						Count: 1
						ForgeCaps: {
							Parent: {
								Amount: 16000
								FluidName: "gtceu:star_matter_plasma"
								capacity: 16000
							}
						}
						id: "evilcraft:dark_tank"
						tag: {
							Fluid: {
								Amount: 16000
								FluidName: "gtceu:star_matter_plasma"
							}
							capacity: 16000
						}
					}
					type: "item"
				}
			]
			subtitle: "星尘物质"
			tasks: [{
				id: "5D4E3E64BF2AF1FE"
				item: "gtceu:star_matter_plasma_bucket"
				type: "item"
			}]
			x: 1.799999999999999d
			y: -6.0d
		}
		{
			description: ["&a液态铁&f等离子体看似是种奇怪的资源,但对你要制作的东西来说必不可少."]
			id: "1FEB1ECDDDD8AD0E"
			rewards: [{
				id: "45C6CD8552FBD30E"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:iron_plasma"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:iron_plasma"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "万物"
			tasks: [{
				id: "3E9D00BCF9710B1B"
				item: "gtceu:iron_plasma_bucket"
				type: "item"
			}]
			x: 0.7999999999999989d
			y: -5.0d
		}
		{
			dependencies: ["2C9DDB3AAA30E02A"]
			description: ["液态&a氦等离子体&f.请确保你的生产线能产出足够的氦"]
			id: "418E02FFE241A734"
			rewards: [{
				id: "3437B1764F2C8CB3"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:helium_plasma"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:helium_plasma"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "等离子"
			tasks: [{
				id: "06BCA11DE67C5D89"
				item: "gtceu:helium_plasma_bucket"
				type: "item"
			}]
			x: 1.799999999999999d
			y: -5.0d
		}
		{
			description: ["&a液氧&f等离子体.虽然我们已经消耗了大量氧气,但还有更多用途!"]
			id: "79E29CFE2399AE96"
			rewards: [{
				id: "49A3DC24BAC9CCED"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:oxygen_plasma"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:oxygen_plasma"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "等离子体"
			tasks: [{
				id: "1E765ED0D810792E"
				item: "gtceu:oxygen_plasma_bucket"
				type: "item"
			}]
			x: 2.8000000000000007d
			y: -5.0d
		}
		{
			dependencies: ["39CD35C91F07258C"]
			description: [
				"下一阶段我们需要制作大量微宇宙催化剂."
				""
				"这个组件使用星尘&a等离子物质&f来制作催化剂."
				""
				"虽然现在不需要立即制作,但提前准备会很有帮助,因为制作16倍催化剂需要大量星尘&a等离子物质&f."
			]
			id: "2C9DDB3AAA30E02A"
			rewards: [{
				id: "1DC2112E52BF01A3"
				type: "xp"
				xp: 1000
			}]
			subtitle: "现在开始"
			tasks: [{
				id: "6F07606E3F7BE160"
				title: "快速启动"
				type: "checkmark"
			}]
			x: 1.799999999999999d
			y: -4.0d
		}
		{
			dependencies: ["2B8E66760514BE77"]
			description: [
				"你成功了!"
				""
				"这就是你能制作的&a最高&f级处理器!"
				""
				"但旅程还未结束...虽然这是最后的处理器,后面还有更多内容.从现在开始会更有趣!"
			]
			id: "054D2D3C20C2D32F"
			rewards: [{
				exclude_from_claim_all: true
				id: "10481F8A04D25540"
				table_id: 8781463007120195614L
				type: "loot"
			}]
			shape: "gear"
			size: 1.75d
			subtitle: "巨大成功!!!"
			tasks: [{
				id: "61316E46DA048A55"
				item: "gtceu:wetware_processor_mainframe"
				type: "item"
			}]
			x: 4.25d
			y: 0.8000000000000007d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务被故意隐藏,若你看到此信息,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "6023F9AF1606940A"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "2A53921BBB7FC08F"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "466E86A305EA10CC"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -9.0d
			y: 4.0d
		}
		{
			description: ["这是个奇特元素.在干燥环境中具有极强的抗腐蚀性,但在潮湿环境中却不耐腐蚀."]
			id: "28F49121DEB0000A"
			rewards: [{
				id: "443A6F81AD167386"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:lutetium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:lutetium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "液态镥"
			tasks: [{
				id: "41A278CBAEF5905A"
				item: "gtceu:lutetium_bucket"
				type: "item"
			}]
			x: -8.0d
			y: -0.5d
		}
		{
			dependencies: ["28F49121DEB0000A"]
			description: [
				"镅通常用作烟雾探测器中的粒子检测器."
				""
				"其用量极其微小且对人体无害,但对烟雾检测非常有效,因此被广泛使用."
			]
			id: "5AD33290313151D6"
			rewards: [{
				id: "365681A8DF30134E"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:americium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:americium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "液态镅"
			tasks: [{
				id: "56FF9905F151C1C3"
				item: "gtceu:americium_bucket"
				type: "item"
			}]
			x: -7.0d
			y: 0.0d
		}
		{
			dependencies: [
				"097735604CB11E83"
				"5AD33290313151D6"
				"6E18951E41103391"
			]
			description: ["中子素,一种假想的完全由中子构成的物质.\\n\\n我们需要大量这种物质,建议采用被动生产方式!"]
			id: "695E846B663EC185"
			rewards: [{
				id: "0C7A433FBA240813"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:neutronium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:neutronium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			shape: "gear"
			size: 1.75d
			subtitle: "中子素!!!"
			tasks: [{
				id: "273FC653514D8BC2"
				item: "gtceu:neutronium_bucket"
				type: "item"
			}]
			x: -5.449999999999999d
			y: 0.775d
		}
		{
			description: ["你可以直接用提取器处理锭块,或者使用&a湮灭反应堆&f Mk3!\\n\\n为什么要用&a湮灭反应堆&f？通常Naquadah粉尘与Naquadria锭的比例是6:1,但使用聚变反应时,富集Naq与Naquadria的比例会变成4:1."]
			id: "097735604CB11E83"
			rewards: [{
				id: "559BE511F5D982CE"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:naquadria"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:naquadria"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "液态Naquadria"
			tasks: [{
				id: "65C1C5D7EF816179"
				item: "gtceu:naquadria_bucket"
				type: "item"
			}]
			x: -7.0d
			y: 1.5d
		}
		{
			dependencies: ["695E846B663EC185"]
			description: [
				"抱歉用了双关语,但我实在忍不住."
				""
				"总之,掺杂了中子素的硅具有超高效率和精细品质,让我们从单块晶锭就能获得巨大回报!"
			]
			id: "142D12B41C36D68A"
			rewards: [{
				count: 3
				id: "473AF9906EB7EF01"
				item: "gtceu:neutronium_boule"
				random_bonus: 3
				type: "item"
			}]
			subtitle: "晶锭大丰收"
			tasks: [{
				id: "0EB38073B9196667"
				item: "gtceu:neutronium_boule"
				type: "item"
			}]
			x: -5.5d
			y: -1.0d
		}
		{
			dependencies: ["142D12B41C36D68A"]
			description: ["中子素掺杂晶圆.&a高&f品质的硅材料!"]
			id: "0F3DF502872A308E"
			rewards: [
				{
					count: 4
					id: "38517B0FE2A644DA"
					item: "gtceu:neutronium_wafer"
					random_bonus: 4
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "2A962A16171EB051"
					table_id: 341947171990021391L
					type: "loot"
				}
			]
			subtitle: "切片"
			tasks: [{
				id: "21548670852833ED"
				item: "gtceu:neutronium_wafer"
				type: "item"
			}]
			x: -4.5d
			y: -1.6999999999999997d
		}
		{
			dependencies: ["0F3DF502872A308E"]
			description: ["将中子素晶圆放入激光雕刻机,搭配黑色透镜,就能得到HASOC芯片,这对我们后续发展大有裨益."]
			id: "5F3BF06A1DA8AB8D"
			rewards: [{
				count: 4
				id: "3212B54A2EE954FA"
				item: "gtceu:highly_advanced_soc_wafer"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "激光雕刻"
			tasks: [
				{
					id: "74999972610FD53F"
					item: "gtceu:highly_advanced_soc_wafer"
					type: "item"
				}
				{
					id: "6CF65EBE0534FBA6"
					item: "gtceu:black_glass_lens"
					type: "item"
				}
			]
			x: -3.5d
			y: -2.3999999999999995d
		}
		{
			dependencies: ["5F3BF06A1DA8AB8D"]
			description: ["现在我们可以一次性制作4个LuV级&a湿件处理器&f了!"]
			id: "3F3F70FEEE8AFEEA"
			rewards: [{
				count: 4
				id: "0D40ED5B4CD77D3F"
				item: "gtceu:highly_advanced_soc"
				random_bonus: 8
				type: "item"
			}]
			tasks: [{
				id: "530BBDE31298E519"
				item: "gtceu:highly_advanced_soc"
				type: "item"
			}]
			x: -2.5d
			y: -3.099999999999999d
		}
		{
			dependencies: ["695E846B663EC185"]
			description: ["引力之星是我们即将制造的高级机器中另一个至关重要的组件."]
			id: "6786C701B7C4980B"
			rewards: [{
				exclude_from_claim_all: true
				id: "556BE291ABBFB5A8"
				table_id: 341947171990021391L
				type: "loot"
			}]
			subtitle: "星辰引力场"
			tasks: [{
				id: "6B219FCE15079A27"
				item: "gtceu:gravi_star"
				type: "item"
			}]
			x: -1.5d
			y: -1.6999999999999993d
		}
		{
			description: [""]
			id: "5074BBBCE86D058E"
			rewards: [{
				id: "0B2326152AE29F28"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:nitrogen_plasma"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:nitrogen_plasma"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "4169613D6343A843"
				item: "gtceu:nitrogen_plasma_bucket"
				type: "item"
			}]
			x: 1.799999999999999d
			y: -7.0d
		}
	]
	title: "超高压电"
}
