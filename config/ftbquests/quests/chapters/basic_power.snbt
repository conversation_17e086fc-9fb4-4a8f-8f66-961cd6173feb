{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "basic_power"
	group: "6614EE2378B8AFB9"
	icon: {
		Count: 1
		id: "mekanism:creative_energy_cube"
		tag: {
			mekData: {
				EnergyContainers: [{
					Container: 0b
					stored: "18446744073709551615.9999"
				}]
			}
		}
	}
	id: "5D045EF1AB73DF70"
	images: [{
		height: 5.0d
		image: "atm:textures/questpics/allthepower.png"
		rotation: 0.0d
		width: 15.0d
		x: -3.5d
		y: -3.5d
	}]
	order_index: 0
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["本章节专注能源相关所有内容!\\n\\n在这里你将学习能源生产、存储与传输的基础方法."]
			icon: "minecraft:redstone_torch"
			id: "4AB0DD227471FDBF"
			rewards: [{
				id: "3C8F065AEE4CDB80"
				type: "xp"
				xp: 10
			}]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "4800DD5A7039B8B7"
				title: "全方位能源解决方案!"
				type: "checkmark"
			}]
			x: -3.5d
			y: 0.5d
		}
		{
			dependencies: ["5E41363F9AE243F3"]
			description: ["&9&d极限反应堆&f&r提供可完全自定义尺寸、效率等多参数的多方块反应堆.\\n\\n关于该模组的入门指南,请查阅专属任务线!"]
			hide_dependency_lines: false
			icon: "bigreactors:basic_reactorchargingportfe"
			id: "6D6E07564D8FDD8D"
			rewards: [{
				id: "1C7FBA6844D50ABA"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			subtitle: "高度可定制的反应堆!"
			tasks: [{
				id: "70CC25EA15DBE615"
				title: "&d&a大型反应堆&f&f"
				type: "checkmark"
			}]
			title: "&d极限反应堆&f"
			x: -0.5d
			y: 3.0d
		}
		{
			dependencies: ["42D173B9FF8D16E4"]
			description: ["&d通用机械&f提供了多种利用可再生资源发电的方式.\\n\\n&9&a太阳能发电模块&f&r利用太阳能满足基础电力需求.夜间无法工作,且必须暴露在天空下,玻璃遮挡不影响.\\n\\n&9&a风力发电机&f&r通过风力发电.除非你能手动旋转它,否则其转速取决于放置的Y轴高度,同时上方不能有任何方块遮挡."]
			hide_dependency_lines: false
			id: "48DC9E8E9D21A2FA"
			rewards: [
				{
					id: "2E06B6F7B5224A02"
					type: "xp"
					xp: 25
				}
				{
					exclude_from_claim_all: true
					id: "089AE645B9673F30"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "rsquare"
			subtitle: "利用太阳能"
			tasks: [{
				id: "59DE118DD6597A4E"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanismgenerators:solar_generator"
							}
							{
								Count: 1b
								id: "mekanismgenerators:advanced_solar_generator"
							}
							{
								Count: 1b
								id: "mekanismgenerators:wind_generator"
							}
						]
					}
				}
				title: "&a再生能源&f Gens"
				type: "item"
			}]
			title: "&d通用机械&f: &a再生能源&f"
			x: -5.0d
			y: 4.5d
		}
		{
			dependencies: ["42D173B9FF8D16E4"]
			description: ["&a&d通用机械&f的&r &a热力发电机&f采用独特的发电机制,拥有两种工作模式:\\n\\n&9被动模式:&r用熔岩源或流动熔岩块包围发电机,会通过热量持续产生电力.可在顶部放置熔岩源让其自然流下,注意先连接好能量管道!\\n\\n&9主动模式:&r放入煤炭或木材等可燃物燃烧发电,但效率较低."]
			id: "11D09E918015355C"
			rewards: [
				{
					id: "3E3F4483E3804F21"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "4C4527E368FC896E"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "rsquare"
			subtitle: "&d通用机械&f的初始电力方案"
			tasks: [{
				id: "44999798D86177A3"
				item: "mekanismgenerators:heat_generator"
				type: "item"
			}]
			title: "&a热力发电机&f"
			x: -6.5d
			y: 3.0d
		}
		{
			dependencies: ["42D173B9FF8D16E4"]
			description: ["&dRF工具箱&f的&a燃煤发电机&f是最易制作的燃煤发电机之一.\\n\\n操作简单,根据输入燃料产生可观电力,并能自动向相邻方块输送能量."]
			id: "0BB367839D28607D"
			rewards: [
				{
					id: "698CCE0DFA894C3A"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "7D172A6A2CA90F91"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "rsquare"
			tasks: [{
				id: "0F7D7AE91E20F778"
				item: "rftoolspower:coalgenerator"
				type: "item"
			}]
			title: "&a燃煤发电机&f"
			x: -6.0d
			y: 4.0d
		}
		{
			description: ["寻找前期发电方案？这些是理想的入门选择.\\n\\n虽然发电量有限,但在游戏前期通常容易获取且成本低廉!"]
			hide_dependency_lines: true
			icon: "minecraft:coal"
			id: "42D173B9FF8D16E4"
			rewards: [{
				id: "0CE525ECCA99C748"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 1.5d
			subtitle: "初始电力设备"
			tasks: [{
				id: "55F3497E936C4885"
				title: "&a游戏初期&f能源方案"
				type: "checkmark"
			}]
			x: -5.0d
			y: 3.0d
		}
		{
			dependencies: ["1F81EA5E45424308"]
			description: ["这是可通过管道升级件强化的基础管道.\\n\\n要从方块提取能量,将管道贴邻放置后,用管道扳手按住Shift+右键点击连接面设置为提取模式.\\n\\n&9Pipez&r模组还能运输物品、气体和液体!也可制作多功能合一的&a&a通用管道&f&r."]
			id: "4EEAB467C722ECE7"
			rewards: [
				{
					id: "4FE5FAE1AA387086"
					type: "xp"
					xp: 10
				}
				{
					count: 8
					id: "08460933939CF881"
					item: "pipez:energy_pipe"
					type: "item"
				}
			]
			shape: "rsquare"
			tasks: [
				{
					id: "467584E3BB06E9CD"
					item: "pipez:energy_pipe"
					type: "item"
				}
				{
					id: "5299A78587A00044"
					item: "pipez:wrench"
					type: "item"
				}
			]
			title: "&9使用Pipez模组:&r &a&a能量管道&f"
			x: -10.5d
			y: 0.5d
		}
		{
			dependencies: ["1F81EA5E45424308"]
			description: ["&d通用机械&f提供美观的能量传输线缆.\\n\\n若连接的机器自带输入输出功能则无需配置,否则需用&9配置器&r调整线缆模式,Shift+右键切换推送/拉取模式."]
			id: "5C47935A3B2877FF"
			rewards: [
				{
					id: "1C5C0D374EC4391D"
					type: "xp"
					xp: 10
				}
				{
					id: "46E642A34AD07DB6"
					item: "mekanism:alloy_infused"
					type: "item"
				}
			]
			shape: "rsquare"
			subtitle: "&d通用机械&f的&a能量传输&f管道"
			tasks: [{
				id: "6C4B8A2662030CB0"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:basic_universal_cable"
							}
							{
								Count: 1b
								id: "mekanism:advanced_universal_cable"
							}
							{
								Count: 1b
								id: "mekanism:elite_universal_cable"
							}
							{
								Count: 1b
								id: "mekanism:ultimate_universal_cable"
							}
						]
					}
				}
				title: "通用电缆"
				type: "item"
			}]
			title: "&9使用&d通用机械&f:&r &a基础通用线缆"
			x: -7.5d
			y: 0.5d
		}
		{
			description: ["这里收录了多种机器能量输出方案!\\n\\n包含&a有线&r和&9无线&r等多种传输方式."]
			hide_dependency_lines: true
			icon: {
				Count: 1
				id: "mekanism:energy_tablet"
				tag: {
					mekData: {
						EnergyContainers: [{
							Container: 0b
							stored: "1000000"
						}]
					}
				}
			}
			id: "1F81EA5E45424308"
			rewards: [{
				id: "5C863FCC138617D2"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "6D3C243268479EE9"
				title: "能量传输"
				type: "checkmark"
			}]
			x: -9.0d
			y: 0.5d
		}
		{
			dependencies: ["1F81EA5E45424308"]
			description: ["深入&d通用机械&f后,这个装置能实现万物无线传输.\\n\\n可设置特定频道单独传输物品、能量或气体,功能强大."]
			id: "7FE969CB4B419FC6"
			rewards: [
				{
					id: "31A1DA2A1735B16F"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2960A7A9FFFE9624"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "rsquare"
			subtitle: "同时支持物品、气体、液体和热量传输"
			tasks: [{
				id: "3E843C44C9CD3ACC"
				item: "mekanism:quantum_entangloporter"
				type: "item"
			}]
			title: "&9无线传输:&r &e&a量子传送装置&f"
			x: -10.0d
			y: 1.5d
		}
		{
			description: ["本节展示多种能量存储方案!"]
			hide_dependency_lines: true
			icon: {
				Count: 1
				id: "powah:battery_nitro"
				tag: { }
			}
			id: "76EA017B12E8F01B"
			rewards: [{
				id: "477F18566EC6FB2A"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "3121BA0C9976AFCD"
				title: "能量存储"
				type: "checkmark"
			}]
			x: 3.0d
			y: 0.5d
		}
		{
			dependencies: ["76EA017B12E8F01B"]
			description: ["&d通用机械&f是最佳的能量存储模组之一,尤其在游戏前期.\\n\\n基础能量立方体配置简单、易于合成且存储量大.它还能升级,并可通过界面直接给物品充电!\\n\\n要了解能量立方体升级及模组详情,请前往&a&d通用机械&f&r任务线!"]
			id: "477B411F84342EEA"
			rewards: [
				{
					id: "039E00B83AAA154A"
					type: "xp"
					xp: 10
				}
				{
					id: "7E2E1F9ED0EEB289"
					item: "mekanism:steel_casing"
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "6F7ED193F8C1262A"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "rsquare"
			subtitle: "&d通用机械&f"
			tasks: [{
				id: "76378687551A79C3"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanism:basic_energy_cube"
							}
							{
								Count: 1b
								id: "mekanism:advanced_energy_cube"
							}
							{
								Count: 1b
								id: "mekanism:elite_energy_cube"
							}
							{
								Count: 1b
								id: "mekanism:ultimate_energy_cube"
							}
						]
					}
				}
				title: "能量立方体"
				type: "item"
			}]
			title: "&d通用机械&f的能量立方体"
			x: 3.0d
			y: 2.0d
		}
		{
			dependencies: ["76EA017B12E8F01B"]
			description: ["&dRF工具箱&f能源模组提供可存储能量的&9能量单元&r,这些多方块结构可自定义升级.\\n\\n你需要扳手来设定能量输入输出方向."]
			id: "05B6DB75AEC01187"
			rewards: [
				{
					id: "54A1A37A02201DB9"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "6D7BF04203FE0E60"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "rsquare"
			subtitle: "&dRF工具箱&f"
			tasks: [{
				id: "7CA8741CA073C1DA"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "rftoolspower:cell1"
							}
							{
								Count: 1b
								id: "rftoolspower:cell2"
							}
							{
								Count: 1b
								id: "rftoolspower:cell3"
							}
						]
					}
				}
				title: "能源核心"
				type: "item"
			}]
			title: "来自&dRF工具箱&f的能量单元"
			x: 4.0d
			y: 1.5d
		}
		{
			dependencies: ["76EA017B12E8F01B"]
			description: ["&9集成动力&r提供简易能量存储系统.这些电池甚至能在工作台中合并以提升总容量!"]
			id: "72EA25D05C46D39A"
			rewards: [
				{
					id: "59A5734689019951"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "5D6EEC3641B95A9E"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "rsquare"
			subtitle: "&d动态联合/集成动力&f"
			tasks: [{
				id: "460A67B09BD45CF4"
				item: {
					Count: 1
					id: "integrateddynamics:energy_battery"
					tag: {
						energy: 0
					}
				}
				type: "item"
			}]
			title: "能量电池"
			x: 2.0d
			y: 1.5d
		}
		{
			description: ["总不能永远靠煤炭供电!若资源充足,是时候升级能源系统了!\\n\\n这些方案通常需要简单配置,但产能够支撑你长期发展."]
			icon: "powah:dielectric_casing"
			id: "5E41363F9AE243F3"
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "5A8F474927CC1E80"
				title: "中期能源方案"
				type: "checkmark"
			}]
			title: "中期能源方案"
			x: -2.0d
			y: 3.0d
		}
		{
			dependencies: ["5E41363F9AE243F3"]
			description: ["所谓'西瓜发电'说的就是这个.&d通用机械&f的&9&a燃气发电机&f&r通过注入&9乙烯&r(由&a西瓜块&f制成)可产生大量电力.\\n\\n制作&d乙烯&r需先用&e粉碎机&r将有机材料粉碎成&e&a生物燃料&f&r,通常使用&a西瓜块&f!随后将其泵入&d&a加压反应室&f&r(简称PRC).\\n\\n&a加压反应室&f需要水、&a生物燃料&f和氢气来制造乙烯.氢气可通过&9&a电解分离器&f&r分解水获得.\\n\\n乙烯产出后,将其泵入&a燃气发电机&f即可发电!"]
			id: "0AEC181F5E21A299"
			min_width: 300
			rewards: [
				{
					count: 8
					id: "7B9C300DF6442A1D"
					item: "minecraft:melon_slice"
					type: "item"
				}
				{
					id: "742C6774E1F40C96"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "542C81754D93237B"
					table_id: 4196188979167302596L
					type: "random"
				}
			]
			shape: "rsquare"
			subtitle: "西瓜的&a力量&f"
			tasks: [{
				id: "2ADE2B487637FA3F"
				item: "mekanismgenerators:gas_burning_generator"
				type: "item"
			}]
			title: "&e&d通用机械&f的&d&a燃气发电机&f"
			x: -2.0d
			y: 4.5d
		}
		{
			dependencies: ["5E41363F9AE243F3"]
			description: ["信不信由你,&dPowah&r模组有些绝妙的...能量方案.\\n\\n记得查看&cPowah&r章节了解更多!"]
			icon: "powah:book"
			id: "35ABB0DEE70DF7FD"
			min_width: 300
			rewards: [{
				id: "2374BE0128AF2ED8"
				type: "xp"
				xp: 10
			}]
			shape: "rsquare"
			tasks: [{
				id: "215F37D96DF88189"
				title: "能源升级套件"
				type: "checkmark"
			}]
			title: "需要更多&9能量"
			x: -1.0d
			y: 4.0d
		}
		{
			description: ["若需巨量能源,可从&9中期能源&r方案扩展:扩建&e&d极限反应堆&f&r,将&9热能发电机&r升级至硝化级,尽情发挥.\\n\\n&9&d通用机械&f&r还有难以超越的终极能源方案.\\n\\n&d&a湮灭反应堆&f&r产能高达200MRF/t,建造方法请参阅&a&d通用机械&f:&r &d进阶&r章节!"]
			icon: "mekanismgenerators:fusion_reactor_controller"
			id: "3BDB94F17765EE77"
			min_width: 300
			rewards: [{
				id: "45BE4BD6F9CCB927"
				type: "xp"
				xp: 10
			}]
			shape: "hexagon"
			size: 1.5d
			subtitle: "超乎所需的能量"
			tasks: [{
				id: "03A4FFC4CFCA5DB7"
				title: "终局能源方案"
				type: "checkmark"
			}]
			x: -3.5d
			y: 4.5d
		}
		{
			dependencies: ["4EEAB467C722ECE7"]
			description: ["要让管道传输更多能量,需进行升级.\\n\\n按住Shift右键管道设为抽取模式后,可手持升级件右键安装,或空手右键通过GUI操作.\\n\\n设置过滤器至少需要&9高级管道升级&r."]
			hide_until_deps_visible: true
			id: "1409C17773B6A131"
			rewards: [
				{
					id: "3B6AEDA1AF0D94C2"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "2C5A013A4D52E1A4"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			tasks: [{
				id: "098F1B932F851616"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "pipez:basic_upgrade"
							}
							{
								Count: 1b
								id: "pipez:improved_upgrade"
							}
							{
								Count: 1b
								id: "pipez:advanced_upgrade"
							}
							{
								Count: 1b
								id: "pipez:ultimate_upgrade"
							}
						]
					}
				}
				title: "管道升级组件"
				type: "item"
			}]
			title: "管道升级指南"
			x: -11.5d
			y: 0.5d
		}
		{
			dependencies: ["477B411F84342EEA"]
			description: [
				"&9通用机械&r的&a输导矩阵&r是最顶级的能量存储方案.\\n\\n若想寻找游戏中最强大的能量存储设备,请查阅&a通用机械&r模组的&d进阶&r章节."
				""
				"{image:atm:textures/questpics/mek/mek_induction_matrix_small.png width:125 height:150 align:1}"
			]
			id: "682034C680FDEDC2"
			min_width: 300
			rewards: [{
				id: "4897BCA1A6B1D623"
				type: "xp"
				xp: 10
			}]
			shape: "gear"
			size: 1.5d
			tasks: [{
				id: "4FC7F1A8F2BDEF93"
				title: "需要更多存储空间？"
				type: "checkmark"
			}]
			x: 3.0d
			y: 3.5d
		}
		{
			dependencies: ["1F81EA5E45424308"]
			description: ["&9&d通量网络&f&r是一个旨在满足你所有无线能源需求的模组.\\n\\n&a强化&f本身不具备发电功能,但可以存储并无线传输能量,甚至跨维度传输.它还能为你物品栏中的物品充能.\\n\\n使用这个模组,你甚至可以在飞行时给喷气背包充能.是不是很酷？\\n\\n要开始使用这个模组,你需要一些通量粉尘.前往基岩层,在基岩块上扔些红石.然后在悬浮的红石正上方放置一个黑曜石块,最后左键点击黑曜石."]
			id: "35CC898E0E49FE58"
			min_width: 300
			rewards: [
				{
					count: 2
					id: "16D31D3D55F77046"
					item: "fluxnetworks:flux_dust"
					random_bonus: 2
					type: "item"
				}
				{
					id: "19CFBA244CF82C53"
					type: "xp"
					xp: 10
				}
				{
					exclude_from_claim_all: true
					id: "5CB51B7DDAE608F1"
					table_id: 487623848494439020L
					type: "loot"
				}
			]
			shape: "pentagon"
			size: 1.25d
			subtitle: "终极无线能源解决方案"
			tasks: [{
				count: 4L
				id: "0F0CBAF912DE462F"
				item: "fluxnetworks:flux_dust"
				type: "item"
			}]
			title: "&d通量网络&f"
			x: -9.0d
			y: 2.5d
		}
		{
			dependencies: ["35CC898E0E49FE58"]
			description: ["在这个模组中,你需要&a&a通量核心&f&r和&a通量块&r来制作网络的核心部件.每种都做一些吧!"]
			id: "1BE26A00A420DAE3"
			rewards: [
				{
					count: 4
					id: "4FA376B34C2BF04A"
					item: "fluxnetworks:flux_dust"
					random_bonus: 4
					type: "item"
				}
				{
					id: "3C2BBFC2A36E722F"
					type: "xp"
					xp: 100
				}
			]
			tasks: [
				{
					count: 4L
					id: "5E760075B279BA71"
					item: "fluxnetworks:flux_core"
					type: "item"
				}
				{
					id: "01B3CDAA69D1FA2F"
					item: "fluxnetworks:flux_block"
					type: "item"
				}
			]
			title: "核心合成材料"
			x: -9.0d
			y: 4.0d
		}
		{
			dependencies: ["1BE26A00A420DAE3"]
			description: ["启动通量网络所需的第一个物品是&9&a通量接入点&f&r.\\n\\n接入点用于从它所连接的方块中'抽取'能量.除了小型缓冲外,接入点本身不存储能量,所以不用担心它会耗尽你所有的能源.\\n\\n建议将接入点放置在能量存储方块上,比如能量立方体.它可以连接到电缆、管道或任何发电机器的输出端.\\n\\n要学习如何建立你的第一个网络,请查看下一个任务!"]
			id: "5F078A574A783B02"
			rewards: [
				{
					id: "46F6946231FB28A0"
					item: "fluxnetworks:flux_block"
					type: "item"
				}
				{
					id: "615F6356856A3371"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "116BF6D4838A099B"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			tasks: [{
				id: "00B1C30B3E10D332"
				item: "fluxnetworks:flux_plug"
				type: "item"
			}]
			title: "建立你的网络"
			x: -9.0d
			y: 5.0d
		}
		{
			dependencies: ["5F078A574A783B02"]
			description: ["右键点击你的接入点,你会看到通量网络界面.在右上角点击+按钮创建你的第一个网络.创建网络需要设置密码,但你还可以选择颜色!\\n\\n在这里,你可以进入&a网络选择&f标签页,在接入点上激活你的网络.如果接入点连接了能源,你现在就可以使用&a通量接出点&f在系统中的任何地方获取能量!\\n\\n专业提示:如果你想用不同的能源为系统的不同部分供电,可以创建多个网络!"]
			icon: "fluxnetworks:flux_configurator"
			id: "36DEA17CBB696CC7"
			min_width: 300
			rewards: [
				{
					count: 4
					id: "2AC42E9338722803"
					item: "fluxnetworks:flux_dust"
					type: "item"
				}
				{
					id: "739E3CA0422DB9F6"
					type: "xp"
					xp: 100
				}
			]
			subtitle: "你现在是网络管理员了!"
			tasks: [{
				id: "2D0212699F99459F"
				title: "我的第一个能源网络"
				type: "checkmark"
			}]
			title: "我的第一个网络"
			x: -9.0d
			y: 6.0d
		}
		{
			dependencies: ["36DEA17CBB696CC7"]
			description: ["设置好接入点后,我们现在可以从网络中获取能量了.&9&a通量接出点&f&r正是为此而生.它将网络中的能量导向所连接的方块,包括管道、电缆,或直接连接机器!\\n\\n将接出点放置在你想供电的机器或方块上后,&a右击&f它,在&a网络选择&f标签页中选择你的网络.和接入点一样,你可以调整能量提取量、优先级等设置."]
			id: "56B6ABF3D6EA0D84"
			rewards: [
				{
					id: "34498E12E8224864"
					item: "fluxnetworks:flux_core"
					random_bonus: 2
					type: "item"
				}
				{
					id: "175D04352F2F6D97"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "36DA068B9758EC94"
					table_id: 487623848494439020L
					type: "random"
				}
			]
			shape: "diamond"
			tasks: [{
				id: "026DE584C4DF393C"
				item: "fluxnetworks:flux_point"
				type: "item"
			}]
			title: "接入网络能源"
			x: -9.5d
			y: 7.0d
		}
		{
			dependencies: ["36DEA17CBB696CC7"]
			description: ["&d通量网络&f确实提供了为你的网络存储能量的方法!\\n\\n这些存储设备可以容纳大量能量,并且可以通过升级来存储更多!"]
			id: "79AD74A863EA43CB"
			rewards: [
				{
					id: "6A142D7F593E9DBA"
					type: "xp"
					xp: 100
				}
				{
					exclude_from_claim_all: true
					id: "69236C65B496550C"
					table_id: 4196188979167302596L
					type: "loot"
				}
			]
			shape: "diamond"
			subtitle: "能量存储"
			tasks: [{
				id: "3C574DDC057353DF"
				item: "fluxnetworks:basic_flux_storage"
				type: "item"
			}]
			title: "通量存储"
			x: -8.5d
			y: 7.0d
		}
		{
			dependencies: ["36DEA17CBB696CC7"]
			description: [
				"&a右键点击&f功能性通量网络方块会显示此界面.\\n\\n每个插头或接入点都可自定义名称、优先级和能量传输上限,实现对整个系统的精确控制.\\n\\n&a突破限制&r选项可无视设定的传输上限.\\n\\n其他标签页主要显示网络统计数据!"
				""
				"{image:atm:textures/questpics/flux/flux_ui.png width:125 height:150 align:1}"
			]
			icon: "fluxnetworks:admin_configurator"
			id: "2EB7784D5296F410"
			min_width: 350
			optional: true
			rewards: [{
				id: "64751B6E2F284585"
				type: "xp"
				xp: 10
			}]
			shape: "diamond"
			tasks: [{
				id: "3D721B55E8D33FE8"
				title: "&d通量网络&f控制界面"
				type: "checkmark"
			}]
			title: "&d通量网络&f控制界面"
			x: -10.0d
			y: 6.0d
		}
		{
			dependencies: ["36DEA17CBB696CC7"]
			description: [
				"&d通量网络&f还能实现跨维度无线充电功能!\\n\\n在能源系统中安装插头后,需要制作&9通量控制器&r并放置.\\n\\n&a右键点击&f打开界面,进入「无线充电」标签页.在此可选择需要保持充电的物品栏区域,最后启用底部的无线充电开关并点击应用即可激活."
				""
				"{image:atm:textures/questpics/flux/wireless_ui.png width:125 height:150 align:1}"
			]
			id: "27A4FA38992448A0"
			min_width: 300
			rewards: [
				{
					exclude_from_claim_all: true
					id: "4D553DA55A83294D"
					table_id: 4196188979167302596L
					type: "random"
				}
				{
					id: "37E08FCD1F0A3D26"
					type: "xp"
					xp: 100
				}
			]
			shape: "diamond"
			tasks: [{
				id: "013BD44E5E82802A"
				item: "fluxnetworks:flux_controller"
				type: "item"
			}]
			title: "无线充能系统"
			x: -9.0d
			y: 7.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包编写."
				"由于所有&eAllTheMods&r整合包都采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,此任务不得用于任何非官方发布的公开整合包中."
				""
				""
				""
				"此任务默认隐藏,如果你看到这个提示,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "2FD7D264024592D7"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "330BFC92D5AEAD82"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "6AC2C9C3DA38E390"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: -3.5d
			y: -1.5d
		}
	]
	title: "全能源"
}
