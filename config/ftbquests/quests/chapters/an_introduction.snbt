{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "an_introduction"
	group: "1DA67E79B40AB130"
	icon: {
		Count: 1
		id: "gtceu:iron_hammer"
		tag: {
			Damage: 0
			GT.Tool: {
				Damage: 0
			}
		}
	}
	id: "415BA265E2C00859"
	images: [{
		alpha: 150
		height: 5.0d
		image: "gtceu:textures/gui/icon/gregtech_logo.png"
		rotation: 0.0d
		width: 5.0d
		x: 0.1071428571428541d
		y: 0.6607142857142847d
	}]
	order_index: 0
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"格雷科技中的能源以&a能量单元&f(EU)计量."
				""
				"能源利用率以EU/t为单位测量."
				""
				"您可以在GT中生产EU能源,也可以选择使用Powah等其他模组生产能源并转换为EU(后续详述)."
			]
			id: "4F086B3CF27D5C1A"
			rewards: [{
				id: "1DE4C6A260259E85"
				type: "xp"
				xp: 10
			}]
			size: 1.5d
			subtitle: "为安全起见,请仔细阅读"
			tasks: [{
				id: "7B1C0F47F1D43B20"
				title: "能源概念"
				type: "checkmark"
			}]
			title: "能源系统"
			x: 0.0d
			y: 0.55d
		}
		{
			dependencies: ["4F086B3CF27D5C1A"]
			description: [
				"机器和电缆/电线都有特定的&l电压等级限制&l"
				""
				"给机器过高电压会导致&c&l爆炸!"
				""
				"给电线或电缆过高电压会导致烧毁"
				""
				"低压(LV)为32伏特(32 EU/t/A)."
				"中压(MV)为128伏特."
				"高压(HV)为512伏特."
				"以此类推."
			]
			id: "66D86E1EDEBF542B"
			rewards: [{
				id: "43A69E40F972B3FA"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "0F0F3C716F921B64"
				title: "电压"
				type: "checkmark"
			}]
			x: -1.5d
			y: 0.5d
		}
		{
			dependencies: ["4F086B3CF27D5C1A"]
			description: [
				"安培数就像能量包(EU/t),可以有不同的电压等级(LV, MV, HV等)."
				""
				"接收额外安培数的机器不会爆炸."
				""
				"若超过额定安培数,电缆和电线会烧毁."
			]
			id: "6635E4C76260C4CB"
			rewards: [{
				id: "285CB9EE3130A1B3"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "58DC240BC9900646"
				title: "电流"
				type: "checkmark"
			}]
			x: 1.5d
			y: 0.5d
		}
		{
			dependencies: [
				"66D86E1EDEBF542B"
				"6635E4C76260C4CB"
			]
			description: [
				"电线和电缆存在能量损耗,会以热能形式损失部分传输能量."
				""
				"您可以通过用橡胶包裹电线制成电缆来减少部分损耗."
				""
				"超导体例外!它们没有能量损耗."
			]
			id: "459787E9F1029CC6"
			rewards: [{
				id: "7F31B0D07E8F2421"
				type: "xp"
				xp: 10
			}]
			subtitle: "一格即为一米"
			tasks: [{
				id: "4CC5C3088A4F5C7D"
				title: "电压损耗"
				type: "checkmark"
			}]
			x: 0.0d
			y: 2.0d
		}
		{
			dependencies: ["66D86E1EDEBF542B"]
			description: [
				"你可能已经注意到,4安培的低压电(32 EU/t/安培)相当于128 EU/t."
				""
				"1安培的中压电同样也是128 EU/t."
				""
				"它们能互相转换吗？某种程度上可以!"
				""
				"&a电力高炉&f(EBF)就是典型例子——两个低压能源仓各可接受2安培低压电,从而使电力高炉能够处理中压配方!这是格雷科技中电压等级进阶的关键机制."
				""
				"或者你也可以制作一个电压变压器."
			]
			id: "33590A79C5C554C8"
			rewards: [{
				id: "0145C8D4665C08CA"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4BE4C9994786C1D0"
				title: "电压转换"
				type: "checkmark"
			}]
			x: 0.0d
			y: -1.0d
		}
		{
			dependencies: [
				"66D86E1EDEBF542B"
				"4F086B3CF27D5C1A"
				"6635E4C76260C4CB"
				"459787E9F1029CC6"
				"33590A79C5C554C8"
			]
			description: [
				"工具可由多种材料制成,最初级的可能是铁制工具!"
				""
				"与原版工具类似,工具可在铁砧上用相同材料修复."
				""
				"&a锤子&r用于手工锻造金属板,这是制作进阶工具的必备材料!"
			]
			hide_dependency_lines: true
			id: "790F509BAA15A68E"
			rewards: [{
				exclude_from_claim_all: true
				id: "1C46CF2729A805AC"
				table_id: 487623848494439020L
				type: "loot"
			}]
			subtitle: "入门指南"
			tasks: [{
				id: "30443BA4C089AA8B"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:tools/hammers"
					}
				}
				title: "GT锤"
				type: "item"
			}]
			title: "工具"
			x: -2.0d
			y: -4.5d
		}
		{
			dependencies: ["790F509BAA15A68E"]
			description: [
				"&a扳手&r不仅是合成工具,更是格雷科技机器的采矿工具!&a右键点击&f设定机器输出面,潜行+&a右键&f旋转机器朝向."
				""
				"&a锉刀&r是纯合成工具."
				""
				"&a锯子&r也是合成工具,但还能将1根原木分解为6块木板!"
			]
			id: "5D3C8198D9756004"
			rewards: [{
				exclude_from_claim_all: true
				id: "5FD9DE6BC79E6ADB"
				table_id: 487623848494439020L
				type: "loot"
			}]
			tasks: [
				{
					id: "491DC7AF3356B0AE"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/wrenches"
						}
					}
					title: "GT扳手"
					type: "item"
				}
				{
					id: "3CEA3285F2CD5680"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/files"
						}
					}
					title: "GT锉刀"
					type: "item"
				}
				{
					id: "5270CD1653E1BAC6"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/saws"
						}
					}
					title: "GT锯"
					type: "item"
				}
			]
			title: "实用工具"
			x: 0.0d
			y: -2.5d
		}
		{
			dependencies: ["5D3C8198D9756004"]
			description: [
				"&a螺丝刀&r用于机器特殊配置和覆盖板安装."
				""
				"&a钢丝钳&r能通过&a右键点击&f电线/电缆来启用或禁用与其他方块的连接."
				""
				"&a研钵&r可将物品研磨成粉尘."
				""
				"&a小刀&r同样是合成工具..."
				""
				"&e软锤&r通过右键点击暂停/恢复机器运作."
				""
				"&e撬棍&r能&a移除&f格雷科技机器上的覆盖板."
				""
				"&e镰刀&r可自动收割并补种区域内作物,还是造型炫酷的武器!"
				""
				"&e疏通器&r能清理机器内淤积的流体&c&l[未实装]。"
				""
				"&e屠宰刀&r自带&a抢夺III&f效果，前期获取皮革的利器！"
			]
			id: "17BA6F1E5179DB8C"
			min_width: 400
			rewards: [{
				exclude_from_claim_all: true
				id: "50B36A2E833872D2"
				table_id: 487623848494439020L
				type: "loot"
			}]
			subtitle: "这些工具终将派上用场"
			tasks: [
				{
					id: "15389D4D64D19ACA"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/screwdrivers"
						}
					}
					title: "GT螺丝刀"
					type: "item"
				}
				{
					id: "3B9FECB49AF6F450"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/wire_cutters"
						}
					}
					title: "GT剪线钳"
					type: "item"
				}
				{
					id: "4EEBD6ABA3D55379"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/mortars"
						}
					}
					title: "GT研钵"
					type: "item"
				}
				{
					id: "33B208F02F05BF09"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/knives"
						}
					}
					title: "GT小刀"
					type: "item"
				}
				{
					id: "65A04FD7D3F264DD"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/mallets"
						}
					}
					title: "GT木槌"
					type: "item"
				}
				{
					id: "1B670B474AD80852"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/crowbars"
						}
					}
					title: "GT撬棍"
					type: "item"
				}
				{
					id: "5F8C9C7E6DFA0E6C"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/scythes"
						}
					}
					title: "GT镰刀"
					type: "item"
				}
				{
					id: "25E539125A1C5D69"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/plungers"
						}
					}
					title: "GT柱塞"
					type: "item"
				}
				{
					id: "465C43AE595A1A9F"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:tools/butchery_knives"
						}
					}
					title: "GT屠宰刀"
					type: "item"
				}
			]
			title: "全套工具"
			x: 2.0d
			y: -4.5d
		}
		{
			description: [
				"矿脉以每3区块间隔在不同高度生成."
				""
				"矿脉通常包含3种及以上矿物."
				""
				"所有格雷科技矿脉已从其他维度迁移至&a采矿维度&f."
			]
			disable_toast: true
			icon: "minecraft:grass_block"
			id: "072FA02152FBC5B1"
			invisible_until_tasks: 5
			rewards: [{
				id: "45B4971B15822AF8"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				dimension: "allthemodium:mining"
				id: "5E966121508B53EA"
				type: "dimension"
			}]
			title: "主世界矿脉分布"
			x: -5.5d
			y: 0.5d
		}
		{
			dependencies: ["244220A5D9F4C702"]
			icon: "gtceu:endstone_bauxite_ore"
			id: "68B6B946B6BA24CB"
			rewards: [{
				id: "1ECD3CFE7C276679"
				type: "xp"
				xp: 10
			}]
			subtitle: "铝的来源"
			tasks: [{
				id: "5FF14D712FF20F3A"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:endstone_bauxite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_bauxite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_ilmenite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_ilmenite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_aluminium_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_aluminium"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "铝土矿脉"
			x: 6.0d
			y: -4.5d
		}
		{
			dependencies: ["244220A5D9F4C702"]
			icon: "gtceu:endstone_magnetite_ore"
			id: "27E9F48972741701"
			rewards: [{
				id: "35A7B740B8F8F0E9"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "18CA627A460E3E4E"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:endstone_magnetite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_magnetite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_vanadium_magnetite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_vanadium_magnetite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_chromite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_chromite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_gold_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_gold"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "磁铁矿脉"
			x: 7.5d
			y: -3.5d
		}
		{
			dependencies: ["244220A5D9F4C702"]
			icon: "gtceu:endstone_naquadah_ore"
			id: "037BB7C486D2D360"
			rewards: [{
				id: "3CB43D21AEDA9957"
				type: "xp"
				xp: 10
			}]
			subtitle: "确实如此"
			tasks: [{
				id: "71DDF8155FD8FCAA"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:endstone_naquadah_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_naquadah"
							}
							{
								Count: 1b
								id: "gtceu:endstone_plutonium_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_plutonium"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "耐酸矿脉"
			x: 7.5d
			y: -1.5d
		}
		{
			dependencies: ["244220A5D9F4C702"]
			icon: "gtceu:endstone_pitchblende_ore"
			id: "3F69CB46311A37C8"
			rewards: [{
				id: "3FE3E20D6B31A717"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "125DE9E99E5DC392"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:endstone_pitchblende_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_pitchblende"
							}
							{
								Count: 1b
								id: "gtceu:endstone_uraninite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_uraninite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "沥青铀矿脉"
			x: 4.5d
			y: -3.5d
		}
		{
			dependencies: ["244220A5D9F4C702"]
			icon: "gtceu:endstone_scheelite_ore"
			id: "4300938A4E8AF937"
			rewards: [{
				id: "43B051AAEBD24976"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "70DEDB210CBD79D9"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:endstone_scheelite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_scheelite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_tungstate_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_tungstate"
							}
							{
								Count: 1b
								id: "gtceu:endstone_lithium_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_lithium"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "白钨矿脉"
			x: 4.5d
			y: -1.5d
		}
		{
			dependencies: ["244220A5D9F4C702"]
			icon: "gtceu:endstone_cooperite_ore"
			id: "0C78FB6EB275960B"
			rewards: [{
				id: "350EF34944578879"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "508A2B09B32E5244"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:endstone_bornite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_bornite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_cooperite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_cooperite"
							}
							{
								Count: 1b
								id: "gtceu:endstone_platinum_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_platinum"
							}
							{
								Count: 1b
								id: "gtceu:endstone_palladium_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_palladium"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "谢尔顿矿脉"
			x: 6.0d
			y: -0.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_goethite_ore"
			id: "5B64384E4CE27851"
			rewards: [{
				id: "12AC8012CA8B498E"
				type: "xp"
				xp: 10
			}]
			subtitle: "&a带状铁矿&f矿脉"
			tasks: [{
				id: "7FA60EE3F8F0C57B"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_goethite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_goethite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_yellow_limonite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_yellow_limonite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_hematite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_hematite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_gold_ore"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "针铁矿脉"
			x: 7.0d
			y: 1.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_beryllium_ore"
			id: "61B3730E93DD24DB"
			rewards: [{
				id: "68084D26CC1A15E5"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "083DE18D8C046344"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_beryllium_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_beryllium"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_emerald_ore"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "绿柱石矿脉"
			x: 8.0d
			y: 2.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_certus_quartz_ore"
			id: "1DFD4712153C1A55"
			rewards: [{
				id: "1F6361DEF1584BE6"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "3AC1607997CAAAAC"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_certus_quartz_ore"
							}
							{
								Count: 1b
								id: "gtceu:certus_quartz_gem"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_barite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_barite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "&a赛特斯石英&f矿脉"
			x: 8.0d
			y: 4.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_grossular_ore"
			id: "109E34AE0BE0F266"
			rewards: [{
				id: "10C50F3500300270"
				type: "xp"
				xp: 10
			}]
			subtitle: "锰的营养来源"
			tasks: [{
				id: "047722F89EBCC4DE"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_grossular_ore"
							}
							{
								Count: 1b
								id: "gtceu:grossular_gem"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_pyrolusite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_pyrolusite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_tantalite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_tantalite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "钙铝榴石矿脉"
			x: 7.0d
			y: 5.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_molybdenum_ore"
			id: "554389C781241743"
			rewards: [{
				id: "24E1D9FCDCD56E06"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "589F9B25E0FC4A24"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_wulfenite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_wulfenite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_molybdenite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_molybdenite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_molybdenum_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_molybdenum"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_powellite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_powellite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "钼矿脉"
			x: 6.0d
			y: 5.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_bastnasite_ore"
			id: "4F8E090CB349A681"
			rewards: [{
				id: "2D703261DBD7DAB0"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "5711092D4CD64197"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_bastnasite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_bastnasite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_neodymium_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_neodymium"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "氟碳铈矿脉"
			x: 5.0d
			y: 5.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_quartzite_ore"
			id: "2B5733BDAF83B2D4"
			rewards: [{
				id: "7FA4B674422ACD63"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "1791DBA7A318DE68"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_quartzite_ore"
							}
							{
								Count: 1b
								id: "gtceu:quartzite_gem"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "石英岩矿脉"
			x: 4.0d
			y: 4.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_redstone_ore"
			id: "354012CD62D346E5"
			rewards: [{
				id: "3967B8EC8A32C7FC"
				type: "xp"
				xp: 10
			}]
			subtitle: "每块原矿产出5倍红石!"
			tasks: [{
				id: "778C0EDD0752F465"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_redstone_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_redstone"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_ruby_ore"
							}
							{
								Count: 1b
								id: "gtceu:ruby_gem"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_cinnabar_ore"
							}
							{
								Count: 1b
								id: "gtceu:cinnabar_gem"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "红石矿脉"
			x: 4.0d
			y: 3.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_saltpeter_ore"
			id: "3F064B466CC4914B"
			rewards: [{
				id: "572A0B6B80423FD0"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6A623C45C3A556B6"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_saltpeter_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_saltpeter"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_diatomite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_diatomite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_electrotine_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_electrotine"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_alunite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_alunite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "硝石矿脉"
			x: 4.0d
			y: 2.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_sulfur_ore"
			id: "333D2A9F20B2E738"
			rewards: [{
				id: "54F48572B72DA35A"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "3A75356E8DE59132"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_sulfur_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_sulfur"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_pyrite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_pyrite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_sphalerite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_sphalerite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "硫磺矿脉"
			x: 5.0d
			y: 1.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_tetrahedrite_ore"
			id: "2A2ED3B4BE8C7E67"
			rewards: [{
				id: "0B829C830AD45B69"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "5DED626D70F7EE98"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_tetrahedrite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_tetrahedrite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_stibnite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_stibnite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "黝铜矿脉"
			x: 6.0d
			y: 1.5d
		}
		{
			dependencies: ["0D20644407244A60"]
			icon: "gtceu:netherrack_topaz_ore"
			id: "34E8D3AD37BB76C5"
			rewards: [{
				id: "736FC97FBC6CAC2D"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "556A070D9E9A67E5"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:netherrack_topaz_ore"
							}
							{
								Count: 1b
								id: "gtceu:topaz_gem"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_blue_topaz_ore"
							}
							{
								Count: 1b
								id: "gtceu:blue_topaz_gem"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_chalcocite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_chalcocite"
							}
							{
								Count: 1b
								id: "gtceu:netherrack_bornite_ore"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "黄玉矿脉"
			x: 8.0d
			y: 3.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:apatite_ore"
			id: "5E1FA88B5AC652DC"
			rewards: [{
				id: "05C9FCF2AF25361B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "044F6F66134EEFAB"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:apatite_ore"
							}
							{
								Count: 1b
								id: "gtceu:apatite_gem"
							}
							{
								Count: 1b
								id: "gtceu:tricalcium_phosphate_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_tricalcium_phosphate"
							}
							{
								Count: 1b
								id: "gtceu:pyrochlore_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_pyrochlore"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "磷灰石矿脉"
			x: -7.5d
			y: 0.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:cassiterite_ore"
			id: "30F16A6BD3C7F4FC"
			rewards: [{
				id: "26B09100349E2906"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "015B712B90E69E36"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:cassiterite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_cassiterite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "锡石矿脉"
			x: -8.0d
			y: 1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:chalcopyrite_ore"
			id: "6A0D1E9D534958B8"
			rewards: [
				{
					count: 2
					id: "3041712FFF09D1EF"
					item: "gtceu:realgar_gem"
					random_bonus: 2
					type: "item"
				}
				{
					id: "36A950CF20366A71"
					type: "xp"
					xp: 10
				}
			]
			tasks: [{
				id: "0D18011DF2D3F338"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:chalcopyrite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_chalcopyrite"
							}
							{
								Count: 1b
								id: "gtceu:zeolite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_zeolite"
							}
							{
								Count: 1b
								id: "gtceu:realgar_ore"
							}
							{
								Count: 1b
								id: "gtceu:realgar_gem"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "黄铜矿脉"
			x: -6.0d
			y: 2.0d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:galena_ore"
			id: "411759CC8C320C19"
			rewards: [{
				id: "0FCD9C5303E23494"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "1B94E7A224A87F29"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:galena_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_galena"
							}
							{
								Count: 1b
								id: "gtceu:silver_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_silver"
							}
							{
								Count: 1b
								id: "gtceu:lead_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_lead"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "方铅矿脉"
			x: -8.0d
			y: -0.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:garnet_sand_ore"
			id: "5E56B9758F8365B4"
			rewards: [{
				id: "0833FE78D518B0A7"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6E16C4B0E9B4CEC0"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:garnet_sand_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_garnet_sand"
							}
							{
								Count: 1b
								id: "gtceu:asbestos_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_asbestos"
							}
							{
								Count: 1b
								id: "gtceu:diatomite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_diatomite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "&a石榴石粉&f矿脉"
			x: -4.0d
			y: 2.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:red_garnet_ore"
			id: "51494B3023705E54"
			rewards: [{
				id: "1191D5C5A66B7E83"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "2E62FDEF3A6A88B8"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:red_garnet_ore"
							}
							{
								Count: 1b
								id: "gtceu:red_garnet_gem"
							}
							{
								Count: 1b
								id: "gtceu:yellow_garnet_ore"
							}
							{
								Count: 1b
								id: "gtceu:yellow_garnet_gem"
							}
							{
								Count: 1b
								id: "gtceu:amethyst_ore"
							}
							{
								Count: 1b
								id: "gtceu:opal_ore"
							}
							{
								Count: 1b
								id: "gtceu:opal_gem"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "石榴石矿脉"
			x: -4.0d
			y: 1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:goethite_ore"
			id: "53BBE717148C0B18"
			rewards: [{
				id: "156232EA5EF66602"
				type: "xp"
				xp: 10
			}]
			subtitle: "这些矿石富含铁元素"
			tasks: [{
				id: "6029CFFA9BD91661"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:goethite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_goethite"
							}
							{
								Count: 1b
								id: "gtceu:yellow_limonite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_yellow_limonite"
							}
							{
								Count: 1b
								id: "gtceu:hematite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_hematite"
							}
							{
								Count: 1b
								id: "gtceu:malachite_ore"
							}
							{
								Count: 1b
								id: "gtceu:malachite_gem"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "针铁矿脉"
			x: -3.0d
			y: 1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:soapstone_ore"
			id: "2A54256CA9A23BB3"
			rewards: [{
				id: "77541746B5FBDE66"
				type: "xp"
				xp: 10
			}]
			subtitle: "可用于制作润滑剂"
			tasks: [{
				id: "0AABC07011A622BB"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:soapstone_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_soapstone"
							}
							{
								Count: 1b
								id: "gtceu:talc_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_talc"
							}
							{
								Count: 1b
								id: "gtceu:glauconite_sand_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_glauconite_sand"
							}
							{
								Count: 1b
								id: "gtceu:pentlandite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_pentlandite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "皂石矿脉"
			x: -7.0d
			y: 2.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:magnetite_ore"
			id: "14276334DCF898D6"
			rewards: [{
				id: "4BB5AFDD639063BF"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "31A7661D828CB33C"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:magnetite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_magnetite"
							}
							{
								Count: 1b
								id: "gtceu:vanadium_magnetite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_vanadium_magnetite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "磁铁矿脉"
			x: -3.0d
			y: -0.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:basaltic_mineral_sand_ore"
			id: "0D47F491C27BBD5E"
			rewards: [{
				id: "4C16A3B319CCCB78"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "4730AB96AAD93282"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:basaltic_mineral_sand_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_basaltic_mineral_sand"
							}
							{
								Count: 1b
								id: "gtceu:granitic_mineral_sand_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_granitic_mineral_sand"
							}
							{
								Count: 1b
								id: "gtceu:fullers_earth_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_fullers_earth"
							}
							{
								Count: 1b
								id: "gtceu:gypsum_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_gypsum"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "矿物砂脉"
			x: -4.0d
			y: -0.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:garnierite_ore"
			id: "0BA5A0DDD4A5363C"
			rewards: [
				{
					id: "37391F97F144C822"
					item: "gtceu:cobaltite_ore"
					random_bonus: 2
					type: "item"
				}
				{
					id: "56FE674314510718"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "此处可能有钴矿"
			tasks: [{
				id: "0430C905BE3CA0CF"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:garnierite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_garnierite"
							}
							{
								Count: 1b
								id: "gtceu:cobaltite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_cobaltite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "硅镁镍矿脉"
			x: -3.5d
			y: -1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:rock_salt_ore"
			id: "4F2BBB75E5B5EAD8"
			rewards: [{
				id: "636D84C117F69866"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6962BAEF473C8C18"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:rock_salt_ore"
							}
							{
								Count: 1b
								id: "gtceu:rock_salt_gem"
							}
							{
								Count: 1b
								id: "gtceu:salt_ore"
							}
							{
								Count: 1b
								id: "gtceu:salt_gem"
							}
							{
								Count: 1b
								id: "gtceu:lepidolite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_lepidolite"
							}
							{
								Count: 1b
								id: "gtceu:spodumene_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_spodumene"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "盐矿脉"
			x: -7.0d
			y: -0.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:graphite_ore"
			id: "1BE2CB4684AF7DFB"
			rewards: [
				{
					count: 2
					id: "28E95708986F70B6"
					item: "minecraft:diamond"
					random_bonus: 2
					type: "item"
				}
				{
					id: "73372AD360DA6C63"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "可能藏有钻石"
			tasks: [{
				id: "7B87E500C6FB74B3"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:graphite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_graphite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "石墨矿脉"
			x: -4.5d
			y: -1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:lazurite_ore"
			id: "246DC099F4457712"
			rewards: [{
				id: "0E0F8446337E8132"
				type: "xp"
				xp: 10
			}]
			subtitle: "可找到青金石"
			tasks: [{
				id: "3A308744C31E3ED4"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:lazurite_ore"
							}
							{
								Count: 1b
								id: "gtceu:lazurite_gem"
							}
							{
								Count: 1b
								id: "gtceu:sodalite_ore"
							}
							{
								Count: 1b
								id: "gtceu:sodalite_gem"
							}
							{
								Count: 1b
								id: "gtceu:calcite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_calcite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "天青石矿脉"
			x: -7.5d
			y: -1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:kyanite_ore"
			id: "7AFEEC895FB293D6"
			rewards: [{
				id: "09CF1C0670D4F1DE"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "529FF9EA2C01FB6E"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:kyanite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_kyanite"
							}
							{
								Count: 1b
								id: "gtceu:mica_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_mica"
							}
							{
								Count: 1b
								id: "gtceu:bauxite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_bauxite"
							}
							{
								Count: 1b
								id: "gtceu:pollucite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_pollucite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "蓝晶石矿脉"
			x: -5.0d
			y: 2.0d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:grossular_ore"
			id: "03B842E4A2A6B2C9"
			rewards: [{
				id: "0677B3A82EDD9DF9"
				type: "xp"
				xp: 10
			}]
			subtitle: "富含锰元素"
			tasks: [{
				id: "0CD83CD8CF777CB9"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:grossular_ore"
							}
							{
								Count: 1b
								id: "gtceu:grossular_gem"
							}
							{
								Count: 1b
								id: "gtceu:spessartine_ore"
							}
							{
								Count: 1b
								id: "gtceu:spessartine_gem"
							}
							{
								Count: 1b
								id: "gtceu:pyrolusite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_pyrolusite"
							}
							{
								Count: 1b
								id: "gtceu:tantalite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_tantalite"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "钙铝榴石矿脉"
			x: -6.5d
			y: -1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:bentonite_ore"
			id: "2EEEA951011D523D"
			rewards: [{
				id: "10A9046D2C389088"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "69ED2BD3D6D30648"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:bentonite_ore"
							}
							{
								Count: 1b
								id: "gtceu:raw_bentonite"
							}
							{
								Count: 1b
								id: "gtceu:olivine_ore"
							}
							{
								Count: 1b
								id: "gtceu:olivine_gem"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "膨润土矿脉"
			x: -3.5d
			y: 0.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:cinnabar_ore"
			id: "4BACAC18E4B982DA"
			rewards: [{
				id: "424E53C9A54EEE1B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "7F7725AF8B2A5B13"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:cinnabar_gem"
							}
							{
								Count: 1b
								id: "alltheores:ruby"
							}
							{
								Count: 1b
								id: "gtceu:cinnabar_ore"
							}
							{
								Count: 1b
								id: "alltheores:ruby_ore"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "辰砂矿脉"
			x: -7.0d
			y: 1.5d
		}
		{
			dependencies: ["072FA02152FBC5B1"]
			icon: "gtceu:almandine_ore"
			id: "7255B9A6E1319DCA"
			rewards: [
				{
					id: "7CDE77676C561B5D"
					item: "gtceu:green_sapphire_ore"
					random_bonus: 2
					type: "item"
				}
				{
					id: "4C675CC7CFBF1E94"
					type: "xp"
					xp: 10
				}
			]
			subtitle: "可寻获蓝宝石"
			tasks: [{
				id: "558C014604ECC490"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "alltheores:sapphire"
							}
							{
								Count: 1b
								id: "gtceu:pyrope_gem"
							}
							{
								Count: 1b
								id: "gtceu:green_sapphire_gem"
							}
							{
								Count: 1b
								id: "gtceu:almandine_gem"
							}
							{
								Count: 1b
								id: "alltheores:sapphire_ore"
							}
							{
								Count: 1b
								id: "gtceu:pyrope_ore"
							}
							{
								Count: 1b
								id: "gtceu:green_sapphire_ore"
							}
							{
								Count: 1b
								id: "gtceu:almandine_ore"
							}
						]
					}
				}
				title: "有效矿石"
				type: "item"
			}]
			title: "铁铝榴石矿脉"
			x: -5.5d
			y: 3.0d
		}
		{
			dependencies: [
				"66D86E1EDEBF542B"
				"4F086B3CF27D5C1A"
				"6635E4C76260C4CB"
				"459787E9F1029CC6"
				"33590A79C5C554C8"
			]
			description: ["需要寻找矿石？制作这个探测器并放入任何机器的能量槽位充能即可使用."]
			hide_dependency_lines: true
			id: "55C47B868C5ECF54"
			rewards: [{
				count: 2
				id: "222FD5D0F6323720"
				item: "gtceu:realgar_gem"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "6F29F3E1EB2B25FD"
				item: "gtceu:prospector.lv"
				type: "item"
			}]
			x: -2.0d
			y: 5.5d
		}
		{
			dependencies: ["55C47B868C5ECF54"]
			description: ["寻找地下流体？这款勘探器配备流体探测模式,专为此类任务设计!"]
			id: "55F0472830CC6BF6"
			rewards: [{
				id: "6ECEEE679E6910B8"
				item: "gtceu:polyethylene_bucket"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "683EF14A8FA0F178"
				item: "gtceu:prospector.hv"
				type: "item"
			}]
			x: 0.0d
			y: 3.5d
		}
		{
			dependencies: ["55F0472830CC6BF6"]
			description: ["全能型探测器,大容量电池,超远距离矿石/流体探测仪."]
			id: "2E5EF984B9CE0CB9"
			rewards: [{
				count: 8
				id: "3D56EC2CF0D1E941"
				item: "gtceu:rhodium_dust"
				random_bonus: 8
				type: "item"
			}]
			tasks: [{
				id: "559399984984CE67"
				item: "gtceu:prospector.luv"
				type: "item"
			}]
			x: 2.0d
			y: 5.5d
		}
		{
			description: ["每3个区块就会发现新的矿脉!"]
			icon: "minecraft:netherrack"
			id: "0D20644407244A60"
			rewards: [{
				id: "0A80DD25662434D8"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				dimension: "allthemodium:mining"
				id: "500548E772861A58"
				type: "dimension"
			}]
			title: "下界层矿石"
			x: 6.0d
			y: 3.5d
		}
		{
			description: ["每隔3个区块分布,以防你还不知道这个规律."]
			icon: "minecraft:end_stone"
			id: "244220A5D9F4C702"
			rewards: [{
				id: "056FC6F179E3B43C"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				dimension: "allthemodium:mining"
				id: "540231448A4DE43B"
				type: "dimension"
			}]
			title: "末地层矿石"
			x: 6.0d
			y: -2.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若您看到此提示,说明正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "1C9F143427D1E68C"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "67C278CBE8BD4F0C"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "405256482E90B52E"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: 5.5d
		}
	]
	title: "入门指南"
}
