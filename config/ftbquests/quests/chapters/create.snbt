{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "create"
	group: "2B51AC12041E3F89"
	icon: "create:large_cogwheel"
	id: "100C477F4E63F20A"
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"&5&l欢迎来到机械动力!"
				""
				"机械动力是一款沉浸式科技模组,将现实主义以全新维度带入Minecraft!"
				""
				"若对任何方块或物品有疑问,按住W键即可查看惊艳的3D游戏内百科!"
			]
			id: "57A7A5C79389A96A"
			rewards: [{
				id: "1E5B06A1884F3578"
				item: "create:wrench"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				icon: "create:wrench"
				id: "695A0E0BC4B78AEC"
				title: "这将是该模组大部分物品和方块的主要合成材料."
				type: "checkmark"
			}]
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["57A7A5C79389A96A"]
			description: ["这将是本模组大多数物品和方块的主要原料."]
			id: "0F16498769DFB3B0"
			rewards: [{
				count: 32
				id: "50217BC8953EBD3E"
				item: "minecraft:andesite"
				type: "item"
			}]
			tasks: [{
				count: 32L
				id: "5D2B9139C49F5ABA"
				item: "create:andesite_alloy"
				type: "item"
			}]
			x: 0.0d
			y: -3.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: ["&n&5传动轴&r可在不改变齿轮转速的情况下传递旋转动力."]
			id: "5B36DE3826F26963"
			tasks: [{
				count: 16L
				id: "2F89C6C2CD322B51"
				item: "create:shaft"
				type: "item"
			}]
			x: -1.0d
			y: -5.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: [
				"&n&5齿轮&r不仅能传递旋转动力,还能使转速加倍或减半."
				""
				"转速加倍时系统承受的压力也会加倍."
			]
			id: "3E5E0C768038CDF4"
			rewards: [{
				count: 8
				id: "76537C95327D603D"
				item: "create:cogwheel"
				type: "item"
			}]
			tasks: [{
				count: 8L
				id: "68F2BD699AADA753"
				item: "create:cogwheel"
				type: "item"
			}]
			x: 1.0d
			y: -5.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: [
				"&n&5齿轮&r不仅能传递旋转动力,还能使转速加倍或减半."
				""
				"转速加倍时系统承受的压力也会加倍."
			]
			id: "23A9617F183C4EB1"
			rewards: [{
				count: 8
				id: "01DCE2A5A57DAEC5"
				item: "create:large_cogwheel"
				type: "item"
			}]
			tasks: [{
				count: 8L
				id: "3E2A4428BDE92738"
				item: "create:large_cogwheel"
				type: "item"
			}]
			x: 0.0d
			y: -6.0d
		}
		{
			dependencies: ["23A9617F183C4EB1"]
			description: [
				"&n&5&a水车&f&r是最基础的旋转动力生成方式之一.可通过并排放置来连接多个水车."
				""
				"使用不同原木还能改变其外观样式!"
			]
			id: "1AC0B7934F275EDE"
			rewards: [{
				id: "5118928E884676B3"
				item: "create:water_wheel"
				type: "item"
			}]
			tasks: [{
				count: 3L
				id: "35709DE2871F0CED"
				item: "create:water_wheel"
				type: "item"
			}]
			x: 0.0d
			y: -7.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5&a鼓风机&f&r旋转时可推动/吸引物品与实体.箭头指示其朝向,旋转方向决定是推动还是吸引."]
			id: "1E9B2D814F50A265"
			tasks: [{
				id: "1A4B96C7C821174E"
				item: "create:encased_fan"
				type: "item"
			}]
			x: 7.0d
			y: 1.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5变速箱&r能以90度角任意改变旋转力的传递方向."]
			id: "4285510271B5223D"
			tasks: [{
				id: "1840DB80F48921E2"
				item: "create:gearbox"
				type: "item"
			}]
			x: 6.5d
			y: 2.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["当接收到红石信号时,&n&5离合器&r将停止所有旋转运动."]
			id: "2BB3DB19D5EFC7E2"
			tasks: [{
				id: "54F14BE619292D1D"
				item: "create:clutch"
				type: "item"
			}]
			x: 6.0d
			y: 3.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["当接收到红石信号时,&n&5换向齿轮&r将反转旋转方向."]
			id: "67A46ED73E488CEE"
			tasks: [{
				id: "0CE491535E6FA50B"
				item: "create:gearshift"
				type: "item"
			}]
			x: 5.5d
			y: 4.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5链条传动装置&r可以朝任意方向与其他装置连接,只要它们相互接触.它们会将旋转力横向传递."]
			id: "7F8E50FA436DB4E6"
			tasks: [{
				id: "1674E46511507301"
				item: "create:encased_chain_drive"
				type: "item"
			}]
			x: 4.5d
			y: 5.0d
		}
		{
			dependencies: ["7F8E50FA436DB4E6"]
			description: [
				"无红石信号时,&n&5可调式链条传动装置&r功能与普通链条传动装置相同."
				""
				"当接收到红石信号且作为旋转力输入端时,会使连接的链条传动装置转速加倍."
				""
				"当接收到红石信号但非旋转力输入端时,会使连接的链条传动装置转速减半."
			]
			id: "54EC1C7FC1DA9107"
			tasks: [{
				id: "115F81D0A661F71F"
				item: "create:adjustable_chain_gearshift"
				type: "item"
			}]
			x: 4.5d
			y: 6.0d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5压力机&r可用于制造金属板或金属块."]
			id: "2D41B04C75FA02BC"
			tasks: [{
				id: "52136CB29F02E923"
				item: "create:mechanical_press"
				type: "item"
			}]
			x: 9.5d
			y: 0.0d
		}
		{
			dependencies: ["2D41B04C75FA02BC"]
			description: ["&n&5搅拌器&r可与混合盆配合使用进行合成."]
			id: "5DC892BA79EB52EC"
			tasks: [{
				id: "057EDA1FF37AE35E"
				item: "create:mechanical_mixer"
				type: "item"
			}]
			x: 10.0d
			y: 1.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: ["&n&5混合盆&r用于配方合成,主要包括&n&5&a动力辊压机&f&r和&n&5&a动力搅拌器&f&r."]
			id: "75CBB5BD8C1DFEA1"
			tasks: [{
				id: "56DBF4356117F26A"
				item: "create:basin"
				type: "item"
			}]
			x: 2.5d
			y: -3.5d
		}
		{
			dependencies: ["2D41B04C75FA02BC"]
			description: [
				"获取&n&5烈焰燃烧器&r需要先制作空烈焰燃烧器,然后对烈焰人右键点击."
				""
				"将其置于混合盆下方可为不同配方提供加热或超高温加热."
			]
			id: "1B182A30604655E2"
			tasks: [{
				id: "50FED4EED98A8648"
				item: "create:blaze_burner"
				type: "item"
			}]
			x: 8.0d
			y: 1.5d
		}
		{
			dependencies: ["47897D827C50629D"]
			description: ["&n&5&a动力活塞&f&r与普通活塞类似,可以推动方块,但你可以根据需要添加任意数量的延长杆."]
			id: "45EC31812FB9934D"
			tasks: [{
				id: "0BD1BA7949BB5855"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "create:mechanical_piston"
							}
							{
								Count: 1b
								id: "create:sticky_mechanical_piston"
							}
						]
					}
				}
				title: "&a动力活塞&f"
				type: "item"
			}]
			title: "&a动力活塞&f"
			x: 3.5d
			y: -2.0d
		}
		{
			dependencies: ["4F95F00ED78FBAB9"]
			description: ["&a速度表&f可显示当前连接齿轮的转速."]
			id: "29917E6196649F5D"
			tasks: [{
				id: "6A5BBAA13F1730F2"
				item: "create:speedometer"
				type: "item"
			}]
			x: -0.5d
			y: 3.5d
		}
		{
			dependencies: ["4F95F00ED78FBAB9"]
			description: ["&a应力表&f连接后可显示系统的应力负荷."]
			id: "48BE7DAC5082044D"
			tasks: [{
				id: "10CB6FCDDD3E38D4"
				item: "create:stressometer"
				type: "item"
			}]
			x: 0.5d
			y: 3.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: [
				"&n&5&a矿车装配机&f&r可放置在铁轨上.当接收到红石信号时,其上建造的任何结构都会被矿车拾取."
				""
				"所有方块必须用粘液球粘合,需要旋转力的方块会自动工作."
				""
				"要\"卸载\"矿车上的方块,只需关闭红石信号让矿车通过即可."
			]
			id: "09936F8FCEA72C5C"
			tasks: [{
				id: "15BA4109324B4A44"
				item: "create:cart_assembler"
				type: "item"
			}]
			x: -2.5d
			y: -3.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: ["&n&5&a机壳底盘&f&r功能类似&n&5&a强力胶&f&r,无需粘合即可直线连接方块."]
			id: "51EA6B1452883AB2"
			tasks: [{
				id: "4A80AC1DE4002191"
				item: "create:linear_chassis"
				type: "item"
			}]
			x: 1.0d
			y: -1.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: ["&n&5&a旋转底盘&f&r功能类似&n&5&a强力胶&f&r,无需粘合即可在侧面直线连接方块."]
			id: "459BA85E48B343AE"
			tasks: [{
				id: "3598B8EDD5834F92"
				item: "create:radial_chassis"
				type: "item"
			}]
			x: -1.0d
			y: -1.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: ["与&n&5&a旋转底盘&f&r组合可制作高应力容量的强力风车."]
			id: "76CBFA38D021AC95"
			tasks: [{
				id: "3652E21F6AD6D33F"
				item: "create:white_sail"
				type: "item"
			}]
			x: 2.0d
			y: -4.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5钻头&r会破坏前方的任何方块.若连接有容器,物品将被自动存储."]
			id: "3029E1E133B91ED8"
			tasks: [{
				id: "072131C19C185896"
				item: "create:mechanical_drill"
				type: "item"
			}]
			x: 3.0d
			y: 3.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5锯木机&r可砍伐前方的树木,也可用作锯木厂.若连接有容器,物品将被自动存储."]
			id: "72DCE154E1714890"
			tasks: [{
				id: "36C1D75275CE2AAB"
				item: "create:mechanical_saw"
				type: "item"
			}]
			x: 2.5d
			y: 2.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5部署器&r可用于放置物品/方块,或使用剑类物品进行攻击."]
			id: "3314FBC4FEAE1D08"
			tasks: [{
				id: "79D2F12337D1F637"
				item: "create:deployer"
				type: "item"
			}]
			x: 3.5d
			y: 4.5d
		}
		{
			dependencies: ["48EA6D9923E38B71"]
			description: [
				"&n&5便携式接口&r需要成对使用.一个需固定放置,另一个需安装在移动实体(如矿车)上."
				""
				"当两个接口相对时,它们会建立连接并相互传输物品."
			]
			id: "134BC365E77C6DB8"
			tasks: [{
				id: "2D54A1DB29903A0A"
				item: "create:portable_storage_interface"
				type: "item"
			}]
			x: 13.5d
			y: 4.0d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5收割机&r会收割它经过的所有作物.如果连接了容器,物品将被存储其中."]
			id: "4C31649D721F76B5"
			tasks: [{
				id: "1357FF84B9B133C4"
				item: "create:mechanical_harvester"
				type: "item"
			}]
			x: 1.5d
			y: 0.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5犁地机&r会破坏所有非固体方块,将泥土转化为耕地并无伤弹射实体.如果连接了容器,物品将被存储其中."]
			id: "0759CA52EECF3B49"
			tasks: [{
				id: "7B9B459AAB9F6483"
				item: "create:mechanical_plough"
				type: "item"
			}]
			x: 2.0d
			y: 1.5d
		}
		{
			dependencies: ["57A7A5C79389A96A"]
			description: ["&n&5机械外壳&r是大多数方块的合成材料."]
			id: "17885C2DE986F1BD"
			tasks: [{
				id: "0341E5F9B7361D1F"
				item: "create:andesite_casing"
				type: "item"
			}]
			x: 4.5d
			y: 0.0d
		}
		{
			dependencies: ["1DA6B8B2DCC97809"]
			description: ["&n&5机械外壳&r是大多数方块的合成材料."]
			id: "48EA6D9923E38B71"
			tasks: [{
				id: "23E40F6D58658887"
				item: "create:brass_casing"
				type: "item"
			}]
			x: 12.0d
			y: 4.0d
		}
		{
			dependencies: ["2D41B04C75FA02BC"]
			description: ["&n&5机械外壳&r是大多数方块的合成材料."]
			id: "3F663416E824720C"
			tasks: [{
				id: "7FE62F264B4FA5DE"
				item: "create:copper_casing"
				type: "item"
			}]
			x: 11.0d
			y: 0.0d
		}
		{
			dependencies: ["4194397DFD0199C2"]
			description: ["&n&5机械臂&r是可以从仓库或传送带上取物,并将其放置到其他仓库、传送带或合成器中的机器.\\n\\n手持机械臂右键点击方块可设置输入/输出目标.\\n\\n手持机械臂左键点击方块可取消选择."]
			id: "3F2C1A81C17D2D67"
			tasks: [{
				id: "3EC06DE78E520CC9"
				item: "create:mechanical_arm"
				type: "item"
			}]
			x: 13.5d
			y: 2.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: ["&n&5漏斗&r可以从连接的容器中导入或导出物品."]
			id: "47A6769B6BF1A46D"
			tasks: [{
				id: "5CB1191F8F7BBAA4"
				item: "create:andesite_funnel"
				type: "item"
			}]
			x: 2.0d
			y: -2.5d
		}
		{
			dependencies: ["1DA6B8B2DCC97809"]
			description: ["&n&5漏斗&r可以从连接的容器中导入或导出物品."]
			id: "495B0CC178B4CFA9"
			tasks: [{
				id: "1C5B4D14826937BB"
				item: "create:brass_funnel"
				type: "item"
			}]
			x: 9.0d
			y: 3.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: ["&n&5隧道&r可放置在传送带上过滤通过的物品.相邻放置的隧道会自动连接."]
			id: "7C8CDD259495A31A"
			tasks: [{
				id: "07162DA2FD7B0E05"
				item: "create:andesite_tunnel"
				type: "item"
			}]
			x: -2.0d
			y: -2.5d
		}
		{
			dependencies: ["1DA6B8B2DCC97809"]
			description: ["&n&5隧道&r可放置在传送带上过滤通过的物品.相邻放置的隧道会自动连接."]
			id: "78656C89EEE80DB5"
			tasks: [{
				id: "688E14A6938EBEF7"
				item: "create:brass_tunnel"
				type: "item"
			}]
			x: 10.5d
			y: 5.0d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5仓库&r主要用于存储物品,配合注口使用."]
			id: "7F0D59EC1573FDC0"
			tasks: [{
				id: "7EFB9500BBDD745C"
				item: "create:depot"
				type: "item"
			}]
			x: 5.5d
			y: -1.0d
		}
		{
			dependencies: ["2D41B04C75FA02BC"]
			description: ["&n&5溜槽&r可用于从容器存取物品,或在传送带上放置/提取物品."]
			id: "3D2A03EB2B91E9C1"
			tasks: [{
				id: "105F8859D59964C4"
				item: "create:chute"
				type: "item"
			}]
			x: 9.5d
			y: -1.5d
		}
		{
			dependencies: ["57A7A5C79389A96A"]
			description: ["这副护目镜能让你更详细地观察机械装置.\\n\\n可显示转速和应力等数据."]
			id: "4F95F00ED78FBAB9"
			tasks: [{
				id: "6989058FD488CE64"
				item: "create:goggles"
				type: "item"
			}]
			x: 0.0d
			y: 2.0d
		}
		{
			dependencies: ["57A7A5C79389A96A"]
			description: ["可通过制作&a玫瑰水晶&f和&a砂纸&f来获得.\\n\\n可将水晶放在副手,或扔在地上后手持&a砂纸&f右键点击."]
			id: "610DEC4FC4FBB63E"
			rewards: [{
				id: "43C2760F8FDD84CA"
				item: {
					Count: 1
					id: "create:sand_paper"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			tasks: [{
				count: 8L
				id: "3BC40DF423178031"
				item: "create:polished_rose_quartz"
				type: "item"
			}]
			x: -2.5d
			y: 0.0d
		}
		{
			dependencies: ["1C2309DB4B890E71"]
			description: ["&n&5&a烈焰蛋糕&f&r用于为烈焰燃烧器提供超高温,以合成&a异彩化合物&f."]
			id: "4C77ABCD41383F32"
			tasks: [{
				id: "2E9A4E9BFFC10819"
				item: "create:blaze_cake"
				type: "item"
			}]
			x: 14.0d
			y: 0.0d
		}
		{
			dependencies: ["1DA6B8B2DCC97809"]
			description: ["&n&5合成器&r可以相互连接形成大型实体合成台.\\n\\n所有合成器的箭头最终必须指向同一个合成器才能完成配方.使用扳手右键点击可旋转箭头方向."]
			id: "4194397DFD0199C2"
			tasks: [{
				id: "65C6E83AF17405E0"
				item: "create:mechanical_crafter"
				type: "item"
			}]
			x: 12.0d
			y: 2.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5&a起重机取物器&f&r可安装在&a起重机杆&f上,当轴旋转时会带动滑车移动."]
			id: "75B14A09FE39EDDA"
			tasks: [{
				id: "7DA78C3A582368E2"
				item: "create:gantry_carriage"
				type: "item"
			}]
			x: 7.5d
			y: 0.5d
		}
		{
			dependencies: ["7F0D59EC1573FDC0"]
			description: ["&n&5&a弹射置物台&f&r可将物品或实体发射到指定位置."]
			id: "4EA4EDD5A7923F98"
			tasks: [{
				id: "34313AA5D327317C"
				item: "create:weighted_ejector"
				type: "item"
			}]
			x: 5.5d
			y: -2.0d
		}
		{
			dependencies: ["3D2A03EB2B91E9C1"]
			description: ["&n&5&a智能溜槽&f&r在基础溜槽功能上增加了堆叠数量和过滤等额外功能."]
			id: "7EEEEDD5FF31ACD3"
			tasks: [{
				id: "37EE09651ED58F6F"
				item: "create:smart_chute"
				type: "item"
			}]
			x: 9.5d
			y: -3.0d
		}
		{
			dependencies: ["57A7A5C79389A96A"]
			description: ["&a蓝图桌&f可以读取/写入结构图纸.\\n\\n用于复制建筑或与他人分享设计!"]
			id: "0CF69DBA9573A7B3"
			rewards: [{
				id: "35DCC6CB7C472F17"
				item: "create:schematic_and_quill"
				type: "item"
			}]
			tasks: [{
				id: "0FB1E9BD14FE8EF8"
				item: "create:schematic_table"
				type: "item"
			}]
			x: -1.5d
			y: 1.5d
		}
		{
			dependencies: ["0CF69DBA9573A7B3"]
			description: ["&a加农炮&f会根据图纸建造结构,从附近箱子获取材料并使用火药作为燃料."]
			id: "7D67058592EE5958"
			tasks: [{
				id: "5D989C876E9B8AA5"
				item: "create:schematicannon"
				type: "item"
			}]
			x: -2.5d
			y: 2.5d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			description: ["&n&5&a绳索滑轮&f&r可使方块上下移动,多个滑轮可通过粘液块连接."]
			id: "24E658BA47367A44"
			tasks: [{
				id: "0674C07C7DE332A9"
				item: "create:rope_pulley"
				type: "item"
			}]
			x: 4.5d
			y: -1.5d
		}
		{
			dependencies: ["3F663416E824720C"]
			description: ["&n&5喷口&r用于为物品注入液体.最佳做法是在其下方放置一个&n&5储罐&r来盛放物品."]
			id: "1C2309DB4B890E71"
			tasks: [{
				id: "6772D04457414991"
				item: "create:spout"
				type: "item"
			}]
			x: 12.5d
			y: 0.0d
		}
		{
			dependencies: ["3F663416E824720C"]
			description: ["&n&5&a软管滑轮&f&r是一种可以抽取或放置液体的泵."]
			id: "77382D4114E901CB"
			tasks: [{
				id: "739701B00C0F8A7B"
				item: "create:hose_pulley"
				type: "item"
			}]
			x: 12.0d
			y: 1.0d
		}
		{
			dependencies: ["17885C2DE986F1BD"]
			id: "47897D827C50629D"
			tasks: [{
				id: "473DA14538E1B94C"
				item: "create:piston_extension_pole"
				type: "item"
			}]
			x: 3.5d
			y: -1.0d
		}
		{
			dependencies: ["3F663416E824720C"]
			description: [
				"&n&5便携式接口&r需要成对使用.你需要将一个放置在世界中,另一个安装在移动实体上(比如矿车)."
				""
				"当两个接口相互对准时,它们会建立连接并互相传输液体."
			]
			id: "71B1B53A03A16296"
			tasks: [{
				id: "339D26EDFBB54047"
				item: "create:portable_fluid_interface"
				type: "item"
			}]
			x: 12.0d
			y: -1.0d
		}
		{
			dependencies: ["5DC892BA79EB52EC"]
			id: "1DA6B8B2DCC97809"
			tasks: [{
				icon: "create:brass_ingot"
				id: "07896B715ED0E04F"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:ingots/brass"
					}
				}
				title: "任意&a黄铜锭&f"
				type: "item"
			}]
			title: "黄铜"
			x: 10.5d
			y: 3.0d
		}
		{
			dependencies: ["2D41B04C75FA02BC"]
			description: ["&n&5&a强力胶&f&r可用于将方块粘合在一起,以便用活塞或其他方式移动它们."]
			id: "1F58AA51814C7568"
			tasks: [{
				id: "1A9BC10CDDA9FCF2"
				item: {
					Count: 1
					id: "create:super_glue"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 8.0d
			y: -1.5d
		}
		{
			dependencies: ["0F16498769DFB3B0"]
			description: [
				"&n&5传动带&r可以安装在两个&n&5传动轴&r之间来运输生物/物品,或单纯传递旋转能量."
				""
				"两个&n&5传动轴&r之间的角度必须为45度、90度或180度."
			]
			id: "13AEBC331F29BC3D"
			tasks: [{
				count: 4L
				id: "4D2A907287515990"
				item: "create:belt_connector"
				type: "item"
			}]
			x: -2.0d
			y: -4.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经明确授权,本任务不得用于任何非&eAllTheMods团队&r发布的公开整合包."
				""
				""
				""
				"此任务默认隐藏,若你看到此提示,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "0577F0923D8A72F5"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "0605DDB40EAB99C5"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "0F9E33F1BF4C38CF"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: 0.0d
			y: 5.0d
		}
	]
	title: "机械动力"
}
