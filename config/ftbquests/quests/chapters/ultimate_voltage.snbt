{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "ultimate_voltage"
	group: "1DA67E79B40AB130"
	icon: "gtceu:crystal_processor_mainframe"
	id: "754B57DEA4C711A5"
	images: [
		{
			height: 3.0d
			image: "gtceu:item/uv_solar_panel"
			rotation: 0.0d
			width: 3.0d
			x: 5.5d
			y: -8.5d
		}
		{
			height: 3.0d
			image: "gtceu:item/uv_electric_motor"
			rotation: 0.0d
			width: 3.0d
			x: 9.5d
			y: 1.5d
		}
		{
			height: 3.0d
			image: "gtceu:item/uv_sensor"
			rotation: 0.0d
			width: 3.0d
			x: 1.0d
			y: 1.5d
		}
		{
			height: 3.0d
			image: "gtceu:item/uv_emitter"
			rotation: 0.0d
			width: 3.0d
			x: -3.5d
			y: 1.5d
		}
		{
			height: 3.0d
			image: "gtceu:item/uv_voltage_coil"
			rotation: 0.0d
			width: 3.0d
			x: 5.0d
			y: 1.5d
		}
	]
	order_index: 9
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"我们已抵达&a晶体处理器&f阶段,现拥有UV级处理器!"
				""
				"虽已取得重大进展,但征程尚未结束.继续前进!"
			]
			id: "5D1B9EACB654BDF8"
			rewards: [{
				id: "7C3A3BB1E3F90D18"
				type: "xp"
				xp: 1000
			}]
			shape: "diamond"
			size: 1.5d
			subtitle: "施华洛世奇"
			tasks: [{
				id: "2D7446E656151002"
				item: "gtceu:crystal_processor_mainframe"
				type: "item"
			}]
			x: -8.0d
			y: -0.5d
		}
		{
			dependencies: [
				"47932104E994DBE9"
				"39615B8E568E0380"
			]
			description: [
				"两个字.终极电压."
				""
				"不过别担心,这之后我们还有更高一级.但能制造UV级机器将极大助力工厂建设并加速生产线."
			]
			id: "0A3F4D7A15E61B43"
			rewards: [{
				exclude_from_claim_all: true
				id: "5E29138ECD0EA5AE"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "忒修斯之壳"
			tasks: [{
				id: "37AB8A72600C0C72"
				item: "gtceu:uv_machine_hull"
				type: "item"
			}]
			x: -2.5d
			y: -6.0d
		}
		{
			dependencies: [
				"406C924820DE5473"
				"01D895369791881A"
			]
			description: [
				"这台高性能精密仪器以极速处理海量数据和复杂运算著称."
				""
				"我们的超级计算机终极形态!"
				""
				"但等等...大型主机去哪了？？"
			]
			id: "65A075160D46BEF7"
			rewards: [{
				exclude_from_claim_all: true
				id: "6BF32EF707257907"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			shape: "diamond"
			size: 1.5d
			subtitle: "至尊超算"
			tasks: [{
				id: "5D545D872BD5DC3B"
				item: "gtceu:wetware_processor_computer"
				type: "item"
			}]
			x: 10.0d
			y: -0.5d
		}
		{
			dependencies: [
				"406C924820DE5473"
				"5F7F05E4C3310724"
			]
			description: ["现在每次合成能获得2个ZPM处理器!重大突破让机器布局扩展更轻松!"]
			id: "01D895369791881A"
			rewards: [{
				exclude_from_claim_all: true
				id: "6D17392565F396EB"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "堆叠升级"
			tasks: [{
				id: "635887CB234211A8"
				item: "gtceu:wetware_processor_assembly"
				type: "item"
			}]
			x: 8.0d
			y: -0.5d
		}
		{
			dependencies: [
				"6335DC1E7517E940"
				"4FFD94248EDBE5FA"
			]
			description: [
				"这是我们终极处理器系列的首个产品!"
				""
				"虽然尚无法使用最佳配方(顶级配方可产出4个处理器),但下一章节解锁UV级&a电路组装机&f后就能实现."
			]
			id: "5F7F05E4C3310724"
			rewards: [{
				exclude_from_claim_all: true
				id: "66FCEA1D3183EE2B"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "&a终局&f的起点"
			tasks: [{
				id: "0DCE137263BB781D"
				item: "gtceu:wetware_processor"
				type: "item"
			}]
			x: 6.5d
			y: -0.5d
		}
		{
			dependencies: ["04A371896B1E0CEC"]
			description: ["这个组件造价昂贵,但后续发展必不可少.多个多方块结构都依赖ZPM力场发生器."]
			id: "0D259B9B93B39FAE"
			rewards: [{
				exclude_from_claim_all: true
				id: "10C11563C6E2EC52"
				table_id: 5732951907492768982L
				type: "loot"
			}]
			subtitle: "造价不菲,物有所值"
			tasks: [{
				id: "4987C63F3B87D1E6"
				item: "gtceu:zpm_field_generator"
				type: "item"
			}]
			x: -6.0d
			y: -7.5d
		}
		{
			dependencies: ["0EA56C514D26BA69"]
			description: [
				"&aZPM级导线&f在电流损耗方面并非最优选."
				""
				"但有了超导导线,ZPM机器和多方块结构的能量损耗将不复存在!"
			]
			id: "346A926E23840EF7"
			rewards: [{
				count: 8
				id: "5D266CC95D2D322A"
				item: "gtceu:uranium_rhodium_dinaquadide_single_wire"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "&aZPM超导体&f"
			tasks: [{
				id: "73A806A2C50F9EB2"
				item: "gtceu:uranium_rhodium_dinaquadide_single_wire"
				type: "item"
			}]
			x: -8.0d
			y: -7.5d
		}
		{
			description: [
				"又一款合金造就新一级超导体!"
				""
				"记住:超导体长距离传输EU时零损耗,是工厂布线的不二之选"
				""
				"制造此粉末需至少&cZPM&r级搅拌机"
			]
			id: "39D7F47A8C44D5AF"
			rewards: [{
				count: 6
				id: "2AFE0E431E6CD680"
				item: "gtceu:uranium_rhodium_dinaquadide_dust"
				random_bonus: 6
				type: "item"
			}]
			subtitle: "更多超导体"
			tasks: [{
				id: "7F73948EAC24D937"
				item: "gtceu:uranium_rhodium_dinaquadide_dust"
				type: "item"
			}]
			x: -7.0d
			y: -5.5d
		}
		{
			dependencies: ["5D1B9EACB654BDF8"]
			description: ["我们再次升级电弧炉线圈——这是冶炼金属合金的必要工序."]
			id: "16522A3A1E66C914"
			rewards: [{
				exclude_from_claim_all: true
				id: "27AD25E82DD54F1F"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "三钴线圈真不错"
			tasks: [{
				id: "6F0C524BD6D013D1"
				item: "gtceu:trinium_coil_block"
				type: "item"
			}]
			x: -8.0d
			y: -4.0d
		}
		{
			dependencies: [
				"39D7F47A8C44D5AF"
				"16522A3A1E66C914"
			]
			description: ["获得锭形态的超导体后,终于能加工成所需导线了!"]
			id: "0EA56C514D26BA69"
			rewards: [
				{
					count: 4
					id: "15A1112A2B7B648E"
					item: "gtceu:uranium_rhodium_dinaquadide_ingot"
					random_bonus: 4
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "75E52BC18CCD5CAA"
					table_id: 1818042308417101752L
					type: "loot"
				}
			]
			subtitle: "锭形态"
			tasks: [{
				id: "39D1F52918EB7CF8"
				item: "gtceu:uranium_rhodium_dinaquadide_ingot"
				type: "item"
			}]
			x: -8.0d
			y: -5.5d
		}
		{
			dependencies: ["6C30EEA91FB21A3B"]
			description: [
				"湿件印刷电路板完成了处理器所需的电路板序列."
				""
				"这是我们顶级处理器最关键的组件之一!"
			]
			id: "406C924820DE5473"
			rewards: [{
				exclude_from_claim_all: true
				id: "1302BAF34B2A6BF5"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "终章之舞"
			tasks: [{
				id: "0C0CB7B75FB75A98"
				item: "gtceu:wetware_printed_circuit_board"
				type: "item"
			}]
			x: 9.0d
			y: -2.0d
		}
		{
			dependencies: ["5041EDE3E75E1EDF"]
			description: ["这将是打造终极处理器所需的最后一块&a电路板&f."]
			id: "6C30EEA91FB21A3B"
			rewards: [{
				count: 2
				id: "43F1AB345AC5B209"
				item: "gtceu:wetware_circuit_board"
				random_bonus: 2
				type: "item"
			}]
			subtitle: "终极电路"
			tasks: [{
				id: "280DD834BBF2E057"
				item: "gtceu:wetware_circuit_board"
				type: "item"
			}]
			x: 9.0d
			y: -3.5d
		}
		{
			dependencies: [
				"406C924820DE5473"
				"773543FFEF631C5E"
				"5041EDE3E75E1EDF"
			]
			description: ["将有机物质以特定构型整合到无机组件中,可实现无限处理能力!"]
			id: "6335DC1E7517E940"
			rewards: [{
				count: 4
				id: "795B6E4161F757B5"
				item: "gtceu:neuro_processing_unit"
				random_bonus: 4
				type: "item"
			}]
			subtitle: "半有机体"
			tasks: [{
				id: "0E3259E08D4F3BEF"
				item: "gtceu:neuro_processing_unit"
				type: "item"
			}]
			x: 6.5d
			y: -2.0d
		}
		{
			dependencies: ["3AA49DDE45705233"]
			description: ["&a干细胞&f是生命的基础材料——所有功能特化细胞都由此生成"]
			id: "773543FFEF631C5E"
			rewards: [{
				count: 4
				id: "0B703691F3393181"
				item: "gtceu:stem_cells"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "基础构建单元"
			tasks: [{
				id: "25F1C01FA6CF8743"
				item: "gtceu:stem_cells"
				type: "item"
			}]
			x: 5.0d
			y: -2.0d
		}
		{
			dependencies: ["06242EEB80032F27"]
			description: [
				"务必小心处理这个桶."
				""
				"想必你也不希望因此感染."
			]
			id: "3AA49DDE45705233"
			rewards: [{
				id: "7232D203497DBE97"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:bacteria"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:bacteria"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "传染性物质"
			tasks: [{
				id: "758A46679EEA2C0B"
				item: "gtceu:bacteria_bucket"
				type: "item"
			}]
			x: 4.0d
			y: -2.0d
		}
		{
			dependencies: ["3F0C949C8F243AFD"]
			description: ["培养基是一种固体、液体或半固体物质,旨在通过细胞增殖过程支持微生物群体或如小立碗藓这类小型植物的生长."]
			id: "5041EDE3E75E1EDF"
			rewards: [{
				id: "5C9C341847D2CC3F"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:sterilized_growth_medium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:sterilized_growth_medium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "培育有机物"
			tasks: [{
				id: "0139D419A28AFB0C"
				item: "gtceu:sterilized_growth_medium_bucket"
				type: "item"
			}]
			x: 7.5d
			y: -3.5d
		}
		{
			dependencies: [
				"49E2D3DF6A9A5716"
				"1F442C8E82FB9EB8"
			]
			description: ["我们需要一些培养基来协助这条生产线,但目前只有培养基原液,尚未获得最终成品."]
			id: "71941882F9E2ADAC"
			rewards: [{
				id: "16FC77E719CEF0F4"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:raw_growth_medium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:raw_growth_medium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "实验室测试"
			tasks: [{
				id: "2C6EEA1EA636A1B6"
				item: "gtceu:raw_growth_medium_bucket"
				type: "item"
			}]
			x: 6.5d
			y: -4.5d
		}
		{
			dependencies: ["71941882F9E2ADAC"]
			description: ["制作任意IV级及以上的&a流体加热器&f,以便处理我们制备的&a培养基原液&f."]
			id: "3F0C949C8F243AFD"
			rewards: [{
				exclude_from_claim_all: true
				id: "71BB60012EB5F292"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "&a流体加热器&f"
			tasks: [{
				id: "525F7E756598E2C5"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						display: {
							Name: "{\"text\":\"任意&a流体加热器&f IV级或更高\"}"
						}
						items: [
							{
								Count: 1b
								id: "gtceu:iv_fluid_heater"
							}
							{
								Count: 1b
								id: "gtceu:luv_fluid_heater"
							}
							{
								Count: 1b
								id: "gtceu:zpm_fluid_heater"
							}
						]
					}
				}
				type: "item"
			}]
			x: 7.5d
			y: -4.5d
		}
		{
			dependencies: ["57F6323716A0ED24"]
			description: ["在遗传学中,诱变剂是指能永久改变生物体遗传物质(通常是DNA)的物理或化学制剂,从而使突变频率超过自然本底水平"]
			id: "1F442C8E82FB9EB8"
			rewards: [{
				id: "4E413FE2B9D92D10"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:mutagen"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:mutagen"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "忍者神龟既视感..."
			tasks: [{
				id: "33A9425D8B0E0F7E"
				item: "gtceu:mutagen_bucket"
				type: "item"
			}]
			x: 5.5d
			y: -4.5d
		}
		{
			dependencies: ["3F810160E8B58BD2"]
			description: ["琼脂是由多糖构成的凝胶状物质."]
			id: "49E2D3DF6A9A5716"
			rewards: [{
				count: 4
				id: "363B76EA748784C2"
				item: "gtceu:agar_dust"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "琼脂粉"
			tasks: [{
				id: "0D320F34CBBAD9E1"
				item: "gtceu:agar_dust"
				type: "item"
			}]
			x: 6.5d
			y: -6.0d
		}
		{
			dependencies: ["77A12A37D5A9B3A4"]
			description: ["明胶是从动物胶原蛋白中提取的蛋白质,通常来源于牛和猪."]
			id: "3F810160E8B58BD2"
			rewards: [{
				id: "6310C9D5F146814D"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:gelatin_mixture"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:gelatin_mixture"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "果冻...等等...好像哪里不对..."
			tasks: [{
				id: "7DF5163094EE01BE"
				item: "gtceu:gelatin_mixture_bucket"
				type: "item"
			}]
			x: 5.5d
			y: -6.0d
		}
		{
			dependencies: ["06242EEB80032F27"]
			description: ["胶原蛋白是构成人体皮肤、肌肉、骨骼、肌腱、韧带及其他结缔组织的主要基础材料."]
			id: "77A12A37D5A9B3A4"
			rewards: [{
				count: 4
				id: "790F3F53067EDCEC"
				item: "gtceu:collagen_dust"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "有机材料加工!"
			tasks: [{
				id: "54CD766F95D8BEB7"
				item: "gtceu:collagen_dust"
				type: "item"
			}]
			x: 4.5d
			y: -6.0d
		}
		{
			dependencies: ["33B8FDDBE3E95108"]
			description: ["船体外壳板材每提升一级都更具挑战性,但这是必要工序.尝试实现自动化生产,这样就能用UV级机器填满你的工厂!"]
			id: "47932104E994DBE9"
			rewards: [{
				count: 8
				id: "31B7BF0FE120B1C6"
				item: "gtceu:darmstadtium_plate"
				random_bonus: 16
				type: "item"
			}]
			subtitle: "这镝金属板太大了!"
			tasks: [{
				id: "67AFFB65A14B7EB3"
				item: "gtceu:darmstadtium_plate"
				type: "item"
			}]
			x: -2.5d
			y: -7.0d
		}
		{
			dependencies: ["4EEFECB9D741B371"]
			description: ["将钇钡铜氧材料加工成导线."]
			id: "39615B8E568E0380"
			rewards: [{
				count: 8
				id: "5A2107F598E16D92"
				item: "gtceu:yttrium_barium_cuprate_single_wire"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "钇(Y)到底发不发音？"
			tasks: [{
				id: "073DE59F75353E8B"
				item: "gtceu:yttrium_barium_cuprate_single_wire"
				type: "item"
			}]
			x: -2.5d
			y: -5.0d
		}
		{
			dependencies: ["0C3B3D7F82A6DB3D"]
			description: ["只需将液态金属加工成锭块.现在达到UV级后,我们拥有足够的速度和动力快速完成这一工序!"]
			id: "33B8FDDBE3E95108"
			rewards: [{
				count: 4
				id: "0E5B1E1BED18930F"
				item: "gtceu:darmstadtium_ingot"
				random_bonus: 8
				type: "item"
			}]
			shape: "pentagon"
			subtitle: "镝金属也能铸锭？"
			tasks: [{
				id: "137A99CC72FCDED4"
				item: "gtceu:darmstadtium_ingot"
				type: "item"
			}]
			x: -2.5d
			y: -8.0d
		}
		{
			dependencies: [
				"515D958C0F436BE8"
				"39615B8E568E0380"
				"54FEF677CA5E1C54"
				"2EDEBC5A44D8E531"
			]
			description: [
				"满足你所有UV级加工需求!"
				""
				"现在可以升级我们的多方块结构,使其以UV级加工速度运行!开始吧!"
			]
			id: "49109192F6D95AB4"
			rewards: [{
				exclude_from_claim_all: true
				id: "454F4BA3717F2C9D"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			shape: "gear"
			size: 1.5d
			subtitle: "UV级能源仓!"
			tasks: [{
				id: "6D19FDF5AACD74FD"
				item: "gtceu:uv_energy_input_hatch"
				type: "item"
			}]
			x: 1.0d
			y: -5.0d
		}
		{
			dependencies: ["49109192F6D95AB4"]
			description: ["什么？觉得UV能源仓还不够用？\\n\\n这款4A UV级能源仓给你真正澎湃的动力!"]
			id: "4DB3A860A30D7BF5"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "43F02BCFF646B3AE"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "需要更强动力？"
			tasks: [{
				id: "3CCC1ED871A58DCC"
				item: "gtceu:uv_energy_input_hatch_4a"
				type: "item"
			}]
			x: 1.0d
			y: -3.5d
		}
		{
			dependencies: [
				"39615B8E568E0380"
				"59F96E1680617EF0"
			]
			description: ["&a马达&f是构建其他UV级机器组件和外壳的重要基础零件."]
			id: "3D59BC5C2F5A073C"
			rewards: [{
				exclude_from_claim_all: true
				id: "45B22C21A1BB8073"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "更多马力!"
			tasks: [{
				id: "371494C22E66ED90"
				item: "gtceu:uv_electric_motor"
				type: "item"
			}]
			x: -2.5d
			y: -4.0d
		}
		{
			dependencies: [
				"3D59BC5C2F5A073C"
				"57630D2B5150F714"
			]
			description: [
				"UV级机械臂作为机器外挂时存在搬运物品数量限制."
				""
				"如果你在任何机器上使用机械臂外挂,试试这个升级版,绝对不会让你失望!"
			]
			hide_dependent_lines: true
			id: "0A8698ACCD58FADA"
			rewards: [{
				exclude_from_claim_all: true
				id: "75BFDF94F9491096"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "全自动搬运系统"
			tasks: [{
				id: "1B7904560D4DBB3C"
				item: "gtceu:uv_robot_arm"
				type: "item"
			}]
			x: -2.0d
			y: -3.0d
		}
		{
			dependencies: [
				"346A926E23840EF7"
				"2CB735807E38903F"
			]
			description: [
				"MKII型&a湮灭反应堆&f显著提升了&a湮灭反应堆&f的处理能力."
				""
				"虽然MKII型标注需要16个能源仓和16个输入输出仓,但并非强制要求.用较少仓体也能组成多方块结构.不过相比制造更多MKII型外壳,批量添加仓体反而更经济."
			]
			icon: "gtceu:zpm_fusion_reactor"
			id: "5E62E6F314843E1D"
			rewards: [{
				exclude_from_claim_all: true
				id: "080CC7CCD3172258"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			shape: "gear"
			size: 1.5d
			subtitle: "&a湮灭反应堆&f MKII"
			tasks: [
				{
					id: "467C52E14DB0DF89"
					item: "gtceu:zpm_fusion_reactor"
					type: "item"
				}
				{
					count: 48L
					id: "5114D966C1029C52"
					item: "gtceu:fusion_casing_mk2"
					type: "item"
				}
				{
					count: 4L
					id: "6EBAB4D8602B463A"
					item: "gtceu:fusion_coil"
					type: "item"
				}
				{
					count: 31L
					id: "6A4AB07796B318E1"
					item: { Count: 31, id: "gtceu:fusion_glass" }
					type: "item"
				}
			]
			x: -7.999999999999998d
			y: -9.75d
		}
		{
			dependencies: ["5D1B9EACB654BDF8"]
			description: ["ZPM级&a电路组装机&f将使我们能够制作除主机外的最终系列处理器."]
			id: "4FFD94248EDBE5FA"
			rewards: [{
				exclude_from_claim_all: true
				id: "44FF645D738A7987"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "终极前奏"
			tasks: [{
				id: "3A14FDEFAC495032"
				item: "gtceu:zpm_circuit_assembler"
				type: "item"
			}]
			x: 4.5d
			y: -0.5d
		}
		{
			dependencies: ["5D1B9EACB654BDF8"]
			description: [
				"64!你没听错,就是64!"
				""
				"这个舱口能让你的多方块结构同时运行64个并行进程!"
				""
				"如果你觉得超频已经很惊艳了,等你把这个大家伙装进多方块机器就知道了!"
			]
			id: "1B64314492605E47"
			optional: true
			rewards: [{
				id: "5A647E62180767FE"
				item: "gtceu:zpm_parallel_hatch"
				type: "item"
			}]
			subtitle: "并行世界"
			tasks: [{
				id: "4A3081071239F769"
				item: "gtceu:zpm_parallel_hatch"
				type: "item"
			}]
			x: -9.0d
			y: 1.5d
		}
		{
			dependencies: [
				"0FD475E5254E3AD2"
				"3FCF444190D02ADF"
				"5E62E6F314843E1D"
			]
			description: [
				"我们用了这么多&a湮灭反应堆&f!"
				""
				"但我告诉过你这个结构很重要.如果还没做的话,叠加&a湮灭反应堆&f的环会大有帮助."
			]
			id: "0C3B3D7F82A6DB3D"
			rewards: [{
				id: "3971ED2DC7E4D1C9"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:darmstadtium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:darmstadtium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "利用&a湮灭反应堆&f"
			tasks: [{
				id: "75F57FA0810D5D71"
				item: "gtceu:darmstadtium_bucket"
				type: "item"
			}]
			x: -2.5d
			y: -9.0d
		}
		{
			description: [
				"获取钌本身就很麻烦,但我们还要进一步加工!"
				""
				"相信我,绝对值得!"
			]
			id: "0FD475E5254E3AD2"
			rewards: [{
				id: "3DD67AA679D3594E"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:ruthenium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:ruthenium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "七道工序之后..."
			tasks: [{
				id: "07322A70E68867E2"
				item: "gtceu:ruthenium_bucket"
				type: "item"
			}]
			x: -3.5d
			y: -8.5d
		}
		{
			description: ["镓跑掉了,所以我们只剩砷,不过没关系,因为我们只需要砷!"]
			id: "3FCF444190D02ADF"
			rewards: [{
				id: "0EEBC6191B9920A4"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:arsenic"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:arsenic"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "砷的&a折返&f!"
			tasks: [{
				id: "14B3DB906EC6A871"
				item: "gtceu:arsenic_bucket"
				type: "item"
			}]
			x: -1.5d
			y: -8.5d
		}
		{
			dependencies: ["5D1B9EACB654BDF8"]
			description: ["&a水晶&f CPU应该已经在你的产线中成熟应用了.这只是让那些&a晶体CPU&f更进一步的升级步骤!\\n\\n你需要ZPM级的激光刻录机来完成这个步骤."]
			hide_dependency_lines: true
			id: "1216BAFF021FED25"
			rewards: [
				{
					count: 4
					id: "48B5917B5BE832FE"
					item: "gtceu:crystal_soc"
					random_bonus: 4
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "5BFADD171B16EBCC"
					table_id: 1818042308417101752L
					type: "loot"
				}
			]
			subtitle: "晶体芯片"
			tasks: [{
				id: "786A3FCF60B70292"
				item: "gtceu:crystal_soc"
				type: "item"
			}]
			x: -3.5d
			y: -6.0d
		}
		{
			dependencies: [
				"678BA300CED48E5E"
				"1216BAFF021FED25"
			]
			description: [
				"现在,把我们制作的&a钇钡铜氧合金&f螺栓和晶体SOC结合,就能得到最经济实惠的IV级处理器!!"
				""
				"记住,我们大多数\"大型\"多方块机器的控制器都需要IV级处理器来制作."
			]
			id: "06F55064A36274D2"
			rewards: [{
				id: "1CD4A6F85E058DD0"
				type: "xp"
				xp: 1000
			}]
			subtitle: "极致性价比"
			tasks: [{
				id: "1C193E204C8A2270"
				title: "最便宜的IV级处理器"
				type: "checkmark"
			}]
			x: -4.0d
			y: -7.0d
		}
		{
			dependencies: ["4EEFECB9D741B371"]
			description: ["让我们批量生产这些&a钇钡铜氧合金&f螺栓,它们能大幅降低IV级处理器的成本!"]
			id: "678BA300CED48E5E"
			rewards: [{
				count: 6
				id: "12D8045E4A45329F"
				item: "gtceu:yttrium_barium_cuprate_bolt"
				random_bonus: 12
				type: "item"
			}]
			subtitle: "&a降低成本&f"
			tasks: [{
				id: "59E57E67ABB107DF"
				item: "gtceu:yttrium_barium_cuprate_bolt"
				type: "item"
			}]
			x: -4.5d
			y: -6.0d
		}
		{
			dependencies: [
				"02CC3E13B8905603"
				"04508F8403F319D1"
			]
			description: [
				"这将是我们为能量舱制作的最后一个线圈!"
				""
				"虽然后面还有更高等级,但就能量舱使用的线圈而言,这是最后一个需要作为合成材料的等级了!"
			]
			id: "515D958C0F436BE8"
			rewards: [{
				exclude_from_claim_all: true
				id: "7B94C6B0D0BA4841"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "终极电压"
			tasks: [{
				id: "3028B3756C1D05D5"
				item: "gtceu:uv_voltage_coil"
				type: "item"
			}]
			x: 1.0d
			y: -8.0d
		}
		{
			dependencies: ["346A926E23840EF7"]
			description: [
				"把导线再精炼一次,就能得到ZPM力场发生器需要的精细导线."
				""
				"不幸的是我们需要大量精细导线,继续加工吧!"
			]
			id: "04A371896B1E0CEC"
			rewards: [{
				count: 6
				id: "34217503990BB714"
				item: "gtceu:fine_uranium_rhodium_dinaquadide_wire"
				random_bonus: 12
				type: "item"
			}]
			subtitle: "精益求精"
			tasks: [{
				id: "54B52A773F2CD6EC"
				item: "gtceu:fine_uranium_rhodium_dinaquadide_wire"
				type: "item"
			}]
			x: -7.0d
			y: -7.5d
		}
		{
			dependencies: [
				"4E384F9FEF629386"
				"28EB26056EF18C5F"
				"5E62E6F314843E1D"
			]
			description: [
				"钛与耐蚀钢的合金."
				""
				"现在我们有了一种能制作超强耐热EBF线圈的材料!"
				""
				"不过这个稍后再用,现在还用不上."
			]
			id: "74726A2DD4BBDA7B"
			rewards: [{
				id: "688D18E862547180"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:tritanium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:tritanium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "真·强度爆表"
			tasks: [{
				id: "7A649562119111CF"
				item: "gtceu:tritanium_bucket"
				type: "item"
			}]
			x: 2.5d
			y: -9.700000000000001d
		}
		{
			description: [
				"既然叫耐蚀钢,我希望这种元素确实够耐蚀."
				""
				"总之我们需要把它和钛混合.得到的材料将会非常坚固!"
			]
			id: "28EB26056EF18C5F"
			rewards: [{
				id: "4B93A5DEE035FD01"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:duranium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:duranium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "耐蚀？"
			tasks: [{
				id: "4227BFF6CC54F6F4"
				item: "gtceu:duranium_bucket"
				type: "item"
			}]
			x: 3.5d
			y: -10.5d
		}
		{
			description: [
				"多么强韧的液体啊!"
				""
				"不知道液态钛会不会像金属钛受热时那样呈现彩虹色..."
			]
			id: "4E384F9FEF629386"
			rewards: [{
				id: "6F1C23A533A3C505"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:titanium"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:titanium"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "强得离谱!"
			tasks: [{
				id: "3AE5FFBDA9846579"
				item: "gtceu:titanium_bucket"
				type: "item"
			}]
			x: 1.5d
			y: -10.5d
		}
		{
			dependencies: ["74726A2DD4BBDA7B"]
			description: [
				"将三钛固化,现在我们就有了一种多用途材料."
				""
				"相信我,我们要制作的很多组件都需要钛."
			]
			id: "02CC3E13B8905603"
			rewards: [{
				count: 4
				id: "7838ED6234554DDD"
				item: "gtceu:tritanium_ingot"
				random_bonus: 8
				type: "item"
			}]
			shape: "pentagon"
			subtitle: "固若金汤"
			tasks: [{
				id: "53CA597D2FC28F8C"
				item: "gtceu:tritanium_ingot"
				type: "item"
			}]
			x: 2.5d
			y: -8.700000000000001d
		}
		{
			dependencies: ["10DF2125B647379E"]
			description: [
				"我们是在制造变异的下水道生物吗？"
				""
				"我得去告诉斯普林特大师..."
			]
			id: "57F6323716A0ED24"
			rewards: [{
				id: "29EB06A6002C1F12"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:enriched_bacterial_sludge"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:enriched_bacterial_sludge"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "忍者神龟"
			tasks: [{
				id: "1A846F6C83B83EA7"
				item: "gtceu:enriched_bacterial_sludge_bucket"
				type: "item"
			}]
			x: 5.5d
			y: -3.5d
		}
		{
			dependencies: [
				"3AA49DDE45705233"
				"06242EEB80032F27"
			]
			description: ["我保证,这绝对是你不想触碰或沾到皮肤的东西..."]
			id: "10DF2125B647379E"
			rewards: [{
				id: "28EF601D8BC715F4"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:bacterial_sludge"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:bacterial_sludge"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			subtitle: "请勿触摸!"
			tasks: [
				{
					id: "58184E4DD25CA47F"
					item: "gtceu:bacterial_sludge_bucket"
					type: "item"
				}
				{
					id: "19AE1A9761C20844"
					item: "alltheores:uranium_dust"
					type: "item"
				}
			]
			x: 4.5d
			y: -4.0d
		}
		{
			dependencies: [
				"06FF1116AFABE2FE"
				"02CC3E13B8905603"
			]
			description: [
				"让我们为我们的装备库再添一间洁净室!不过这一间不仅能确保房间清洁,还能对环境进行灭菌处理."
				""
				"我们将能在洁净室中处理有机材料,再也不用担心污染物了!"
				""
				"&l&e注意:&r无菌洁净室无法运行普通洁净室配方!别把你原来的洁净室拆了!"
			]
			icon: "gtceu:sterilizing_filter_casing"
			id: "06242EEB80032F27"
			rewards: [{
				exclude_from_claim_all: true
				id: "4A3ECA6F42BB924D"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			shape: "gear"
			size: 1.0d
			subtitle: "二号洁净室"
			tasks: [
				{
					id: "5F34E42659E76FA1"
					item: "gtceu:sterilizing_filter_casing"
					type: "item"
				}
				{
					id: "4AF85FA060CCDAD4"
					item: "gtceu:cleanroom"
					type: "item"
				}
				{
					id: "429197E4705F8C59"
					item: "gtceu:plascrete"
					type: "item"
				}
				{
					id: "41315A48588AB4A2"
					item: "gtceu:cleanroom_glass"
					type: "item"
				}
			]
			x: 2.5d
			y: -6.0d
		}
		{
			dependencies: ["5D1B9EACB654BDF8"]
			description: [
				"黑光灯能发射紫外线光谱的光线.而紫外线恰好具有极强的杀菌消毒能力,能消灭细菌和其他污染物."
				""
				"我们的洁净室必须一尘不染,连一粒灰尘都不能有.但如果我们需要一个更洁净、更无菌的环境呢？"
			]
			hide_dependency_lines: true
			id: "06FF1116AFABE2FE"
			rewards: [{
				exclude_from_claim_all: true
				id: "1A92DD0B57233908"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "派对时间到!"
			tasks: [{
				id: "197856700ACC3967"
				item: "gtceu:blacklight"
				type: "item"
			}]
			x: 2.5d
			y: -3.5d
		}
		{
			dependencies: ["3D59BC5C2F5A073C"]
			description: ["紫外线活塞是紫外线机器和紫外线机械臂的必要组件."]
			id: "57630D2B5150F714"
			rewards: [{
				exclude_from_claim_all: true
				id: "2E61E90EFC3053FF"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			subtitle: "最佳活塞"
			tasks: [{
				id: "6F1E32A141D99E03"
				item: "gtceu:uv_electric_piston"
				type: "item"
			}]
			x: -3.0d
			y: -3.0d
		}
		{
			dependencies: ["2586C2C9D1EFB2DC"]
			description: ["ME物品输入口,功能类似接口.提供两行空间供您使用,可将物品链接到多方块结构中."]
			id: "1E8BE8BDE142941D"
			optional: true
			rewards: [{
				id: "139463DEBD01A30F"
				item: "gtceu:me_input_bus"
				type: "item"
			}]
			subtitle: "ME物品输入口"
			tasks: [{
				id: "359A810F8A1FE7D7"
				item: "gtceu:me_input_bus"
				type: "item"
			}]
			x: -8.0d
			y: 1.0d
		}
		{
			dependencies: ["2586C2C9D1EFB2DC"]
			description: ["ME物品输出口!让成品直接返回您的ME系统."]
			id: "3A27B65A0B4FFB5B"
			optional: true
			rewards: [{
				id: "45DFB07959C24CFC"
				item: "gtceu:me_output_bus"
				type: "item"
			}]
			subtitle: "ME物品输出口"
			tasks: [{
				id: "547CEDD62A451075"
				item: "gtceu:me_output_bus"
				type: "item"
			}]
			x: -8.0d
			y: 2.0d
		}
		{
			dependencies: ["2586C2C9D1EFB2DC"]
			description: ["ME流体输入口!为多方块结构中的工艺流程输入流体,甚至能在仓内保持库存!"]
			id: "043A424FDDA71692"
			optional: true
			rewards: [{
				id: "1966812A4729EA10"
				item: "gtceu:me_input_hatch"
				type: "item"
			}]
			subtitle: "ME流体输入口"
			tasks: [{
				id: "67DFF0D9CD927DBC"
				item: "gtceu:me_input_hatch"
				type: "item"
			}]
			x: -6.0d
			y: 1.0d
		}
		{
			dependencies: ["2586C2C9D1EFB2DC"]
			description: ["ME流体输出口!将产出的流体或副产品流体直接送回ME系统!绝对的高效设计!"]
			id: "6829D2769ACC1BDB"
			optional: true
			rewards: [{
				id: "3CAB3AB9AA4216FB"
				item: "gtceu:me_output_hatch"
				type: "item"
			}]
			subtitle: "ME流体输出口"
			tasks: [{
				id: "3A03D598E1B6007E"
				item: "gtceu:me_output_hatch"
				type: "item"
			}]
			x: -6.0d
			y: 2.0d
		}
		{
			dependencies: ["5D1B9EACB654BDF8"]
			description: [
				"将所有多方块结构连接到ME系统会消耗大量接口.再加上需要用覆盖板来调控资源,操作起来相当繁琐."
				""
				"别担心!这些仓口和总线将以您意想不到的方式帮您优化所有多方块结构!想象一下集成的ME功能吧!"
				""
				"这就是我们提供的解决方案!它们将成为您多方块结构的绝佳工具!甚至配有可编程芯片,能为特定生产线指定专用ME仓口/总线!"
			]
			id: "2586C2C9D1EFB2DC"
			rewards: [{
				id: "2F807BA8758A4ED5"
				type: "xp"
				xp: 1000
			}]
			subtitle: "用于多方块结构的ME仓口"
			tasks: [{
				id: "4E3E4CA01C64C9E2"
				title: "ME接口仓"
				type: "checkmark"
			}]
			title: "ME总线和仓口"
			x: -7.0d
			y: 1.5d
		}
		{
			dependencies: ["1B64314492605E47"]
			description: [
				"先前我们制作了&a大型化学反应釜&f,但那个版本无法接收并行控制仓口."
				""
				"而这个进阶版&a大型化学反应釜&f可以接收并行控制仓口,现在您能同时运行多个工艺流程了."
				""
				"这将彻底改变我们的化工生产线!"
			]
			icon: "gtceu:advanced_large_chemical_reactor"
			id: "13417E27790B9AB2"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "67893AFA75DB6DB9"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			shape: "gear"
			subtitle: "高级LCR"
			tasks: [
				{
					id: "2EF9FCC2A1E61FC7"
					item: "gtceu:advanced_large_chemical_reactor"
					type: "item"
				}
				{
					count: 12L
					id: "60A9E200F4F324EA"
					item: "gtceu:rtm_alloy_coil_block"
					type: "item"
				}
				{
					count: 15L
					id: "154AEA7A726F6D6F"
					item: "gtceu:ptfe_pipe_casing"
					type: "item"
				}
				{
					count: 62L
					id: "0395D55EFEBE8005"
					item: "gtceu:inert_machine_casing"
					type: "item"
				}
				{
					id: "3178FDFBEA274B84"
					item: "gtceu:zpm_parallel_hatch"
					type: "item"
				}
			]
			x: -9.0d
			y: 3.0d
		}
		{
			description: [
				"正如前文所述,合金的复杂度将持续提升,这在意料之中."
				""
				"而这种特殊合金能用来制造某些重要组件!"
			]
			id: "4EEFECB9D741B371"
			rewards: [{
				count: 4
				id: "17CCDD1027ED3B23"
				item: "gtceu:yttrium_barium_cuprate_ingot"
				random_bonus: 8
				type: "item"
			}]
			subtitle: "更复杂的合金"
			tasks: [{
				id: "6D6CAEC798AB5C3B"
				item: "gtceu:yttrium_barium_cuprate_ingot"
				type: "item"
			}]
			x: -4.0d
			y: -5.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods官方团队&r或&2社区贡献者&r为AllTheMods整合包编写."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若您看到此提示,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "5CCB34969AF38175"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "0B8410A78CA31E87"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "3E3975575FE59087"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -9.5d
			y: -0.5d
		}
		{
			dependencies: [
				"5D0FA7878B033938"
				"16522A3A1E66C914"
			]
			description: ["我曾困惑于必须用聚变反应制造Naquadria,没想到居然可以不用聚变就能造出来!"]
			id: "2CB735807E38903F"
			rewards: [
				{
					count: 8
					id: "368A24C1EBC28E65"
					item: "gtceu:naquadria_ingot"
					random_bonus: 16
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "614A3FD46B1297E5"
					table_id: 1818042308417101752L
					type: "loot"
				}
			]
			tasks: [{
				id: "4872647CCF87A557"
				item: "gtceu:naquadria_ingot"
				type: "item"
			}]
			x: -9.5d
			y: -7.5d
		}
		{
			dependencies: ["4BC7E522FF98B292"]
			description: ["继续离心分离以获取粉尘!\\n\\n别忘了处理你的废料!"]
			id: "5D0FA7878B033938"
			rewards: [{
				count: 30
				id: "1624A34BA1C1EAE9"
				item: "gtceu:naquadria_sulfate_dust"
				type: "item"
			}]
			tasks: [{
				count: 6L
				id: "42E495DA47D6FB62"
				item: "gtceu:naquadria_sulfate_dust"
				type: "item"
			}]
			x: -10.5d
			y: -7.0d
		}
		{
			dependencies: ["618D703190FAEC6D"]
			description: ["加入硫酸开始金属固化"]
			id: "4BC7E522FF98B292"
			rewards: [{
				id: "03CB835E108C7879"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:acidic_naquadria_solution"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:acidic_naquadria_solution"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "492D659AE35EFB93"
				item: "gtceu:acidic_naquadria_solution_bucket"
				type: "item"
			}]
			x: -10.0d
			y: -6.0d
		}
		{
			dependencies: ["134536FFE3E9C6E7"]
			description: ["离心分离杂质"]
			id: "618D703190FAEC6D"
			rewards: [{
				id: "6CFB0B527E679D51"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:naquadria_solution"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:naquadria_solution"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "6FA2C2317D635F04"
				item: "gtceu:naquadria_solution_bucket"
				type: "item"
			}]
			x: -9.5d
			y: -5.0d
		}
		{
			description: ["终于,NaqLine的另一半!"]
			id: "134536FFE3E9C6E7"
			rewards: [{
				id: "159EE3A0F3DF834A"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							Amount: 16000
							FluidName: "gtceu:impure_naquadria_solution"
							capacity: 16000
						}
					}
					id: "evilcraft:dark_tank"
					tag: {
						Fluid: {
							Amount: 16000
							FluidName: "gtceu:impure_naquadria_solution"
						}
						capacity: 16000
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "611A678ABFE17EA6"
				item: "gtceu:impure_naquadria_solution_bucket"
				type: "item"
			}]
			x: -9.0d
			y: -4.0d
		}
		{
			dependencies: ["59F96E1680617EF0"]
			description: ["网络交换机(需在连接的HPCAs上安装HPCA桥接组件)可输出比单个HPCA生成量更大的CWU.\\n\\n上次测试时可连接16个HPCAs,输出576 CWU &a每刻&f!"]
			icon: "gtceu:network_switch"
			id: "54FEF677CA5E1C54"
			rewards: [{
				exclude_from_claim_all: true
				id: "4AE197A6DF4177E7"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			tasks: [
				{
					id: "19D1C2DFDB18DB4E"
					item: "gtceu:network_switch"
					type: "item"
				}
				{
					count: 7L
					id: "58B40CD10A8BA973"
					item: { Count: 7, id: "gtceu:computer_casing" }
					type: "item"
				}
				{
					id: "1CE228529968F01E"
					item: "gtceu:advanced_computer_casing"
					type: "item"
				}
				{
					count: 2L
					id: "04D8194AE9277D88"
					item: { Count: 2, id: "gtceu:computation_receiver_hatch" }
					type: "item"
				}
				{
					id: "1D61129C818AC555"
					item: "gtceu:computation_transmitter_hatch"
					type: "item"
				}
			]
			x: -2.5d
			y: -1.5d
		}
		{
			dependencies: ["54FEF677CA5E1C54"]
			description: ["让HPCAs协同工作的必要组件.\\n\\n安装此组件后,每个HPCA最多可获取36 CWU."]
			id: "38DA9B7E6BD18CCA"
			rewards: [{
				exclude_from_claim_all: true
				id: "6A482B47F61E32C6"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			tasks: [{
				id: "40C8852CA1B85078"
				item: "gtceu:hpca_bridge_component"
				type: "item"
			}]
			x: -0.5d
			y: -1.5d
		}
		{
			dependencies: [
				"5D1B9EACB654BDF8"
				"0D259B9B93B39FAE"
			]
			description: ["如何突破16 CWU上限？答案就是这个方块!\\n\\n3个该方块配合6个主动冷却组件,可使单个HPCA达到48 CWU上限.\\n\\n别忘了准备PCB冷却液!"]
			id: "59F96E1680617EF0"
			rewards: [{
				exclude_from_claim_all: true
				id: "6DF364F9EB7C41CC"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			tasks: [{
				id: "092CA789AFE254B1"
				item: "gtceu:hpca_advanced_computation_component"
				type: "item"
			}]
			x: -6.0d
			y: -3.0d
		}
		{
			dependencies: [
				"0A3F4D7A15E61B43"
				"0A8698ACCD58FADA"
			]
			description: ["即便有了前期的多方块结构,我们仍需制作单方块机器.唉,但为了进步,这是必要的妥协!"]
			id: "04508F8403F319D1"
			rewards: [{
				exclude_from_claim_all: true
				id: "4215DF48488C1181"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			tasks: [{
				id: "0C97AC2B5CFB18A7"
				item: "gtceu:uv_assembler"
				type: "item"
			}]
			x: -0.5d
			y: -6.0d
		}
		{
			dependencies: ["4DB3A860A30D7BF5"]
			description: ["当你需要更强大动力时!"]
			id: "5F3113B405B48849"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "2D7C54A7ED517761"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			tasks: [{
				id: "51E20B36C11B6582"
				item: "gtceu:uv_energy_input_hatch_16a"
				type: "item"
			}]
			x: 1.0d
			y: -2.5d
		}
		{
			dependencies: ["406C924820DE5473"]
			description: ["接近终局了,这将助你研究次世代物品.\\n\\n存储技术的突破让我们能在微型模块中储存海量数据,欢呼吧!"]
			hide_dependent_lines: true
			id: "2EDEBC5A44D8E531"
			rewards: [{
				exclude_from_claim_all: true
				id: "5FB19F977F0F3E12"
				table_id: 1818042308417101752L
				type: "loot"
			}]
			tasks: [{
				id: "25AA82A5DC763402"
				item: {
					Count: 1
					id: "gtceu:data_module"
					tag: { }
				}
				type: "item"
			}]
			x: 10.0d
			y: -2.0d
		}
		{
			dependencies: ["5E62E6F314843E1D"]
			description: ["Mk2 &a湮灭反应堆&f可生产镅、&a氡气&f、&a钚-241&f和铟(额外产出鐽和三钛).\\n\\n还能制造氮等离子体、氧等离子体和氩等离子体!\\n\\n我们很快会重访氧等离子体和镅."]
			id: "5D08AFFAA4A85E39"
			rewards: [{
				id: "7367679FA8D65D16"
				type: "xp"
				xp: 1000
			}]
			tasks: [{
				id: "1393164467061CC0"
				title: "其他Mk2产品"
				type: "checkmark"
			}]
			title: "其他Mk2产物"
			x: -6.5d
			y: -8.5d
		}
	]
	title: "终极电压"
}
