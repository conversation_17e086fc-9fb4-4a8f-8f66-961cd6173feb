{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "low_voltage"
	group: "1DA67E79B40AB130"
	icon: "gtceu:basic_electronic_circuit"
	id: "37A28F4697946CB4"
	order_index: 2
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: ["升级机器将解锁新配方并提升处理速度\\n\\n这称为超频,若不想超频(例如为了节能)可在机器GUI中配置\\n\\n超频会使处理速度翻倍但耗电量增至四倍——总体意味着能耗翻倍"]
			id: "5C1BB559DD83E88F"
			rewards: [
				{
					count: 8
					id: "63DE356D2C74A10A"
					item: "gtceu:tin_single_cable"
					random_bonus: 4
					type: "item"
				}
				{
					count: 8
					id: "02B5C3D33312FDA2"
					item: "gtceu:copper_single_wire"
					random_bonus: 4
					type: "item"
				}
			]
			size: 1.5d
			subtitle: "欢迎来到低压阶段"
			tasks: [{
				id: "08EA6CE798092CE9"
				item: "gtceu:basic_electronic_circuit"
				type: "item"
			}]
			x: -2.5d
			y: 0.0d
		}
		{
			dependencies: [
				"11D16434F78D2C2C"
				"7480A00B82C5DDB5"
			]
			description: [
				"你一直记得批量合成物品对吧？"
				""
				"这次先放你一马,就当你有好好操作"
			]
			id: "4D6885EFA4EE272F"
			rewards: [
				{
					count: 2
					id: "39993B4D19452515"
					item: "gtceu:basic_electronic_circuit"
					random_bonus: 4
					type: "item"
				}
				{
					count: 2
					id: "5C954CCCF1770A9A"
					item: "gtceu:diode"
					random_bonus: 6
					type: "item"
				}
			]
			size: 1.5d
			subtitle: "向中压阶段进发!"
			tasks: [{
				id: "312B4A8B539DCD5D"
				item: "gtceu:good_electronic_circuit"
				type: "item"
			}]
			x: 7.5d
			y: -0.8999999999999999d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"线材轧机可将锭加工成两倍长度的1x线材"
				""
				"同时解锁更便宜的精制线材配方"
			]
			id: "2A26032DC9C8CFD6"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "6E917EAC6D910F41"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			shape: "diamond"
			subtitle: "经济型线材"
			tasks: [{
				id: "570680D6E9DF6FA7"
				item: "gtceu:lv_wiremill"
				type: "item"
			}]
			x: -4.0d
			y: 3.5d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"这台机器能轻松将锭加工成板材"
				""
				"板材可进一步加工成箔片"
			]
			id: "1906C5D1C80035E4"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "611C4B513D3D1ADF"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			shape: "diamond"
			subtitle: "经济型板材"
			tasks: [{
				id: "2077EC584FD16863"
				item: "gtceu:lv_bender"
				type: "item"
			}]
			x: -3.0d
			y: 3.5d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"这个大家伙解锁了更经济的橡胶合成配方!"
				""
				"用1个硫粉+9个生橡胶浆就能制成相当于9个橡胶锭的流体形态"
			]
			id: "620C406CC24F179C"
			rewards: [{
				exclude_from_claim_all: true
				id: "52F68D22D0F72D31"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			subtitle: "橡胶多多益善"
			tasks: [{
				id: "52A1256EE8999962"
				item: "gtceu:lv_chemical_reactor"
				type: "item"
			}]
			x: 0.5d
			y: 3.5d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["该机器还使用&a铸模&f将流体塑造成不同形态"]
			icon: "gtceu:lv_fluid_solidifier"
			id: "78C51E9B7B8315F6"
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "3D8FA55D8F428E7B"
					table_id: 4804065436311136435L
					type: "loot"
				}
				{
					count: 2
					id: "6F4A88033B4245E2"
					item: "alltheores:steel_ingot"
					type: "item"
				}
			]
			shape: "diamond"
			subtitle: "需要固化流体？"
			tasks: [
				{
					id: "091F155D7F3E3BB9"
					item: "gtceu:lv_fluid_solidifier"
					type: "item"
				}
				{
					id: "2DCDA83AF43F55A3"
					item: "gtceu:ingot_casting_mold"
					type: "item"
				}
			]
			x: -1.5d
			y: 3.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"更经济的线材橡胶包覆方案!"
				""
				"制造高级线缆必须用此机器包覆高阶线材"
				""
				"进入中压阶段获得聚乙烯后,可用它制作机器外壳"
			]
			id: "0718FA338E8BA792"
			rewards: [{
				exclude_from_claim_all: true
				id: "6435CAC26D2AE960"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			tasks: [{
				id: "1F057DDA8380A19B"
				item: "gtceu:lv_assembler"
				type: "item"
			}]
			x: 0.5d
			y: -2.5d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["满足所有杆状材料生产需求!"]
			id: "601DA80C08C3F9AC"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "2C6B38D702FD7813"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			shape: "diamond"
			subtitle: "经典杆材"
			tasks: [{
				id: "6B700066BC245B01"
				item: "gtceu:lv_lathe"
				type: "item"
			}]
			x: -3.5d
			y: 3.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["离心机通过高速旋转分离化合物成分"]
			id: "7A8242E6D0BC6294"
			rewards: [{
				exclude_from_claim_all: true
				id: "2611E3D86AEBB72F"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			subtitle: "旋转跳跃不停歇"
			tasks: [{
				id: "367EC7D585D98AA3"
				item: "gtceu:lv_centrifuge"
				type: "item"
			}]
			x: 0.5d
			y: -3.5d
		}
		{
			dependencies: [
				"2BE4B6F1CCAA36AC"
				"2E2B6921723681C7"
				"402B0C7242A00309"
			]
			description: [
				"终于可以制作二极管了"
				""
				"需将砷化镓粉分装成小份,或设置自动处理配方"
			]
			id: "0F9829B0A5EEE67B"
			rewards: [{
				id: "5779FAD09110C8D8"
				item: "gtceu:small_gallium_arsenide_dust"
				type: "item"
			}]
			tasks: [{
				id: "1D26B60F4283057A"
				item: "gtceu:gallium_arsenide_dust"
				type: "item"
			}]
			x: 6.0d
			y: 5.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["混合器能融合多种材料,对炼制高级钢材和合金尤为实用"]
			id: "2BE4B6F1CCAA36AC"
			rewards: [{
				exclude_from_claim_all: true
				id: "6E15475C8EC7E18A"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			tasks: [{
				id: "18293B4BFE73030A"
				item: "gtceu:lv_mixer"
				type: "item"
			}]
			x: 0.5d
			y: 5.0d
		}
		{
			dependencies: ["0F9829B0A5EEE67B"]
			description: [
				"提取器可获取液态玻璃用于制造二极管"
				""
				"聚乙烯配方后续会解锁,记得升级这个配方!"
			]
			id: "11D16434F78D2C2C"
			rewards: [
				{
					count: 2
					id: "1DBCAADC256D6736"
					item: "gtceu:fine_copper_wire"
					random_bonus: 2
					type: "item"
				}
				{
					exclude_from_claim_all: true
					id: "731C849551B7E97E"
					table_id: 4804065436311136435L
					type: "loot"
				}
			]
			subtitle: "中压阶段近在咫尺!"
			tasks: [{
				id: "61B743E1BD9B2C2A"
				item: "gtceu:diode"
				type: "item"
			}]
			x: 7.5d
			y: 5.0d
		}
		{
			dependencies: ["59BE2AD1CD0C4ECE"]
			id: "7480A00B82C5DDB5"
			rewards: [{
				count: 2
				id: "2ED65BD250ADB8EB"
				item: "gtceu:silver_single_wire"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "783AA495FA7BB3DD"
				item: "gtceu:phenolic_printed_circuit_board"
				type: "item"
			}]
			x: 7.5d
			y: -2.5d
		}
		{
			dependencies: ["7A8242E6D0BC6294"]
			id: "0251FD45582C3164"
			rewards: [{
				count: 3
				id: "040F3CC75232341A"
				item: "gtceu:sticky_resin"
				random_bonus: 2
				type: "item"
			}]
			subtitle: "请勿吸入"
			tasks: [{
				id: "198DCF429BE2642F"
				item: "gtceu:glue_bucket"
				type: "item"
			}]
			x: 5.5d
			y: -3.5d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["将生产的蒸汽转化为EU电力!"]
			id: "1A000021C07943C4"
			optional: true
			rewards: [
				{
					count: 4
					id: "44700E5D3BE7BFB9"
					item: "alltheores:bronze_plate"
					random_bonus: 4
					type: "item"
				}
				{
					count: 8
					id: "0FBE44A202516001"
					item: "alltheores:steel_plate"
					random_bonus: 8
					type: "item"
				}
			]
			subtitle: "没电不用愁"
			tasks: [{
				id: "630369075AEDE4C6"
				item: "gtceu:lv_steam_turbine"
				type: "item"
			}]
			x: -5.5d
			y: -1.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"已有RF/FE发电设备？"
				""
				"可制作&a能源转换&f将RF转为EU供电"
				""
				"默认设置为EU转RF,需用软锤切换模式"
				""
				"&a红色&f面接EU,绿色面接RF/FE.潜行&a右击&f扳手可旋转转换器"
				""
				"提示:用剪线钳控制线缆连接状态"
			]
			id: "01339A8C26CC6E0C"
			optional: true
			rewards: [
				{
					count: 2
					id: "3B1BB8375B8C17BE"
					item: "gtceu:red_alloy_single_wire"
					random_bonus: 8
					type: "item"
				}
				{
					count: 8
					id: "1D6C384DF92D080D"
					item: "gtceu:tin_single_wire"
					random_bonus: 8
					type: "item"
				}
				{
					count: 8
					id: "3C0F3AFEB9EE2CD2"
					item: "alltheores:steel_plate"
					random_bonus: 8
					type: "item"
				}
			]
			tasks: [{
				id: "52AFA2CE57617039"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:lv_1a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:lv_4a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:lv_8a_energy_converter"
							}
							{
								Count: 1b
								id: "gtceu:lv_16a_energy_converter"
							}
						]
					}
				}
				title: "任意低压能量转换器"
				type: "item"
			}]
			title: "&a能源转换&f"
			x: -5.5d
			y: 1.0d
		}
		{
			dependencies: ["1A000021C07943C4"]
			description: ["当单方块锅炉供汽不足时,就该建造&a大型青铜锅炉&f了!"]
			id: "4E55A1169742B901"
			optional: true
			rewards: [
				{
					count: 8
					id: "55AC853EA70BCE2A"
					item: "alltheores:bronze_plate"
					random_bonus: 4
					type: "item"
				}
				{
					count: 2
					id: "51DD260C78F55705"
					item: "gtceu:tin_single_cable"
					random_bonus: 2
					type: "item"
				}
			]
			subtitle: "蒸汽生产多方块结构"
			tasks: [{
				id: "003E27141ED2E7DB"
				item: "gtceu:bronze_large_boiler"
				type: "item"
			}]
			x: -7.0d
			y: -1.0d
		}
		{
			dependencies: [
				"0718FA338E8BA792"
				"0251FD45582C3164"
			]
			id: "59BE2AD1CD0C4ECE"
			rewards: [{
				id: "08120615CAF1096F"
				item: "gtceu:wood_dust"
				type: "item"
			}]
			tasks: [{
				id: "5BFAFA9C71807BEC"
				item: "gtceu:phenolic_circuit_board"
				type: "item"
			}]
			x: 5.5d
			y: -2.5d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"许多机器使用预设电路配置来决定可处理的合成配方"
				""
				"你可以在机器的图形界面中找到更改预设电路配置的选项,只需选择与你当前合成配方对应的程序,机器就会开始运作"
				""
				"你无需专门制作预设电路"
				""
				"通常为特定程序配置专门分配一台机器并制作新机器,比手动为不同合成配方更改程序设置更为便捷"
			]
			icon: {
				Count: 1
				id: "gtceu:programmed_circuit"
				tag: {
					Configuration: 0
				}
			}
			id: "4875FB6628C1FCDC"
			min_width: 300
			rewards: [{
				id: "2190B18F4B77EA5B"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "71AF3878DBC0AB29"
				title: "&a编程电路&f"
				type: "checkmark"
			}]
			x: -5.0d
			y: 0.0d
		}
		{
			dependencies: [
				"7D5F805A6F2551F0"
				"14C017569BECA2CE"
				"6E2F24117ACF3694"
			]
			description: [
				"将辉钴矿粉与三桶氧气放入你的&a电力高炉&f中,即可获得&a砒霜&f粉"
				""
				"该过程同时会产生&a氧化钴&f和部分&a二氧化硫&f,后者可进一步加工以回收部分氧气"
			]
			id: "74EE98F95F483C37"
			rewards: [{
				id: "5C651B2AB9D6925B"
				item: "gtceu:arsenic_trioxide_dust"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "1D82282268F050A6"
				item: "gtceu:arsenic_trioxide_dust"
				type: "item"
			}]
			x: 4.5d
			y: 0.0d
		}
		{
			dependencies: ["439C8AEBE9E1C666"]
			description: [
				"收集所列材料后查看JEI的多方块结构信息页了解搭建方式"
				""
				"每个LV能源仓可接收2安培低压电.组合后共提供4安培低压电,这种情况下电力高炉就能像接收1安培中压电那样处理MV级配方!"
				""
				"&a钻井平台&f信息页展示了该多方块结构的一种可能配置,但通常并非理想配置.请务必确认各位置的有效方块类型"
				""
				"当输入输出方块颜色变得与控制器和外壳一致时,即表示结构成型"
			]
			icon: "gtceu:electric_blast_furnace"
			id: "6E2F24117ACF3694"
			min_width: 350
			rewards: [
				{
					exclude_from_claim_all: true
					id: "0B35E2C6F42DFF49"
					table_id: 4804065436311136435L
					type: "loot"
				}
				{
					count: 2
					id: "7EB932BBDF978DF9"
					item: "gtceu:tin_octal_cable"
					type: "item"
				}
			]
			subtitle: "首个多方块结构"
			tasks: [
				{
					id: "4C753457114B1DEF"
					item: "gtceu:electric_blast_furnace"
					type: "item"
				}
				{
					count: 2L
					id: "26560CEB97AF9425"
					item: "gtceu:lv_energy_input_hatch"
					type: "item"
				}
				{
					count: 16L
					id: "25E39941CF0E60F0"
					item: "gtceu:cupronickel_coil_block"
					type: "item"
				}
				{
					id: "50C5803D3BF1B2BC"
					item: "gtceu:lv_input_hatch"
					type: "item"
				}
				{
					id: "7A8AEC45CF09062F"
					item: "gtceu:lv_output_hatch"
					type: "item"
				}
				{
					id: "162FB1B27E0828BC"
					item: "gtceu:lv_input_bus"
					type: "item"
				}
				{
					id: "14DAA8338634372F"
					item: "gtceu:lv_output_bus"
					type: "item"
				}
				{
					id: "4F425DAD0F9D4FC4"
					item: "gtceu:lv_muffler_hatch"
					type: "item"
				}
				{
					id: "35A54E0FF0FEE7FD"
					item: "gtceu:maintenance_hatch"
					type: "item"
				}
				{
					count: 9L
					id: "691D046D2C4AC344"
					item: "gtceu:heatproof_machine_casing"
					type: "item"
				}
			]
			x: 1.5d
			y: 0.0d
		}
		{
			description: [
				"强烈建议提前制备氧气,因其需求量大"
				""
				"获取氧气方式多样,在JEI查询时注意配方标注的使用等级为LV或ULV"
				""
				"针铁矿粉可通过离心获得氧气,若追求效率不妨电解蓝宝石粉"
				""
				"也可建造基础&a集气装置&f收集空气再离心制氧.电解水也能制氧,但需注意这些方法效率较低"
			]
			id: "7D5F805A6F2551F0"
			min_width: 250
			rewards: [{
				id: "5EB301581155D469"
				type: "xp"
				xp: 10
			}]
			shape: "square"
			size: 1.25d
			tasks: [{
				id: "41E980F316610E20"
				item: "gtceu:oxygen_bucket"
				type: "item"
			}]
			x: 4.525d
			y: -1.25d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"粉碎机 -> &a洗矿厂&f -> &a热能离心机&f -> 粉碎机"
				""
				"这是一套矿石处理流水线配置,其他可行方案请查阅JEI!"
				""
				"当你在高压阶段建成该流水线后,将获得多种副产品"
				""
				"这些副产品常是后续合成配方所需材料,务必在高压阶段重新利用并保留它们!"
				""
				"该流水线有多种自动化实现方式,具体方案留给读者自行探索"
				""
				"[ \"\", { \"text\": \"Note: \", \"color\":\"#FFFF55\", \"bold\":\"true\" }, { \"text\": \"The Ore Washer requires \" }, { \"text\": \"water\", \"color\":\"#5555FF\" }, { \"text\": \" and a \" }, { \"text\":\"&a编程电路&f\", \"color\":\"#FFAA00\", \"clickEvent\": { \"action\":\"change_page\", \"value\":\"4875FB6628C1FCDC\" }, \"underlined\":\"true\", \"hoverEvent\": { \"action\":\"show_text\", \"contents\": { \"text\":\"&a点击此处&f查看提示\" } } }, { \"text\":\" 设置运行\" } ]"
			]
			id: "25DBFE887B041E94"
			min_width: 350
			rewards: [{
				exclude_from_claim_all: true
				id: "7275D97C107F982C"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			subtitle: "基础入门"
			tasks: [
				{
					id: "30667DF7F6540C0B"
					item: "gtceu:lv_macerator"
					type: "item"
				}
				{
					id: "0BFF441E2D88D565"
					item: "gtceu:lv_ore_washer"
					type: "item"
				}
				{
					id: "72F6075E2C976BE5"
					item: "gtceu:lv_thermal_centrifuge"
					type: "item"
				}
			]
			title: "矿石处理"
			x: 0.5d
			y: 1.0d
		}
		{
			dependencies: ["25DBFE887B041E94"]
			description: [
				"辉钴矿粉可通过加工辉钴矿获得"
				""
				"在&a矿脉维度&f的岩石层中挖掘可找到&a辉钴矿矿石&f."
			]
			id: "14C017569BECA2CE"
			rewards: [{
				count: 4
				id: "1A770CEE35A99BEA"
				item: "gtceu:raw_cobaltite"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "1FC622A82F527A54"
				item: "gtceu:cobaltite_dust"
				type: "item"
			}]
			x: 4.5d
			y: 1.0d
		}
		{
			dependencies: ["512BD9BCA43AEE83"]
			description: [
				"若运气好找到雄黄矿石,可直接通过离心获得砷"
				""
				"辉钴矿也可直接电解获取砷(需MV电压).但通过制作&a砒霜&f粉再电解能获得更多砷"
			]
			id: "2E2B6921723681C7"
			rewards: [{
				count: 2
				id: "1E0C65C8BDFA9A54"
				item: "gtceu:realgar_dust"
				type: "item"
			}]
			tasks: [{
				id: "26E1467CEA5DF223"
				item: "gtceu:arsenic_dust"
				type: "item"
			}]
			x: 6.0d
			y: 2.0d
		}
		{
			dependencies: ["6E2F24117ACF3694"]
			description: [
				"拿起工具打开维护舱,该解决问题了!"
				""
				"使用对应工具点击维护舱即可修复所有故障"
				""
				"需要什么工具？打开&a维护舱&f界面时,右侧会显示浮动扳手图标,鼠标悬停即可查看提示"
				""
				"修复完成后,电力高炉即可正常使用!"
				""
				"注意故障可能再次发生,但频率不高且易于修复"
			]
			id: "23B55A8C7D6482FF"
			min_width: 300
			rewards: [{
				id: "47DE96702DC29027"
				type: "xp"
				xp: 10
			}]
			subtitle: "没错,它&a已损坏&f"
			tasks: [{
				id: "0362C10508FC4615"
				title: "设备维护"
				type: "checkmark"
			}]
			x: 1.5d
			y: -1.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"类似蒸汽时代设备,本装置可将固态金属转化为液态"
				""
				"此外还能制取&a生橡胶浆&f"
			]
			id: "504DEE88DFDBD380"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "597248294BEF3E6F"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			shape: "diamond"
			subtitle: "资产液化"
			tasks: [{
				id: "74FD2DABE6D932C9"
				item: "gtceu:lv_extractor"
				type: "item"
			}]
			x: -2.0d
			y: 3.5d
		}
		{
			dependencies: [
				"25DBFE887B041E94"
				"4059373B88845C6F"
				"7B8ADB4104E7C440"
			]
			description: [
				"需先制备&a过硫酸钠&f用于&a化学浸洗器&f,在单独的矿石&a处理&f产线中替代&a洗矿厂&f,从而在加工铝土矿时有机会获得&a镓粉&f."
				""
				"达到HV电压后,也可通过加工铝土矿或闪锌矿获取镓."
			]
			id: "402B0C7242A00309"
			rewards: [{
				count: 4
				id: "7C44EBCF5BBBA3EE"
				item: "gtceu:raw_bauxite"
				random_bonus: 4
				type: "item"
			}]
			tasks: [{
				id: "41846B8B240030AE"
				item: "gtceu:gallium_dust"
				type: "item"
			}]
			x: 4.5d
			y: 2.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["制作锻铁需要将&a铁粒&f熔炼成&a锻铁粒&f"]
			id: "313AA0A45CD2BBB9"
			rewards: [{
				count: 2
				id: "50FC7B684543C55A"
				item: "gtceu:wrought_iron_ingot"
				random_bonus: 2
				type: "item"
			}]
			shape: "pentagon"
			size: 1.5d
			tasks: [{
				id: "7D2E6775D39566B8"
				item: "gtceu:wrought_iron_nugget"
				type: "item"
			}]
			x: -2.45d
			y: -2.5500000000000003d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: [
				"希望你手头还有那个&a合金炉&f!"
				""
				"将铜和镍一起放入&a合金炉&f可以制成白铜,这是制作&a电力高炉&f线圈所需的材料"
			]
			id: "439C8AEBE9E1C666"
			rewards: [{
				id: "418875730A8386DC"
				item: "gtceu:cupronickel_ingot"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "5E797E24FD42DB71"
				item: "gtceu:cupronickel_ingot"
				type: "item"
			}]
			x: 0.5d
			y: 0.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["为什么要被动生成资源？因为被动生成常见资源比按需订购更合理,这样你就能在需要时立即获得所需物资,而不必长时间等待\\n\\n在世界中设置&a碎石机&f,左侧加水右侧加岩浆,然后放入圆石来生产圆石\\n\\n将圆石输出到&a锻造锤&f制成沙砾\\n\\n将沙砾输出到另一个&a锻造锤&f制成沙子\\n\\n将沙子与氧气一起放入&a电弧炉&f制成玻璃\\n\\n将玻璃放入粉碎机制成玻璃粉\\n\\n将玻璃粉放入离心机获得&a二氧化硅&f\\n\\n将&a二氧化硅&f放入电解机可获得&a硅粉&f和氧气!\\n\\n启动所需的所有材料就是少量氧气,系统会以绿宝石粉的形式提供给你!"]
			id: "0D99638C96AB2EEA"
			min_width: 400
			optional: true
			rewards: [
				{
					exclude_from_claim_all: true
					id: "7BC86ED902460525"
					table_id: 4804065436311136435L
					type: "loot"
				}
				{
					count: 5
					id: "7E5734BA79A85F7B"
					item: "gtceu:green_sapphire_dust"
					type: "item"
				}
			]
			shape: "diamond"
			size: 1.25d
			subtitle: "你会需要它"
			tasks: [
				{
					id: "00648047034C9531"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "gtceu:lv_rock_crusher"
								}
								{
									Count: 1b
									id: "gtceu:lp_steam_rock_crusher"
								}
								{
									Count: 1b
									id: "gtceu:hp_steam_rock_crusher"
								}
							]
						}
					}
					title: "&a碎石机&f"
					type: "item"
				}
				{
					count: 2L
					id: "6D9C084B63F0AE29"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "gtceu:lv_forge_hammer"
								}
								{
									Count: 1b
									id: "gtceu:lp_steam_forge_hammer"
								}
								{
									Count: 1b
									id: "gtceu:hp_steam_forge_hammer"
								}
							]
						}
					}
					title: "&a锻造锤子&f"
					type: "item"
				}
				{
					id: "357F4A0BA739F0F3"
					item: "gtceu:lv_arc_furnace"
					type: "item"
				}
				{
					id: "016BA8271625AE36"
					item: {
						Count: 1
						id: "itemfilters:or"
						tag: {
							items: [
								{
									Count: 1b
									id: "gtceu:lv_macerator"
								}
								{
									Count: 1b
									id: "gtceu:lp_steam_macerator"
								}
								{
									Count: 1b
									id: "gtceu:hp_steam_macerator"
								}
							]
						}
					}
					title: "粉碎机"
					type: "item"
				}
				{
					id: "75D597970DB148DA"
					item: "gtceu:lv_centrifuge"
					type: "item"
				}
				{
					id: "50AD676E09AE6D96"
					item: "gtceu:lv_electrolyzer"
					type: "item"
				}
			]
			title: "被动供氧产线"
			x: -2.5249999999999995d
			y: 2.9000000000000004d
		}
		{
			dependencies: ["74EE98F95F483C37"]
			description: [
				"电解机将根据电荷将化合物分离成其组成成分"
				""
				"在我们的案例中,它会把&d&a砒霜&f&r分解成&e砷&r和&b氧气"
			]
			id: "512BD9BCA43AEE83"
			rewards: [{
				exclude_from_claim_all: true
				id: "1E0610F2A25EA1CB"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			subtitle: "像电鳗一样电我"
			tasks: [{
				id: "4DE768EBE8A565BA"
				item: "gtceu:lv_electrolyzer"
				type: "item"
			}]
			x: 6.0d
			y: 0.0d
		}
		{
			dependencies: ["620C406CC24F179C"]
			description: [
				"在&e&a化学反应器&f&r中选择&a程序2&r,将氧气和硫粉反应可生成二氧化硫"
				""
				"如果硫粉供应跟不上,你可以开始离心处理2个烈焰粉来制作硫粉"
			]
			id: "64314EEB9916CAB1"
			rewards: [{
				id: "26B12B2CFCFA4919"
				item: "gtceu:sulfur_dust"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "7198853CC3A9DEAC"
				item: "gtceu:sulfur_dioxide_bucket"
				type: "item"
			}]
			x: 1.5d
			y: 4.0d
		}
		{
			dependencies: ["64314EEB9916CAB1"]
			description: [
				"将二氧化硫与更多氧气进行化学反应可制成三氧化硫"
				""
				"氧气非常重要,如果你还没准备好,建议回头专门搭建一套被动生产氧气的设备"
			]
			id: "5143E0F48520D41B"
			rewards: [{
				id: "203401EB866852ED"
				item: "gtceu:sulfur_dioxide_bucket"
				type: "item"
			}]
			tasks: [{
				id: "5D9AA8DF5ECCFCAC"
				item: "gtceu:sulfur_trioxide_bucket"
				type: "item"
			}]
			x: 2.5d
			y: 4.0d
		}
		{
			dependencies: ["5143E0F48520D41B"]
			description: [
				"将水与三氧化硫化学反应可获得硫酸"
				""
				"你可以通过连接&a厨房水槽&f在数字存储系统中获得无限水源——只需确保设置为仅提取模式!否则插入时会清空流体"
			]
			id: "17A935989A9505E1"
			rewards: [{
				id: "1A9E73CE0805E879"
				item: "gtceu:sulfur_trioxide_bucket"
				type: "item"
			}]
			subtitle: "加水就行!"
			tasks: [{
				id: "75017109130EE055"
				item: "gtceu:sulfuric_acid_bucket"
				type: "item"
			}]
			x: 3.5d
			y: 4.0d
		}
		{
			dependencies: ["5C1BB559DD83E88F"]
			description: ["&e&a化学浸洗器&f&r用于&a矿物处理&r产线,通过在汞或过硫酸钠中洗涤粉碎矿石来获取某些&d副产品&r"]
			id: "4059373B88845C6F"
			rewards: [{
				exclude_from_claim_all: true
				id: "1CE93A5EA7A30FA3"
				table_id: 4804065436311136435L
				type: "loot"
			}]
			tasks: [{
				id: "58FC9BE9F7D8485C"
				item: "gtceu:lv_chemical_bath"
				type: "item"
			}]
			x: 0.5d
			y: 2.0d
		}
		{
			dependencies: [
				"620C406CC24F179C"
				"17A935989A9505E1"
			]
			description: ["盐和&a硫酸&f在程序1中反应可制成&a硫酸氢钠&f"]
			id: "4835BE1596B65206"
			rewards: [{
				count: 3
				id: "247CDED45EA75B54"
				item: "gtceu:sodium_bisulfate_dust"
				random_bonus: 3
				type: "item"
			}]
			tasks: [{
				id: "55065DC2687DC4D2"
				item: "gtceu:sodium_bisulfate_dust"
				type: "item"
			}]
			x: 2.5d
			y: 3.0d
		}
		{
			dependencies: ["4835BE1596B65206"]
			description: ["[ \"You need to \", { \"text\": \"Electrolyze\", \"color\": \"yellow\", \"underlined\": \"true\", \"clickEvent\": { \"action\": \"change_page\", \"value\": \"512BD9BCA43AEE83\" } }, \" the Sodium Bisulfate\" ]"]
			id: "7B8ADB4104E7C440"
			rewards: [{
				id: "0E2AB9F3A1583996"
				item: "gtceu:sodium_persulfate_bucket"
				type: "item"
			}]
			tasks: [{
				id: "64890DABF420C352"
				item: "gtceu:sodium_persulfate_bucket"
				type: "item"
			}]
			x: 3.5d
			y: 3.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若你看到此信息,说明你正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "513832144F0EA96D"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "30CA18B3FD3DBC7A"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
				{
					id: "5B990E6FE40995D6"
					title: "AllTheMods任务集"
					type: "checkmark"
				}
			]
			x: -2.5d
			y: -4.0d
		}
	]
	title: "低压电"
}
