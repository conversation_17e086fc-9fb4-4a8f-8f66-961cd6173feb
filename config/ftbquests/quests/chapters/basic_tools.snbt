{
	autofocus_id: "5151CDD8FCDE7A07"
	default_hide_dependency_lines: false
	default_quest_shape: "diamond"
	filename: "basic_tools"
	group: "22FB35B0FEF1343D"
	icon: {
		Count: 1
		id: "minecraft:diamond_pickaxe"
		tag: {
			Damage: 0
		}
	}
	id: "4B9D26E0087CD163"
	images: [
		{
			height: 0.5d
			image: "quark:textures/attribute/attack_damage.png"
			rotation: 0.0d
			width: 0.5d
			x: -3.0d
			y: 4.0d
		}
		{
			height: 0.5d
			image: "quark:textures/attribute/attack_damage.png"
			rotation: 0.0d
			width: 0.5d
			x: -3.0d
			y: 5.0d
		}
		{
			height: 0.5d
			image: "quark:textures/attribute/attack_damage.png"
			rotation: 0.0d
			width: 0.5d
			x: -3.0d
			y: 6.0d
		}
		{
			height: 0.5d
			image: "quark:textures/attribute/attack_damage.png"
			rotation: 0.0d
			width: 0.5d
			x: -3.0d
			y: 7.0d
		}
		{
			height: 0.5d
			image: "quark:textures/attribute/attack_damage.png"
			rotation: 0.0d
			width: 0.5d
			x: -3.0d
			y: 8.0d
		}
		{
			height: 0.5d
			image: "quark:textures/attribute/attack_damage.png"
			rotation: 0.0d
			width: 0.5d
			x: -3.0d
			y: 11.0d
		}
		{
			height: 0.7d
			image: "atm:textures/questpics/basictools/4.png"
			rotation: 0.0d
			width: 0.5d
			x: -2.5d
			y: 4.0d
		}
		{
			height: 0.7d
			image: "atm:textures/questpics/basictools/5.png"
			rotation: 0.0d
			width: 0.5d
			x: -2.5d
			y: 5.0d
		}
		{
			height: 0.7d
			image: "atm:textures/questpics/basictools/6.png"
			rotation: 0.0d
			width: 0.5d
			x: -2.5d
			y: 6.0d
		}
		{
			height: 0.7d
			image: "atm:textures/questpics/basictools/7.png"
			rotation: 0.0d
			width: 0.5d
			x: -2.5d
			y: 7.0d
		}
		{
			height: 0.7d
			image: "atm:textures/questpics/basictools/8.png"
			rotation: 0.0d
			width: 0.5d
			x: -2.5d
			y: 8.0d
		}
		{
			height: 0.7d
			image: "atm:textures/questpics/basictools/13.png"
			rotation: 0.0d
			width: 1.4d
			x: -2.15d
			y: 11.0d
		}
		{
			height: 1.0d
			image: "minecraft:item/wooden_pickaxe"
			rotation: 0.0d
			width: 1.0d
			x: 5.0d
			y: 0.0d
		}
		{
			height: 1.0d
			image: "minecraft:item/iron_pickaxe"
			rotation: 0.0d
			width: 1.0d
			x: 5.0d
			y: -1.0d
		}
		{
			height: 1.0d
			image: "minecraft:item/netherite_pickaxe"
			rotation: 0.0d
			width: 1.0d
			x: 5.0d
			y: -2.0d
		}
		{
			height: 1.0d
			image: "minecraft:item/stone_pickaxe"
			rotation: 0.0d
			width: 1.0d
			x: 6.0d
			y: -0.5d
		}
		{
			height: 1.0d
			image: "minecraft:item/diamond_pickaxe"
			rotation: 0.0d
			width: 1.0d
			x: 6.0d
			y: -1.5d
		}
		{
			height: 1.0d
			image: "allthemodium:item/alloy_pick"
			rotation: 0.0d
			width: 1.0d
			x: 6.0d
			y: -2.5d
		}
	]
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["5151CDD8FCDE7A07"]
			description: ["&9等级1&r是最低的镐子等级,需要一把镐子.任何镐子都能开采这些方块.等级1的物品包括石头、熔炉和&a煤矿石&f."]
			id: "57F47BA495B27F83"
			rewards: [{
				count: 32
				id: "4311F03D24305238"
				item: "minecraft:cobblestone"
				type: "item"
			}]
			tasks: [{
				id: "39B72EA881F24AAE"
				item: "minecraft:cobblestone"
				type: "item"
			}]
			title: "&9采掘等级1"
			x: -3.0d
			y: 0.0d
		}
		{
			dependencies: ["57F47BA495B27F83"]
			description: ["&f等级2&r是石镐级别,几乎只用于开采铁质物品."]
			icon: "minecraft:iron_ore"
			id: "3917DECAEF56A06A"
			rewards: [{
				count: 5
				id: "16E6A3E5A0BF91BA"
				item: "minecraft:raw_iron"
				type: "item"
			}]
			tasks: [{
				id: "777BAD04536F4BA7"
				item: "minecraft:raw_iron"
				type: "item"
			}]
			title: "&f采掘等级2"
			x: -2.5d
			y: -0.5d
		}
		{
			dependencies: ["3917DECAEF56A06A"]
			description: ["&b等级3&r是&a铁镐&f级别,可以开采大多数矿石,如钻石、绿宝石和金矿."]
			icon: "minecraft:diamond_ore"
			id: "6601450C63FEEE61"
			rewards: [{
				count: 3
				id: "4C83D836AF808F98"
				item: "minecraft:diamond"
				type: "item"
			}]
			tasks: [{
				id: "70DE0AD7C07C5B90"
				item: "minecraft:diamond"
				type: "item"
			}]
			title: "&b采掘等级3"
			x: -3.0d
			y: -1.0d
		}
		{
			dependencies: ["6601450C63FEEE61"]
			description: ["&5等级4&r是钻石工具级别,用于获取黑曜石和下界合金.还包括一些模组矿石,如铀和铂."]
			id: "17FE0C7748DD65CA"
			rewards: [{
				id: "2C89B133ECDB0D55"
				item: "minecraft:ancient_debris"
				type: "item"
			}]
			tasks: [{
				id: "055D5441B11779D7"
				item: "minecraft:ancient_debris"
				type: "item"
			}]
			title: "&5采掘等级4"
			x: -2.5d
			y: -1.5d
		}
		{
			dependencies: ["17FE0C7748DD65CA"]
			description: ["&e等级5&r基本上只用于&aATM矿石&f,其他用途不多."]
			icon: "allthemodium:allthemodium_ore"
			id: "03E05018D64DDEE1"
			tasks: [{
				id: "13294ADF4877B642"
				item: "allthemodium:raw_allthemodium"
				type: "item"
			}]
			title: "&e采掘等级5"
			x: -3.0d
			y: -2.0d
		}
		{
			dependencies: ["03E05018D64DDEE1"]
			description: ["&d等级6&r是最高级别,任何可破坏的方块都可以用它开采,包括振金和不可得矿."]
			icon: "allthemodium:unobtainium_ore"
			id: "09733948CBCB3FB9"
			tasks: [{
				id: "04FF7D3823C4ECF5"
				item: "allthemodium:raw_unobtainium"
				type: "item"
			}]
			title: "&d采掘等级6"
			x: -2.5d
			y: -2.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2EE4E88FC814AFA4"
			optional: true
			rewards: [{
				id: "3031ADF00E92BCF7"
				type: "xp"
				xp: 10
			}]
			subtitle: "&9等级1"
			tasks: [{
				id: "2E885362FFDF770D"
				item: {
					Count: 1
					id: "minecraft:wooden_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.5d
			y: 0.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2E6D11A5F5626A6C"
			optional: true
			rewards: [{
				id: "34BC62235F514594"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f等级2"
			tasks: [{
				id: "4CDD283558108D8B"
				item: {
					Count: 1
					id: "minecraft:stone_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: -0.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "70ED849A2EB44DEE"
			optional: true
			rewards: [{
				id: "6BBFC661F4E60DFA"
				type: "xp"
				xp: 10
			}]
			subtitle: "&b等级3"
			tasks: [{
				id: "41A47F38D96A9747"
				item: {
					Count: 1
					id: "minecraft:iron_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.5d
			y: -1.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "29A05589E96C0569"
			optional: true
			rewards: [{
				id: "55C618EF06572D1C"
				type: "xp"
				xp: 10
			}]
			subtitle: "&5等级4"
			tasks: [{
				id: "3CAFF729F25A9850"
				item: {
					Count: 1
					id: "minecraft:diamond_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: -1.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "299F56B753286D0C"
			optional: true
			rewards: [{
				id: "10F391CFBE32F48C"
				type: "xp"
				xp: 10
			}]
			subtitle: "&e等级5"
			tasks: [{
				id: "111144CBEF06EC7F"
				item: {
					Count: 1
					id: "minecraft:netherite_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.5d
			y: -2.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "0C31B053EAB8B2AB"
			optional: true
			rewards: [{
				id: "7BA0E65200C38DFB"
				type: "xp"
				xp: 10
			}]
			subtitle: "&b等级3"
			tasks: [{
				id: "30CDC00635935527"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanismtools:steel_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:bronze_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:osmium_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "undergarden:cloggrum_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "undergarden:froststeel_pickaxe"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d通用机械&f与深暗之园&a铁镐&f"
				type: "item"
			}]
			x: -0.5d
			y: -1.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "512396B331108BAF"
			optional: true
			rewards: [{
				id: "68E68D3EE5C5D4E1"
				type: "xp"
				xp: 10
			}]
			subtitle: "&e等级5"
			tasks: [{
				id: "40971F256D8743D9"
				item: {
					Count: 1
					id: "mekanismtools:refined_obsidian_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -0.5d
			y: -2.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "51DBE688ED630DB1"
			optional: true
			rewards: [{
				id: "722E86916673F0A9"
				type: "xp"
				xp: 10
			}]
			subtitle: "&5等级4"
			tasks: [{
				id: "45C9F5CDC7FD3229"
				item: {
					Count: 1
					id: "mekanismtools:refined_glowstone_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: -1.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "6A4419E89C0BA985"
			optional: true
			rewards: [{
				id: "2345DC06D3DAE1A5"
				type: "xp"
				xp: 10
			}]
			subtitle: "&d等级6"
			tasks: [{
				id: "2500C7ACDE984FBD"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "naturesaura:sky_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "naturesaura:depth_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:manasteel_pick"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:elementium_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "forbidden_arcanus:slimec_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "forbidden_arcanus:reinforced_deorum_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "forbidden_arcanus:draco_arcanus_pickaxe"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "魔法模组终极镐具"
				type: "item"
			}]
			x: 0.0d
			y: -2.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "3E78FA34A7D40044"
			optional: true
			rewards: [{
				id: "7C632E7849124ED9"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f等级2"
			tasks: [{
				id: "4A58C303DAC0C081"
				item: {
					Count: 1
					id: "aiotbotania:livingrock_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 3.0d
			y: -0.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "70ACECAEEE8947A5"
			optional: true
			rewards: [{
				id: "5BD2AF14EC2B15AB"
				type: "xp"
				xp: 10
			}]
			subtitle: "&9等级1"
			tasks: [{
				id: "4E240FF2592F5CD4"
				item: {
					Count: 1
					id: "aiotbotania:livingwood_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 2.5d
			y: 0.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "451D03BE892CDE74"
			optional: true
			rewards: [{
				id: "7793D05A19ACE401"
				type: "xp"
				xp: 10
			}]
			subtitle: "&d等级6"
			tasks: [{
				id: "6CCC0DE3A3CC048E"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "aether:gravitite_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "aether:valkyrie_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "lost_aether_content:phoenix_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "undergarden:utherium_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "undergarden:forgotten_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "voidscape:voidic_crystal_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "voidscape:titanite_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "voidscape:astral_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "voidscape:ichor_pickaxe"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "探险模组终极镐具"
				type: "item"
			}]
			x: 1.0d
			y: -2.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "568A1121F820345F"
			optional: true
			rewards: [{
				id: "41613A9D11DF31AC"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f等级2"
			tasks: [{
				id: "1680A9E188D1F4E1"
				item: {
					Count: 1
					id: "blue_skies:pyrope_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.0d
			y: -0.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "114504C48DF53A03"
			optional: true
			rewards: [{
				id: "3BEF99628A1F022B"
				type: "xp"
				xp: 10
			}]
			subtitle: "&b等级3"
			tasks: [{
				id: "33689D889904670A"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "blue_skies:aquite_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "aether:zanite_pickaxe"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d蔚蓝浩空&f与天境&a铁镐&f"
				type: "item"
			}]
			x: 0.5d
			y: -1.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "58DDD32F849940A0"
			optional: true
			rewards: [{
				id: "1BA79ADBAC38C919"
				type: "xp"
				xp: 10
			}]
			subtitle: "&d等级6"
			tasks: [{
				id: "3994D5448E03DF6B"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "blue_skies:horizonite_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:charoite_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:diopside_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mysticalagriculture:imperium_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mysticalagriculture:supremium_pickaxe"
							}
							{
								Count: 1b
								id: "mysticalagriculture:awakened_supremium_pickaxe"
							}
						]
					}
				}
				title: "神秘工艺与&d蔚蓝浩空&f终极镐"
				type: "item"
			}]
			x: 2.0d
			y: -2.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "518105318E1C38B2"
			optional: true
			rewards: [{
				id: "2A92E494186FAA02"
				type: "xp"
				xp: 10
			}]
			subtitle: "&9等级1"
			tasks: [{
				id: "5E5533CDA9CA9C18"
				item: {
					Count: 1
					id: "botania:glass_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.5d
			y: 0.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "67F992E5672671CD"
			optional: true
			rewards: [{
				id: "3D4775E2E14D6363"
				type: "xp"
				xp: 10
			}]
			subtitle: "&e等级5"
			tasks: [{
				id: "31D48DF4CE59F793"
				item: {
					Count: 1
					id: "cataclysm:void_forge"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.5d
			y: -2.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "768B5E3633A76ED0"
			optional: true
			rewards: [{
				id: "271D3C6C1F40A2AB"
				type: "xp"
				xp: 10
			}]
			subtitle: "&e等级5"
			tasks: [{
				id: "1162C2EBA5671A9E"
				item: {
					Count: 1
					id: "cataclysm:infernal_forge"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.5d
			y: -2.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2AD4FE4B9DB741CC"
			optional: true
			rewards: [{
				id: "2E0C11381668A78D"
				type: "xp"
				xp: 10
			}]
			subtitle: "&5等级4"
			tasks: [{
				id: "6996A70A79D41BC2"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mysticalagriculture:inferium_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mysticalagriculture:prudentium_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mysticalagriculture:tertium_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "twilightforest:steeleaf_pickaxe"
								tag: {
									Damage: 0
									Enchantments: [{
										id: "minecraft:fortune"
										lvl: 2s
									}]
								}
							}
							{
								Count: 1b
								id: "twilightforest:knightmetal_pickaxe"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "神秘工艺与暮色钻石镐"
				type: "item"
			}]
			x: 1.0d
			y: -1.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "076A26C8177FED1A"
			optional: true
			rewards: [{
				id: "7C1E77A2EA651584"
				type: "xp"
				xp: 10
			}]
			subtitle: "&9等级1"
			tasks: [{
				id: "48FBE7B7D70A0833"
				item: {
					Count: 1
					id: "aether:skyroot_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.5d
			y: 0.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "5DB7C962203735ED"
			optional: true
			rewards: [{
				id: "376FBC74B9FD5236"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f等级2"
			tasks: [{
				id: "6ACFECB8D48FC52A"
				item: {
					Count: 1
					id: "aether:holystone_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 2.0d
			y: -0.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "4F68F142D9DE569A"
			optional: true
			rewards: [{
				id: "14A627944590A711"
				type: "xp"
				xp: 10
			}]
			subtitle: "&b等级3"
			tasks: [{
				id: "35227D0C9A4DC669"
				item: {
					Count: 1
					id: "thermal:flux_drill"
					tag: { }
				}
				type: "item"
			}]
			x: 2.5d
			y: -1.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "3B88D58B11B5DC78"
			optional: true
			rewards: [{
				id: "6307DF789996C68E"
				type: "xp"
				xp: 10
			}]
			subtitle: "&5等级4"
			tasks: [
				{
					id: "3D7B36B1CBAB6ADF"
					item: {
						Count: 1
						id: "mininggadgets:mininggadget_simple"
						tag: {
							battery_tier: 0
						}
					}
					type: "item"
				}
				{
					id: "5F1B08F29E1383DE"
					item: {
						Count: 1
						id: "mininggadgets:mininggadget_fancy"
						tag: {
							battery_tier: 0
						}
					}
					type: "item"
				}
				{
					id: "470E74755C072CFE"
					item: {
						Count: 1
						id: "mininggadgets:mininggadget"
						tag: {
							battery_tier: 0
						}
					}
					type: "item"
				}
			]
			title: "&a采掘小帮手&f"
			x: 3.0d
			y: -1.5d
		}
		{
			description: ["欢迎来到基础工具指南!\\n\\n是否遇到过从未见过或听说过的工具,想知道它与其他工具的对比？那么这个任务非常适合你!\\n\\n(剑按基础伤害排序,可升级或用于提升伤害的剑不包括在内)\\n\\n(请不要将此作为收集清单,寻找所有工具和武器会让你99%%发疯)."]
			hide_dependent_lines: true
			id: "5151CDD8FCDE7A07"
			rewards: [{
				count: 16
				id: "2BA08F25D16B9421"
				item: "minecraft:stick"
				type: "item"
			}]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "35A05A5BEA46A198"
				item: "minecraft:stick"
				type: "item"
			}]
			title: "工具大全!"
			x: 0.0d
			y: 2.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "0583862ED0114442"
			optional: true
			rewards: [{
				id: "14B87D0BEB9DFC4A"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f4&r &4点伤害"
			tasks: [{
				id: "77C3BFB8C7F805CF"
				item: {
					Count: 1
					id: "minecraft:wooden_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 4.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "54D3D6806C1F33F3"
			optional: true
			rewards: [{
				id: "787BC2943A8FC876"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f5&r &4点伤害"
			tasks: [{
				id: "13AFEBED5C116FC8"
				item: {
					Count: 1
					id: "minecraft:stone_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 5.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2B9AF0D6ED8BA628"
			optional: true
			rewards: [{
				id: "7991802B4098F206"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f6&r &4点伤害"
			tasks: [{
				id: "1C1B919636DDC925"
				item: {
					Count: 1
					id: "minecraft:iron_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 6.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "55A9EB63F4964FCF"
			optional: true
			rewards: [{
				id: "142CB6AA86100D70"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7&r &4点伤害"
			tasks: [{
				id: "3B9E51CF30228269"
				item: {
					Count: 1
					id: "minecraft:diamond_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 7.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "36A3ECD4F6CA2732"
			optional: true
			rewards: [{
				id: "536FA73E85E1A9A1"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f8&r &4点伤害"
			tasks: [{
				id: "5CDEF9E938811FC8"
				item: {
					Count: 1
					id: "minecraft:netherite_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 8.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "3BF1900346772703"
			optional: true
			rewards: [{
				id: "33ADFAFC2A58E22E"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f等级2"
			tasks: [{
				id: "6D6CF45BE39E11BB"
				item: {
					Count: 1
					id: "minecraft:golden_pickaxe"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: -0.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "75238B267CBFFAFE"
			optional: true
			rewards: [{
				id: "108C748DB005633D"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f4.5&r &4点伤害"
			tasks: [{
				id: "6C29A4A606852691"
				item: {
					Count: 1
					id: "aiotbotania:livingwood_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.5d
			y: 4.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "04A8077171395133"
			optional: true
			rewards: [{
				id: "32D5D666757E4CDF"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f5.5&r &4点伤害"
			tasks: [{
				id: "36FE0FF2381C511A"
				item: {
					Count: 1
					id: "aiotbotania:livingrock_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.5d
			y: 5.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2FB62AE3C62CA052"
			optional: true
			rewards: [{
				id: "353B92E668DF254B"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f4.5&r &4点伤害"
			tasks: [{
				id: "271A6907F87F76B4"
				item: {
					Count: 1
					id: "blue_skies:comet_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -0.5d
			y: 4.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "077A214422FEFF02"
			optional: true
			rewards: [{
				id: "44C433B57CA339B1"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f6.4&r &4点伤害"
			tasks: [{
				id: "53CFFE444921CB11"
				item: {
					Count: 1
					id: "ae2:fluix_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -0.5d
			y: 6.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "665C3A14C987E9C6"
			optional: true
			rewards: [{
				id: "290A6FF547B1005B"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f5.5&r &4点伤害"
			tasks: [{
				id: "4AB69297368845A0"
				item: {
					Count: 1
					id: "blue_skies:pyrope_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -0.5d
			y: 5.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2664388EBA1E8F90"
			optional: true
			rewards: [{
				id: "2C7143146C70C6C0"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f8&r &4点伤害"
			tasks: [{
				id: "41D9CCC7AD3283EE"
				item: {
					Count: 1
					id: "mekanismtools:osmium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.0d
			y: 8.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "7F076FC4F2187692"
			optional: true
			rewards: [{
				id: "12CFEFB9FC68BD40"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f8&r &4点伤害"
			tasks: [{
				id: "49620BB635D10CF5"
				item: {
					Count: 1
					id: "mysticalagriculture:inferium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: 8.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "713803066B62CE9C"
			optional: true
			rewards: [{
				id: "67CF8E9D0ADAA6D6"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f6.5&r &4点伤害"
			tasks: [{
				id: "0E51C3384A52EEED"
				item: {
					Count: 1
					id: "railcraft:steel_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.5d
			y: 6.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "14518D5C1786DE65"
			optional: true
			rewards: [{
				id: "2FFE929DD6AB4E45"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f5&r &4点伤害"
			tasks: [{
				id: "75808D8AE827252E"
				item: {
					Count: 1
					id: "aether:holystone_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.0d
			y: 5.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "38C76A58EBED6C37"
			optional: true
			rewards: [{
				id: "2B6324DFC4772A89"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7.5&r &4伤害"
			tasks: [{
				id: "163BDCDFC0B39FB8"
				item: {
					Count: 1
					id: "twilightforest:ice_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -0.5d
			y: 7.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "7CFEE836C68B3903"
			optional: true
			rewards: [{
				id: "27CE1AC9F42F9781"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7.5&r &4伤害"
			tasks: [{
				id: "6ACBFDD957AE32EB"
				item: {
					Count: 1
					id: "undergarden:utherium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.5d
			y: 7.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2D0AFABAD80A1DB5"
			optional: true
			rewards: [{
				id: "7087CCC90CB08A89"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f10&r &4伤害"
			tasks: [{
				id: "0A81B5C9739FB083"
				item: {
					Count: 1
					id: "voidscape:corrupt_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.5d
			y: 9.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "0B6772E52FE97284"
			optional: true
			rewards: [{
				id: "6AD4330F557168CE"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f11&r &4伤害值"
			tasks: [{
				id: "51FB97EC1A4441B9"
				item: {
					Count: 1
					id: "voidscape:titanite_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 10.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "744DDB36296AEFAE"
			optional: true
			rewards: [{
				id: "07E442572E98E98D"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f13&r &4伤害值"
			tasks: [{
				id: "45F7BD53A56AF350"
				item: {
					Count: 1
					id: "voidscape:astral_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 11.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "7EDF2CC08FC774F9"
			optional: true
			rewards: [{
				id: "09446E7DBF4E04D2"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f10&r &4伤害值"
			tasks: [{
				id: "279E99110C6542B0"
				item: {
					Count: 1
					id: "mysticalagriculture:prudentium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -0.5d
			y: 9.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "680ECE3D8DCDC515"
			optional: true
			rewards: [{
				id: "5BDE840C501B7B5E"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f13&r &4伤害值"
			tasks: [{
				id: "1794270850D4F05D"
				item: {
					Count: 1
					id: "mysticalagriculture:tertium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: 11.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "6856A5572B239D10"
			optional: true
			rewards: [{
				id: "68A2DB9F4291B525"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f16&r &4伤害值"
			tasks: [{
				id: "3D0DC6C4794CE2BF"
				item: "allthemodium:allthemodium_sword"
				type: "item"
			}]
			x: -0.5d
			y: 11.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "4DCEC4B97E24B6F1"
			optional: true
			rewards: [{
				id: "0704EC6C6A576460"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f17&r &4伤害值"
			tasks: [{
				id: "75022D8AF7DB6369"
				item: {
					Count: 1
					id: "mysticalagriculture:imperium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -1.0d
			y: 12.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2007EF64F0406090"
			optional: true
			rewards: [{
				id: "6FA2F0200A92DCE9"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f24&r &4伤害值"
			tasks: [{
				id: "60ECBCB782EF3B95"
				item: "mysticalagriculture:supremium_sword"
				type: "item"
			}]
			x: -0.5d
			y: 13.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "5A6670364ADE0858"
			optional: true
			rewards: [{
				id: "2F5FDEFBEB9150F7"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f29&r &4伤害值"
			tasks: [{
				id: "5B39CDF9A0FCC084"
				item: "mysticalagriculture:awakened_supremium_sword"
				type: "item"
			}]
			x: -0.5d
			y: 14.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "47AED7219704EB3E"
			optional: true
			rewards: [{
				id: "37E00F3F444A939F"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f22&r &4伤害值"
			tasks: [{
				id: "7D7CDB9137482E36"
				item: "allthemodium:vibranium_sword"
				type: "item"
			}]
			x: -1.0d
			y: 13.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "744AC6BD82FC2DEE"
			optional: true
			rewards: [{
				id: "579F1B36D4F70550"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f28&r &4伤害值"
			tasks: [{
				id: "3D392B67BAAED4D0"
				item: "allthemodium:unobtainium_sword"
				type: "item"
			}]
			x: -1.0d
			y: 14.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "7BCE96070C36D547"
			optional: true
			rewards: [{
				id: "4CF759546A2F5935"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7.5&r &4伤害值"
			tasks: [{
				id: "2000C08E501E9216"
				item: {
					Count: 1
					id: "aquaculture:neptunium_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.5d
			y: 7.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2F90CA5DF9225209"
			optional: true
			rewards: [{
				id: "132896A455BD5017"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f12&r &4伤害值"
			tasks: [{
				id: "33868BAD67921CD5"
				item: {
					Count: 1
					id: "twilightforest:giant_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: -0.5d
			y: 10.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "31B0ADD444A593CD"
			optional: true
			rewards: [{
				id: "2F314B2B5F811641"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f10&r &4伤害值"
			tasks: [{
				id: "4414BD0C6D0C1692"
				item: {
					Count: 1
					id: "lost_aether_content:phoenix_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.5d
			y: 9.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "4C8C56F960D92E9D"
			optional: true
			rewards: [{
				id: "54CDEF7C7EEA1543"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f47&r &4伤害值"
			tasks: [{
				id: "571788A3D48695E9"
				item: "allthemodium:alloy_sword"
				type: "item"
			}]
			x: -1.0d
			y: 15.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "6EE68EEFE388CF8A"
			optional: true
			rewards: [{
				id: "61075B248913A2C9"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f4&r &4伤害值"
			tasks: [{
				id: "477AAD8E6A0BB944"
				item: {
					Count: 1
					id: "minecraft:golden_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: 4.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "25C1A764618E601B"
			optional: true
			rewards: [{
				id: "31F6A25E041C5EF9"
				type: "xp"
				xp: 10
			}]
			subtitle: "&9等级1"
			tasks: [{
				id: "079736CCF9AC8123"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "blue_skies:bluebright_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:starlit_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:frostbright_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:lunar_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:dusk_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:maple_pickaxe"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:comet_pickaxe"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d蔚蓝浩空&f &a木镐&f"
				type: "item"
			}]
			x: -0.5d
			y: 0.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "609D03E335CF32E2"
			optional: true
			rewards: [{
				id: "7BBF272CDE7721EA"
				type: "xp"
				xp: 10
			}]
			subtitle: "&d等级6"
			tasks: [{
				id: "0C40B254FF7C7636"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "allthemodium:allthemodium_pickaxe"
							}
							{
								Count: 1b
								id: "allthemodium:vibranium_pickaxe"
							}
							{
								Count: 1b
								id: "allthemodium:unobtainium_pickaxe"
							}
							{
								Count: 1b
								id: "allthemodium:alloy_pick"
							}
						]
					}
				}
				title: "ATM矿石与&a合金镐&f"
				type: "item"
			}]
			x: -1.0d
			y: -2.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "3FBCE2054D6038F8"
			optional: true
			rewards: [{
				id: "7E8C04449E72AAA3"
				type: "xp"
				xp: 10
			}]
			subtitle: "&b等级3"
			tasks: [{
				id: "78D7E717C343E174"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:aluminium_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 767
										ToolSpeed: 10.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:iron_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 255
										ToolSpeed: 6.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:bronze_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 191
										ToolSpeed: 7.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:invar_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									Enchantments: [{
										id: "minecraft:efficiency"
										lvl: 1s
									}]
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 383
										ToolSpeed: 8.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:sterling_silver_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 767
										ToolSpeed: 7.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:rose_gold_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									Enchantments: [{
										id: "minecraft:fortune"
										lvl: 2s
									}]
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 767
										ToolSpeed: 16.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:wrought_iron_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 383
										ToolSpeed: 6.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:cobalt_brass_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 1023
										ToolSpeed: 6.5f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:flint_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 2
										MaxDamage: 63
										ToolSpeed: 5.5f
									}
									HideFlags: 2
								}
							}
						]
					}
				}
				title: "&d格雷科技&f &a铁镐&f"
				type: "item"
			}]
			x: 1.5d
			y: -1.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "3FB589181C086B75"
			optional: true
			rewards: [{
				id: "1B820F84E342C526"
				type: "xp"
				xp: 10
			}]
			subtitle: "&5等级4"
			tasks: [{
				id: "29308D4A6CE0C509"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:titanium_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 1535
										ToolSpeed: 12.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:diamond_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 767
										ToolSpeed: 10.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:stainless_steel_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 1023
										ToolSpeed: 11.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:steel_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 511
										ToolSpeed: 9.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:damascus_steel_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									Enchantments: [{
										id: "minecraft:fortune"
										lvl: 3s
									}]
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 1023
										ToolSpeed: 10.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:vanadium_steel_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 1535
										ToolSpeed: 7.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:red_steel_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 2559
										ToolSpeed: 11.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:blue_steel_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 3
										MaxDamage: 1023
										ToolSpeed: 19.0f
									}
									HideFlags: 2
								}
							}
						]
					}
				}
				title: "格雷钻石镐"
				type: "item"
			}]
			x: 2.0d
			y: -1.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "5DB69A67A95C6B69"
			optional: true
			rewards: [{
				id: "0863C25172566342"
				type: "xp"
				xp: 10
			}]
			subtitle: "&d等级6"
			tasks: [{
				id: "5B5634EA99833425"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:neutronium_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: {
										RelocateMinedBlocks: 1b
									}
									GT.Tool: {
										Damage: 0
										HarvestLevel: 6
										MaxDamage: 65534
										ToolSpeed: 184.0f
									}
									HideFlags: 2
									Unbreakable: 1b
								}
							}
							{
								Count: 1b
								id: "gtceu:duranium_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: {
										RelocateMinedBlocks: 1b
									}
									GT.Tool: {
										Damage: 0
										HarvestLevel: 5
										MaxDamage: 8191
										ToolSpeed: 18.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:ultimet_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 4
										MaxDamage: 2047
										ToolSpeed: 14.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:tungsten_carbide_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 4
										MaxDamage: 1023
										ToolSpeed: 64.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:tungsten_steel_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 4
										MaxDamage: 2047
										ToolSpeed: 13.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:naquadah_alloy_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: {
										RelocateMinedBlocks: 1b
									}
									GT.Tool: {
										Damage: 0
										HarvestLevel: 5
										MaxDamage: 3071
										ToolSpeed: 44.0f
									}
									HideFlags: 2
								}
							}
							{
								Count: 1b
								id: "gtceu:hsse_pickaxe"
								tag: {
									DisallowContainerItem: 0b
									GT.Behaviours: { }
									GT.Tool: {
										Damage: 0
										HarvestLevel: 4
										MaxDamage: 3071
										ToolSpeed: 9.0f
									}
									HideFlags: 2
								}
							}
						]
					}
				}
				title: "&d格雷科技&f顶级镐"
				type: "item"
			}]
			x: 3.0d
			y: -2.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "384A7832DAE93E5A"
			optional: true
			rewards: [{
				id: "508C4E88B11C8CC5"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f4&r &4伤害值"
			tasks: [{
				id: "4D01DD196F609F50"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "blue_skies:bluebright_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:starlit_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:frostbright_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:lunar_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:dusk_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:maple_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d蔚蓝浩空&f &a木剑&f"
				type: "item"
			}]
			x: 1.0d
			y: 4.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "0F36FE9BCBA83129"
			optional: true
			rewards: [{
				id: "51D93891C77EB94B"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f5&r &4伤害值"
			tasks: [{
				id: "5DBA085EFD3EDBA7"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "blue_skies:turquoise_stone_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:lunar_stone_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d蔚蓝浩空&f &a石剑&f"
				type: "item"
			}]
			x: 2.0d
			y: 5.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2647B2830F8F022F"
			optional: true
			rewards: [{
				id: "0F668B05EED8873E"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f6&r &4伤害值"
			tasks: [{
				id: "2FE284174F29CEE6"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "ae2:certus_quartz_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "ae2:nether_quartz_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:refined_glowstone_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "mekanismtools:bronze_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "机械动力与AE2 &a铁剑&f"
				type: "item"
			}]
			x: 0.0d
			y: 6.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2ED7878B7919AF6D"
			optional: true
			rewards: [{
				id: "1D644A8D47B32BA6"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f6&r &4伤害值"
			tasks: [{
				id: "3A1AEA99F9234662"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "blue_skies:aquite_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "blue_skies:horizonite_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:manasteel_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:elementium_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d植物魔法&f and &d蔚蓝浩空&f &a铁剑&f"
				type: "item"
			}]
			x: 1.0d
			y: 6.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "191302E7A2670477"
			optional: true
			rewards: [{
				id: "1DAF2A99AE55BE2F"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7&r &4伤害值"
			tasks: [{
				id: "5B45FF3A6839E957"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "twilightforest:steeleaf_sword"
								tag: {
									Damage: 0
									Enchantments: [{
										id: "minecraft:looting"
										lvl: 2s
									}]
								}
							}
							{
								Count: 1b
								id: "twilightforest:knightmetal_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "twilightforest:fiery_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:terra_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "botania:star_sword"
								tag: {
									Damage: 0
									lastTriggerTime: 13134143L
								}
							}
							{
								Count: 1b
								id: "botania:thunder_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "&d植物魔法&f与暮色森林 &a钻石剑&f"
				type: "item"
			}]
			x: 1.0d
			y: 7.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "6F8935F23CBB19DE"
			optional: true
			rewards: [{
				id: "7CC10A728A08336B"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7&r &4伤害值"
			tasks: [{
				id: "45D22AB384D77465"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "aether:holy_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "aether:vampire_blade"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "aether:lightning_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "aether:flaming_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "undergarden:cloggrum_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "undergarden:forgotten_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "天境与深暗之园 &a钻石剑&f"
				type: "item"
			}]
			x: 2.0d
			y: 7.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "0D7FA4B9120DDE0E"
			optional: true
			rewards: [{
				id: "2B239B73B3916AAB"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f9&r &4伤害值"
			tasks: [{
				id: "130C8A47B1A57C48"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "blue_skies:diopside_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "naturesaura:depth_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "sgjourney:naquadah_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "锻造之剑"
				type: "item"
			}]
			x: 0.0d
			y: 9.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "782B6B849D28C9D8"
			optional: true
			rewards: [{
				id: "47FC4C58A69D446D"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f9&r &4伤害值"
			tasks: [{
				id: "160F2A806080C3D7"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "twilightdelight:teardrop_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "deeperdarker:warden_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "voidscape:voidic_crystal_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "探索模组之剑"
				type: "item"
			}]
			x: -1.0d
			y: 9.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "4FF76F6A2CE2A8D3"
			optional: true
			rewards: [{
				id: "4CDC85F46F666079"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f12&r &4伤害值"
			tasks: [{
				id: "68E94040D1688C61"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "mekanismtools:refined_obsidian_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "wstweaks:lava_blade"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "wstweaks:blaze_blade"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "铸剑工艺"
				type: "item"
			}]
			x: 1.5d
			y: 10.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "6F1A9A95F40F554D"
			optional: true
			rewards: [{
				id: "02E5FE8577E0DF38"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f12&r &4伤害值"
			tasks: [{
				id: "7D41B4F30D2D6A8C"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "forbidden_arcanus:draco_arcanus_sword"
								tag: {
									Damage: 0
								}
							}
							{
								Count: 1b
								id: "voidscape:ichor_sword"
								tag: {
									Damage: 0
								}
							}
						]
					}
				}
				title: "魔法剑刃"
				type: "item"
			}]
			x: 0.5d
			y: 10.5d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r创作,专用于AllTheMods整合包."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确许可,不得在任何非官方发布的公开整合包中使用本任务."
				""
				""
				""
				"该任务默认隐藏,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "639B291B873981DD"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "05828F635F0966DA"
					title: "全模组任务"
					type: "checkmark"
				}
				{
					id: "4AF4F930CB819414"
					title: "全模组任务"
					type: "checkmark"
				}
			]
			x: -1.5d
			y: 2.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "4F0D08798A3C3F4D"
			optional: true
			rewards: [{
				id: "78B92395A96D59CD"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7.88&r &4伤害值"
			tasks: [{
				id: "630F570E212DF235"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-227329855
									-169787229
									-**********
									**********
								]
							}
						}
					}
					id: "draconicevolution:draconic_sword"
				}
				type: "item"
			}]
			x: 2.5d
			y: 7.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "7FC12F6ECF006917"
			optional: true
			rewards: [{
				id: "0CDE4D22FD2A4776"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f17.5&r &4伤害值"
			tasks: [{
				id: "39220D02A268A932"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-972130503
									79448394
									-**********
									-282135181
								]
							}
						}
					}
					id: "draconicevolution:chaotic_sword"
				}
				type: "item"
			}]
			x: -0.5d
			y: 12.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "7A81BA9E13247B49"
			optional: true
			rewards: [{
				id: "275B6227664416F6"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f8.75&r &4伤害值"
			tasks: [{
				id: "624DA423C5413BF9"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-**********
									-**********
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:wyvern_sword"
				}
				type: "item"
			}]
			x: -0.5d
			y: 8.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "360DE1D2EC05931F"
			optional: true
			rewards: [{
				id: "3872FF8ECBFB882F"
				type: "xp"
				xp: 10
			}]
			subtitle: "&d等级6"
			tasks: [{
				id: "5CC0D27CD1902BC3"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								ForgeCaps: {
									Parent: {
										energy: {
											energy: 0
										}
										module_host: {
											modules: [ ]
											properties: {
												mining_speed: {
													hud: 1b
													value: 1.0d
												}
											}
											provider_id: [I;
												-**********
												**********
												-**********
												**********
											]
										}
									}
								}
								id: "draconicevolution:chaotic_pickaxe"
							}
							{
								Count: 1b
								ForgeCaps: {
									Parent: {
										energy: {
											energy: 0
										}
										module_host: {
											modules: [ ]
											properties: {
												mining_speed: {
													hud: 1b
													value: 1.0d
												}
											}
											provider_id: [I;
												996242180
												727992846
												-**********
												-**********
											]
										}
									}
								}
								id: "draconicevolution:draconic_pickaxe"
							}
							{
								Count: 1b
								ForgeCaps: {
									Parent: {
										energy: {
											energy: 0
										}
										module_host: {
											modules: [ ]
											properties: {
												mining_speed: {
													hud: 1b
													value: 1.0d
												}
											}
											provider_id: [I;
												657769730
												-**********
												-**********
												-**********
											]
										}
									}
								}
								id: "draconicevolution:wyvern_pickaxe"
							}
						]
					}
				}
				title: "&d龙之进化/龙之研究&f Pickaxes"
				type: "item"
			}]
			x: 4.0d
			y: -2.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "2D7A71D073AB0858"
			optional: true
			rewards: [{
				id: "7902279A74B88768"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f7&r &4伤害值"
			tasks: [{
				id: "2FE31B231DC2C034"
				item: {
					Count: 1
					id: "minecolonies:chiefsword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: 7.0d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "0AAF48DD0C557CF9"
			optional: true
			rewards: [{
				id: "5C0E5E865DCBB0A7"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f5.8&r &4伤害值"
			tasks: [{
				id: "39EB9CD1DEE828CA"
				item: {
					Count: 1
					id: "everythingcopper:copper_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 1.5d
			y: 5.5d
		}
		{
			dependencies: ["5151CDD8FCDE7A07"]
			id: "76B12D6E5A0230CE"
			optional: true
			rewards: [{
				id: "1D833B72479051E6"
				type: "xp"
				xp: 10
			}]
			subtitle: "&f5&r &4伤害值"
			tasks: [{
				id: "074C133F07A6CED9"
				item: {
					Count: 1
					id: "mekanismtools:lapis_lazuli_sword"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			x: 0.0d
			y: 5.0d
		}
	]
	title: "基础工具"
}
