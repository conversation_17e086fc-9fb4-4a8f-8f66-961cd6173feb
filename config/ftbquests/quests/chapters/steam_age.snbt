{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "steam_age"
	group: "1DA67E79B40AB130"
	icon: "gtceu:bronze_machine_casing"
	id: "435C9D14D471D326"
	order_index: 1
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			description: [
				"欢迎来到蒸汽时代!蒸汽机器可处理JEI中所有配方,最高支持&e&l32 EU/t(低压)&r."
				""
				"机器分为&a高压&f和低压两种版本.低压机器处理配方耗时翻倍,但蒸汽消耗量更低."
				""
				"&l&4注意:&r&r每次完成配方后,&n&e蒸汽机器需排出废蒸汽&r."
				""
				"默认排气口位于机器背面,但使用&b扳手&r可调整排气方向!"
				""
				"若排气口被堵塞,机器将停止运作."
			]
			icon: "alltheores:bronze_plate"
			id: "4DE719FC2E4C69AB"
			min_width: 250
			rewards: [{
				count: 4
				id: "11D5E446B48BCB06"
				item: "alltheores:bronze_plate"
				random_bonus: 8
				type: "item"
			}]
			size: 1.5d
			subtitle: "新时代的序幕"
			tasks: [{
				icon: "alltheores:bronze_plate"
				id: "107ADEF19CD50661"
				item: {
					Count: 1
					id: "itemfilters:tag"
					tag: {
						value: "forge:plates/bronze"
					}
				}
				title: "&a青铜板&f"
				type: "item"
			}]
			title: "&a蒸汽时代&f"
			x: -6.5d
			y: 0.0d
		}
		{
			dependencies: ["4DE719FC2E4C69AB"]
			description: [
				"向&e锅炉&r注入&b水&r并添加&e燃料&r,加热后即可产生&7蒸汽&r!"
				""
				"可使用任何水源,例如通过Pipez模组的&a流体管道&f连接&d自动饮水机&r."
				""
				"务必先注水再加热,向高温锅炉直接注水会导致&c&l爆炸&r."
				""
				"固体燃料锅炉使用煤炭等燃料,液体燃料锅炉则使用熔岩等流体燃料."
			]
			id: "672B308FD1DC0F45"
			min_width: 250
			rewards: [
				{
					id: "7987E2F8CE314F65"
					item: "minecraft:bricks"
					random_bonus: 2
					type: "item"
				}
				{
					count: 4
					id: "4A0AA6262881DDAC"
					item: "alltheores:bronze_plate"
					random_bonus: 4
					type: "item"
				}
			]
			subtitle: "蒸汽升腾"
			tasks: [{
				id: "2D783271D8830D0E"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "gtceu:lp_steam_solid_boiler"
							}
							{
								Count: 1b
								id: "gtceu:lp_steam_liquid_boiler"
							}
							{
								Count: 1b
								id: "gtceu:hp_steam_solid_boiler"
							}
							{
								Count: 1b
								id: "gtceu:hp_steam_liquid_boiler"
							}
						]
					}
				}
				title: "Any &a蒸汽锅炉&f"
				type: "item"
			}]
			title: "&a蒸汽锅炉&f"
			x: -4.0d
			y: 0.0d
		}
		{
			dependencies: ["672B308FD1DC0F45"]
			description: [
				"获得蒸汽后,你需要传输系统!"
				""
				"铺设这些管道可将锅炉产生的蒸汽输送至机器."
				""
				"使用&b扳手&r可随时增减连接节点."
			]
			id: "7F9131107E7F9AC8"
			rewards: [{
				count: 2
				id: "62C8841452E17861"
				item: "gtceu:bronze_small_fluid_pipe"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "3CF902E6B1C242C6"
				item: "gtceu:bronze_small_fluid_pipe"
				type: "item"
			}]
			x: -2.0d
			y: 0.0d
		}
		{
			dependencies: ["7F9131107E7F9AC8"]
			description: [
				"该机器能将金属锭转化为流体形态."
				""
				"还可将某些物品加工成浆料,例如将粘性树脂转化为生橡胶浆."
			]
			id: "48600733998CA349"
			rewards: [{
				exclude_from_claim_all: true
				id: "760218D52AB7047B"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			subtitle: "流体加工专家"
			tasks: [{
				id: "4A0FE858EDC3A2BA"
				item: "gtceu:lp_steam_extractor"
				type: "item"
			}]
			x: 0.0d
			y: 0.0d
		}
		{
			dependencies: ["7F9131107E7F9AC8"]
			description: [
				"粉碎机是矿物处理的第一步(后续流程稍后介绍)"
				""
				"现在我们需要木粉,放入木材即可研磨获取"
			]
			id: "0DA5BC8BA056B975"
			rewards: [{
				exclude_from_claim_all: true
				id: "530E6E0767EF6F79"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			subtitle: "破碎研磨"
			tasks: [{
				id: "47A54220BCA6BC2C"
				item: "gtceu:lp_steam_macerator"
				type: "item"
			}]
			x: 0.0d
			y: -2.0d
		}
		{
			dependencies: ["7F9131107E7F9AC8"]
			description: [
				"这台机器专精压缩工艺"
				""
				"可将植物材料压制成植物球(后续可能有用)"
				""
				"还能将&a木粉&f压缩成&a木板&f"
			]
			id: "60CD82B8DE0D0CFE"
			rewards: [{
				exclude_from_claim_all: true
				id: "4D54AC27062492A5"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			subtitle: "强力压制"
			tasks: [{
				id: "326CC07CDB0FAA28"
				item: "gtceu:lp_steam_compressor"
				type: "item"
			}]
			x: 0.0d
			y: -1.0d
		}
		{
			dependencies: ["7F9131107E7F9AC8"]
			description: [
				"&a锻造锤&f能将金属棒锻造成长金属棒,或将金属锭压制成金属板"
				""
				"后续将解锁更经济的替代方案"
			]
			id: "3FE956C2968896DC"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "4798DFD809E160E8"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			shape: "diamond"
			subtitle: "万物皆可锻"
			tasks: [{
				id: "731F6F20A3582633"
				item: "gtceu:lp_steam_forge_hammer"
				type: "item"
			}]
			x: -1.5d
			y: 2.0d
		}
		{
			dependencies: ["7F9131107E7F9AC8"]
			description: ["这台设备本质上是蒸汽驱动的熔炉"]
			id: "6F15619C8A07E78A"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "52B12E2432C25FC4"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			shape: "diamond"
			subtitle: "全能熔炉"
			tasks: [{
				id: "3820B5F114B12CD5"
				item: "gtceu:lp_steam_furnace"
				type: "item"
			}]
			x: -2.0d
			y: 2.5d
		}
		{
			dependencies: ["7F9131107E7F9AC8"]
			description: [
				"合金炉可冶炼多种材料合金"
				""
				"配合铸造模具能将材料定型,例如将玻璃粉制成玻璃管"
				""
				"模具可重复使用并根据产物更换"
				""
				"自动化生产时需移除模具,建议建造多台设备避免手动更换模具"
			]
			id: "6ECBB6F5D0D99DEE"
			rewards: [{
				exclude_from_claim_all: true
				id: "613040F2C64344D1"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			tasks: [{
				id: "59A7DF75092E335E"
				item: "gtceu:lp_steam_alloy_smelter"
				type: "item"
			}]
			x: 0.0d
			y: 1.0d
		}
		{
			dependencies: ["7F9131107E7F9AC8"]
			description: [
				"该机器通过两侧分别输入熔岩和水,可生成圆石或石头"
				""
				"高阶版本甚至能生成黑曜石(高压阶段解锁)"
			]
			id: "489B77B85B000B39"
			optional: true
			rewards: [{
				exclude_from_claim_all: true
				id: "4F255984F59E55A1"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			shape: "diamond"
			tasks: [{
				id: "4B1AA0FF29878B0E"
				item: "gtceu:lp_steam_rock_crusher"
				type: "item"
			}]
			x: -2.5d
			y: 2.0d
		}
		{
			dependencies: [
				"73FC1166FBD6C30D"
				"506360EEA2268E82"
				"1E811D532BC593F2"
				"49D740C5B5EB593C"
			]
			description: [
				"所有&aLV机器&f都需通过该电路解锁"
				""
				"建议保留蒸汽机器,它们在下一章节仍会派上用场"
				""
				"为何此任务需要32个电路？这涉及批量合成概念——提前制备大量组件比现用现做更高效"
				""
				"若你已实现自动化生产,很好!可使用&aME请求器&f(适用于AE2/RS系统)维持物品库存量,建议对橡胶、粘性树脂等合成组件启用此功能,并随章节推进逐步扩展"
			]
			id: "219C80DAFBAB36B8"
			min_width: 250
			rewards: [
				{
					count: 4
					id: "684F5DBBBF4DCC8C"
					item: "gtceu:tin_single_cable"
					random_bonus: 4
					type: "item"
				}
				{
					count: 8
					id: "6C6F241F645EC187"
					item: "gtceu:basic_electronic_circuit"
					type: "item"
				}
				{
					count: 2
					id: "05379BEF6C9E0574"
					item: "gtceu:lv_machine_hull"
					random_bonus: 2
					type: "item"
				}
			]
			shape: "gear"
			size: 1.5d
			subtitle: "蒸汽时代的终极挑战"
			tasks: [{
				count: 32L
				id: "74CFECDB943C6F6C"
				item: "gtceu:basic_electronic_circuit"
				type: "item"
			}]
			x: 5.0d
			y: 3.0d
		}
		{
			description: [
				"寻找并种植橡胶树获取粘性树脂"
				""
				"熔炉烧炼粘液球也可获得"
			]
			hide_dependency_lines: true
			id: "60069A897F2B0F78"
			rewards: [
				{
					id: "3D206EFD430BE865"
					item: "gtceu:rubber_sapling"
					random_bonus: 2
					type: "item"
				}
				{
					count: 4
					id: "3C7C21E0AD257BCE"
					item: "gtceu:sticky_resin"
					random_bonus: 4
					type: "item"
				}
				{
					count: 4
					id: "6CCB612632B6F1AA"
					item: "minecraft:slime_ball"
					random_bonus: 4
					type: "item"
				}
			]
			shape: "square"
			subtitle: "粘液农场？"
			tasks: [{
				id: "6D9971A5B71CBD4F"
				item: "gtceu:sticky_resin"
				type: "item"
			}]
			x: 4.0d
			y: 0.0d
		}
		{
			dependencies: [
				"48600733998CA349"
				"60069A897F2B0F78"
			]
			description: [
				"橡胶树所有部位都可提取生橡胶浆"
				""
				"粘液球同样适用!"
			]
			id: "0D3DF9C8F742AE0D"
			rewards: [{
				count: 3
				id: "7A37EBF9E1EA60D7"
				item: "gtceu:raw_rubber_dust"
				random_bonus: 6
				type: "item"
			}]
			tasks: [{
				id: "0C9EB5A9C4F84107"
				item: "gtceu:raw_rubber_dust"
				type: "item"
			}]
			x: 1.0d
			y: 0.0d
		}
		{
			dependencies: ["6ECBB6F5D0D99DEE"]
			description: [
				"用锤子粉碎玻璃,将玻璃粉与铸模合金熔炼制成"
				""
				"钢锭可通过烧炼钢粉获得,钢粉由铁粉与煤混合制成"
			]
			icon: "gtceu:glass_tube"
			id: "7A62C1B9385DF643"
			rewards: [{
				count: 2
				id: "094E4ABC1D1CF926"
				item: "gtceu:glass_dust"
				random_bonus: 2
				type: "item"
			}]
			tasks: [
				{
					id: "7D6A7F80D911E032"
					item: "gtceu:ball_casting_mold"
					type: "item"
				}
				{
					id: "6EB6D2BE3ADAC972"
					item: "gtceu:glass_tube"
					type: "item"
				}
			]
			title: "&a玻璃管&f"
			x: 0.0d
			y: 3.0d
		}
		{
			dependencies: ["7A62C1B9385DF643"]
			description: ["还有人记得老式CRT电视吗？那些设备使用真空管"]
			id: "506360EEA2268E82"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "2DF5FF7E955C0404"
					table_id: 4444697382338980938L
					type: "loot"
				}
				{
					count: 2
					id: "11BFF788180FEAFC"
					item: "gtceu:steel_bolt"
					random_bonus: 4
					type: "item"
				}
			]
			tasks: [{
				id: "27C43F4FBE72C8EA"
				item: "gtceu:vacuum_tube"
				type: "item"
			}]
			x: 3.5d
			y: 3.0d
		}
		{
			dependencies: ["60069A897F2B0F78"]
			description: ["无需担心,电阻器不需区分阻值制作"]
			id: "73FC1166FBD6C30D"
			rewards: [
				{
					exclude_from_claim_all: true
					id: "47EBCBABACD056C8"
					table_id: 4444697382338980938L
					type: "loot"
				}
				{
					count: 4
					id: "00E177F0C36E89F9"
					item: "gtceu:copper_single_wire"
					random_bonus: 4
					type: "item"
				}
			]
			subtitle: "欧姆定律"
			tasks: [{
				id: "64598EA96BFA992A"
				item: "gtceu:resistor"
				type: "item"
			}]
			x: 4.0d
			y: 1.0d
		}
		{
			dependencies: [
				"0D3DF9C8F742AE0D"
				"6ECBB6F5D0D99DEE"
			]
			description: [
				"硫矿石&a生成于下界&f,烧炼获得硫粉.也可通过蜂箱获取!"
				""
				"将生橡胶浆与硫粉放入合金炉,制成初级橡胶"
			]
			icon: "gtceu:rubber_ingot"
			id: "71B1416A45FBBE40"
			rewards: [
				{
					id: "785274FE73892E39"
					item: "gtceu:rubber_ingot"
					random_bonus: 2
					type: "item"
				}
				{
					id: "4F57BD95D084CD38"
					item: "chemlib:sulfur_dust"
					random_bonus: 2
					type: "item"
				}
			]
			tasks: [
				{
					id: "48C19477CB216AD4"
					item: {
						Count: 1
						id: "itemfilters:tag"
						tag: {
							value: "forge:dusts/sulfur"
						}
					}
					title: "&a硫粉&f"
					type: "item"
				}
				{
					id: "7E516EDDAA975CC8"
					item: "gtceu:rubber_ingot"
					type: "item"
				}
			]
			title: "橡胶"
			x: 1.0d
			y: 1.0d
		}
		{
			dependencies: [
				"60CD82B8DE0D0CFE"
				"30DA40DA0CEB05A0"
			]
			description: ["将木粉压缩成板材"]
			id: "57B4881AA36A1D76"
			rewards: [{
				id: "668BD43798C5A151"
				item: "gtceu:wood_plate"
				random_bonus: 2
				type: "item"
			}]
			subtitle: "这是胶合板吗？"
			tasks: [{
				id: "61098A8012A228F5"
				item: "gtceu:wood_plate"
				type: "item"
			}]
			x: 1.0d
			y: -1.0d
		}
		{
			dependencies: ["0DA5BC8BA056B975"]
			description: ["粉碎&a原木&f可获得大量木粉"]
			id: "30DA40DA0CEB05A0"
			rewards: [{
				count: 2
				id: "10836E8996839998"
				item: "gtceu:wood_dust"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "0160C43AF8C68BDA"
				item: "gtceu:wood_dust"
				type: "item"
			}]
			x: 1.0d
			y: -2.0d
		}
		{
			dependencies: ["71B1416A45FBBE40"]
			description: [
				"两个橡胶锭配合铸模合金熔炼可制成橡胶板"
				""
				"后续包覆导线制作电缆时会用到"
			]
			icon: "gtceu:rubber_plate"
			id: "6CE157D05F59A994"
			rewards: [{
				id: "49F77B7C44DA42D0"
				item: "gtceu:rubber_ingot"
				random_bonus: 2
				type: "item"
			}]
			tasks: [
				{
					id: "61B056192AC9B3D6"
					item: "gtceu:rubber_plate"
					type: "item"
				}
				{
					id: "11BCA8CFBA47D094"
					item: "gtceu:plate_casting_mold"
					type: "item"
				}
			]
			x: 2.0d
			y: 1.0d
		}
		{
			dependencies: [
				"60069A897F2B0F78"
				"57B4881AA36A1D76"
			]
			id: "3BEE6BE4F91FA1B0"
			rewards: [{
				id: "291A0BDB657730E2"
				item: "gtceu:resin_circuit_board"
				random_bonus: 2
				type: "item"
			}]
			tasks: [{
				id: "1F521DD245848DC5"
				item: "gtceu:resin_circuit_board"
				type: "item"
			}]
			x: 5.0d
			y: -1.0d
		}
		{
			dependencies: ["3BEE6BE4F91FA1B0"]
			description: ["还以为我们会用面包板呢"]
			id: "1E811D532BC593F2"
			rewards: [{
				exclude_from_claim_all: true
				id: "0AF278948C957669"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			tasks: [{
				id: "6159C8627548752D"
				item: "gtceu:resin_printed_circuit_board"
				type: "item"
			}]
			x: 5.0d
			y: 0.5d
		}
		{
			dependencies: ["4DE719FC2E4C69AB"]
			description: [
				"比起手动合成板材导线,自动化生产更高效"
				""
				"&5应用能源2&r开启\"使用替代方案\"后效果更佳"
				"&e注意:&r若频道受限,可用指令设置4倍或无限频道模式(需OP权限或开启作弊)"
				"&o/ae2 频道模式 x4"
				"&o/ae2 频道模式 无限"
				""
				"&b&d精致存储&f&r因NBT限制无法重复使用工具"
				""
				"可改用&a&dRF工具箱&f合成器实现自动化"
				""
				"强烈推荐自动化生产,特别是涉及流体的环节"
				""
				"设置&eLV+机器&f自动回传至模式供应器时,务必在GUI中&a开启&f\"允许输入侧输出\".蒸汽机器需额外&c导入&r系统"
			]
			id: "12F916CDC2FB7A79"
			min_width: 350
			rewards: [{
				id: "31599B5243F888C0"
				type: "xp"
				xp: 10
			}]
			tasks: [{
				id: "6C841FAADFD094AC"
				title: "自动化"
				type: "checkmark"
			}]
			x: -6.5d
			y: -2.0d
		}
		{
			dependencies: ["6ECBB6F5D0D99DEE"]
			description: ["在&e&a合金炉&f&r中使用一个&e&a铜锭&f&r和四个&c&a红石粉&f&r可以制作出这种可爱的金属锭"]
			id: "3302A9306CAD659A"
			rewards: [{
				exclude_from_claim_all: true
				id: "3B3B4D9925B69097"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			tasks: [{
				id: "4F21E8821A465876"
				item: "gtceu:red_alloy_ingot"
				type: "item"
			}]
			x: 1.0d
			y: 2.0d
		}
		{
			dependencies: [
				"3302A9306CAD659A"
				"6CE157D05F59A994"
			]
			description: ["&a红色合金&f导线是制作&b低压电路&r并脱离蒸汽时代的关键组件"]
			id: "49D740C5B5EB593C"
			rewards: [{
				exclude_from_claim_all: true
				id: "393B393299331031"
				table_id: 4444697382338980938L
				type: "loot"
			}]
			tasks: [{
				id: "31DEB8892D2B486F"
				item: "gtceu:red_alloy_single_cable"
				type: "item"
			}]
			x: 3.0d
			y: 2.0d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r为AllTheMods整合包创作."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"此任务默认隐藏,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "50F07315C26E42C1"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "33EA567BC103D960"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
				{
					id: "215526A434E68880"
					title: "任务由AllTheMods提供"
					type: "checkmark"
				}
			]
			x: -6.5d
			y: 2.0d
		}
	]
	title: "蒸汽时代"
}
