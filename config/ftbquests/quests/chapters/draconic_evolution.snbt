{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "draconic_evolution"
	group: "2B51AC12041E3F89"
	icon: "draconicevolution:draconium_core"
	id: "0B826D13BAD43EEB"
	images: [
		{
			height: 3.0d
			image: "atm:textures/questpics/draconic/draconic_title.png"
			rotation: 0.0d
			width: 8.019230769230768d
			x: 6.5d
			y: -15.5d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/draconic/draconic_core_1on.png"
			rotation: 0.0d
			width: 2.0089285714285716d
			x: -4.0d
			y: -0.5d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/draconic/draconic_core_8on.png"
			rotation: 0.0d
			width: 5.038167938931297d
			x: -4.0d
			y: 3.5d
		}
		{
			height: 2.0d
			image: "atm:textures/questpics/draconic/draconic_energy.png"
			rotation: 0.0d
			width: 4.1257142857142854d
			x: 6.0d
			y: 4.5d
		}
		{
			height: 5.0d
			image: "atm:textures/questpics/draconic/draconic_reactor_off_down.png"
			rotation: 0.0d
			width: 11.26126126126126d
			x: 13.0d
			y: 8.0d
		}
		{
			height: 3.0d
			image: "atm:textures/questpics/draconic/draconic_fusion.png"
			rotation: 0.0d
			width: 9.072463768115941d
			x: 11.5d
			y: -7.5d
		}
	]
	order_index: 3
	progression_mode: "flexible"
	quest_links: [ ]
	quests: [
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["&a能量模块&f是最重要的模块,使用龙之进化物品必备.其容量决定物品可存储/吸收的OP值,详情请查看&a能量注入器&f.&a能量模块&f等级越高,物品能存储/使用的OP值越多——这点很重要,因为物品升级后每次使用都会消耗更多OP."]
			id: "574E5806D1A98FE5"
			rewards: [{
				exclude_from_claim_all: true
				id: "5A1B214896CADF59"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "1D76A5C236FC8717"
				item: "draconicevolution:item_draconium_energy"
				type: "item"
			}]
			title: "基础&a能量模块&f"
			x: 17.25d
			y: -12.0d
		}
		{
			dependencies: ["574E5806D1A98FE5"]
			id: "120B403D56EE5C79"
			rewards: [{
				exclude_from_claim_all: true
				id: "6AC58363EDEC7B2A"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "10764264F36D3ED8"
				item: "draconicevolution:item_wyvern_energy"
				type: "item"
			}]
			title: "&a飞龙能量模块&f"
			x: 18.0d
			y: -12.0d
		}
		{
			dependencies: ["120B403D56EE5C79"]
			id: "5021D159A5EC2315"
			rewards: [{
				exclude_from_claim_all: true
				id: "01AF0C1D9B9EB1E0"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "30574D39064813B9"
				item: "draconicevolution:item_draconic_energy"
				type: "item"
			}]
			title: "&a神龙能量模块&f"
			x: 18.75d
			y: -12.0d
		}
		{
			dependencies: ["5021D159A5EC2315"]
			id: "54F78C9A945CF0E7"
			rewards: [{
				exclude_from_claim_all: true
				id: "7A2728A923F439CD"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "7EEBD2FBBE9762E8"
				item: "draconicevolution:item_chaotic_energy"
				type: "item"
			}]
			title: "&a混沌能量模块&f"
			x: 19.5d
			y: -12.0d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["&a速度模块&f非常实用,几乎适用于所有物品并能加速操作!剑挥砍更快、镐挖掘更快、斧砍伐更快、弓射击更快,胸甲还能提升移速.&a速度模块&f等级越高加速效果越强!(顺带一提,多数属性可叠加,两个基础&a速度模块&f就能提升20%%速度!)"]
			id: "7C54581562D2EA75"
			rewards: [{
				exclude_from_claim_all: true
				id: "4BB6811D3AF64A43"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "02D424E19C5B49D3"
				item: "draconicevolution:item_draconium_speed"
				type: "item"
			}]
			title: "基础&a速度模块&f"
			x: 17.25d
			y: -10.5d
		}
		{
			dependencies: ["7C54581562D2EA75"]
			id: "2AA08313F927981E"
			rewards: [{
				exclude_from_claim_all: true
				id: "41BA34EC0065F8A8"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2E73CED80A975B02"
				item: "draconicevolution:item_wyvern_speed"
				type: "item"
			}]
			title: "&a飞龙速度模块&f"
			x: 18.0d
			y: -10.5d
		}
		{
			dependencies: ["2AA08313F927981E"]
			id: "28AF8DD34412CBAA"
			rewards: [{
				exclude_from_claim_all: true
				id: "34CBBBF2D6236D77"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2335FC0A8CDCE48C"
				item: "draconicevolution:item_draconic_speed"
				type: "item"
			}]
			title: "&a神龙速度模块&f"
			x: 18.75d
			y: -10.5d
		}
		{
			dependencies: ["28AF8DD34412CBAA"]
			id: "2A5C5B44E9643A5D"
			rewards: [{
				exclude_from_claim_all: true
				id: "0108EA0A77EAD8AA"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "77F5E176FDD21534"
				item: "draconicevolution:item_chaotic_speed"
				type: "item"
			}]
			title: "&a混沌速度模块&f"
			x: 19.5d
			y: -10.5d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["想让护盾随时恢复(而非濒死时才生效)？你需要护盾恢复模块.安装&a多个&f模块可加快恢复速度."]
			id: "4CD2ADD3601B8191"
			rewards: [{
				exclude_from_claim_all: true
				id: "549DB2B92186656C"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "4DB67015C52358FC"
				item: "draconicevolution:item_wyvern_shield_recovery"
				type: "item"
			}]
			title: "&a飞龙护盾恢复模块&f"
			x: 18.0d
			y: -0.75d
		}
		{
			dependencies: ["4CD2ADD3601B8191"]
			id: "594BBD31461F212D"
			rewards: [{
				exclude_from_claim_all: true
				id: "2FE573139A791AE8"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "56B97A32636D2195"
				item: "draconicevolution:item_draconic_shield_recovery"
				type: "item"
			}]
			title: "&a神龙护盾恢复模块&f"
			x: 18.75d
			y: -0.75d
		}
		{
			dependencies: ["594BBD31461F212D"]
			id: "0AE8B51D63327EC3"
			rewards: [{
				exclude_from_claim_all: true
				id: "5A73D88E2CF02062"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2727668096263E0F"
				item: "draconicevolution:item_chaotic_shield_recovery"
				type: "item"
			}]
			title: "&a混沌护盾恢复模块&f"
			x: 19.5d
			y: -0.75d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["胸甲机制类似Meka套装:所有伤害会先由护盾值抵消.护盾容量决定护盾上限,恢复速度决定充能效率,充能时会消耗OP值."]
			id: "37CC51B689CB8A40"
			rewards: [{
				exclude_from_claim_all: true
				id: "389CD1FD2E15FCE4"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "43159CD00B790742"
				item: "draconicevolution:item_wyvern_shield_capacity"
				type: "item"
			}]
			title: "&a飞龙护盾容量模块&f"
			x: 18.0d
			y: -2.25d
		}
		{
			dependencies: ["37CC51B689CB8A40"]
			id: "76064465104B1C28"
			rewards: [{
				exclude_from_claim_all: true
				id: "013043A10A13DD74"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "701E53B2A54779D9"
				item: "draconicevolution:item_draconic_shield_capacity"
				type: "item"
			}]
			title: "&a神龙护盾容量模块&f"
			x: 18.75d
			y: -2.25d
		}
		{
			dependencies: ["76064465104B1C28"]
			id: "4A3A5684302C8627"
			rewards: [{
				exclude_from_claim_all: true
				id: "25AA36E7E9137AF5"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2FA9F37D667C5CCD"
				item: "draconicevolution:item_chaotic_shield_capacity"
				type: "item"
			}]
			title: "&a混沌护盾容量模块&f"
			x: 19.5d
			y: -2.25d
		}
		{
			dependencies: ["37CC51B689CB8A40"]
			description: ["效果同前序任务,但容量大幅提升."]
			id: "3CB8F5F1D33C8F9F"
			rewards: [{
				exclude_from_claim_all: true
				id: "545A6DF1312F86BF"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2CD48906720862D1"
				item: "draconicevolution:item_wyvern_large_shield_capacity"
				type: "item"
			}]
			title: "&a飞龙大护盾容量模块&f"
			x: 18.0d
			y: -1.5d
		}
		{
			dependencies: [
				"3CB8F5F1D33C8F9F"
				"76064465104B1C28"
			]
			id: "4F6A9F1AEFD535F5"
			rewards: [{
				exclude_from_claim_all: true
				id: "06691905C6FA5052"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "501BB3832EEE40E1"
				item: "draconicevolution:item_draconic_large_shield_capacity"
				type: "item"
			}]
			title: "&a神龙大护盾容量模块&f"
			x: 18.75d
			y: -1.5d
		}
		{
			dependencies: [
				"4F6A9F1AEFD535F5"
				"4A3A5684302C8627"
			]
			id: "7081E9BF44EFBCB0"
			rewards: [{
				exclude_from_claim_all: true
				id: "66BEFEE65CB095EB"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "74CF4D2E4CBD3637"
				item: "draconicevolution:item_chaotic_large_shield_capacity"
				type: "item"
			}]
			title: "&a混沌大护盾容量模块&f"
			x: 19.5d
			y: -1.5d
		}
		{
			dependencies: [
				"4EFFE300EC781C3E"
				"37CC51B689CB8A40"
			]
			description: ["不朽模块相当于复活图腾!当生命值归零时,模块会激活并提供吸收与生命恢复效果拯救你.每次激活后有冷却时间,且需消耗OP值重新充能.在此期间请尽量避免死亡!升级模块可缩短冷却时间."]
			id: "16FF2219F110F50A"
			rewards: [{
				exclude_from_claim_all: true
				id: "6ABAA8806E74DFBA"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "33FAF1015478CB17"
				item: "draconicevolution:item_wyvern_undying"
				type: "item"
			}]
			title: "飞龙不朽模块"
			x: 18.0d
			y: -3.0d
		}
		{
			dependencies: [
				"16FF2219F110F50A"
				"76064465104B1C28"
			]
			id: "542AD6FD1927C3E6"
			rewards: [{
				exclude_from_claim_all: true
				id: "53EBC8782D6FE00F"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "20B4C158C9DD7EA2"
				item: "draconicevolution:item_draconic_undying"
				type: "item"
			}]
			title: "神龙不朽模块"
			x: 18.75d
			y: -3.0d
		}
		{
			dependencies: [
				"542AD6FD1927C3E6"
				"4A3A5684302C8627"
			]
			id: "52EA9793A7100E75"
			rewards: [{
				exclude_from_claim_all: true
				id: "4A483E34ADE96759"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "309835FD9511F154"
				item: "draconicevolution:item_chaotic_undying"
				type: "item"
			}]
			title: "混沌不朽模块"
			x: 19.5d
			y: -3.0d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["&a作用范围&f是玩家熟悉的术语.它能提升工具的作用范围:3x3范围的镐能同时挖掘3x3区域,3x3范围的剑能击杀范围内所有怪物.模块等级越高作用范围越大.注意:该效果不可叠加!"]
			id: "7143CE930D08F160"
			rewards: [{
				exclude_from_claim_all: true
				id: "290B7A9895407C81"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "3322B804D048BF30"
				item: "draconicevolution:item_draconium_aoe"
				type: "item"
			}]
			title: "基础范围效果模块"
			x: 17.25d
			y: -6.0d
		}
		{
			dependencies: ["7143CE930D08F160"]
			id: "165030137EF59299"
			rewards: [{
				exclude_from_claim_all: true
				id: "43400A673B6F46D0"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "4F825AF56523DA5E"
				item: "draconicevolution:item_wyvern_aoe"
				type: "item"
			}]
			title: "飞龙范围效果模块"
			x: 18.0d
			y: -6.0d
		}
		{
			dependencies: ["165030137EF59299"]
			id: "655BE42CD5BB9D82"
			rewards: [{
				exclude_from_claim_all: true
				id: "034E3DC99536CD11"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2ACB040E880AA278"
				item: "draconicevolution:item_draconic_aoe"
				type: "item"
			}]
			title: "龙之范围效果模块"
			x: 18.75d
			y: -6.0d
		}
		{
			dependencies: ["655BE42CD5BB9D82"]
			id: "33D2901594655A77"
			rewards: [{
				exclude_from_claim_all: true
				id: "1C07185ABA183179"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "22158164E32379BE"
				item: "draconicevolution:item_chaotic_aoe"
				type: "item"
			}]
			title: "混沌范围效果模块"
			x: 19.5d
			y: -6.0d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["护盾控制器可以改变你当前的护盾状态及其恢复机制."]
			id: "1A53B6544782442E"
			rewards: [{
				exclude_from_claim_all: true
				id: "6C5FDC9D75A55BDE"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "51EA851DDB2779B9"
				item: "draconicevolution:item_wyvern_shield_control"
				type: "item"
			}]
			title: "&a飞龙护盾控制模块&f"
			x: 18.0d
			y: 0.0d
		}
		{
			dependencies: ["1A53B6544782442E"]
			id: "2158A566C2F54AA9"
			rewards: [{
				exclude_from_claim_all: true
				id: "3FA8512E8EE880E9"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "568B97C70C8462DD"
				item: "draconicevolution:item_draconic_shield_control"
				type: "item"
			}]
			title: "&a神龙护盾控制模块&f"
			x: 18.75d
			y: 0.0d
		}
		{
			dependencies: ["2158A566C2F54AA9"]
			id: "0E487EACA837EA9B"
			rewards: [{
				exclude_from_claim_all: true
				id: "08F5AD5442F36A79"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "1FC237D67BA936FA"
				item: "draconicevolution:item_chaotic_shield_control"
				type: "item"
			}]
			title: "&a混沌护盾控制模块&f"
			x: 19.5d
			y: 0.0d
		}
		{
			dependencies: ["5C2CF51D28F84CBE"]
			description: [
				"击败后将掉落经验值、&a龙之心&f,最重要的是释放&a混沌碎片&f.观察闪电落点并向下挖掘,即可找到&a混沌碎片&f.采集后可获得5个&a混沌碎片&f,可合成更小的组件!"
				"{image:atm:textures/questpics/draconic/draconic_shard.png width:150 height:100 align:1}"
			]
			id: "6B8F2E05429C185F"
			rewards: [{
				count: 3
				id: "559928E5B60D764F"
				item: "draconicevolution:chaos_shard"
				type: "item"
			}]
			shape: "diamond"
			size: 1.3d
			tasks: [{
				count: 5L
				id: "0D221A5709E45811"
				item: { Count: 5, id: "draconicevolution:chaos_shard" }
				type: "item"
			}]
			title: "&a混沌碎片&f{s}"
			x: 0.5d
			y: -6.0d
		}
		{
			description: ["进入&d龙之进化/龙之研究&f需要获取&a混沌碎片&f.这些碎片位于末地的&a混沌岛屿&f上,由&a混沌守卫&f看守.前往末地坐标X/Z为10k倍数的位置,如10k,10k / 20k,20k等."]
			icon: "allthetweaks:mini_end"
			id: "45EE60207C466D6C"
			rewards: [{
				exclude_from_claim_all: true
				id: "20A35C6F29F30427"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "rsquare"
			size: 1.5d
			subtitle: "寻找混沌岛屿"
			tasks: [{
				dimension: "minecraft:the_end"
				id: "2D70A09F9ACCE52B"
				type: "dimension"
			}]
			title: "他在哪里？"
			x: 0.5d
			y: -12.0d
		}
		{
			dependencies: ["45EE60207C466D6C"]
			description: [
				"战斗的第一阶段需要破坏维持&a混沌守卫&f护盾的水晶.在摧毁这些水晶前,你无法对其造成伤害.若无龙之武器,摧毁这些水晶绝非易事.&a混沌守卫&f发射的火球能削弱护盾,助你将其击破(没错,它需要朝你射击/击中水晶).当护盾可被破坏时,你会注意到粒子效果,受到攻击时会发出声响.护盾越接近崩溃,声响越低沉,水晶也会愈发暗淡.一旦护盾瓦解,你就能真正伤害到它了!"
				"{image:atm:textures/questpics/draconic/draconic_end_crystal.png width:150 height:100 align:1}"
			]
			icon: {
				Count: 1
				id: "draconicevolution:mob_soul"
				tag: {
					EntityName: "draconicevolution:guardian_crystal"
				}
			}
			id: "106D08542AAFA166"
			rewards: [{
				exclude_from_claim_all: true
				id: "6F1E69A5F6F19013"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			tasks: [{
				id: "6A516E772C246DFE"
				item: "draconicevolution:infused_obsidian"
				type: "item"
			}]
			title: "定位并摧毁14个&a守卫水晶&f"
			x: 0.5d
			y: -10.0d
		}
		{
			dependencies: ["45EE60207C466D6C"]
			description: ["在前往混沌岛屿前务必做好战备.虽然外形类似&a末影龙&f,但混沌守卫的难度远超想象.建议使用高伤害的龙系武器/工具,并配备无法获取合金护甲或MEKA套装.再生、吸收和抗性效果能显著提升生存率.若准备不足,随时可以撤退改日再战."]
			icon: {
				Count: 1
				id: "botania:cosmetic_questgiver_mark"
				tag: { }
			}
			id: "0000A88BB40B2149"
			optional: true
			rewards: [{
				id: "334B1BFA0DE0D94B"
				item: {
					Count: 1
					id: "minecraft:potion"
					tag: {
						Potion: "apotheosis:long_resistance"
					}
				}
				type: "item"
			}]
			tasks: [{
				id: "6EAC5BBFAA0184BC"
				title: "战前准备"
				type: "checkmark"
			}]
			title: "战斗艰难,需要充分准备"
			x: 2.0d
			y: -12.0d
		}
		{
			dependencies: ["106D08542AAFA166"]
			description: [
				"真正的战斗现在开始!它拥有两条生命值,必须全部归零才能获得&a混沌碎片&f.别靠得太近,否则会遭到一连串火球轰击!(注:旧版本中&a混沌守卫&f甚至能杀死创造模式的玩家!所幸现在不行了)"
				"{image:atm:textures/questpics/draconic/draconic_guardian.png width:150 height:100 align:1}"
			]
			icon: {
				Count: 1
				id: "draconicevolution:mob_soul"
				tag: {
					EntityName: "draconicevolution:draconic_guardian"
				}
			}
			icon_scale: 2.0d
			id: "5C2CF51D28F84CBE"
			rewards: [
				{
					id: "60E48A4C376F2739"
					type: "xp_levels"
					xp_levels: 20
				}
				{
					exclude_from_claim_all: true
					id: "282B4FD146E06EE4"
					table_id: 7145388980997284804L
					type: "random"
				}
				{
					exclude_from_claim_all: true
					id: "62CE93FB0A7817BF"
					table_id: 7145388980997284804L
					type: "random"
				}
				{
					exclude_from_claim_all: true
					id: "3D0F7E5B7E5F012B"
					table_id: 7145388980997284804L
					type: "random"
				}
			]
			shape: "rsquare"
			size: 1.3d
			tasks: [{
				entity: "draconicevolution:draconic_guardian"
				id: "3AFB2998606E4212"
				type: "kill"
				value: 1L
			}]
			title: "击杀&a混沌守卫&f"
			x: 0.5d
			y: -8.0d
		}
		{
			description: ["&a能量核心&f堪称Minecraft最强大的能量存储装置.核心方块作为多方块结构的中枢(需悬空放置),其GUI界面包含建造指南、降级/升级选项及&a组装核心&f功能.&a连接器&f决定核心的规模与功率,&a建造指南&f展示当前层级的搭建方案.备齐材料后点击&a组装核心&f即可自动完成建造!"]
			id: "7F757CD6F8C57733"
			rewards: [{
				id: "720F8AFF5D480BCA"
				item: "draconicevolution:draconium_block"
				type: "item"
			}]
			shape: "square"
			subtitle: "龙之&a能量存储&f"
			tasks: [{
				id: "7433809103907ECE"
				item: "draconicevolution:energy_core"
				type: "item"
			}]
			title: "&a能量核心&f多方块结构"
			x: 0.5d
			y: 1.5d
		}
		{
			dependencies: [
				"7F757CD6F8C57733"
				"5443E15E226DFC86"
				"421954D7D46FAAD6"
			]
			description: [
				"一级仅需核心和4个稳定器即可制作."
				"{image:atm:textures/questpics/draconic/draconic_core_1off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_1on.png width:100 height:100 align:1}"
			]
			icon: "minecraft:cobblestone"
			id: "389655CD41C7A691"
			shape: "square"
			subtitle: "&a储能容量&f4550万RF"
			tasks: [{
				count: 4L
				id: "13DD9FFA114EF17D"
				item: "draconicevolution:energy_core_stabilizer"
				type: "item"
			}]
			title: "第一阶段"
			x: 0.5d
			y: 0.5d
		}
		{
			dependencies: ["389655CD41C7A691"]
			description: [
				"{atm9.quest.draconic.desc.2)"
				"{image:atm:textures/questpics/draconic/draconic_core_2off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_2on.png width:100 height:100 align:1}"
			]
			icon: "minecraft:iron_ingot"
			id: "35767977FB9E0B1B"
			rewards: [{
				count: 3
				id: "0984BF62D5AC6568"
				item: "draconicevolution:draconium_block"
				type: "item"
			}]
			shape: "square"
			subtitle: "&a储能容量&f2.73亿RF"
			tasks: [
				{
					count: 4L
					id: "0EFC269472493100"
					item: "draconicevolution:energy_core_stabilizer"
					type: "item"
				}
				{
					count: 6L
					id: "21075B38EDF2D46C"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
			]
			title: "第二阶段"
			x: 0.5d
			y: -0.5d
		}
		{
			dependencies: ["35767977FB9E0B1B"]
			description: [
				"三级需要26个&a龙锭&f方块完全包裹核心."
				"{image:atm:textures/questpics/draconic/draconic_core_3off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_3on.png width:100 height:100 align:1}"
			]
			icon: "minecraft:gold_ingot"
			id: "713D3B3954E58C4A"
			rewards: [
				{
					count: 5
					id: "0E9FBDF13E433F68"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 3
					id: "12DAC8DCDD2475FB"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			shape: "square"
			subtitle: "&a储能容量&f16.4亿RF"
			tasks: [
				{
					count: 4L
					id: "28EA4F8DA9D5D693"
					item: "draconicevolution:energy_core_stabilizer"
					type: "item"
				}
				{
					count: 26L
					id: "7584789C7AD709E6"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
			]
			title: "第三阶段"
			x: 1.5d
			y: 0.5d
		}
		{
			dependencies: ["713D3B3954E58C4A"]
			description: [
				"四级有所不同.首先需要26个&a红石砖&f包裹核心,然后在红石上方铺设56个&a龙锭&f方块."
				"{image:atm:textures/questpics/draconic/draconic_core_4off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_4on.png width:100 height:100 align:1}"
			]
			icon: "minecraft:diamond"
			id: "1F7D147C9AF6A4FC"
			rewards: [
				{
					count: 7
					id: "2BED49CA22B91087"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 6
					id: "44EDE2F2A5297FA6"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			shape: "square"
			subtitle: "&a储能容量&f98.8亿RF"
			tasks: [
				{
					count: 4L
					id: "5F67C42E49748528"
					item: "draconicevolution:energy_core_stabilizer"
					type: "item"
				}
				{
					count: 54L
					id: "2D8913A86D59AEB4"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 26L
					id: "36AD2A449FE452E8"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			title: "第四阶段"
			x: 2.5d
			y: 1.5d
		}
		{
			dependencies: ["0817FD6E45C127E8"]
			description: [
				"五级需要90个&a龙锭&f方块、80个&a红石砖&f,外加4个高级稳定器或36个普通稳定器."
				"{image:atm:textures/questpics/draconic/draconic_core_5off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_5on.png width:100 height:100 align:1}"
			]
			icon: "minecraft:netherite_scrap"
			id: "131AA933D59D3017"
			rewards: [
				{
					count: 30
					id: "3B6EFCC1BA08CCE2"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 15
					id: "50696F2DBA7CC770"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			shape: "square"
			subtitle: "&a储能容量&f593亿RF"
			tasks: [
				{
					count: 36L
					id: "55B1860918629DA2"
					item: "draconicevolution:energy_core_stabilizer"
					type: "item"
				}
				{
					count: 90L
					id: "13CAFA40CDF003B5"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 80L
					id: "5E2F12A4A10B340F"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			title: "第五阶段"
			x: 0.5d
			y: 2.5d
		}
		{
			dependencies: ["131AA933D59D3017"]
			description: [
				"六级需要150个&a龙锭&f方块和178个&a红石砖&f(别忘了36个稳定器)."
				"{image:atm:textures/questpics/draconic/draconic_core_6off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_6on.png width:100 height:100 align:1}"
			]
			icon: "allthemodium:allthemodium_ingot"
			id: "2DB2A4A1182FE0BB"
			rewards: [
				{
					count: 35
					id: "03709589AC562EFD"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 30
					id: "3BCBEFBD8F3CCB95"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			shape: "square"
			subtitle: "&a储能容量&f3560亿RF"
			tasks: [
				{
					count: 36L
					id: "6D79259DB6ABE2DA"
					item: "draconicevolution:energy_core_stabilizer"
					type: "item"
				}
				{
					count: 150L
					id: "28819FFDA0827620"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 178L
					id: "31103B425588A4DF"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			title: "第六阶段"
			x: -0.5d
			y: 2.5d
		}
		{
			dependencies: ["3B4CEE8A8CE0D6CB"]
			description: [
				"最后是最强的八级!你需要378个&a觉醒龙块&f和786个&a龙锭&f方块!值得吗？"
				"{image:atm:textures/questpics/draconic/draconic_core_8off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_8on.png width:100 height:100 align:1}"
			]
			icon: "allthemodium:unobtainium_ingot"
			id: "04BF68E4554D69AA"
			rewards: [
				{
					id: "0D01EDA8EB654EB8"
					type: "xp_levels"
					xp_levels: 100
				}
				{
					id: "054E3891B12269F7"
					item: {
						Count: 1
						id: "mekanism:ultimate_energy_cube"
						tag: {
							mekData: {
								EnergyContainers: [{
									Container: 0b
									stored: "256000000"
								}]
							}
						}
					}
					type: "item"
				}
			]
			shape: "square"
			subtitle: "&a储能容量&f近乎无限"
			tasks: [
				{
					count: 36L
					id: "4B1B2DCF8BB90B8B"
					item: "draconicevolution:energy_core_stabilizer"
					type: "item"
				}
				{
					count: 786L
					id: "046C14338B30DFCD"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 378L
					id: "19BAF6A931B40F5D"
					item: "draconicevolution:awakened_draconium_block"
					type: "item"
				}
			]
			title: "第八阶段"
			x: -0.5d
			y: 0.5d
		}
		{
			dependencies: ["7F757CD6F8C57733"]
			description: ["能量塔是&a能量核心&f的输入/输出接口.至少需要两个(建议更多)——分别用于输入输出,放置时需靠近核心并覆盖玻璃.建成后&a右击&f塔顶能量球可切换传输方向(箭头标识会变化),连接能量晶体即可实现能量传输."]
			id: "5443E15E226DFC86"
			rewards: [{
				id: "37CC840573CA6E05"
				item: "draconicevolution:draconium_block"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				count: 2L
				id: "3D670D4469917BF1"
				item: "draconicevolution:energy_pylon"
				type: "item"
			}]
			title: "能量传输的关键节点"
			x: -0.5d
			y: 1.5d
		}
		{
			dependencies: ["2DB2A4A1182FE0BB"]
			description: [
				"即将达成!七级需要210个&a龙锭&f和328个&a红石砖&f!"
				"{image:atm:textures/questpics/draconic/draconic_core_7off.png width:100 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_core_7on.png width:100 height:100 align:1}"
			]
			icon: "allthemodium:vibranium_ingot"
			id: "3B4CEE8A8CE0D6CB"
			rewards: [
				{
					count: 30
					id: "779B84A12F802685"
					item: "draconicevolution:awakened_draconium_block"
					type: "item"
				}
				{
					count: 50
					id: "7DC5F09AA916DE83"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
			]
			shape: "square"
			subtitle: "&a储能容量&f21.4万亿RF"
			tasks: [
				{
					count: 36L
					id: "5A9E0E8A579C863F"
					item: "draconicevolution:energy_core_stabilizer"
					type: "item"
				}
				{
					count: 210L
					id: "1B67C959C7F2AAE7"
					item: "draconicevolution:draconium_block"
					type: "item"
				}
				{
					count: 328L
					id: "1AFB7D87A71F862A"
					item: "minecraft:redstone_block"
					type: "item"
				}
			]
			title: "第七阶段"
			x: -1.5d
			y: 1.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["(详情参见模块升级指南)作为六级镐可开采所有等级方块(从石头到无法获取合金),采集物将自动存入背包."]
			id: "3765485408041FC6"
			rewards: [{
				exclude_from_claim_all: true
				id: "125C0E00B1DABC77"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "58CEA0A63FEF1406"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									-**********
									958352009
									-**********
									934577912
								]
							}
						}
					}
					id: "draconicevolution:wyvern_pickaxe"
				}
				type: "item"
			}]
			title: "&a飞龙镐&f"
			x: 9.5d
			y: -4.5d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["&a飞龙镐&f的&a强化版&f"]
			id: "166D2A191871F80E"
			rewards: [{
				exclude_from_claim_all: true
				id: "7FC47CD14C5A5DFC"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "39CE823346207A21"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									**********
									**********
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:draconic_pickaxe"
				}
				type: "item"
			}]
			title: "&a神龙镐&f"
			x: 9.5d
			y: -3.0d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["所有镐具开采等级相同,升级仅提升效率并解锁法杖功能"]
			id: "69396F87A09FBD85"
			rewards: [{
				exclude_from_claim_all: true
				id: "573A47B0679D1378"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "2F1C35191E6C5B60"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									484774789
									**********
									-**********
									896813227
								]
							}
						}
					}
					id: "draconicevolution:chaotic_pickaxe"
				}
				type: "item"
			}]
			title: "&a混沌镐&f"
			x: 9.5d
			y: -1.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["(详情参见模块升级指南)挥剑砍杀——这还需要解释吗？"]
			id: "10690FDB9E892250"
			rewards: [{
				exclude_from_claim_all: true
				id: "33E8F7B53AF7FBC7"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "2910D9CDBCF9AE24"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-**********
									**********
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:wyvern_sword"
				}
				type: "item"
			}]
			title: "&a飞龙剑&f"
			x: 8.5d
			y: -4.5d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["&a飞龙剑&f的&a强化版&f"]
			id: "710C7FEFAA6C0979"
			rewards: [{
				exclude_from_claim_all: true
				id: "508AB15EF1FD7E67"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "1BF799C8898716E4"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									222970897
									-**********
									-**********
									-486642495
								]
							}
						}
					}
					id: "draconicevolution:draconic_sword"
				}
				type: "item"
			}]
			title: "&a神龙剑&f"
			x: 8.5d
			y: -3.0d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["比&a下界合金剑&f更强更酷炫!"]
			id: "39AF9B1A996DA949"
			rewards: [{
				exclude_from_claim_all: true
				id: "2276BB717D82DDA9"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "0AFD95B14EA35FEB"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									**********
									-**********
									-**********
									**********
								]
							}
						}
					}
					id: "draconicevolution:chaotic_sword"
				}
				type: "item"
			}]
			title: "&a混沌剑&f"
			x: 8.5d
			y: -1.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["查看模块了解更多关于如何使用和升级DE物品的信息!我知道你们玩家真正关心的是&a飞龙锹&f.你再也不用辗转难眠了,因为现在你知道,DE铲子可以开辟道路."]
			id: "3235FBD9CE21D5EE"
			rewards: [{
				exclude_from_claim_all: true
				id: "2C8BA7504D7D8C1E"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "407EEF67B575361C"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									-**********
									439631883
									-**********
									**********
								]
							}
						}
					}
					id: "draconicevolution:wyvern_shovel"
				}
				type: "item"
			}]
			title: "&a飞龙锹&f"
			x: 11.5d
			y: -4.5d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["&a飞龙锹&f的&a增强&f版本."]
			id: "46C816844C513D5D"
			rewards: [{
				exclude_from_claim_all: true
				id: "4E7BA1AF6FFFAC20"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "57516D08A532A6D9"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									-**********
									-**********
									-**********
									-182582560
								]
							}
						}
					}
					id: "draconicevolution:draconic_shovel"
				}
				type: "item"
			}]
			title: "&a神龙锹&f"
			x: 11.5d
			y: -3.0d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["同样可以开辟道路!"]
			id: "052E261F934AB439"
			rewards: [{
				exclude_from_claim_all: true
				id: "1EAE86065324E051"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "2C3BC16D36192BC5"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									-**********
									-**********
									-**********
									**********
								]
							}
						}
					}
					id: "draconicevolution:chaotic_shovel"
				}
				type: "item"
			}]
			title: "&a混沌锹&f"
			x: 11.5d
			y: -1.5d
		}
		{
			dependencies: [
				"46C816844C513D5D"
				"166D2A191871F80E"
				"710C7FEFAA6C0979"
				"5ADFC45B03BAB852"
			]
			description: ["你知道传说中的万能工具吗？它是镐、斧和铲的组合？这个类似,是剑、镐和铲的组合!它能做所有龙之工具能做的事!"]
			id: "22D383BAEF8A2B39"
			rewards: [{
				exclude_from_claim_all: true
				id: "2EFEABA2178F69CD"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "5B309CC849575BAB"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									**********
									**********
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:draconic_staff"
				}
				type: "item"
			}]
			title: "龙法杖"
			x: 13.5d
			y: -3.0d
		}
		{
			dependencies: [
				"052E261F934AB439"
				"69396F87A09FBD85"
				"39AF9B1A996DA949"
				"763EB5C911D7E78C"
			]
			description: ["最好的剑加上最快的镐再加上铲子？!？!你还想要什么!"]
			id: "12469CC0CCFBA1C5"
			rewards: [{
				exclude_from_claim_all: true
				id: "2864579B74DDB579"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "5D5E2B91237B0A83"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									825504265
									124930682
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:chaotic_staff"
				}
				type: "item"
			}]
			title: "混沌法杖"
			x: 13.5d
			y: -1.5d
		}
		{
			description: ["模块对我们的DE物品非常重要.重要到没有它们就无法工作!大多数模块由&a模块核心&f或前一个模块制成.手持物品并按Shift和C键打开&a模式菜单&f.在那里你会看到一些插槽和按钮!&a栏位&f是模块实际放置的地方,而网格决定了可以放置多少模块.当模块显示1x1时,它只占用1个插槽,而显示2x2的模块将占用4个插槽.右侧的&a低&fest按钮会显示可以应用于你手持物品的模块.其余的只是改变GUI的外观."]
			hide_dependency_lines: true
			hide_dependent_lines: true
			icon_scale: 2.0d
			id: "4EFFE300EC781C3E"
			rewards: [{
				count: 10
				id: "098743FB755EF67D"
				item: "draconicevolution:module_core"
				type: "item"
			}]
			shape: "square"
			size: 1.5d
			tasks: [{
				id: "526113C0A6FEE835"
				item: "draconicevolution:module_core"
				type: "item"
			}]
			title: "模块!"
			x: 15.75d
			y: -9.0d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["查看模块了解更多关于如何使用和升级DE物品的信息!像普通弓一样通过射箭工作.每次射击都会从弓本身消耗一定量的能量,所以是的,你至少需要&a能量模块&f."]
			id: "0AE930BA037DC565"
			rewards: [{
				exclude_from_claim_all: true
				id: "438E73859975B84E"
				table_id: 5036812014823277174L
				type: "random"
			}]
			size: 1.0d
			tasks: [{
				id: "4E218D4579DA19CA"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-777390198
									-841727973
									-**********
									**********
								]
							}
						}
					}
					id: "draconicevolution:wyvern_bow"
				}
				type: "item"
			}]
			title: "&a飞龙弓&f"
			x: 7.5d
			y: -4.5d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["&a飞龙弓&f的&a增强&f版本."]
			id: "43C2B07AB25C708E"
			rewards: [{
				exclude_from_claim_all: true
				id: "758E5E513DAA1D6E"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "6BBAE543089547D9"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									184112862
									**********
									-**********
									-964317728
								]
							}
						}
					}
					id: "draconicevolution:draconic_bow"
				}
				type: "item"
			}]
			title: "&a神龙弓&f"
			x: 7.5d
			y: -3.0d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["来自&d龙之进化/龙之研究&f的最好的弓,还有更多的模块插槽!"]
			id: "7EB97C0DDE60DED4"
			rewards: [{
				exclude_from_claim_all: true
				id: "66E6E3D9A6D53B39"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "26BE6BA90399F55D"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0L
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									135971894
									-**********
									-**********
									**********
								]
							}
						}
					}
					id: "draconicevolution:chaotic_bow"
				}
				type: "item"
			}]
			title: "&a混沌弓&f"
			x: 7.5d
			y: -1.5d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["自动进食会在你饥饿时自动喂食你物品栏中的任何食物.不过它有最大食物点存储,每个完整的食物条是2个食物点.所以基础版本只能存储相当于8块牛排的食物."]
			id: "40F34EBE09DA7752"
			rewards: [{
				exclude_from_claim_all: true
				id: "41EFF8AB501D6815"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "7DC8BE92D6D7DBCC"
				item: "draconicevolution:item_draconium_auto_feed"
				type: "item"
			}]
			title: "基础&a自动进食模块&f"
			x: 17.25d
			y: 2.25d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["非常简单的前提,斧和剑可以造成更多&a伤害&f.是的,它可以叠加!"]
			id: "1C8210712349A2E3"
			rewards: [{
				exclude_from_claim_all: true
				id: "46C67A58E57778D1"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "1ED5F0AA6588F503"
				item: "draconicevolution:item_draconium_damage"
				type: "item"
			}]
			title: "基础&a伤害模块&f"
			x: 17.25d
			y: -4.5d
		}
		{
			dependencies: ["1C8210712349A2E3"]
			id: "1E41A5071854DEC8"
			rewards: [{
				exclude_from_claim_all: true
				id: "6C38DC7CD9C0786C"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "56370732D830D093"
				item: "draconicevolution:item_wyvern_damage"
				type: "item"
			}]
			title: "&a飞龙伤害模块&f"
			x: 18.0d
			y: -4.5d
		}
		{
			dependencies: ["1E41A5071854DEC8"]
			id: "7B9F5EEC76FDD0D4"
			rewards: [{
				exclude_from_claim_all: true
				id: "02954B819D1738ED"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "0746EC1EA680B78D"
				item: "draconicevolution:item_draconic_damage"
				type: "item"
			}]
			title: "&a神龙伤害模块&f"
			x: 18.75d
			y: -4.5d
		}
		{
			dependencies: ["7B9F5EEC76FDD0D4"]
			id: "04695DA1BD3BF1B2"
			rewards: [{
				exclude_from_claim_all: true
				id: "79679B41B462574B"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "33FC8252185CB8ED"
				item: "draconicevolution:item_chaotic_damage"
				type: "item"
			}]
			title: "&a混沌伤害模块&f"
			x: 19.5d
			y: -4.5d
		}
		{
			description: ["欢迎来到&d龙之进化/龙之研究&f,一个非常有趣且强力的模组!开始时你需要挖掘龙矿石来获取&a龙尘&f.它在所有(原版)维度生成,但最多的是在&a末地&f!"]
			icon: {
				Count: 1
				ForgeCaps: {
					Parent: {
						energy: {
							energy: 0
						}
						module_host: {
							modules: [ ]
							properties: {
								charge_armor: {
									hud: 1b
								}
								charge_curios: {
									hud: 1b
								}
								charge_held_item: {
									hud: 1b
								}
								charge_hot_bar: {
									hud: 1b
								}
								charge_main: {
									hud: 1b
								}
							}
							provider_id: [I;
								-**********
								**********
								-**********
								-**********
							]
						}
					}
				}
				id: "draconicevolution:wyvern_capacitor"
			}
			id: "26DF1427AC3966DE"
			rewards: [{
				count: 12
				id: "2E37C878B24D6913"
				item: "draconicevolution:draconium_ingot"
				type: "item"
			}]
			shape: "gear"
			size: 2.5d
			tasks: [{
				count: 6L
				id: "76BCC3C288514737"
				item: { Count: 6, id: "draconicevolution:draconium_dust" }
				type: "item"
			}]
			title: "&d龙之进化/龙之研究&f"
			x: 6.0d
			y: -12.5d
		}
		{
			dependencies: ["26DF1427AC3966DE"]
			description: ["&a龙核心&f是&d龙之进化/龙之研究&f中许多配方的重要成分.特别是基础等级的物品."]
			id: "12BDE512C734D19A"
			rewards: [{
				count: 4
				id: "680CD324CBA643BA"
				item: "draconicevolution:draconium_core"
				type: "item"
			}]
			tasks: [{
				id: "1177AB4D5588F257"
				item: "draconicevolution:draconium_core"
				type: "item"
			}]
			title: "基础(龙)核心"
			x: 6.0d
			y: -9.0d
		}
		{
			dependencies: ["12BDE512C734D19A"]
			description: ["&a聚合装置&f是制作大多数(至少是有趣的物品)的方式.开始时你需要一个&a聚合核心&f,将其放置在地面上方约一个方块的位置.&a核心&f是配方可以从JEI自动放置的地方,中间物品放置的地方,以及最终物品将被放置的地方.它需要注入器放置在离它几个方块远的地方,朝向核心的两侧.查看下一个任务以了解更多关于这些的信息.注入器还需要能量,这将在晶体绑定任务中详细解释."]
			id: "4B7840F1A8CF1378"
			rewards: [{
				count: 8
				id: "644DD7D6B57612E7"
				item: "draconicevolution:draconium_core"
				type: "item"
			}]
			shape: "hexagon"
			size: 1.5d
			tasks: [{
				id: "4F50EF2523C3878B"
				item: "draconicevolution:crafting_core"
				type: "item"
			}]
			title: "&a聚合装置&f"
			x: 6.0d
			y: -7.5d
		}
		{
			dependencies: ["4B7840F1A8CF1378"]
			description: ["&a聚合装置&f具有由注入器决定的等级体系.&a连接&f遵循升序排列:龙矿(我称之为基础级以便区分)、飞龙、神龙、混沌.注入器需要放置在&a聚合核心&f左右两侧,与其相隔数格距离.它们可以呈十字形排列,每侧最多放置5个.为注入器供能时,连接中继IO水晶即可生效,详情请参阅水晶绑定章节."]
			id: "1D3E2363CFD4C5E0"
			rewards: [{
				exclude_from_claim_all: true
				id: "3480AB7F7CCB595D"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "3D86285F124928F8"
				item: "draconicevolution:basic_crafting_injector"
				type: "item"
			}]
			title: "基础等级"
			x: 6.0d
			y: -6.0d
		}
		{
			dependencies: ["1D3E2363CFD4C5E0"]
			description: ["在&d龙之进化/龙之研究&f中提升&a聚合装置&f等级时,配方所需的注入器必须达到或高于当前等级.例如制作&a飞龙斧&f至少需要6个飞龙注入器,每侧各3个.别担心,合成核心始终不变!"]
			id: "51CF09EC333DB1A3"
			rewards: [{
				exclude_from_claim_all: true
				id: "09D389B16F321F3C"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "3B5336539F8701BD"
				item: "draconicevolution:wyvern_crafting_injector"
				type: "item"
			}]
			title: "飞龙等级"
			x: 6.0d
			y: -4.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["这正是&a龙芯&f的用武之地."]
			id: "5ADFC45B03BAB852"
			rewards: [{
				exclude_from_claim_all: true
				id: "61B2C0ECEAC859F2"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "456AE9FF8794BBD4"
				item: "draconicevolution:awakened_crafting_injector"
				type: "item"
			}]
			title: "神龙等级"
			x: 6.0d
			y: -3.0d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["这是合成灌注的终极阶段——混沌等级.达到此等级后,你可以制作所有物品,不仅是工具,还包括反应堆所需的一切组件."]
			id: "763EB5C911D7E78C"
			rewards: [{
				exclude_from_claim_all: true
				id: "42006DAC1EC5B749"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "rsquare"
			size: 1.25d
			tasks: [{
				id: "2F8AF8AE6F59B5CE"
				item: "draconicevolution:chaotic_crafting_injector"
				type: "item"
			}]
			title: "混沌等级"
			x: 6.0d
			y: -1.5d
		}
		{
			dependencies: ["1D3E2363CFD4C5E0"]
			description: ["基础级灌注的两大产物之一,但千万别小看龙箱.它比双箱容量更大,能熔炼和合成物品,最棒的是还能变色!这绝对是我在所有模组中最钟爱的箱子!"]
			id: "285656AF1F870AF8"
			rewards: [{
				exclude_from_claim_all: true
				id: "538CE96BF841A65B"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "hexagon"
			tasks: [{
				id: "56308DFC57B1249E"
				item: "draconicevolution:draconium_chest"
				type: "item"
			}]
			title: "&a龙箱&f"
			x: 7.5d
			y: -6.0d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["&a觉醒龙&f源自&a聚合装置&f中的方块.这些方块可分解为9个锭,是&d龙之进化/龙之研究&f中第二重要的锭材料,对飞龙和神龙等级至关重要."]
			id: "41BD497108CA109F"
			rewards: [{
				exclude_from_claim_all: true
				id: "083432516AC49C29"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "471F944E1DA4FCBC"
				item: "draconicevolution:awakened_draconium_block"
				type: "item"
			}]
			title: "&a觉醒龙&f"
			x: 2.5d
			y: -4.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["&a电容&f在&d龙之进化/龙之研究&f中充当电池.它们需要&a能量模块&f才能存储能量,记住'&a多多&f益善'!"]
			id: "2B6D71DAF72EAFA4"
			rewards: [{
				exclude_from_claim_all: true
				id: "760AB3C32B47092D"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "15CE4B82B5863A8B"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: {
									charge_armor: {
										hud: 1b
									}
									charge_curios: {
										hud: 1b
									}
									charge_held_item: {
										hud: 1b
									}
									charge_hot_bar: {
										hud: 1b
									}
									charge_main: {
										hud: 1b
									}
								}
								provider_id: [I;
									-354770428
									**********
									-**********
									810657246
								]
							}
						}
					}
					id: "draconicevolution:wyvern_capacitor"
				}
				type: "item"
			}]
			title: "&a飞龙通量容器&f"
			x: 4.5d
			y: -4.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["&d龙之进化/龙之研究&f认为没必要用蹩脚护甲覆盖全身,因为你可以使用自带护盾的单件胸甲...等等,其实护盾不是内置的,需要模块支持...抱歉."]
			id: "4AB564420C48579B"
			rewards: [{
				exclude_from_claim_all: true
				id: "2E77C63492DDAABE"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "03D8669E761BA583"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-395766992
									-**********
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:wyvern_chestpiece"
				}
				type: "item"
			}]
			title: "&a双足飞龙胸甲&f"
			x: 13.5d
			y: -4.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["我知道这是飞龙等级,为什么会出现神龙物品？因为要升级到神龙等级需要神龙注入器,而制作神龙注入器又需要&a龙芯&f.现在明白它们出现在这里的原因了吧？"]
			id: "5E0D78B7B2CF1729"
			rewards: [{
				exclude_from_claim_all: true
				id: "71CB585D324CD8BB"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "414D0C56813D9BBB"
				item: "draconicevolution:awakened_core"
				type: "item"
			}]
			title: "&a龙芯&f"
			x: 3.5d
			y: -4.5d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["这是最终混沌等级的必要材料.希望你已经完成了关于获取&a混沌碎片&f的任务线!"]
			id: "75F0405FFAB9C080"
			rewards: [{
				exclude_from_claim_all: true
				id: "2BB4430C85BAF1FE"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "12DC5DD555FB3DEF"
				item: "draconicevolution:chaotic_core"
				type: "item"
			}]
			title: "&a混沌核心&f"
			x: 3.5d
			y: -3.0d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["&a双足飞龙胸甲&f的&a强化&f版本."]
			id: "4E27182763DA83DC"
			rewards: [{
				exclude_from_claim_all: true
				id: "676CA444E828631E"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "42F81219DD85E34F"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									**********
									-144685975
									-**********
									-46833520
								]
							}
						}
					}
					id: "draconicevolution:draconic_chestpiece"
				}
				type: "item"
			}]
			title: "&a神龙胸甲&f"
			x: 14.5d
			y: -3.0d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["但愿这件护甲能保护好你."]
			id: "01612963DBBAC9A1"
			rewards: [{
				exclude_from_claim_all: true
				id: "063B16BF2ED952B0"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "002993328BED5A60"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									865838435
									-389592656
									-**********
									-801567611
								]
							}
						}
					}
					id: "draconicevolution:chaotic_chestpiece"
				}
				type: "item"
			}]
			title: "混沌胸甲"
			x: 14.5d
			y: -1.5d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["龙研科技能提供的最佳能量电池."]
			id: "36DAFC3FECC67406"
			rewards: [{
				exclude_from_claim_all: true
				id: "77F92CFA2217F16F"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "49A3CC960C35F9C9"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: {
									charge_armor: {
										hud: 1b
									}
									charge_curios: {
										hud: 1b
									}
									charge_held_item: {
										hud: 1b
									}
									charge_hot_bar: {
										hud: 1b
									}
									charge_main: {
										hud: 1b
									}
								}
								provider_id: [I;
									**********
									832194230
									-**********
									-538613212
								]
							}
						}
					}
					id: "draconicevolution:chaotic_capacitor"
				}
				type: "item"
			}]
			title: "&a混沌通量容器&f"
			x: 4.5d
			y: -1.5d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["提供更强大的运作潜能!"]
			id: "6DBD81BA83B1D0A5"
			rewards: [{
				exclude_from_claim_all: true
				id: "41DF0B8AD669EB13"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "73F4047BAB9D0B77"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: {
									charge_armor: {
										hud: 1b
									}
									charge_curios: {
										hud: 1b
									}
									charge_held_item: {
										hud: 1b
									}
									charge_hot_bar: {
										hud: 1b
									}
									charge_main: {
										hud: 1b
									}
								}
								provider_id: [I;
									-475253622
									-631421354
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:draconic_capacitor"
				}
				type: "item"
			}]
			title: "&a神龙通量容器&f"
			x: 4.5d
			y: -3.0d
		}
		{
			dependencies: ["1D3E2363CFD4C5E0"]
			description: ["基础级(&a能量中继器&f)水晶是&d龙之进化/龙之研究&f能量传输的起点!"]
			id: "2E74B5149D4BD0A0"
			rewards: [{
				exclude_from_claim_all: true
				id: "3213FB4F8DF45B42"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "1E33B6AC90BEE300"
				item: "draconicevolution:basic_relay_crystal"
				type: "item"
			}]
			title: "基础&a能量中继器&f水晶"
			x: 4.5d
			y: -6.0d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["这些水晶能传输更多能量并建立更多链接,属于&a高&f阶能量水晶."]
			id: "36F123B2A6B52A01"
			rewards: [{
				exclude_from_claim_all: true
				id: "4586E0C268BBFE01"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "165C17055E69492C"
				item: "draconicevolution:draconic_relay_crystal"
				type: "item"
			}]
			title: "&a神龙能量中继水晶&f"
			x: 2.5d
			y: -3.0d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["飞龙级(&a能量中继器&f)水晶.功能与基础水晶完全相同,但能处理更多能量和链接."]
			id: "715F9F7107374299"
			rewards: [{
				exclude_from_claim_all: true
				id: "0DC3F6778A331AF4"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "diamond"
			tasks: [{
				id: "2C3AA490328FAE8C"
				item: "draconicevolution:wyvern_relay_crystal"
				type: "item"
			}]
			title: "&a飞龙能量中继水晶&f"
			x: 1.5d
			y: -4.5d
		}
		{
			dependencies: ["0252B8F77A038D17"]
			id: "5E671F1F370DDE43"
			rewards: [{
				exclude_from_claim_all: true
				id: "79379DEBB9CB1DC8"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "527E10E9550048CA"
				item: "draconicevolution:item_draconic_auto_feed"
				type: "item"
			}]
			title: "&a神龙自动进食模块&f"
			x: 18.75d
			y: 2.25d
		}
		{
			dependencies: ["40F34EBE09DA7752"]
			id: "0252B8F77A038D17"
			rewards: [{
				exclude_from_claim_all: true
				id: "5CD43FE49565A25C"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "4782C7D803835881"
				item: "draconicevolution:item_wyvern_auto_feed"
				type: "item"
			}]
			title: "&a飞龙自动进食模块&f"
			x: 18.0d
			y: 2.25d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["其实就是 &a跳跃提升&f."]
			id: "6B0CD6732BE0E819"
			rewards: [{
				exclude_from_claim_all: true
				id: "3D22F77DD119FBA5"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "310DD2EEBEF0EBA9"
				item: "draconicevolution:item_draconium_jump"
				type: "item"
			}]
			title: "基础 &a指向传送模块&f"
			x: 17.25d
			y: 1.5d
		}
		{
			dependencies: ["6B0CD6732BE0E819"]
			id: "529123A789D790E3"
			rewards: [{
				exclude_from_claim_all: true
				id: "1EC3DF861C5060E2"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "1671F1293B373AF3"
				item: "draconicevolution:item_wyvern_jump"
				type: "item"
			}]
			title: "&a飞龙跳跃模块&f"
			x: 18.0d
			y: 1.5d
		}
		{
			dependencies: ["529123A789D790E3"]
			id: "79FC674836B6340E"
			rewards: [{
				exclude_from_claim_all: true
				id: "7E41EF73BB548E61"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "05ABF37F8E1DE264"
				item: "draconicevolution:item_draconic_jump"
				type: "item"
			}]
			title: "&a神龙跳跃模块&f"
			x: 18.75d
			y: 1.5d
		}
		{
			dependencies: ["79FC674836B6340E"]
			id: "50013DD7370F8EDB"
			rewards: [{
				exclude_from_claim_all: true
				id: "0481395E6E3404C4"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "0B88252BE82CF5C6"
				item: "draconicevolution:item_chaotic_jump"
				type: "item"
			}]
			title: "&a混沌跳跃模块&f"
			x: 19.5d
			y: 1.5d
		}
		{
			dependencies: ["7F757CD6F8C57733"]
			description: ["&a能量核心稳定器&f...稳定器？&a核心&f.前4级核心只需在其周围放置4个稳定器.你也能通过稳定器访问核心的图形界面.如果放置位置离核心太近或位置错误,系统会提示摆放不正确."]
			id: "421954D7D46FAAD6"
			rewards: [{
				id: "44747E919A1A6161"
				item: "draconicevolution:draconium_block"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				id: "64A54A26814AC387"
				item: "draconicevolution:energy_core_stabilizer"
				type: "item"
			}]
			title: "&a能量核心稳定器&f"
			x: 1.5d
			y: 1.5d
		}
		{
			dependencies: ["1F7D147C9AF6A4FC"]
			description: ["5-8级核心需要高级稳定器.什么是高级稳定器？就是在3x1x3区域内放置9个稳定器.正确摆放时会转化为环形结构!摆放方式与单个稳定器相同."]
			id: "2D9AF97C03C5AEC7"
			rewards: [{
				count: 9
				id: "59EA83789B61F7C8"
				item: "draconicevolution:energy_core_stabilizer"
				type: "item"
			}]
			shape: "square"
			tasks: [{
				count: 9L
				id: "48C475EE944E4AC6"
				item: { Count: 9, id: "draconicevolution:energy_core_stabilizer" }
				type: "item"
			}]
			title: "高级稳定器"
			x: 1.5d
			y: 2.5d
		}
		{
			dependencies: ["2D9AF97C03C5AEC7"]
			description: ["技术上这是可选项但建议配置!使用更多能量塔可以输入输出更多OP能量!"]
			id: "0817FD6E45C127E8"
			optional: true
			rewards: [
				{
					count: 8
					id: "22AD062EB66F018E"
					item: "minecraft:glass"
					type: "item"
				}
				{
					count: 2
					id: "5CA458A3C6DE4B78"
					item: "draconicevolution:draconic_io_crystal"
					type: "item"
				}
			]
			shape: "square"
			tasks: [{
				count: 8L
				id: "1185E056FCB6918C"
				item: { Count: 8, id: "draconicevolution:energy_pylon" }
				type: "item"
			}]
			title: "更多能量塔(可选)"
			x: 0.5d
			y: 3.5d
		}
		{
			dependencies: ["12BDE512C734D19A"]
			description: ["运作潜能是&d龙之进化/龙之研究&f的能量源.传输OP的主要方式(非唯一方式)是通过IO中继水晶.要将OP输入物品需要&a能量传输器&f."]
			id: "7A4ABFCD12202A91"
			rewards: [{
				exclude_from_claim_all: true
				id: "507859BC7C414057"
				table_id: 8319312224449668135L
				type: "random"
			}]
			shape: "gear"
			size: 2.0d
			tasks: [{
				id: "1EA0384F407273BA"
				item: "draconicevolution:crystal_binder"
				type: "item"
			}]
			title: "什么是能量(我们称之为OP)？"
			x: 6.0d
			y: 1.5d
		}
		{
			dependencies: [
				"6616A62426ED4D15"
				"69986817D58AC8BE"
			]
			description: [
				"&a反应堆芯&f是反应堆的核心部件!将其放置在距注入器和稳定器至少5格远的位置,&a右键点击&f可打开界面,你会看到若干进度条、球体、按钮和插槽.从左至右的进度条分别代表:温度、&a容纳力场&f、&a能量储量&f和&a燃料转化&f.你已了解力场和能量,燃料知识将在后续任务中学习.温度至关重要——预热需达到2000°C才能激活,但若&a过高&f将导致...熔毁!&a球体&f显示核心状态(当前存在材质显示错误的bug).&a按钮&f可切换界面外观、启停反应堆及充能."
				"{image:atm:textures/questpics/draconic/draconic_reactor_off_down.png width:200 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_reactor_off_up.png width:150 height:150 align:1}"
			]
			icon_scale: 2.0d
			id: "5BC6CC3C09F512A7"
			min_width: 300
			rewards: [{
				count: 2
				id: "5B61E2E794F3E809"
				item: "draconicevolution:awakened_draconium_block"
				type: "item"
			}]
			size: 1.5d
			subtitle: "无限能源"
			tasks: [{
				id: "4DCBC4B5CD5C7016"
				item: "draconicevolution:reactor_core"
				type: "item"
			}]
			title: "反应堆建造工程"
			x: 9.5d
			y: 2.0d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["稳定器用于构建&a容纳力场&f(环绕&a反应堆芯&f的力场护盾).当反应堆运行时若护盾值降为0将开始熔毁.4个稳定器需距离核心数格(5格以上)并与核心保持同轴排列.此处也是从反应堆输出OP的接口,注意不要过量抽取!"]
			hide_dependency_lines: true
			icon_scale: 1.5d
			id: "69986817D58AC8BE"
			rewards: [{
				count: 2
				id: "5B495025B19F24D7"
				item: "draconicevolution:awakened_draconium_block"
				type: "item"
			}]
			shape: "hexagon"
			tasks: [{
				count: 4L
				id: "2184F68F4DDA4834"
				item: { Count: 4, id: "draconicevolution:reactor_stabilizer" }
				type: "item"
			}]
			title: "&a反应堆稳定器&f"
			x: 9.5d
			y: 0.5d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["注入器需与所有稳定器位于不同侧面,且必须与核心同轴.这里是OP能量的输入口!要激活产需先提升&a能量储量&f.&a能量储量&f指存储能量和即将产生的能量."]
			hide_dependency_lines: true
			icon_scale: 1.5d
			id: "6616A62426ED4D15"
			rewards: [{
				count: 2
				id: "4B0DB77929F4EFFD"
				item: "draconicevolution:awakened_draconium_block"
				type: "item"
			}]
			shape: "rsquare"
			tasks: [{
				id: "3A62FA10AC96760C"
				item: "draconicevolution:reactor_injector"
				type: "item"
			}]
			title: "&a反应堆能量注入器&f"
			x: 9.5d
			y: 3.5d
		}
		{
			dependencies: ["5BC6CC3C09F512A7"]
			description: ["&a反应堆&f使用&a觉醒龙&f作为燃料,效率很高!&a燃料转化&f决定何时转为混沌状态.每次最多处理8个方块,且只能在反应堆关闭时添加燃料.SAS模式可简化操作!燃料需放入右下角槽位."]
			id: "129E09C03E40BA57"
			rewards: [{
				count: 2
				id: "01BEDB40AA9A03A7"
				item: "draconicevolution:awakened_draconium_block"
				type: "item"
			}]
			tasks: [{
				count: 8L
				id: "47941C447E669A0E"
				item: { Count: 8, id: "draconicevolution:awakened_draconium_block" }
				type: "item"
			}]
			title: "反应堆燃料"
			x: 11.5d
			y: 1.5d
		}
		{
			dependencies: ["5BC6CC3C09F512A7"]
			description: ["&a反应堆&f最多储存10亿OP,这个数值很快会不够用.当&a能量储量&f达100%时会急剧升温导致熔毁.激活需要5亿OP,使用&a能量阀门&f可控制OP流量(注:1RF=1OP).需要红石信号激活,可双向调节OP.建议将输出能量导入&a能量核心&f避免熔毁!"]
			id: "6601ACCDDF6CA5FF"
			rewards: [{
				id: "4338F529190DC56C"
				item: "minecraft:lever"
				type: "item"
			}]
			tasks: [{
				id: "5E453CAF75E1370A"
				item: "draconicevolution:flux_gate"
				type: "item"
			}]
			title: "启动准备"
			x: 11.5d
			y: 2.5d
		}
		{
			dependencies: [
				"129E09C03E40BA57"
				"6601ACCDDF6CA5FF"
			]
			description: [
				"5亿OP？就绪.燃料？就绪.温度2000°C？就绪.点击激活按钮后,祈祷别发生熔毁吧!"
				"{image:atm:textures/questpics/draconic/draconic_reactor_on_down.png width:200 height:100 align:1}"
				"{image:atm:textures/questpics/draconic/draconic_reactor_on_up.png width:150 height:150 align:1}"
			]
			id: "7D159D333B2AC57E"
			rewards: [{
				id: "2CC933DAF73F7D73"
				type: "xp_levels"
				xp_levels: 20
			}]
			subtitle: "现在我们需要祈祷和希望..."
			tasks: [{
				id: "61CCAD2018478894"
				title: "激活仪式"
				type: "checkmark"
			}]
			title: "启动反应堆"
			x: 13.5d
			y: 2.0d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["装备在胸甲上可实现滑翔!效果类似鞘翅(但不消耗护甲值).我更喜欢称之为'有格调的下落'."]
			id: "5E8D527DE9D7DD46"
			rewards: [{
				exclude_from_claim_all: true
				id: "0B0CD51089B460A7"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "57DC917AF757F1A0"
				item: "draconicevolution:item_wyvern_flight"
				type: "item"
			}]
			title: "&a飞龙飞行模块&f"
			x: 18.0d
			y: 0.75d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["原始如弓箭的武器难求精准.但这些模块能实现!装备后&a箭矢&f更易命中目标且不易偏离."]
			id: "1830412AB77E9526"
			rewards: [{
				exclude_from_claim_all: true
				id: "003B079C83E81567"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "3F28B3211730AFB9"
				item: "draconicevolution:item_wyvern_proj_accuracy"
				type: "item"
			}]
			title: "&a飞龙射击精度模块&f"
			x: 18.0d
			y: -7.5d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["这些模块专为DE弓设计,与之前的不同.&a弹射物伤害&f模块可提升弓箭的射击伤害.某些模块可能会带来负面效果,比如这个会略微降低弓的精准度.所有效果均可叠加!"]
			id: "7D7E4C31DFE59E29"
			rewards: [{
				exclude_from_claim_all: true
				id: "7789A9C74ACC2E2A"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2DF91C91574F18A1"
				item: "draconicevolution:item_wyvern_proj_damage"
				type: "item"
			}]
			title: "&a飞龙射击伤害模块&f"
			x: 18.0d
			y: -9.75d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["若您自1.13版本后就没玩过Minecraft可能会困惑,但您应该知道弩和穿透附魔.这个模块正是让箭矢可以穿透多个目标持续造成伤害,就像穿透附魔那样!"]
			id: "78FBB0C88182040E"
			rewards: [{
				exclude_from_claim_all: true
				id: "597C37DF492A2679"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2771CA29BA10C3F7"
				item: "draconicevolution:item_wyvern_proj_penetration"
				type: "item"
			}]
			title: "&a飞龙箭矢穿透模块&f"
			x: 18.0d
			y: -8.25d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["速度即物体朝某方向运动的速率.既然我们的箭矢不是来自银河护卫队,简单来说就是箭的飞行速度.每级升级都能缩短箭矢从弓弦到目标的飞行时间,代价是略微降低精准度."]
			id: "61419D230687CBB5"
			rewards: [{
				exclude_from_claim_all: true
				id: "1FC38A0F49D5C24E"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "4EFCDD3427D5CAE8"
				item: "draconicevolution:item_wyvern_proj_velocity"
				type: "item"
			}]
			title: "&a飞龙箭矢速度模块&f"
			x: 18.0d
			y: -9.0d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["砍倒树木时,所有相连的树木都会一并倒下."]
			id: "607337D8AA20FA18"
			rewards: [{
				exclude_from_claim_all: true
				id: "178F59F4B878E2E7"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "3143C632F79A604C"
				item: "draconicevolution:item_wyvern_tree_harvest"
				type: "item"
			}]
			title: "&a飞龙树木砍伐者&f"
			x: 18.0d
			y: -3.75d
		}
		{
			dependencies: ["5E8D527DE9D7DD46"]
			description: ["这个模块同时提供滑翔和真正的飞行能力,更准确地说,是创造模式的飞行."]
			id: "3FD19A001899D8C6"
			rewards: [{
				exclude_from_claim_all: true
				id: "098CB8A67E47A53B"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "4BCB651847D1D6B1"
				item: "draconicevolution:item_draconic_flight"
				type: "item"
			}]
			title: "&a神龙飞行模块&f"
			x: 18.75d
			y: 0.75d
		}
		{
			dependencies: ["1830412AB77E9526"]
			id: "38AD547C36BDF4C4"
			rewards: [{
				exclude_from_claim_all: true
				id: "54E0B6A9050D7A60"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "59BD7F05AA2944EB"
				item: "draconicevolution:item_draconic_proj_accuracy"
				type: "item"
			}]
			title: "&a神龙箭矢精度模块&f"
			x: 18.75d
			y: -7.5d
		}
		{
			dependencies: ["7D7E4C31DFE59E29"]
			id: "4903BDC389C73082"
			rewards: [{
				exclude_from_claim_all: true
				id: "32F7414782748A64"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "32BB5E044BAE4F77"
				item: "draconicevolution:item_draconic_proj_damage"
				type: "item"
			}]
			title: "&a神龙箭矢伤害模块&f"
			x: 18.75d
			y: -9.75d
		}
		{
			dependencies: ["78FBB0C88182040E"]
			id: "070F8045CC14B50D"
			rewards: [{
				exclude_from_claim_all: true
				id: "72A360B0CA04CDDA"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "03D37837BCDC5FCB"
				item: "draconicevolution:item_draconic_proj_penetration"
				type: "item"
			}]
			title: "&a神龙箭矢穿透模块&f"
			x: 18.75d
			y: -8.25d
		}
		{
			dependencies: ["61419D230687CBB5"]
			id: "0499A372F55D04C8"
			rewards: [{
				exclude_from_claim_all: true
				id: "2ECF4A9BD8F36C07"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "6B2CD2E18A26E55C"
				item: "draconicevolution:item_draconic_proj_velocity"
				type: "item"
			}]
			title: "&a神龙箭矢速度模块&f"
			x: 18.75d
			y: -9.0d
		}
		{
			dependencies: ["607337D8AA20FA18"]
			id: "3DF794EBCAA7AE1A"
			rewards: [{
				exclude_from_claim_all: true
				id: "67D726BECA1955E3"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2971418598EED7C8"
				item: "draconicevolution:item_draconic_tree_harvest"
				type: "item"
			}]
			title: "神龙&a树木砍伐&f者"
			x: 18.75d
			y: -3.75d
		}
		{
			dependencies: ["3FD19A001899D8C6"]
			id: "236C936FE3BA8396"
			rewards: [{
				exclude_from_claim_all: true
				id: "284F087C3382A6AF"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "1D27F6BC00BA4375"
				item: "draconicevolution:item_chaotic_flight"
				type: "item"
			}]
			title: "&a混沌飞行模块&f"
			x: 19.5d
			y: 0.75d
		}
		{
			dependencies: ["38AD547C36BDF4C4"]
			id: "35A674E2C6F66673"
			rewards: [{
				exclude_from_claim_all: true
				id: "23CBDEAE42B4DDC6"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "53E64B7B94EBE42A"
				item: "draconicevolution:item_chaotic_proj_accuracy"
				type: "item"
			}]
			title: "&a混沌箭矢精度模块&f"
			x: 19.5d
			y: -7.5d
		}
		{
			dependencies: ["0499A372F55D04C8"]
			id: "10E4ADA1D0063EB7"
			rewards: [{
				exclude_from_claim_all: true
				id: "560394B2D8706345"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "7E5AFCCDE171EEA5"
				item: "draconicevolution:item_chaotic_proj_velocity"
				type: "item"
			}]
			title: "&a混沌箭矢速度模块&f"
			x: 19.5d
			y: -9.0d
		}
		{
			dependencies: ["4903BDC389C73082"]
			id: "52C301DF1776E0D8"
			rewards: [{
				exclude_from_claim_all: true
				id: "0FABAC1744F97143"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "45C1EEF97BCF5A10"
				item: "draconicevolution:item_chaotic_proj_damage"
				type: "item"
			}]
			title: "&a混沌箭矢伤害模块&f"
			x: 19.5d
			y: -9.75d
		}
		{
			dependencies: ["070F8045CC14B50D"]
			id: "154D9EA863FD2A32"
			rewards: [{
				exclude_from_claim_all: true
				id: "228BA2D039C606C8"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "23F6AD87539D4BE0"
				item: "draconicevolution:item_chaotic_proj_penetration"
				type: "item"
			}]
			title: "&a混沌箭矢穿透模块&f"
			x: 19.5d
			y: -8.25d
		}
		{
			dependencies: ["3DF794EBCAA7AE1A"]
			id: "7496B2380A23FDC6"
			rewards: [{
				exclude_from_claim_all: true
				id: "40C862693B920C84"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "77FC8AB08FF5339C"
				item: "draconicevolution:item_chaotic_tree_harvest"
				type: "item"
			}]
			title: "混沌&a树木砍伐&f者"
			x: 19.5d
			y: -3.75d
		}
		{
			dependencies: ["7A4ABFCD12202A91"]
			description: ["首先您需要支持OP能量的物品:&a飞龙弓&f、&a混沌通量容器&f或能量法杖等.然后在物品中安装&a能量模块&f作为OP存储单元.准备好&a能量注入器&f并充入OP后,放入物品即可!当OP能量开始流动发光时,就说明运作正常."]
			id: "295EB6CFEC029AE2"
			rewards: [{
				id: "7968898E0EF56026"
				item: "draconicevolution:draconic_relay_crystal"
				type: "item"
			}]
			shape: "pentagon"
			tasks: [{
				id: "748F42CFD74B7147"
				item: "draconicevolution:energy_transfuser"
				type: "item"
			}]
			title: "为物品添加OP能量"
			x: 7.0d
			y: 2.5d
		}
		{
			dependencies: ["7A4ABFCD12202A91"]
			description: ["IO水晶可作为连接区块传输能量的媒介.使用水晶绑定器时,按住Shift+&a右键&f设置起点,再&a右键&f点击要连接的水晶.查看水晶时会显示「从方块输入」或「向方块输出」,分别表示能量来源和输送目标."]
			id: "5D87088E8D71DB9B"
			rewards: [{
				id: "7980CC45AFED67D6"
				item: "draconicevolution:crystal_binder"
				type: "item"
			}]
			shape: "pentagon"
			tasks: [{
				id: "664130FB28E419EF"
				item: {
					Count: 1
					id: "itemfilters:or"
					tag: {
						items: [
							{
								Count: 1b
								id: "draconicevolution:basic_io_crystal"
							}
							{
								Count: 1b
								id: "draconicevolution:draconic_io_crystal"
							}
							{
								Count: 1b
								id: "draconicevolution:wyvern_io_crystal"
							}
						]
					}
				}
				type: "item"
			}]
			title: "传输OP能量"
			x: 5.0d
			y: 2.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["查看模块说明了解DE物品的使用与升级方法!它能开垦农田,或者...拿走你的钱？哦等等这是Minecraft的锄头,我刚才想到别的了."]
			id: "164BBBD98163F64C"
			rewards: [{
				exclude_from_claim_all: true
				id: "510C577B7A82333D"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "4679901448A62578"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									475754603
									972900585
									-**********
									-718867576
								]
							}
						}
					}
					id: "draconicevolution:wyvern_hoe"
				}
				type: "item"
			}]
			title: "&a飞龙锄&f"
			x: 12.5d
			y: -4.5d
		}
		{
			dependencies: ["51CF09EC333DB1A3"]
			description: ["查看模块说明了解DE物品的使用与升级方法!&a飞龙&f斧在战斗更新后类似原版斧头,既可设计为杀敌利器,也能用作伐木工具."]
			id: "30F97EB743B0A07F"
			rewards: [{
				exclude_from_claim_all: true
				id: "705181BDA592FBF9"
				table_id: 5036812014823277174L
				type: "random"
			}]
			tasks: [{
				id: "4C3121ED2A5CEDAD"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									-639362057
									-**********
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:wyvern_axe"
				}
				type: "item"
			}]
			title: "&a飞龙斧&f"
			x: 10.5d
			y: -4.5d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["&a飞龙斧&f的&a强化&f版本"]
			id: "5895B7FA5497BD68"
			rewards: [{
				exclude_from_claim_all: true
				id: "25901FCB86B61111"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "4C963CB81FC120C0"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									751625970
									667894999
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:draconic_axe"
				}
				type: "item"
			}]
			title: "&a神龙斧&f"
			x: 10.5d
			y: -3.0d
		}
		{
			dependencies: ["5ADFC45B03BAB852"]
			description: ["升级版的锄头,会不会变成优雅的女士呢？"]
			id: "34D3F6D54F5B26C2"
			rewards: [{
				exclude_from_claim_all: true
				id: "6A343FA37FD4C127"
				table_id: 7145388980997284804L
				type: "random"
			}]
			tasks: [{
				id: "04B28A210F54A390"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-**********
									182207722
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:draconic_hoe"
				}
				type: "item"
			}]
			title: "&a神龙锄&f"
			x: 12.5d
			y: -3.0d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["这就是关键所在,你是想像卡车一样狠狠伤害敌人但速度更慢？还是宁愿做个用剑的普通人."]
			id: "3BE3B54B4A681668"
			rewards: [{
				exclude_from_claim_all: true
				id: "20CCF663FFB8B1D2"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "26477C55298CBA27"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: {
									mining_speed: {
										hud: 1b
										value: 1.0d
									}
								}
								provider_id: [I;
									952293489
									-765310133
									-**********
									-**********
								]
							}
						}
					}
					id: "draconicevolution:chaotic_axe"
				}
				type: "item"
			}]
			title: "&a混沌斧&f"
			x: 10.5d
			y: -1.5d
		}
		{
			dependencies: ["763EB5C911D7E78C"]
			description: ["一把&a混沌锄&f,我以前还和其中一把约会过呢."]
			id: "52C3F61D678B00F5"
			rewards: [{
				exclude_from_claim_all: true
				id: "3200568E991D54ED"
				table_id: 1711091222353074689L
				type: "random"
			}]
			tasks: [{
				id: "22CE82B2A8B1584A"
				item: {
					Count: 1
					ForgeCaps: {
						Parent: {
							energy: {
								energy: 0
							}
							module_host: {
								modules: [ ]
								properties: { }
								provider_id: [I;
									-825978574
									-**********
									-**********
									642985972
								]
							}
						}
					}
					id: "draconicevolution:chaotic_hoe"
				}
				type: "item"
			}]
			title: "&a混沌锄&f"
			x: 12.5d
			y: -1.5d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["无线&a能量链接&f能让无线水晶给你的物品充能.&a模块越多&f,它能承受的功率就越大,传输效率也更高."]
			id: "20D8DA523E900A52"
			rewards: [{
				exclude_from_claim_all: true
				id: "2C570D8951F25581"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "6263FF7F49AAC2E5"
				item: "draconicevolution:item_wyvern_energy_link"
				type: "item"
			}]
			title: "无线&a能量链接&f"
			x: 18.0d
			y: -11.25d
		}
		{
			dependencies: ["20D8DA523E900A52"]
			description: ["显然&a能量链接&f或隧道越高级,能传输的功率就越大,但你可能也注意到&a能量链接&f有个缺陷——它不能在维度间工作!幸好高级版本可以,只要确保另一端区块已加载."]
			id: "4B9FF9E972B0E530"
			rewards: [{
				exclude_from_claim_all: true
				id: "701701C03EDFC5BE"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "7634D5C19B7C8904"
				item: "draconicevolution:item_draconic_energy_link"
				type: "item"
			}]
			title: "&a量子能量隧道模块&f"
			x: 18.75d
			y: -11.25d
		}
		{
			dependencies: ["4B9FF9E972B0E530"]
			id: "4638F21544D6D337"
			rewards: [{
				exclude_from_claim_all: true
				id: "341BFB320FE17E57"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "64938488390AE717"
				item: "draconicevolution:item_chaotic_energy_link"
				type: "item"
			}]
			title: "&a混沌纠缠量子能量隧道模块&f"
			x: 19.5d
			y: -11.25d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["物理定律怎么总是背叛我们？重力会让射出的箭矢逐渐下坠.重力补偿让我们少操这份心."]
			id: "376D154F27A00BCD"
			rewards: [{
				exclude_from_claim_all: true
				id: "637FC27A8552410D"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "6C567B271F8A7871"
				item: "draconicevolution:item_wyvern_proj_grav_comp"
				type: "item"
			}]
			title: "&a飞龙箭矢减速模块&f"
			x: 18.0d
			y: -6.75d
		}
		{
			dependencies: ["376D154F27A00BCD"]
			id: "265D0DB0E8CED73B"
			rewards: [{
				exclude_from_claim_all: true
				id: "494957D3FE4F7318"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "53D181AA185BE944"
				item: "draconicevolution:item_draconic_proj_grav_comp"
				type: "item"
			}]
			title: "&a神龙箭矢重力修正模块&f"
			x: 18.75d
			y: -6.75d
		}
		{
			dependencies: ["265D0DB0E8CED73B"]
			id: "7EB1C1AE52A0027F"
			rewards: [{
				exclude_from_claim_all: true
				id: "021B5047554D81B0"
				table_id: 1711091222353074689L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "0FE2A541A688C114"
				item: "draconicevolution:item_chaotic_proj_grav_comp"
				type: "item"
			}]
			title: "&a混沌箭矢重力修正模块&f"
			x: 19.5d
			y: -6.75d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["牛顿告诉我们:作用力等于反作用力.当箭矢击中生物时,生物施加的反作用力会使箭矢减速——除非你有这个模块.它不会让所有生物都像纸一样脆弱,而是让箭矢仿佛穿透无物.箭矢穿透目标后不会减速."]
			id: "60C27206F1414DD6"
			rewards: [{
				exclude_from_claim_all: true
				id: "0CDF154E0CA633EA"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "7D162713642FEC01"
				item: "draconicevolution:item_draconic_proj_anti_immune"
				type: "item"
			}]
			title: "&a神龙反箭矢免疫模块&f"
			x: 21.0d
			y: -9.0d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["大多数弓都是半自动的:拉弦、放箭、再装填,周而复始.现在只需按住&a右键&f,弓就会在拉满时自动连射!"]
			id: "4ABEF61DADBD0640"
			rewards: [{
				exclude_from_claim_all: true
				id: "7CAE8975EFD8FBA3"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "344FA029990B58A7"
				item: "draconicevolution:item_wyvern_auto_fire"
				type: "item"
			}]
			title: "&a自动射击模块&f"
			x: 21.0d
			y: -7.5d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["由此工具获取的物品会自动存入你的&a末影箱&f.注意是原版末影箱,不是模组的."]
			id: "731A70C25C2C7281"
			rewards: [{
				exclude_from_claim_all: true
				id: "1A925BB3A71AC024"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "2871F9FB5BF04B78"
				item: "draconicevolution:item_wyvern_ender_collection"
				type: "item"
			}]
			title: "&a末影收集模块&f"
			x: 18.0d
			y: -5.25d
		}
		{
			dependencies: ["731A70C25C2C7281"]
			description: ["功能相同,但增加了过滤选项."]
			id: "74C715F8F24B8BAE"
			rewards: [{
				exclude_from_claim_all: true
				id: "0DC5355FBB92DB50"
				table_id: 7145388980997284804L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "5A39B70F8A227ECB"
				item: "draconicevolution:item_draconic_ender_collection"
				type: "item"
			}]
			title: "&a末影收集过滤模块&f"
			x: 18.75d
			y: -5.25d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["过滤还不够？!你还想销毁物品？!你这个恶魔!"]
			id: "79886D63280DBBF3"
			rewards: [{
				exclude_from_claim_all: true
				id: "7D8EC629BA6F70CC"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "14816C5D68A251CF"
				item: "draconicevolution:item_wyvern_junk_filter"
				type: "item"
			}]
			title: "&a选择性焚烧模块&f"
			x: 21.0d
			y: -5.25d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["当'下落'时你的挖掘速度会减慢.这包括在创造模式飞行或甚至在水里弹跳时.幸运的是你可以使用空气亲和附魔来抵消这个效果!虽然不是原版附魔但仍然很有帮助!哦等等我本来应该谈论这个模块的...同样的道理!"]
			id: "2AAE61752728EA8D"
			rewards: [{
				exclude_from_claim_all: true
				id: "01AD0176C25009E1"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "42F5BB7FAEE85979"
				item: "draconicevolution:item_wyvern_mining_stability"
				type: "item"
			}]
			title: "&a挖掘稳定器&f模块"
			x: 21.0d
			y: 0.75d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["其实就是开启自动跳跃功能.我个人很喜欢,用不用随你!"]
			id: "4AAADEC52D16461A"
			rewards: [{
				exclude_from_claim_all: true
				id: "1B72EE99A62E3862"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "77F6D4559C370B83"
				item: "draconicevolution:item_wyvern_hill_step"
				type: "item"
			}]
			title: "&a攀爬模块&f"
			x: 21.0d
			y: 1.5d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["水有点粘稠,或者说比空气密度大.因此水下挖掘会变慢.你可以用&a水亲和&f附魔,或者用这个模块.自己选吧!"]
			id: "39865681B03CABAA"
			rewards: [{
				exclude_from_claim_all: true
				id: "014FBFC2864E0441"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "6ECA086766BF1840"
				item: "draconicevolution:item_wyvern_aqua_adapt"
				type: "item"
			}]
			title: "&a水适应模块&f"
			x: 21.0d
			y: 0.0d
		}
		{
			dependencies: ["4EFFE300EC781C3E"]
			description: ["除非你是用&a夜视&f材质包的作弊玩家,否则黑暗总会让人不安.好在DE胸甲里的&a夜视模块&f能克服黑暗恐惧...不过对黑暗中的怪物可没用."]
			id: "32EBB3D53AA91E09"
			rewards: [{
				exclude_from_claim_all: true
				id: "58052130D410273C"
				table_id: 5036812014823277174L
				type: "random"
			}]
			shape: "square"
			size: 0.75d
			tasks: [{
				id: "24DF3AAA0346AA18"
				item: "draconicevolution:item_wyvern_night_vision"
				type: "item"
			}]
			title: "&a夜视模块&f"
			x: 21.0d
			y: -0.75d
		}
		{
			can_repeat: false
			description: [
				"本任务由&eAllTheMods团队&r或&2社区贡献者&r创作,专用于AllTheMods整合包."
				"由于所有&eAllTheMods&r整合包均采用&e保留所有权利&r许可协议,未经&eAllTheMods团队&r明确授权,不得在任何非官方发布的公开整合包中使用此任务."
				""
				""
				""
				"该任务默认隐藏,若您能看到此说明,说明您正处于编辑模式."
			]
			disable_toast: true
			hide_details_until_startable: true
			hide_until_deps_visible: true
			icon: {
				Count: 1
				id: "ftbquests:custom_icon"
				tag: {
					Icon: "minecraft:item/barrier"
				}
			}
			id: "366431661CC43A6E"
			invisible: true
			optional: true
			shape: "octagon"
			tasks: [
				{
					disable_toast: true
					id: "2AE8A0F6A6C2D453"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
				{
					id: "1EFD3D7C04F9682A"
					title: "AllTheMods任务线"
					type: "checkmark"
				}
			]
			x: 8.0d
			y: -12.5d
		}
	]
	title: "&d龙之进化/龙之研究&f"
}
