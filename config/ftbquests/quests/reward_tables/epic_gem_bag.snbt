{
	icon: {
		Count: 1
		id: "apotheosis:gem"
		tag: {
			gem: "apotheosis:the_nether/inferno"
			rarity: "epic"
		}
	}
	id: "101AB5CE5A067BA6"
	loot_size: 1
	order_index: 66
	rewards: [
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/ballast"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/brawlers"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/breach"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/combatant"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/guardian"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/lightning"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/lunar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/samurai"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/slipstream"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/solar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/splendor"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/tyrannical"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:core/warlord"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "irons_spellbooks:intelligent"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:overworld/earth"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:overworld/royalty"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:the_end/endersurge"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:the_end/mageslayer"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:the_nether/blood_lord"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:the_nether/inferno"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:twilight/forest"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:epic"
					}
					gem: "apotheosis:twilight/queen"
				}
			}
		}
	]
	title: "史诗宝石袋"
	use_title: true
}
