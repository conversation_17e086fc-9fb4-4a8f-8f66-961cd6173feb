{
	icon: {
		Count: 1
		id: "apotheosis:gem"
		tag: {
			gem: "apotheosis:overworld/earth"
			rarity: "rare"
		}
	}
	id: "5B39DB9E88926050"
	loot_size: 1
	order_index: 64
	rewards: [
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/ballast"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "irons_spellbooks:intelligent"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/brawlers"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/breach"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/combatant"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/guardian"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/lunar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/samurai"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/slipstream"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/solar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/splendor"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/tyrannical"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:uncommon"
					}
					gem: "apotheosis:core/warlord"
				}
			}
		}
	]
	title: "普通宝石袋"
	use_title: true
}
