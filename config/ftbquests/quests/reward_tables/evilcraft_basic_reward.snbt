{
	id: "67D8094B10FAA687"
	loot_size: 1
	order_index: 37
	rewards: [
		{ count: 2, item: "evilcraft:dark_gem", random_bonus: 2, weight: 100.0f }
		{ item: "evilcraft:dark_power_gem", random_bonus: 1, weight: 50.0f }
		{ item: "evilcraft:hardened_blood", weight: 50.0f }
		{ count: 2, item: "evilcraft:hardened_blood_shard", random_bonus: 4, weight: 50.0f }
		{
			item: {
				Count: 1
				ForgeCaps: {
					Parent: {
						Amount: 0
						FluidName: "minecraft:empty"
						capacity: 5000
					}
				}
				id: "evilcraft:blood_extractor"
				tag: {
					Fluid: {
						Amount: 0
						FluidName: "minecraft:empty"
					}
					capacity: 5000
				}
			}
			weight: 25.0f
		}
		{
			item: {
				Count: 1
				ForgeCaps: {
					Parent: {
						Amount: 0
						FluidName: "minecraft:empty"
						capacity: 16000
					}
				}
				id: "evilcraft:dark_tank"
				tag: {
					Fluid: {
						Amount: 0
						FluidName: "minecraft:empty"
					}
					capacity: 16000
				}
			}
			weight: 25.0f
		}
		{ item: "evilcraft:blook", random_bonus: 1, weight: 25.0f }
		{ item: "evilcraft:potentia_sphere", random_bonus: 1, weight: 25.0f }
		{ count: 2, item: "evilcraft:dark_gem_crushed", random_bonus: 4, weight: 100.0f }
		{ item: "evilcraft:box_of_eternal_closure", weight: 10.0f }
		{ item: "evilcraft:bowl_of_promises_dusted", weight: 10.0f }
		{ count: 5, item: "evilcraft:condensed_blood", random_bonus: 10, weight: 75.0f }
		{ item: "evilcraft:blood_infusion_core", weight: 10.0f }
		{ item: "evilcraft:vengeance_essence", weight: 10.0f }
		{
			item: {
				Count: 1
				id: "evilcraft:vengeance_pickaxe"
				tag: {
					Damage: 0
					Enchantments: [
						{
							id: "evilcraft:vengeance"
							lvl: 3s
						}
						{
							id: "minecraft:fortune"
							lvl: 5s
						}
					]
				}
			}
			weight: 5.0f
		}
		{
			item: {
				Count: 1
				id: "evilcraft:vein_sword"
				tag: {
					Damage: 0
					Enchantments: [{
						id: "minecraft:looting"
						lvl: 2s
					}]
				}
			}
			weight: 5.0f
		}
	]
	title: "&d邪恶工艺&f基础奖励"
	use_title: true
}
