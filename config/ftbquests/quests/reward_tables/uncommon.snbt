{
	icon: "ftbquests:lootcrate"
	id: "3A3BDBA4E9AD13C4"
	loot_size: 1
	order_index: 13
	rewards: [
		{
			item: {
				Count: 1
				id: "reliquary:handgun"
				tag: {
					bulletCount: 8s
					coolDownTime: 472179L
					magazineType: "reliquary:magazines/neutral_magazine"
				}
			}
			weight: 5.0f
		}
		{ item: "mob_grinding_utils:saw", weight: 3.0f }
		{
			item: {
				Count: 1
				id: "modularrouters:distributor_module"
				tag: {
					modularrouters: {
						ModuleFilter: { }
					}
				}
			}
		}
		{
			item: {
				Count: 1
				id: "modularrouters:energy_distributor_module"
				tag: {
					modularrouters: {
						Direction: "NONE"
						ModuleFilter: { }
					}
				}
			}
		}
		{
			item: {
				Count: 1
				id: "modularrouters:puller_module_2"
				tag: {
					modularrouters: {
						ModuleFilter: { }
					}
				}
			}
		}
		{ count: 4, item: "modularrouters:speed_upgrade", random_bonus: 4 }
		{ item: "pipez:advanced_upgrade", random_bonus: 1, weight: 3.0f }
		{ count: 8, item: "pipez:universal_pipe", random_bonus: 16, weight: 10.0f }
		{ item: "productivebees:upgrade_breeding" }
		{ item: "mekanism:advanced_tier_installer" }
		{
			item: {
				Count: 1
				id: "mekanism:basic_energy_cube"
				tag: {
					mekData: {
						EnergyContainers: [{
							Container: 0b
							stored: "4000000"
						}]
					}
				}
			}
			weight: 3.0f
		}
		{ item: "mekanismgenerators:wind_generator", weight: 5.0f }
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:fortune"
						lvl: 1s
					}]
				}
			}
		}
		{ item: "minecraft:netherite_ingot" }
		{
			item: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "potionsmaster:netherite_sight"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "potionsmaster:diamond_sight"
				}
			}
			weight: 5.0f
		}
		{ item: "mob_grinding_utils:golden_egg", weight: 2.0f }
		{ item: "mob_grinding_utils:rotten_egg", weight: 2.0f }
		{
			item: {
				Count: 1
				id: "ironjetpacks:jetpack"
				tag: {
					Id: "ironjetpacks:iron"
					Throttle: 1.0d
				}
			}
		}
		{ item: "hostilenetworks:sim_chamber" }
		{ item: "hostilenetworks:loot_fabricator" }
		{ item: "functionalstorage:gold_upgrade", weight: 2.0f }
		{
			item: {
				Count: 1
				id: "enderchests:ender_chest"
				tag: {
					code: "000"
					owner: "all"
				}
			}
			weight: 2.0f
		}
		{ item: "sophisticatedbackpacks:void_upgrade", weight: 2.0f }
		{ item: "dankstorage:dank_3" }
		{ item: "ars_nouveau:glyph_accelerate" }
		{ item: "ars_nouveau:glyph_aoe" }
		{ item: "reliquary:lantern_of_paranoia", weight: 2.0f }
		{ item: "sophisticatedbackpacks:stack_upgrade_tier_1", weight: 5.0f }
		{ item: "sophisticatedstorage:stack_upgrade_tier_1", weight: 5.0f }
		{ count: 2, item: "apotheosis:gem_dust", random_bonus: 4, weight: 10.0f }
		{ item: "sophisticatedstorage:void_upgrade", weight: 10.0f }
		{
			item: {
				Count: 1
				id: "sophisticatedstorage:iron_chest"
				tag: {
					woodType: "oak"
				}
			}
			weight: 10.0f
		}
		{ item: "ars_nouveau:glyph_explosion" }
		{ item: "productivebees:upgrade_simulator" }
		{ item: "dankstorage:dank_2", weight: 5.0f }
		{ item: "productivebees:upgrade_time", weight: 2.0f }
		{ count: 3, item: "minecraft:diamond", random_bonus: 3, weight: 10.0f }
		{ count: 8, item: "minecraft:iron_ingot", random_bonus: 8, weight: 20.0f }
		{ count: 4, item: "minecraft:gold_ingot", random_bonus: 4, weight: 15.0f }
		{ count: 16, item: "minecraft:lapis_lazuli", random_bonus: 8, weight: 20.0f }
		{ item: "minecraft:iron_block", weight: 15.0f }
		{ item: "minecraft:diamond_block", weight: 10.0f }
		{ item: "minecraft:gold_block", weight: 12.0f }
		{ item: "minecraft:redstone_block", random_bonus: 2, weight: 20.0f }
		{ item: "minecraft:emerald_block", weight: 10.0f }
		{
			item: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "potionsmaster:diamond_sight"
				}
			}
			weight: 5.0f
		}
		{
			item: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "potionsmaster:gold_sight"
				}
			}
			weight: 10.0f
		}
		{
			item: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "potionsmaster:iron_sight"
				}
			}
			weight: 15.0f
		}
		{
			item: {
				Count: 1
				id: "minecraft:potion"
				tag: {
					Potion: "potionsmaster:redstone_sight"
				}
			}
			weight: 15.0f
		}
		{ item: "minecraft:netherite_scrap", random_bonus: 1, weight: 5.0f }
		{ item: "minecraft:netherite_ingot" }
		{ count: 2, item: "croptopia:toast", random_bonus: 4, weight: 20.0f }
		{ count: 2, item: "croptopia:buttered_toast", random_bonus: 4, weight: 7.0f }
		{ count: 2, item: "croptopia:avocado_toast", random_bonus: 4, weight: 5.0f }
		{ item: "farmersdelight:hamburger", random_bonus: 1, weight: 15.0f }
		{ item: "croptopia:fruit_salad", weight: 5.0f }
		{ count: 8, item: "minecraft:redstone", random_bonus: 16, weight: 15.0f }
		{ item: "minecraft:lava_bucket", weight: 5.0f }
		{ item: "mekanismgenerators:wind_generator", weight: 10.0f }
		{ item: "powah:solar_panel_basic", weight: 10.0f }
		{ item: "powah:thermo_generator_basic", weight: 5.0f }
		{ item: "mekanismgenerators:gas_burning_generator", weight: 5.0f }
		{ item: "functionalstorage:compacting_drawer", weight: 10.0f }
		{ item: "functionalstorage:storage_controller", weight: 3.0f }
		{ count: 4, item: "minecraft:ender_pearl", random_bonus: 4, weight: 10.0f }
		{ item: "minecraft:ender_eye", random_bonus: 2, weight: 5.0f }
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:protection"
						lvl: 2s
					}]
				}
			}
		}
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:fire_protection"
						lvl: 2s
					}]
				}
			}
		}
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:feather_falling"
						lvl: 2s
					}]
				}
			}
		}
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:thorns"
						lvl: 2s
					}]
				}
			}
		}
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:sharpness"
						lvl: 2s
					}]
				}
			}
		}
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:looting"
						lvl: 2s
					}]
				}
			}
		}
		{
			item: {
				Count: 1
				id: "minecraft:enchanted_book"
				tag: {
					StoredEnchantments: [{
						id: "minecraft:fortune"
						lvl: 2s
					}]
				}
			}
		}
		{ count: 4, item: "minecraft:quartz", random_bonus: 4, weight: 10.0f }
		{ count: 2, item: "ae2:silicon", random_bonus: 4, weight: 15.0f }
		{ item: "botania:mana_pool" }
		{ count: 2, item: "botania:manasteel_ingot", random_bonus: 2, weight: 10.0f }
		{ item: "botania:mana_diamond", random_bonus: 2, weight: 5.0f }
		{ item: "botania:mana_pearl", random_bonus: 2, weight: 5.0f }
		{
			item: {
				Count: 1
				id: "twilightforest:giant_sword"
				tag: {
					Damage: 0
				}
			}
		}
		{
			item: {
				Count: 1
				id: "aquaculture:neptunium_sword"
				tag: {
					Damage: 0
				}
			}
		}
		{ item: "reliquary:pedestals/passive/white_passive_pedestal" }
		{ count: 4, item: "functionalstorage:oak_1", random_bonus: 4, weight: 10.0f }
		{ item: "mob_grinding_utils:absorption_hopper" }
		{ count: 2, item: "botanypots:terracotta_hopper_botany_pot", random_bonus: 2, weight: 10.0f }
		{ item: "mysticalagriculture:imperium_essence", weight: 8.0f }
		{ item: "mysticalagriculture:tertium_essence", random_bonus: 1, weight: 10.0f }
		{ count: 2, item: "mysticalagriculture:prudentium_essence", random_bonus: 2, weight: 20.0f }
		{ item: "functionalstorage:void_upgrade", random_bonus: 2, weight: 10.0f }
		{ item: "sophisticatedbackpacks:stack_upgrade_tier_2" }
		{ item: "sophisticatedstorage:stack_upgrade_tier_2" }
		{ item: "minecraft:saddle", weight: 5.0f }
		{ item: "minecraft:name_tag", weight: 5.0f }
		{ item: "ironfurnaces:gold_furnace", weight: 3.0f }
		{ item: "reliquary:fertile_lily_pad", random_bonus: 2, weight: 5.0f }
		{ item: "minecraft:wither_skeleton_skull", weight: 10.0f }
	]
	title: "&a普通奖励"
	use_title: true
}
