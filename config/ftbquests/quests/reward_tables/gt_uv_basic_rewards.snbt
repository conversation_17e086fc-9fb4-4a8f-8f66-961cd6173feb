{
	id: "193AFBD8DBD51FB8"
	loot_size: 1
	order_index: 71
	rewards: [
		{ item: "gtceu:crystal_processor_mainframe", weight: 3.0f }
		{ item: "gtceu:uv_robot_arm", weight: 3.0f }
		{ count: 4, item: "gtceu:advanced_smd_capacitor", random_bonus: 4, weight: 4.0f }
		{ count: 4, item: "gtceu:advanced_smd_transistor", random_bonus: 4, weight: 4.0f }
		{ item: "gtceu:multilayer_fiber_reinforced_printed_circuit_board", weight: 4.0f }
		{ item: "gtceu:uv_electric_pump", weight: 4.0f }
		{ item: "gtceu:uv_electric_piston", weight: 4.0f }
		{ item: "gtceu:uv_conveyor_module", weight: 4.0f }
		{ item: "gtceu:crystal_processor_computer", weight: 4.0f }
		{ item: "gtceu:uv_electric_motor", weight: 4.0f }
		{ count: 12, item: "gtceu:highly_advanced_soc", random_bonus: 24, weight: 5.0f }
		{ count: 12, item: "gtceu:highly_advanced_soc_wafer", random_bonus: 24, weight: 5.0f }
		{ count: 6, item: "gtceu:agar_dust", random_bonus: 18, weight: 6.0f }
		{ item: "gtceu:crystal_processor_assembly", weight: 6.0f }
		{ count: 6, item: "gtceu:advanced_smd_inductor", random_bonus: 12, weight: 8.0f }
		{ count: 6, item: "gtceu:advanced_smd_diode", random_bonus: 12, weight: 8.0f }
		{ count: 6, item: "gtceu:advanced_smd_resistor", random_bonus: 12, weight: 8.0f }
		{ count: 6, item: "gtceu:nand_memory_chip", random_bonus: 12, weight: 8.0f }
		{ count: 6, item: "gtceu:nor_memory_chip", random_bonus: 12, weight: 8.0f }
		{ count: 8, item: "gtceu:neutronium_wafer", random_bonus: 8, weight: 10.0f }
		{ item: "gtceu:uv_machine_hull", weight: 10.0f }
		{ count: 8, item: "gtceu:tritanium_ingot", random_bonus: 16, weight: 10.0f }
		{ count: 6, item: "gtceu:crystal_soc", random_bonus: 6, weight: 10.0f }
		{ count: 6, item: "gtceu:stem_cells", random_bonus: 6, weight: 10.0f }
		{ count: 8, item: "gtceu:darmstadtium_plate", random_bonus: 16, weight: 10.0f }
		{ count: 8, item: "gtceu:yttrium_dust", random_bonus: 16, weight: 12.0f }
		{ count: 6, item: "gtceu:neutronium_dust", random_bonus: 12, weight: 12.0f }
		{ item: "gtceu:uv_machine_casing", weight: 12.0f }
		{ count: 6, item: "gtceu:rhodium_dust", random_bonus: 12, weight: 10.0f }
		{ count: 4, item: "gtceu:neuro_processing_unit", random_bonus: 8, weight: 10.0f }
	]
	title: "格雷科技UV级基础奖励"
}
