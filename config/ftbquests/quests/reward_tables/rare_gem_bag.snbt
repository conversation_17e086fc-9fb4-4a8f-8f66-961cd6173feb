{
	icon: {
		Count: 1
		id: "apotheosis:gem"
		tag: {
			gem: "apotheosis:overworld/royalty"
			rarity: "rare"
		}
	}
	id: "667A8490FFFF37F7"
	loot_size: 1
	order_index: 65
	rewards: [
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/ballast"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/brawlers"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/breach"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/combatant"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/guardian"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/lightning"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/lunar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/samurai"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/slipstream"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/solar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/splendor"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/tyrannical"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:core/warlord"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "irons_spellbooks:intelligent"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:overworld/earth"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:overworld/royalty"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:the_nether/blood_lord"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:the_nether/inferno"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:twilight/forest"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:rare"
					}
					gem: "apotheosis:twilight/queen"
				}
			}
		}
	]
	title: "稀有宝石袋"
	use_title: true
}
