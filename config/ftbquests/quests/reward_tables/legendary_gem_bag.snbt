{
	icon: {
		Count: 1
		id: "apotheosis:gem"
		tag: {
			gem: "apotheosis:core/warlord"
			rarity: "mythic"
		}
	}
	id: "32CB28874F648ECF"
	loot_size: 1
	order_index: 67
	rewards: [
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/ballast"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/brawlers"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/breach"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/combatant"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/guardian"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/lightning"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/lunar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/samurai"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/slipstream"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/solar"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/splendor"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/tyrannical"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:core/warlord"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "irons_spellbooks:intelligent"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:overworld/earth"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:overworld/royalty"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:the_end/endersurge"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:the_end/mageslayer"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:the_nether/blood_lord"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:the_nether/inferno"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:twilight/forest"
				}
			}
		}
		{
			item: {
				Count: 1
				id: "apotheosis:gem"
				tag: {
					affix_data: {
						rarity: "apotheosis:mythic"
					}
					gem: "apotheosis:twilight/queen"
				}
			}
		}
	]
	title: "传奇宝石袋"
	use_title: true
}
