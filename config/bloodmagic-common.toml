
#Stops the listed entities from being used in the Well of Suffering.
#Use the registry name of the entity. Vanilla entities do not require the modid.
[Blacklist]
	wellOfSuffering = []

#Amount of LP the Sacrificial Dagger should provide for each damage dealt.
["Config Values"]
	#Range: 0 ~ 10000
	sacrificialDaggerConversion = 100
	#Declares the amount of LP gained per HP sacrificed for the given entity.
	#Setting the value to 0 will blacklist it.
	#Use the registry name of the entity followed by a ';' and then the value you want.
	#Vanilla entities do not require the modid.
	sacrificialValues = ["villager;100", "slime;15", "enderman;10", "cow;100", "chicken;100", "horse;100", "sheep;100", "wolf;100", "ocelot;100", "pig;100", "rabbit;100"]
	#State that the dungeon spawning ritual can only be activated when using a Creative Activation Crystal.
	#Used on servers for if you do not trust your players to not destroy other people's bases.
	makeDungeonRitualCreativeOnly = false

