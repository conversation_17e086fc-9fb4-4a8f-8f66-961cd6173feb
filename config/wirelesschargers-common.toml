[General]
    [General."Basic Wireless Block Charger"]
        # In what range should the Basic Wireless Block Charger power blocks? For example, a range of 1 means a 3x3x3 area.
        # Allowed range: 1 ~ 5 - Default: 2
        basicBlockChargerRange = 2

        # How much energy should the Basic Wireless Block Charger be able to store?
        # Allowed range: 1000 ~ 10000000 - Default: 25000
        basicBlockChargerCapacity = 25000

        # How much energy per tick per block should the Basic Wireless Block Charger transfer? 1 second = 20 ticks.
        # Allowed range: 10 ~ 10000 - Default: 50
        basicBlockChargerTransferRate = 50

    [General."Advanced Wireless Block Charger"]
        # In what range should the Advanced Wireless Block Charger power blocks? For example, a range of 1 means a 3x3x3 area.
        # Allowed range: 1 ~ 5 - Default: 3
        advancedBlockChargerRange = 3

        # How much energy should the Advanced Wireless Block Charger be able to store?
        # Allowed range: 1000 ~ 10000000 - Default: 100000
        advancedBlockChargerCapacity = 100000

        # How much energy per tick per block should the Advanced Wireless Block Charger transfer? 1 second = 20 ticks.
        # Allowed range: 10 ~ 10000 - Default: 200
        advancedBlockChargerTransferRate = 200

    [General."Basic Wireless Player Charger"]
        # In what range should the Basic Wireless Player Charger power players' items? For example, a range of 1 means a 3x3x3 area.
        # Allowed range: 1 ~ 10 - Default: 4
        basicPlayerChargerRange = 4

        # How much energy should the Basic Wireless Player Charger be able to store?
        # Allowed range: 1000 ~ 10000000 - Default: 25000
        basicPlayerChargerCapacity = 25000

        # How much energy per tick per block should the Basic Wireless Player Charger transfer? 1 second = 20 ticks.
        # Allowed range: 10 ~ 10000 - Default: 50
        basicPlayerChargerTransferRate = 50

    [General."Advanced Wireless Player Charger"]
        # In what range should the Advanced Wireless Player Charger power players' items? For example, a range of 1 means a 3x3x3 area.
        # Allowed range: 1 ~ 10 - Default: 6
        advancedPlayerChargerRange = 6

        # How much energy should the Advanced Wireless Player Charger be able to store?
        # Allowed range: 1000 ~ 10000000 - Default: 100000
        advancedPlayerChargerCapacity = 100000

        # How much energy per tick per block should the Advanced Wireless Player Charger transfer? 1 second = 20 ticks.
        # Allowed range: 10 ~ 10000 - Default: 200
        advancedPlayerChargerTransferRate = 200

