key.attack:key.mouse.left:NONE
key.use:key.mouse.right:NONE
key.forward:key.keyboard.w:NONE
key.left:key.keyboard.a:NONE
key.back:key.keyboard.s:NONE
key.right:key.keyboard.d:NONE
key.jump:key.keyboard.space:NONE
key.sneak:key.keyboard.left.shift:NONE
key.sprint:key.keyboard.left.control:NONE
key.drop:key.keyboard.q:NONE
key.inventory:key.keyboard.e:NONE
key.chat:key.keyboard.t:NONE
key.playerlist:key.keyboard.tab:NONE
key.pickItem:key.mouse.middle:NONE
key.command:key.keyboard.slash:NONE
key.socialInteractions:key.keyboard.p:NONE
key.screenshot:key.keyboard.f2:NONE
key.togglePerspective:key.keyboard.f5:NONE
key.smoothCamera:key.keyboard.unknown:NONE
key.fullscreen:key.keyboard.f11:NONE
key.spectatorOutlines:key.keyboard.unknown:NONE
key.swapOffhand:key.keyboard.f:NONE
key.saveToolbarActivator:key.keyboard.c:NONE
key.loadToolbarActivator:key.keyboard.x:NONE
key.advancements:key.keyboard.l:NONE
key.hotbar.1:key.keyboard.1:NONE
key.hotbar.2:key.keyboard.2:NONE
key.hotbar.3:key.keyboard.3:NONE
key.hotbar.4:key.keyboard.4:NONE
key.hotbar.5:key.keyboard.5:NONE
key.hotbar.6:key.keyboard.6:NONE
key.hotbar.7:key.keyboard.7:NONE
key.hotbar.8:key.keyboard.8:NONE
key.hotbar.9:key.keyboard.9:NONE
keybind.advancedperipherals.description:key.keyboard.left.control:NONE
simplemagnets.keys.toggle:key.keyboard.h:NONE
gui.xaero_open_map:key.keyboard.m:NONE
gui.xaero_open_settings:key.keyboard.right.bracket:NONE
gui.xaero_map_zoom_in:key.keyboard.unknown:NONE
gui.xaero_map_zoom_out:key.keyboard.unknown:NONE
gui.xaero_quick_confirm:key.keyboard.right.shift:NONE
placebo.toggleTrails:key.keyboard.keypad.9:NONE
placebo.toggleWings:key.keyboard.keypad.8:NONE
key.modernfix.config:key.keyboard.unknown:NONE
key.shrink.shrink:key.keyboard.g:NONE
keybind.sophisticatedbackpacks.open_backpack:key.keyboard.b:NONE
keybind.sophisticatedbackpacks.inventory_interaction:key.keyboard.c:NONE
keybind.sophisticatedbackpacks.tool_swap:key.keyboard.unknown:NONE
keybind.sophisticatedbackpacks.sort:key.mouse.middle:NONE
keybind.sophisticatedbackpacks.toggle_upgrade_1:key.keyboard.z:ALT
keybind.sophisticatedbackpacks.toggle_upgrade_2:key.keyboard.x:ALT
keybind.sophisticatedbackpacks.toggle_upgrade_3:key.keyboard.unknown:NONE
keybind.sophisticatedbackpacks.toggle_upgrade_4:key.keyboard.unknown:NONE
keybind.sophisticatedbackpacks.toggle_upgrade_5:key.keyboard.unknown:NONE
supplementaries.keybind.quiver:key.keyboard.unknown:NONE
key.curios.open.desc:key.keyboard.g:NONE
iris.keybind.reload:key.keyboard.r:NONE
iris.keybind.toggleShaders:key.keyboard.k:NONE
iris.keybind.shaderPackSelection:key.keyboard.o:NONE
tombstone.message.knowledge_of_death:key.keyboard.unknown:NONE
tombstone.message.config:key.keyboard.unknown:NONE
tombstone.message.compendium:key.keyboard.unknown:NONE
key.ftbultimine:key.keyboard.grave.accent:NONE
key.ftbteams.open_gui:key.keyboard.unknown:NONE
key.ftbchunks.map:key.keyboard.m:NONE
key.ftbchunks.claim_manager:key.keyboard.unknown:NONE
key.ftbchunks.minimap.zoomIn:key.keyboard.equal:NONE
key.ftbchunks.minimap.zoomOut:key.keyboard.minus:NONE
key.ftbchunks.add_waypoint:key.keyboard.unknown:NONE
key.ftbchunks.waypoint_manager:key.keyboard.unknown:NONE
key.moreoverlays.lightoverlay.desc:key.keyboard.f7:NONE
key.moreoverlays.chunkbounds.desc:key.keyboard.f9:NONE
key.sfm.more_info:key.keyboard.left.shift:NONE
crafting_on_a_stick.key.open_curios:key.keyboard.unknown:NONE
key.trashslot.toggle:key.keyboard.t:NONE
key.trashslot.delete:key.keyboard.delete:NONE
key.trashslot.deleteAll:key.keyboard.delete:SHIFT
keybind.sophisticatedstorage.sort:key.mouse.middle:NONE
key.travelersbackpack.inventory:key.keyboard.b:NONE
key.travelersbackpack.toggle_tank:key.keyboard.n:NONE
key.travelersbackpack.cycle_tool:key.keyboard.z:NONE
key.openManual:key.keyboard.f1:NONE
key.nextDestination:key.keyboard.right.bracket:NONE
key.prevDestination:key.keyboard.left.bracket:NONE
key.craftingtweaks.rotate:key.keyboard.unknown:NONE
key.craftingtweaks.rotate_counter_clockwise:key.keyboard.unknown:NONE
key.craftingtweaks.balance:key.keyboard.unknown:NONE
key.craftingtweaks.spread:key.keyboard.unknown:NONE
key.craftingtweaks.clear:key.keyboard.unknown:NONE
key.craftingtweaks.force_clear:key.keyboard.unknown:NONE
key.craftingtweaks.compressOne:key.keyboard.k:CONTROL
key.craftingtweaks.compressStack:key.keyboard.k:NONE
key.craftingtweaks.compressAll:key.keyboard.k:SHIFT
key.craftingtweaks.decompressOne:key.keyboard.unknown:NONE
key.craftingtweaks.decompressStack:key.keyboard.unknown:NONE
key.craftingtweaks.decompressAll:key.keyboard.unknown:NONE
key.craftingtweaks.refill_last:key.keyboard.tab:CONTROL
key.craftingtweaks.refill_last_stack:key.keyboard.tab:NONE
key.craftingtweaks.transfer_stack:key.keyboard.unknown:NONE
key.toolbelt.open:key.keyboard.r:NONE
key.toolbelt.cycle.left:key.keyboard.unknown:NONE
key.toolbelt.cycle.right:key.keyboard.unknown:NONE
key.toolbelt.slot:key.keyboard.unknown:NONE
key.jade.config:key.keyboard.keypad.0:NONE
key.jade.show_overlay:key.keyboard.keypad.1:NONE
key.jade.toggle_liquid:key.keyboard.keypad.2:NONE
key.jade.show_recipes:key.keyboard.keypad.3:NONE
key.jade.show_uses:key.keyboard.keypad.4:NONE
key.jade.narrate:key.keyboard.keypad.5:NONE
key.jade.show_details:key.keyboard.left.shift:NONE
key.exchangers.open_gui:key.keyboard.comma:NONE
key.exchangers.range_switch:key.keyboard.unknown:NONE
key.exchangers.mode_switch:key.keyboard.unknown:NONE
key.exchangers.force_drop_items_mode_toggle:key.keyboard.unknown:NONE
key.exchangers.directional_placement_mode_toggle:key.keyboard.unknown:NONE
key.exchangers.fuzzy_placement_mode_toggle:key.keyboard.unknown:NONE
key.exchangers.void_items_mode_toggle:key.keyboard.unknown:NONE
key.unmountVehicle:key.keyboard.backslash:NONE
gui.xaero_switch_waypoint_set:key.keyboard.unknown:NONE
gui.xaero_instant_waypoint:key.keyboard.keypad.add:NONE
gui.xaero_toggle_slime:key.keyboard.unknown:NONE
gui.xaero_toggle_grid:key.keyboard.unknown:NONE
gui.xaero_toggle_waypoints:key.keyboard.unknown:NONE
gui.xaero_toggle_map_waypoints:key.keyboard.unknown:NONE
gui.xaero_toggle_map:key.keyboard.unknown:NONE
gui.xaero_enlarge_map:key.keyboard.z:NONE
gui.xaero_waypoints_key:key.keyboard.u:NONE
gui.xaero_zoom_in:key.keyboard.unknown:NONE
gui.xaero_zoom_out:key.keyboard.unknown:NONE
gui.xaero_new_waypoint:key.keyboard.b:NONE
gui.xaero_display_all_sets:key.keyboard.unknown:NONE
gui.xaero_toggle_light_overlay:key.keyboard.unknown:NONE
gui.xaero_toggle_entity_radar:key.keyboard.unknown:NONE
gui.xaero_reverse_entity_radar:key.keyboard.unknown:NONE
gui.xaero_toggle_manual_cave_mode:key.keyboard.unknown:NONE
gui.xaero_alternative_list_players:key.keyboard.unknown:NONE
gui.xaero_toggle_pac_players:key.keyboard.unknown:NONE
gui.xaero_toggle_pac_chunk_claims:key.keyboard.unknown:NONE
gui.xaero_minimap_settings:key.keyboard.y:NONE
key.jei.toggleCheatModeConfigButton:key.mouse.left:CONTROL
key.jei.showRecipe2:key.mouse.left:NONE
key.jei.toggleOverlay:key.keyboard.o:CONTROL
key.jei.toggleCheatMode:key.keyboard.unknown:NONE
key.jei.cheatItemStack2:key.mouse.middle:NONE
key.jei.nextPage:key.keyboard.unknown:NONE
key.jei.showUses:key.keyboard.u:NONE
key.jei.closeRecipeGui:key.keyboard.escape:NONE
key.jei.recipeBack:key.keyboard.backspace:NONE
key.jei.showUses2:key.mouse.right:NONE
key.jei.cheatOneItem:key.mouse.left:NONE
key.jei.nextCategory:key.keyboard.page.down:SHIFT
key.jei.previousSearch:key.keyboard.up:NONE
key.jei.toggleWildcardHideIngredient:key.mouse.right:CONTROL
key.jei.nextRecipePage:key.keyboard.page.down:NONE
key.jei.previousCategory:key.keyboard.page.up:SHIFT
key.jei.focusSearch:key.keyboard.f:CONTROL
key.jei.nextSearch:key.keyboard.down:NONE
key.jei.cheatItemStack:key.mouse.left:SHIFT
key.jei.copy.recipe.id:key.keyboard.unknown:NONE
key.jei.cheatOneItem2:key.mouse.right:NONE
key.jei.bookmark:key.keyboard.a:NONE
key.jei.toggleHideIngredient:key.mouse.left:CONTROL
key.jei.previousRecipePage:key.keyboard.page.up:NONE
key.jei.previousPage:key.keyboard.unknown:NONE
key.jei.showRecipe:key.keyboard.r:NONE
key.jei.toggleBookmarkOverlay:key.keyboard.unknown:NONE
key.jei.toggleEditMode:key.keyboard.unknown:NONE
key.jei.clearSearchBar:key.mouse.right:NONE
key.ae2.wireless_pattern_access_terminal:key.keyboard.unknown:NONE
key.ae2.ae2wtlib_restock:key.keyboard.unknown:NONE
key.ae2.ae2wtlib_magnet:key.keyboard.unknown:NONE
key.ae2.portable_item_cell:key.keyboard.unknown:NONE
key.ae2.portable_fluid_cell:key.keyboard.unknown:NONE
key.ae2.wireless_pattern_encoding_terminal:key.keyboard.unknown:NONE
key.ae2.wireless_terminal:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_wheel:key.keyboard.r:NONE
key.irons_spellbooks.spell_bar_modifier:key.keyboard.left.shift:NONE
key.irons_spellbooks.spell_quick_cast_1:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_2:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_3:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_4:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_5:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_6:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_7:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_8:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_9:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_10:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_11:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_12:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_13:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_14:key.keyboard.unknown:NONE
key.irons_spellbooks.spell_quick_cast_15:key.keyboard.unknown:NONE
key.occultism.backpack:key.keyboard.b:NONE
key.occultism.storage_remote:key.keyboard.n:NONE
key.occultism.familiar.greedy_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.otherworld_bird:key.keyboard.unknown:NONE
key.occultism.familiar.bat_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.deer_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.cthulhu_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.devil_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.dragon_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.blacksmith_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.guardian_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.headless_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.chimera_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.goat_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.shub_niggurath_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.beholder_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.fairy_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.mummy_familiar:key.keyboard.unknown:NONE
key.occultism.familiar.beaver_familiar:key.keyboard.unknown:NONE
key.open_muffler_gui:key.keyboard.unknown:NONE
cos.key.opencosarmorinventory:key.keyboard.unknown:NONE
key.entityculling.toggle:key.keyboard.unknown:NONE
