"Server" {
	# Allows you to disable the tpx command.
	B:"enable_tpx"=true

	# Uses the right click block event to verify that players have permission to interact with BC / DE blocks.
	# This ensures there is no possible way a player can interact with a BC block if a protection system is blocking the interaction
	# In theory not even a modified client sending raw packets will be able to bypass this.
	# I have added the ability to disable this feature because it seems in rare cases it blocks players who should have access and i have no idea why.
	B:"clientPermissionVerification"=true
}

"Client" {
	# Enable / Disable dark mode in my GUI's. (This can also be toggled in game from any gui that supports dark mode)
	B:"darkMode"=true
}
