# All of the values in this file are the defaults as of the time the file was generated.
# If you wish to set custom values you must set the "override" field to true then specify your custom values.
"Module Stats" {
	"draconicevolution:draconium_energy" {
		B:"override"=false

		L:"capacity"=1000000

		L:"transfer"=16000
	}

	"draconicevolution:wyvern_energy" {
		B:"override"=false

		L:"capacity"=4000000

		L:"transfer"=64000
	}

	"draconicevolution:draconic_energy" {
		B:"override"=false

		L:"capacity"=16000000

		L:"transfer"=256000
	}

	"draconicevolution:chaotic_energy" {
		B:"override"=false

		L:"capacity"=64000000

		L:"transfer"=1024000
	}

	"draconicevolution:wyvern_energy_link" {
		B:"override"=false

		L:"activation"=4000000

		L:"operation"=512

		L:"transfer"=2048

		B:"dimensional"=false
	}

	"draconicevolution:draconic_energy_link" {
		B:"override"=false

		L:"activation"=16000000

		L:"operation"=2048

		L:"transfer"=16000

		B:"dimensional"=true
	}

	"draconicevolution:chaotic_energy_link" {
		B:"override"=false

		L:"activation"=64000000

		L:"operation"=8192

		L:"transfer"=128000

		B:"dimensional"=true
	}

	"draconicevolution:draconium_speed" {
		B:"override"=false

		D:"speed_boost"=0.1
	}

	"draconicevolution:wyvern_speed" {
		B:"override"=false

		D:"speed_boost"=0.25
	}

	"draconicevolution:draconic_speed" {
		B:"override"=false

		D:"speed_boost"=0.5
	}

	"draconicevolution:chaotic_speed" {
		B:"override"=false

		D:"speed_boost"=1.5
	}

	"draconicevolution:draconium_damage" {
		B:"override"=false

		D:"damage_boost"=2.0
	}

	"draconicevolution:wyvern_damage" {
		B:"override"=false

		D:"damage_boost"=4.0
	}

	"draconicevolution:draconic_damage" {
		B:"override"=false

		D:"damage_boost"=8.0
	}

	"draconicevolution:chaotic_damage" {
		B:"override"=false

		D:"damage_boost"=16.0
	}

	"draconicevolution:draconium_aoe" {
		B:"override"=false

		I:"aoe"=1
	}

	"draconicevolution:wyvern_aoe" {
		B:"override"=false

		I:"aoe"=2
	}

	"draconicevolution:draconic_aoe" {
		B:"override"=false

		I:"aoe"=3
	}

	"draconicevolution:chaotic_aoe" {
		B:"override"=false

		I:"aoe"=5
	}

	"draconicevolution:wyvern_tree_harvest" {
		B:"override"=false

		I:"range"=16

		I:"speed"=5
	}

	"draconicevolution:draconic_tree_harvest" {
		B:"override"=false

		I:"range"=48

		I:"speed"=15
	}

	"draconicevolution:chaotic_tree_harvest" {
		B:"override"=false

		I:"range"=144

		I:"speed"=45
	}

	"draconicevolution:wyvern_proj_velocity" {
		B:"override"=false

		D:"velocity_modifier"=0.15000000596046448

		D:"accuracy_modifier"=-0.01875000074505806

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.0

		D:"damage_modifier"=0.0
	}

	"draconicevolution:draconic_proj_velocity" {
		B:"override"=false

		D:"velocity_modifier"=0.3499999940395355

		D:"accuracy_modifier"=-0.02187499962747097

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.04374999925494194

		D:"damage_modifier"=0.0
	}

	"draconicevolution:chaotic_proj_velocity" {
		B:"override"=false

		D:"velocity_modifier"=0.75

		D:"accuracy_modifier"=-0.0

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.1875

		D:"damage_modifier"=0.0
	}

	"draconicevolution:wyvern_proj_accuracy" {
		B:"override"=false

		D:"velocity_modifier"=-0.0625

		D:"accuracy_modifier"=0.125

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=-0.015625

		D:"damage_modifier"=0.0
	}

	"draconicevolution:draconic_proj_accuracy" {
		B:"override"=false

		D:"velocity_modifier"=-0.05000000074505806

		D:"accuracy_modifier"=0.20000000298023224

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=-0.012500000186264515

		D:"damage_modifier"=0.0
	}

	"draconicevolution:chaotic_proj_accuracy" {
		B:"override"=false

		D:"velocity_modifier"=-0.0

		D:"accuracy_modifier"=0.25

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=-0.0

		D:"damage_modifier"=0.0
	}

	"draconicevolution:wyvern_proj_grav_comp" {
		B:"override"=false

		D:"velocity_modifier"=-0.02500000037252903

		D:"accuracy_modifier"=-0.05000000074505806

		D:"anti_grav_modifier"=0.20000000298023224

		D:"penetration_modifier"=-0.0062500000931322575

		D:"damage_modifier"=-0.06000000238418579
	}

	"draconicevolution:draconic_proj_grav_comp" {
		B:"override"=false

		D:"velocity_modifier"=-0.015625

		D:"accuracy_modifier"=-0.03125

		D:"anti_grav_modifier"=0.25

		D:"penetration_modifier"=-0.00390625

		D:"damage_modifier"=-0.03750000149011612
	}

	"draconicevolution:chaotic_proj_grav_comp" {
		B:"override"=false

		D:"velocity_modifier"=-0.0

		D:"accuracy_modifier"=-0.0

		D:"anti_grav_modifier"=0.5

		D:"penetration_modifier"=-0.0

		D:"damage_modifier"=-0.0
	}

	"draconicevolution:wyvern_proj_penetration" {
		B:"override"=false

		D:"velocity_modifier"=0.0

		D:"accuracy_modifier"=-0.125

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.25

		D:"damage_modifier"=0.0
	}

	"draconicevolution:draconic_proj_penetration" {
		B:"override"=false

		D:"velocity_modifier"=0.0

		D:"accuracy_modifier"=-0.125

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.5

		D:"damage_modifier"=0.0
	}

	"draconicevolution:chaotic_proj_penetration" {
		B:"override"=false

		D:"velocity_modifier"=0.0

		D:"accuracy_modifier"=-0.0

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.75

		D:"damage_modifier"=0.0
	}

	"draconicevolution:wyvern_proj_damage" {
		B:"override"=false

		D:"velocity_modifier"=0.0

		D:"accuracy_modifier"=-0.0625

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.0

		D:"damage_modifier"=0.25
	}

	"draconicevolution:draconic_proj_damage" {
		B:"override"=false

		D:"velocity_modifier"=0.0

		D:"accuracy_modifier"=-0.0625

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.0

		D:"damage_modifier"=0.5
	}

	"draconicevolution:chaotic_proj_damage" {
		B:"override"=false

		D:"velocity_modifier"=0.0

		D:"accuracy_modifier"=-0.0

		D:"anti_grav_modifier"=0.0

		D:"penetration_modifier"=0.0

		D:"damage_modifier"=0.75
	}

	"draconicevolution:wyvern_shield_control" {
		B:"override"=false

		I:"cool_down_ticks"=400
	}

	"draconicevolution:draconic_shield_control" {
		B:"override"=false

		I:"cool_down_ticks"=200
	}

	"draconicevolution:chaotic_shield_control" {
		B:"override"=false

		I:"cool_down_ticks"=100
	}

	"draconicevolution:wyvern_shield_capacity" {
		B:"override"=false

		I:"capacity"=25

		D:"recharge"=0.005
	}

	"draconicevolution:draconic_shield_capacity" {
		B:"override"=false

		I:"capacity"=50

		D:"recharge"=0.0125
	}

	"draconicevolution:chaotic_shield_capacity" {
		B:"override"=false

		I:"capacity"=100

		D:"recharge"=0.025
	}

	"draconicevolution:wyvern_large_shield_capacity" {
		B:"override"=false

		I:"capacity"=125

		D:"recharge"=0.0
	}

	"draconicevolution:draconic_large_shield_capacity" {
		B:"override"=false

		I:"capacity"=250

		D:"recharge"=0.0
	}

	"draconicevolution:chaotic_large_shield_capacity" {
		B:"override"=false

		I:"capacity"=500

		D:"recharge"=0.0
	}

	"draconicevolution:wyvern_shield_recovery" {
		B:"override"=false

		I:"capacity"=5

		D:"recharge"=0.05
	}

	"draconicevolution:draconic_shield_recovery" {
		B:"override"=false

		I:"capacity"=10

		D:"recharge"=0.125
	}

	"draconicevolution:chaotic_shield_recovery" {
		B:"override"=false

		I:"capacity"=20

		D:"recharge"=0.25
	}

	"draconicevolution:wyvern_flight" {
		B:"override"=false

		D:"elytra_boost_speed"=1.0
	}

	"draconicevolution:draconic_flight" {
		B:"override"=false

		D:"elytra_boost_speed"=2.0
	}

	"draconicevolution:chaotic_flight" {
		B:"override"=false

		D:"elytra_boost_speed"=3.5
	}

	"draconicevolution:wyvern_undying" {
		B:"override"=false

		D:"health_boost"=6.0

		D:"shield_boost"=25.0

		I:"shield_boost_time"=300

		I:"charge_ticks"=2400

		L:"charge_energy"=5000000

		D:"invulnerable_time"=2.0
	}

	"draconicevolution:draconic_undying" {
		B:"override"=false

		D:"health_boost"=12.0

		D:"shield_boost"=50.0

		I:"shield_boost_time"=600

		I:"charge_ticks"=1200

		L:"charge_energy"=10000000

		D:"invulnerable_time"=3.0
	}

	"draconicevolution:chaotic_undying" {
		B:"override"=false

		D:"health_boost"=20.0

		D:"shield_boost"=100.0

		I:"shield_boost_time"=2400

		I:"charge_ticks"=900

		L:"charge_energy"=20000000

		D:"invulnerable_time"=3.0
	}

	"draconicevolution:draconium_auto_feed" {
		B:"override"=false

		D:"food_storage"=40.0
	}

	"draconicevolution:wyvern_auto_feed" {
		B:"override"=false

		D:"food_storage"=150.0
	}

	"draconicevolution:draconic_auto_feed" {
		B:"override"=false

		D:"food_storage"=400.0
	}

	"draconicevolution:draconium_jump" {
		B:"override"=false

		D:"jump_boost"=0.25
	}

	"draconicevolution:wyvern_jump" {
		B:"override"=false

		D:"jump_boost"=0.75
	}

	"draconicevolution:draconic_jump" {
		B:"override"=false

		D:"jump_boost"=1.25
	}

	"draconicevolution:chaotic_jump" {
		B:"override"=false

		D:"jump_boost"=4.0
	}
}
