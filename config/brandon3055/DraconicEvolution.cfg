"Server" {
	# This is a randomly generated id that clients will use to map their tool config settings to this server.
	S:"serverID"="d895d83a-9074-4df3-b759-c3dd749f105c"

	# This can be used to limit the maximum speed boost allowed by the modular armor.
	# A value of for example 1 means a maximum boost of +100% over default character speed.
	# Set to -1 for no limit, Default: 16 (+1600%)
	D:"armorSpeedLimit"=16.0

	# Allows you to disable elytra flight supplied by DE's armor
	B:"enableElytraFlight"=true

	# Allows you to disable creative flight supplied by DE's armor
	B:"enableCreativeFlight"=true

	# Sets the maximum blink range for the Advanced Dislocator
	I:"dislocatorBlinkRange"=32

	# Sets the blinks to regular fuel ratio. Default 1 regular fuel (1 pearl) allows 4 blinks.
	I:"dislocatorBlinksPerPearl"=4

	# Sets how far fusion crafting injectors can be from the fusion crafting core
	I:"fusionInjectorRange"=16

	# Sets the minimum distance a fusion injector must be from the fusion crafting core.
	I:"fusionInjectorMinDist"=2

	# Time in ticks required for charging phase of fusion crafting with each injector tier. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Chaotic
	I:"fusionChargeTime" <
		300
		220
		140
		60
	>

	# Time in ticks required for crafting phase of fusion crafting with each injector tier. Draconium, Wyvern, Draconic, Chaotic
	# The time selected is based on the lowest tier injector used in the craft.
	# Max value for any of these is 32,767 (27.3 minutes)
	I:"fusionCraftTime" <
		300
		220
		140
		60
	>

	# This is a list of entities that the "Projectile Immunity Cancellation" module will work on. Add additional entities as required. (Let me know if i missed any)
	S:"projectileAntiImmuneEntities" <
		minecraft:enderman
		minecraft:wither
		minecraft:ender_dragon
		draconicevolution:guardian_wither
		minecraft:shulker
	>

	# Config values related to the chaos guardian fight.
	# The default values of -99 are markers indicating the internal hard coded value should be used.
	# This allows these values to be updated between mod versions for balance adjustments. Setting them to anything other than -99 will override the internal values.
	"Guardian Fight" {
		# Sets the base shield strength for chaos guardian crystals.
		# Default: 512
		I:"guardianCrystalShield"=-99

		# Sets how long the guarian crystal's shield will be unstable after receiving damage from the chaos guardian
		# Default: 200 (10 seconds)
		I:"guardianCrystalUnstableWindow"=-99

		# Sets the guardians base health value (After you break through the guardians shield)
		# Default: 1000
		I:"guardianHealth"=-99

		# Sets the guardians shield capacity (You will need to break through this after disabling the guardian crystals)
		# Keep in mind there is no limit to how fast you can hit the guardians shield so this will melt with a high damage rapid fire bow.
		# Default: 16000
		I:"guardianShield"=-99

		# Allows chaotic weapons to destabilize the guardian crystal shields.
		# This makes it much easier to farm the guardian but only after you have chaos tier weapons.
		B:"chaoticBypassCrystalShield"=true

		# Number of chaos shards dropped by the chaos crystal when broken by a player
		I:"chaosDropCount"=5
	}

	# These are all config fields related to the Stabilized Spawner and mob souls
	"Stabilized Spawner" {
		# Mobs have a 1 in {this number} chance to drop a soul when killed with the Reaper enchantment.  Note: This is the base value; higher enchantment levels increase this chance.
		I:"soulDropChance"=1000

		# Passive (Animals) Mobs have a 1 in {this number} chance to drop a soul when killed with the Reaper enchantment.  Note: This is the base value; higher enchantment levels increase this chance.
		I:"passiveSoulDropChance"=800

		# By default, any entities added to this list will not drop their souls and will not be spawnable by the Stabilized Spawner. Use entity registry name. e.g. minecraft:cow
		S:"spawnerList" <
		>

		# Changes the spawner list to a whitelist instead of a blacklist.
		B:"spawnerListWhiteList"=false

		# Enabling this allows boss souls to drop. Use with caution!
		B:"allowBossSouls"=false

		# Sets the min and max spawn delay in ticks for each spawner tier. Order is as follows.\nBasic MIN, MAX, Wyvern MIN, MAX, Draconic MIN, MAX, Chaotic MIN MAX
		I:"spawnerDelays" <
			200
			800
			100
			400
			50
			200
			25
			100
		>
	}

	# Sets the maximum fuel that can be added to an Advanced Dislocator.
	I:"dislocatorMaxFuel"=1024

	# Sets blink cooldown in ticks
	I:"dislocatorBlinkCooldown"=5

	# Sets maximum area (in blocks) for a DE portal, The default value 65536 is equivalent to a 256x256 portal
	I:"portalMaxArea"=65536

	# This is more for sanity than actually limiting portal size. Sets the max distance a portal block can be from the receptacle
	I:"portalMaxDistance"=256

	# These are all (server side) config fields related to the reactor
	"Reactor" {
		# Adjusts the energy output multiplier of the reactor.
		D:"reactorOutputMultiplier"=1.0

		# Adjusts the fuel usage multiplier of the reactor.
		D:"reactorFuelUsageMultiplier"=1.0

		# Allows you to adjust the overall scale of the reactor explosion. Use \"disableLargeReactorBoom\" to disable explosion completely.
		D:"reactorExplosionScale"=1.0

		# If true, this will disable the massive reactor explosion and replace it with a much smaller one.
		B:"disableLargeReactorBoom"=false
	}

	# This can be used to adjust the amount of Draconium Dust the Ender Dragon drops when killed.
	# The amount dropped will be this number +/- 10%
	I:"dragonDustLootModifier"=64

	# By default, the dragon egg only ever spawns once. This forces it to spawn every time the dragon is killed.
	B:"dragonEggSpawnOverride"=true

	# This is a blacklist of key words that can be used to prevent certain storage items from being stored in a draconium chest.
	# If the items registry name contains any or these strings it will not be allowed
	S:"chestBlacklist" <
		draconium_chest
		shulker_box
		pouch
		bag
		strongbox
	>

	# The new tag based tool tier system makes it incredibly difficult to add over powered tools that can mine blocks of any harvest level. So for Draconic and Chaotic tier i simply dont use it.
	# This means they can mine pretty much any minable block. Setting this to true will enable the tag system on these tiers and by default put them right along side wyvern at netherite tier.
	# This may be useful for people like pack developers who want to add custom tool tier progression.
	B:"useToolTierTags"=false

	# Allows you to adjust the capacity of each energy core tier.
	# Warning changing the number entries in this list will crash your game.
	# For tier 8 -1 = BigInteger.MAX_VALUE * Long.MAX_VALUE, Otherwise the max you can specify is 9223372036854775807
	L:"coreCapacity" <
		45500000
		273000000
		**********
		**********
		59300000000
		356000000000
		2140000000000
		-1
	>

	# Mob Grinder energy required per entity health point
	I:"grinderEnergyPerHeart"=80

	# Mob Grinder entity blacklist.
	S:"grinderBlackList" <
		evilcraft:vengeance_spirit
		minecraft:armor_stand
	>

	# Allow mob grinder to grind players
	B:"allowGrindingPlayers"=false
}

"Chaos Island" {
	# Allows you to disable generation of chaos islands
	B:"enableGeneration"=true

	# Allows you to disable generation of end comets
	B:"enderCometEnabled"=true

	# Toggles whether the full chaos island should spawn or just the guardian crystals, the chaos crystal, and the guardian.
	B:"chaosIslandVoidMode"=false

	# The island will generate so that the chaos crystal is at this y position
	I:"chaosIslandYPosition"=80

	# Allows you to adjust the chaos island spawn grid size.
	I:"chaosIslandSeparation"=10000

	# Rough radius of the main chaos island. If you prefer the smaller island from pre 1.18 set this to 80
	I:"chaosIslandSize"=160
}

# These are client side config properties.
"Client" {
	# Set this to false to disable the fancy 3D tool models. (Requires restart)
	B:"fancyToolModels"=true

	# Set this to false to disable chaos guardian shaders. (May visually break some stuff but could be useful if you are experiencing gl crashes.)
	B:"guardianShaders"=true

	# These settings is accessible in game via the "Configure Equipment" gui.
	"itemConfigGUI" {
		# Setting this to false will prevent properties from being displayed if their associated item is not in your inventory.
		B:"showUnavailable"=true

		# Setting this to false will disable property window snapping.
		B:"enableSnapping"=true

		# Setting this to false will disable the highlight/animation that occurs over a properties associated item when hovering over or editing a property.
		B:"enableVisualization"=true

		# Setting this to false will hide the "Add Group" button.
		B:"enableAddGroupButton"=true

		# Setting this to false will hide the "Delete Zone"
		B:"enableDeleteZone"=true

		# If enabled your configured properties, property groups and presets will still be accessible when in the simple configuration mode.
		B:"enableAdvancedXOver"=false
	}

	# Enable / Disable item dislocator pickup sound
	B:"itemDislocatorSound"=true
}

# These settings allow you to override the base stats for DE's equipment.
# Please note the generated default values "-99" is actually a marker that tells DE to use the actual internal default value.
# This value is listed in each properties description but may not be valid if this config was generated by a previous version of DE.
"Equipment" {
	# Internal Default Value: 10.0
	D:"draconiumHarvestSpeed"=-99.0

	# Internal Default Value: 15.0
	D:"wyvernHarvestSpeed"=-99.0

	# Internal Default Value: 25.0
	D:"draconicHarvestSpeed"=-99.0

	# Internal Default Value: 50.0
	D:"chaoticHarvestSpeed"=-99.0

	# Base Attack Damage
	# Internal Default Value: 1.125
	D:"draconiumDamage"=-99.0

	# Base Attack Damage
	# Internal Default Value: 1.25
	D:"wyvernDamage"=-99.0

	# Base Attack Damage
	# Internal Default Value: 1.75
	D:"draconicDamage"=-99.0

	# Base Attack Damage
	# Internal Default Value: 2.5
	D:"chaoticDamage"=-99.0

	# Base Attack Speed (How many times you can attack at full power per second)
	# Internal Default Value: 1.125
	D:"draconiumSwingSpeed"=-99.0

	# Base Attack Speed (How many times you can attack at full power per second)
	# Internal Default Value: 1.25
	D:"wyvernSwingSpeed"=-99.0

	# Base Attack Speed (How many times you can attack at full power per second)
	# Internal Default Value: 1.5
	D:"draconicSwingSpeed"=-99.0

	# Base Attack Speed (How many times you can attack at full power per second)
	# Internal Default Value: 2.0
	D:"chaoticSwingSpeed"=-99.0

	# Internal Default Value: 12
	I:"draconiumEnchantability"=-99

	# Internal Default Value: 15
	I:"wyvernEnchantability"=-99

	# Internal Default Value: 25
	I:"draconicEnchantability"=-99

	# Internal Default Value: 35
	I:"chaoticEnchantability"=-99

	# This is a multiplier that is applied to the base attack damage
	# Internal Default Value: 9.0
	D:"staffDamageMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack damage
	# Internal Default Value: 7.0
	D:"swordDamageMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack damage
	# Internal Default Value: 9.0
	D:"axeDamageMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack damage
	# Internal Default Value: 5.0
	D:"pickaxeDamageMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack damage
	# Internal Default Value: 5.5
	D:"shovelDamageMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack damage
	# Internal Default Value: 1.0
	D:"hoeDamageMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack speed
	# Internal Default Value: 0.5
	D:"staffSwingSpeedMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack speed
	# Internal Default Value: 1.6
	D:"swordSwingSpeedMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack speed
	# Internal Default Value: 1.0
	D:"axeSwingSpeedMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack speed
	# Internal Default Value: 1.2
	D:"pickaxeSwingSpeedMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack speed
	# Internal Default Value: 1.0
	D:"shovelSwingSpeedMultiplier"=-99.0

	# This is a multiplier that is applied to the base attack speed
	# Internal Default Value: 4.0
	D:"hoeSwingSpeedMultiplier"=-99.0

	# This is an efficiency multiplier specifically for the staff of power.
	# The staff gets its own multiplier because its a "special tool"
	# Internal Default Value: 3.0
	D:"staffEfficiencyMultiplier"=-99.0

	# This is the base energy value for each tier before the type multiplier is applied.
	# Internal Default Value: 0
	L:"draconiumBaseEnergy"=-99

	# This is the base energy value for each tier before the type multiplier is applied.
	# Internal Default Value: 0
	L:"wyvernBaseEnergy"=-99

	# This is the base energy value for each tier before the type multiplier is applied.
	# Internal Default Value: 0
	L:"draconicBaseEnergy"=-99

	# This is the base energy value for each tier before the type multiplier is applied.
	# Internal Default Value: 0
	L:"chaoticBaseEnergy"=-99

	# This is a multiplier that is applied to the base energy value.
	# Internal Default Value: 3.0
	D:"staffEnergyMult"=-99.0

	# This is a multiplier that is applied to the base energy value.
	# Internal Default Value: 1.0
	D:"toolEnergyMult"=-99.0

	# This is a multiplier that is applied to the base energy value.
	# Internal Default Value: 8.0
	D:"capacitorEnergyMult"=-99.0

	# This is a multiplier that is applied to the base energy value.
	# Internal Default Value: 2.0
	D:"chestpieceEnergyMult"=-99.0

	# This is the per block energy requirement of all mining tools.
	# Internal Default Value: 256
	I:"energyHarvest"=-99

	# This is the energy requirement for weapons. This is multiplied by the weapons attack damage.
	# Internal Default Value: 1024
	I:"energyAttack"=-99

	# Shield recharge base energy per shield point.
	# Internal Default Value: 8192
	I:"energyShieldChg"=-99

	# This controls the shield's passive power usage. The formula is: passiveDraw = (shieldPoints^2 * shieldPassiveModifier) OP/t
	# Internal Default Value: 5.0E-4
	D:"shieldPassiveModifier"=-99.0

	# Elytra flight energy use per tick.
	# Internal Default Value: 1024
	I:"elytraFlightEnergy"=-99

	# Creative flight energy use per tick.
	# Internal Default Value: 4096
	I:"creativeFlightEnergy"=-99

	# Elytra boost energy per tick, Internal Default Value: 1024
	I:"elytraWyvernEnergy"=-99

	# Elytra boost energy per tick, Internal Default Value: 2048
	I:"elytraDraconicEnergy"=-99

	# Elytra boost energy per tick, Internal Default Value: 8192
	I:"elytraChaoticEnergy"=-99

	# Bow base energy per calculated damage point, per shot, Internal Default Value: 1024
	I:"bowBaseEnergy"=-99

	# Energy required for the ender storage module to transfer one (single) item into storage, Internal Default Value: 32
	I:"enderModulePerItemEnergy"=-99

	# Night vision module energy consumption while operation. (OP per tick), Internal Default Value: 20
	I:"nightVisionEnergy"=-99
}

# These settings allow you to override the base module grid sizes for DE's modular items.
# The format is:
# 	I:"configTag" <
# 		width
# 		height
# 	>
# Leave blank to use the internal default value.
# This value is listed in each properties description but may not be valid if this config was generated by a previous version of DE.
# Please note reducing grid size will not remove modules from existing items but will instead leave modules in an invalid state where they are
# still fully functional but installed outside of the grid bounds.
"Module Grids" {
	# (read category description)
	# Setting this to true will cause invalid modules to be deleted from the module grid.
	B:"removeInvalidModules"=false

	# Internal Default Value: 4 x 4
	I:"wyvernTools" <
	>

	# Internal Default Value: 6 x 5
	I:"draconicTools" <
	>

	# Internal Default Value: 8 x 6
	I:"chaoticTools" <
	>

	# Internal Default Value: 8 x 6
	I:"draconicStaff" <
	>

	# Internal Default Value: 10 x 8
	I:"chaoticStaff" <
	>

	# Internal Default Value: 6 x 5
	I:"wyvernChestpiece" <
	>

	# Internal Default Value: 8 x 6
	I:"draconicChestpiece" <
	>

	# Internal Default Value: 10 x 8
	I:"chaoticChestpiece" <
	>

	# Internal Default Value: 4 x 4
	I:"wyvernCapacitor" <
	>

	# Internal Default Value: 5 x 5
	I:"draconicCapacitor" <
	>

	# Internal Default Value: 8 x 6
	I:"chaoticCapacitor" <
	>
}
