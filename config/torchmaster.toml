
[General]
	#Show additional information in the tooltip of certain items and blocks
	beginnerTooltips = true
	#By default, mega torches only block natural spawns (i.e. from low light levels). Setting this to false will also block spawns from spawners
	blockOnlyNaturalSpawns = true
	#If this setting is enabled, the mega torch will block all natural spawn attempts from Lycanites Mobs in its radius
	lycanitesMobsBlockAll = true
	#If this setting is enabled, the mega torch will block village sieges from zombies
	blockVillageSieges = true
	#The radius of the mega torch in each direction (cube) with the torch at its center
	#Range: > 0
	megaTorchRadius = 64
	#The radius of the dread lamp in each direction (cube) with the torch at its center
	#Range: > 0
	dreadLampRadius = 64
	#Use this setting to override the internal lists for entity blocking
	#You can use this to block more entities or even allow certain entities to still spawn
	#The + prefix will add the entity to the list, effectivly denying its spawns
	#The - prefix will remove the entity from the list (if necessary), effectivly allowing its spawns
	#Note: Each entry needs to be put in quotes! Multiple Entries should be separated by comma.
	#Block zombies: "+minecraft:zombie"
	#Allow creepers: "-minecraft:creeper"
	megaTorchEntityBlockListOverrides = []
	#Same as the mega torch block list override, just for the dread lamp
	#Block squid: +minecraft:squid
	#Allow pigs: -minecraft:pig
	dreadLampEntityBlockListOverrides = []
	#The radius in which the feral flare should try to place lights
	#Range: 1 ~ 127
	feralFlareRadius = 16
	#Controls how often the flare should try to place lights. 1 means every tick, 10 every 10th tick, etc
	#Range: > 1
	feralFlareTickRate = 5
	#The target minimum light level to place lights for
	#Range: 0 ~ 15
	feralFlareMinLightLevel = 10
	#The maximum amount of invisble lights a feral flare lantern can place. Set to 0 to disable light placement.
	#Warning: Setting this value too high in conjunction with the feralFlareMinLightLevel and Radius can lead to world corruption!
	#(Badly compressed packet error)
	#Range: 0 ~ 32767
	feralFlareLanternLightCountHardcap = 255
	#Durability of the frozen pearl. Each removed light will remove one charge from the pearl. Set to 0 to disable durability
	#Range: 0 ~ 32767
	frozenPearlDurability = 1024
	#Print entity spawn checks to the debug log
	logSpawnChecks = false
	#Configures the spawn check to be more aggressive, effectivly overriding the CheckSpawn results of other mods
	aggressiveSpawnChecks = false

