
#General settings of Crash Assistant mod.
[general]
	#Link which will be opened in browser on request_help_button pressed.
	#If equals CHANGE_ME, will open Forge/NeoForge/Fabric/Quilt discord link. Names of communities/channels also will be used not from config, but according to this link.
	#Must start with 'https://' or 'www.'
	help_link = "https://discord.gg/allthemods"
	#Anyways log will be uploaded to mclo.gs, but with this option you can wrap link to gnomebot.dev for better formatting.
	#If help_link equals 'CHANGE_ME', this value will be ignored and gnomebot.dev used.
	#Supported values: mclo.gs / gnomebot.dev
	upload_to = "gnomebot.dev"
	#Show gui on minecraft crashed on modloading and FML error screen displayed.
	show_on_fml_error_screen = true
	#Close old CrashAssistantApp if it's still running when starting a new instance of Minecraft, to avoid confusing player with window from old crash.
	kill_old_app = true
	#If options.txt doesn't exist, the default language will be used.
	default_lang = "en_us"
	#Append comment text with notice about sending screenshot of this gui tells nothing to modpack creators.
	show_dont_send_screenshot_of_gui_notice = true
	#List of blacklisted log files. This files won't show in GUI logs list.
	blacklisted_logs = ["CrashAssistant: latest.log"]

#Here you can change text of lang placeHolders.
#Also you can change any text in lang files.
#You don't need to modify jar. You can change it in config/crash_assistant/lang. For more info read README.md file located where.
[text]
	#$CONFIG.text.support_name$ in lang files will be replaced with this value.
	#For example this placeHolder used in: "Request help in the $CONFIG.text.support_name$"
	support_name = "ATM9 Support Channel"
	#$CONFIG.text.support_place$ in lang files will be replaced with this value.
	support_place = "#atm9-techsupport channel"
	#$CONFIG.text.modpack_name$ in lang files will be replaced with this value.
	#For example this placeHolder used in: "Oops, $CONFIG.text.modpack_name$ crashed!"
	#Supports Better Compatibility Checker integration. You can use $BCC.modpackName$, $BCC.modpackVersion$, etc and it will be replaced with value from BCC config.
	modpack_name = "$BCC.modpackName$ - $BCC.modpackVersion$"

#Settings of message generated by Upload all button
[generated_message]
	#Add ### prefix before filename.
	#This can prevent too small, hard to hit on mobile links.
	h3_prefix = true
	#Replaces "\n" separator between logs to "   |   " to make message vertically smaller.
	one_line_logs = true
	#Adds line in log list about this Intel processor can be corrupted.
	intel_corrupted_notification = true
	#If the modpack is created for a non-English-speaking audience, сhange this to the language the modpack is designed for.
	#This lang will be used only for generating message by "Upload all..." button.Do not modify this value if there's a chance that the generated message will be sent to English-speaking communities.
	generated_msg_lang = "en_us"
	#This text will be under "$CONFIG.text.modpack_name$ crashed!" in generated message by Upload all button.
	#You can include:
	#   * some form, which users should fill out.
	#   * additional information like Minecraft version, etc.
	text_under_crashed = ""
	#With this option you can notify user about something related with posting generated message.
	#For example if they need to fill some option from "text_under_crashed", etc.
	#Supports html formatting, placeholders.
	#Leave empty to prevent showing this warning message.
	warning_after_upload_all_button_press = ""

#Settings of modlist feature.
#Adds in generated msg block about which mods modpack user added/removed/updated.
#Also you can see diff by running '/crash_assistant modlist diff' command.
[modpack_modlist]
	#Enable feature.
	enabled = true
	#nicknames of players, who considered as modpack creator.
	#Only this players can overwrite modlist.json
	#If this feature is enabled and this array is empty, will be appended with nickname of current player.
	modpack_creators = ["mitchell52", "whatthedrunk"]
	#If enabled, modlist.json will be overwritten on every launch(first tick of TitleScreen),
	#then game is launched by modpack creator.
	#So you won't forget to save it before publishing.
	#If you want to save manually: disable this and use '/crash_assistant modlist save' command.
	auto_update = false
	#If enabled, will add resourcepacks to modlist.json
	#After filename where will be ' (resourcepack)' suffix.
	add_resourcepacks = true

#Settings of '/crash_assistant crash' command feature.
[crash_command]
	#Enable feature.
	enabled = false
	#To ensure the user really wants to crash the game, the command needs to be run again within this amount of seconds.
	#Set to <= 0 to disable the confirmation.
	seconds = 10

#Settings of notifying about intel corrupted processors.
[intel_corrupted]
	#Enable feature.
	enabled = true
	#Show funny related gif in warning message.
	show_gif = true

[greeting]
	#You don't need to touch this option.
	#On first world join of modpack creator if set to false shows greeting, then self enables.
	shown_greeting = true

