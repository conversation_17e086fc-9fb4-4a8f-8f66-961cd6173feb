{
	// The chance everlasting beef drops when a cow or mooshroom is killed by a player
	"everlastingBeefChance": 0.002,
	// The chance that a skeleton, zombie or piglin spawns with an artifact equipped
	"entityEquipmentChance": 0.001,
	// The chance that an artifact generates in suspicious sand or gravel
	"archaeologyChance": 0.0625,
	// Whether the Kitty Slippers and <PERSON> Hoppers change the player's hurt sounds
	"modifyHurtSounds": true,
	"campsite": {
		/* How many times a campsite will attempt to generate per chunk
		   Set this to 0 to prevent campsites from generating
		*/
		"count": 4,
		// The minimum height campsites can spawn at
		"minY": -60,
		// The maximum height campsites can spawn at
		"maxY": 40,
		// Probability that a campsite has a mimic instead of a chest
		"mimicChance": 0.3,
		// Whether to use wooden chests from other mods when generating campsites
		"useModdedChests": true,
		// Whether campsites can contain blocks that emit light
		"allowLightSources": true
	},
	/* Affects how common artifacts are in chests.
	   Values above 1 will make artifacts rarer, values between 0 and 1 will make artifacts more common.
	   Doubling this value will make artifacts approximately twice as hard to find, and vice versa.
	   To prevent artifacts from appearing as chest loot, set this to 10000.
	   
	   To disable or change the effects of specific items, the /gamerule command can be used.
	   A list of available game rules and their effects can be found on the wiki on GitHub:
	   https://github.com/ochotonida/artifacts/wiki
	*/
	"artifactRarity": 1.0
}