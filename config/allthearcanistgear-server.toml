
[armor_config]
	_comment = "A world restart is needed after changing these config options."

	[armor_config.allthemodium]
		_comment = "Config for Allthemodium armor"

		[armor_config.allthemodium.armor_values]
			#Hat
			#Range: > 0
			hat = 4
			#Robes
			#Range: > 0
			robes = 9
			#Leggings
			#Range: > 0
			leggings = 7
			#Boots
			#Range: > 0
			boots = 4
			#Toughness
			#Range: > 0
			toughness = 4
			#Knockback Resistance
			#Range: 0.0 ~ 2.147483647E9
			knockback_resistance = 0.0

		[armor_config.allthemodium.arcanist_stats]
			#Max Mana Bonus
			#Range: > 0
			mana_boost = 200
			#Mana Regen Bonus
			#Range: 0.0 ~ 2.147483647E9
			mana_regen = 6.0
			#Spell Power Bonus
			#Range: 0.0 ~ 2.147483647E9
			spell_power = 6.0

		[armor_config.allthemodium.capabilities]
			#Should Helmet Prevent Drowning?
			prevent_drowning = true
			#Should Helmet Prevent Kinetic Damage?
			prevent_kinetic = true
			#Should Chestplate Prevent Fire Damage?
			prevent_fire = true
			#Should Chestplate Prevent Dragon's Breath Damage?
			prevent_dragons_breath = false
			#Should Leggings Prevent Wither?
			prevent_wither = false
			#Should Leggings Prevent Levitation?
			prevent_levitation = false
			#Should Boots Prevent Fall Damage?
			prevent_fall_damage = true

	[armor_config.vibranium]
		_comment = "Config for Vibranium armor"

		[armor_config.vibranium.armor_values]
			#Hat
			#Range: > 0
			hat = 6
			#Robes
			#Range: > 0
			robes = 11
			#Leggings
			#Range: > 0
			leggings = 9
			#Boots
			#Range: > 0
			boots = 6
			#Toughness
			#Range: > 0
			toughness = 5
			#Knockback Resistance
			#Range: 0.0 ~ 2.147483647E9
			knockback_resistance = 0.0

		[armor_config.vibranium.arcanist_stats]
			#Max Mana Bonus
			#Range: > 0
			mana_boost = 325
			#Mana Regen Bonus
			#Range: 0.0 ~ 2.147483647E9
			mana_regen = 9.0
			#Spell Power Bonus
			#Range: 0.0 ~ 2.147483647E9
			spell_power = 9.0

		[armor_config.vibranium.capabilities]
			#Should Helmet Prevent Drowning?
			prevent_drowning = true
			#Should Helmet Prevent Kinetic Damage?
			prevent_kinetic = true
			#Should Chestplate Prevent Fire Damage?
			prevent_fire = true
			#Should Chestplate Prevent Dragon's Breath Damage?
			prevent_dragons_breath = false
			#Should Leggings Prevent Wither?
			prevent_wither = true
			#Should Leggings Prevent Levitation?
			prevent_levitation = false
			#Should Boots Prevent Fall Damage?
			prevent_fall_damage = true

	[armor_config.unobtainium]
		_comment = "Config for Unobtainium armor"

		[armor_config.unobtainium.armor_values]
			#Hat
			#Range: > 0
			hat = 8
			#Robes
			#Range: > 0
			robes = 13
			#Leggings
			#Range: > 0
			leggings = 11
			#Boots
			#Range: > 0
			boots = 8
			#Toughness
			#Range: > 0
			toughness = 6
			#Knockback Resistance
			#Range: 0.0 ~ 2.147483647E9
			knockback_resistance = 0.0

		[armor_config.unobtainium.arcanist_stats]
			#Max Mana Bonus
			#Range: > 0
			mana_boost = 450
			#Mana Regen Bonus
			#Range: 0.0 ~ 2.147483647E9
			mana_regen = 12.0
			#Spell Power Bonus
			#Range: 0.0 ~ 2.147483647E9
			spell_power = 12.0

		[armor_config.unobtainium.capabilities]
			#Should Helmet Prevent Drowning?
			prevent_drowning = true
			#Should Helmet Prevent Kinetic Damage?
			prevent_kinetic = true
			#Should Chestplate Prevent Fire Damage?
			prevent_fire = true
			#Should Chestplate Prevent Dragon's Breath Damage?
			prevent_dragons_breath = true
			#Should Leggings Prevent Wither?
			prevent_wither = true
			#Should Leggings Prevent Levitation?
			prevent_levitation = true
			#Should Boots Prevent Fall Damage?
			prevent_fall_damage = true

