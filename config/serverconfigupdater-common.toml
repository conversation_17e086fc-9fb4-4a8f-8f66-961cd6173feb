
["Version Configuration"]
	# Define a version here. On world load the mod will look up the serverconfig version and reset all files that specified up to the newest version.
	# Example: ["1=minecraft","2=forge"] will reset minecraft and forge config on first load, but will only reset forge if the world has been loaded before with only version 1 defined
	versions = [""]

["File Deleter"]
	# This is intended for deleting files for pack updates. This is a last resort! Replace with empty files instead when possible. The file will be deleted every launch if it exists! Specify the path to the file. Comma Separated List. Example: scripts/badscript.zs
	files = ["kubejs/data/sgjourney/worldgen/", "kubejs/data/sgjourney/tags/worldgen/"]
	# By default Folders are only deleted if they are empty. Set to true to change that.
	deleteFoldersWithContent = true

