
#Common Settings
[common]
	#Disable / enable any items here (disables their recipes)
	enabledItems = ["sophisticatedstorage:stack_upgrade_tier_1|true", "sophisticatedstorage:stack_upgrade_tier_2|true", "sophisticatedstorage:stack_upgrade_tier_3|true", "sophisticatedstorage:stack_upgrade_tier_4|true", "sophisticatedstorage:stack_upgrade_tier_5|true", "sophisticatedstorage:upgrade_base|true", "sophisticatedstorage:crafting_upgrade|true", "sophisticatedstorage:limited_gold_barrel_1|true", "sophisticatedstorage:limited_gold_barrel_2|true", "sophisticatedstorage:limited_gold_barrel_3|true", "sophisticatedstorage:limited_gold_barrel_4|true", "sophisticatedstorage:shulker_box|true", "sophisticatedstorage:magnet_upgrade|true", "sophisticatedstorage:gold_barrel|true", "sophisticatedstorage:auto_smoking_upgrade|true", "sophisticatedbackpacks:chipped/glassblower_upgrade|true", "sophisticatedbackpacks:chipped/tinkering_table_upgrade|true", "sophisticatedstorage:smoking_upgrade|true", "sophisticatedbackpacks:auto_blasting_upgrade|true", "sophisticatedstorage:gold_shulker_box|true", "sophisticatedbackpacks:chipped/loom_table_upgrade|true", "sophisticatedbackpacks:filter_upgrade|true", "sophisticatedstorage:compacting_upgrade|true", "sophisticatedstorage:limited_iron_barrel_3|true", "sophisticatedstorage:chipped/loom_table_upgrade|true", "sophisticatedbackpacks:stack_upgrade_tier_3|true", "sophisticatedbackpacks:advanced_filter_upgrade|true", "sophisticatedbackpacks:stack_upgrade_tier_4|true", "sophisticatedbackpacks:everlasting_upgrade|true", "sophisticatedstorage:barrel|true", "sophisticatedbackpacks:advanced_jukebox_upgrade|true", "sophisticatedstorage:basic_to_iron_tier_upgrade|true", "sophisticatedbackpacks:chipped/mason_table_upgrade|true", "sophisticatedstorage:chipped/mason_table_upgrade|true", "sophisticatedbackpacks:advanced_pump_upgrade|true", "sophisticatedbackpacks:refill_upgrade|true", "sophisticatedstorage:void_upgrade|true", "sophisticatedstorage:iron_shulker_box|true", "sophisticatedstorage:stack_downgrade_tier_3|true", "sophisticatedstorage:limited_netherite_barrel_1|true", "sophisticatedstorage:limited_netherite_barrel_2|true", "sophisticatedstorage:limited_netherite_barrel_3|true", "sophisticatedstorage:limited_netherite_barrel_4|true", "sophisticatedbackpacks:advanced_tool_swapper_upgrade|true", "sophisticatedstorage:advanced_feeding_upgrade|true", "sophisticatedstorage:stack_upgrade_tier_1_plus|true", "sophisticatedstorage:chest|true", "sophisticatedstorage:iron_chest|true", "sophisticatedbackpacks:smithing_upgrade|true", "sophisticatedbackpacks:advanced_pickup_upgrade|true", "sophisticatedstorage:chipped/botanist_workbench_upgrade|true", "sophisticatedstorage:auto_blasting_upgrade|true", "sophisticatedstorage:netherite_shulker_box|true", "sophisticatedbackpacks:smelting_upgrade|true", "sophisticatedbackpacks:battery_upgrade|true", "sophisticatedstorage:limited_copper_barrel_1|true", "sophisticatedstorage:limited_copper_barrel_2|true", "sophisticatedstorage:limited_copper_barrel_3|true", "sophisticatedstorage:limited_copper_barrel_4|true", "sophisticatedstorage:hopper_upgrade|true", "sophisticatedstorage:storage_output|true", "sophisticatedbackpacks:deposit_upgrade|true", "sophisticatedstorage:advanced_magnet_upgrade|true", "sophisticatedstorage:stonecutter_upgrade|true", "sophisticatedstorage:limited_diamond_barrel_1|true", "sophisticatedstorage:limited_diamond_barrel_2|true", "sophisticatedstorage:limited_diamond_barrel_3|true", "sophisticatedstorage:limited_diamond_barrel_4|true", "sophisticatedstorage:feeding_upgrade|true", "sophisticatedbackpacks:jukebox_upgrade|true", "sophisticatedbackpacks:auto_smoking_upgrade|true", "sophisticatedbackpacks:xp_pump_upgrade|true", "sophisticatedstorage:copper_to_netherite_tier_upgrade|true", "sophisticatedstorage:limited_barrel_1|true", "sophisticatedstorage:limited_barrel_2|true", "sophisticatedstorage:limited_barrel_3|true", "sophisticatedstorage:limited_barrel_4|true", "sophisticatedbackpacks:advanced_deposit_upgrade|true", "sophisticatedstorage:copper_chest|true", "sophisticatedstorage:limited_iron_barrel_4|true", "sophisticatedbackpacks:auto_smelting_upgrade|true", "sophisticatedstorage:advanced_compacting_upgrade|true", "sophisticatedbackpacks:chipped/botanist_workbench_upgrade|true", "sophisticatedbackpacks:stack_upgrade_starter_tier|true", "sophisticatedbackpacks:advanced_magnet_upgrade|true", "sophisticatedbackpacks:pickup_upgrade|true", "sophisticatedbackpacks:stack_downgrade_tier_2|true", "sophisticatedbackpacks:stack_downgrade_tier_1|true", "sophisticatedbackpacks:stack_downgrade_tier_3|true", "sophisticatedstorage:diamond_shulker_box|true", "sophisticatedstorage:diamond_to_netherite_tier_upgrade|true", "sophisticatedstorage:jukebox_upgrade|true", "sophisticatedstorage:pickup_upgrade|true", "sophisticatedstorage:auto_smelting_upgrade|true", "sophisticatedstorage:diamond_chest|true", "sophisticatedbackpacks:stack_upgrade_omega_tier|true", "sophisticatedstorage:stack_downgrade_tier_2|true", "sophisticatedstorage:advanced_void_upgrade|true", "sophisticatedbackpacks:void_upgrade|true", "sophisticatedbackpacks:chipped/alchemy_bench_upgrade|true", "sophisticatedstorage:advanced_pickup_upgrade|true", "sophisticatedstorage:diamond_barrel|true", "sophisticatedbackpacks:advanced_feeding_upgrade|true", "sophisticatedbackpacks:magnet_upgrade|true", "sophisticatedbackpacks:iron_backpack|true", "sophisticatedstorage:netherite_chest|true", "sophisticatedbackpacks:tool_swapper_upgrade|true", "sophisticatedbackpacks:inception_upgrade|true", "sophisticatedstorage:copper_to_gold_tier_upgrade|true", "sophisticatedstorage:blasting_upgrade|true", "sophisticatedstorage:chipped/carpenters_table_upgrade|true", "sophisticatedstorage:stack_upgrade_omega_tier|true", "sophisticatedstorage:storage_input|true", "sophisticatedbackpacks:blasting_upgrade|true", "sophisticatedbackpacks:gold_backpack|true", "sophisticatedbackpacks:advanced_compacting_upgrade|true", "sophisticatedbackpacks:advanced_restock_upgrade|true", "sophisticatedstorage:smelting_upgrade|true", "sophisticatedbackpacks:tank_upgrade|true", "sophisticatedstorage:storage_link|true", "sophisticatedstorage:netherite_barrel|true", "sophisticatedbackpacks:feeding_upgrade|true", "sophisticatedbackpacks:smoking_upgrade|true", "sophisticatedstorage:storage_io|true", "sophisticatedstorage:iron_to_netherite_tier_upgrade|true", "sophisticatedbackpacks:crafting_upgrade|true", "sophisticatedstorage:chipped/tinkering_table_upgrade|true", "sophisticatedbackpacks:stack_upgrade_tier_2|true", "sophisticatedbackpacks:stack_upgrade_tier_1|true", "sophisticatedstorage:advanced_filter_upgrade|true", "sophisticatedstorage:basic_to_diamond_tier_upgrade|true", "sophisticatedstorage:copper_to_diamond_tier_upgrade|true", "sophisticatedstorage:advanced_jukebox_upgrade|true", "sophisticatedstorage:packing_tape|true", "sophisticatedstorage:filter_upgrade|true", "sophisticatedbackpacks:stonecutter_upgrade|true", "sophisticatedbackpacks:backpack|true", "sophisticatedbackpacks:advanced_void_upgrade|true", "sophisticatedstorage:compression_upgrade|true", "sophisticatedbackpacks:anvil_upgrade|true", "sophisticatedbackpacks:upgrade_base|true", "sophisticatedstorage:storage_tool|true", "sophisticatedstorage:chipped/glassblower_upgrade|true", "sophisticatedstorage:iron_barrel|true", "sophisticatedbackpacks:copper_backpack|true", "sophisticatedstorage:limited_iron_barrel_1|true", "sophisticatedbackpacks:restock_upgrade|true", "sophisticatedbackpacks:pump_upgrade|true", "sophisticatedstorage:gold_to_netherite_tier_upgrade|true", "sophisticatedstorage:stack_downgrade_tier_1|true", "sophisticatedbackpacks:compacting_upgrade|true", "sophisticatedstorage:iron_to_diamond_tier_upgrade|true", "sophisticatedbackpacks:diamond_backpack|true", "sophisticatedstorage:copper_barrel|true", "sophisticatedstorage:gold_chest|true", "sophisticatedstorage:controller|true", "sophisticatedstorage:basic_to_copper_tier_upgrade|true", "sophisticatedstorage:advanced_hopper_upgrade|true", "sophisticatedstorage:copper_shulker_box|true", "sophisticatedbackpacks:chipped/carpenters_table_upgrade|true", "sophisticatedbackpacks:advanced_refill_upgrade|true", "sophisticatedstorage:copper_to_iron_tier_upgrade|true", "sophisticatedstorage:limited_iron_barrel_2|true", "sophisticatedstorage:chipped/alchemy_bench_upgrade|true", "sophisticatedstorage:iron_to_gold_tier_upgrade|true", "sophisticatedstorage:basic_to_netherite_tier_upgrade|true", "sophisticatedstorage:gold_to_diamond_tier_upgrade|true", "sophisticatedstorage:basic_tier_upgrade|true", "sophisticatedstorage:basic_to_gold_tier_upgrade|true", "sophisticatedbackpacks:netherite_backpack|true", "sophisticatedbackpacks:infinity_upgrade|true", "sophisticatedbackpacks:survival_infinity_upgrade|true", "sophisticatedstorage:pump_upgrade|true", "sophisticatedstorage:advanced_pump_upgrade|true", "sophisticatedstorage:xp_pump_upgrade|true", "sophisticatedstorage:infinity_upgrade|true", "sophisticatedstorage:survival_infinity_upgrade|true", "sophisticatedstorage:debug_tool|true", "sophisticatedstorage:paintbrush|true", "sophisticatedstorage:decoration_table|true"]

