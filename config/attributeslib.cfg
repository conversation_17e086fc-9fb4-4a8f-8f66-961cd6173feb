# File Specification: https://gist.github.com/Shadows-of-Fire/88ac714a758636c57a52e32ace5474c1

# 

general {
    # If the Attributes GUI is available.
    # Default: true
    B:"Enable Attributes GUI"=true

    # If description tooltips will be added to potion items.
    # Default: true
    B:"Enable Potion Tooltips"=true

    # A list of attributes that will be hidden from the Attributes GUI.
    # Default: [forge:nametag_distance], [attributeslib:creative_flight], [attributeslib:elytra_flight], [attributeslib:ghost_health]
    S:"Hidden Attributes" <
        forge:nametag_distance
        attributeslib:creative_flight
        attributeslib:elytra_flight
        attributeslib:ghost_health
     >
}


combat_rules {
    # The protection damage reduction formula.
    # Computed after Prot Pierce and Prot Shred are applied.
    # Arguments:
    #     'protPoints' - The number of protection points the user has after reductions.
    # Output:
    #     The percentage of damage taken after protection has been applied, from 0 (no damage taken) to 1 (full damage taken).
    # Reference:
    #     See https://github.com/ezylang/EvalEx#usage-examples for how to write expressions.
    # 
    # Default: 1 - min(0.025 * protPoints, 0.85)
    S:"Protection Formula"=1 - min(0.025 * protPoints, 0.85)

    # The a-value formula, which computes an intermediate used in the armor formula.
    # Arguments:
    #     'damage' - The damage of the incoming attack.
    # Output:
    #     The a-value, which will be supplied as an argument to the armor formula.
    # Reference:
    #     See https://github.com/ezylang/EvalEx#usage-examples for how to write expressions.
    # 
    # Default: if(damage < 20, 10, 10 + (damage - 20) / 2)
    S:"A-Value Formula"=if(damage < 20, 10, 10 + (damage - 20) / 2)

    # The armor damage reduction formula.
    # Computed after Armor Pierce and Armor Shred are applied.
    # Arguments:
    #     'a' - The a-value computed by the a-value formula.
    #     'damage' - The damage of the incoming attack.
    #     'armor' - The armor value of the user after reductions.
    #     'toughness' - The armor toughness value of the user.
    # Output:
    #     The percentage of damage taken after armor has been applied, from 0 (no damage taken) to 1 (full damage taken).
    # Reference:
    #     See https://github.com/ezylang/EvalEx#usage-examples for how to write expressions.
    # Note:
    #     The vanilla formula is: 1 - min(max(armor - damage / (2 + toughness / 4), armor / 5), 20) / 25
    # 
    # Default: a / (a + armor)
    S:"Armor Formula"=a / (a + armor)
}


