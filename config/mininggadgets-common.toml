
#Power settings
[power]

	#Upgrade Cost Settings
	[power.upgrades]
		#Cost per block for Efficiency 5 upgrade
		#Range: > 0
		upgradeEfficiency5 = 50
		#Cost per Light Block placed
		#Range: > 0
		upgradeLight = 100
		#Cost per block for Silk Touch upgrade
		#Range: > 0
		upgradeSilkCost = 100
		#Cost per block Frozen
		#Range: > 0
		upgradeFreeze = 100
		#Cost per block for Fortune 3 upgrade
		#Range: > 0
		upgradeFortune3 = 100
		#Cost per block for Fortune 1 upgrade
		#Range: > 0
		upgradeFortune1 = 30
		#Cost per block for Fortune 2 upgrade
		#Range: > 0
		upgradeFortune2 = 60
		#Capacity Boost from Battery 1 Upgrade
		#Range: > 0
		battery1 = 2000000
		#Capacity Boost from Battery 2 Upgrade
		#Range: > 0
		battery2 = 5000000
		#Capacity Boost from Battery 3 Upgrade
		#Range: > 0
		battery3 = 10000000
		#Cost per block for Efficiency 1 upgrade
		#Range: > 0
		upgradeEfficiency1 = 10
		#Cost per block for Efficiency 2 upgrade
		#Range: > 0
		upgradeEfficiency2 = 20
		#Cost per block for Magnet upgrade
		#Range: > 0
		upgradeMagnet = 25
		#Cost per block for Void Junk upgrade
		#Range: > 0
		upgradeVoid = 10
		#Cost per block for Efficiency 3 upgrade
		#Range: > 0
		upgradeEfficiency3 = 30
		#Cost per block for Efficiency 4 upgrade
		#Range: > 0
		upgradeEfficiency4 = 40

	#Mining Gadget Settings
	[power.mining_gadget]
		#Base cost per block broken
		#Range: > 0
		baseCost = 200
		#Maximum power for the Mining Gadget
		#Range: > 0
		maxPower = 1000000

