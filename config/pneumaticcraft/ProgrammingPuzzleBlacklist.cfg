{"description": "In the 'blacklist' tag you can add any progwidget registry names you wish to blacklist from this instance. When they were used in existing programs already they will be deleted. A reference list of all known programming puzzle names can be seen in 'allWidgets'.", "blacklist": [], "allWidgets": ["pneumaticcraft:comment", "pneumaticcraft:start", "pneumaticcraft:area", "pneumaticcraft:text", "pneumaticcraft:item_filter", "pneumaticcraft:item_assign", "pneumaticcraft:liquid_filter", "pneumaticcraft:coordinate", "pneumaticcraft:coordinate_operator", "pneumaticcraft:entity_attack", "pneumaticcraft:dig", "pneumaticcraft:harvest", "pneumaticcraft:place", "pneumaticcraft:block_right_click", "pneumaticcraft:entity_right_click", "pneumaticcraft:pickup_item", "pneumaticcraft:drop_item", "pneumaticcraft:void_item", "pneumaticcraft:void_liquid", "pneumaticcraft:inventory_export", "pneumaticcraft:inventory_import", "pneumaticcraft:liquid_export", "pneumaticcraft:liquid_import", "pneumaticcraft:entity_export", "pneumaticcraft:entity_import", "pneumaticcraft:rf_import", "pneumaticcraft:rf_export", "pneumaticcraft:goto", "pneumaticcraft:teleport", "pneumaticcraft:emit_redstone", "pneumaticcraft:label", "pneumaticcraft:jump", "pneumaticcraft:jump_sub", "pneumaticcraft:wait", "pneumaticcraft:rename", "pneumaticcraft:suicide", "pneumaticcraft:external_program", "pneumaticcraft:crafting", "pneumaticcraft:standby", "pneumaticcraft:logistics", "pneumaticcraft:for_each_coordinate", "pneumaticcraft:for_each_item", "pneumaticcraft:edit_sign", "pneumaticcraft:condition_coordinate", "pneumaticcraft:condition_redstone", "pneumaticcraft:condition_light", "pneumaticcraft:condition_item_inventory", "pneumaticcraft:condition_block", "pneumaticcraft:condition_liquid_inventory", "pneumaticcraft:condition_entity", "pneumaticcraft:condition_pressure", "pneumaticcraft:condition_item", "pneumaticcraft:drone_condition_item", "pneumaticcraft:drone_condition_liquid", "pneumaticcraft:drone_condition_entity", "pneumaticcraft:drone_condition_pressure", "pneumaticcraft:drone_condition_upgrades", "pneumaticcraft:condition_rf", "pneumaticcraft:drone_condition_rf", "pneumaticcraft:computer_control"]}