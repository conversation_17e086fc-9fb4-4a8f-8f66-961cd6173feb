
#Modded Furnace Settings
[modded_furnaces]

	[modded_furnaces.vibranium_furnace]
		# How much RF to generate per tick
		# Default: 3000
		#Range: 1 ~ 100000
		generation = 3000
		# Number of items that can be smelted at once. The regular furnace only smelts 1 item at once of course.
		# Default: 32
		#Range: 1 ~ 64
		mult = 32
		# What tier this furnace should be.
		# Default: 2
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 3
		#Range: 1 ~ 72000
		speed = 3

	[modded_furnaces.allthemodium_furnace]
		# How much RF to generate per tick
		# Default: 2000
		#Range: 1 ~ 100000
		generation = 2000
		# Number of items that can be smelted at once. The regular furnace only smelts 1 item at once of course.
		# Default: 16
		#Range: 1 ~ 64
		mult = 16
		# What tier this furnace should be.
		# Default: 2
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 5
		#Range: 1 ~ 72000
		speed = 5

	[modded_furnaces.unobtainium_furnace]
		# How much RF to generate per tick
		# Default: 5000
		#Range: 1 ~ 100000
		generation = 5000
		# Number of items that can be smelted at once. The regular furnace only smelts 1 item at once of course.
		# Default: 64
		#Range: 1 ~ 64
		mult = 64
		# What tier this furnace should be.
		# Default: 2
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 1
		#Range: 1 ~ 72000
		speed = 1

#Update Checker Settings
[updates]

	[updates.check_updates]
		# true = check for updates, false = don't check for updates.
		# Default: true.
		updates = false

#Furnace Settings
[furnaces]
	# The capacity of the recipe cache, higher values use more memory.
	# Default: 10
	#Range: 1 ~ 100
	recipe_cache = 10

	[furnaces.iron_furnace]
		# How much RF to generate per tick
		# Default: 40
		#Range: 1 ~ 100000
		generation = 40
		# What tier this furnace should be.
		# Default: 0
		#Range: 0 ~ 2
		tier = 0
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 160
		#Range: 2 ~ 72000
		speed = 160

	[furnaces.obsidian_furnace]
		# How much RF to generate per tick
		# Default: 500
		#Range: 1 ~ 100000
		generation = 500
		# What tier this furnace should be.
		# Default: 2
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 20
		#Range: 2 ~ 72000
		speed = 20

	[furnaces.recipeMaxXPLevel]
		# How many levels of experience that can be stored in recipes stored in the furnace, after the experience stored in the recipe reaches this value (in levels) it will be voided.
		# Default: 100 
		# 100 levels is 30971 XP
		#Range: 1 ~ 1000
		level = 100

	[furnaces.netherite_furnace]
		# How much RF to generate per tick
		# Default: 1000
		#Range: 1 ~ 100000
		generation = 1000
		# What tier this furnace should be.
		# Default: 2
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 5
		#Range: 2 ~ 72000
		speed = 5

	[furnaces.copper_furnace]
		# How much RF to generate per tick
		# Default: 40
		#Range: 1 ~ 100000
		generation = 40
		# What tier this furnace should be.
		# Default: 0
		#Range: 0 ~ 2
		tier = 0
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 180
		#Range: 2 ~ 72000
		speed = 180

	[furnaces.emerald_furnace]
		# How much RF to generate per tick
		# Default: 320
		#Range: 1 ~ 100000
		generation = 320
		# What tier this furnace should be.
		# Default: 1
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 40
		#Range: 2 ~ 72000
		speed = 40

	[furnaces.million_furnace]
		# What tier this furnace should be.
		# Default: 2
		#Range: 0 ~ 2
		tier = 2

	[furnaces.crystal_furnace]
		# How much RF to generate per tick
		# Default: 360
		#Range: 1 ~ 100000
		generation = 360
		# What tier this furnace should be.
		# Default: 2
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 40
		#Range: 2 ~ 72000
		speed = 40

	[furnaces.diamond_furnace]
		# How much RF to generate per tick
		# Default: 240
		#Range: 1 ~ 100000
		generation = 240
		# What tier this furnace should be.
		# Default: 1
		#Range: 0 ~ 2
		tier = 2
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 80
		#Range: 2 ~ 72000
		speed = 80

	[furnaces.silver_furnace]
		# How much RF to generate per tick
		# Default: 100
		#Range: 1 ~ 100000
		generation = 100
		# What tier this furnace should be.
		# Default: 1
		#Range: 0 ~ 2
		tier = 1
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 140
		#Range: 2 ~ 72000
		speed = 140

	[furnaces.rainbow_furnace]
		# How much power the Rainbow Furnace will generate.
		# Default: 50000
		#Range: 1 ~ 100000000
		rainbow_generation = 50000
		# How much RF to generate per tick
		# Default: 2000
		#Range: 1 ~ 100000
		generation = 2000
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 20
		#Range: 2 ~ 72000
		speed = 20

	[furnaces.energy]
		# How much energy can be stored in tier 0 furnaces.
		# Default: 80 000
		#Range: > 4000
		tier_0 = 80000
		# How much energy can be stored in tier 2 furnaces.
		# Default: 1 000 000
		#Range: > 4000
		tier_2 = 1000000
		# How much energy can be stored in tier 1 furnaces.
		# Default: 200 000
		#Range: > 4000
		tier_1 = 200000

	[furnaces.gold_furnace]
		# How much RF to generate per tick
		# Default: 160
		#Range: 1 ~ 100000
		generation = 160
		# What tier this furnace should be.
		# Default: 1
		#Range: 0 ~ 2
		tier = 1
		# Number of ticks to complete one smelting operation.
		# 200 ticks is what a regular furnace takes.
		# Default: 120
		#Range: 2 ~ 72000
		speed = 120

#JEI Settings
[jei]

	[jei.jei]
		# Enable or disable the Click Area inside the GUI in all of Iron Furnaces' furnaces.
		enable_jei_click_area = true
		# Enable or disable the Catalysts in Jei for Iron Furnaces.
		enable_jei_catalysts = true
		# Enable or disable the JeiPlugin of Iron Furnaces.
		enable_jei = true

#Misc
[misc]

	[misc.misc]
		# Enable or disable the Rainbow Content
		rainbow = true
		# Enable or disable light-updates, furances will no longer emit light, true = disable
		lightupdates = false
		# Enable or disable version checking and player identification through the web, true = disabled, if your server is using firewall software you might want to disable this
		web = false
		# Show furnace settings errors in chat, used for debugging
		errors = false

