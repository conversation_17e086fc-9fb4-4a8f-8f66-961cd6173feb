
#General settings
[general]
	#Dimensions where hostile mobs will not spawn. Ex: ["minecraft:overworld", "undergarden:undergarden"]. . Run /forge dimensions for a list.
	dimensionBlacklist = ["twilightforest:twilight_forest", "ae2:spatial_storage", "mythicbotany:alfheim", "bloodmagic:dungeon"]
	#Spawn a book in the players inventory on login
	spawnBook = false
	#How much mana whirlisprigs consume per generation
	#Range: 0 ~ 10000
	sylphManaCost = 250
	#How much progress whirlisprigs must accumulate before creating resources
	#Range: 0 ~ 10000
	whirlisprigProgress = 250
	#Should the Wilden Hunter attack animals?
	hunterHuntsAnimals = false
	#Should the Wilden Stalker attack animals?
	stalkerHuntsAnimals = false
	#Should the Wilden Defender attack animals?
	defenderHuntsAnimals = false
	#Should the Wilden Chimera dive bomb destroy blocks?
	destructiveDiveBomb = true
	#Archwood forest spawn weight
	#Range: > 0
	archwoodForest = 2
	#How many inventories can lectern support per bookwyrm
	#Range: > 1
	bookwyrmLimit = 8

[drygmy_production]
	#How much source drygmys consume per generation
	#Range: 0 ~ 10000
	drygmyManaCost = 1000
	#How many channels must occur before a drygmy produces loot
	#Range: 0 ~ 300
	drygmyMaxProgress = 20
	#Bonus number of items a drygmy produces per unique mob
	#Range: 0 ~ 300
	drygmyUniqueBonus = 2
	#Base number of items a drygmy produces per cycle before bonuses.
	#Range: > -2147483648
	drygmyBaseItems = 1
	#Max Bonus number of items a drygmy produces from nearby entities. Each entity equals 1 item.
	#Range: 0 ~ 300
	drygmyQuantityCap = 5

#Items
[item]
	#Spawn Caster Tomes in Dungeon Loot?
	spawnTomes = true
	#How much mana the Ring of Jumping consumes per jump
	#Range: 0 ~ 10000
	jumpRingCost = 30

#Blocks
[block]
	#How much potion a melder takes from each input jar. 100 = 1 potion
	#Range: > 100
	melderInputCost = 200
	#How much potion a melder outputs per cycle. 100 = 1 potion
	#Range: > 100
	melderOutput = 100
	#How much source a melder takes per cycle
	#Range: > 0
	melderSourceCost = 300
	#The max potion level the enchanted flask can grant. This isnt needed unless you have an infinite potion leveling exploit.
	#Range: > 2
	enchantedFlaskCap = 255

#Debug
[debug]
	#Max number of log events to keep on entities. Lowering this number may make it difficult to debug why your entities are stuck.
	#Range: > 0
	maxLogEvents = 100

