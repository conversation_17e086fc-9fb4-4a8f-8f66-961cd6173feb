
#Generic settings
[generic]
	#Duration in ticks of each step of a crucible recipe.
	#Range: 20 ~ 1200
	crucibleStepDuration = 100
	#Makes it so that the Crucible will not fizzle out unless the recipe has failed, giving players more time to organize and plan their next step and behave more like a turn-based recipe.
	turnBasedCrucible = false
	#For turn-based Crucible, duration in ticks between each recipe check once the step duration expired.
	crucibleBackoff = 40
	#Maximum amount of ethereal health (soul half-hearts) an entity can have at once.
	#Range: 0 ~ 1000
	maxEtherealHealth = 60

#Soul Enchanter
[soulEnchanter]
	#How often the Soul Enchanter can apply enchantments on an item (a value below 0 means unlimited)
	soulEnchanterMaximumUses = -1
	#How many enchantments the item is allowed to have to be applicable for soul enchanting (a value below 0 means unlimited)
	soulEnchanterMaximumEnchantments = -1

