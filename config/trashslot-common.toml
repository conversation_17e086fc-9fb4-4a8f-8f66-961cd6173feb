#This causes the deletion slot to delete items instantly, similar to Creative Mode.
instantDeletion = false
#Set to true if you want the delete keybindings to work in creative as well. Note: Items will be deleted permanently in creative, regardless of instantDeletion setting!
enableDeleteKeysInCreative = true
#TrashSlot will show a hint the first time the trash slot is toggled off or can be enabled on a supported screen. Set to false to disable.
enableHints = true
#Not recommended, but this will allow you to use the keybinds for deleting items even if the trash slot itself is hidden.
allowDeletionWhileTrashSlotIsInvisible = false
#List of items that cannot be deleted, in comma-separated format of "modid:name".
deletionDenyList = []

