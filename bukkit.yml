settings:
  allow-end: true
  warn-on-overload: false
  permissions-file: permissions.yml
  update-folder: update
  plugin-profiling: false
  connection-throttle: 4000
  query-plugins: true
  deprecated-verbose: default
  shutdown-message: Server closed
  minimum-api: none
  use-map-color-cache: true
  spawn-radius: 0
spawn-limits:
  axolotls: 5
  monsters: 70
  animals: 10
  water-animals: 5
  water-ambient: 20
  water-underground-creature: 5
  ambient: 15
chunk-gc:
  period-in-ticks: 600
ticks-per:
  axolotl-spawns: 1
  animal-spawns: 400
  monster-spawns: 1
  water-spawns: 1
  water-ambient-spawns: 1
  water-underground-creature-spawns: 1
  ambient-spawns: 1
  autosave: 6000
aliases: now-in-commands.yml
