[297月2025 17:03:41.972] [main/INFO] [Luminara/]: 

        __                    _                       
       / /   __  ______ ___  (_)___  ____ __________ _
      / /   / / / / __ `__ \/ / __ \/ __ `/ ___/ __ `/
     / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / 
    /_____/\__,_/_/ /_/ /_/_/_/ /_/\__,_/_/   \__,_/  

    Luminara·流明纳拉 服务端 By QianMoo0121(QianMo_ProMax)
    运行版本 顿顽 1.20.1 / luminara-1.20.1-1.0.8-a193d85
    构建日期 2025-07-29 06:36:08

[297月2025 17:03:41.981] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher running: args [--launchTarget, arclightserver, --fml.forgeVersion, 47.4.4, --fml.mcVersion, 1.20.1, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, 20230612.114412, -nogui]
[297月2025 17:03:41.981] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher 10.0.9+10.0.9+main.dcd20f30 starting: java version 21.0.6 by Oracle Corporation; OS Linux arch amd64 version 6.14.6-x64v3-xanmod1
[297月2025 17:03:42.826] [main/INFO] [net.minecraftforge.fml.loading.ImmediateWindowHandler/]: ImmediateWindowProvider not loading because launch target is arclightserver
[297月2025 17:03:42.839] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.5 Source=union:/root/server/main/libraries/org/spongepowered/mixin/0.8.5/mixin-0.8.5.jar%2399!/ Service=ModLauncher Env=SERVER
[297月2025 17:03:43.165] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/fmlcore/1.20.1-47.4.4/fmlcore-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:03:43.165] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/javafmllanguage/1.20.1-47.4.4/javafmllanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:03:43.165] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/lowcodelanguage/1.20.1-47.4.4/lowcodelanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:03:43.166] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/mclanguage/1.20.1-47.4.4/mclanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:03:43.404] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: configuration. Using Mod File: /root/server/main/mods/configuration-forge-1.20.1-3.1.0.jar
[297月2025 17:03:43.404] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: geckolib. Using Mod File: /root/server/main/mods/geckolib-forge-1.20.1-4.7.1.2.jar
[297月2025 17:03:43.404] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: curios. Using Mod File: /root/server/main/mods/curios-forge-5.14.1+1.20.1.jar
[297月2025 17:03:43.404] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: resourcefullib. Using Mod File: /root/server/main/mods/resourcefullib-forge-1.20.1-2.1.29.jar
[297月2025 17:03:43.404] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: ldlib. Using Mod File: /root/server/main/mods/ldlib-forge-1.20.1-1.0.41.b.jar
[297月2025 17:03:43.404] [main/INFO] [net.minecraftforge.fml.loading.moddiscovery.JarInJarDependencyLocator/]: Found 36 dependencies adding them to mods collection
[297月2025 17:03:46.663] [main/INFO] [mixin/]: Compatibility level set to JAVA_17
[297月2025 17:03:46.793] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [com.thevortex.allthetweaks.mixin.MixinConnector]
[297月2025 17:03:46.794] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [com.leobeliik.extremesoundmuffler.MixinConnector]
[297月2025 17:03:46.795] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [shetiphian.endertanks.mixins.MixinConnector]
[297月2025 17:03:46.795] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [shetiphian.core.mixins.MixinConnector]
[297月2025 17:03:46.795] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [ca.spottedleaf.starlight.mixin.MixinConnector]
[297月2025 17:03:46.796] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [io.izzel.arclight.common.mod.ArclightConnector]
[297月2025 17:03:46.805] [main/INFO] [Arclight/]: 核心 Mixin 配置已加载
[297月2025 17:03:46.806] [main/INFO] [Arclight/]: 优化 Mixin 配置已加载
[297月2025 17:03:46.808] [main/INFO] [cpw.mods.modlauncher.LaunchServiceHandler/MODLAUNCHER]: Launching target 'arclightserver' with arguments [-nogui]
[297月2025 17:03:46.815] [main/WARN] [mixin/]: Reference map 'universalgrid.refmap.json' for universalgrid.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.820] [main/WARN] [mixin/]: Reference map 'handcrafted-forge-1.20.1-forge-refmap.json' for handcrafted.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.823] [main/WARN] [mixin/]: Reference map 'yungsextras.refmap.json' for yungsextras.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.823] [main/WARN] [mixin/]: Reference map 'yungsextras.refmap.json' for yungsextras_forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.825] [main/WARN] [mixin/]: Reference map 'nitrogen_internals.refmap.json' for nitrogen_internals.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.828] [main/WARN] [mixin/]: Reference map 'EpheroLib-refmap.json' for epherolib.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.832] [main/WARN] [mixin/]: Reference map '${refmap_target}refmap.json' for corgilib.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.840] [main/WARN] [mixin/]: Reference map 'tempad-forge-1.20.1-forge-refmap.json' for tempad.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.869] [main/INFO] [com.abdelaziz.saturn.common.Saturn/]: Loaded Saturn config file with 4 configurable options
[297月2025 17:03:46.904] [main/WARN] [mixin/]: Reference map 'cristellib-forge-refmap.json' for cristellib.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.942] [main/WARN] [mixin/]: Reference map 'naturalist-forge-forge-refmap.json' for naturalist.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.942] [main/WARN] [mixin/]: Reference map 'cookingforblockheads.refmap.json' for cookingforblockheads.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.955] [main/WARN] [mixin/]: Reference map 'trashslot.refmap.json' for trashslot.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.961] [main/WARN] [mixin/]: Reference map 'bloodmagic.refmap.json' for bloodmagic.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.984] [main/WARN] [mixin/]: Reference map 'modonomicon.refmap.json' for modonomicon.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.984] [main/WARN] [mixin/]: Reference map 'modonomicon.refmap.json' for modonomicon.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:46.989] [main/WARN] [mixin/]: Reference map 'packetfixer-forge-forge-refmap.json' for packetfixer.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:03:47.185] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:03:47.191] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:03:47.245] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/inventory/AnvilMenu
[297月2025 17:03:47.260] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:03:47.260] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:03:47.361] [main/ERROR] [net.minecraftforge.coremod.transformer.CoreModBaseTransformer/COREMOD]: Error occurred applying transform of coremod coremods/field_to_method.js function biome
java.lang.IllegalStateException: Field f_47437_ is not private and an instance field
	at net.minecraftforge.coremod.api.ASMAPI.redirectFieldToMethod(ASMAPI.java:1069) ~[coremods-5.2.4.jar%2388!/:?] {}
	at org.openjdk.nashorn.internal.scripts.Script$Recompilation$123$292A$\^eval\_.initializeCoreMod#transformer(<eval>:11) ~[?:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunctionData.invoke(ScriptFunctionData.java:648) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunction.invoke(ScriptFunction.java:513) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptRuntime.apply(ScriptRuntime.java:520) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.api.scripting.ScriptObjectMirror.call(ScriptObjectMirror.java:111) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at net.minecraftforge.coremod.NashornFactory.lambda$getFunction$0(NashornFactory.java:37) ~[coremods-5.2.4.jar%2388!/:5.2.4] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:22) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:14) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModBaseTransformer.transform(CoreModBaseTransformer.java:60) ~[coremods-5.2.4.jar%2388!/:?] {}
	at cpw.mods.modlauncher.TransformerHolder.transform(TransformerHolder.java:41) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.performVote(ClassTransformer.java:179) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:117) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.getMaybeTransformedClassBytes(ModuleClassLoader.java:250) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.buildTransformedClassNodeFor(TransformingClassLoader.java:58) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchPluginHandler.lambda$announceLaunch$10(LaunchPluginHandler.java:100) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.getClassNode(MixinLaunchPluginLegacy.java:222) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.getClassNode(MixinLaunchPluginLegacy.java:207) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.ClassInfo.forName(ClassInfo.java:2005) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.getTargetClass(MixinInfo.java:1017) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.readTargetClasses(MixinInfo.java:1007) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.parseTargets(MixinInfo.java:895) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinConfig.prepareMixins(MixinConfig.java:867) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinConfig.prepare(MixinConfig.java:775) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.prepareConfigs(MixinProcessor.java:539) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.select(MixinProcessor.java:462) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.checkSelect(MixinProcessor.java:438) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:290) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:250) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.service.modlauncher.MixinTransformationHandler.processClass(MixinTransformationHandler.java:131) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.processClass(MixinLaunchPluginLegacy.java:131) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at cpw.mods.modlauncher.serviceapi.ILaunchPluginService.processClassWithFlags(ILaunchPluginService.java:156) ~[modlauncher-10.0.9.jar%2389!/:10.0.9+10.0.9+main.dcd20f30] {}
	at cpw.mods.modlauncher.LaunchPluginHandler.offerClassNodeToPlugins(LaunchPluginHandler.java:88) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:120) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.readerToClass(ModuleClassLoader.java:113) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.lambda$findClass$15(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadFromModule(ModuleClassLoader.java:229) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.findClass(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at java.lang.ClassLoader.loadClass(ClassLoader.java:638) ~[?:?] {}
	at java.lang.Class.forName(Class.java:625) ~[?:?] {}
	at java.lang.Class.forName(Class.java:600) ~[?:?] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler$DummyProvider.lambda$updateModuleReads$1(ImmediateWindowHandler.java:145) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at java.util.Optional.map(Optional.java:260) ~[?:?] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler$DummyProvider.updateModuleReads(ImmediateWindowHandler.java:145) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler.acceptGameLayer(ImmediateWindowHandler.java:71) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.FMLLoader.beforeStart(FMLLoader.java:216) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.launchService(CommonLaunchHandler.java:92) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:03:47.460] [main/WARN] [mixin/]: Error loading class: net/dries007/tfc/common/blocks/rock/AqueductBlock (java.lang.ClassNotFoundException: net.dries007.tfc.common.blocks.rock.AqueductBlock)
[297月2025 17:03:47.460] [main/WARN] [mixin/]: @Mixin target net.dries007.tfc.common.blocks.rock.AqueductBlock was not found allthetweaks.mixins.json:FluiDucts
[297月2025 17:03:47.469] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Patching FishingHook#catchingFish
[297月2025 17:03:47.485] [main/WARN] [mixin/]: Error loading class: mcjty/theoneprobe/apiimpl/providers/DefaultProbeInfoProvider (java.lang.ClassNotFoundException: mcjty.theoneprobe.apiimpl.providers.DefaultProbeInfoProvider)
[297月2025 17:03:47.485] [main/WARN] [mixin/]: @Mixin target mcjty.theoneprobe.apiimpl.providers.DefaultProbeInfoProvider was not found mixins.endertanks.json:ET_HideFluidBars$_TheOneProbe
[297月2025 17:03:47.498] [main/WARN] [mixin/]: Error loading class: com/illusivesoulworks/diet/common/DietApiImpl (java.lang.ClassNotFoundException: com.illusivesoulworks.diet.common.DietApiImpl)
[297月2025 17:03:47.498] [main/WARN] [mixin/]: @Mixin target com.illusivesoulworks.diet.common.DietApiImpl was not found caupona.mixins.json:DietApiImplMixin
[297月2025 17:03:47.505] [main/WARN] [mixin/]: Error loading class: vazkii/quark/addons/oddities/inventory/BackpackMenu (java.lang.ClassNotFoundException: vazkii.quark.addons.oddities.inventory.BackpackMenu)
[297月2025 17:03:47.505] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/gui/screens/inventory/CraftingScreen (java.lang.ClassNotFoundException: net.minecraft.client.gui.screens.inventory.CraftingScreen)
[297月2025 17:03:47.505] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.gui.screens.inventory.CraftingScreen was not found fastbench.mixins.json:MixinCraftingScreen
[297月2025 17:03:47.513] [main/WARN] [mixin/]: Error loading class: shadows/placebo/patreon/TrailsManager (java.lang.ClassNotFoundException: shadows.placebo.patreon.TrailsManager)
[297月2025 17:03:47.513] [main/WARN] [mixin/]: @Mixin target shadows.placebo.patreon.TrailsManager was not found smsn.mixins.json:placebo.TrailsManagerMixin
[297月2025 17:03:47.514] [main/WARN] [mixin/]: Error loading class: shadows/placebo/patreon/WingsManager (java.lang.ClassNotFoundException: shadows.placebo.patreon.WingsManager)
[297月2025 17:03:47.514] [main/WARN] [mixin/]: @Mixin target shadows.placebo.patreon.WingsManager was not found smsn.mixins.json:placebo.WingsManagerMixin
[297月2025 17:03:47.571] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/multiplayer/MultiPlayerGameMode for invalid dist DEDICATED_SERVER
[297月2025 17:03:47.572] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/multiplayer/MultiPlayerGameMode (java.lang.RuntimeException: Attempted to load class net/minecraft/client/multiplayer/MultiPlayerGameMode for invalid dist DEDICATED_SERVER)
[297月2025 17:03:47.572] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.multiplayer.MultiPlayerGameMode was not found mixins.cofhcore.json:MultiPlayerGameModeMixin
[297月2025 17:03:47.642] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:03:47.642] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTreasureOnly() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:03:47.642] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTradeable() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:03:47.687] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/item/CreativeModeTabs
[297月2025 17:03:47.727] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/renderer/entity/PhantomRenderer (java.lang.ClassNotFoundException: net.minecraft.client.renderer.entity.PhantomRenderer)
[297月2025 17:03:47.727] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.renderer.entity.PhantomRenderer was not found mixins.deeperdarker.json:PhantomRendererMixin
[297月2025 17:03:47.742] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/gui/screens/inventory/AbstractContainerScreen for invalid dist DEDICATED_SERVER
[297月2025 17:03:47.742] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/gui/screens/inventory/AbstractContainerScreen (java.lang.RuntimeException: Attempted to load class net/minecraft/client/gui/screens/inventory/AbstractContainerScreen for invalid dist DEDICATED_SERVER)
[297月2025 17:03:47.742] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.gui.screens.inventory.AbstractContainerScreen was not found findme-common.mixins.json:MixinSlotRenderer
[297月2025 17:03:47.743] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/particle/ParticleEngine for invalid dist DEDICATED_SERVER
[297月2025 17:03:47.743] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/particle/ParticleEngine (java.lang.RuntimeException: Attempted to load class net/minecraft/client/particle/ParticleEngine for invalid dist DEDICATED_SERVER)
[297月2025 17:03:47.743] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.particle.ParticleEngine was not found findme-common.mixins.json:ParticleEngineAccessor
[297月2025 17:03:47.821] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/resources/model/ModelBakery for invalid dist DEDICATED_SERVER
[297月2025 17:03:47.821] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/resources/model/ModelBakery (java.lang.RuntimeException: Attempted to load class net/minecraft/client/resources/model/ModelBakery for invalid dist DEDICATED_SERVER)
[297月2025 17:03:47.821] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.resources.model.ModelBakery was not found mixins.aae.json:client.ModelBakeryMixin
[297月2025 17:03:47.841] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:03:47.841] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isDiscoverable() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:03:48.226] [main/INFO] [MixinExtras|Service/]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.5.0).
[297月2025 17:03:48.410] [main/INFO] [mixin/]: Mixing server.MixinDedicatedServer from mixins/common/nochatreports.mixins.json into net.minecraft.server.dedicated.DedicatedServer
[297月2025 17:03:48.964] [main/WARN] [mixin/]: Method overwrite conflict for getThis in mixins.brandonscore.json:LivingEntityMixin, previously written by com.teammoeg.caupona.mixin.LivingEntityMixin. Skipping method.
[297月2025 17:03:48.985] [main/WARN] [mixin/]: @Redirect conflict. Skipping attributeslib.mixins.json:LivingEntityMixin->@Redirect::apoth_sunderingHasEffect(Lnet/minecraft/world/entity/LivingEntity;Lnet/minecraft/world/effect/MobEffect;)Z with priority 1000, already redirected by mixins.arclight.core.json:world.entity.LivingEntityMixin$ApotheosisCompatMixin->@Redirect::arclight$mutePotion(Lnet/minecraft/world/entity/LivingEntity;Lnet/minecraft/world/effect/MobEffect;)Z with priority 1500
[297月2025 17:03:49.182] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/item/CreativeModeTabs
[297月2025 17:03:49.324] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/experimental/module/GameNerfsModule
[297月2025 17:03:49.732] [main/WARN] [mixin/]: @Redirect conflict. Skipping securitycraft.mixins.json:camera.ServerPlayerMixin->@Redirect::securitycraft$tick(Lnet/minecraft/server/level/ServerPlayer;DDDFF)V with priority 1100, already redirected by railways-common.mixins.json:conductor_possession.ServerPlayerMixin->@Redirect::railways$securitycraft$tick(Lnet/minecraft/server/level/ServerPlayer;DDDFF)V with priority 1200
[297月2025 17:03:49.872] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_150430_ in mixins.arclight.core.json:world.inventory.AbstractContainerMenuMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:03:49.943] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 5 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/module/AncientTomesModule
[297月2025 17:03:49.955] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:03:49.955] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:03:50.110] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/inventory/AnvilMenu
[297月2025 17:03:50.114] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:03:50.114] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:03:50.114] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:03:50.354] [main/WARN] [mixin/]: Injection warning: LVT in net/minecraft/world/item/BoatItem::m_7203_(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;)Lnet/minecraft/world/InteractionResultHolder; has incompatible changes at opcode 158 in callback securitycraft.mixins.json:boat.BoatItemMixin->@Inject::securitycraft$maybeSetSecuritySeaBoatOwner(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/HitResult;Lnet/minecraft/world/phys/Vec3;DLjava/util/List;Lnet/minecraft/world/entity/vehicle/Boat;)V.
 Expected: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/HitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;]
    Found: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/BlockHitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;]
Available: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/BlockHitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;, Ljava/util/Iterator;, Lnet/minecraft/world/entity/Entity;, Lnet/minecraft/world/phys/AABB;]
[297月2025 17:03:50.757] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_147092_ in mixins.arclight.core.json:world.entity.ExperienceOrbMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:03:51.081] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_37547_ in mixins.arclight.core.json:world.entity.projectile.ThrownPotionMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:03:51.147] [main/INFO] [mixin/]: Mixing common.MixinFriendlyByteBuf from mixins/common/nochatreports.mixins.json into net.minecraft.network.FriendlyByteBuf
[297月2025 17:03:51.147] [main/INFO] [mixin/]: Renaming synthetic method lambda$onWriteJsonWithCodec$1(Ljava/lang/Object;Ljava/lang/String;)Lio/netty/handler/codec/EncoderException; to mdcaf68a$lambda$onWriteJsonWithCodec$1$0 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[297月2025 17:03:51.147] [main/INFO] [mixin/]: Renaming synthetic method lambda$onReadJsonWithCodec$0(Ljava/lang/String;)Lio/netty/handler/codec/DecoderException; to mdcaf68a$lambda$onReadJsonWithCodec$0$1 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[297月2025 17:03:51.253] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Patching FishingHook#catchingFish
[297月2025 17:03:51.321] [main/INFO] [mixin/]: Mixing server.MixinServerGamePacketListenerImpl from mixins/common/nochatreports.mixins.json into net.minecraft.server.network.ServerGamePacketListenerImpl
[297月2025 17:03:51.603] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:03:51.603] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isDiscoverable() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:03:51.913] [main/ERROR] [net.minecraftforge.coremod.transformer.CoreModBaseTransformer/COREMOD]: Error occurred applying transform of coremod coremods/field_to_method.js function biome
java.lang.IllegalStateException: Field f_47437_ is not private and an instance field
	at net.minecraftforge.coremod.api.ASMAPI.redirectFieldToMethod(ASMAPI.java:1069) ~[coremods-5.2.4.jar%2388!/:?] {}
	at org.openjdk.nashorn.internal.scripts.Script$Recompilation$123$292A$\^eval\_.initializeCoreMod#transformer(<eval>:11) ~[?:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunctionData.invoke(ScriptFunctionData.java:648) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunction.invoke(ScriptFunction.java:513) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptRuntime.apply(ScriptRuntime.java:520) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.api.scripting.ScriptObjectMirror.call(ScriptObjectMirror.java:111) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at net.minecraftforge.coremod.NashornFactory.lambda$getFunction$0(NashornFactory.java:37) ~[coremods-5.2.4.jar%2388!/:5.2.4] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:22) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:14) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModBaseTransformer.transform(CoreModBaseTransformer.java:60) ~[coremods-5.2.4.jar%2388!/:?] {}
	at cpw.mods.modlauncher.TransformerHolder.transform(TransformerHolder.java:41) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.performVote(ClassTransformer.java:179) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:117) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.readerToClass(ModuleClassLoader.java:113) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.lambda$findClass$15(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadFromModule(ModuleClassLoader.java:229) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.findClass(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadClass(ModuleClassLoader.java:135) ~[securejarhandler-2.1.10.jar:?] {}
	at java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?] {}
	at net.minecraft.world.level.biome.FixedBiomeSource.<clinit>(FixedBiomeSource.java:17) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:classloading,pl:accesstransformer:B}
	at net.minecraft.world.level.biome.BiomeSources.m_220586_(BiomeSources.java:8) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:classloading}
	at net.minecraft.core.registries.BuiltInRegistries.m_258029_(BuiltInRegistries.java:448) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.core.registries.BuiltInRegistries.m_258037_(BuiltInRegistries.java:462) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:986) ~[?:?] {}
	at net.minecraft.core.registries.BuiltInRegistries.m_257453_(BuiltInRegistries.java:461) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.core.registries.BuiltInRegistries.m_257498_(BuiltInRegistries.java:455) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.server.Bootstrap.m_135870_(BootstrapMixin.java:55) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,re:classloading,pl:mixin:APP:mixins.arclight.core.json:server.BootstrapMixin,pl:mixin:APP:ae2.mixins.json:EarlyStartupMixin,pl:mixin:A}
	at net.minecraft.server.Main.main(Main.java:121) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:03:52.245] [main/INFO] [com.almostreliable.merequester.MERequester/]: Registering content
[297月2025 17:03:52.423] [modloading-worker-0/INFO] [com.aetherteam.cumulus.Cumulus/]: Disabling Cumulus as it is on server.
[297月2025 17:03:52.442] [modloading-worker-0/INFO] [LowDragLib/]: LowDragLib is initializing on platform: Forge
[297月2025 17:03:52.442] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for industrialforegoing
[297月2025 17:03:52.446] [modloading-worker-0/INFO] [universalgrid/]: Loading config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:03:52.446] [modloading-worker-0/INFO] [universalgrid/]: Built config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:03:52.447] [modloading-worker-0/INFO] [universalgrid/]: Loaded config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:03:52.449] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class PatchouliPlugin for plugin patchouli
[297月2025 17:03:52.449] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class CuriosPlugin for plugin curios
[297月2025 17:03:52.451] [modloading-worker-0/INFO] [PluginManager/]: Constructed class PatchouliPlugin for plugin patchouli for mod industrialforegoing
[297月2025 17:03:52.451] [modloading-worker-0/INFO] [PluginManager/]: Constructed class CuriosPlugin for plugin curios for mod industrialforegoing
[297月2025 17:03:52.452] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class PatchouliPlugin
[297月2025 17:03:52.455] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class CuriosPlugin
[297月2025 17:03:52.456] [modloading-worker-0/INFO] [create_new_age/]: Hello 1.20.1 Create!
[297月2025 17:03:52.460] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class PatchouliPlugin
[297月2025 17:03:52.460] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class CuriosPlugin
[297月2025 17:03:52.505] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 14 blocks registered.
[297月2025 17:03:52.505] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 29 items registered.
[297月2025 17:03:52.505] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 13 tiles registered.
[297月2025 17:03:52.505] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 13 containers registered.
[297月2025 17:03:52.549] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for sushigocrafting
[297月2025 17:03:52.551] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class PatchouliPlugin for plugin patchouli
[297月2025 17:03:52.551] [modloading-worker-0/INFO] [PluginManager/]: Constructed class PatchouliPlugin for plugin patchouli for mod sushigocrafting
[297月2025 17:03:52.551] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class PatchouliPlugin
[297月2025 17:03:52.551] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class PatchouliPlugin
[297月2025 17:03:52.570] [modloading-worker-0/INFO] [mixin/]: Mixing server.MixinPlayerList from mixins/common/nochatreports.mixins.json into net.minecraft.server.players.PlayerList
[297月2025 17:03:52.600] [modloading-worker-0/INFO] [Advanced Peripherals/]: AdvancedPeripherals says hello!
[297月2025 17:03:52.610] [modloading-worker-0/INFO] [net.bdew.generators.integration.ic2c.IC2CIntegration$/]: IC2 Not loaded, skipping integration
[297月2025 17:03:52.650] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Warped Netherlands
[297月2025 17:03:52.650] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class PatchouliPlugin
[297月2025 17:03:52.650] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Stalactite Caves
[297月2025 17:03:52.650] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Space Wars
[297月2025 17:03:52.650] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Shire
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Desert Oasis
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Pagoda
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Minecolonies Original
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Nordic Spruce
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Spruce
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Oak
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Dark Oak
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Birch
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Lost Mesa City
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Jungle Treehouse
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Incan
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Fortress
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Dark Oak Treehouse
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Colonial
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Cavern
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Caledonia
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Urban Birch
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Ancient Athens
[297月2025 17:03:52.651] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Urban Savanna
[297月2025 17:03:52.651] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:pufferfish_bucket is now minecraft:bucket.
[297月2025 17:03:52.651] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:salmon_bucket is now minecraft:bucket.
[297月2025 17:03:52.651] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:cod_bucket is now minecraft:bucket.
[297月2025 17:03:52.651] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:tropical_fish_bucket is now minecraft:bucket.
[297月2025 17:03:52.651] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:axolotl_bucket is now minecraft:bucket.
[297月2025 17:03:52.651] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:powder_snow_bucket is now minecraft:bucket.
[297月2025 17:03:52.651] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:tadpole_bucket is now minecraft:bucket.
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Antique
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: High Magic
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Frontier
[297月2025 17:03:52.652] [Structurize IO Worker #0/WARN] [structurize/]: Missing Mod: byg for Pack: Corrupted
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Crimson Keep
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: FairyTale
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Functional Fantasy
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Underwater Base
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Aquatica
[297月2025 17:03:52.652] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Steampunk
[297月2025 17:03:52.653] [modloading-worker-0/INFO] [de.keksuccino.konkrete.Konkrete/]: [KONKRETE] Successfully initialized!
[297月2025 17:03:52.653] [modloading-worker-0/INFO] [de.keksuccino.konkrete.Konkrete/]: [KONKRETE] Server-side libs ready to use!
[297月2025 17:03:52.653] [Structurize IO Worker #0/WARN] [structurize/]: Failed loading packs from main folder path: .
[297月2025 17:03:52.653] [Structurize IO Worker #0/WARN] [structurize/]: Failed loading client packs from main folder path: .
[297月2025 17:03:52.653] [Structurize IO Worker #0/WARN] [structurize/]: Finished discovering Server Structure packs
[297月2025 17:03:52.654] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class PatchouliPlugin
[297月2025 17:03:52.711] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for industrialforegoingsouls
[297月2025 17:03:52.714] [modloading-worker-0/INFO] [fr.samlegamer.mcwbiomesoplenty.McwBOP/]: Macaw's Biomes O' Plenty Loading...
[297月2025 17:03:52.718] [modloading-worker-0/INFO] [cy.jdkdigital.utilitarian.Utilitarian/]: setting up server config
[297月2025 17:03:52.734] [modloading-worker-0/INFO] [com.cupboard.Cupboard/]: Loaded config for: cupboard.json
[297月2025 17:03:52.748] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for powah
[297月2025 17:03:52.748] [modloading-worker-0/WARN] [defaultsettings/]: DefaultSettings is a client-side mod only! It won't do anything on servers!
[297月2025 17:03:52.751] [modloading-worker-0/INFO] [voidscape/]: Starting Donator Handler
[297月2025 17:03:52.752] [Voidscape Donator Loader/INFO] [voidscape/]: Loading donor data
[297月2025 17:03:52.753] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for create
[297月2025 17:03:52.757] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for mekanism
[297月2025 17:03:52.826] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for botania
[297月2025 17:03:52.881] [modloading-worker-0/INFO] [net.permutated.pylons.Pylons/]: Registering mod: pylons
[297月2025 17:03:52.883] [modloading-worker-0/INFO] [fr.samlegamer.mcwbiomesoplenty.McwBOP/]: Macaw's Biomes O' Plenty Is Charged !
[297月2025 17:03:52.918] [modloading-worker-0/WARN] [de.keksuccino.justzoom.JustZoom/]: [JUST ZOOM] Disabling 'Just Zoom' since it's a client-side mod and current environment is server!
[297月2025 17:03:52.918] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class PatchouliPlugin
[297月2025 17:03:52.918] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class CuriosPlugin
[297月2025 17:03:52.928] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for functionalstorage
[297月2025 17:03:52.965] [modloading-worker-0/INFO] [net.permutated.pylons.Pylons/]: Registered 1 network packets
[297月2025 17:03:53.008] [modloading-worker-0/INFO] [net.bdew.generators.integration.mekanism.MekanismIntegration$/]: Mekanism loaded, activating integration
[297月2025 17:03:53.020] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id architectury:sync_ids
[297月2025 17:03:53.026] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class PatchouliPlugin
[297月2025 17:03:53.026] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class CuriosPlugin
[297月2025 17:03:53.033] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/42a7ad70b1cc371599a0eff744096b8a
[297月2025 17:03:53.065] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/74a5e841822a3a87854ae896a33430d6
[297月2025 17:03:53.086] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/eb3d1e2748533430848cadf0f37c7e9c
[297月2025 17:03:53.090] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/91c8520f19f93b3e8b6a727568e194ab
[297月2025 17:03:53.100] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/8c2784d778293fd482ed84b8aa5fedb9
[297月2025 17:03:53.102] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/ea038224ea783d40b2863f52239e2604
[297月2025 17:03:53.106] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'multiblock' for mod 'bdlib'
[297月2025 17:03:53.106] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/97ad64b7ecaf33209991c4f031501a58
[297月2025 17:03:53.121] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'misc' for mod 'bdlib'
[297月2025 17:03:53.133] [modloading-worker-0/INFO] [net.bdew.lib.BdLib$/]: Initialized multiblock manager for advgenerators
[297月2025 17:03:53.187] [modloading-worker-0/INFO] [net.permutated.novillagerdm.NoVillagerDeathMessages/]: Registering mod: novillagerdm
[297月2025 17:03:53.254] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'generators' for mod 'advgenerators'
[297月2025 17:03:53.296] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Found 6 RS API injection points
[297月2025 17:03:53.296] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.ultramega.universalgrid.UniversalGrid RSAPI
[297月2025 17:03:53.296] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in net.gigabit101.rebornstorage.RebornStorage RSAPI
[297月2025 17:03:53.318] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.enderio.conduits.common.integrations.refinedstorage.RSTicker RSAPI
[297月2025 17:03:53.338] [modloading-worker-0/INFO] [cabletiers/]: Loading config: /root/server/main/config/cabletiers-common.toml
[297月2025 17:03:53.338] [modloading-worker-0/INFO] [cabletiers/]: Built config: /root/server/main/config/cabletiers-common.toml
[297月2025 17:03:53.342] [modloading-worker-0/INFO] [cabletiers/]: Loaded config: /root/server/main/config/cabletiers-common.toml
[297月2025 17:03:53.379] [modloading-worker-0/INFO] [BrandonsCore/]: Knock Knock...
[297月2025 17:03:53.380] [modloading-worker-0/WARN] [draconicevolution/]: Reactor detonation initiated.
[297月2025 17:03:53.380] [modloading-worker-0/INFO] [BrandonsCore/]: Wait... NO! What?
[297月2025 17:03:53.380] [modloading-worker-0/INFO] [BrandonsCore/]: Stop That! That's not how this works!
[297月2025 17:03:53.380] [modloading-worker-0/WARN] [draconicevolution/]: Calculating explosion ETA
[297月2025 17:03:53.380] [modloading-worker-0/INFO] [BrandonsCore/]: Ahh... NO... NONONO! DONT DO THAT!!! STOP THIS NOW!
[297月2025 17:03:53.380] [modloading-worker-0/WARN] [draconicevolution/]: **Explosion Imminent!!!**
[297月2025 17:03:53.380] [modloading-worker-0/INFO] [BrandonsCore/]: Well...... fork...
[297月2025 17:03:53.380] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.enderio.conduits.common.integrations.refinedstorage.RSNodeHost RSAPI
[297月2025 17:03:53.380] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in org.cyclops.integrateddynamicscompat.modcompat.refinedstorage.aspect.RefinedStorageAspects RS
[297月2025 17:03:53.381] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.refinedmods.refinedstorageaddons.RSAddons RSAPI
[297月2025 17:03:53.414] [modloading-worker-0/INFO] [Mystical Agriculture/]: Registered plugin: com.blakebr0.mysticalagriculture.lib.ModCorePlugin
[297月2025 17:03:53.449] [modloading-worker-0/INFO] [Mystical Agriculture/]: Registered plugin: com.blakebr0.mysticalcustomization.lib.ModCorePlugin
[297月2025 17:03:53.454] [modloading-worker-0/INFO] [Mystical Agriculture/]: Registered plugin: com.blakebr0.mysticalagradditions.lib.ModCorePlugin
[297月2025 17:03:53.455] [modloading-worker-0/INFO] [cy.jdkdigital.dyenamicsandfriends.DyenamicsAndFriends/]: registerCompatBlocks
[297月2025 17:03:53.489] [modloading-worker-0/INFO] [net.minecraftforge.common.ForgeMod/FORGEMOD]: Forge mod loading, version 47.4.4, for MC 1.20.1 with MCP 20230612.114412
[297月2025 17:03:53.489] [modloading-worker-0/INFO] [net.minecraftforge.common.MinecraftForge/FORGE]: MinecraftForge v47.4.4 Initialized
[297月2025 17:03:53.493] [modloading-worker-0/INFO] [Mystical Agriculture/]: Loaded 3 plugins
[297月2025 17:03:53.495] [modloading-worker-0/WARN] [ironfurnaces.IronFurnaces/]: You have disabled Iron Furnaces's Update Checker, to re-enable: change the value of Update Checker in .minecraft->config->ironfurnaces-client.toml to 'true'.
[297月2025 17:03:53.499] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id powah:packet
[297月2025 17:03:53.530] [modloading-worker-0/INFO] [NoChatReports/]: KONNICHIWA ZA WARUDO!
[297月2025 17:03:53.530] [modloading-worker-0/INFO] [NoChatReports/]: Default JVM text encoding is: UTF-8
[297月2025 17:03:53.537] [modloading-worker-0/INFO] [NoChatReports/]: Reading config file NoChatReports/NCR-Common.json...
[297月2025 17:03:53.545] [modloading-worker-0/INFO] [NoChatReports/]: Writing config file NoChatReports/NCR-Common.json...
[297月2025 17:03:53.622] [Modding Legacy/blue_skies/Supporters thread/INFO] [ModdingLegacy/blue_skies/Supporter/]: Attempting to load the Modding Legacy supporters list from https://moddinglegacy.com/supporters-changelogs/supporters.txt
[297月2025 17:03:53.664] [modloading-worker-0/INFO] [de.melanx.aiotbotania.AIOTBotania/]: Items registered.
[297月2025 17:03:53.664] [modloading-worker-0/INFO] [de.melanx.aiotbotania.AIOTBotania/]: Blocks registered.
[297月2025 17:03:53.664] [modloading-worker-0/INFO] [de.melanx.aiotbotania.AIOTBotania/]: Global loot modifiers registered.
[297月2025 17:03:53.731] [modloading-worker-0/INFO] [plus.dragons.createdragonlib.DragonLib/]: Create: Dragon Lib 1.4.3 has initialized, ready to support your Create add-ons!
[297月2025 17:03:53.741] [modloading-worker-0/INFO] [Railways/]: Steam 'n' Rails v1.6.7 initializing! Commit hash: 0a7cc000d6c7a1419919bc3e315bc62932287eb9 on Create version: 0.5.1j on platform: Forge
[297月2025 17:03:53.772] [modloading-worker-0/INFO] [com.epherical.epherolib.config.CommonConfig/]: Creating default config file: croptopia_v3.conf
[297月2025 17:03:53.801] [modloading-worker-0/INFO] [thedarkcolour.kotlinforforge.test.KotlinForForge/]: Kotlin For Forge Enabled!
[297月2025 17:03:53.822] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:key_pressed
[297月2025 17:03:53.822] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:mode_changed
[297月2025 17:03:53.823] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:sync_config_to_server
[297月2025 17:03:53.831] [modloading-worker-0/INFO] [dev.ftb.mods.ftbultimine.FTBUltimine/]: FTB Ranks detected, listening for ranks events
[297月2025 17:03:53.837] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id findme:default/93a3df5fb37f371cb25c481f6e5b9323
[297月2025 17:03:53.839] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id findme:default/b71e26ec071d32928d9fb5d2c3ec8a7a
[297月2025 17:03:53.840] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id findme:default/1f516f929b313631ad555a7b42b1d39c
[297月2025 17:03:53.848] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftblibrary:edit_nbt_response
[297月2025 17:03:53.848] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.BetterChannel$PartialPacketBegin
[297月2025 17:03:53.852] [modloading-worker-0/INFO] [zeta/]: Doing super early config setup for zeta
[297月2025 17:03:53.870] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:open_gui
[297月2025 17:03:53.870] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:request_map_data
[297月2025 17:03:53.875] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:share_waypoint
[297月2025 17:03:53.876] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:update_settings
[297月2025 17:03:53.877] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/61a49a35c5283c0aaa7b0dfea5b7a53a
[297月2025 17:03:53.877] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.BetterChannel$PartialPacketBegin
[297月2025 17:03:53.878] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:send_message
[297月2025 17:03:53.878] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:request_chunk_change
[297月2025 17:03:53.878] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.BetterChannel$PartialPacketData
[297月2025 17:03:53.881] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:create_party
[297月2025 17:03:53.881] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:teleport_from_map
[297月2025 17:03:53.882] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:player_gui_operation
[297月2025 17:03:53.883] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:sync_tx
[297月2025 17:03:53.885] [modloading-worker-0/INFO] [Void Totem/]: Loading up Void Totem (Forge)
[297月2025 17:03:53.885] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/3dd73fd88be832559bc3a3c4a2e63b4a
[297月2025 17:03:53.885] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.BetterChannel$PartialPacketData
[297月2025 17:03:53.887] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.C2SPacket$InitTPSProfile
[297月2025 17:03:53.887] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:update_force_load_expiry
[297月2025 17:03:53.887] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:server_config_request
[297月2025 17:03:53.890] [modloading-worker-0/INFO] [Eidolon Repraised/]: Loaded [apotheosis] compatibility
[297月2025 17:03:53.890] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/b453c08f23b83b82b690c31bf8b2a45a
[297月2025 17:03:53.890] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.C2SPacket$InitTPSProfile
[297月2025 17:03:53.893] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.C2SPacket$RequestAvailability
[297月2025 17:03:53.897] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/0c662822629034678f5a8f0e70375315
[297月2025 17:03:53.897] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.C2SPacket$RequestAvailability
[297月2025 17:03:53.898] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilingStarted
[297月2025 17:03:53.905] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/2e815604a29b3a5cbfe0208e619d86c4
[297月2025 17:03:53.905] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilingStarted
[297月2025 17:03:53.906] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilingCompleted
[297月2025 17:03:53.910] [modloading-worker-0/INFO] [KubeJS/]: Loaded common.properties
[297月2025 17:03:53.910] [modloading-worker-0/INFO] [KubeJS/]: Loaded dev.properties
[297月2025 17:03:53.910] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/9ae83199b65a3768862799cc5e461a02
[297月2025 17:03:53.910] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilingCompleted
[297月2025 17:03:53.911] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilerInactive
[297月2025 17:03:53.916] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/ae8dfae1238e312397aa53e9d14c45ca
[297月2025 17:03:53.916] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilerInactive
[297月2025 17:03:53.917] [modloading-worker-0/INFO] [KubeJS/]: Looking for KubeJS plugins...
[297月2025 17:03:53.918] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilingResult
[297月2025 17:03:53.923] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source kubejs
[297月2025 17:03:53.924] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/3a94cae7336038df9152bcb7b4b94825
[297月2025 17:03:53.924] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilingResult
[297月2025 17:03:53.925] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$Availability
[297月2025 17:03:53.926] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id itemfilters:main/14e7fa1454283aec8ae811ef844ada28
[297月2025 17:03:53.930] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id itemfilters:main/8f6a899247753217b9d86ab427a2b279
[297月2025 17:03:53.933] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/c86a824e756d312396d1ba30e943e6aa
[297月2025 17:03:53.933] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.latvian.mods.kubejs.forge.BuiltinKubeJSForgeClientPlugin does not load on server side, skipping
[297月2025 17:03:53.933] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.latvian.mods.kubejs.integration.forge.gamestages.GameStagesIntegration does not have required mod gamestages loaded, skipping
[297月2025 17:03:53.933] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$Availability
[297月2025 17:03:53.933] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source ldlib
[297月2025 17:03:53.935] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source ftbxmodcompat
[297月2025 17:03:53.937] [modloading-worker-0/INFO] [Dungeon Crawl/]: Here we go! Launching Dungeon Crawl 2.3.15...
[297月2025 17:03:53.947] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.ftb.mods.ftbxmodcompat.ftbfiltersystem.kubejs.FFSKubeJSPlugin does not have required mod ftbfiltersystem loaded, skipping
[297月2025 17:03:53.951] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source gtceu
[297月2025 17:03:54.007] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:submit_task
[297月2025 17:03:54.008] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_reward
[297月2025 17:03:54.010] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:get_emergency_items
[297月2025 17:03:54.012] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_all_rewards
[297月2025 17:03:54.012] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_choice_reward
[297月2025 17:03:54.016] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_pinned
[297月2025 17:03:54.016] [modloading-worker-0/INFO] [GregTechCEu/]: GregTechCEu is initializing...
[297月2025 17:03:54.017] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_chapter_pinned
[297月2025 17:03:54.018] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_editing_mode
[297月2025 17:03:54.019] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:force_save
[297月2025 17:03:54.019] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:set_custom_image
[297月2025 17:03:54.027] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for titanium
[297月2025 17:03:54.029] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:task_screen_config_resp
[297月2025 17:03:54.029] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:change_progress
[297月2025 17:03:54.031] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:create_object
[297月2025 17:03:54.032] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:create_task_at
[297月2025 17:03:54.033] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:delete_object
[297月2025 17:03:54.035] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:edit_object
[297月2025 17:03:54.042] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_chapter
[297月2025 17:03:54.045] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_quest
[297月2025 17:03:54.046] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:change_chapter_group
[297月2025 17:03:54.048] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_chapter_group
[297月2025 17:03:54.054] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:copy_quest
[297月2025 17:03:54.055] [modloading-worker-0/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/root/server/main/libraries/net/minecraft/server/1.20.1-20230612.114412/server-1.20.1-20230612.114412-srg.jar%23963!/assets/.mcassetsroot' uses unexpected schema
[297月2025 17:03:54.055] [modloading-worker-0/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/root/server/main/libraries/net/minecraft/server/1.20.1-20230612.114412/server-1.20.1-20230612.114412-srg.jar%23963!/data/.mcassetsroot' uses unexpected schema
[297月2025 17:03:54.056] [modloading-worker-0/INFO] [Railways/]: Registered bogey styles from railways
[297月2025 17:03:54.062] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:copy_chapter_image
[297月2025 17:03:54.062] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:sync_structures_request
[297月2025 17:03:54.065] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:request_team_data
[297月2025 17:03:54.065] [modloading-worker-0/INFO] [com.Pdiddy973.AllTheCompressed.AllTheCompressed/]: Registering mod: allthecompressed
[297月2025 17:03:54.067] [modloading-worker-0/INFO] [quark/]: Initializing TerraBlender underground biome compat
[297月2025 17:03:54.080] [modloading-worker-0/INFO] [com.Pdiddy973.AllTheCompressed.AllTheCompressed/]: Registering overlays for loaded mods: [allthemodium, alltheores, allthetweaks, botania, enderio, minecraft, powah, productivebees, supplementaries]
[297月2025 17:03:54.080] [modloading-worker-0/INFO] [com.Pdiddy973.AllTheCompressed.AllTheCompressed/]: Skipping overlays for absent mods: []
[297月2025 17:03:54.082] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source createoreexcavation
[297月2025 17:03:54.083] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source morejs
[297月2025 17:03:54.084] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source cucumber
[297月2025 17:03:54.084] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source ponderjs
[297月2025 17:03:54.086] [modloading-worker-0/INFO] [KubeJS/]: Done in 168.1 ms
[297月2025 17:03:54.116] [modloading-worker-0/INFO] [Luminara/]: Luminara Mod 已加载
[297月2025 17:03:54.147] [modloading-worker-0/INFO] [dev.tonimatas.packetfixer.PacketFixer/]: Packet Fixer has been initialized successfully
[297月2025 17:03:54.147] [modloading-worker-0/INFO] [be.florens.expandability.ExpandAbility/]: ExpandAbility here, who dis?
[297月2025 17:03:54.149] [modloading-worker-0/INFO] [Luminara/]: Luminara 事件系统已注册
[297月2025 17:03:54.168] [modloading-worker-0/INFO] [STDOUT/]: TEST
[297月2025 17:03:54.169] [modloading-worker-0/INFO] [STDOUT/]: TEST2
[297月2025 17:03:54.184] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised items.
[297月2025 17:03:54.199] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised blocks.
[297月2025 17:03:54.201] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised block entities.
[297月2025 17:03:54.204] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/experimental/module/GameNerfsModule
[297月2025 17:03:54.205] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised Applied Botanics integration.
[297月2025 17:03:54.253] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised Applied Mekanistics integration.
[297月2025 17:03:54.307] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised Ars Énergistique integration.
[297月2025 17:03:54.345] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:03:54.345] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTreasureOnly() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:03:54.345] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTradeable() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:03:54.350] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for Mekanism has been loaded
[297月2025 17:03:54.350] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for Applied Mekanistics has been loaded
[297月2025 17:03:54.350] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for AE2 Things has been loaded
[297月2025 17:03:54.350] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for Applied Botanics has been loaded
[297月2025 17:03:54.350] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for FTB Teams has been loaded
[297月2025 17:03:54.350] [modloading-worker-0/INFO] [Railways/]: Registering data fixers
[297月2025 17:03:54.368] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Quests] Enabled KubeJS integration
[297月2025 17:03:54.373] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Chunks] Enabled KubeJS integration
[297月2025 17:03:54.375] [modloading-worker-0/INFO] [com.mojang.datafixers.DataFixerBuilder/]: 0 Datafixer optimizations took 1 milliseconds
[297月2025 17:03:54.380] [modloading-worker-0/INFO] [Configuration/FileWatching]: Registered gtceu config for auto-sync function
[297月2025 17:03:54.392] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Hex Casting
[297月2025 17:03:54.397] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Oh The Biomes You'll Go
[297月2025 17:03:54.397] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Blue Skies
[297月2025 17:03:54.418] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Twilight Forest
[297月2025 17:03:54.425] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Biomes O' Plenty
[297月2025 17:03:54.430] [modloading-worker-0/INFO] [GregTechCEu/]: High-Tier is Disabled.
[297月2025 17:03:54.431] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Nature's Spirit
[297月2025 17:03:54.432] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Dreams and Desires
[297月2025 17:03:54.433] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Quark
[297月2025 17:03:54.438] [modloading-worker-0/INFO] [Railways/]: Registering tracks for TerraFirmaCraft
[297月2025 17:03:54.469] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:t_corridor
[297月2025 17:03:54.489] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@1313acd3
[297月2025 17:03:54.489] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:four_way_corridor_loot
[297月2025 17:03:54.490] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@217dd5aa
[297月2025 17:03:54.490] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:four_way_corridor
[297月2025 17:03:54.491] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@31929b6e
[297月2025 17:03:54.491] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/ore_hold_1
[297月2025 17:03:54.491] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6a2b70cf
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:straight_corridor
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@246310b5
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:overlapped_corridor
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@71fb0053
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:spiral_staircase
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@4c3e61d7
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/water_way
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@4c1fb99c
[297月2025 17:03:54.492] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/mine_entrance
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@1faddce5
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/mine_key
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@bf2865
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/pit
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@48f40128
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/corner_zombie_trap
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@4d288925
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/split_road
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@656180d0
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/station
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@3b6f8f19
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/downward_tunnel
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@2f92a6b2
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/junction_station
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@9aad9a6
[297月2025 17:03:54.493] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/downward_shaft
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6056b2c6
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/nature_crossroad
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6a6568c1
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/wolf_den
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6775157
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/ore_cavern
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@2bf7f6de
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/straight_corridor
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@2833d9be
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/bent_corridor
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@d9cf04d
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/fourway_corridor
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6f5adbcc
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/library
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@20b25385
[297月2025 17:03:54.494] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/armoury
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6bcddf24
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/farm
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@5ddbe787
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/portal_nether
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6eaae07d
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/crypt
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@7229dc8a
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/challenge_tower
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@2431594a
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/big_library
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@1bd734b0
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_crane
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@11a25e13
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_library
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@58469db8
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_smithy
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@15ffd50a
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/tall_spiral
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@7b68cd4d
[297月2025 17:03:54.495] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_arena
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@4ae29ab7
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/antechamber
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@1f30eebd
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/destroyed_end_portal
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@78b8e4f9
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/four_way_corridor_loot
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@64caebea
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:t3_entrance
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@5da75d0a
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard_entrance
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@72960e83
[297月2025 17:03:54.496] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:default_deadend
[297月2025 17:03:54.497] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@2df00e46
[297月2025 17:03:54.497] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/deadend
[297月2025 17:03:54.497] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@10aef658
[297月2025 17:03:54.497] [modloading-worker-0/INFO] [STDOUT/]: # schematics: 42
[297月2025 17:03:54.506] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 5 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/module/AncientTomesModule
[297月2025 17:03:54.522] [modloading-worker-0/INFO] [quark-zeta/]: Discovered 164 modules to load.
[297月2025 17:03:54.522] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Abacus...
[297月2025 17:03:54.528] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Adjustable Chat...
[297月2025 17:03:54.528] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ambient Discs...
[297月2025 17:03:54.528] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ancient Tomes...
[297月2025 17:03:54.530] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 3 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/item/AncientTomeItem
[297月2025 17:03:54.536] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ancient Wood...
[297月2025 17:03:54.544] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Armed Armor Stands...
[297月2025 17:03:54.544] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Auto Walk Keybind...
[297月2025 17:03:54.545] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Automatic Recipe Unlock...
[297月2025 17:03:54.545] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Automatic Tool Restock...
[297月2025 17:03:54.546] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Azalea Wood...
[297月2025 17:03:54.546] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Back Button Keybind...
[297月2025 17:03:54.546] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Backpack...
[297月2025 17:03:54.549] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Beacon Redirection...
[297月2025 17:03:54.549] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Better Elytra Rocket...
[297月2025 17:03:54.549] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Big Stone Clusters...
[297月2025 17:03:54.558] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Blossom Trees...
[297月2025 17:03:54.560] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Bottled Cloud...
[297月2025 17:03:54.565] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Buckets Show Inhabitants...
[297月2025 17:03:54.565] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Camera...
[297月2025 17:03:54.573] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Campfires Boost Elytra...
[297月2025 17:03:54.573] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Celebratory Lamps...
[297月2025 17:03:54.573] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chains Connect Blocks...
[297月2025 17:03:54.574] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chest Searching...
[297月2025 17:03:54.574] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chorus Vegetation...
[297月2025 17:03:54.576] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chute...
[297月2025 17:03:54.577] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Climate Control Remover...
[297月2025 17:03:54.577] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Color Runes...
[297月2025 17:03:54.580] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Loading Rhino Minecraft remapper...
[297月2025 17:03:54.581] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Compasses Work Everywhere...
[297月2025 17:03:54.581] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Compressed Blocks...
[297月2025 17:03:54.582] [modloading-worker-0/INFO] [dev.latvian.mods.rhino.mod.util.RhinoProperties/]: Rhino properties loaded.
[297月2025 17:03:54.582] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Coral On Cactus...
[297月2025 17:03:54.582] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Corundum...
[297月2025 17:03:54.584] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Loading mappings for 1.20.1
[297月2025 17:03:54.596] [modloading-worker-0/INFO] [pneumaticcraft/]: Thirdparty integration activated for [cofh_core,immersiveengineering,computercraft,jei,botania,mekanism,curios,patchouli,create]
[297月2025 17:03:54.600] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crabs...
[297月2025 17:03:54.601] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crafter...
[297月2025 17:03:54.608] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crate...
[297月2025 17:03:54.609] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Diamond Repair...
[297月2025 17:03:54.610] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dispensers Place Blocks...
[297月2025 17:03:54.610] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Double Door Opening...
[297月2025 17:03:54.610] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dragon Scales...
[297月2025 17:03:54.611] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Duskbound Blocks...
[297月2025 17:03:54.611] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dyeable Item Frames...
[297月2025 17:03:54.612] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Easy Transfering...
[297月2025 17:03:54.612] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Elytra Indicator...
[297月2025 17:03:54.612] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Emotes...
[297月2025 17:03:54.612] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enchantment Predicates...
[297月2025 17:03:54.612] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enchantments Begone...
[297月2025 17:03:54.613] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ender Watcher...
[297月2025 17:03:54.613] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Endermosh Music Disc...
[297月2025 17:03:54.614] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enhanced Ladders...
[297月2025 17:03:54.614] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Expanded Item Interactions...
[297月2025 17:03:54.615] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Fairy Rings...
[297月2025 17:03:54.615] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Fallen Logs...
[297月2025 17:03:54.619] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Feeding Trough...
[297月2025 17:03:54.620] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Forgotten...
[297月2025 17:03:54.626] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Foxhound...
[297月2025 17:03:54.627] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Framed Glass...
[297月2025 17:03:54.627] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Game Nerfs...
[297月2025 17:03:54.627] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glass Item Frame...
[297月2025 17:03:54.629] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glass Shard...
[297月2025 17:03:54.629] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glimmering Weald...
[297月2025 17:03:54.642] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gold Bars...
[297月2025 17:03:54.642] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gold Tools Have Fortune...
[297月2025 17:03:54.642] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Grab Chickens...
[297月2025 17:03:54.643] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Grate...
[297月2025 17:03:54.652] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Done in 0.072 s
[297月2025 17:03:54.656] [modloading-worker-0/WARN] [mixin/]: Method overwrite conflict for getPalettes in immersiveengineering.mixins.json:accessors.TemplateAccess, previously written by com.almostreliable.morejs.mixin.structure.StructureTemplateMixin. Skipping method.
[297月2025 17:03:54.658] [modloading-worker-0/WARN] [mixin/]: Method overwrite conflict for getPalettes in ars_nouveau.mixins.json:structure.StructureTemplateAccessor, previously written by com.almostreliable.morejs.mixin.structure.StructureTemplateMixin. Skipping method.
[297月2025 17:03:54.658] [modloading-worker-0/WARN] [mixin/]: Method overwrite conflict for getPalettes in structure_gel.mixins.json:StructureTemplateAccessor, previously written by com.almostreliable.morejs.mixin.structure.StructureTemplateMixin. Skipping method.
[297月2025 17:03:54.660] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gravisand...
[297月2025 17:03:54.661] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Greener Grass...
[297月2025 17:03:54.663] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hedges...
[297月2025 17:03:54.664] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hoe Harvesting...
[297月2025 17:03:54.664] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hollow Logs...
[297月2025 17:03:54.664] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Horses Swim...
[297月2025 17:03:54.664] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hotbar Changer...
[297月2025 17:03:54.665] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Improved Sponges...
[297月2025 17:03:54.665] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Improved Tooltips...
[297月2025 17:03:54.665] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Industrial Palette...
[297月2025 17:03:54.665] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Inventory Sorting...
[297月2025 17:03:54.665] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Iron Rod...
[297月2025 17:03:54.669] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Item Sharing...
[297月2025 17:03:54.669] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Japanese Palette...
[297月2025 17:03:54.669] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Leaf Carpet...
[297月2025 17:03:54.670] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Lock Rotation...
[297月2025 17:03:54.670] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Long Range Pick Block...
[297月2025 17:03:54.670] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Magma Keeps Concrete Powder...
[297月2025 17:03:54.670] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Magnets...
[297月2025 17:03:54.691] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Map Washing...
[297月2025 17:03:54.691] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Matrix Enchanting...
[297月2025 17:03:54.695] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Metal Buttons...
[297月2025 17:03:54.695] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Microcrafting Helper...
[297月2025 17:03:54.695] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Midori...
[297月2025 17:03:54.695] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Monster Box...
[297月2025 17:03:54.695] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Banner Layers...
[297月2025 17:03:54.695] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Brick Types...
[297月2025 17:03:54.696] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Mud Blocks...
[297月2025 17:03:54.696] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Note Block Sounds...
[297月2025 17:03:54.696] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Potted Plants...
[297月2025 17:03:54.696] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Villagers...
[297月2025 17:03:54.696] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Narrator Readout...
[297月2025 17:03:54.696] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Nether Brick Fence Gate...
[297月2025 17:03:54.696] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Nether Obsidian Spikes...
[297月2025 17:03:54.697] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module New Stone Types...
[297月2025 17:03:54.698] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module No Durability On Cosmetics...
[297月2025 17:03:54.698] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module No More Lava Pockets...
[297月2025 17:03:54.698] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Obsidian Plate...
[297月2025 17:03:54.698] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Overlay Shader...
[297月2025 17:03:54.699] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Parrot Eggs...
[297月2025 17:03:54.699] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pat The Dogs...
[297月2025 17:03:54.700] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pathfinder Maps...
[297月2025 17:03:54.701] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Permafrost...
[297月2025 17:03:54.701] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Petals On Water...
[297月2025 17:03:54.702] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pickarang...
[297月2025 17:03:54.703] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pig Litters...
[297月2025 17:03:54.703] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pipes...
[297月2025 17:03:54.705] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pistons Move Tile Entities...
[297月2025 17:03:54.705] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Poison Potato Usage...
[297月2025 17:03:54.705] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Quick Armor Swapping...
[297月2025 17:03:54.705] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Rainbow Lamps...
[297月2025 17:03:54.705] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Raw Metal Bricks...
[297月2025 17:03:54.706] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Reacharound Placing...
[297月2025 17:03:54.706] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Redstone Randomizer...
[297月2025 17:03:54.707] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Renewable Spore Blossoms...
[297月2025 17:03:54.707] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Replace Scaffolding...
[297月2025 17:03:54.707] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Rope...
[297月2025 17:03:54.709] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Safer Creatures...
[297月2025 17:03:54.710] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Seed Pouch...
[297月2025 17:03:54.716] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shear Vines...
[297月2025 17:03:54.725] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shiba...
[297月2025 17:03:54.726] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shingles...
[297月2025 17:03:54.726] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shulker Packing...
[297月2025 17:03:54.726] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Simple Harvest...
[297月2025 17:03:54.727] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Skull Pikes...
[297月2025 17:03:54.731] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slabs To Blocks...
[297月2025 17:03:54.732] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slime In A Bucket...
[297月2025 17:03:54.734] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slimes To Magma Cubes...
[297月2025 17:03:54.734] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Snow Golem Player Heads...
[297月2025 17:03:54.734] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Soul Candles...
[297月2025 17:03:54.734] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Soul Sandstone...
[297月2025 17:03:54.734] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Spawner Replacer...
[297月2025 17:03:54.735] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Spiral Spires...
[297月2025 17:03:54.737] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Stonelings...
[297月2025 17:03:54.738] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Stools...
[297月2025 17:03:54.738] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Sturdy Stone...
[297月2025 17:03:54.739] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Thatch...
[297月2025 17:03:54.739] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Tiny Potato...
[297月2025 17:03:54.740] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Torch Arrow...
[297月2025 17:03:54.741] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Toretoise...
[297月2025 17:03:54.742] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Totem Of Holding...
[297月2025 17:03:54.742] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Trowel...
[297月2025 17:03:54.743] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Usage Ticker...
[297月2025 17:03:54.743] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Uses For Curses...
[297月2025 17:03:54.743] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Utility Recipes...
[297月2025 17:03:54.743] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Animal Textures...
[297月2025 17:03:54.744] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Bookshelves...
[297月2025 17:03:54.744] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Chests...
[297月2025 17:03:54.749] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Furnaces...
[297月2025 17:03:54.753] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Ladders...
[297月2025 17:03:54.753] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Selector...
[297月2025 17:03:54.754] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vertical Planks...
[297月2025 17:03:54.754] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vertical Slabs...
[297月2025 17:03:54.761] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vexes Die With Their Masters...
[297月2025 17:03:54.761] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Villager Rerolling Rework...
[297月2025 17:03:54.765] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Villagers Follow Emeralds...
[297月2025 17:03:54.765] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wooden Posts...
[297月2025 17:03:54.766] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wool Shuts Up Minecarts...
[297月2025 17:03:54.766] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wraith...
[297月2025 17:03:54.767] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Zombie Villagers On Normal...
[297月2025 17:03:54.767] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Stone Variants...
[297月2025 17:03:54.767] [modloading-worker-0/INFO] [quark-zeta/]: Constructed 164 modules.
[297月2025 17:03:54.792] [modloading-worker-0/INFO] [quark-zeta/]: Doing super early config setup for quark
[297月2025 17:03:54.811] [modloading-worker-0/INFO] [mixin/]: Mixing common.MixinServerStatus from mixins/common/nochatreports.mixins.json into net.minecraft.network.protocol.status.ServerStatus
[297月2025 17:03:54.869] [modloading-worker-0/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/gui/GuiGraphics for invalid dist DEDICATED_SERVER
[297月2025 17:03:54.869] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.lowdragmc.lowdraglib.gui.widget.PhantomFluidWidget: java.lang.RuntimeException: Attempted to load class net/minecraft/client/gui/GuiGraphics for invalid dist DEDICATED_SERVER
[297月2025 17:03:54.873] [modloading-worker-0/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class com/lowdragmc/lowdraglib/client/scene/WorldSceneRenderer for invalid dist DEDICATED_SERVER
[297月2025 17:03:54.873] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.lowdragmc.lowdraglib.gui.widget.SceneWidget: java.lang.RuntimeException: Attempted to load class com/lowdragmc/lowdraglib/client/scene/WorldSceneRenderer for invalid dist DEDICATED_SERVER
[297月2025 17:03:54.918] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.gregtechceu.gtceu.common.data.GTMaterialBlocks: java.lang.NoClassDefFoundError: net/minecraft/client/color/item/ItemColor
[297月2025 17:03:54.929] [modloading-worker-0/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/multiplayer/ClientLevel for invalid dist DEDICATED_SERVER
[297月2025 17:03:54.929] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.gregtechceu.gtceu.common.data.GTItems: java.lang.RuntimeException: Attempted to load class net/minecraft/client/multiplayer/ClientLevel for invalid dist DEDICATED_SERVER
[297月2025 17:03:54.932] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.gregtechceu.gtceu.common.data.GTMaterialItems: java.lang.NoClassDefFoundError: net/minecraft/client/color/item/ItemColor
[297月2025 17:03:55.003] [modloading-worker-0/INFO] [KubeJS/]: Added bindings for script type STARTUP from mod 'almostunified': [AlmostUnified]
[297月2025 17:03:55.030] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#5: Loaded Java class 'net.minecraftforge.resource.ResourcePackLoader'
[297月2025 17:03:55.030] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#8: Loaded Java class 'java.util.stream.Collectors'
[297月2025 17:03:55.031] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#9: Loaded Java class 'net.minecraft.server.packs.repository.ServerPacksSource'
[297月2025 17:03:55.032] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#10: Loaded Java class 'net.minecraft.server.packs.resources.FallbackResourceManager'
[297月2025 17:03:55.033] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:read_json_from_mod.js in 0.029 s
[297月2025 17:03:55.035] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:worldgen.js in 0.002 s
[297月2025 17:03:55.036] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:AE2/Universal_Press.js in 0.001 s
[297月2025 17:03:55.038] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#18: Loaded Java class 'mekanism.api.chemical.slurry.Slurry'
[297月2025 17:03:55.038] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#19: Loaded Java class 'mekanism.api.chemical.slurry.SlurryBuilder'
[297月2025 17:03:55.038] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#20: Loaded Java class 'mekanism.api.chemical.gas.Gas'
[297月2025 17:03:55.038] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#21: Loaded Java class 'mekanism.api.chemical.gas.GasBuilder'
[297月2025 17:03:55.038] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:mekanismStartup.js in 0.002 s
[297月2025 17:03:55.040] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/greenhouse.js#4: Loaded Java class 'dev.latvian.mods.kubejs.util.Tags'
[297月2025 17:03:55.040] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/greenhouse.js in 0.002 s
[297月2025 17:03:55.041] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/alcr.js in 0.001 s
[297月2025 17:03:55.042] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/miner.js in 0.001 s
[297月2025 17:03:55.065] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/mega_fusion_reactor.js#4: Loaded Java class 'com.gregtechceu.gtceu.common.machine.multiblock.electric.FusionReactorMachine'
[297月2025 17:03:55.066] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/mega_fusion_reactor.js in 0.023 s
[297月2025 17:03:55.068] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/gregstar_placeholders.js#4: Loaded Java class 'com.gregtechceu.gtceu.common.machine.multiblock.part.RotorHolderPartMachine'
[297月2025 17:03:55.068] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/gregstar_placeholders.js in 0.002 s
[297月2025 17:03:55.070] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#4: Loaded Java class 'com.gregtechceu.gtceu.api.data.chemical.material.properties.FluidProperty'
[297月2025 17:03:55.071] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#5: Loaded Java class 'com.gregtechceu.gtceu.api.data.chemical.material.properties.OreProperty'
[297月2025 17:03:55.071] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#6: Loaded Java class 'com.gregtechceu.gtceu.api.fluids.FluidBuilder'
[297月2025 17:03:55.073] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#7: Loaded Java class 'com.gregtechceu.gtceu.api.fluids.store.FluidStorageKeys'
[297月2025 17:03:55.074] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/material_modification.js in 0.006 s
[297月2025 17:03:55.075] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/starforge.js in 0.001 s
[297月2025 17:03:55.075] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/neocube.js in 0.0 s
[297月2025 17:03:55.078] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/micro_universe_orb.js#4: Loaded Java class 'com.gregtechceu.gtceu.common.machine.multiblock.part.EnergyHatchPartMachine'
[297月2025 17:03:55.079] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/micro_universe_orb.js#5: Loaded Java class 'com.gregtechceu.gtceu.api.capability.recipe.IO'
[297月2025 17:03:55.079] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/micro_universe_orb.js in 0.004 s
[297月2025 17:03:55.084] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/ore_processing_plant.js#4: Loaded Java class 'net.minecraft.sounds.SoundEvents'
[297月2025 17:03:55.085] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/ore_processing_plant.js#5: Loaded Java class 'net.minecraft.sounds.SoundSource'
[297月2025 17:03:55.085] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/ore_processing_plant.js#6: Loaded Java class 'com.gregtechceu.gtceu.api.sound.ExistingSoundEntry'
[297月2025 17:03:55.085] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/ore_processing_plant.js in 0.006 s
[297月2025 17:03:55.086] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/neural_node.js in 0.001 s
[297月2025 17:03:55.086] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/apiary.js in 0.0 s
[297月2025 17:03:55.087] [modloading-worker-0/INFO] [KubeJS Startup/]: mysticalagriculture.js#4: Loaded Java class 'com.blakebr0.mysticalagriculture.api.MysticalAgricultureAPI'
[297月2025 17:03:55.087] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:mysticalagriculture.js in 0.001 s
[297月2025 17:03:55.089] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:settings.js in 0.002 s
[297月2025 17:03:55.090] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:mining_dim_layers.js in 0.001 s
[297月2025 17:03:55.091] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:custom_additions.js in 0.001 s
[297月2025 17:03:55.092] [modloading-worker-0/INFO] [KubeJS Startup/]: farmingForBlockheads.js#11: Loaded Java class 'net.blay09.mods.farmingforblockheads.api.FarmingForBlockheadsAPI'
[297月2025 17:03:55.093] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:farmingForBlockheads.js in 0.002 s
[297月2025 17:03:55.095] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded 21/21 KubeJS startup scripts in 0.441 s with 0 errors and 0 warnings
[297月2025 17:03:55.121] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id kubejs:send_data_from_client
[297月2025 17:03:55.122] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id kubejs:first_click
[297月2025 17:03:55.126] [main/INFO] [GregTechCEu/]: GTCEu common proxy init!
[297月2025 17:03:55.147] [main/INFO] [GregTechCEu/]: Registering material registries
[297月2025 17:03:55.148] [main/INFO] [GregTechCEu/]: Registering GTCEu Materials
[297月2025 17:03:55.198] [main/INFO] [GregTechCEu/]: Registering addon Materials
[297月2025 17:03:55.236] [main/WARN] [GregTechCEu/]: FluidStorageKey{gtceu:liquid} already has an associated fluid for material gtceu:water
[297月2025 17:03:55.246] [main/WARN] [GregTechCEu/]: FluidStorageKey{gtceu:liquid} already has an associated fluid for material gtceu:lava
[297月2025 17:03:55.246] [main/WARN] [GregTechCEu/]: FluidStorageKey{gtceu:liquid} already has an associated fluid for material gtceu:milk
[297月2025 17:03:55.497] [main/ERROR] [KubeJS Startup/]: gtceu/alcr.js#51: Error in 'GTCEuStartupEvents.registry': TypeError: Cannot find function workableCasingRenderer in object com.gregtechceu.gtceu.api.registry.registrate.MultiblockMachineBuilder@7ea073d2.
[297月2025 17:03:55.594] [main/INFO] [GregTechCEu/]: Registering KeyBinds
[297月2025 17:03:56.039] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:03:56.043] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/loot/EnchantTome
[297月2025 17:03:56.864] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:03:57.657] [main/INFO] [Potion-Blender/]: Loaded config
[297月2025 17:03:58.490] [Voidscape Donator Loader/INFO] [voidscape/]: Donor data loaded
[297月2025 17:04:10.207] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry minecraft:menu: The object net.minecraft.world.inventory.MenuType@52ededef has been registered twice for the same name ae2:export_card.
[297月2025 17:04:10.207] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry minecraft:menu: The object net.minecraft.world.inventory.MenuType@1dc3e33b has been registered twice for the same name ae2:insert_card.
[297月2025 17:04:10.214] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:10.238] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry minecraft:menu: The object net.minecraft.world.inventory.MenuType@396763e3 has been registered twice for the same name ae2:portable_fe_cell.
[297月2025 17:04:10.477] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry minecraft:menu: The object net.minecraft.world.inventory.MenuType@6e6c401e has been registered twice for the same name ae2:portable_mana_cell.
[297月2025 17:04:10.568] [main/INFO] [Moonlight/]: Initialized block sets in 60ms
[297月2025 17:04:10.568] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:10.602] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:10.651] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:12.005] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:12.012] [main/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ae2wtlib:cycle_terminal
[297月2025 17:04:12.677] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:12.694] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:12.705] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:12.714] [main/INFO] [evilcraft/]: 2205 possible Broom base combinations are ready for flying!
[297月2025 17:04:12.725] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:12.735] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:12.755] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.037] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.073] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.082] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.091] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.097] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.104] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.111] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.120] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.229] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.259] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.415] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.427] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.444] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.452] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.467] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.488] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.497] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.504] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.511] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.532] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.545] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.552] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.557] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.562] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.568] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.574] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.579] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.583] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.592] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.650] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.664] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.673] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.690] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.701] [Modding Legacy/blue_skies/Supporters thread/INFO] [ModdingLegacy/blue_skies/Supporter/]: Couldn't load the Modding Legacy supporters list. You may be offline or our website could be having issues. If you are a supporter, some cosmetic features may not work.
java.net.ConnectException: 连接超时
	at sun.nio.ch.Net.connect0(Native Method) ~[?:?] {}
	at sun.nio.ch.Net.connect(Net.java:589) ~[?:?] {}
	at sun.nio.ch.Net.connect(Net.java:578) ~[?:?] {}
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:583) ~[?:?] {}
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?] {}
	at java.net.Socket.connect(Socket.java:751) ~[?:?] {}
	at sun.security.ssl.SSLSocketImpl.connect(SSLSocketImpl.java:304) ~[?:?] {}
	at sun.security.ssl.BaseSSLSocketImpl.connect(BaseSSLSocketImpl.java:181) ~[?:?] {}
	at sun.net.NetworkClient.doConnect(NetworkClient.java:183) ~[?:?] {}
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:531) ~[?:?] {}
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:636) ~[?:?] {}
	at sun.net.www.protocol.https.HttpsClient.<init>(HttpsClient.java:264) ~[?:?] {}
	at sun.net.www.protocol.https.HttpsClient.New(HttpsClient.java:377) ~[?:?] {}
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.getNewHttpClient(AbstractDelegateHttpsURLConnection.java:193) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1252) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1138) ~[?:?] {}
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:179) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.followRedirect0(HttpURLConnection.java:2924) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.followRedirect(HttpURLConnection.java:2833) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1944) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1614) ~[?:?] {}
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getInputStream(HttpsURLConnectionImpl.java:223) ~[?:?] {}
	at com.legacy.blue_skies.MLSupporter$GetSupportersThread.run(MLSupporter.java:165) ~[blue_skies-1.20.1-1.3.31.jar%23621!/:1.3.31] {re:classloading}
[297月2025 17:04:13.704] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.714] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.723] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.734] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.746] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.754] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.769] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.778] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.794] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.806] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.813] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.822] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.830] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.836] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.840] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.843] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.846] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.849] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.852] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.902] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.916] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.926] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.935] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.941] [main/INFO] [STDOUT/]: Attempting to load Anointment: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/anointment/melee_damage.json, path: /data/bloodmagic/anointment/melee_damage.json
[297月2025 17:04:13.946] [main/INFO] [STDOUT/]: Attempting to load Anointment: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/anointment/holy_water.json, path: /data/bloodmagic/anointment/holy_water.json
[297月2025 17:04:13.946] [main/INFO] [STDOUT/]: Attempting to load Anointment: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/anointment/hidden_knowledge.json, path: /data/bloodmagic/anointment/hidden_knowledge.json
[297月2025 17:04:13.946] [main/INFO] [STDOUT/]: Attempting to load Anointment: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/anointment/quick_draw.json, path: /data/bloodmagic/anointment/quick_draw.json
[297月2025 17:04:13.946] [main/INFO] [STDOUT/]: Attempting to load Anointment: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/anointment/bow_power.json, path: /data/bloodmagic/anointment/bow_power.json
[297月2025 17:04:13.946] [main/INFO] [STDOUT/]: Attempting to load Anointment: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/anointment/bow_velocity.json, path: /data/bloodmagic/anointment/bow_velocity.json
[297月2025 17:04:13.946] [main/INFO] [STDOUT/]: Attempting to load Anointment: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/anointment/repairing.json, path: /data/bloodmagic/anointment/repairing.json
[297月2025 17:04:13.946] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.952] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.957] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/arrow_protect.json, path: /data/bloodmagic/living_armor/arrow_protect.json
[297月2025 17:04:13.961] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/fall_protect.json, path: /data/bloodmagic/living_armor/fall_protect.json
[297月2025 17:04:13.961] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/physical_protect.json, path: /data/bloodmagic/living_armor/physical_protect.json
[297月2025 17:04:13.961] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/jump.json, path: /data/bloodmagic/living_armor/jump.json
[297月2025 17:04:13.961] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/health.json, path: /data/bloodmagic/living_armor/health.json
[297月2025 17:04:13.961] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/experienced.json, path: /data/bloodmagic/living_armor/experienced.json
[297月2025 17:04:13.961] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/sprint_attack.json, path: /data/bloodmagic/living_armor/sprint_attack.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/self_sacrifice.json, path: /data/bloodmagic/living_armor/self_sacrifice.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/speed.json, path: /data/bloodmagic/living_armor/speed.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/poison_resist.json, path: /data/bloodmagic/living_armor/poison_resist.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/fire_resist.json, path: /data/bloodmagic/living_armor/fire_resist.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/digging.json, path: /data/bloodmagic/living_armor/digging.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/knockback_resist.json, path: /data/bloodmagic/living_armor/knockback_resist.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/diamond_protect.json, path: /data/bloodmagic/living_armor/diamond_protect.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/elytra.json, path: /data/bloodmagic/living_armor/elytra.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/curios_socket.json, path: /data/bloodmagic/living_armor/curios_socket.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/melee_damage.json, path: /data/bloodmagic/living_armor/melee_damage.json
[297月2025 17:04:13.962] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/repair.json, path: /data/bloodmagic/living_armor/repair.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/gilded.json, path: /data/bloodmagic/living_armor/gilded.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/quenched.json, path: /data/bloodmagic/living_armor/downgrade/quenched.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/storm_trooper.json, path: /data/bloodmagic/living_armor/downgrade/storm_trooper.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/battle_hungry.json, path: /data/bloodmagic/living_armor/downgrade/battle_hungry.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/melee_decrease.json, path: /data/bloodmagic/living_armor/downgrade/melee_decrease.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/dig_slowdown.json, path: /data/bloodmagic/living_armor/downgrade/dig_slowdown.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/slow_heal.json, path: /data/bloodmagic/living_armor/downgrade/slow_heal.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/crippled_arm.json, path: /data/bloodmagic/living_armor/downgrade/crippled_arm.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/swim_decrease.json, path: /data/bloodmagic/living_armor/downgrade/swim_decrease.json
[297月2025 17:04:13.963] [main/INFO] [STDOUT/]: Attempting to load Living Armour Upgrade: union:/root/server/main/mods/bloodmagic-1.20.1-3.3.3-45.jar%23620!/data/bloodmagic/living_armor/downgrade/speed_decrease.json, path: /data/bloodmagic/living_armor/downgrade/speed_decrease.json
[297月2025 17:04:13.964] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.974] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.983] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:13.993] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.002] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.013] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.022] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.030] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.036] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.057] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.070] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.079] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.088] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.097] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.107] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.116] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.128] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.137] [main/INFO] [ModdingLegacy/StructureGel/]: [structure_gel] Registering data for structure_gel:loot_table_alias
[297月2025 17:04:14.142] [main/INFO] [ModdingLegacy/StructureGel/]: [structure_gel] Registering data for structure_gel:data_handler_type
[297月2025 17:04:14.143] [main/INFO] [ModdingLegacy/StructureGel/]: [structure_gel] Registering data for structure_gel:dynamic_spawner
[297月2025 17:04:14.144] [main/INFO] [ModdingLegacy/StructureGel/]: [structure_gel] Registering data for structure_gel:jigsaw_type
[297月2025 17:04:14.157] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.166] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.177] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.190] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.213] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry forge:global_loot_modifier_serializers: The object RecordCodec[UnitDecoder[vectorwing.farmersdelight.common.loot.modifier.AddItemModifier$$Lambda/0x000000008681b670@584155c2] * Field[conditions: passthrough[flatXmapped]] * Field[item: net.minecraftforge.registries.ForgeRegistry$RegistryCodec@39389209] * OptionalFieldCodec[count: Int][xmapped]] has been registered twice for the same name farmersdelight:add_item.
[297月2025 17:04:14.213] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry forge:global_loot_modifier_serializers: The object RecordCodec[UnitDecoder[vectorwing.farmersdelight.common.loot.modifier.ReplaceItemModifier$$Lambda/0x000000008681c160@1ad3345e] * Field[conditions: passthrough[flatXmapped]] * Field[removed_item: net.minecraftforge.registries.ForgeRegistry$RegistryCodec@39389209] * Field[added_item: net.minecraftforge.registries.ForgeRegistry$RegistryCodec@39389209] * OptionalFieldCodec[count: Int][xmapped]] has been registered twice for the same name farmersdelight:replace_item.
[297月2025 17:04:14.213] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry forge:global_loot_modifier_serializers: The object RecordCodec[UnitDecoder[vectorwing.farmersdelight.common.loot.modifier.AddLootTableModifier$$Lambda/0x000000008681c800@4fa73fd6] * Field[conditions: passthrough[flatXmapped]] * Field[lootTable: String[comapFlatMapped]]] has been registered twice for the same name farmersdelight:add_loot_table.
[297月2025 17:04:14.213] [main/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry forge:global_loot_modifier_serializers: The object RecordCodec[UnitDecoder[vectorwing.farmersdelight.common.loot.modifier.PastrySlicingModifier$$Lambda/0x000000008681ce80@4029f205] * Field[conditions: passthrough[flatXmapped]] * Field[slice: net.minecraftforge.registries.ForgeRegistry$RegistryCodec@39389209]] has been registered twice for the same name farmersdelight:pastry_slicing.
[297月2025 17:04:14.215] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.227] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.236] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.242] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.247] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.251] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.255] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.258] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.263] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.269] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.276] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.282] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.287] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.293] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.297] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.300] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.303] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.308] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.314] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.358] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.367] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.376] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.383] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.388] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.395] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.400] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.405] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.413] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.435] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.445] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.455] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.462] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.471] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.479] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.487] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.493] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.500] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.507] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.516] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.529] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.538] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.545] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.550] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.556] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.562] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.568] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.575] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.581] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.586] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.593] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.599] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.604] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.608] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.615] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.622] [main/INFO] [uk.co.hexeption.aeinfinitybooster.AEInfinityBooster/]: Creating Creative Mode Tab
[297月2025 17:04:14.645] [main/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class com.klikli_dev.occultism.common.entity.spirit.DjinniEntity from class com.klikli_dev.occultism.common.entity.spirit.FoliotEntity
[297月2025 17:04:14.688] [Thread-7/WARN] [com.mrbysco.structurecompass.StructureCompass/]: Structure Compass' config just got changed on the file system!
[297月2025 17:04:14.689] [Thread-7/WARN] [com.mrbysco.structurecompass.StructureCompass/]: Structure Compass' config just got changed on the file system!
[297月2025 17:04:14.749] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.749] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class CuriosPlugin
[297月2025 17:04:14.749] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.749] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class CuriosPlugin
[297月2025 17:04:14.749] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.749] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class CuriosPlugin
[297月2025 17:04:14.749] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.749] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class CuriosPlugin
[297月2025 17:04:14.754] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.754] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class CuriosPlugin
[297月2025 17:04:14.754] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.754] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class CuriosPlugin
[297月2025 17:04:14.754] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.754] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class CuriosPlugin
[297月2025 17:04:14.754] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.754] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class CuriosPlugin
[297月2025 17:04:14.754] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.754] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class CuriosPlugin
[297月2025 17:04:14.755] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.755] [main/INFO] [PluginManager/]: Executing phase CONFIG_LOAD for plugin class CuriosPlugin
[297月2025 17:04:14.755] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class PatchouliPlugin
[297月2025 17:04:14.755] [Thread-7/INFO] [PluginManager/]: Executing phase CONFIG_RELOAD for plugin class CuriosPlugin
[297月2025 17:04:14.765] [main/INFO] [com.tom.createores.CreateOreExcavation/]: Loaded Create Ore Excavation config file createoreexcavation-common.toml
[297月2025 17:04:14.783] [Thread-7/INFO] [zeta/]: About to refresh zeta's config, looking for better thread than 'Thread-7'...
[297月2025 17:04:14.784] [Thread-7/WARN] [zeta/]: Using thread 'Thread-7' instead of the server thread
[297月2025 17:04:14.784] [Thread-7/INFO] [zeta/]: zeta's config is 'BEFORE_INIT', ignoring config refresh. Current thread: Thread-7
[297月2025 17:04:14.784] [Thread-7/INFO] [zeta/]: About to refresh zeta's config, looking for better thread than 'Thread-7'...
[297月2025 17:04:14.784] [Thread-7/WARN] [zeta/]: Using thread 'Thread-7' instead of the server thread
[297月2025 17:04:14.784] [Thread-7/INFO] [zeta/]: zeta's config is 'BEFORE_INIT', ignoring config refresh. Current thread: Thread-7
[297月2025 17:04:14.799] [Thread-7/INFO] [quark-zeta/]: About to refresh quark's config, looking for better thread than 'Thread-7'...
[297月2025 17:04:14.799] [Thread-7/WARN] [quark-zeta/]: Using thread 'Thread-7' instead of the server thread
[297月2025 17:04:14.799] [Thread-7/INFO] [quark-zeta/]: quark's config is 'BEFORE_INIT', ignoring config refresh. Current thread: Thread-7
[297月2025 17:04:14.801] [modloading-worker-0/INFO] [PluginManager/]: Executing phase COMMON_SETUP for plugin class PatchouliPlugin
[297月2025 17:04:14.801] [modloading-worker-0/INFO] [PluginManager/]: Executing phase COMMON_SETUP for plugin class CuriosPlugin
[297月2025 17:04:14.801] [modloading-worker-0/INFO] [thetadev.constructionwand.ConstructionWand/]: ConstructionWand says hello - may the odds be ever in your favor.
[297月2025 17:04:14.802] [modloading-worker-0/INFO] [PluginManager/]: Executing phase COMMON_SETUP for plugin class PatchouliPlugin
[297月2025 17:04:14.806] [modloading-worker-0/INFO] [CorgiLib/]: Initializing network...
[297月2025 17:04:14.806] [modloading-worker-0/INFO] [Nature's Aura/]: Loading compat module for mod patchouli
[297月2025 17:04:14.806] [modloading-worker-0/INFO] [Nature's Aura/]: Loading compat module for mod curios
[297月2025 17:04:14.815] [modloading-worker-0/INFO] [CorgiLib/]: Initialized network!
[297月2025 17:04:14.817] [modloading-worker-0/INFO] [lostcities/]: Creating standard profiles into 'config/lostcities/profiles'
[297月2025 17:04:14.819] [modloading-worker-0/INFO] [thetadev.constructionwand.ConstructionWand/]: Botania integration added
[297月2025 17:04:14.822] [modloading-worker-0/INFO] [cn.mcmod_mmf.mmlib.Main/]: Presented by Zaia
[297月2025 17:04:14.823] [modloading-worker-0/INFO] [mythicbotany/]: Loading MythicBotany
[297月2025 17:04:14.826] [modloading-worker-0/INFO] [Modular Routers/]: Modular Routers is loading!
[297月2025 17:04:14.829] [modloading-worker-0/INFO] [integrateddynamics/]: Registered packet handler.
[297月2025 17:04:14.832] [modloading-worker-0/INFO] [com.maideniles.maidensmerrymaking.MaidensMerryMaking/]: HELLO FROM COMMON SETUP
[297月2025 17:04:14.839] [modloading-worker-0/INFO] [lostcities/]: Reading existing profiles from 'config/lostcities/profiles'
[297月2025 17:04:14.842] [modloading-worker-0/INFO] [mekanism.common.Mekanism/]: Version 10.4.16 initializing...
[297月2025 17:04:14.843] [modloading-worker-0/INFO] [giselle.gmut.GravitationalModulatingUnitTweaks/]: Version 3.4.0 initializing...
[297月2025 17:04:14.845] [modloading-worker-0/INFO] [pneumaticcraft/]: PneumaticCraft: Repressurized is loading!
[297月2025 17:04:14.850] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: Global Forge version check system disabled, no further processing.
[297月2025 17:04:14.867] [modloading-worker-0/WARN] [mixin/]: @Redirect conflict. Skipping securitycraft.mixins.json:camera.ChunkMapMixin->@Redirect::securitycraft$getCameraSectionPos(Lnet/minecraft/server/level/ServerPlayer;)Lnet/minecraft/core/SectionPos; with priority 1100, already redirected by railways-common.mixins.json:conductor_possession.ChunkMapMixin->@Redirect::securitycraft$getCameraSectionPos(Lnet/minecraft/server/level/ServerPlayer;)Lnet/minecraft/core/SectionPos; with priority 1200
[297月2025 17:04:14.871] [modloading-worker-0/INFO] [Mystical Agriculture/]: Loaded 147 crops
[297月2025 17:04:14.871] [modloading-worker-0/INFO] [Mystical Agriculture/]: Loaded 8 crop tiers
[297月2025 17:04:14.871] [modloading-worker-0/INFO] [Mystical Agriculture/]: Loaded 2 crop types
[297月2025 17:04:14.871] [modloading-worker-0/INFO] [Mystical Agriculture/]: Loaded 55 augments
[297月2025 17:04:14.871] [modloading-worker-0/INFO] [Mystical Agriculture/]: Loaded 20 mob soul types
[297月2025 17:04:14.871] [modloading-worker-0/INFO] [com.tom.createores.CreateOreExcavation/]: Create Ore Excavation starting
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [mekanism.common.Mekanism/]: Loaded 'Mekanism: Generators' module.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: air_canister compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: reinforced_air_canister compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: vortex_cannon compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: pneumatic_wrench compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: manometer compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: logistics_configurator compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: amadron_tablet compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: minigun compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: camo_applicator compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: jackhammer compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: pneumatic_helmet compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: pneumatic_chestplate compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: pneumatic_leggings compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: pneumatic_boots compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: drone compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: logistics_drone compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: harvesting_drone compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: guard_drone compatible with Holding enchantment.
[297月2025 17:04:14.879] [modloading-worker-0/INFO] [pneumaticcraft/]: Making item: collector_drone compatible with Holding enchantment.
[297月2025 17:04:14.883] [Immersive Engineering Contributors Thread/INFO] [immersiveengineering/]: Attempting to download special revolvers from GitHub
[297月2025 17:04:14.887] [modloading-worker-0/INFO] [integrateddynamicscompat/]: Registered packet handler.
[297月2025 17:04:14.896] [modloading-worker-0/INFO] [com.tom.createores.CreateOreExcavation/]: Initilaized Network Handler
[297月2025 17:04:14.898] [modloading-worker-0/INFO] [evilcraft/]: Registered packet handler.
[297月2025 17:04:14.904] [modloading-worker-0/INFO] [com.darkere.serverconfigupdater.ServerConfigUpdater/]: Attempting to delete 2 files/folders defined in config
[297月2025 17:04:14.908] [modloading-worker-0/INFO] [mekanism.common.Mekanism/]: Loaded 'Mekanism: Tools' module.
[297月2025 17:04:14.913] [modloading-worker-0/INFO] [FTB Library/]: Setting game stages provider implementation to: KubeJS Stages
[297月2025 17:04:14.913] [modloading-worker-0/INFO] [FTB XMod Compat/]: Chose [KubeJS Stages] as the active game stages implementation
[297月2025 17:04:14.914] [modloading-worker-0/INFO] [com.klikli_dev.occultism.Occultism/]: Common setup complete.
[297月2025 17:04:14.919] [modloading-worker-0/INFO] [FTB Library/]: Setting permissions provider implementation to: FTB Ranks
[297月2025 17:04:14.919] [modloading-worker-0/INFO] [FTB XMod Compat/]: Chose [FTB Ranks] as the active permissions implementation
[297月2025 17:04:14.921] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Quests] recipe helper provider is [JEI]
[297月2025 17:04:14.924] [modloading-worker-0/INFO] [Dungeon Crawl/]: Common Setup
[297月2025 17:04:14.925] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Quests] Enabled Item Filters integration
[297月2025 17:04:14.927] [modloading-worker-0/INFO] [mcjtylib/]: Detected RFTools Control: enabling support
[297月2025 17:04:14.928] [modloading-worker-0/INFO] [mcjtylib/]: XNet Detected RFTools Control: enabling support
[297月2025 17:04:14.929] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Chunks] FTB Ranks detected, listening for ranks events
[297月2025 17:04:14.930] [modloading-worker-0/INFO] [mcjtylib/]: RFTools Storage Detected XNet: enabling support
[297月2025 17:04:14.932] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Chunks] Enabled Waystones integration
[297月2025 17:04:14.942] [modloading-worker-0/INFO] [Nature's Aura/]: -- Nature's Aura Fake Player Information --
[297月2025 17:04:14.942] [modloading-worker-0/INFO] [Nature's Aura/]: Name: [Minecraft]
[297月2025 17:04:14.942] [modloading-worker-0/INFO] [Nature's Aura/]: UUID: 41C82C87-7AfB-4024-BA57-13D2C99CAE77
[297月2025 17:04:14.942] [modloading-worker-0/INFO] [Nature's Aura/]: -------------------------------------------
[297月2025 17:04:14.944] [modloading-worker-0/ERROR] [patchouli/]: Failed to load book rebornstorage:rs_book defined by mod rebornstorage, skipping
java.lang.IllegalArgumentException: Book rebornstorage:rs_book has use_resource_pack set to false. This behaviour was removed in 1.20. The book author should enable this flag and move all book contents clientside to /assets/, leaving the book.json in /data/. See https://vazkiimods.github.io/Patchouli/docs/upgrading/upgrade-guide-120 for details.
	at vazkii.patchouli.common.book.Book.<init>(Book.java:145) ~[Patchouli-1.20.1-84.1-FORGE.jar%23835!/:1.20.1-84.1-FORGE] {re:classloading}
	at vazkii.patchouli.common.book.BookRegistry.loadBook(BookRegistry.java:90) ~[Patchouli-1.20.1-84.1-FORGE.jar%23835!/:1.20.1-84.1-FORGE] {re:classloading}
	at vazkii.patchouli.common.book.BookRegistry.lambda$init$3(BookRegistry.java:75) ~[Patchouli-1.20.1-84.1-FORGE.jar%23835!/:1.20.1-84.1-FORGE] {re:classloading}
	at java.util.HashMap.forEach(HashMap.java:1429) ~[?:?] {re:mixin}
	at vazkii.patchouli.common.book.BookRegistry.init(BookRegistry.java:70) ~[Patchouli-1.20.1-84.1-FORGE.jar%23835!/:1.20.1-84.1-FORGE] {re:classloading}
	at vazkii.patchouli.forge.common.ForgeModInitializer.onInitialize(ForgeModInitializer.java:76) ~[Patchouli-1.20.1-84.1-FORGE.jar%23835!/:1.20.1-84.1-FORGE] {re:classloading}
	at vazkii.patchouli.forge.common.__ForgeModInitializer_onInitialize_FMLCommonSetupEvent.invoke(.dynamic) ~[Patchouli-1.20.1-84.1-FORGE.jar%23835!/:1.20.1-84.1-FORGE] {re:classloading,pl:eventbus:B}
	at net.minecraftforge.eventbus.ASMEventHandler.invoke(ASMEventHandler.java:73) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:315) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:296) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.fml.javafmlmod.FMLModContainer.acceptEvent(FMLModContainer.java:121) ~[javafmllanguage-1.20.1-47.4.4.jar%23965!/:?] {}
	at net.minecraftforge.fml.ModContainer.lambda$buildTransitionHandler$5(ModContainer.java:127) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804) ~[?:?] {}
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796) ~[?:?] {}
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387) ~[?:?] {}
	at java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1310) ~[?:?] {}
	at java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1841) ~[?:?] {re:computing_frames}
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1806) ~[?:?] {re:computing_frames}
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188) ~[?:?] {}
[297月2025 17:04:14.956] [modloading-worker-0/INFO] [integrateddynamics/]: Registered packet handler.
[297月2025 17:04:14.960] [modloading-worker-0/INFO] [com.klikli_dev.theurgy.Theurgy/]: Common setup complete.
[297月2025 17:04:15.015] [modloading-worker-0/INFO] [mekanism.common.Mekanism/]: Fake player readout: UUID = 46e82cd0-d480-3d48-800a-77431ede078e, name = [Mekanism]
[297月2025 17:04:15.015] [modloading-worker-0/INFO] [mekanism.common.Mekanism/]: Mod loaded.
[297月2025 17:04:15.185] [main/INFO] [com.tiviacz.travelersbackpack.TravelersBackpack/]: Registered the FluidEffect with Unique ID of minecraft:water for Water (Fluid Amount Required: 1000) with the ID 0
[297月2025 17:04:15.185] [main/INFO] [com.tiviacz.travelersbackpack.TravelersBackpack/]: Registered the FluidEffect with Unique ID of minecraft:lava for Lava (Fluid Amount Required: 1000) with the ID 1
[297月2025 17:04:15.185] [main/INFO] [com.tiviacz.travelersbackpack.TravelersBackpack/]: Registered the FluidEffect with Unique ID of travelersbackpack:potion for Uncraftable Potion (Fluid Amount Required: 250) with the ID 2
[297月2025 17:04:15.185] [main/INFO] [com.tiviacz.travelersbackpack.TravelersBackpack/]: Registered the FluidEffect with Unique ID of minecraft:milk for Milk (Fluid Amount Required: 1000) with the ID 3
[297月2025 17:04:15.200] [main/INFO] [terrablender/]: Registered region minecraft:overworld to index 0 for type OVERWORLD
[297月2025 17:04:15.200] [main/INFO] [terrablender/]: Registered region minecraft:nether to index 0 for type NETHER
[297月2025 17:04:15.200] [main/INFO] [terrablender/]: Registered region biomesoplenty:overworld_primary to index 1 for type OVERWORLD
[297月2025 17:04:15.200] [main/INFO] [terrablender/]: Registered region biomesoplenty:overworld_secondary to index 2 for type OVERWORLD
[297月2025 17:04:15.200] [main/INFO] [terrablender/]: Registered region biomesoplenty:overworld_rare to index 3 for type OVERWORLD
[297月2025 17:04:15.200] [main/INFO] [terrablender/]: Registered region biomesoplenty:nether_common to index 1 for type NETHER
[297月2025 17:04:15.200] [main/INFO] [terrablender/]: Registered region biomesoplenty:nether_rare to index 2 for type NETHER
[297月2025 17:04:15.212] [main/INFO] [zeta/]: Common setup: Performing initial refresh of zeta's config on thread 'main'
[297月2025 17:04:15.212] [main/INFO] [zeta/]: 'zeta' is enabling Zeta's piston structure resolver.
[297月2025 17:04:15.213] [main/INFO] [zeta/]: Waiting for server start before accepting filewatcher changes to zeta's config
[297月2025 17:04:15.219] [main/INFO] [terrablender/]: Registered region integrateddynamics:overworld to index 4 for type OVERWORLD
[297月2025 17:04:15.229] [main/INFO] [com.klikli_dev.occultism.Occultism/]: Registered compostable Items
[297月2025 17:04:15.242] [main/INFO] [STDOUT/]: Class: class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier
[297月2025 17:04:15.248] [main/INFO] [journeymap/]: Initializing Packet Registries
[297月2025 17:04:15.356] [main/INFO] [Moonlight/]: Initialized color sets in 89ms
[297月2025 17:04:15.361] [main/INFO] [GregTechCEu/]: CC: Tweaked found. Enabling integration...
[297月2025 17:04:15.382] [main/INFO] [terrablender/]: Registered region quark:biome_provider to index 5 for type OVERWORLD
[297月2025 17:04:15.382] [main/INFO] [quark-zeta/]: Common setup: Performing initial refresh of quark's config on thread 'main'
[297月2025 17:04:15.413] [main/INFO] [quark-zeta/]: Waiting for server start before accepting filewatcher changes to quark's config
[297月2025 17:04:15.455] [main/INFO] [Supplementaries/]: Finished mod setup in: [7, 5, 0, 0, 0, 0, 9, 19, 0] ms
[297月2025 17:04:15.466] [main/INFO] [MEGA Cells/]: Initialised AE2WT integration.
[297月2025 17:04:15.507] [main/INFO] [terrablender/]: Registered region ars_nouveau:overworld to index 6 for type OVERWORLD
[297月2025 17:04:15.579] [modloading-worker-0/INFO] [com.klikli_dev.occultism.Occultism/]: Dedicated server setup complete.
[297月2025 17:04:15.579] [modloading-worker-0/INFO] [com.klikli_dev.theurgy.Theurgy/]: Dedicated server setup complete.
[297月2025 17:04:15.581] [modloading-worker-0/INFO] [pneumaticcraft/]: Sending IMC messages.
[297月2025 17:04:15.588] [modloading-worker-0/INFO] [io.redspace.ironsspellbooks.IronsSpellbooks/]: Got IMC []
[297月2025 17:04:15.588] [modloading-worker-0/INFO] [Apotheosis : Adventure/]: Mod mythicbotany has overriden the loot category of mythicbotany:mjoellnir to sword.
[297月2025 17:04:15.590] [modloading-worker-0/INFO] [net.blay09.mods.craftingtweaks.IMCHandler/]: refinedstorage has registered com.refinedmods.refinedstorage.container.GridContainerMenu for CraftingTweaks via IMC
[297月2025 17:04:15.595] [modloading-worker-0/INFO] [STDOUT/]: Create Crafts & Additions Initialized!
[297月2025 17:04:15.596] [modloading-worker-0/INFO] [net.xalcon.torchmaster.Torchmaster/]: Applying mega torch entity block list overrides...
[297月2025 17:04:15.596] [modloading-worker-0/INFO] [net.xalcon.torchmaster.Torchmaster/]: Applying dread lamp entity block list overrides...
[297月2025 17:04:15.597] [modloading-worker-0/INFO] [AttributeFix/]: Loaded values for 78 compatible attributes.
[297月2025 17:04:15.601] [modloading-worker-0/INFO] [AttributeFix/]: Loaded 78 values from config.
[297月2025 17:04:15.604] [modloading-worker-0/INFO] [AttributeFix/]: Saving config file. 78 entries.
[297月2025 17:04:15.604] [modloading-worker-0/INFO] [AttributeFix/]: Applying changes for 78 attributes.
[297月2025 17:04:15.605] [modloading-worker-0/WARN] [minecolonies/]: Register mappings
[297月2025 17:04:15.625] [modloading-worker-0/INFO] [com.tom.createores.CreateOreExcavation/]: Loaded KubeJS integration
[297月2025 17:04:15.625] [modloading-worker-0/ERROR] [KubeJS/]: Startup script errors:
1) gtceu/alcr.js#51: Error in 'GTCEuStartupEvents.registry': TypeError: Cannot find function workableCasingRenderer in object com.gregtechceu.gtceu.api.registry.registrate.MultiblockMachineBuilder@7ea073d2.
[297月2025 17:04:15.625] [modloading-worker-0/ERROR] [net.minecraftforge.fml.javafmlmod.FMLModContainer/]: Exception caught during firing event: There were KubeJS startup script syntax errors! See logs/kubejs/startup.log for more info
	Index: 1
	Listeners:
		0: LOW
		1: net.minecraftforge.eventbus.EventBus$$Lambda/0x0000000080f374e8@4e2dcbfe
java.lang.RuntimeException: There were KubeJS startup script syntax errors! See logs/kubejs/startup.log for more info
	at TRANSFORMER/kubejs@2001.6.5-build.16/dev.latvian.mods.kubejs.KubeJS.loadComplete(KubeJS.java:226)
	at TRANSFORMER/kubejs@2001.6.5-build.16/dev.latvian.mods.kubejs.forge.KubeJSForge.loadComplete(KubeJSForge.java:135)
	at MC-BOOTSTRAP/net.minecraftforge.eventbus/net.minecraftforge.eventbus.EventBus.doCastFilter(EventBus.java:260)
	at MC-BOOTSTRAP/net.minecraftforge.eventbus/net.minecraftforge.eventbus.EventBus.lambda$addListener$11(EventBus.java:252)
	at MC-BOOTSTRAP/net.minecraftforge.eventbus/net.minecraftforge.eventbus.EventBus.post(EventBus.java:315)
	at MC-BOOTSTRAP/net.minecraftforge.eventbus/net.minecraftforge.eventbus.EventBus.post(EventBus.java:296)
	at LAYER PLUGIN/javafmllanguage@1.20.1-47.4.4/net.minecraftforge.fml.javafmlmod.FMLModContainer.acceptEvent(FMLModContainer.java:121)
	at LAYER PLUGIN/fmlcore@1.20.1-47.4.4/net.minecraftforge.fml.ModContainer.lambda$buildTransitionHandler$5(ModContainer.java:127)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1310)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1841)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1806)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)

[297月2025 17:04:15.625] [modloading-worker-0/ERROR] [net.minecraftforge.fml.javafmlmod.FMLModContainer/LOADING]: Caught exception during event FMLLoadCompleteEvent dispatch for modid kubejs
java.lang.RuntimeException: There were KubeJS startup script syntax errors! See logs/kubejs/startup.log for more info
	at dev.latvian.mods.kubejs.KubeJS.loadComplete(KubeJS.java:226) ~[kubejs-forge-2001.6.5-build.16.jar%23770!/:2001.6.5-build.16] {re:classloading}
	at dev.latvian.mods.kubejs.forge.KubeJSForge.loadComplete(KubeJSForge.java:135) ~[kubejs-forge-2001.6.5-build.16.jar%23770!/:2001.6.5-build.16] {re:classloading}
	at net.minecraftforge.eventbus.EventBus.doCastFilter(EventBus.java:260) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.lambda$addListener$11(EventBus.java:252) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:315) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:296) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.fml.javafmlmod.FMLModContainer.acceptEvent(FMLModContainer.java:121) ~[javafmllanguage-1.20.1-47.4.4.jar%23965!/:?] {}
	at net.minecraftforge.fml.ModContainer.lambda$buildTransitionHandler$5(ModContainer.java:127) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804) ~[?:?] {}
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796) ~[?:?] {}
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387) ~[?:?] {}
	at java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1310) ~[?:?] {}
	at java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1841) ~[?:?] {re:computing_frames}
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1806) ~[?:?] {re:computing_frames}
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188) ~[?:?] {}
[297月2025 17:04:15.626] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at dev.shadowsoffire.gateways.compat.GatewayJadePlugin
[297月2025 17:04:15.628] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at dev.shadowsoffire.apotheosis.spawn.compat.SpawnerHwylaPlugin
[297月2025 17:04:15.628] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at dev.shadowsoffire.apotheosis.ench.compat.EnchHwylaPlugin
[297月2025 17:04:15.629] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at dev.shadowsoffire.apotheosis.adventure.compat.AdventureHwylaPlugin
[297月2025 17:04:15.629] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at ovh.corail.tombstone.compatibility.IntegrationJade
[297月2025 17:04:15.630] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at edivad.extrastorage.compat.jade.JadeCompatibility
[297月2025 17:04:15.630] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at net.geforcemods.securitycraft.compat.hudmods.JadeDataProvider
[297月2025 17:04:15.631] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at shetiphian.endertanks.modintegration.jade.JadePlugin
[297月2025 17:04:15.632] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at shetiphian.platforms.modintegration.jade.JadePlugin
[297月2025 17:04:15.632] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.blakebr0.mysticalagriculture.compat.JadeCompat
[297月2025 17:04:15.632] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at de.maxhenkel.easyvillagers.integration.waila.PluginEasyVillagers
[297月2025 17:04:15.632] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at reliquary.compat.jade.JadeCompat
[297月2025 17:04:15.633] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at me.desht.modularrouters.integration.waila.WailaIntegration
[297月2025 17:04:15.634] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at net.blay09.mods.balm.forge.compat.hudinfo.ForgeJadeModCompat
[297月2025 17:04:15.634] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at shetiphian.core.internal.modintegration.jade.JadePlugin
[297月2025 17:04:15.634] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.blakebr0.mysticalagradditions.compat.JadeCompat
[297月2025 17:04:15.634] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at xfacthd.framedblocks.common.compat.jade.FramedJadePlugin
[297月2025 17:04:15.634] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at snownee.jade.addon.JadeAddonsBase
[297月2025 17:04:15.639] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at de.melanx.utilitix.compat.jade.UtilJade
[297月2025 17:04:15.640] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at mekanism.common.integration.lookingat.jade.MekanismJadePlugin
[297月2025 17:04:15.640] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at me.desht.pneumaticcraft.common.thirdparty.waila.WailaRegistration
[297月2025 17:04:15.642] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at blusunrize.immersiveengineering.common.util.compat.jade.IEWailaPlugin
[297月2025 17:04:15.642] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at de.maxhenkel.pipez.integration.waila.PluginPipes
[297月2025 17:04:15.643] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at org.cyclops.integrateddynamicscompat.modcompat.jade.JadeIntegratedDynamicsConfig
[297月2025 17:04:15.644] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at mods.railcraft.integrations.jade.RailcraftPlugin
[297月2025 17:04:15.644] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.hollingsworth.arsnouveau.client.waila.WailaArsNouveauPlugin
[297月2025 17:04:15.644] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.aetherteam.aether.integration.jade.AetherJadePlugin
[297月2025 17:04:15.645] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at net.blay09.mods.cookingforblockheads.compat.ForgeJadeCookingForBlockheadsPlugin
[297月2025 17:04:15.645] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at twilightforest.compat.jade.JadeCompat
[297月2025 17:04:15.646] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.supermartijn642.entangled.integration.EntangledWailaPlugin
[297月2025 17:04:15.646] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at cy.jdkdigital.productivebees.compat.hwyla.ProductiveBeesWailaPlugin
[297月2025 17:04:15.647] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at net.p3pp3rf1y.sophisticatedstorage.compat.jade.StorageJadePlugin
[297月2025 17:04:15.647] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at net.blay09.mods.waystones.compat.JadeIntegration
[297月2025 17:04:15.648] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at edivad.dimstorage.compat.waila.WailaCompatibility
[297月2025 17:04:15.648] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.gregtechceu.gtceu.integration.jade.GTJadePlugin
[297月2025 17:04:15.650] [modloading-worker-0/ERROR] [bloodmagic/]: MysticalAgriculture integration cancelled: unable to find a class: com.blakebr0.mysticalagriculture.api.crop.ICrop
[297月2025 17:04:15.669] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at snownee.jade.addon.vanilla.VanillaPlugin
[297月2025 17:04:15.674] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at snownee.jade.addon.universal.UniversalPlugin
[297月2025 17:04:15.677] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at snownee.jade.addon.core.CorePlugin
[297月2025 17:04:15.678] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at appeng.integration.modules.jade.JadeModule
[297月2025 17:04:15.686] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.glodblock.github.extendedae.xmod.jade.JadePlugin
[297月2025 17:04:15.686] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.glodblock.github.appflux.xmod.jade.JadePlugin
[297月2025 17:04:15.686] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at com.klikli_dev.theurgy.integration.jade.JadePlugin
[297月2025 17:04:15.687] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at net.mehvahdjukaar.supplementaries.integration.JadeCompat
[297月2025 17:04:15.687] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at net.brnbrd.delightful.compat.jade.DelightfulJadePlugin
[297月2025 17:04:15.687] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at shetiphian.enderchests.modintegration.jade.JadePlugin
[297月2025 17:04:15.687] [modloading-worker-0/INFO] [Jade/]: Start loading plugin at me.ramidzkh.mekae2.integration.jade.AMJadePlugin
[297月2025 17:04:15.693] [main/FATAL] [net.minecraftforge.fml.ModLoader/LOADING]: Failed to complete lifecycle event COMPLETE, 1 errors found
[297月2025 17:04:15.738] [main/INFO] [STDOUT/]: Negative index in crash report handler (24/26)
[297月2025 17:04:15.768] [main/FATAL] [net.minecraftforge.common.ForgeMod/]: Preparing crash report with UUID bd7df46a-e48c-4389-81f1-f06fda1b96c6
[297月2025 17:04:15.768] [main/FATAL] [net.minecraftforge.server.loading.ServerModLoader/]: Crash report saved to ./crash-reports/crash-2025-07-29_17.04.15-fml.txt
[297月2025 17:04:15.778] [main/FATAL] [net.minecraftforge.common.ForgeMod/]: Preparing crash report with UUID 888203b6-0943-43a5-a6a0-7d7da7406392
[297月2025 17:04:15.779] [main/ERROR] [net.minecraft.server.Main/FATAL]: Failed to start the minecraft server
net.minecraftforge.fml.LoadingFailedException: Loading errors encountered: [
	KubeJS (kubejs) encountered an error during the complete event phase
§7java.lang.RuntimeException: There were KubeJS startup script syntax errors! See logs/kubejs/startup.log for more info
]
	at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:246) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$finishMods$17(ModLoader.java:197) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.finishMods(ModLoader.java:197) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:32) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
