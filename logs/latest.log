[297月2025 17:01:40.656] [main/INFO] [Luminara/]: 

        __                    _                       
       / /   __  ______ ___  (_)___  ____ __________ _
      / /   / / / / __ `__ \/ / __ \/ __ `/ ___/ __ `/
     / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / 
    /_____/\__,_/_/ /_/ /_/_/_/ /_/\__,_/_/   \__,_/  

    Luminara·流明纳拉 服务端 By QianMoo0121(QianMo_ProMax)
    运行版本 顿顽 1.20.1 / luminara-1.20.1-1.0.8-a193d85
    构建日期 2025-07-29 06:36:08

[297月2025 17:01:40.665] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher running: args [--launchTarget, arclightserver, --fml.forgeVersion, 47.4.4, --fml.mcVersion, 1.20.1, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, 20230612.114412, -nogui]
[297月2025 17:01:40.665] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher 10.0.9+10.0.9+main.dcd20f30 starting: java version 21.0.6 by Oracle Corporation; OS Linux arch amd64 version 6.14.6-x64v3-xanmod1
[297月2025 17:01:41.491] [main/INFO] [net.minecraftforge.fml.loading.ImmediateWindowHandler/]: ImmediateWindowProvider not loading because launch target is arclightserver
[297月2025 17:01:41.504] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.5 Source=union:/root/server/main/libraries/org/spongepowered/mixin/0.8.5/mixin-0.8.5.jar%2399!/ Service=ModLauncher Env=SERVER
[297月2025 17:01:41.851] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/fmlcore/1.20.1-47.4.4/fmlcore-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:01:41.852] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/javafmllanguage/1.20.1-47.4.4/javafmllanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:01:41.852] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/lowcodelanguage/1.20.1-47.4.4/lowcodelanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:01:41.852] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/mclanguage/1.20.1-47.4.4/mclanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:01:42.090] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: configuration. Using Mod File: /root/server/main/mods/configuration-forge-1.20.1-3.1.0.jar
[297月2025 17:01:42.090] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: geckolib. Using Mod File: /root/server/main/mods/geckolib-forge-1.20.1-4.7.1.2.jar
[297月2025 17:01:42.090] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: curios. Using Mod File: /root/server/main/mods/curios-forge-5.14.1+1.20.1.jar
[297月2025 17:01:42.090] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: resourcefullib. Using Mod File: /root/server/main/mods/resourcefullib-forge-1.20.1-2.1.29.jar
[297月2025 17:01:42.090] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: ldlib. Using Mod File: /root/server/main/mods/ldlib-forge-1.20.1-1.0.41.b.jar
[297月2025 17:01:42.090] [main/INFO] [net.minecraftforge.fml.loading.moddiscovery.JarInJarDependencyLocator/]: Found 36 dependencies adding them to mods collection
[297月2025 17:01:45.224] [main/INFO] [mixin/]: Compatibility level set to JAVA_17
[297月2025 17:01:45.352] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [com.thevortex.allthetweaks.mixin.MixinConnector]
[297月2025 17:01:45.353] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [com.leobeliik.extremesoundmuffler.MixinConnector]
[297月2025 17:01:45.354] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [shetiphian.endertanks.mixins.MixinConnector]
[297月2025 17:01:45.354] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [shetiphian.core.mixins.MixinConnector]
[297月2025 17:01:45.354] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [ca.spottedleaf.starlight.mixin.MixinConnector]
[297月2025 17:01:45.355] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [io.izzel.arclight.common.mod.ArclightConnector]
[297月2025 17:01:45.365] [main/INFO] [Arclight/]: 核心 Mixin 配置已加载
[297月2025 17:01:45.365] [main/INFO] [Arclight/]: 优化 Mixin 配置已加载
[297月2025 17:01:45.368] [main/INFO] [cpw.mods.modlauncher.LaunchServiceHandler/MODLAUNCHER]: Launching target 'arclightserver' with arguments [-nogui]
[297月2025 17:01:45.374] [main/WARN] [mixin/]: Reference map 'universalgrid.refmap.json' for universalgrid.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.380] [main/WARN] [mixin/]: Reference map 'handcrafted-forge-1.20.1-forge-refmap.json' for handcrafted.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.382] [main/WARN] [mixin/]: Reference map 'yungsextras.refmap.json' for yungsextras.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.382] [main/WARN] [mixin/]: Reference map 'yungsextras.refmap.json' for yungsextras_forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.385] [main/WARN] [mixin/]: Reference map 'nitrogen_internals.refmap.json' for nitrogen_internals.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.388] [main/WARN] [mixin/]: Reference map 'EpheroLib-refmap.json' for epherolib.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.393] [main/WARN] [mixin/]: Reference map '${refmap_target}refmap.json' for corgilib.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.401] [main/WARN] [mixin/]: Reference map 'tempad-forge-1.20.1-forge-refmap.json' for tempad.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.430] [main/INFO] [com.abdelaziz.saturn.common.Saturn/]: Loaded Saturn config file with 4 configurable options
[297月2025 17:01:45.462] [main/WARN] [mixin/]: Reference map 'cristellib-forge-refmap.json' for cristellib.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.498] [main/WARN] [mixin/]: Reference map 'naturalist-forge-forge-refmap.json' for naturalist.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.499] [main/WARN] [mixin/]: Reference map 'cookingforblockheads.refmap.json' for cookingforblockheads.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.511] [main/WARN] [mixin/]: Reference map 'trashslot.refmap.json' for trashslot.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.518] [main/WARN] [mixin/]: Reference map 'bloodmagic.refmap.json' for bloodmagic.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.539] [main/WARN] [mixin/]: Reference map 'modonomicon.refmap.json' for modonomicon.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.540] [main/WARN] [mixin/]: Reference map 'modonomicon.refmap.json' for modonomicon.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.544] [main/WARN] [mixin/]: Reference map 'packetfixer-forge-forge-refmap.json' for packetfixer.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:01:45.739] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:01:45.746] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:01:45.806] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/inventory/AnvilMenu
[297月2025 17:01:45.824] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:01:45.824] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:01:45.938] [main/ERROR] [net.minecraftforge.coremod.transformer.CoreModBaseTransformer/COREMOD]: Error occurred applying transform of coremod coremods/field_to_method.js function biome
java.lang.IllegalStateException: Field f_47437_ is not private and an instance field
	at net.minecraftforge.coremod.api.ASMAPI.redirectFieldToMethod(ASMAPI.java:1069) ~[coremods-5.2.4.jar%2388!/:?] {}
	at org.openjdk.nashorn.internal.scripts.Script$Recompilation$123$292A$\^eval\_.initializeCoreMod#transformer(<eval>:11) ~[?:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunctionData.invoke(ScriptFunctionData.java:648) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunction.invoke(ScriptFunction.java:513) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptRuntime.apply(ScriptRuntime.java:520) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.api.scripting.ScriptObjectMirror.call(ScriptObjectMirror.java:111) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at net.minecraftforge.coremod.NashornFactory.lambda$getFunction$0(NashornFactory.java:37) ~[coremods-5.2.4.jar%2388!/:5.2.4] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:22) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:14) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModBaseTransformer.transform(CoreModBaseTransformer.java:60) ~[coremods-5.2.4.jar%2388!/:?] {}
	at cpw.mods.modlauncher.TransformerHolder.transform(TransformerHolder.java:41) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.performVote(ClassTransformer.java:179) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:117) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.getMaybeTransformedClassBytes(ModuleClassLoader.java:250) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.buildTransformedClassNodeFor(TransformingClassLoader.java:58) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchPluginHandler.lambda$announceLaunch$10(LaunchPluginHandler.java:100) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.getClassNode(MixinLaunchPluginLegacy.java:222) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.getClassNode(MixinLaunchPluginLegacy.java:207) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.ClassInfo.forName(ClassInfo.java:2005) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.getTargetClass(MixinInfo.java:1017) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.readTargetClasses(MixinInfo.java:1007) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.parseTargets(MixinInfo.java:895) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinConfig.prepareMixins(MixinConfig.java:867) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinConfig.prepare(MixinConfig.java:775) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.prepareConfigs(MixinProcessor.java:539) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.select(MixinProcessor.java:462) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.checkSelect(MixinProcessor.java:438) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:290) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:250) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.service.modlauncher.MixinTransformationHandler.processClass(MixinTransformationHandler.java:131) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.processClass(MixinLaunchPluginLegacy.java:131) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at cpw.mods.modlauncher.serviceapi.ILaunchPluginService.processClassWithFlags(ILaunchPluginService.java:156) ~[modlauncher-10.0.9.jar%2389!/:10.0.9+10.0.9+main.dcd20f30] {}
	at cpw.mods.modlauncher.LaunchPluginHandler.offerClassNodeToPlugins(LaunchPluginHandler.java:88) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:120) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.readerToClass(ModuleClassLoader.java:113) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.lambda$findClass$15(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadFromModule(ModuleClassLoader.java:229) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.findClass(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at java.lang.ClassLoader.loadClass(ClassLoader.java:638) ~[?:?] {}
	at java.lang.Class.forName(Class.java:625) ~[?:?] {}
	at java.lang.Class.forName(Class.java:600) ~[?:?] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler$DummyProvider.lambda$updateModuleReads$1(ImmediateWindowHandler.java:145) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at java.util.Optional.map(Optional.java:260) ~[?:?] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler$DummyProvider.updateModuleReads(ImmediateWindowHandler.java:145) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler.acceptGameLayer(ImmediateWindowHandler.java:71) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.FMLLoader.beforeStart(FMLLoader.java:216) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.launchService(CommonLaunchHandler.java:92) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:01:46.041] [main/WARN] [mixin/]: Error loading class: net/dries007/tfc/common/blocks/rock/AqueductBlock (java.lang.ClassNotFoundException: net.dries007.tfc.common.blocks.rock.AqueductBlock)
[297月2025 17:01:46.041] [main/WARN] [mixin/]: @Mixin target net.dries007.tfc.common.blocks.rock.AqueductBlock was not found allthetweaks.mixins.json:FluiDucts
[297月2025 17:01:46.050] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Patching FishingHook#catchingFish
[297月2025 17:01:46.068] [main/WARN] [mixin/]: Error loading class: mcjty/theoneprobe/apiimpl/providers/DefaultProbeInfoProvider (java.lang.ClassNotFoundException: mcjty.theoneprobe.apiimpl.providers.DefaultProbeInfoProvider)
[297月2025 17:01:46.068] [main/WARN] [mixin/]: @Mixin target mcjty.theoneprobe.apiimpl.providers.DefaultProbeInfoProvider was not found mixins.endertanks.json:ET_HideFluidBars$_TheOneProbe
[297月2025 17:01:46.078] [main/WARN] [mixin/]: Error loading class: com/illusivesoulworks/diet/common/DietApiImpl (java.lang.ClassNotFoundException: com.illusivesoulworks.diet.common.DietApiImpl)
[297月2025 17:01:46.078] [main/WARN] [mixin/]: @Mixin target com.illusivesoulworks.diet.common.DietApiImpl was not found caupona.mixins.json:DietApiImplMixin
[297月2025 17:01:46.085] [main/WARN] [mixin/]: Error loading class: vazkii/quark/addons/oddities/inventory/BackpackMenu (java.lang.ClassNotFoundException: vazkii.quark.addons.oddities.inventory.BackpackMenu)
[297月2025 17:01:46.086] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/gui/screens/inventory/CraftingScreen (java.lang.ClassNotFoundException: net.minecraft.client.gui.screens.inventory.CraftingScreen)
[297月2025 17:01:46.086] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.gui.screens.inventory.CraftingScreen was not found fastbench.mixins.json:MixinCraftingScreen
[297月2025 17:01:46.093] [main/WARN] [mixin/]: Error loading class: shadows/placebo/patreon/TrailsManager (java.lang.ClassNotFoundException: shadows.placebo.patreon.TrailsManager)
[297月2025 17:01:46.093] [main/WARN] [mixin/]: @Mixin target shadows.placebo.patreon.TrailsManager was not found smsn.mixins.json:placebo.TrailsManagerMixin
[297月2025 17:01:46.094] [main/WARN] [mixin/]: Error loading class: shadows/placebo/patreon/WingsManager (java.lang.ClassNotFoundException: shadows.placebo.patreon.WingsManager)
[297月2025 17:01:46.094] [main/WARN] [mixin/]: @Mixin target shadows.placebo.patreon.WingsManager was not found smsn.mixins.json:placebo.WingsManagerMixin
[297月2025 17:01:46.149] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/multiplayer/MultiPlayerGameMode for invalid dist DEDICATED_SERVER
[297月2025 17:01:46.149] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/multiplayer/MultiPlayerGameMode (java.lang.RuntimeException: Attempted to load class net/minecraft/client/multiplayer/MultiPlayerGameMode for invalid dist DEDICATED_SERVER)
[297月2025 17:01:46.149] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.multiplayer.MultiPlayerGameMode was not found mixins.cofhcore.json:MultiPlayerGameModeMixin
[297月2025 17:01:46.219] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:01:46.219] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTreasureOnly() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:01:46.219] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTradeable() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:01:46.262] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/item/CreativeModeTabs
[297月2025 17:01:46.313] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/renderer/entity/PhantomRenderer (java.lang.ClassNotFoundException: net.minecraft.client.renderer.entity.PhantomRenderer)
[297月2025 17:01:46.313] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.renderer.entity.PhantomRenderer was not found mixins.deeperdarker.json:PhantomRendererMixin
[297月2025 17:01:46.328] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/gui/screens/inventory/AbstractContainerScreen for invalid dist DEDICATED_SERVER
[297月2025 17:01:46.328] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/gui/screens/inventory/AbstractContainerScreen (java.lang.RuntimeException: Attempted to load class net/minecraft/client/gui/screens/inventory/AbstractContainerScreen for invalid dist DEDICATED_SERVER)
[297月2025 17:01:46.328] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.gui.screens.inventory.AbstractContainerScreen was not found findme-common.mixins.json:MixinSlotRenderer
[297月2025 17:01:46.329] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/particle/ParticleEngine for invalid dist DEDICATED_SERVER
[297月2025 17:01:46.329] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/particle/ParticleEngine (java.lang.RuntimeException: Attempted to load class net/minecraft/client/particle/ParticleEngine for invalid dist DEDICATED_SERVER)
[297月2025 17:01:46.329] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.particle.ParticleEngine was not found findme-common.mixins.json:ParticleEngineAccessor
[297月2025 17:01:46.406] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/resources/model/ModelBakery for invalid dist DEDICATED_SERVER
[297月2025 17:01:46.406] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/resources/model/ModelBakery (java.lang.RuntimeException: Attempted to load class net/minecraft/client/resources/model/ModelBakery for invalid dist DEDICATED_SERVER)
[297月2025 17:01:46.406] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.resources.model.ModelBakery was not found mixins.aae.json:client.ModelBakeryMixin
[297月2025 17:01:46.424] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:01:46.424] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isDiscoverable() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:01:46.751] [main/INFO] [MixinExtras|Service/]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.5.0).
[297月2025 17:01:46.920] [main/INFO] [mixin/]: Mixing server.MixinDedicatedServer from mixins/common/nochatreports.mixins.json into net.minecraft.server.dedicated.DedicatedServer
[297月2025 17:01:47.450] [main/WARN] [mixin/]: Method overwrite conflict for getThis in mixins.brandonscore.json:LivingEntityMixin, previously written by com.teammoeg.caupona.mixin.LivingEntityMixin. Skipping method.
[297月2025 17:01:47.468] [main/WARN] [mixin/]: @Redirect conflict. Skipping attributeslib.mixins.json:LivingEntityMixin->@Redirect::apoth_sunderingHasEffect(Lnet/minecraft/world/entity/LivingEntity;Lnet/minecraft/world/effect/MobEffect;)Z with priority 1000, already redirected by mixins.arclight.core.json:world.entity.LivingEntityMixin$ApotheosisCompatMixin->@Redirect::arclight$mutePotion(Lnet/minecraft/world/entity/LivingEntity;Lnet/minecraft/world/effect/MobEffect;)Z with priority 1500
[297月2025 17:01:47.656] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/item/CreativeModeTabs
[297月2025 17:01:47.813] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/experimental/module/GameNerfsModule
[297月2025 17:01:48.190] [main/WARN] [mixin/]: @Redirect conflict. Skipping securitycraft.mixins.json:camera.ServerPlayerMixin->@Redirect::securitycraft$tick(Lnet/minecraft/server/level/ServerPlayer;DDDFF)V with priority 1100, already redirected by railways-common.mixins.json:conductor_possession.ServerPlayerMixin->@Redirect::railways$securitycraft$tick(Lnet/minecraft/server/level/ServerPlayer;DDDFF)V with priority 1200
[297月2025 17:01:48.326] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_150430_ in mixins.arclight.core.json:world.inventory.AbstractContainerMenuMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:01:48.388] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 5 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/module/AncientTomesModule
[297月2025 17:01:48.401] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:01:48.401] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:01:48.540] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/inventory/AnvilMenu
[297月2025 17:01:48.544] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:01:48.544] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:01:48.544] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:01:48.774] [main/WARN] [mixin/]: Injection warning: LVT in net/minecraft/world/item/BoatItem::m_7203_(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;)Lnet/minecraft/world/InteractionResultHolder; has incompatible changes at opcode 158 in callback securitycraft.mixins.json:boat.BoatItemMixin->@Inject::securitycraft$maybeSetSecuritySeaBoatOwner(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/HitResult;Lnet/minecraft/world/phys/Vec3;DLjava/util/List;Lnet/minecraft/world/entity/vehicle/Boat;)V.
 Expected: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/HitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;]
    Found: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/BlockHitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;]
Available: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/BlockHitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;, Ljava/util/Iterator;, Lnet/minecraft/world/entity/Entity;, Lnet/minecraft/world/phys/AABB;]
[297月2025 17:01:49.156] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_147092_ in mixins.arclight.core.json:world.entity.ExperienceOrbMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:01:49.483] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_37547_ in mixins.arclight.core.json:world.entity.projectile.ThrownPotionMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:01:49.548] [main/INFO] [mixin/]: Mixing common.MixinFriendlyByteBuf from mixins/common/nochatreports.mixins.json into net.minecraft.network.FriendlyByteBuf
[297月2025 17:01:49.548] [main/INFO] [mixin/]: Renaming synthetic method lambda$onWriteJsonWithCodec$1(Ljava/lang/Object;Ljava/lang/String;)Lio/netty/handler/codec/EncoderException; to mda0f065$lambda$onWriteJsonWithCodec$1$0 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[297月2025 17:01:49.549] [main/INFO] [mixin/]: Renaming synthetic method lambda$onReadJsonWithCodec$0(Ljava/lang/String;)Lio/netty/handler/codec/DecoderException; to mda0f065$lambda$onReadJsonWithCodec$0$1 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[297月2025 17:01:49.654] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Patching FishingHook#catchingFish
[297月2025 17:01:49.768] [main/INFO] [mixin/]: Mixing server.MixinServerGamePacketListenerImpl from mixins/common/nochatreports.mixins.json into net.minecraft.server.network.ServerGamePacketListenerImpl
[297月2025 17:01:49.999] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:01:49.999] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isDiscoverable() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:01:50.318] [main/ERROR] [net.minecraftforge.coremod.transformer.CoreModBaseTransformer/COREMOD]: Error occurred applying transform of coremod coremods/field_to_method.js function biome
java.lang.IllegalStateException: Field f_47437_ is not private and an instance field
	at net.minecraftforge.coremod.api.ASMAPI.redirectFieldToMethod(ASMAPI.java:1069) ~[coremods-5.2.4.jar%2388!/:?] {}
	at org.openjdk.nashorn.internal.scripts.Script$Recompilation$123$292A$\^eval\_.initializeCoreMod#transformer(<eval>:11) ~[?:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunctionData.invoke(ScriptFunctionData.java:648) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunction.invoke(ScriptFunction.java:513) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptRuntime.apply(ScriptRuntime.java:520) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.api.scripting.ScriptObjectMirror.call(ScriptObjectMirror.java:111) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at net.minecraftforge.coremod.NashornFactory.lambda$getFunction$0(NashornFactory.java:37) ~[coremods-5.2.4.jar%2388!/:5.2.4] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:22) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:14) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModBaseTransformer.transform(CoreModBaseTransformer.java:60) ~[coremods-5.2.4.jar%2388!/:?] {}
	at cpw.mods.modlauncher.TransformerHolder.transform(TransformerHolder.java:41) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.performVote(ClassTransformer.java:179) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:117) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.readerToClass(ModuleClassLoader.java:113) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.lambda$findClass$15(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadFromModule(ModuleClassLoader.java:229) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.findClass(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadClass(ModuleClassLoader.java:135) ~[securejarhandler-2.1.10.jar:?] {}
	at java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?] {}
	at net.minecraft.world.level.biome.FixedBiomeSource.<clinit>(FixedBiomeSource.java:17) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:classloading,pl:accesstransformer:B}
	at net.minecraft.world.level.biome.BiomeSources.m_220586_(BiomeSources.java:8) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:classloading}
	at net.minecraft.core.registries.BuiltInRegistries.m_258029_(BuiltInRegistries.java:448) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.core.registries.BuiltInRegistries.m_258037_(BuiltInRegistries.java:462) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:986) ~[?:?] {}
	at net.minecraft.core.registries.BuiltInRegistries.m_257453_(BuiltInRegistries.java:461) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.core.registries.BuiltInRegistries.m_257498_(BuiltInRegistries.java:455) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.server.Bootstrap.m_135870_(BootstrapMixin.java:55) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,re:classloading,pl:mixin:APP:mixins.arclight.core.json:server.BootstrapMixin,pl:mixin:APP:ae2.mixins.json:EarlyStartupMixin,pl:mixin:A}
	at net.minecraft.server.Main.main(Main.java:121) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:01:50.648] [main/INFO] [com.almostreliable.merequester.MERequester/]: Registering content
[297月2025 17:01:50.835] [modloading-worker-0/INFO] [com.aetherteam.cumulus.Cumulus/]: Disabling Cumulus as it is on server.
[297月2025 17:01:50.859] [modloading-worker-0/INFO] [LowDragLib/]: LowDragLib is initializing on platform: Forge
[297月2025 17:01:50.859] [modloading-worker-0/INFO] [universalgrid/]: Loading config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:01:50.859] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for industrialforegoing
[297月2025 17:01:50.859] [modloading-worker-0/INFO] [universalgrid/]: Built config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:01:50.859] [modloading-worker-0/INFO] [universalgrid/]: Loaded config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:01:50.865] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class PatchouliPlugin for plugin patchouli
[297月2025 17:01:50.865] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class CuriosPlugin for plugin curios
[297月2025 17:01:50.866] [modloading-worker-0/INFO] [PluginManager/]: Constructed class PatchouliPlugin for plugin patchouli for mod industrialforegoing
[297月2025 17:01:50.866] [modloading-worker-0/INFO] [PluginManager/]: Constructed class CuriosPlugin for plugin curios for mod industrialforegoing
[297月2025 17:01:50.867] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class PatchouliPlugin
[297月2025 17:01:50.869] [modloading-worker-0/INFO] [create_new_age/]: Hello 1.20.1 Create!
[297月2025 17:01:50.870] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class CuriosPlugin
[297月2025 17:01:50.875] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class PatchouliPlugin
[297月2025 17:01:50.875] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class CuriosPlugin
[297月2025 17:01:50.931] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 14 blocks registered.
[297月2025 17:01:50.932] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 29 items registered.
[297月2025 17:01:50.932] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 13 tiles registered.
[297月2025 17:01:50.932] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 13 containers registered.
[297月2025 17:01:50.940] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for sushigocrafting
[297月2025 17:01:50.956] [modloading-worker-0/INFO] [mixin/]: Mixing server.MixinPlayerList from mixins/common/nochatreports.mixins.json into net.minecraft.server.players.PlayerList
[297月2025 17:01:50.975] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class PatchouliPlugin for plugin patchouli
[297月2025 17:01:50.975] [modloading-worker-0/INFO] [PluginManager/]: Constructed class PatchouliPlugin for plugin patchouli for mod sushigocrafting
[297月2025 17:01:50.975] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class PatchouliPlugin
[297月2025 17:01:50.976] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class PatchouliPlugin
[297月2025 17:01:51.001] [modloading-worker-0/INFO] [net.bdew.generators.integration.ic2c.IC2CIntegration$/]: IC2 Not loaded, skipping integration
[297月2025 17:01:51.006] [modloading-worker-0/INFO] [Advanced Peripherals/]: AdvancedPeripherals says hello!
[297月2025 17:01:51.039] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class PatchouliPlugin
[297月2025 17:01:51.043] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class PatchouliPlugin
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Warped Netherlands
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Stalactite Caves
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Space Wars
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Shire
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Desert Oasis
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Pagoda
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Minecolonies Original
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Nordic Spruce
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Spruce
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Oak
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Dark Oak
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Birch
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Lost Mesa City
[297月2025 17:01:51.052] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Jungle Treehouse
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Incan
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Fortress
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Dark Oak Treehouse
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Colonial
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Cavern
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Caledonia
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Urban Birch
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Ancient Athens
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Urban Savanna
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Antique
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: High Magic
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Frontier
[297月2025 17:01:51.053] [Structurize IO Worker #0/WARN] [structurize/]: Missing Mod: byg for Pack: Corrupted
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Crimson Keep
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: FairyTale
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Functional Fantasy
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Underwater Base
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Aquatica
[297月2025 17:01:51.053] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Steampunk
[297月2025 17:01:51.054] [Structurize IO Worker #0/WARN] [structurize/]: Failed loading packs from main folder path: .
[297月2025 17:01:51.054] [Structurize IO Worker #0/WARN] [structurize/]: Failed loading client packs from main folder path: .
[297月2025 17:01:51.054] [Structurize IO Worker #0/WARN] [structurize/]: Finished discovering Server Structure packs
[297月2025 17:01:51.055] [modloading-worker-0/INFO] [de.keksuccino.konkrete.Konkrete/]: [KONKRETE] Successfully initialized!
[297月2025 17:01:51.055] [modloading-worker-0/INFO] [de.keksuccino.konkrete.Konkrete/]: [KONKRETE] Server-side libs ready to use!
[297月2025 17:01:51.058] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:pufferfish_bucket is now minecraft:bucket.
[297月2025 17:01:51.058] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:salmon_bucket is now minecraft:bucket.
[297月2025 17:01:51.058] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:cod_bucket is now minecraft:bucket.
[297月2025 17:01:51.058] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:tropical_fish_bucket is now minecraft:bucket.
[297月2025 17:01:51.058] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:axolotl_bucket is now minecraft:bucket.
[297月2025 17:01:51.058] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:powder_snow_bucket is now minecraft:bucket.
[297月2025 17:01:51.058] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:tadpole_bucket is now minecraft:bucket.
[297月2025 17:01:51.089] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for industrialforegoingsouls
[297月2025 17:01:51.091] [modloading-worker-0/INFO] [cy.jdkdigital.utilitarian.Utilitarian/]: setting up server config
[297月2025 17:01:51.091] [modloading-worker-0/INFO] [fr.samlegamer.mcwbiomesoplenty.McwBOP/]: Macaw's Biomes O' Plenty Loading...
[297月2025 17:01:51.108] [modloading-worker-0/INFO] [com.cupboard.Cupboard/]: Loaded config for: cupboard.json
[297月2025 17:01:51.125] [modloading-worker-0/WARN] [defaultsettings/]: DefaultSettings is a client-side mod only! It won't do anything on servers!
[297月2025 17:01:51.133] [modloading-worker-0/INFO] [voidscape/]: Starting Donator Handler
[297月2025 17:01:51.134] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for powah
[297月2025 17:01:51.135] [Voidscape Donator Loader/INFO] [voidscape/]: Loading donor data
[297月2025 17:01:51.140] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for create
[297月2025 17:01:51.143] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for mekanism
[297月2025 17:01:51.207] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for botania
[297月2025 17:01:51.252] [modloading-worker-0/INFO] [fr.samlegamer.mcwbiomesoplenty.McwBOP/]: Macaw's Biomes O' Plenty Is Charged !
[297月2025 17:01:51.257] [modloading-worker-0/INFO] [net.permutated.pylons.Pylons/]: Registering mod: pylons
[297月2025 17:01:51.276] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class PatchouliPlugin
[297月2025 17:01:51.276] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class CuriosPlugin
[297月2025 17:01:51.283] [modloading-worker-0/WARN] [de.keksuccino.justzoom.JustZoom/]: [JUST ZOOM] Disabling 'Just Zoom' since it's a client-side mod and current environment is server!
[297月2025 17:01:51.290] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for functionalstorage
[297月2025 17:01:51.352] [modloading-worker-0/INFO] [net.bdew.generators.integration.mekanism.MekanismIntegration$/]: Mekanism loaded, activating integration
[297月2025 17:01:51.359] [modloading-worker-0/INFO] [net.permutated.pylons.Pylons/]: Registered 1 network packets
[297月2025 17:01:51.383] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id architectury:sync_ids
[297月2025 17:01:51.387] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/42a7ad70b1cc371599a0eff744096b8a
[297月2025 17:01:51.395] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/74a5e841822a3a87854ae896a33430d6
[297月2025 17:01:51.396] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/eb3d1e2748533430848cadf0f37c7e9c
[297月2025 17:01:51.400] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/91c8520f19f93b3e8b6a727568e194ab
[297月2025 17:01:51.404] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class PatchouliPlugin
[297月2025 17:01:51.405] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class CuriosPlugin
[297月2025 17:01:51.414] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/8c2784d778293fd482ed84b8aa5fedb9
[297月2025 17:01:51.415] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/ea038224ea783d40b2863f52239e2604
[297月2025 17:01:51.418] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/97ad64b7ecaf33209991c4f031501a58
[297月2025 17:01:51.438] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'multiblock' for mod 'bdlib'
[297月2025 17:01:51.459] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'misc' for mod 'bdlib'
[297月2025 17:01:51.472] [modloading-worker-0/INFO] [net.bdew.lib.BdLib$/]: Initialized multiblock manager for advgenerators
[297月2025 17:01:51.558] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'generators' for mod 'advgenerators'
[297月2025 17:01:51.565] [modloading-worker-0/INFO] [net.permutated.novillagerdm.NoVillagerDeathMessages/]: Registering mod: novillagerdm
[297月2025 17:01:51.682] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Found 6 RS API injection points
[297月2025 17:01:51.682] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.ultramega.universalgrid.UniversalGrid RSAPI
[297月2025 17:01:51.682] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in net.gigabit101.rebornstorage.RebornStorage RSAPI
[297月2025 17:01:51.683] [modloading-worker-0/INFO] [Mystical Agriculture/]: Registered plugin: com.blakebr0.mysticalagriculture.lib.ModCorePlugin
[297月2025 17:01:51.683] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.enderio.conduits.common.integrations.refinedstorage.RSTicker RSAPI
