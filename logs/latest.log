[297月2025 17:05:18.572] [main/INFO] [Luminara/]: 

        __                    _                       
       / /   __  ______ ___  (_)___  ____ __________ _
      / /   / / / / __ `__ \/ / __ \/ __ `/ ___/ __ `/
     / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / 
    /_____/\__,_/_/ /_/ /_/_/_/ /_/\__,_/_/   \__,_/  

    Luminara·流明纳拉 服务端 By QianMoo0121(QianMo_ProMax)
    运行版本 顿顽 1.20.1 / luminara-1.20.1-1.0.8-a193d85
    构建日期 2025-07-29 06:36:08

[297月2025 17:05:18.582] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher running: args [--launchTarget, arclightserver, --fml.forgeVersion, 47.4.4, --fml.mcVersion, 1.20.1, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, 20230612.114412, -nogui]
[297月2025 17:05:18.582] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher 10.0.9+10.0.9+main.dcd20f30 starting: java version 21.0.6 by Oracle Corporation; OS Linux arch amd64 version 6.14.6-x64v3-xanmod1
[297月2025 17:05:19.429] [main/INFO] [net.minecraftforge.fml.loading.ImmediateWindowHandler/]: ImmediateWindowProvider not loading because launch target is arclightserver
[297月2025 17:05:19.443] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.5 Source=union:/root/server/main/libraries/org/spongepowered/mixin/0.8.5/mixin-0.8.5.jar%2399!/ Service=ModLauncher Env=SERVER
[297月2025 17:05:19.789] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/fmlcore/1.20.1-47.4.4/fmlcore-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:05:19.790] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/javafmllanguage/1.20.1-47.4.4/javafmllanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:05:19.790] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/lowcodelanguage/1.20.1-47.4.4/lowcodelanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:05:19.790] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file /root/server/main/libraries/net/minecraftforge/mclanguage/1.20.1-47.4.4/mclanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 17:05:20.028] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: configuration. Using Mod File: /root/server/main/mods/configuration-forge-1.20.1-3.1.0.jar
[297月2025 17:05:20.028] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: geckolib. Using Mod File: /root/server/main/mods/geckolib-forge-1.20.1-4.7.1.2.jar
[297月2025 17:05:20.028] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: curios. Using Mod File: /root/server/main/mods/curios-forge-5.14.1+1.20.1.jar
[297月2025 17:05:20.028] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: resourcefullib. Using Mod File: /root/server/main/mods/resourcefullib-forge-1.20.1-2.1.29.jar
[297月2025 17:05:20.028] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select a dependency jar for JarJar which was passed in as source: ldlib. Using Mod File: /root/server/main/mods/ldlib-forge-1.20.1-1.0.41.b.jar
[297月2025 17:05:20.028] [main/INFO] [net.minecraftforge.fml.loading.moddiscovery.JarInJarDependencyLocator/]: Found 36 dependencies adding them to mods collection
[297月2025 17:05:23.257] [main/INFO] [mixin/]: Compatibility level set to JAVA_17
[297月2025 17:05:23.385] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [com.thevortex.allthetweaks.mixin.MixinConnector]
[297月2025 17:05:23.387] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [com.leobeliik.extremesoundmuffler.MixinConnector]
[297月2025 17:05:23.387] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [shetiphian.endertanks.mixins.MixinConnector]
[297月2025 17:05:23.387] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [shetiphian.core.mixins.MixinConnector]
[297月2025 17:05:23.388] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [ca.spottedleaf.starlight.mixin.MixinConnector]
[297月2025 17:05:23.389] [main/INFO] [mixin/]: Successfully loaded Mixin Connector [io.izzel.arclight.common.mod.ArclightConnector]
[297月2025 17:05:23.398] [main/INFO] [Arclight/]: 核心 Mixin 配置已加载
[297月2025 17:05:23.398] [main/INFO] [Arclight/]: 优化 Mixin 配置已加载
[297月2025 17:05:23.400] [main/INFO] [cpw.mods.modlauncher.LaunchServiceHandler/MODLAUNCHER]: Launching target 'arclightserver' with arguments [-nogui]
[297月2025 17:05:23.406] [main/WARN] [mixin/]: Reference map 'universalgrid.refmap.json' for universalgrid.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.411] [main/WARN] [mixin/]: Reference map 'handcrafted-forge-1.20.1-forge-refmap.json' for handcrafted.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.413] [main/WARN] [mixin/]: Reference map 'yungsextras.refmap.json' for yungsextras.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.413] [main/WARN] [mixin/]: Reference map 'yungsextras.refmap.json' for yungsextras_forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.415] [main/WARN] [mixin/]: Reference map 'nitrogen_internals.refmap.json' for nitrogen_internals.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.418] [main/WARN] [mixin/]: Reference map 'EpheroLib-refmap.json' for epherolib.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.422] [main/WARN] [mixin/]: Reference map '${refmap_target}refmap.json' for corgilib.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.429] [main/WARN] [mixin/]: Reference map 'tempad-forge-1.20.1-forge-refmap.json' for tempad.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.458] [main/INFO] [com.abdelaziz.saturn.common.Saturn/]: Loaded Saturn config file with 4 configurable options
[297月2025 17:05:23.490] [main/WARN] [mixin/]: Reference map 'cristellib-forge-refmap.json' for cristellib.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.526] [main/WARN] [mixin/]: Reference map 'naturalist-forge-forge-refmap.json' for naturalist.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.526] [main/WARN] [mixin/]: Reference map 'cookingforblockheads.refmap.json' for cookingforblockheads.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.538] [main/WARN] [mixin/]: Reference map 'trashslot.refmap.json' for trashslot.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.545] [main/WARN] [mixin/]: Reference map 'bloodmagic.refmap.json' for bloodmagic.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.567] [main/WARN] [mixin/]: Reference map 'modonomicon.refmap.json' for modonomicon.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.567] [main/WARN] [mixin/]: Reference map 'modonomicon.refmap.json' for modonomicon.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.572] [main/WARN] [mixin/]: Reference map 'packetfixer-forge-forge-refmap.json' for packetfixer.forge.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 17:05:23.766] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:05:23.772] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:05:23.829] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/inventory/AnvilMenu
[297月2025 17:05:23.848] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:05:23.848] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:05:23.961] [main/ERROR] [net.minecraftforge.coremod.transformer.CoreModBaseTransformer/COREMOD]: Error occurred applying transform of coremod coremods/field_to_method.js function biome
java.lang.IllegalStateException: Field f_47437_ is not private and an instance field
	at net.minecraftforge.coremod.api.ASMAPI.redirectFieldToMethod(ASMAPI.java:1069) ~[coremods-5.2.4.jar%2388!/:?] {}
	at org.openjdk.nashorn.internal.scripts.Script$Recompilation$123$292A$\^eval\_.initializeCoreMod#transformer(<eval>:11) ~[?:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunctionData.invoke(ScriptFunctionData.java:648) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunction.invoke(ScriptFunction.java:513) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptRuntime.apply(ScriptRuntime.java:520) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.api.scripting.ScriptObjectMirror.call(ScriptObjectMirror.java:111) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at net.minecraftforge.coremod.NashornFactory.lambda$getFunction$0(NashornFactory.java:37) ~[coremods-5.2.4.jar%2388!/:5.2.4] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:22) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:14) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModBaseTransformer.transform(CoreModBaseTransformer.java:60) ~[coremods-5.2.4.jar%2388!/:?] {}
	at cpw.mods.modlauncher.TransformerHolder.transform(TransformerHolder.java:41) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.performVote(ClassTransformer.java:179) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:117) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.getMaybeTransformedClassBytes(ModuleClassLoader.java:250) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.buildTransformedClassNodeFor(TransformingClassLoader.java:58) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchPluginHandler.lambda$announceLaunch$10(LaunchPluginHandler.java:100) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.getClassNode(MixinLaunchPluginLegacy.java:222) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.getClassNode(MixinLaunchPluginLegacy.java:207) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.ClassInfo.forName(ClassInfo.java:2005) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.getTargetClass(MixinInfo.java:1017) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.readTargetClasses(MixinInfo.java:1007) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinInfo.parseTargets(MixinInfo.java:895) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinConfig.prepareMixins(MixinConfig.java:867) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinConfig.prepare(MixinConfig.java:775) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.prepareConfigs(MixinProcessor.java:539) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.select(MixinProcessor.java:462) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.checkSelect(MixinProcessor.java:438) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:290) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:250) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.service.modlauncher.MixinTransformationHandler.processClass(MixinTransformationHandler.java:131) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.processClass(MixinLaunchPluginLegacy.java:131) ~[mixin-0.8.5.jar%2399!/:0.8.5+Jenkins-b310.git-155314e6e91465dad727e621a569906a410cd6f4] {}
	at cpw.mods.modlauncher.serviceapi.ILaunchPluginService.processClassWithFlags(ILaunchPluginService.java:156) ~[modlauncher-10.0.9.jar%2389!/:10.0.9+10.0.9+main.dcd20f30] {}
	at cpw.mods.modlauncher.LaunchPluginHandler.offerClassNodeToPlugins(LaunchPluginHandler.java:88) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:120) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.readerToClass(ModuleClassLoader.java:113) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.lambda$findClass$15(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadFromModule(ModuleClassLoader.java:229) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.findClass(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at java.lang.ClassLoader.loadClass(ClassLoader.java:638) ~[?:?] {}
	at java.lang.Class.forName(Class.java:625) ~[?:?] {}
	at java.lang.Class.forName(Class.java:600) ~[?:?] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler$DummyProvider.lambda$updateModuleReads$1(ImmediateWindowHandler.java:145) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at java.util.Optional.map(Optional.java:260) ~[?:?] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler$DummyProvider.updateModuleReads(ImmediateWindowHandler.java:145) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.ImmediateWindowHandler.acceptGameLayer(ImmediateWindowHandler.java:71) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.FMLLoader.beforeStart(FMLLoader.java:216) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:1.0] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.launchService(CommonLaunchHandler.java:92) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:05:24.074] [main/WARN] [mixin/]: Error loading class: net/dries007/tfc/common/blocks/rock/AqueductBlock (java.lang.ClassNotFoundException: net.dries007.tfc.common.blocks.rock.AqueductBlock)
[297月2025 17:05:24.074] [main/WARN] [mixin/]: @Mixin target net.dries007.tfc.common.blocks.rock.AqueductBlock was not found allthetweaks.mixins.json:FluiDucts
[297月2025 17:05:24.084] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Patching FishingHook#catchingFish
[297月2025 17:05:24.104] [main/WARN] [mixin/]: Error loading class: mcjty/theoneprobe/apiimpl/providers/DefaultProbeInfoProvider (java.lang.ClassNotFoundException: mcjty.theoneprobe.apiimpl.providers.DefaultProbeInfoProvider)
[297月2025 17:05:24.104] [main/WARN] [mixin/]: @Mixin target mcjty.theoneprobe.apiimpl.providers.DefaultProbeInfoProvider was not found mixins.endertanks.json:ET_HideFluidBars$_TheOneProbe
[297月2025 17:05:24.115] [main/WARN] [mixin/]: Error loading class: com/illusivesoulworks/diet/common/DietApiImpl (java.lang.ClassNotFoundException: com.illusivesoulworks.diet.common.DietApiImpl)
[297月2025 17:05:24.115] [main/WARN] [mixin/]: @Mixin target com.illusivesoulworks.diet.common.DietApiImpl was not found caupona.mixins.json:DietApiImplMixin
[297月2025 17:05:24.122] [main/WARN] [mixin/]: Error loading class: vazkii/quark/addons/oddities/inventory/BackpackMenu (java.lang.ClassNotFoundException: vazkii.quark.addons.oddities.inventory.BackpackMenu)
[297月2025 17:05:24.122] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/gui/screens/inventory/CraftingScreen (java.lang.ClassNotFoundException: net.minecraft.client.gui.screens.inventory.CraftingScreen)
[297月2025 17:05:24.122] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.gui.screens.inventory.CraftingScreen was not found fastbench.mixins.json:MixinCraftingScreen
[297月2025 17:05:24.130] [main/WARN] [mixin/]: Error loading class: shadows/placebo/patreon/TrailsManager (java.lang.ClassNotFoundException: shadows.placebo.patreon.TrailsManager)
[297月2025 17:05:24.130] [main/WARN] [mixin/]: @Mixin target shadows.placebo.patreon.TrailsManager was not found smsn.mixins.json:placebo.TrailsManagerMixin
[297月2025 17:05:24.131] [main/WARN] [mixin/]: Error loading class: shadows/placebo/patreon/WingsManager (java.lang.ClassNotFoundException: shadows.placebo.patreon.WingsManager)
[297月2025 17:05:24.131] [main/WARN] [mixin/]: @Mixin target shadows.placebo.patreon.WingsManager was not found smsn.mixins.json:placebo.WingsManagerMixin
[297月2025 17:05:24.188] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/multiplayer/MultiPlayerGameMode for invalid dist DEDICATED_SERVER
[297月2025 17:05:24.188] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/multiplayer/MultiPlayerGameMode (java.lang.RuntimeException: Attempted to load class net/minecraft/client/multiplayer/MultiPlayerGameMode for invalid dist DEDICATED_SERVER)
[297月2025 17:05:24.188] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.multiplayer.MultiPlayerGameMode was not found mixins.cofhcore.json:MultiPlayerGameModeMixin
[297月2025 17:05:24.254] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:05:24.254] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTreasureOnly() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:05:24.254] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTradeable() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:05:24.293] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/item/CreativeModeTabs
[297月2025 17:05:24.344] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/renderer/entity/PhantomRenderer (java.lang.ClassNotFoundException: net.minecraft.client.renderer.entity.PhantomRenderer)
[297月2025 17:05:24.344] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.renderer.entity.PhantomRenderer was not found mixins.deeperdarker.json:PhantomRendererMixin
[297月2025 17:05:24.361] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/gui/screens/inventory/AbstractContainerScreen for invalid dist DEDICATED_SERVER
[297月2025 17:05:24.362] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/gui/screens/inventory/AbstractContainerScreen (java.lang.RuntimeException: Attempted to load class net/minecraft/client/gui/screens/inventory/AbstractContainerScreen for invalid dist DEDICATED_SERVER)
[297月2025 17:05:24.362] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.gui.screens.inventory.AbstractContainerScreen was not found findme-common.mixins.json:MixinSlotRenderer
[297月2025 17:05:24.363] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/particle/ParticleEngine for invalid dist DEDICATED_SERVER
[297月2025 17:05:24.363] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/particle/ParticleEngine (java.lang.RuntimeException: Attempted to load class net/minecraft/client/particle/ParticleEngine for invalid dist DEDICATED_SERVER)
[297月2025 17:05:24.363] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.particle.ParticleEngine was not found findme-common.mixins.json:ParticleEngineAccessor
[297月2025 17:05:24.430] [main/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/resources/model/ModelBakery for invalid dist DEDICATED_SERVER
[297月2025 17:05:24.431] [main/WARN] [mixin/]: Error loading class: net/minecraft/client/resources/model/ModelBakery (java.lang.RuntimeException: Attempted to load class net/minecraft/client/resources/model/ModelBakery for invalid dist DEDICATED_SERVER)
[297月2025 17:05:24.431] [main/WARN] [mixin/]: @Mixin target net.minecraft.client.resources.model.ModelBakery was not found mixins.aae.json:client.ModelBakeryMixin
[297月2025 17:05:24.451] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:05:24.451] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isDiscoverable() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:05:24.800] [main/INFO] [MixinExtras|Service/]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.5.0).
[297月2025 17:05:24.971] [main/INFO] [mixin/]: Mixing server.MixinDedicatedServer from mixins/common/nochatreports.mixins.json into net.minecraft.server.dedicated.DedicatedServer
[297月2025 17:05:25.507] [main/WARN] [mixin/]: Method overwrite conflict for getThis in mixins.brandonscore.json:LivingEntityMixin, previously written by com.teammoeg.caupona.mixin.LivingEntityMixin. Skipping method.
[297月2025 17:05:25.528] [main/WARN] [mixin/]: @Redirect conflict. Skipping attributeslib.mixins.json:LivingEntityMixin->@Redirect::apoth_sunderingHasEffect(Lnet/minecraft/world/entity/LivingEntity;Lnet/minecraft/world/effect/MobEffect;)Z with priority 1000, already redirected by mixins.arclight.core.json:world.entity.LivingEntityMixin$ApotheosisCompatMixin->@Redirect::arclight$mutePotion(Lnet/minecraft/world/entity/LivingEntity;Lnet/minecraft/world/effect/MobEffect;)Z with priority 1500
[297月2025 17:05:25.727] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/item/CreativeModeTabs
[297月2025 17:05:25.873] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/experimental/module/GameNerfsModule
[297月2025 17:05:26.279] [main/WARN] [mixin/]: @Redirect conflict. Skipping securitycraft.mixins.json:camera.ServerPlayerMixin->@Redirect::securitycraft$tick(Lnet/minecraft/server/level/ServerPlayer;DDDFF)V with priority 1100, already redirected by railways-common.mixins.json:conductor_possession.ServerPlayerMixin->@Redirect::railways$securitycraft$tick(Lnet/minecraft/server/level/ServerPlayer;DDDFF)V with priority 1200
[297月2025 17:05:26.408] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_150430_ in mixins.arclight.core.json:world.inventory.AbstractContainerMenuMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:05:26.477] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 5 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/module/AncientTomesModule
[297月2025 17:05:26.490] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:05:26.491] [main/INFO] [net.minecraftforge.coremod.CoreMod.placebo/COREMODLOG]: Patching IForgeItemStack#getEnchantmentLevel
[297月2025 17:05:26.641] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 2 calls to Enchantment#getMaxLevel() in net/minecraft/world/inventory/AnvilMenu
[297月2025 17:05:26.644] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:05:26.644] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:05:26.644] [main/WARN] [mixin/]: @ModifyConstant conflict. Skipping mixins.arclight.core.json:world.inventory.RepairContainerMixin->@ModifyConstant::arclight$maximumRepairCost(I)I with priority 500, already redirected by apotheosis.mixins.json:AnvilMenuMixin->@ModifyConstant::apoth_removeLevelCap(I)I with priority 1000
[297月2025 17:05:26.879] [main/WARN] [mixin/]: Injection warning: LVT in net/minecraft/world/item/BoatItem::m_7203_(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;)Lnet/minecraft/world/InteractionResultHolder; has incompatible changes at opcode 158 in callback securitycraft.mixins.json:boat.BoatItemMixin->@Inject::securitycraft$maybeSetSecuritySeaBoatOwner(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/HitResult;Lnet/minecraft/world/phys/Vec3;DLjava/util/List;Lnet/minecraft/world/entity/vehicle/Boat;)V.
 Expected: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/HitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;]
    Found: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/BlockHitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;]
Available: [Lnet/minecraft/world/item/ItemStack;, Lnet/minecraft/world/phys/BlockHitResult;, Lnet/minecraft/world/phys/Vec3;, D, Ljava/util/List;, Lnet/minecraft/world/entity/vehicle/Boat;, Ljava/util/Iterator;, Lnet/minecraft/world/entity/Entity;, Lnet/minecraft/world/phys/AABB;]
[297月2025 17:05:27.278] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_147092_ in mixins.arclight.core.json:world.entity.ExperienceOrbMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:05:27.602] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_37547_ in mixins.arclight.core.json:world.entity.projectile.ThrownPotionMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[297月2025 17:05:27.673] [main/INFO] [mixin/]: Mixing common.MixinFriendlyByteBuf from mixins/common/nochatreports.mixins.json into net.minecraft.network.FriendlyByteBuf
[297月2025 17:05:27.673] [main/INFO] [mixin/]: Renaming synthetic method lambda$onWriteJsonWithCodec$1(Ljava/lang/Object;Ljava/lang/String;)Lio/netty/handler/codec/EncoderException; to md4aa8f0$lambda$onWriteJsonWithCodec$1$0 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[297月2025 17:05:27.673] [main/INFO] [mixin/]: Renaming synthetic method lambda$onReadJsonWithCodec$0(Ljava/lang/String;)Lio/netty/handler/codec/DecoderException; to md4aa8f0$lambda$onReadJsonWithCodec$0$1 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[297月2025 17:05:27.782] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Patching FishingHook#catchingFish
[297月2025 17:05:27.850] [main/INFO] [mixin/]: Mixing server.MixinServerGamePacketListenerImpl from mixins/common/nochatreports.mixins.json into net.minecraft.server.network.ServerGamePacketListenerImpl
[297月2025 17:05:28.134] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:05:28.134] [main/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isDiscoverable() in net/minecraft/world/level/storage/loot/functions/EnchantRandomlyFunction
[297月2025 17:05:28.453] [main/ERROR] [net.minecraftforge.coremod.transformer.CoreModBaseTransformer/COREMOD]: Error occurred applying transform of coremod coremods/field_to_method.js function biome
java.lang.IllegalStateException: Field f_47437_ is not private and an instance field
	at net.minecraftforge.coremod.api.ASMAPI.redirectFieldToMethod(ASMAPI.java:1069) ~[coremods-5.2.4.jar%2388!/:?] {}
	at org.openjdk.nashorn.internal.scripts.Script$Recompilation$123$292A$\^eval\_.initializeCoreMod#transformer(<eval>:11) ~[?:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunctionData.invoke(ScriptFunctionData.java:648) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptFunction.invoke(ScriptFunction.java:513) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.internal.runtime.ScriptRuntime.apply(ScriptRuntime.java:520) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at org.openjdk.nashorn.api.scripting.ScriptObjectMirror.call(ScriptObjectMirror.java:111) ~[nashorn-core-15.4.jar%23100!/:?] {}
	at net.minecraftforge.coremod.NashornFactory.lambda$getFunction$0(NashornFactory.java:37) ~[coremods-5.2.4.jar%2388!/:5.2.4] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:22) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModClassTransformer.runCoremod(CoreModClassTransformer.java:14) ~[coremods-5.2.4.jar%2388!/:?] {}
	at net.minecraftforge.coremod.transformer.CoreModBaseTransformer.transform(CoreModBaseTransformer.java:60) ~[coremods-5.2.4.jar%2388!/:?] {}
	at cpw.mods.modlauncher.TransformerHolder.transform(TransformerHolder.java:41) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.performVote(ClassTransformer.java:179) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:117) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.maybeTransformClassBytes(TransformingClassLoader.java:50) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.cl.ModuleClassLoader.readerToClass(ModuleClassLoader.java:113) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.lambda$findClass$15(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadFromModule(ModuleClassLoader.java:229) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.findClass(ModuleClassLoader.java:219) ~[securejarhandler-2.1.10.jar:?] {}
	at cpw.mods.cl.ModuleClassLoader.loadClass(ModuleClassLoader.java:135) ~[securejarhandler-2.1.10.jar:?] {}
	at java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?] {}
	at net.minecraft.world.level.biome.FixedBiomeSource.<clinit>(FixedBiomeSource.java:17) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:classloading,pl:accesstransformer:B}
	at net.minecraft.world.level.biome.BiomeSources.m_220586_(BiomeSources.java:8) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:classloading}
	at net.minecraft.core.registries.BuiltInRegistries.m_258029_(BuiltInRegistries.java:448) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.core.registries.BuiltInRegistries.m_258037_(BuiltInRegistries.java:462) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:986) ~[?:?] {}
	at net.minecraft.core.registries.BuiltInRegistries.m_257453_(BuiltInRegistries.java:461) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.core.registries.BuiltInRegistries.m_257498_(BuiltInRegistries.java:455) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:APP:botania_forge.mixins.json:BuiltInRegistriesForgeAccessor,pl:mixin:APP:terrablender.mixins.json:MixinBuiltInRegistries,pl:mixin:A}
	at net.minecraft.server.Bootstrap.m_135870_(BootstrapMixin.java:55) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,re:classloading,pl:mixin:APP:mixins.arclight.core.json:server.BootstrapMixin,pl:mixin:APP:ae2.mixins.json:EarlyStartupMixin,pl:mixin:A}
	at net.minecraft.server.Main.main(Main.java:121) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:05:28.780] [main/INFO] [com.almostreliable.merequester.MERequester/]: Registering content
[297月2025 17:05:28.956] [modloading-worker-0/INFO] [com.aetherteam.cumulus.Cumulus/]: Disabling Cumulus as it is on server.
[297月2025 17:05:28.976] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for industrialforegoing
[297月2025 17:05:28.977] [modloading-worker-0/INFO] [universalgrid/]: Loading config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:05:28.977] [modloading-worker-0/INFO] [universalgrid/]: Built config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:05:28.977] [modloading-worker-0/INFO] [universalgrid/]: Loaded config: /root/server/main/config/universalgrid-common.toml
[297月2025 17:05:28.978] [modloading-worker-0/INFO] [LowDragLib/]: LowDragLib is initializing on platform: Forge
[297月2025 17:05:28.984] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class PatchouliPlugin for plugin patchouli
[297月2025 17:05:28.984] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class CuriosPlugin for plugin curios
[297月2025 17:05:28.985] [modloading-worker-0/INFO] [PluginManager/]: Constructed class PatchouliPlugin for plugin patchouli for mod industrialforegoing
[297月2025 17:05:28.985] [modloading-worker-0/INFO] [PluginManager/]: Constructed class CuriosPlugin for plugin curios for mod industrialforegoing
[297月2025 17:05:28.985] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class PatchouliPlugin
[297月2025 17:05:28.989] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class CuriosPlugin
[297月2025 17:05:28.993] [modloading-worker-0/INFO] [create_new_age/]: Hello 1.20.1 Create!
[297月2025 17:05:28.994] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class PatchouliPlugin
[297月2025 17:05:28.994] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class CuriosPlugin
[297月2025 17:05:29.045] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 14 blocks registered.
[297月2025 17:05:29.046] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 29 items registered.
[297月2025 17:05:29.046] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 13 tiles registered.
[297月2025 17:05:29.046] [modloading-worker-0/INFO] [de.melanx.extradisks.ExtraDisks/]: 13 containers registered.
[297月2025 17:05:29.072] [modloading-worker-0/INFO] [mixin/]: Mixing server.MixinPlayerList from mixins/common/nochatreports.mixins.json into net.minecraft.server.players.PlayerList
[297月2025 17:05:29.097] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for sushigocrafting
[297月2025 17:05:29.103] [modloading-worker-0/INFO] [PluginManager/]: Found FeaturePluginInstance for class PatchouliPlugin for plugin patchouli
[297月2025 17:05:29.103] [modloading-worker-0/INFO] [PluginManager/]: Constructed class PatchouliPlugin for plugin patchouli for mod sushigocrafting
[297月2025 17:05:29.103] [modloading-worker-0/INFO] [PluginManager/]: Executing phase CONSTRUCTION for plugin class PatchouliPlugin
[297月2025 17:05:29.103] [modloading-worker-0/INFO] [PluginManager/]: Executing phase PRE_INIT for plugin class PatchouliPlugin
[297月2025 17:05:29.118] [modloading-worker-0/INFO] [Advanced Peripherals/]: AdvancedPeripherals says hello!
[297月2025 17:05:29.120] [modloading-worker-0/INFO] [net.bdew.generators.integration.ic2c.IC2CIntegration$/]: IC2 Not loaded, skipping integration
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Warped Netherlands
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Stalactite Caves
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Space Wars
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Shire
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Desert Oasis
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Pagoda
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Minecolonies Original
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Nordic Spruce
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Spruce
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Oak
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Dark Oak
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Medieval Birch
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Lost Mesa City
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Jungle Treehouse
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Incan
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Fortress
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Dark Oak Treehouse
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Colonial
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Cavern
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Caledonia
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Urban Birch
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Ancient Athens
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Urban Savanna
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Antique
[297月2025 17:05:29.169] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: High Magic
[297月2025 17:05:29.170] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Frontier
[297月2025 17:05:29.170] [Structurize IO Worker #0/WARN] [structurize/]: Missing Mod: byg for Pack: Corrupted
[297月2025 17:05:29.170] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Crimson Keep
[297月2025 17:05:29.170] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: FairyTale
[297月2025 17:05:29.170] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Functional Fantasy
[297月2025 17:05:29.170] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Underwater Base
[297月2025 17:05:29.170] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Aquatica
[297月2025 17:05:29.170] [Structurize IO Worker #0/INFO] [structurize/]: Registered structure pack: Steampunk
[297月2025 17:05:29.170] [Structurize IO Worker #0/WARN] [structurize/]: Failed loading packs from main folder path: .
[297月2025 17:05:29.170] [Structurize IO Worker #0/WARN] [structurize/]: Failed loading client packs from main folder path: .
[297月2025 17:05:29.170] [Structurize IO Worker #0/WARN] [structurize/]: Finished discovering Server Structure packs
[297月2025 17:05:29.172] [modloading-worker-0/INFO] [de.keksuccino.konkrete.Konkrete/]: [KONKRETE] Successfully initialized!
[297月2025 17:05:29.172] [modloading-worker-0/INFO] [de.keksuccino.konkrete.Konkrete/]: [KONKRETE] Server-side libs ready to use!
[297月2025 17:05:29.173] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:pufferfish_bucket is now minecraft:bucket.
[297月2025 17:05:29.173] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:salmon_bucket is now minecraft:bucket.
[297月2025 17:05:29.173] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:cod_bucket is now minecraft:bucket.
[297月2025 17:05:29.173] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:tropical_fish_bucket is now minecraft:bucket.
[297月2025 17:05:29.173] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:axolotl_bucket is now minecraft:bucket.
[297月2025 17:05:29.173] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:powder_snow_bucket is now minecraft:bucket.
[297月2025 17:05:29.173] [modloading-worker-0/INFO] [Bookshelf/]: Fixing MC-151457. Crafting remainder for minecraft:tadpole_bucket is now minecraft:bucket.
[297月2025 17:05:29.175] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class PatchouliPlugin
[297月2025 17:05:29.176] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class PatchouliPlugin
[297月2025 17:05:29.228] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for industrialforegoingsouls
[297月2025 17:05:29.229] [modloading-worker-0/INFO] [fr.samlegamer.mcwbiomesoplenty.McwBOP/]: Macaw's Biomes O' Plenty Loading...
[297月2025 17:05:29.239] [modloading-worker-0/INFO] [cy.jdkdigital.utilitarian.Utilitarian/]: setting up server config
[297月2025 17:05:29.258] [modloading-worker-0/INFO] [com.cupboard.Cupboard/]: Loaded config for: cupboard.json
[297月2025 17:05:29.262] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for powah
[297月2025 17:05:29.267] [modloading-worker-0/INFO] [voidscape/]: Starting Donator Handler
[297月2025 17:05:29.267] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for create
[297月2025 17:05:29.268] [modloading-worker-0/WARN] [defaultsettings/]: DefaultSettings is a client-side mod only! It won't do anything on servers!
[297月2025 17:05:29.268] [Voidscape Donator Loader/INFO] [voidscape/]: Loading donor data
[297月2025 17:05:29.270] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for mekanism
[297月2025 17:05:29.306] [Voidscape Donator Loader/ERROR] [voidscape/]: Could not load donor data
[297月2025 17:05:29.328] [modloading-worker-0/WARN] [Advanced Peripherals/]: Successfully loaded integration for botania
[297月2025 17:05:29.370] [modloading-worker-0/INFO] [fr.samlegamer.mcwbiomesoplenty.McwBOP/]: Macaw's Biomes O' Plenty Is Charged !
[297月2025 17:05:29.406] [modloading-worker-0/INFO] [net.permutated.pylons.Pylons/]: Registering mod: pylons
[297月2025 17:05:29.436] [modloading-worker-0/WARN] [de.keksuccino.justzoom.JustZoom/]: [JUST ZOOM] Disabling 'Just Zoom' since it's a client-side mod and current environment is server!
[297月2025 17:05:29.452] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for functionalstorage
[297月2025 17:05:29.456] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class PatchouliPlugin
[297月2025 17:05:29.456] [modloading-worker-0/INFO] [PluginManager/]: Executing phase INIT for plugin class CuriosPlugin
[297月2025 17:05:29.486] [modloading-worker-0/INFO] [net.permutated.pylons.Pylons/]: Registered 1 network packets
[297月2025 17:05:29.510] [modloading-worker-0/INFO] [net.bdew.generators.integration.mekanism.MekanismIntegration$/]: Mekanism loaded, activating integration
[297月2025 17:05:29.555] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id architectury:sync_ids
[297月2025 17:05:29.558] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/42a7ad70b1cc371599a0eff744096b8a
[297月2025 17:05:29.569] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/74a5e841822a3a87854ae896a33430d6
[297月2025 17:05:29.575] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/eb3d1e2748533430848cadf0f37c7e9c
[297月2025 17:05:29.583] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/91c8520f19f93b3e8b6a727568e194ab
[297月2025 17:05:29.586] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'multiblock' for mod 'bdlib'
[297月2025 17:05:29.587] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/8c2784d778293fd482ed84b8aa5fedb9
[297月2025 17:05:29.591] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/ea038224ea783d40b2863f52239e2604
[297月2025 17:05:29.592] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'misc' for mod 'bdlib'
[297月2025 17:05:29.601] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id artifacts:networking_channel/97ad64b7ecaf33209991c4f031501a58
[297月2025 17:05:29.603] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class PatchouliPlugin
[297月2025 17:05:29.603] [modloading-worker-0/INFO] [PluginManager/]: Executing phase POST_INIT for plugin class CuriosPlugin
[297月2025 17:05:29.629] [modloading-worker-0/INFO] [net.bdew.lib.BdLib$/]: Initialized multiblock manager for advgenerators
[297月2025 17:05:29.694] [modloading-worker-0/INFO] [net.bdew.lib.network.NetChannel/]: Initialized network channel 'generators' for mod 'advgenerators'
[297月2025 17:05:29.723] [modloading-worker-0/INFO] [net.permutated.novillagerdm.NoVillagerDeathMessages/]: Registering mod: novillagerdm
[297月2025 17:05:29.759] [modloading-worker-0/INFO] [cabletiers/]: Loading config: /root/server/main/config/cabletiers-common.toml
[297月2025 17:05:29.759] [modloading-worker-0/INFO] [cabletiers/]: Built config: /root/server/main/config/cabletiers-common.toml
[297月2025 17:05:29.761] [modloading-worker-0/INFO] [cabletiers/]: Loaded config: /root/server/main/config/cabletiers-common.toml
[297月2025 17:05:29.766] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Found 6 RS API injection points
[297月2025 17:05:29.766] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.ultramega.universalgrid.UniversalGrid RSAPI
[297月2025 17:05:29.766] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in net.gigabit101.rebornstorage.RebornStorage RSAPI
[297月2025 17:05:29.770] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.enderio.conduits.common.integrations.refinedstorage.RSTicker RSAPI
[297月2025 17:05:29.801] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.enderio.conduits.common.integrations.refinedstorage.RSNodeHost RSAPI
[297月2025 17:05:29.807] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in org.cyclops.integrateddynamicscompat.modcompat.refinedstorage.aspect.RefinedStorageAspects RS
[297月2025 17:05:29.813] [modloading-worker-0/INFO] [com.refinedmods.refinedstorage.apiimpl.API/]: Injected RS API in com.refinedmods.refinedstorageaddons.RSAddons RSAPI
[297月2025 17:05:29.875] [modloading-worker-0/INFO] [BrandonsCore/]: Knock Knock...
[297月2025 17:05:29.875] [modloading-worker-0/WARN] [draconicevolution/]: Reactor detonation initiated.
[297月2025 17:05:29.875] [modloading-worker-0/INFO] [BrandonsCore/]: Wait... NO! What?
[297月2025 17:05:29.875] [modloading-worker-0/INFO] [BrandonsCore/]: Stop That! That's not how this works!
[297月2025 17:05:29.875] [modloading-worker-0/WARN] [draconicevolution/]: Calculating explosion ETA
[297月2025 17:05:29.875] [modloading-worker-0/INFO] [BrandonsCore/]: Ahh... NO... NONONO! DONT DO THAT!!! STOP THIS NOW!
[297月2025 17:05:29.875] [modloading-worker-0/WARN] [draconicevolution/]: **Explosion Imminent!!!**
[297月2025 17:05:29.875] [modloading-worker-0/INFO] [BrandonsCore/]: Well...... fork...
[297月2025 17:05:29.928] [modloading-worker-0/INFO] [cy.jdkdigital.dyenamicsandfriends.DyenamicsAndFriends/]: registerCompatBlocks
[297月2025 17:05:29.963] [modloading-worker-0/INFO] [Mystical Agriculture/]: Registered plugin: com.blakebr0.mysticalagriculture.lib.ModCorePlugin
[297月2025 17:05:29.971] [modloading-worker-0/INFO] [net.minecraftforge.common.ForgeMod/FORGEMOD]: Forge mod loading, version 47.4.4, for MC 1.20.1 with MCP 20230612.114412
[297月2025 17:05:29.971] [modloading-worker-0/INFO] [net.minecraftforge.common.MinecraftForge/FORGE]: MinecraftForge v47.4.4 Initialized
[297月2025 17:05:29.972] [modloading-worker-0/INFO] [Mystical Agriculture/]: Registered plugin: com.blakebr0.mysticalcustomization.lib.ModCorePlugin
[297月2025 17:05:29.976] [modloading-worker-0/INFO] [Mystical Agriculture/]: Registered plugin: com.blakebr0.mysticalagradditions.lib.ModCorePlugin
[297月2025 17:05:29.977] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id powah:packet
[297月2025 17:05:29.987] [modloading-worker-0/WARN] [ironfurnaces.IronFurnaces/]: You have disabled Iron Furnaces's Update Checker, to re-enable: change the value of Update Checker in .minecraft->config->ironfurnaces-client.toml to 'true'.
[297月2025 17:05:30.030] [modloading-worker-0/INFO] [Mystical Agriculture/]: Loaded 3 plugins
[297月2025 17:05:30.033] [modloading-worker-0/INFO] [NoChatReports/]: KONNICHIWA ZA WARUDO!
[297月2025 17:05:30.033] [modloading-worker-0/INFO] [NoChatReports/]: Default JVM text encoding is: UTF-8
[297月2025 17:05:30.037] [modloading-worker-0/INFO] [NoChatReports/]: Reading config file NoChatReports/NCR-Common.json...
[297月2025 17:05:30.040] [modloading-worker-0/INFO] [NoChatReports/]: Writing config file NoChatReports/NCR-Common.json...
[297月2025 17:05:30.103] [Modding Legacy/blue_skies/Supporters thread/INFO] [ModdingLegacy/blue_skies/Supporter/]: Attempting to load the Modding Legacy supporters list from https://moddinglegacy.com/supporters-changelogs/supporters.txt
[297月2025 17:05:30.140] [modloading-worker-0/INFO] [de.melanx.aiotbotania.AIOTBotania/]: Items registered.
[297月2025 17:05:30.140] [modloading-worker-0/INFO] [de.melanx.aiotbotania.AIOTBotania/]: Blocks registered.
[297月2025 17:05:30.140] [modloading-worker-0/INFO] [de.melanx.aiotbotania.AIOTBotania/]: Global loot modifiers registered.
[297月2025 17:05:30.187] [modloading-worker-0/INFO] [plus.dragons.createdragonlib.DragonLib/]: Create: Dragon Lib 1.4.3 has initialized, ready to support your Create add-ons!
[297月2025 17:05:30.201] [modloading-worker-0/INFO] [Railways/]: Steam 'n' Rails v1.6.7 initializing! Commit hash: 0a7cc000d6c7a1419919bc3e315bc62932287eb9 on Create version: 0.5.1j on platform: Forge
[297月2025 17:05:30.268] [modloading-worker-0/INFO] [thedarkcolour.kotlinforforge.test.KotlinForForge/]: Kotlin For Forge Enabled!
[297月2025 17:05:30.280] [modloading-worker-0/INFO] [com.epherical.epherolib.config.CommonConfig/]: Creating default config file: croptopia_v3.conf
[297月2025 17:05:30.308] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:key_pressed
[297月2025 17:05:30.309] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:mode_changed
[297月2025 17:05:30.311] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:sync_config_to_server
[297月2025 17:05:30.319] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id findme:default/93a3df5fb37f371cb25c481f6e5b9323
[297月2025 17:05:30.319] [modloading-worker-0/INFO] [dev.ftb.mods.ftbultimine.FTBUltimine/]: FTB Ranks detected, listening for ranks events
[297月2025 17:05:30.320] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id findme:default/b71e26ec071d32928d9fb5d2c3ec8a7a
[297月2025 17:05:30.322] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id findme:default/1f516f929b313631ad555a7b42b1d39c
[297月2025 17:05:30.328] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.BetterChannel$PartialPacketBegin
[297月2025 17:05:30.330] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftblibrary:edit_nbt_response
[297月2025 17:05:30.350] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:open_gui
[297月2025 17:05:30.351] [modloading-worker-0/INFO] [zeta/]: Doing super early config setup for zeta
[297月2025 17:05:30.352] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:update_settings
[297月2025 17:05:30.354] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:send_message
[297月2025 17:05:30.354] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:request_map_data
[297月2025 17:05:30.357] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:create_party
[297月2025 17:05:30.357] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:share_waypoint
[297月2025 17:05:30.357] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/61a49a35c5283c0aaa7b0dfea5b7a53a
[297月2025 17:05:30.357] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.BetterChannel$PartialPacketBegin
[297月2025 17:05:30.359] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.BetterChannel$PartialPacketData
[297月2025 17:05:30.360] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:player_gui_operation
[297月2025 17:05:30.365] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:request_chunk_change
[297月2025 17:05:30.366] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/3dd73fd88be832559bc3a3c4a2e63b4a
[297月2025 17:05:30.366] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.BetterChannel$PartialPacketData
[297月2025 17:05:30.367] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:teleport_from_map
[297月2025 17:05:30.368] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.C2SPacket$InitTPSProfile
[297月2025 17:05:30.369] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:sync_tx
[297月2025 17:05:30.371] [modloading-worker-0/INFO] [Void Totem/]: Loading up Void Totem (Forge)
[297月2025 17:05:30.375] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:update_force_load_expiry
[297月2025 17:05:30.375] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/b453c08f23b83b82b690c31bf8b2a45a
[297月2025 17:05:30.375] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.C2SPacket$InitTPSProfile
[297月2025 17:05:30.376] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:server_config_request
[297月2025 17:05:30.377] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.C2SPacket$RequestAvailability
[297月2025 17:05:30.383] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/0c662822629034678f5a8f0e70375315
[297月2025 17:05:30.383] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.C2SPacket$RequestAvailability
[297月2025 17:05:30.384] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilingStarted
[297月2025 17:05:30.390] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/2e815604a29b3a5cbfe0208e619d86c4
[297月2025 17:05:30.390] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilingStarted
[297月2025 17:05:30.391] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilingCompleted
[297月2025 17:05:30.394] [modloading-worker-0/INFO] [KubeJS/]: Loaded common.properties
[297月2025 17:05:30.395] [modloading-worker-0/INFO] [KubeJS/]: Loaded dev.properties
[297月2025 17:05:30.395] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/9ae83199b65a3768862799cc5e461a02
[297月2025 17:05:30.396] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilingCompleted
[297月2025 17:05:30.397] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilerInactive
[297月2025 17:05:30.400] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/ae8dfae1238e312397aa53e9d14c45ca
[297月2025 17:05:30.400] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilerInactive
[297月2025 17:05:30.402] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$ProfilingResult
[297月2025 17:05:30.404] [modloading-worker-0/INFO] [KubeJS/]: Looking for KubeJS plugins...
[297月2025 17:05:30.409] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/3a94cae7336038df9152bcb7b4b94825
[297月2025 17:05:30.409] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$ProfilingResult
[297月2025 17:05:30.409] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source kubejs
[297月2025 17:05:30.410] [modloading-worker-0/INFO] [ObservableNet/]: Registering class observable.net.S2CPacket$Availability
[297月2025 17:05:30.416] [modloading-worker-0/INFO] [Eidolon Repraised/]: Loaded [apotheosis] compatibility
[297月2025 17:05:30.416] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id itemfilters:main/14e7fa1454283aec8ae811ef844ada28
[297月2025 17:05:30.417] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id minecraft:channel/observable/c86a824e756d312396d1ba30e943e6aa
[297月2025 17:05:30.417] [modloading-worker-0/INFO] [ObservableNet/]: Registered class observable.net.S2CPacket$Availability
[297月2025 17:05:30.417] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.latvian.mods.kubejs.forge.BuiltinKubeJSForgeClientPlugin does not load on server side, skipping
[297月2025 17:05:30.417] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id itemfilters:main/8f6a899247753217b9d86ab427a2b279
[297月2025 17:05:30.417] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.latvian.mods.kubejs.integration.forge.gamestages.GameStagesIntegration does not have required mod gamestages loaded, skipping
[297月2025 17:05:30.418] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source ldlib
[297月2025 17:05:30.421] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source ftbxmodcompat
[297月2025 17:05:30.425] [modloading-worker-0/INFO] [Dungeon Crawl/]: Here we go! Launching Dungeon Crawl 2.3.15...
[297月2025 17:05:30.437] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.ftb.mods.ftbxmodcompat.ftbfiltersystem.kubejs.FFSKubeJSPlugin does not have required mod ftbfiltersystem loaded, skipping
[297月2025 17:05:30.440] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source gtceu
[297月2025 17:05:30.496] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:submit_task
[297月2025 17:05:30.498] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_reward
[297月2025 17:05:30.501] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:get_emergency_items
[297月2025 17:05:30.504] [modloading-worker-0/INFO] [GregTechCEu/]: GregTechCEu is initializing...
[297月2025 17:05:30.505] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_all_rewards
[297月2025 17:05:30.506] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_choice_reward
[297月2025 17:05:30.509] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_pinned
[297月2025 17:05:30.510] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_chapter_pinned
[297月2025 17:05:30.512] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_editing_mode
[297月2025 17:05:30.513] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:force_save
[297月2025 17:05:30.517] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:set_custom_image
[297月2025 17:05:30.518] [modloading-worker-0/INFO] [PluginManager/]: Scanning classes for titanium
[297月2025 17:05:30.525] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:task_screen_config_resp
[297月2025 17:05:30.525] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:change_progress
[297月2025 17:05:30.526] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:create_object
[297月2025 17:05:30.527] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:create_task_at
[297月2025 17:05:30.528] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:delete_object
[297月2025 17:05:30.529] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:edit_object
[297月2025 17:05:30.530] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_chapter
[297月2025 17:05:30.531] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_quest
[297月2025 17:05:30.532] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:change_chapter_group
[297月2025 17:05:30.538] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_chapter_group
[297月2025 17:05:30.540] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:copy_quest
[297月2025 17:05:30.541] [modloading-worker-0/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/root/server/main/libraries/net/minecraft/server/1.20.1-20230612.114412/server-1.20.1-20230612.114412-srg.jar%23963!/assets/.mcassetsroot' uses unexpected schema
[297月2025 17:05:30.541] [modloading-worker-0/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/root/server/main/libraries/net/minecraft/server/1.20.1-20230612.114412/server-1.20.1-20230612.114412-srg.jar%23963!/data/.mcassetsroot' uses unexpected schema
[297月2025 17:05:30.544] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:copy_chapter_image
[297月2025 17:05:30.545] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:sync_structures_request
[297月2025 17:05:30.553] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:request_team_data
[297月2025 17:05:30.561] [modloading-worker-0/INFO] [quark/]: Initializing TerraBlender underground biome compat
[297月2025 17:05:30.571] [modloading-worker-0/INFO] [com.Pdiddy973.AllTheCompressed.AllTheCompressed/]: Registering mod: allthecompressed
[297月2025 17:05:30.576] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source createoreexcavation
[297月2025 17:05:30.576] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source morejs
[297月2025 17:05:30.578] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source cucumber
[297月2025 17:05:30.579] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source ponderjs
[297月2025 17:05:30.579] [modloading-worker-0/INFO] [Railways/]: Registered bogey styles from railways
[297月2025 17:05:30.583] [modloading-worker-0/INFO] [KubeJS/]: Done in 178.0 ms
[297月2025 17:05:30.588] [modloading-worker-0/INFO] [com.Pdiddy973.AllTheCompressed.AllTheCompressed/]: Registering overlays for loaded mods: [allthemodium, alltheores, allthetweaks, botania, enderio, minecraft, powah, productivebees, supplementaries]
[297月2025 17:05:30.588] [modloading-worker-0/INFO] [com.Pdiddy973.AllTheCompressed.AllTheCompressed/]: Skipping overlays for absent mods: []
[297月2025 17:05:30.608] [modloading-worker-0/INFO] [Luminara/]: Luminara Mod 已加载
[297月2025 17:05:30.638] [modloading-worker-0/INFO] [dev.tonimatas.packetfixer.PacketFixer/]: Packet Fixer has been initialized successfully
[297月2025 17:05:30.639] [modloading-worker-0/INFO] [be.florens.expandability.ExpandAbility/]: ExpandAbility here, who dis?
[297月2025 17:05:30.639] [modloading-worker-0/INFO] [Luminara/]: Luminara 事件系统已注册
[297月2025 17:05:30.653] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised items.
[297月2025 17:05:30.676] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised blocks.
[297月2025 17:05:30.679] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised block entities.
[297月2025 17:05:30.781] [modloading-worker-0/INFO] [STDOUT/]: TEST
[297月2025 17:05:30.781] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised Applied Botanics integration.
[297月2025 17:05:30.782] [modloading-worker-0/INFO] [STDOUT/]: TEST2
[297月2025 17:05:30.809] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised Applied Mekanistics integration.
[297月2025 17:05:30.811] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/experimental/module/GameNerfsModule
[297月2025 17:05:30.811] [modloading-worker-0/INFO] [MEGA Cells/]: Initialised Ars Énergistique integration.
[297月2025 17:05:30.845] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#getMaxLevel() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:05:30.845] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTreasureOnly() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:05:30.845] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 1 calls to Enchantment#isTradeable() in net/minecraft/world/entity/npc/VillagerTrades$EnchantBookForEmeralds
[297月2025 17:05:30.883] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for Mekanism has been loaded
[297月2025 17:05:30.883] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for Applied Mekanistics has been loaded
[297月2025 17:05:30.883] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for AE2 Things has been loaded
[297月2025 17:05:30.883] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for Applied Botanics has been loaded
[297月2025 17:05:30.883] [modloading-worker-0/INFO] [com.the9grounds.aeadditions.LoggerKt/]: Integration for FTB Teams has been loaded
[297月2025 17:05:30.905] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Quests] Enabled KubeJS integration
[297月2025 17:05:30.909] [modloading-worker-0/INFO] [Railways/]: Registering data fixers
[297月2025 17:05:30.909] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Chunks] Enabled KubeJS integration
[297月2025 17:05:30.922] [modloading-worker-0/INFO] [com.mojang.datafixers.DataFixerBuilder/]: 0 Datafixer optimizations took 0 milliseconds
[297月2025 17:05:30.923] [modloading-worker-0/INFO] [Configuration/FileWatching]: Registered gtceu config for auto-sync function
[297月2025 17:05:30.942] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Hex Casting
[297月2025 17:05:30.942] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Oh The Biomes You'll Go
[297月2025 17:05:30.944] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Blue Skies
[297月2025 17:05:30.962] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Twilight Forest
[297月2025 17:05:30.966] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Biomes O' Plenty
[297月2025 17:05:30.967] [modloading-worker-0/INFO] [GregTechCEu/]: High-Tier is Disabled.
[297月2025 17:05:30.970] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Nature's Spirit
[297月2025 17:05:30.971] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Dreams and Desires
[297月2025 17:05:30.973] [modloading-worker-0/INFO] [Railways/]: Registering tracks for Quark
[297月2025 17:05:30.977] [modloading-worker-0/INFO] [Railways/]: Registering tracks for TerraFirmaCraft
[297月2025 17:05:31.014] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:t_corridor
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@5d7b374d
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:four_way_corridor_loot
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@122b150
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:four_way_corridor
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6ffab53f
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/ore_hold_1
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@769a2c70
[297月2025 17:05:31.028] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:straight_corridor
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@700f6104
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:overlapped_corridor
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@16c9323c
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:spiral_staircase
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@4d092227
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/water_way
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@7f6a4cd
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/mine_entrance
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@58ef4d9
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/mine_key
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@47d97318
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/pit
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@3e80a1e
[297月2025 17:05:31.029] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/corner_zombie_trap
[297月2025 17:05:31.030] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@23bd882a
[297月2025 17:05:31.030] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/split_road
[297月2025 17:05:31.030] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@63bdff51
[297月2025 17:05:31.030] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/station
[297月2025 17:05:31.030] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@16cca2f7
[297月2025 17:05:31.030] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/downward_tunnel
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@651c2f3b
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/junction_station
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@1af5f78e
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/downward_shaft
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@32314c05
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/nature_crossroad
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@12a7402
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/wolf_den
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@4d3d7c7f
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/ore_cavern
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@51ca62e9
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/straight_corridor
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@574e3e2
[297月2025 17:05:31.031] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/bent_corridor
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@6ea03f31
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/fourway_corridor
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@2dd053
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/library
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@37c0a713
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/armoury
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@210e66e7
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/farm
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@7f311ee0
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/portal_nether
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@55551538
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mini_dungeon/crypt
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@783e6331
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/challenge_tower
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@70f0f2b0
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/big_library
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@209faf6d
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_crane
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@5cf13322
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_library
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@31959c34
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_smithy
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@4b735198
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/tall_spiral
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@1a3e0bf1
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/small_arena
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@7b3bc6ea
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/antechamber
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@48518e9a
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/destroyed_end_portal
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@48f82bf5
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard/four_way_corridor_loot
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@61871633
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:t3_entrance
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@1231afde
[297月2025 17:05:31.032] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:standard_entrance
[297月2025 17:05:31.033] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@3e9d53fc
[297月2025 17:05:31.033] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:default_deadend
[297月2025 17:05:31.033] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@5ee591da
[297月2025 17:05:31.033] [modloading-worker-0/INFO] [STDOUT/]: Loading schematic: bloodmagic:mines/deadend
[297月2025 17:05:31.033] [modloading-worker-0/INFO] [STDOUT/]: Resulting dungeon: wayoftime.bloodmagic.structures.DungeonRoom@53d7d9ca
[297月2025 17:05:31.033] [modloading-worker-0/INFO] [STDOUT/]: # schematics: 42
[297月2025 17:05:31.039] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 5 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/module/AncientTomesModule
[297月2025 17:05:31.054] [modloading-worker-0/INFO] [quark-zeta/]: Discovered 164 modules to load.
[297月2025 17:05:31.054] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Abacus...
[297月2025 17:05:31.059] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Adjustable Chat...
[297月2025 17:05:31.059] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ambient Discs...
[297月2025 17:05:31.059] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ancient Tomes...
[297月2025 17:05:31.063] [modloading-worker-0/INFO] [net.minecraftforge.coremod.CoreMod.apotheosis/COREMODLOG]: Replaced 3 calls to Enchantment#getMaxLevel() in org/violetmoon/quark/content/tools/item/AncientTomeItem
[297月2025 17:05:31.070] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ancient Wood...
[297月2025 17:05:31.080] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Armed Armor Stands...
[297月2025 17:05:31.080] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Auto Walk Keybind...
[297月2025 17:05:31.080] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Automatic Recipe Unlock...
[297月2025 17:05:31.080] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Automatic Tool Restock...
[297月2025 17:05:31.082] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Azalea Wood...
[297月2025 17:05:31.082] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Back Button Keybind...
[297月2025 17:05:31.083] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Backpack...
[297月2025 17:05:31.086] [modloading-worker-0/INFO] [pneumaticcraft/]: Thirdparty integration activated for [cofh_core,immersiveengineering,computercraft,jei,botania,mekanism,curios,patchouli,create]
[297月2025 17:05:31.087] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Beacon Redirection...
[297月2025 17:05:31.088] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Better Elytra Rocket...
[297月2025 17:05:31.088] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Big Stone Clusters...
[297月2025 17:05:31.097] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Loading Rhino Minecraft remapper...
[297月2025 17:05:31.098] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Blossom Trees...
[297月2025 17:05:31.104] [modloading-worker-0/INFO] [dev.latvian.mods.rhino.mod.util.RhinoProperties/]: Rhino properties loaded.
[297月2025 17:05:31.105] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Bottled Cloud...
[297月2025 17:05:31.106] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Loading mappings for 1.20.1
[297月2025 17:05:31.107] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Buckets Show Inhabitants...
[297月2025 17:05:31.107] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Camera...
[297月2025 17:05:31.109] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Campfires Boost Elytra...
[297月2025 17:05:31.110] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Celebratory Lamps...
[297月2025 17:05:31.110] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chains Connect Blocks...
[297月2025 17:05:31.110] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chest Searching...
[297月2025 17:05:31.110] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chorus Vegetation...
[297月2025 17:05:31.112] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chute...
[297月2025 17:05:31.117] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Climate Control Remover...
[297月2025 17:05:31.117] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Color Runes...
[297月2025 17:05:31.119] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Compasses Work Everywhere...
[297月2025 17:05:31.119] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Compressed Blocks...
[297月2025 17:05:31.120] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Coral On Cactus...
[297月2025 17:05:31.120] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Corundum...
[297月2025 17:05:31.136] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crabs...
[297月2025 17:05:31.138] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crafter...
[297月2025 17:05:31.139] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crate...
[297月2025 17:05:31.140] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Diamond Repair...
[297月2025 17:05:31.141] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dispensers Place Blocks...
[297月2025 17:05:31.141] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Double Door Opening...
[297月2025 17:05:31.141] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dragon Scales...
[297月2025 17:05:31.141] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Duskbound Blocks...
[297月2025 17:05:31.141] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dyeable Item Frames...
[297月2025 17:05:31.143] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Easy Transfering...
[297月2025 17:05:31.143] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Elytra Indicator...
[297月2025 17:05:31.143] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Emotes...
[297月2025 17:05:31.143] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enchantment Predicates...
[297月2025 17:05:31.143] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enchantments Begone...
[297月2025 17:05:31.143] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ender Watcher...
[297月2025 17:05:31.145] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Endermosh Music Disc...
[297月2025 17:05:31.149] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enhanced Ladders...
[297月2025 17:05:31.149] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Expanded Item Interactions...
[297月2025 17:05:31.150] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Fairy Rings...
[297月2025 17:05:31.150] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Fallen Logs...
[297月2025 17:05:31.151] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Feeding Trough...
[297月2025 17:05:31.153] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Forgotten...
[297月2025 17:05:31.159] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Foxhound...
[297月2025 17:05:31.159] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Done in 0.062 s
[297月2025 17:05:31.169] [modloading-worker-0/WARN] [mixin/]: Method overwrite conflict for getPalettes in immersiveengineering.mixins.json:accessors.TemplateAccess, previously written by com.almostreliable.morejs.mixin.structure.StructureTemplateMixin. Skipping method.
[297月2025 17:05:31.171] [modloading-worker-0/WARN] [mixin/]: Method overwrite conflict for getPalettes in ars_nouveau.mixins.json:structure.StructureTemplateAccessor, previously written by com.almostreliable.morejs.mixin.structure.StructureTemplateMixin. Skipping method.
[297月2025 17:05:31.171] [modloading-worker-0/WARN] [mixin/]: Method overwrite conflict for getPalettes in structure_gel.mixins.json:StructureTemplateAccessor, previously written by com.almostreliable.morejs.mixin.structure.StructureTemplateMixin. Skipping method.
[297月2025 17:05:31.173] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Framed Glass...
[297月2025 17:05:31.173] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Game Nerfs...
[297月2025 17:05:31.173] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glass Item Frame...
[297月2025 17:05:31.176] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glass Shard...
[297月2025 17:05:31.177] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glimmering Weald...
[297月2025 17:05:31.228] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gold Bars...
[297月2025 17:05:31.228] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gold Tools Have Fortune...
[297月2025 17:05:31.228] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Grab Chickens...
[297月2025 17:05:31.228] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Grate...
[297月2025 17:05:31.229] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gravisand...
[297月2025 17:05:31.230] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Greener Grass...
[297月2025 17:05:31.230] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hedges...
[297月2025 17:05:31.231] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hoe Harvesting...
[297月2025 17:05:31.231] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hollow Logs...
[297月2025 17:05:31.231] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Horses Swim...
[297月2025 17:05:31.231] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hotbar Changer...
[297月2025 17:05:31.231] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Improved Sponges...
[297月2025 17:05:31.231] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Improved Tooltips...
[297月2025 17:05:31.231] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Industrial Palette...
[297月2025 17:05:31.232] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Inventory Sorting...
[297月2025 17:05:31.232] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Iron Rod...
[297月2025 17:05:31.235] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Item Sharing...
[297月2025 17:05:31.235] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Japanese Palette...
[297月2025 17:05:31.235] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Leaf Carpet...
[297月2025 17:05:31.236] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Lock Rotation...
[297月2025 17:05:31.236] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Long Range Pick Block...
[297月2025 17:05:31.236] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Magma Keeps Concrete Powder...
[297月2025 17:05:31.236] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Magnets...
[297月2025 17:05:31.237] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Map Washing...
[297月2025 17:05:31.237] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Matrix Enchanting...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Metal Buttons...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Microcrafting Helper...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Midori...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Monster Box...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Banner Layers...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Brick Types...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Mud Blocks...
[297月2025 17:05:31.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Note Block Sounds...
[297月2025 17:05:31.241] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Potted Plants...
[297月2025 17:05:31.241] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Villagers...
[297月2025 17:05:31.241] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Narrator Readout...
[297月2025 17:05:31.241] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Nether Brick Fence Gate...
[297月2025 17:05:31.241] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Nether Obsidian Spikes...
[297月2025 17:05:31.241] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module New Stone Types...
[297月2025 17:05:31.242] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module No Durability On Cosmetics...
[297月2025 17:05:31.242] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module No More Lava Pockets...
[297月2025 17:05:31.242] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Obsidian Plate...
[297月2025 17:05:31.243] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Overlay Shader...
[297月2025 17:05:31.243] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Parrot Eggs...
[297月2025 17:05:31.243] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pat The Dogs...
[297月2025 17:05:31.244] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pathfinder Maps...
[297月2025 17:05:31.244] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Permafrost...
[297月2025 17:05:31.244] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Petals On Water...
[297月2025 17:05:31.246] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pickarang...
[297月2025 17:05:31.247] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pig Litters...
[297月2025 17:05:31.247] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pipes...
[297月2025 17:05:31.249] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pistons Move Tile Entities...
[297月2025 17:05:31.249] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Poison Potato Usage...
[297月2025 17:05:31.249] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Quick Armor Swapping...
[297月2025 17:05:31.249] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Rainbow Lamps...
[297月2025 17:05:31.249] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Raw Metal Bricks...
[297月2025 17:05:31.249] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Reacharound Placing...
[297月2025 17:05:31.250] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Redstone Randomizer...
[297月2025 17:05:31.250] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Renewable Spore Blossoms...
[297月2025 17:05:31.250] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Replace Scaffolding...
[297月2025 17:05:31.250] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Rope...
[297月2025 17:05:31.251] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Safer Creatures...
[297月2025 17:05:31.251] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Seed Pouch...
[297月2025 17:05:31.253] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shear Vines...
[297月2025 17:05:31.255] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shiba...
[297月2025 17:05:31.256] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shingles...
[297月2025 17:05:31.256] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shulker Packing...
[297月2025 17:05:31.256] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Simple Harvest...
[297月2025 17:05:31.256] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Skull Pikes...
[297月2025 17:05:31.257] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slabs To Blocks...
[297月2025 17:05:31.257] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slime In A Bucket...
[297月2025 17:05:31.258] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slimes To Magma Cubes...
[297月2025 17:05:31.258] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Snow Golem Player Heads...
[297月2025 17:05:31.258] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Soul Candles...
[297月2025 17:05:31.258] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Soul Sandstone...
[297月2025 17:05:31.258] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Spawner Replacer...
[297月2025 17:05:31.258] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Spiral Spires...
[297月2025 17:05:31.259] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Stonelings...
[297月2025 17:05:31.259] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Stools...
[297月2025 17:05:31.259] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Sturdy Stone...
[297月2025 17:05:31.259] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Thatch...
[297月2025 17:05:31.260] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Tiny Potato...
[297月2025 17:05:31.260] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Torch Arrow...
[297月2025 17:05:31.261] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Toretoise...
[297月2025 17:05:31.261] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Totem Of Holding...
[297月2025 17:05:31.261] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Trowel...
[297月2025 17:05:31.262] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Usage Ticker...
[297月2025 17:05:31.262] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Uses For Curses...
[297月2025 17:05:31.262] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Utility Recipes...
[297月2025 17:05:31.262] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Animal Textures...
[297月2025 17:05:31.262] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Bookshelves...
[297月2025 17:05:31.262] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Chests...
[297月2025 17:05:31.267] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Furnaces...
[297月2025 17:05:31.270] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Ladders...
[297月2025 17:05:31.270] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Selector...
[297月2025 17:05:31.271] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vertical Planks...
[297月2025 17:05:31.271] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vertical Slabs...
[297月2025 17:05:31.280] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vexes Die With Their Masters...
[297月2025 17:05:31.280] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Villager Rerolling Rework...
[297月2025 17:05:31.285] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Villagers Follow Emeralds...
[297月2025 17:05:31.285] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wooden Posts...
[297月2025 17:05:31.285] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wool Shuts Up Minecarts...
[297月2025 17:05:31.285] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wraith...
[297月2025 17:05:31.286] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Zombie Villagers On Normal...
[297月2025 17:05:31.286] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Stone Variants...
[297月2025 17:05:31.286] [modloading-worker-0/INFO] [quark-zeta/]: Constructed 164 modules.
[297月2025 17:05:31.310] [modloading-worker-0/INFO] [quark-zeta/]: Doing super early config setup for quark
[297月2025 17:05:31.339] [modloading-worker-0/INFO] [mixin/]: Mixing common.MixinServerStatus from mixins/common/nochatreports.mixins.json into net.minecraft.network.protocol.status.ServerStatus
[297月2025 17:05:31.399] [modloading-worker-0/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/gui/GuiGraphics for invalid dist DEDICATED_SERVER
[297月2025 17:05:31.400] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.lowdragmc.lowdraglib.gui.widget.PhantomFluidWidget: java.lang.RuntimeException: Attempted to load class net/minecraft/client/gui/GuiGraphics for invalid dist DEDICATED_SERVER
[297月2025 17:05:31.404] [modloading-worker-0/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class com/lowdragmc/lowdraglib/client/scene/WorldSceneRenderer for invalid dist DEDICATED_SERVER
[297月2025 17:05:31.404] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.lowdragmc.lowdraglib.gui.widget.SceneWidget: java.lang.RuntimeException: Attempted to load class com/lowdragmc/lowdraglib/client/scene/WorldSceneRenderer for invalid dist DEDICATED_SERVER
[297月2025 17:05:31.452] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.gregtechceu.gtceu.common.data.GTMaterialBlocks: java.lang.NoClassDefFoundError: net/minecraft/client/color/item/ItemColor
[297月2025 17:05:31.463] [modloading-worker-0/ERROR] [net.minecraftforge.fml.loading.RuntimeDistCleaner/DISTXFORM]: Attempted to load class net/minecraft/client/multiplayer/ClientLevel for invalid dist DEDICATED_SERVER
[297月2025 17:05:31.463] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.gregtechceu.gtceu.common.data.GTItems: java.lang.RuntimeException: Attempted to load class net/minecraft/client/multiplayer/ClientLevel for invalid dist DEDICATED_SERVER
[297月2025 17:05:31.465] [modloading-worker-0/ERROR] [STDERR/]: [Rhino] Failed to get declared methods for com.gregtechceu.gtceu.common.data.GTMaterialItems: java.lang.NoClassDefFoundError: net/minecraft/client/color/item/ItemColor
[297月2025 17:05:31.544] [modloading-worker-0/INFO] [KubeJS/]: Added bindings for script type STARTUP from mod 'almostunified': [AlmostUnified]
[297月2025 17:05:31.571] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#5: Loaded Java class 'net.minecraftforge.resource.ResourcePackLoader'
[297月2025 17:05:31.572] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#8: Loaded Java class 'java.util.stream.Collectors'
[297月2025 17:05:31.572] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#9: Loaded Java class 'net.minecraft.server.packs.repository.ServerPacksSource'
[297月2025 17:05:31.574] [modloading-worker-0/INFO] [KubeJS Startup/]: read_json_from_mod.js#10: Loaded Java class 'net.minecraft.server.packs.resources.FallbackResourceManager'
[297月2025 17:05:31.575] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:read_json_from_mod.js in 0.029 s
[297月2025 17:05:31.577] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:worldgen.js in 0.002 s
[297月2025 17:05:31.577] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:AE2/Universal_Press.js in 0.0 s
[297月2025 17:05:31.580] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#18: Loaded Java class 'mekanism.api.chemical.slurry.Slurry'
[297月2025 17:05:31.580] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#19: Loaded Java class 'mekanism.api.chemical.slurry.SlurryBuilder'
[297月2025 17:05:31.580] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#20: Loaded Java class 'mekanism.api.chemical.gas.Gas'
[297月2025 17:05:31.580] [modloading-worker-0/INFO] [KubeJS Startup/]: mekanismStartup.js#21: Loaded Java class 'mekanism.api.chemical.gas.GasBuilder'
[297月2025 17:05:31.580] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:mekanismStartup.js in 0.003 s
[297月2025 17:05:31.582] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/greenhouse.js#4: Loaded Java class 'dev.latvian.mods.kubejs.util.Tags'
[297月2025 17:05:31.582] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/greenhouse.js in 0.002 s
[297月2025 17:05:31.583] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/alcr.js in 0.001 s
[297月2025 17:05:31.583] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/miner.js in 0.0 s
[297月2025 17:05:31.606] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/mega_fusion_reactor.js#4: Loaded Java class 'com.gregtechceu.gtceu.common.machine.multiblock.electric.FusionReactorMachine'
[297月2025 17:05:31.607] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/mega_fusion_reactor.js in 0.024 s
[297月2025 17:05:31.609] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/gregstar_placeholders.js#4: Loaded Java class 'com.gregtechceu.gtceu.common.machine.multiblock.part.RotorHolderPartMachine'
[297月2025 17:05:31.609] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/gregstar_placeholders.js in 0.002 s
[297月2025 17:05:31.611] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#4: Loaded Java class 'com.gregtechceu.gtceu.api.data.chemical.material.properties.FluidProperty'
[297月2025 17:05:31.612] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#5: Loaded Java class 'com.gregtechceu.gtceu.api.data.chemical.material.properties.OreProperty'
[297月2025 17:05:31.612] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#6: Loaded Java class 'com.gregtechceu.gtceu.api.fluids.FluidBuilder'
[297月2025 17:05:31.614] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/material_modification.js#7: Loaded Java class 'com.gregtechceu.gtceu.api.fluids.store.FluidStorageKeys'
[297月2025 17:05:31.614] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/material_modification.js in 0.005 s
[297月2025 17:05:31.615] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/starforge.js in 0.001 s
[297月2025 17:05:31.615] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/neocube.js in 0.0 s
[297月2025 17:05:31.618] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/micro_universe_orb.js#4: Loaded Java class 'com.gregtechceu.gtceu.common.machine.multiblock.part.EnergyHatchPartMachine'
[297月2025 17:05:31.619] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/micro_universe_orb.js#5: Loaded Java class 'com.gregtechceu.gtceu.api.capability.recipe.IO'
[297月2025 17:05:31.619] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/micro_universe_orb.js in 0.004 s
[297月2025 17:05:31.625] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/ore_processing_plant.js#4: Loaded Java class 'net.minecraft.sounds.SoundEvents'
[297月2025 17:05:31.625] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/ore_processing_plant.js#5: Loaded Java class 'net.minecraft.sounds.SoundSource'
[297月2025 17:05:31.625] [modloading-worker-0/INFO] [KubeJS Startup/]: gtceu/ore_processing_plant.js#6: Loaded Java class 'com.gregtechceu.gtceu.api.sound.ExistingSoundEntry'
[297月2025 17:05:31.625] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/ore_processing_plant.js in 0.006 s
[297月2025 17:05:31.626] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/neural_node.js in 0.001 s
[297月2025 17:05:31.626] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:gtceu/apiary.js in 0.0 s
[297月2025 17:05:31.627] [modloading-worker-0/INFO] [KubeJS Startup/]: mysticalagriculture.js#4: Loaded Java class 'com.blakebr0.mysticalagriculture.api.MysticalAgricultureAPI'
[297月2025 17:05:31.627] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:mysticalagriculture.js in 0.001 s
[297月2025 17:05:31.629] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:settings.js in 0.002 s
[297月2025 17:05:31.629] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:mining_dim_layers.js in 0.0 s
[297月2025 17:05:31.630] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:custom_additions.js in 0.001 s
[297月2025 17:05:31.631] [modloading-worker-0/INFO] [KubeJS Startup/]: farmingForBlockheads.js#11: Loaded Java class 'net.blay09.mods.farmingforblockheads.api.FarmingForBlockheadsAPI'
[297月2025 17:05:31.632] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:farmingForBlockheads.js in 0.002 s
[297月2025 17:05:31.634] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded 21/21 KubeJS startup scripts in 0.473 s with 0 errors and 0 warnings
[297月2025 17:05:31.662] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id kubejs:send_data_from_client
[297月2025 17:05:31.663] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id kubejs:first_click
[297月2025 17:05:31.668] [main/INFO] [GregTechCEu/]: GTCEu common proxy init!
[297月2025 17:05:31.690] [main/INFO] [GregTechCEu/]: Registering material registries
[297月2025 17:05:31.692] [main/INFO] [GregTechCEu/]: Registering GTCEu Materials
[297月2025 17:05:31.747] [main/INFO] [GregTechCEu/]: Registering addon Materials
[297月2025 17:05:31.788] [main/WARN] [GregTechCEu/]: FluidStorageKey{gtceu:liquid} already has an associated fluid for material gtceu:water
[297月2025 17:05:31.798] [main/WARN] [GregTechCEu/]: FluidStorageKey{gtceu:liquid} already has an associated fluid for material gtceu:lava
[297月2025 17:05:31.798] [main/WARN] [GregTechCEu/]: FluidStorageKey{gtceu:liquid} already has an associated fluid for material gtceu:milk
[297月2025 17:05:32.051] [main/ERROR] [KubeJS Startup/]: gtceu/miner.js#34: Error in 'GTCEuStartupEvents.registry': TypeError: Cannot find function workableCasingRenderer in object com.gregtechceu.gtceu.api.registry.registrate.MultiblockMachineBuilder@f885c72.
[297月2025 17:05:32.052] [main/ERROR] [net.minecraftforge.fml.DeferredWorkQueue/]: Mod 'gtceu' encountered an error in a deferred task:
dev.latvian.mods.rhino.EcmaError: TypeError: Cannot find function workableTieredHullRenderer in object com.gregtechceu.gtceu.api.registry.registrate.MachineBuilder@b7dbbc6. (startup_scripts:gtceu/miner.js#9)
	at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3041) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3028) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.ScriptRuntime.typeError(ScriptRuntime.java:3049) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.ScriptRuntime.typeError2(ScriptRuntime.java:3064) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.ScriptRuntime.notFunctionError(ScriptRuntime.java:3119) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThisHelper(ScriptRuntime.java:1792) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThis(ScriptRuntime.java:1775) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.Interpreter.interpretLoop(Interpreter.java:869) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.Interpreter.interpret(Interpreter.java:370) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:72) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.Context.doTopCall(Context.java:1374) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:70) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.ArrowFunction.call(ArrowFunction.java:42) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.InterfaceAdapter.invoke(InterfaceAdapter.java:125) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at dev.latvian.mods.rhino.VMBridge.lambda$newInterfaceProxy$0(VMBridge.java:74) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
	at jdk.proxy3.$Proxy128.apply(Unknown Source) ~[?:?] {}
	at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSTieredMachineBuilder.register(KJSTieredMachineBuilder.java:110) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
	at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:82) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
	at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:18) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
	at com.gregtechceu.gtceu.integration.kjs.GTRegistryInfo.registerFor(GTRegistryInfo.java:177) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
	at com.gregtechceu.gtceu.common.data.GTMachines.init(GTMachines.java:1052) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
	at com.gregtechceu.gtceu.common.CommonProxy.init(CommonProxy.java:144) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804) ~[?:?] {}
	at net.minecraftforge.fml.DeferredWorkQueue.lambda$makeRunnable$2(DeferredWorkQueue.java:81) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.DeferredWorkQueue.makeRunnable(DeferredWorkQueue.java:76) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.DeferredWorkQueue.lambda$runTasks$0(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.concurrent.ConcurrentLinkedDeque.forEach(ConcurrentLinkedDeque.java:1650) ~[?:?] {}
	at net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646) ~[?:?] {}
	at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482) ~[?:?] {}
	at net.minecraftforge.fml.ModWorkManager$SyncExecutor.driveOne(ModWorkManager.java:43) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModWorkManager$DrivenExecutor.drive(ModWorkManager.java:28) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:224) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$gatherAndInitializeMods$13(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.gatherAndInitializeMods(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:30) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:05:32.053] [main/FATAL] [net.minecraftforge.fml.DeferredWorkQueue/LOADING]: Synchronous work queue completed exceptionally in 385.3 ms, see suppressed exceptions for details:
java.lang.RuntimeException: null
	at net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:58) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646) ~[?:?] {}
	at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482) ~[?:?] {}
	at net.minecraftforge.fml.ModWorkManager$SyncExecutor.driveOne(ModWorkManager.java:43) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModWorkManager$DrivenExecutor.drive(ModWorkManager.java:28) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:224) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$gatherAndInitializeMods$13(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.gatherAndInitializeMods(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:30) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	Suppressed: dev.latvian.mods.rhino.EcmaError: TypeError: Cannot find function workableTieredHullRenderer in object com.gregtechceu.gtceu.api.registry.registrate.MachineBuilder@b7dbbc6. (startup_scripts:gtceu/miner.js#9)
		at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3041) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3028) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.typeError(ScriptRuntime.java:3049) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.typeError2(ScriptRuntime.java:3064) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.notFunctionError(ScriptRuntime.java:3119) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThisHelper(ScriptRuntime.java:1792) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThis(ScriptRuntime.java:1775) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Interpreter.interpretLoop(Interpreter.java:869) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Interpreter.interpret(Interpreter.java:370) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:72) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.doTopCall(Context.java:1374) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:70) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ArrowFunction.call(ArrowFunction.java:42) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterfaceAdapter.invoke(InterfaceAdapter.java:125) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.VMBridge.lambda$newInterfaceProxy$0(VMBridge.java:74) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at jdk.proxy3.$Proxy128.apply(Unknown Source) ~[?:?] {}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSTieredMachineBuilder.register(KJSTieredMachineBuilder.java:110) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:82) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:18) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.GTRegistryInfo.registerFor(GTRegistryInfo.java:177) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.common.data.GTMachines.init(GTMachines.java:1052) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.common.CommonProxy.init(CommonProxy.java:144) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804) ~[?:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.lambda$makeRunnable$2(DeferredWorkQueue.java:81) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.makeRunnable(DeferredWorkQueue.java:76) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.lambda$runTasks$0(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.util.concurrent.ConcurrentLinkedDeque.forEach(ConcurrentLinkedDeque.java:1650) ~[?:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
		at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646) ~[?:?] {}
		at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482) ~[?:?] {}
		at net.minecraftforge.fml.ModWorkManager$SyncExecutor.driveOne(ModWorkManager.java:43) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModWorkManager$DrivenExecutor.drive(ModWorkManager.java:28) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:224) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
		at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.lambda$gatherAndInitializeMods$13(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
		at net.minecraftforge.fml.ModLoader.gatherAndInitializeMods(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:30) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
		at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
		at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
		at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:05:32.053] [main/FATAL] [net.minecraftforge.fml.ModLoader/]: Encountered non-modloading exceptions!
java.util.concurrent.CompletionException: java.lang.RuntimeException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315) ~[?:?] {re:mixin,re:computing_frames}
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320) ~[?:?] {re:mixin,re:computing_frames}
	at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:649) ~[?:?] {}
	at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482) ~[?:?] {}
	at net.minecraftforge.fml.ModWorkManager$SyncExecutor.driveOne(ModWorkManager.java:43) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModWorkManager$DrivenExecutor.drive(ModWorkManager.java:28) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:224) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$gatherAndInitializeMods$13(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.gatherAndInitializeMods(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:30) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
Caused by: java.lang.RuntimeException
	at net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:58) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646) ~[?:?] {}
	... 33 more
	Suppressed: dev.latvian.mods.rhino.EcmaError: TypeError: Cannot find function workableTieredHullRenderer in object com.gregtechceu.gtceu.api.registry.registrate.MachineBuilder@b7dbbc6. (startup_scripts:gtceu/miner.js#9)
		at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3041) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3028) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.typeError(ScriptRuntime.java:3049) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.typeError2(ScriptRuntime.java:3064) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.notFunctionError(ScriptRuntime.java:3119) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThisHelper(ScriptRuntime.java:1792) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThis(ScriptRuntime.java:1775) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Interpreter.interpretLoop(Interpreter.java:869) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Interpreter.interpret(Interpreter.java:370) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:72) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.doTopCall(Context.java:1374) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:70) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ArrowFunction.call(ArrowFunction.java:42) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterfaceAdapter.invoke(InterfaceAdapter.java:125) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.VMBridge.lambda$newInterfaceProxy$0(VMBridge.java:74) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at jdk.proxy3.$Proxy128.apply(Unknown Source) ~[?:?] {}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSTieredMachineBuilder.register(KJSTieredMachineBuilder.java:110) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:82) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:18) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.GTRegistryInfo.registerFor(GTRegistryInfo.java:177) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.common.data.GTMachines.init(GTMachines.java:1052) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.common.CommonProxy.init(CommonProxy.java:144) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804) ~[?:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.lambda$makeRunnable$2(DeferredWorkQueue.java:81) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.makeRunnable(DeferredWorkQueue.java:76) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.lambda$runTasks$0(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.util.concurrent.ConcurrentLinkedDeque.forEach(ConcurrentLinkedDeque.java:1650) ~[?:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
		at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646) ~[?:?] {}
		at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482) ~[?:?] {}
		at net.minecraftforge.fml.ModWorkManager$SyncExecutor.driveOne(ModWorkManager.java:43) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModWorkManager$DrivenExecutor.drive(ModWorkManager.java:28) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:224) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
		at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.lambda$gatherAndInitializeMods$13(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
		at net.minecraftforge.fml.ModLoader.gatherAndInitializeMods(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:30) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
		at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
		at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
		at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:05:32.053] [main/ERROR] [net.minecraft.server.Main/FATAL]: Failed to start the minecraft server
java.util.concurrent.CompletionException: java.lang.RuntimeException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315) ~[?:?] {re:mixin,re:computing_frames}
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320) ~[?:?] {re:mixin,re:computing_frames}
	at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:649) ~[?:?] {}
	at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482) ~[?:?] {}
	at net.minecraftforge.fml.ModWorkManager$SyncExecutor.driveOne(ModWorkManager.java:43) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModWorkManager$DrivenExecutor.drive(ModWorkManager.java:28) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:224) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.ModLoader.lambda$gatherAndInitializeMods$13(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
	at net.minecraftforge.fml.ModLoader.gatherAndInitializeMods(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:30) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
Caused by: java.lang.RuntimeException
	at net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:58) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646) ~[?:?] {}
	... 33 more
	Suppressed: dev.latvian.mods.rhino.EcmaError: TypeError: Cannot find function workableTieredHullRenderer in object com.gregtechceu.gtceu.api.registry.registrate.MachineBuilder@b7dbbc6. (startup_scripts:gtceu/miner.js#9)
		at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3041) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.constructError(ScriptRuntime.java:3028) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.typeError(ScriptRuntime.java:3049) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.typeError2(ScriptRuntime.java:3064) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.notFunctionError(ScriptRuntime.java:3119) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThisHelper(ScriptRuntime.java:1792) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ScriptRuntime.getPropFunctionAndThis(ScriptRuntime.java:1775) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Interpreter.interpretLoop(Interpreter.java:869) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Interpreter.interpret(Interpreter.java:370) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:72) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.doTopCall(Context.java:1374) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterpretedFunction.call(InterpretedFunction.java:70) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.ArrowFunction.call(ArrowFunction.java:42) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.Context.callSync(Context.java:1357) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.InterfaceAdapter.invoke(InterfaceAdapter.java:125) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at dev.latvian.mods.rhino.VMBridge.lambda$newInterfaceProxy$0(VMBridge.java:74) ~[rhino-forge-2001.2.3-build.10.jar%23871!/:2001.2.3-build.10] {re:classloading}
		at jdk.proxy3.$Proxy128.apply(Unknown Source) ~[?:?] {}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSTieredMachineBuilder.register(KJSTieredMachineBuilder.java:110) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:82) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.builders.machine.KJSWrappingMachineBuilder.register(KJSWrappingMachineBuilder.java:18) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.integration.kjs.GTRegistryInfo.registerFor(GTRegistryInfo.java:177) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.common.data.GTMachines.init(GTMachines.java:1052) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at com.gregtechceu.gtceu.common.CommonProxy.init(CommonProxy.java:144) ~[gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332c.jar%23738!/:7.1.0-SNAPSHOT] {re:classloading}
		at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804) ~[?:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.lambda$makeRunnable$2(DeferredWorkQueue.java:81) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.makeRunnable(DeferredWorkQueue.java:76) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.lambda$runTasks$0(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.util.concurrent.ConcurrentLinkedDeque.forEach(ConcurrentLinkedDeque.java:1650) ~[?:?] {}
		at net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:60) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
		at java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646) ~[?:?] {}
		at java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482) ~[?:?] {}
		at net.minecraftforge.fml.ModWorkManager$SyncExecutor.driveOne(ModWorkManager.java:43) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModWorkManager$DrivenExecutor.drive(ModWorkManager.java:28) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.waitForTransition(ModLoader.java:224) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.lambda$dispatchAndHandleError$20(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.util.Optional.ifPresent(Optional.java:178) ~[?:?] {re:mixin}
		at net.minecraftforge.fml.ModLoader.dispatchAndHandleError(ModLoader.java:210) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.fml.ModLoader.lambda$gatherAndInitializeMods$13(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?] {re:mixin}
		at net.minecraftforge.fml.ModLoader.gatherAndInitializeMods(ModLoader.java:183) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
		at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:30) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
		at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
		at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
		at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
		at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
		at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
		at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
		at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
[297月2025 17:05:49.701] [Modding Legacy/blue_skies/Supporters thread/INFO] [ModdingLegacy/blue_skies/Supporter/]: Couldn't load the Modding Legacy supporters list. You may be offline or our website could be having issues. If you are a supporter, some cosmetic features may not work.
java.net.ConnectException: 连接超时
	at sun.nio.ch.Net.connect0(Native Method) ~[?:?] {}
	at sun.nio.ch.Net.connect(Net.java:589) ~[?:?] {}
	at sun.nio.ch.Net.connect(Net.java:578) ~[?:?] {}
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:583) ~[?:?] {}
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?] {}
	at java.net.Socket.connect(Socket.java:751) ~[?:?] {}
	at sun.security.ssl.SSLSocketImpl.connect(SSLSocketImpl.java:304) ~[?:?] {}
	at sun.security.ssl.BaseSSLSocketImpl.connect(BaseSSLSocketImpl.java:181) ~[?:?] {}
	at sun.net.NetworkClient.doConnect(NetworkClient.java:183) ~[?:?] {}
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:531) ~[?:?] {}
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:636) ~[?:?] {}
	at sun.net.www.protocol.https.HttpsClient.<init>(HttpsClient.java:264) ~[?:?] {}
	at sun.net.www.protocol.https.HttpsClient.New(HttpsClient.java:377) ~[?:?] {}
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.getNewHttpClient(AbstractDelegateHttpsURLConnection.java:193) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1252) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1138) ~[?:?] {}
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:179) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1690) ~[?:?] {}
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1614) ~[?:?] {}
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.getInputStream(HttpsURLConnectionImpl.java:223) ~[?:?] {}
	at com.legacy.blue_skies.MLSupporter$GetSupportersThread.run(MLSupporter.java:165) ~[blue_skies-1.20.1-1.3.31.jar%23621!/:1.3.31] {re:classloading}
