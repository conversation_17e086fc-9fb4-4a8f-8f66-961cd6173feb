[16:11:00] [INIT] KubeJS 2001.6.5-build.16; MC 2001 forge
[16:11:00] [INIT] Loaded plugins:
[16:11:00] [INIT] - dev.latvian.mods.kubejs.forge.BuiltinKubeJSForgePlugin
[16:11:00] [INIT] - com.lowdragmc.lowdraglib.kjs.LDLibKubeJSPlugin
[16:11:00] [INIT] - dev.ftb.mods.ftbxmodcompat.ftbquests.kubejs.KubeJSIntegration
[16:11:00] [INIT] - dev.ftb.mods.ftbxmodcompat.ftbchunks.kubejs.FTBChunksKubeJSPlugin
[16:11:00] [INIT] - dev.ftb.mods.ftbxmodcompat.ftbteams.kubejs.FTBTeamsKubeJSPlugin
[16:11:00] [INIT] - com.gregtechceu.gtceu.integration.kjs.GregTechKubeJSPlugin
[16:11:00] [INIT] - com.tom.createores.kubejs.KubeJSExcavation
[16:11:00] [INIT] - com.almostreliable.morejs.Plugin
[16:11:00] [INIT] - com.blakebr0.cucumber.compat.kubejs.CucumberKubeJSPlugin
[16:11:00] [INIT] - com.almostreliable.ponderjs.PonderJSPlugin
[16:11:00] [INFO] Loaded script server_scripts:postUnify/_before.js in 0.005 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/wires.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/dust.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/storage_blocks.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/ingots.js in 0.002 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/nuggets.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/raw_materials.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/plates.js in 0.002 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/rods.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:postUnify/gears.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/chemlib/craftRemove.js in 0.0 s
[16:11:00] [INFO] mods/gtceu/apiary_recipes.js#5: Loaded Java class 'dev.latvian.mods.kubejs.util.Tags'
[16:11:00] [INFO] mods/gtceu/apiary_recipes.js#6: Loaded Java class 'dev.latvian.mods.kubejs.fluid.FluidStackJS'
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/apiary_recipes.js in 0.005 s
[16:11:00] [INFO] Loaded script server_scripts:xpFluids.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/mini_portals.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/atm_alloy_tools.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/atm_star.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/patrick_star.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/music_discs.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/atm_shard.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/atm_star_creative.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/atm_alloys.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:modpack/att_items.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:miningDim.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:tags.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:gamemode_shortcuts.js in 0.002 s
[16:11:00] [INFO] Loaded script server_scripts:ore_removal.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:conflicts.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/reliquary/reliquary.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/allthemodium/allthemodium.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/croptopia/croptopia.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/evilcraft/evilcraft.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/draconic_evo/draconic_evo.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/ae/recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/entangled/recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/timeinabottle/recipes.js in 0.0 s
[16:11:00] [INFO] mods/chemlib/blocks.js#4: Loaded Java class 'com.smashingmods.chemlib.registry.ItemRegistry'
[16:11:00] [INFO] Loaded script server_scripts:mods/chemlib/blocks.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/bloodmagic/bmreinforced.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/bloodmagic/meteors.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/angelring/angelring.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/mob_grinding_utils/recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/sushigocrafting/sushigocrafting.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/refined/recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/quarryplus/quarrying.js in 0.0 s
[16:11:00] [INFO] mods/mysticalagriculture/crops.js#4: Loaded Java class 'com.blakebr0.mysticalagriculture.registry.CropRegistry'
[16:11:00] [INFO] Loaded script server_scripts:mods/mysticalagriculture/crops.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/mysticalagriculture/crafting.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/mysticalagriculture/cropRemove.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/cataclysm/cataclysm.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/immersiveengineering/immersiveengineering.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_osmium.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_platinum.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_silver.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_tin.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/create.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_uranium.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_lead.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_nickel.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/create/crushed_aluminum.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/ore_processing_plant_recipes.js in 0.005 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/miner.js in 0.001 s
[16:11:00] [INFO] mods/gtceu/mining_dim_ores.js#4: Loaded Java class 'com.gregtechceu.gtceu.api.data.worldgen.generator.veins.VeinedVeinGenerator'
[16:11:00] [INFO] mods/gtceu/mining_dim_ores.js#5: Loaded Java class 'com.gregtechceu.gtceu.api.data.worldgen.generator.veins.DikeVeinGenerator'
[16:11:00] [INFO] mods/gtceu/mining_dim_ores.js#6: Loaded Java class 'com.gregtechceu.gtceu.api.data.worldgen.generator.veins.CuboidVeinGenerator'
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/mining_dim_ores.js in 0.005 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/allthemodium.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/gtceu.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/micro_universe_orb_recipes.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/greenhouse_recipes.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/mega_fusion_recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/neural_node_recipes.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/gtceu/starforge_recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/computercraft/turtles.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/corail_tombstone/Nuke_Eggs.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/structure_compass/structure_compass.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/IndustrialForegoingSouls/recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/IndustrialForegoingSouls/blacklist.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/minecolonies/mineship.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/industrialforegoing/industrial_foregoing.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/industrialforegoing/fluid_extractor.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/rftools/builder.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/mekanism/mekanism.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/mekanism/mekanismServer.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/alchemistry/alchemistry.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/er2/extremereactors2.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/quark/Chests.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/mininggadgets/upgrade.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/buildinggadgets/recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/silent_gear/salvager.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/deepresonance/deepresonance.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/sophisticated/sophisticated.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/endermanoverhaul/tags.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/endermanoverhaul/recipes.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/ftbquests/customTask.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/hostileneuralnetworks/hnn.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/ars_nouveau/ars_nouveau.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:mods/everythingcopper/disable_shield.js in 0.0 s
[16:11:00] [INFO] mods/farmingforblockheads/market.js#62: Loaded Java class 'net.blay09.mods.farmingforblockheads.api.FarmingForBlockheadsAPI'
[16:11:00] [INFO] Loaded script server_scripts:mods/farmingforblockheads/market.js in 0.001 s
[16:11:00] [INFO] Loaded script server_scripts:mods/ad_astra/ad_astra.js in 0.0 s
[16:11:00] [INFO] Loaded script server_scripts:ore_processing.js in 0.0 s
[16:11:00] [INFO] Loaded 95/95 KubeJS server scripts in 0.065 s with 0 errors and 0 warnings
[16:11:00] [INFO] Scripts loaded
[16:11:00] [INFO] Initializing recipe event...
[16:11:02] [INFO] gamemode_shortcuts.js#9: [游戏模式简写] 正在注册 /gm 命令...
[16:11:02] [INFO] gamemode_shortcuts.js#100: [游戏模式简写] /gm 命令注册完成
[16:11:02] [INFO] [minecraft:entity_type] Found 242 tags, added 60 objects, removed 0 objects
[16:11:02] [INFO] [minecraft:fluid] Found 565 tags, added 3 objects, removed 4 objects
[16:11:02] [INFO] [minecraft:worldgen/biome] Found 864 tags, added 8 objects, removed 0 objects
[16:11:02] [INFO] [minecraft:block] Found 2868 tags, added 827 objects, removed 1 objects
[16:11:02] [INFO] [minecraft:item] Found 8540 tags, added 205 objects, removed 7 objects
[16:11:26] [INFO] Processing recipes...
[16:11:26] [WARN] Failed to parse recipe 'endersdelight:twisted_cereal_wood[minecraft:crafting_shapeless]'! Falling back to vanilla: ItemStack 'result' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_chestplate_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'caupona:stonecutter/sandstone_road_remove_curb[stonecutting]'! Falling back to vanilla: ItemStack 'result' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_hoe_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'twilightdelight:rainbow_ice_cream[minecraft:crafting_shapeless]'! Falling back to vanilla: ItemStack 'result' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'caupona:stonecutter/stone_road_remove_curb[stonecutting]'! Falling back to vanilla: ItemStack 'result' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'delightful:knives/smithing/resonarium_knife[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_shovel_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'gtceu:assembler/lantern[gtceu:assembler]'! Falling back to vanilla: Index 0 out of bounds for length 0
[16:11:26] [WARN] Failed to parse recipe 'caupona:stonecutter/felsic_tuff_road_remove_curb[stonecutting]'! Falling back to vanilla: ItemStack 'result' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_helmet_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'twilightdelight:refreshing_ice_cream[minecraft:crafting_shapeless]'! Falling back to vanilla: ItemStack 'result' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'twilightdelight:twilight_ice_cream[minecraft:crafting_shapeless]'! Falling back to vanilla: ItemStack 'result' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_pickaxe_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_boots_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'twilightdelight:teardrop_sword[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_sword_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_leggings_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [WARN] Failed to parse recipe 'deeperdarker:resonarium_axe_smithing[minecraft:smithing_transform]'! Falling back to vanilla: Ingredient 'template' can't be empty!
[16:11:26] [INFO] Found 100840 recipes in 737.6 ms
[16:11:30] [INFO] mods/gtceu/apiary_recipes.js#95: flower is null for kubejs:gtceu/apiary_ii/lepidolite
[16:11:30] [INFO] Posted recipe events in 3.895 s
[16:11:30] [WARN] Error parsing recipe twilightdelight:twilight_ice_cream[minecraft:crafting_shapeless]: {"type":"minecraft:crafting_shapeless","category":"misc","ingredients":[{"item":"twilightdelight:torchberry_ice_cream"},{"item":"neapolitan:chocolate_ice_cream"},{"item":"neapolitan:strawberry_ice_cream"},{"item":"minecraft:bowl"},{"item":"minecraft:bowl"},{"item":"minecraft:bowl"}],"result":{"count":3,"item":"twilightdelight:twilight_ice_cream"}}: Unknown item 'twilightdelight:torchberry_ice_cream'
[16:11:30] [WARN] Error parsing recipe twilightdelight:refreshing_ice_cream[minecraft:crafting_shapeless]: {"type":"minecraft:crafting_shapeless","category":"misc","ingredients":[{"item":"twilightdelight:glacier_ice_cream"},{"item":"neapolitan:mint_ice_cream"},{"item":"twilightdelight:phytochemical_ice_cream"},{"item":"minecraft:bowl"},{"item":"minecraft:bowl"},{"item":"minecraft:bowl"}],"result":{"count":3,"item":"twilightdelight:refreshing_ice_cream"}}: Unknown item 'twilightdelight:glacier_ice_cream'
[16:11:30] [WARN] Error parsing recipe forbidden_arcanus:edelwood_chest_boat[minecraft:crafting_shapeless]: {"type":"minecraft:crafting_shapeless","category":"misc","ingredients":[{"item":"forbidden_arcanus:edelwood_boat"},{"fabric_value":{"tag":"c:chests"},"forge_value":{"tag":"forge:chests/wooden"}}],"result":{"item":"forbidden_arcanus:edelwood_chest_boat"}}: An ingredient entry needs either a tag or an item
[16:11:30] [WARN] Error parsing recipe thermal:compat/biomesoplenty/tree_extractor_bop_maple[thermal:tree_extractor]: {"type":"thermal:tree_extractor","trunk":{"Name":"minecraft:oak_log"},"leaves":{"Name":"biomesoplenty:maple_leaves","Properties":{"persistent":"false"}},"sapling":"biomesoplenty:maple_sapling","min_height":4,"max_height":10,"min_leaves":16,"max_leaves":24,"result":{"fluid":"thermal:sap","amount":30},"conditions":[{"type":"thermal:flag","flag":"mod_biomesoplenty"}]}: Unknown block 'biomesoplenty:maple_leaves'
[16:11:30] [WARN] Error parsing recipe caupona:stonecutter/sandstone_road_remove_curb[minecraft:stonecutting]: {"type":"stonecutting","ingredient":{"item":"caupona:sandstone_road_side"},"result":{"id":"caupona:sandstone_road"},"count":1}: Expected result to be a string, was an object ({"id...d"})
[16:11:30] [WARN] Error parsing recipe twilightdelight:neapolitan/torchberry_cake_slice[farmersdelight:cutting]: {"type":"farmersdelight:cutting","ingredients":[{"item":"twilightdelight:torchberry_cake"}],"result":[{"count":7,"item":"twilightdelight:torchberry_cake_slice"}],"tool":{"tag":"forge:tools/knives"}}: Unknown item 'twilightdelight:torchberry_cake'
[16:11:30] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/9x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_9x","count":1},"result":{"item":"allthecompressed:deepslate_block_9x"},"energy":2400000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/9x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/1x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_1x","count":1},"result":{"item":"allthecompressed:deepslate_block_1x"},"energy":240000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/1x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/2x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_2x","count":1},"result":{"item":"allthecompressed:deepslate_block_2x"},"energy":480000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/2x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/3x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_3x","count":1},"result":{"item":"allthecompressed:deepslate_block_3x"},"energy":720000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/3x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/5x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_5x","count":1},"result":{"item":"allthecompressed:deepslate_block_5x"},"energy":1200000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/5x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/4x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_4x","count":1},"result":{"item":"allthecompressed:deepslate_block_4x"},"energy":960000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/4x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/6x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_6x","count":1},"result":{"item":"allthecompressed:deepslate_block_6x"},"energy":1440000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/6x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/7x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_7x","count":1},"result":{"item":"allthecompressed:deepslate_block_7x"},"energy":1680000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/7x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/stone/deepslate/8x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:cobbled_deepslate_block_8x","count":1},"result":{"item":"allthecompressed:deepslate_block_8x"},"energy":2160000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/stone/deepslate/8x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe caupona:stonecutter/felsic_tuff_road_remove_curb[minecraft:stonecutting]: {"type":"stonecutting","ingredient":{"item":"caupona:felsic_tuff_road_side"},"result":{"id":"caupona:felsic_tuff_road"},"count":1}: Expected result to be a string, was an object ({"id...d"})
[16:11:31] [WARN] Error parsing recipe twilightdelight:neapolitan/aurora_cake_slice[farmersdelight:cutting]: {"type":"farmersdelight:cutting","ingredients":[{"item":"twilightdelight:aurora_cake"}],"result":[{"count":7,"item":"twilightdelight:aurora_cake_slice"}],"tool":{"tag":"forge:tools/knives"}}: Unknown item 'twilightdelight:aurora_cake'
[16:11:31] [WARN] Error parsing recipe endersdelight:twisted_cereal_wood[minecraft:crafting_shapeless]: {"type":"minecraft:crafting_shapeless","ingredients":[{"tag":"endersdelight:enderman_sight"},{"tag":"endersdelight:enderman_loot"},{"item":"minecraft:popped_chorus_fruit"},{"item":"farmersdelight:milk_bottle"},{"item":"minecraft:bowl"}],"result":{"item":"endersdelight:twisted_cereal_wood","count":1}}: Unknown item 'endersdelight:twisted_cereal_wood'
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/1x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_1x","count":1},"result":{"item":"allthecompressed:terracotta_block_1x"},"energy":240000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/1x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/2x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_2x","count":1},"result":{"item":"allthecompressed:terracotta_block_2x"},"energy":480000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/2x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/5x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_5x","count":1},"result":{"item":"allthecompressed:terracotta_block_5x"},"energy":1200000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/5x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/6x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_6x","count":1},"result":{"item":"allthecompressed:terracotta_block_6x"},"energy":1440000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/6x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/3x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_3x","count":1},"result":{"item":"allthecompressed:terracotta_block_3x"},"energy":720000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/3x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/4x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_4x","count":1},"result":{"item":"allthecompressed:terracotta_block_4x"},"energy":960000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/4x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/9x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_9x","count":1},"result":{"item":"allthecompressed:terracotta_block_9x"},"energy":2400000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/9x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/7x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_7x","count":1},"result":{"item":"allthecompressed:terracotta_block_7x"},"energy":1680000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/7x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe thermal:furnace/allthecompressed/terracotta/8x[thermal:furnace]: {"type":"thermal:furnace","ingredient":{"item":"allthecompressed:clay_block_8x","count":1},"result":{"item":"allthecompressed:terracotta_block_8x"},"energy":2160000,"conditions":[{"type":"forge:and","values":[{"type":"forge:not","value":{"type":"forge:mod_loaded","modid":"compressium"}},{"type":"forge:mod_loaded","modid":"thermal"}]}]}: Invalid Thermal Series recipe: thermal:furnace/allthecompressed/terracotta/8x
Refer to the recipe's ResourceLocation to find the mod responsible and let them know!
[16:11:31] [WARN] Error parsing recipe twilightdelight:neapolitan/phytochemical_cake_slice[farmersdelight:cutting]: {"type":"farmersdelight:cutting","ingredients":[{"item":"twilightdelight:phytochemical_cake"}],"result":[{"count":7,"item":"twilightdelight:phytochemical_cake_slice"}],"tool":{"tag":"forge:tools/knives"}}: Unknown item 'twilightdelight:phytochemical_cake'
[16:11:31] [WARN] Error parsing recipe thermal:compat/biomesoplenty/tree_extractor_bop_yellow_autumn[thermal:tree_extractor]: {"type":"thermal:tree_extractor","trunk":{"Name":"minecraft:birch_log"},"leaves":{"Name":"biomesoplenty:yellow_autumn_leaves","Properties":{"persistent":"false"}},"sapling":"biomesoplenty:yellow_autumn_sapling","min_height":4,"max_height":12,"min_leaves":16,"max_leaves":24,"result":{"fluid":"thermal:sap","amount":15},"conditions":[{"type":"thermal:flag","flag":"mod_biomesoplenty"}]}: Unknown block 'biomesoplenty:yellow_autumn_leaves'
[16:11:31] [WARN] Error parsing recipe forbidden_arcanus:aurum_chest_boat[minecraft:crafting_shapeless]: {"type":"minecraft:crafting_shapeless","category":"misc","ingredients":[{"item":"forbidden_arcanus:aurum_boat"},{"fabric_value":{"tag":"c:chests"},"forge_value":{"tag":"forge:chests/wooden"}}],"result":{"item":"forbidden_arcanus:aurum_chest_boat"}}: An ingredient entry needs either a tag or an item
[16:11:31] [WARN] Error parsing recipe twilightdelight:rainbow_ice_cream[minecraft:crafting_shapeless]: {"type":"minecraft:crafting_shapeless","category":"misc","ingredients":[{"item":"twilightdelight:aurora_ice_cream"},{"item":"neapolitan:banana_ice_cream"},{"item":"neapolitan:adzuki_ice_cream"},{"item":"minecraft:bowl"},{"item":"minecraft:bowl"},{"item":"minecraft:bowl"}],"result":{"count":3,"item":"twilightdelight:rainbow_ice_cream"}}: Unknown item 'twilightdelight:aurora_ice_cream'
[16:11:31] [WARN] Error parsing recipe caupona:stonecutter/stone_road_remove_curb[minecraft:stonecutting]: {"type":"stonecutting","ingredient":{"item":"caupona:stone_road_side"},"result":{"id":"caupona:stone_road"},"count":1}: Expected result to be a string, was an object ({"id...d"})
[16:11:31] [WARN] Error parsing recipe twilightdelight:neapolitan/glacier_cake_slice[farmersdelight:cutting]: {"type":"farmersdelight:cutting","ingredients":[{"item":"twilightdelight:glacier_cake"}],"result":[{"count":7,"item":"twilightdelight:glacier_cake_slice"}],"tool":{"tag":"forge:tools/knives"}}: Unknown item 'twilightdelight:glacier_cake'
[16:11:31] [WARN] Error parsing recipe thermal:compat/biomesoplenty/tree_extractor_bop_orange_autumn[thermal:tree_extractor]: {"type":"thermal:tree_extractor","trunk":{"Name":"minecraft:dark_oak_log"},"leaves":{"Name":"biomesoplenty:orange_autumn_leaves","Properties":{"persistent":"false"}},"sapling":"biomesoplenty:orange_autumn_sapling","min_height":4,"max_height":10,"min_leaves":16,"max_leaves":24,"result":{"fluid":"thermal:resin","amount":15},"conditions":[{"type":"thermal:flag","flag":"mod_biomesoplenty"}]}: Unknown block 'biomesoplenty:orange_autumn_leaves'
[16:11:33] [INFO] Added 14433 recipes, removed 277 recipes, modified 12 recipes, with 34 failed recipes in 2.651 s
[16:11:50] [INFO] Server resource reload complete!
