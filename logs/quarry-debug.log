[2025-07-29T16:13:20,213][W] [TQW/initialLog]: Config in 'A Minecraft Server'
{
  "server": {
    "machineWork": {
      "unworkableDimensions": []
    }
  },
  "common": {
    "common": {
      "reduceMarkerGuideLineIfPlayerIsFar": false,
      "chunkDestroyerLimit": -1,
      "debug": false,
      "removeFrameAfterQuarryIsRemoved": false,
      "removeCommonMaterialsByCD": true,
      "flexMarkerMaxDistance": 256,
      "spawnerBlackList": [
        "minecraft:ender_dragon",
        "minecraft:wither",
        "minecraft:area_effect_cloud",
        "minecraft:item",
        "minecraft:player"
      ],
      "netherTop": 127,
      "removeFluidAfterFinishedByCD": false,
      "sfqEnergy": 2.0,
      "customPlayer": false,
      "noEnergy": false,
      "convertDeepslateOres": false,
      "allowWorkbenchExtraction": false,
      "allowWorkInClaimedChunkByFBTChunks": false,
      "enableChunkLoader": false,
      "logAllQuarryWork": false
    },
    "enableMap": {
      "mini_quarry": true,
      "spawner_controller": false,
      "fuel_module_normal": true,
      "exp_module": true,
      "pump_module": true,
      "filter_module": true,
      "remove_bedrock_module": false,
      "replacer": false,
      "replacer_module": false,
      "repeat_tick_module": false,
      "remote_placer": false,
      "quarry": true,
      "filler_module": true,
      "solid_fuel_quarry": true,
      "adv_quarry": true,
      "mover": true,
      "placer_plus": true,
      "adv_pump": true,
      "filler": true,
      "mining_well": true,
      "exp_pump": true,
      "book_mover": true,
      "workbench": true,
      "pump_plus": true
    },
    "powerMap": {
      "mini_quarry": {
        "maxEnergy": 1000.0,
        "breakBlockBase": 20.0
      },
      "solid_fuel_quarry": {
        "maxEnergy": 1000.0,
        "breakEfficiencyCoefficient": 1.379729661461215,
        "breakBlockBase": 25.0,
        "moveHeadBase": 1.0,
        "efficiencyCoefficient": 1.5848931924611136,
        "makeFrame": 15.0,
        "breakBlockFluid": 125.0,
        "expCollect": 2.5,
        "breakFortuneCoefficient": 1.5874010519681996,
        "breakSilktouchCoefficient": 4.0
      },
      "adv_quarry": {
        "maxEnergy": 50000.0,
        "breakEfficiencyCoefficient": 1.379729661461215,
        "breakBlockBase": 25.0,
        "moveHeadBase": 1.0,
        "efficiencyCoefficient": 1.5848931924611136,
        "makeFrame": 15.0,
        "breakBlockFluid": 125.0,
        "expCollect": 2.5,
        "breakFortuneCoefficient": 1.5874010519681996,
        "breakSilktouchCoefficient": 4.0
      },
      "filler": {
        "maxEnergy": 1000.0,
        "breakBlockBase": 15.0
      },
      "book_mover": {
        "maxEnergy": 50000.0
      },
      "workbench": {
        "maxEnergy": 5.0
      },
      "quarry": {
        "maxEnergy": 10000.0,
        "breakEfficiencyCoefficient": 1.379729661461215,
        "breakBlockBase": 25.0,
        "moveHeadBase": 1.0,
        "efficiencyCoefficient": 1.5848931924611136,
        "makeFrame": 15.0,
        "breakBlockFluid": 125.0,
        "expCollect": 2.5,
        "breakFortuneCoefficient": 1.5874010519681996,
        "breakSilktouchCoefficient": 4.0
      }
    },
    "acceptableEnchantmentsMap": {
      "mini_quarry": [
        "minecraft:efficiency",
        "minecraft:unbreaking"
      ],
      "adv_quarry": [
        "minecraft:efficiency",
        "minecraft:unbreaking",
        "minecraft:fortune",
        "minecraft:silk_touch"
      ],
      "adv_pump": [
        "minecraft:efficiency",
        "minecraft:unbreaking",
        "minecraft:fortune"
      ],
      "quarry": [
        "minecraft:efficiency",
        "minecraft:unbreaking",
        "minecraft:fortune",
        "minecraft:silk_touch"
      ]
    }
  }
}
