# 源代码仓库: https://github.com/QianMoo0121/Luminara
# 提交反馈/错误报告: https://github.com/QianMoo0121/Luminara/issues
# 
# 
# 配置文件版本号，请勿编辑
"_v"=1
# 异步捕获相关设置
# Async Catcher 共有四种模式
# NONE - 保持在当前线程执行
# DISPATCH - 不阻塞地发布到主线程
# BLOCK - 发布到主线程并等待结果
# EXCEPTION - 抛出异常
async-catcher {
    defaultOperation=block
    # 是否在 debug 日志中打印堆栈信息
    dump=true
    overrides {}
    warn=true
}
# 异步世界保存相关设置
# 在服务器关闭时异步保存世界数据，减少关服等待时间
async-world-save {
    # 是否启用异步世界保存功能
    enabled=true
    # 是否在异步保存中包含世界数据
    save-world-data=true
    # 异步保存超时时间（秒）
    # 如果在指定时间内保存未完成，服务器将继续关闭流程
    timeout-seconds=45
}
compatibility {
    entity-property-overrides {}
    # 额外运行逻辑的维度类名
    # 如果有模组世界/功能运行不正常，尝试在日志中搜索和 [EXT_LOGIC] 有关的对应类名并添加
    extra-logic-worlds=[
        "com.example.mod.ExtraLogicWorld",
        "xyz.nucleoid.fantasy.RuntimeWorld"
    ]
    # true - 将 Forge 权限查询请求转发至 Bukkit
    # false - 不启用权限转发
    # reverse - 将 Bukkit 玩家权限查询请求转发至 Forge
    forward-permission=true
    # 允许空 NBT 标签的物品和没有 NBT 标签的物品堆叠
    lenient-item-tag-match=true
    material-property-overrides {}
    # 为模组的维度创建符号链接
    # 推荐启用以增强插件交互兼容性
    # 变更此设置会导致模组世界名称变化，可能造成依赖世界名称的插件数据丢失
    # 参见 https://github.com/IzzelAliz/Arclight/wiki/World-Symlink
    symlink-world=false
    # 用户名合法检查正则表达式，留空为使用原版检查
    # 如果需要允许中文字符可以使用
    # valid-username-regex = "^[ -~\p{sc=Han}]{1,16}$"
    # 如果允许任何用户名可以使用
    # valid-username-regex = ".+"
    valid-username-regex=""
}
# 语言/国际化相关设置
locale {
    current="zh_cn"
    fallback="zh_cn"
}
# 服务端优化相关设置
optimization {
    # 异步系统相关设置
    async-system {
        # 是否启用异步AI
        async-ai-enabled=true
        # 是否启用异步碰撞检测
        async-collision-enabled=false
        # 是否启用异步红石
        async-redstone-enabled=false
        # 出错时是否禁用异步系统
        disable-on-error=true
        # 是否启用异步系统
        enabled=true
        # 事件类黑名单
        event-class-blacklist=[
            "net.minecraftforge.event.TickEvent",
            "net.minecraftforge.event.level.LevelTickEvent",
            "net.minecraftforge.event.entity.living.*"
        ]
        # 最大线程数
        max-threads=6
        mod-blacklist=[]
        # 是否启用严格类检查
        strict-class-checking=true
        # 超时时间（秒）
        timeout-seconds=20
    }
    # 是否缓存插件类以提高性能
    cache-plugin-class=true
    # 区块优化相关设置
    chunk-optimization {
        # 是否启用激进的区块卸载
        aggressive-chunk-unloading=true
        # 区块加载速率限制
        chunk-load-rate-limit=8
        # 区块卸载延迟（tick）
        chunk-unload-delay=200
        # 是否优化区块加载
        optimize-chunk-loading=true
    }
    # 实体优化相关设置
    entity-optimization {
        # 区块实体限制
        chunk-entity-limit=20
        # 是否清理有价值的物品
        clean-valuable-items=false
        # 清理取消消息模板
        cleanup-cancelled-message="&6[枫影轻语] &c实体清理已取消."
        # 清理完成消息模板
        cleanup-complete-message="&6[枫影轻语] &a实体清理完成！! 移除 &c{total} &a个实体 (生物: &c{dead}&a, 物品: &c{items}&a, 密集实体: &c{dense}&a, 剩余: &c{excess}&a)"
        # 是否启用清理通知
        cleanup-notification-enabled=true
        # 清理开始消息模板
        cleanup-start-message="&6[枫影轻语] &e将在&c{time} &秒后清理实体..."
        # 清理警告时间（秒）
        cleanup-warning-time=15
        # 是否禁用实体碰撞检测
        disable-entity-collisions=false
        # 实体检查间隔（tick）
        entity-check-interval=200
        # 是否启用实体清理功能
        entity-cleanup-enabled=true
        # 触发实体清理的实体数量阈值
        entity-cleanup-threshold=300
        # 实体冻结超时时间（毫秒）
        entity-freeze-timeout=15000
        # 实体更新距离
        entity-update-distance=64
        # 物品最大存在时间（tick）
        item-max-age=4800
        # 每个区块最大实体数量
        max-entities-per-chunk=100
        # 每种类型最大实体数量
        max-entities-per-type=150
        # 是否减少实体更新频率
        reduce-entity-updates=true
    }
    # 实体目标选择器的更新间隔
    # 数值越高消耗资源越少
    # 导致实体更改目标速度变慢
    goal-selector-update-interval=3
    # 内存优化相关设置
    memory-optimization {
        # 是否启用缓存清理
        cache-cleanup-enabled=true
        # 缓存清理间隔（秒）
        cache-cleanup-interval=300
        # 是否启用实体清理
        entity-cleanup-enabled=true
    }
    # 世界创建相关设置
    world-creation {
        # 是否异步加载世界数据
        async-world-data-loading=true
        # 是否延迟出生区域准备
        defer-spawn-area-preparation=true
        # 是否提前添加到世界列表
        early-world-list-addition=true
        # 是否启用快速世界创建
        fast-world-creation=true
        # 是否强制关闭加载屏幕
        force-close-loading-screen=true
        # 最大并发世界加载数
        max-concurrent-world-loads=3
        # 是否优化世界边界设置
        optimize-world-border-setup=true
        # 是否并行初始化世界
        parallel-world-initialization=true
        # 是否跳过出生点区块加载
        skip-spawn-chunk-loading=true
        # 出生区域半径
        spawn-area-radius=8
        # 世界初始化超时时间（秒）
        world-init-timeout-seconds=45
    }
}
# Velocity Modern 转发相关设置
velocity {
    # 是否在日志中显示 Velocity 转发相关的调试信息
    # 启用后可以帮助诊断玩家连接问题
    debug-logging=false
    # 是否启用 Velocity Modern 转发支持
    # 启用后可以与 Velocity 代理服务器配合使用
    enabled=true
    # Velocity 转发密钥
    # 必须与 Velocity 配置文件中的 forwarding-secret 完全一致
    # 用于验证来自 Velocity 的连接请求
    forwarding-secret="5tcbRNMxCCDEFj3sfBXc"
    # 是否启用在线模式验证
    # 通常应与 Velocity 配置中的 online-mode 设置保持一致
    online-mode=true
}
