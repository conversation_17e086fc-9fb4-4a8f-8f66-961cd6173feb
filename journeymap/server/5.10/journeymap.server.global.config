// JourneyMap server configuration file. Modify at your own risk!
// To restore the default settings, simply delete this file before starting Minecraft server
// For more information, go to: http://journeymap.info/JourneyMapServer
//
// Global Server Configuration : Applies to all dimensions unless overridden. 
{
  "journeymapEnabled": "true",
  "useWorldId": "true",
  "viewOnlyServerProperties": "true",
  "allowMultiplayerSettings": "ALL",
  "worldPlayerRadar": "ALL",
  "worldPlayerRadarUpdateTime": "5",
  "seeUndergroundPlayers": "ALL",
  "hideOps": "false",
  "hideSpectators": "false",
  "allowDeathPoints": "true",
  "showInGameBeacons": "true",
  "allowWaypoints": "true",
  "teleportEnabled": "false",
  "renderRange": "0",
  "surfaceMapping": "ALL",
  "topoMapping": "ALL",
  "biomeMapping": "ALL",
  "caveMapping": "ALL",
  "radarEnabled": "ALL",
  "playerRadarEnabled": "true",
  "villagerRadarEnabled": "true",
  "animalRadarEnabled": "true",
  "mobRadarEnabled": "true",
  "configVersion": "5.10.3"
}