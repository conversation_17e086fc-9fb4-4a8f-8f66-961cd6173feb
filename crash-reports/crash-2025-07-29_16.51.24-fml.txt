---- Minecraft Crash Report ----
// I just don't know what went wrong :(

Time: 2025-07-29 16:51:24
Description: Mod loading error has occurred

java.lang.Exception: Mod Loading has failed
	at net.minecraftforge.logging.CrashReportExtender.dumpModLoadingCrashReport(CrashReportExtender.java:60) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at net.minecraftforge.server.loading.ServerModLoader.load(ServerModLoader.java:37) ~[forge-1.20.1-47.4.4-universal.jar%23968!/:?] {re:classloading}
	at net.minecraft.server.Main.main(Main.java:125) ~[server-1.20.1-20230612.114412-srg.jar%23963!/:?] {re:mixin,pl:accesstransformer:B,re:classloading,pl:accesstransformer:B,pl:mixin:A}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.serverService(CommonLaunchHandler.java:103) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at net.minecraftforge.fml.loading.targets.CommonServerLaunchHandler.lambda$makeService$0(CommonServerLaunchHandler.java:27) ~[fmlloader-1.20.1-47.4.4.jar%23103!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar%2389!/:?] {}
	at io.izzel.arclight.boot.application.ApplicationBootstrap.accept(ApplicationBootstrap.java:47) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.BootstrapTransformer.onInvoke$BootstrapLauncher(BootstrapTransformer.java:33) ~[luminara-1.20.1-1.0.8.jar:luminara-1.20.1-1.0.8-a193d85] {}
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?] {}
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?] {}
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?] {}
	at io.izzel.arclight.boot.application.Main_Forge.main(Main_Forge.java:33) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}
	at io.izzel.arclight.server.Launcher.main(Launcher.java:18) ~[luminara-1.20.1-1.0.8.jar%2383!/:luminara-1.20.1-1.0.8-a193d85] {}


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: main
Suspected Mod: 
	KubeJS (kubejs), Version: 2001.6.5-build.16
		Issue tracker URL: https://kubejs.com/support?source=kubejs&mc=2001&loader=forge
		at TRANSFORMER/kubejs@2001.6.5-build.16/dev.latvian.mods.kubejs.KubeJS.loadComplete(KubeJS.java:226)
Stacktrace:
	at dev.latvian.mods.kubejs.KubeJS.loadComplete(KubeJS.java:226) ~[kubejs-forge-2001.6.5-build.16.jar%23770!/:2001.6.5-build.16] {re:classloading}
-- MOD kubejs --
Details:
	Mod File: /root/server/main/mods/kubejs-forge-2001.6.5-build.16.jar
	Failure message: KubeJS (kubejs) encountered an error during the complete event phase
		java.lang.RuntimeException: There were KubeJS startup script syntax errors! See logs/kubejs/startup.log for more info
	Mod Version: 2001.6.5-build.16
	Mod Issue URL: https://kubejs.com/support?source=kubejs&mc=2001&loader=forge
	Exception message: java.lang.RuntimeException: There were KubeJS startup script syntax errors! See logs/kubejs/startup.log for more info
Stacktrace:
	at dev.latvian.mods.kubejs.KubeJS.loadComplete(KubeJS.java:226) ~[kubejs-forge-2001.6.5-build.16.jar%23770!/:2001.6.5-build.16] {re:classloading}
	at dev.latvian.mods.kubejs.forge.KubeJSForge.loadComplete(KubeJSForge.java:135) ~[kubejs-forge-2001.6.5-build.16.jar%23770!/:2001.6.5-build.16] {re:classloading}
	at net.minecraftforge.eventbus.EventBus.doCastFilter(EventBus.java:260) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.lambda$addListener$11(EventBus.java:252) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:315) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:296) ~[eventbus-6.0.5.jar%2386!/:?] {}
	at net.minecraftforge.fml.javafmlmod.FMLModContainer.acceptEvent(FMLModContainer.java:121) ~[javafmllanguage-1.20.1-47.4.4.jar%23965!/:?] {}
	at net.minecraftforge.fml.ModContainer.lambda$buildTransitionHandler$5(ModContainer.java:127) ~[fmlcore-1.20.1-47.4.4.jar%23964!/:?] {}
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804) ~[?:?] {}
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1796) ~[?:?] {}
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387) ~[?:?] {}
	at java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1310) ~[?:?] {}
	at java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1841) ~[?:?] {re:computing_frames}
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1806) ~[?:?] {re:computing_frames}
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188) ~[?:?] {}


-- System Details --
Details:
	Minecraft Version: 1.20.1
	Minecraft Version ID: 1.20.1
	Operating System: Linux (amd64) version 6.14.6-x64v3-xanmod1
	Java Version: 21.0.6, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode), Oracle Corporation
	Memory: 16299048944 bytes (15543 MiB) / 34359738368 bytes (32768 MiB) up to 34359738368 bytes (32768 MiB)
	CPUs: 32
	Processor Vendor: AuthenticAMD
	Processor Name: AMD Ryzen 9 9950X 16-Core Processor
	Identifier: AuthenticAMD Family 26 Model 68 Stepping 0
	Microarchitecture: unknown
	Frequency (GHz): -0.00
	Number of physical packages: 1
	Number of physical CPUs: 16
	Number of logical CPUs: 32
	Graphics card #0 name: Granite Ridge [Radeon Graphics]
	Graphics card #0 vendor: Advanced Micro Devices, Inc. [AMD/ATI] (0x1002)
	Graphics card #0 VRAM (MB): 258.00
	Graphics card #0 deviceId: 0x13c0
	Graphics card #0 versionInfo: unknown
	Memory slot #0 capacity (MB): 16384.00
	Memory slot #0 clockSpeed (GHz): -0.00
	Memory slot #0 type: DDR5
	Memory slot #1 capacity (MB): 16384.00
	Memory slot #1 clockSpeed (GHz): -0.00
	Memory slot #1 type: DDR5
	Memory slot #2 capacity (MB): 16384.00
	Memory slot #2 clockSpeed (GHz): -0.00
	Memory slot #2 type: DDR5
	Memory slot #3 capacity (MB): 16384.00
	Memory slot #3 clockSpeed (GHz): -0.00
	Memory slot #3 type: DDR5
	Virtual memory max (MB): 64712.61
	Virtual memory used (MB): 51555.17
	Swap memory total (MB): 33743.99
	Swap memory used (MB): 968.95
	JVM Flags: 73 total; -XX:ThreadPriorityPolicy=1 -XX:+UnlockExperimentalVMOptions -XX:+EnableJVMCIProduct -XX:-UnlockExperimentalVMOptions -Xmx32G -Xms32G -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UnlockDiagnosticVMOptions -XX:+DisableExplicitGC -XX:+AlwaysPreTouch -XX:G1NewSizePercent=28 -XX:G1MaxNewSizePercent=50 -XX:G1HeapRegionSize=16M -XX:G1ReservePercent=15 -XX:G1MixedGCCountTarget=3 -XX:InitiatingHeapOccupancyPercent=20 -XX:G1MixedGCLiveThresholdPercent=90 -XX:SurvivorRatio=32 -XX:G1HeapWastePercent=5 -XX:MaxTenuringThreshold=1 -XX:+PerfDisableSharedMem -XX:G1SATBBufferEnqueueingThresholdPercent=30 -XX:G1ConcMarkStepDurationMillis=5 -XX:G1RSetUpdatingPauseTimePercent=0 -XX:+UseNUMA -XX:-DontCompileHugeMethods -XX:MaxNodeLimit=240000 -XX:NodeLimitFudgeFactor=8000 -XX:ReservedCodeCacheSize=400M -XX:NonNMethodCodeHeapSize=12M -XX:ProfiledCodeHeapSize=194M -XX:NonProfiledCodeHeapSize=194M -XX:NmethodSweepActivity=1 -XX:+UseFastUnorderedTimeStamps -XX:+UseCriticalJavaThreadPriority -XX:AllocatePrefetchStyle=3 -XX:+AlwaysActAsServerClassMachine -XX:+EagerJVMCI -XX:+UseStringDeduplication -XX:+UseAES -XX:+UseAESIntrinsics -XX:+UseFMA -XX:+UseLoopPredicate -XX:+RangeCheckElimination -XX:+OptimizeStringConcat -XX:+UseThreadPriorities -XX:+OmitStackTraceInFastThrow -XX:+RewriteBytecodes -XX:+RewriteFrequentPairs -XX:+UseFPUForSpilling -XX:+UseFastStosb -XX:+UseNewLongLShift -XX:+UseVectorCmov -XX:+UseXMMForArrayCopy -XX:+UseXmmI2D -XX:+UseXmmI2F -XX:+UseXmmLoadAndClearUpper -XX:+UseXmmRegToRegMoveAll -XX:+EliminateLocks -XX:+DoEscapeAnalysis -XX:+AlignVector -XX:+OptimizeFill -XX:+EnableVectorSupport -XX:+UseCharacterCompareIntrinsics -XX:+UseCopySignIntrinsic -XX:+UseVectorStubs -XX:UseAVX=2 -XX:UseSSE=4 -XX:+UseFastJNIAccessors -XX:+UseInlineCaches -XX:+SegmentedCodeCache
	ModLauncher: 10.0.9+10.0.9+main.dcd20f30
	ModLauncher launch target: arclightserver
	ModLauncher naming: srg
	ModLauncher services: 
		mixin-0.8.5.jar mixin PLUGINSERVICE 
		luminara-1.20.1-1.0.8.jar arclight_implementer PLUGINSERVICE 
		eventbus-6.0.5.jar eventbus PLUGINSERVICE 
		fmlloader-1.20.1-47.4.4.jar slf4jfixer PLUGINSERVICE 
		fmlloader-1.20.1-47.4.4.jar object_holder_definalize PLUGINSERVICE 
		fmlloader-1.20.1-47.4.4.jar runtime_enum_extender PLUGINSERVICE 
		fmlloader-1.20.1-47.4.4.jar capability_token_subclass PLUGINSERVICE 
		accesstransformers-8.0.4.jar accesstransformer PLUGINSERVICE 
		fmlloader-1.20.1-47.4.4.jar runtimedistcleaner PLUGINSERVICE 
		modlauncher-10.0.9.jar jcplugin TRANSFORMATIONSERVICE 
		modlauncher-10.0.9.jar mixin TRANSFORMATIONSERVICE 
		modlauncher-10.0.9.jar fml TRANSFORMATIONSERVICE 
	FML Language Providers: 
		minecraft@1.0
		kotlinforforge@4.11.0
		javafml@null
		lowcodefml@null
		kotori_scala@3.3.1-build-0
	Mod List: 
		YungsBetterDungeons-1.20-Forge-4.0.4.jar          |YUNG's Better Dungeons        |betterdungeons                |1.20-Forge-4.0.4    |DONE      |Manifest: NOSIGNATURE
		simplemagnets-1.1.12-forge-mc1.20.1.jar           |Simple Magnets                |simplemagnets                 |1.1.12              |DONE      |Manifest: NOSIGNATURE
		IntegratedTerminals-1.20.1-1.6.6.jar              |IntegratedTerminals           |integratedterminals           |1.6.6               |DONE      |Manifest: NOSIGNATURE
		laserio-1.6.8.jar                                 |LaserIO                       |laserio                       |1.6.8               |DONE      |Manifest: NOSIGNATURE
		EvilCraft-1.20.1-1.2.52.jar                       |EvilCraft                     |evilcraft                     |1.2.52              |DONE      |Manifest: NOSIGNATURE
		useitemonblockevent-1.20.1-1.0.0.2.jar            |Use Item on Block Event       |useitemonblockevent           |1.0.0.2             |DONE      |Manifest: NOSIGNATURE
		YungsApi-1.20-Forge-4.0.6.jar                     |YUNG's API                    |yungsapi                      |1.20-Forge-4.0.6    |DONE      |Manifest: NOSIGNATURE
		GatewaysToEternity-1.20.1-4.2.6.jar               |Gateways To Eternity          |gateways                      |4.2.6               |DONE      |Manifest: NOSIGNATURE
		jumbofurnace-1.20.1-4.0.0.5.jar                   |Jumbo Furnace                 |jumbofurnace                  |4.0.0.5             |DONE      |Manifest: NOSIGNATURE
		WitherSkeletonTweaks-1.20.1-9.1.0.jar             |Wither Skeleton Tweaks        |wstweaks                      |9.1.0               |DONE      |Manifest: NOSIGNATURE
		Shrink-1.20.1-1.4.5.jar                           |Shrink                        |shrink                        |1.4.5               |DONE      |Manifest: NOSIGNATURE
		universalgrid-1.20.1-1.1.jar                      |Universal Grid                |universalgrid                 |1.20.1-1.1          |DONE      |Manifest: NOSIGNATURE
		DarkUtilities-Forge-1.20.1-17.0.5.jar             |DarkUtilities                 |darkutils                     |17.0.5              |DONE      |Manifest: NOSIGNATURE
		Apotheosis-1.20.1-7.4.7.jar                       |Apotheosis                    |apotheosis                    |7.4.7               |DONE      |Manifest: NOSIGNATURE
		ldlib-forge-1.20.1-1.0.41.b.jar                   |LowDragLib                    |ldlib                         |1.0.41.b            |DONE      |Manifest: NOSIGNATURE
		create-new-age-forge-1.20.1-1.1.2.jar             |Create: New Age               |create_new_age                |1.1.2               |DONE      |Manifest: NOSIGNATURE
		YungsBetterNetherFortresses-1.20-Forge-2.0.6.jar  |YUNG's Better Nether Fortresse|betterfortresses              |1.20-Forge-2.0.6    |DONE      |Manifest: NOSIGNATURE
		Paraglider-forge-20.1.3.jar                       |Paraglider                    |paraglider                    |20.1.3              |DONE      |Manifest: NOSIGNATURE
		cloth-config-11.1.136-forge.jar                   |Cloth Config v10 API          |cloth_config                  |11.1.136            |DONE      |Manifest: NOSIGNATURE
		durabilitytooltip-1.1.5-forge-mc1.20.jar          |Durability Tooltip            |durabilitytooltip             |1.1.5               |DONE      |Manifest: NOSIGNATURE
		structure_gel-1.20.1-2.16.2.jar                   |Structure Gel API             |structure_gel                 |2.16.2              |DONE      |Manifest: NOSIGNATURE
		industrial-foregoing-1.20.1-3.5.19.jar            |Industrial Foregoing          |industrialforegoing           |3.5.19              |DONE      |Manifest: NOSIGNATURE
		handcrafted-forge-1.20.1-3.0.6.jar                |Handcrafted                   |handcrafted                   |3.0.6               |DONE      |Manifest: NOSIGNATURE
		repurposed_structures-7.1.15+1.20.1-forge.jar     |Repurposed Structures         |repurposed_structures         |7.1.15+1.20.1-forge |DONE      |Manifest: NOSIGNATURE
		StructureCompass-1.20.1-2.1.0.jar                 |Structure Compass Mod         |structurecompass              |2.1.0               |DONE      |Manifest: NOSIGNATURE
		Botania-1.20.1-448-FORGE.jar                      |Botania                       |botania                       |1.20.1-448-FORGE    |DONE      |Manifest: NOSIGNATURE
		corail_woodcutter-1.20.1-3.0.6.jar                |Corail Woodcutter             |corail_woodcutter             |3.0.6               |DONE      |Manifest: NOSIGNATURE
		advgenerators-1.6.0.6-mc1.20.1.jar                |Advanced Generators           |advgenerators                 |1.6.0.6             |DONE      |Manifest: NOSIGNATURE
		YungsExtras-1.20-Forge-4.0.3.jar                  |YUNG's Extras                 |yungsextras                   |1.20-Forge-4.0.3    |DONE      |Manifest: NOSIGNATURE
		ApothicAttributes-1.20.1-1.3.7.jar                |Apothic Attributes            |attributeslib                 |1.3.7               |DONE      |Manifest: NOSIGNATURE
		tombstone-1.20.1-8.9.4.jar                        |Corail Tombstone              |tombstone                     |8.9.4               |DONE      |Manifest: NOSIGNATURE
		ExtraStorage-1.20.1-4.0.7.jar                     |ExtraStorage                  |extrastorage                  |4.0.7               |DONE      |Manifest: NOSIGNATURE
		cumulus_menus-1.20.1-1.0.1-neoforge.jar           |Cumulus                       |cumulus_menus                 |1.20.1-1.0.1-neoforg|DONE      |Manifest: NOSIGNATURE
		NaturesAura-39.4.jar                              |NaturesAura                   |naturesaura                   |39.4                |DONE      |Manifest: NOSIGNATURE
		constructionwand-1.20.1-2.11.jar                  |Construction Wand             |constructionwand              |1.20.1-2.11         |DONE      |Manifest: NOSIGNATURE
		mcw-roofs-2.3.1-mc1.20.1forge.jar                 |Macaw's Roofs                 |mcwroofs                      |2.3.1               |DONE      |Manifest: NOSIGNATURE
		littlelogistics-mc1.20.1-v1.20.1.2.jar            |Little Logistics              |littlelogistics               |1.20.1.2            |DONE      |Manifest: NOSIGNATURE
		cfm-forge-1.20.1-7.0.0-pre36.jar                  |MrCrayfish's Furniture Mod    |cfm                           |7.0.0-pre36         |DONE      |Manifest: 0d:78:5f:44:c0:47:0c:8c:e2:63:a3:04:43:d4:12:7d:b0:7c:35:37:dc:40:b1:c1:98:ec:51:eb:3b:3c:45:99
		Chimes-v2.0.1-1.20.1.jar                          |Chimes                        |chimes                        |2.0.1               |DONE      |Manifest: NOSIGNATURE
		flib-1.20.1-0.0.15.jar                            |flib                          |flib                          |0.0.15              |DONE      |Manifest: 1f:47:ac:b1:61:82:96:b8:47:19:16:d2:61:81:11:60:3a:06:4b:61:31:56:7d:44:31:1e:0c:6f:22:5b:4c:ed
		YungsBetterEndIsland-1.20-Forge-2.0.6.jar         |YUNG's Better End Island      |betterendisland               |1.20-Forge-2.0.6    |DONE      |Manifest: NOSIGNATURE
		nitrogen_internals-1.20.1-1.0.12-neoforge.jar     |Nitrogen                      |nitrogen_internals            |1.20.1-1.0.12-neofor|DONE      |Manifest: NOSIGNATURE
		Potion-Blender-1.20.1-FORGE-3.1.2.jar             |Potion-Blender                |potionblender                 |3.1.2               |DONE      |Manifest: NOSIGNATURE
		l2library-2.4.16-slim.jar                         |L2 Library                    |l2library                     |2.4.16              |DONE      |Manifest: NOSIGNATURE
		FastLeafDecay-32.jar                              |Fast Leaf Decay               |fastleafdecay                 |32                  |DONE      |Manifest: NOSIGNATURE
		Super Factory Manager (SFM)-MC1.20.1-4.21.0.jar   |Super Factory Manager (SFM)   |sfm                           |4.21.0              |DONE      |Manifest: NOSIGNATURE
		MobDespawnTimers-1.20.1-3.0.1.jar                 |Mob Despawn Timers            |despawntimers                 |3.0.1               |DONE      |Manifest: NOSIGNATURE
		mcw-lights-1.1.0-mc1.20.1forge.jar                |Macaw's Lights and Lamps      |mcwlights                     |1.1.0               |DONE      |Manifest: NOSIGNATURE
		YungsBetterJungleTemples-1.20-Forge-2.0.5.jar     |YUNG's Better Jungle Temples  |betterjungletemples           |1.20-Forge-2.0.5    |DONE      |Manifest: NOSIGNATURE
		SmartBrainLib-forge-1.20.1-1.15.jar               |SmartBrainLib                 |smartbrainlib                 |1.15                |DONE      |Manifest: NOSIGNATURE
		rechiseled-1.1.6-forge-mc1.20.jar                 |Rechiseled                    |rechiseled                    |1.1.6               |DONE      |Manifest: NOSIGNATURE
		AttributeFix-Forge-1.20.1-21.0.4.jar              |AttributeFix                  |attributefix                  |21.0.4              |DONE      |Manifest: eb:c4:b1:67:8b:f9:0c:db:dc:4f:01:b1:8e:61:64:39:4c:10:85:0b:a6:c4:c7:48:f0:fa:95:f2:cb:08:3a:e5
		caelus-forge-3.2.0+1.20.1.jar                     |Caelus API                    |caelus                        |3.2.0+1.20.1        |DONE      |Manifest: NOSIGNATURE
		EpheroLib-1.20.1-FORGE-1.2.0.jar                  |BOZOID                        |epherolib                     |0.1.2               |DONE      |Manifest: NOSIGNATURE
		BotanyPots-Forge-1.20.1-13.0.41.jar               |BotanyPots                    |botanypots                    |13.0.41             |DONE      |Manifest: NOSIGNATURE
		farmingforblockheads-forge-1.20.1-14.0.2.jar      |Farming for Blockheads        |farmingforblockheads          |14.0.2              |DONE      |Manifest: NOSIGNATURE
		rechiseledcreate-1.0.2-forge-mc1.20.jar           |Rechiseled: Create            |rechiseledcreate              |1.0.2               |DONE      |Manifest: NOSIGNATURE
		additional_lights-1.20.1-2.1.7.jar                |Additional Lights             |additional_lights             |2.1.7               |DONE      |Manifest: NOSIGNATURE
		fusion-1.2.7-forge-mc1.20.1.jar                   |Fusion                        |fusion                        |1.2.7               |DONE      |Manifest: NOSIGNATURE
		ExtraDisks-1.20.1-3.0.3.jar                       |Extra Disks                   |extradisks                    |1.20.1-3.0.3        |DONE      |Manifest: NOSIGNATURE
		EdivadLib-1.20.1-2.0.1.jar                        |EdivadLib                     |edivadlib                     |2.0.1               |DONE      |Manifest: NOSIGNATURE
		MythicBotany-1.20.1-4.0.3.jar                     |MythicBotany                  |mythicbotany                  |1.20.1-4.0.3        |DONE      |Manifest: NOSIGNATURE
		IntegratedCrafting-1.20.1-1.1.11.jar              |IntegratedCrafting            |integratedcrafting            |1.1.11              |DONE      |Manifest: NOSIGNATURE
		DungeonsArise-1.20.x-2.1.58-release.jar           |When Dungeons Arise           |dungeons_arise                |2.1.58-1.20.x       |DONE      |Manifest: NOSIGNATURE
		server-1.20.1-20230612.114412-srg.jar             |Minecraft                     |minecraft                     |1.20.1              |DONE      |Manifest: NOSIGNATURE
		TerraBlender-forge-1.20.1-3.0.1.10.jar            |TerraBlender                  |terrablender                  |3.0.1.10            |DONE      |Manifest: NOSIGNATURE
		CorgiLib-forge-1.20.1-4.0.1.3.jar                 |CorgiLib                      |corgilib                      |4.0.1.3             |DONE      |Manifest: NOSIGNATURE
		sushigocrafting-1.20.1-0.5.3.jar                  |Sushi Go Crafting             |sushigocrafting               |0.5.3               |DONE      |Manifest: NOSIGNATURE
		domum_ornamentum-1.20.1-1.0.285-snapshot-universal|Domum Ornamentum              |domum_ornamentum              |1.20.1-1.0.285-snaps|DONE      |Manifest: NOSIGNATURE
		flywheel-forge-1.20.1-0.6.11-13.jar               |Flywheel                      |flywheel                      |0.6.11-13           |DONE      |Manifest: NOSIGNATURE
		baubley-heart-canisters-1.20.1-1.1.0.jar          |Baubley Heart Canisters       |bhc                           |1.20.1-1.1.0        |DONE      |Manifest: NOSIGNATURE
		JustEnoughProfessions-forge-1.20.1-3.0.1.jar      |Just Enough Professions (JEP) |justenoughprofessions         |3.0.1               |DONE      |Manifest: NOSIGNATURE
		[1.20.1] SecurityCraft v1.9.12.jar                |SecurityCraft                 |securitycraft                 |1.9.12              |DONE      |Manifest: NOSIGNATURE
		almostunified-forge-1.20.1-0.9.4.jar              |AlmostUnified                 |almostunified                 |1.20.1-0.9.4        |DONE      |Manifest: NOSIGNATURE
		structurize-1.20.1-1.0.769-snapshot.jar           |Structurize                   |structurize                   |1.20.1-1.0.769-snaps|DONE      |Manifest: NOSIGNATURE
		FastFurnace-1.20.1-8.0.2.jar                      |FastFurnace                   |fastfurnace                   |8.0.2               |DONE      |Manifest: NOSIGNATURE
		lootr-forge-1.20-0.7.35.91.jar                    |Lootr                         |lootr                         |0.7.35.91           |DONE      |Manifest: NOSIGNATURE
		occultism-1.20.1-1.141.4.jar                      |Occultism                     |occultism                     |1.141.4             |DONE      |Manifest: NOSIGNATURE
		allthetweaks-1.20.1-47.2.20-2.3.2.jar             |AllTheTweaks                  |allthetweaks                  |2.3.2               |DONE      |Manifest: NOSIGNATURE
		ExtremeSoundMuffler-3.49-forge-1.20.1.jar         |Extreme Sound Muffler         |extremesoundmuffler           |3.48                |DONE      |Manifest: NOSIGNATURE
		cosmeticarmorreworked-1.20.1-v1a.jar              |CosmeticArmorReworked         |cosmeticarmorreworked         |1.20.1-v1a          |DONE      |Manifest: 5e:ed:25:99:e4:44:14:c0:dd:89:c1:a9:4c:10:b5:0d:e4:b1:52:50:45:82:13:d8:d0:32:89:67:56:57:01:53
		morered-1.20.1-4.0.0.4.jar                        |More Red                      |morered                       |4.0.0.4             |DONE      |Manifest: NOSIGNATURE
		ad_astra-forge-1.20.1-1.15.19.jar                 |Ad Astra                      |ad_astra                      |1.15.19             |DONE      |Manifest: NOSIGNATURE
		rsrequestify-1.20.1-2.3.3.jar                     |RSRequestify                  |rsrequestify                  |2.3.3               |DONE      |Manifest: NOSIGNATURE
		kuma-api-forge-20.1.10+1.20.1.jar                 |KumaAPI                       |kuma_api                      |20.1.10             |DONE      |Manifest: NOSIGNATURE
		alchemylib-1.20.1-1.0.30.jar                      |AlchemyLib                    |alchemylib                    |1.0.30              |DONE      |Manifest: NOSIGNATURE
		AdvancedPeripherals-1.20.1-0.7.41r.jar            |Advanced Peripherals          |advancedperipherals           |0.7.41r             |DONE      |Manifest: NOSIGNATURE
		tinyredstone-1.20-5.0.3.jar                       |Tiny Redstone                 |tinyredstone                  |1.20-5.0.3          |DONE      |Manifest: NOSIGNATURE
		towntalk-1.20.1-1.1.0.jar                         |TownTalk                      |towntalk                      |1.1.0               |DONE      |Manifest: NOSIGNATURE
		YungsBetterOceanMonuments-1.20-Forge-3.0.4.jar    |YUNG's Better Ocean Monuments |betteroceanmonuments          |1.20-Forge-3.0.4    |DONE      |Manifest: NOSIGNATURE
		sophisticatedcore-1.20.1-1.2.47.958.jar           |Sophisticated Core            |sophisticatedcore             |1.2.47.958          |DONE      |Manifest: NOSIGNATURE
		glassential-renewed-forge-1.20.1-2.4.4.jar        |Glassential Renewed           |glassential                   |2.4.4               |DONE      |Manifest: NOSIGNATURE
		Placebo-1.20.1-8.6.3.jar                          |Placebo                       |placebo                       |8.6.3               |DONE      |Manifest: NOSIGNATURE
		Bookshelf-Forge-1.20.1-20.2.13.jar                |Bookshelf                     |bookshelf                     |20.2.13             |DONE      |Manifest: eb:c4:b1:67:8b:f9:0c:db:dc:4f:01:b1:8e:61:64:39:4c:10:85:0b:a6:c4:c7:48:f0:fa:95:f2:cb:08:3a:e5
		sophisticatedbackpacks-1.20.1-3.23.13.1229.jar    |Sophisticated Backpacks       |sophisticatedbackpacks        |3.23.13.1229        |DONE      |Manifest: NOSIGNATURE
		littlecontraptions-forge-1.20.1.2.jar             |Little Contraptions           |littlecontraptions            |1.20.1.2            |DONE      |Manifest: NOSIGNATURE
		mcw-doors-1.1.2-mc1.20.1forge.jar                 |Macaw's Doors                 |mcwdoors                      |1.1.2               |DONE      |Manifest: NOSIGNATURE
		utilitarian-1.20.1-0.9.1.jar                      |Utilitarian                   |utilitarian                   |1.20.1-0.9.1        |DONE      |Manifest: NOSIGNATURE
		absentbydesign-1.20.1-1.9.0.jar                   |Absent By Design Mod          |absentbydesign                |1.9.0               |DONE      |Manifest: 1f:47:ac:b1:61:82:96:b8:47:19:16:d2:61:81:11:60:3a:06:4b:61:31:56:7d:44:31:1e:0c:6f:22:5b:4c:ed
		konkrete_forge_1.8.0_MC_1.20-1.20.1.jar           |Konkrete                      |konkrete                      |1.8.0               |DONE      |Manifest: NOSIGNATURE
		RSInfinityBooster-1.20.1-1.0+39.jar               |RSInfinityBooster             |rsinfinitybooster             |1.20.1-1.0+39       |DONE      |Manifest: NOSIGNATURE
		refinedstorage-1.12.4.jar                         |Refined Storage               |refinedstorage                |1.12.4              |DONE      |Manifest: NOSIGNATURE
		chipped-forge-1.20.1-3.0.7.jar                    |Chipped                       |chipped                       |3.0.7               |DONE      |Manifest: NOSIGNATURE
		mcw-bridges-3.0.0-mc1.20.1forge.jar               |Macaw's Bridges               |mcwbridges                    |3.0.0               |DONE      |Manifest: NOSIGNATURE
		rebornstorage-1.20.1-5.0.7.jar                    |RebornStorage                 |rebornstorage                 |5.0.7               |DONE      |Manifest: NOSIGNATURE
		tempad-forge-1.20.1-2.3.4.jar                     |Tempad                        |tempad                        |2.3.4               |DONE      |Manifest: NOSIGNATURE
		HostileNeuralNetworks-1.20.1-5.3.3.jar            |Hostile Neural Networks       |hostilenetworks               |5.3.3               |DONE      |Manifest: NOSIGNATURE
		endertanks-forge-1.20.1-1.4.jar                   |EnderTanks                    |endertanks                    |1.20.1-1.4          |DONE      |Manifest: NOSIGNATURE
		jearchaeology-1.20.1-1.0.4.jar                    |Just Enough Archaeology       |jearchaeology                 |1.20.1-1.0.4        |DONE      |Manifest: NOSIGNATURE
		fuelgoeshere-1.20.0-1.0.1.jar                     |Fuel Goes Here                |fuelgoeshere                  |1.20.0-1.0.1        |DONE      |Manifest: NOSIGNATURE
		simplylight-1.20.1-1.4.6-build.50.jar             |Simply Light                  |simplylight                   |1.20.1-1.4.6-build.5|DONE      |Manifest: NOSIGNATURE
		industrial-foregoing-souls-1.20.1-1.0.9.jar       |Industrial Foregoing Souls    |industrialforegoingsouls      |1.20.1-1.0.9        |DONE      |Manifest: NOSIGNATURE
		lionfishapi-2.4-Fix.jar                           |LionfishAPI                   |lionfishapi                   |2.4-Fix             |DONE      |Manifest: NOSIGNATURE
		mcwbiomesoplenty-1.20.1-1.2.1.jar                 |Macaw's Biomes O' Plenty      |mcwbiomesoplenty              |1.20.1-1.2.1        |DONE      |Manifest: NOSIGNATURE
		addonslib-1.20.1-1.4.jar                          |Addons Lib                    |addonslib                     |1.20.1-1.4          |DONE      |Manifest: NOSIGNATURE
		L_Enders_Cataclysm-2.64.jar                       |cataclysm                     |cataclysm                     |2.64                |DONE      |Manifest: NOSIGNATURE
		blockui-1.20.1-1.0.190-snapshot.jar               |UI Library Mod                |blockui                       |1.20.1-1.0.190-snaps|DONE      |Manifest: NOSIGNATURE
		time-in-a-bottle-4.0.4-mc1.20.1.jar               |Time In A Bottle              |tiab                          |4.0.4-mc1.20.1      |DONE      |Manifest: NOSIGNATURE
		villagertools-1.20.1-1.0.3.jar                    |villagertools                 |villagertools                 |1.0.3               |DONE      |Manifest: 1f:47:ac:b1:61:82:96:b8:47:19:16:d2:61:81:11:60:3a:06:4b:61:31:56:7d:44:31:1e:0c:6f:22:5b:4c:ed
		MysticalCustomization-1.20.1-5.0.2.jar            |Mystical Customization        |mysticalcustomization         |5.0.2               |DONE      |Manifest: NOSIGNATURE
		lostcities-1.20-7.3.6.jar                         |LostCities                    |lostcities                    |1.20-7.3.6          |DONE      |Manifest: NOSIGNATURE
		elevatorid-1.20.1-lex-1.9.jar                     |Elevator Mod                  |elevatorid                    |1.20.1-lex-1.9      |DONE      |Manifest: NOSIGNATURE
		Runelic-Forge-1.20.1-18.0.2.jar                   |Runelic                       |runelic                       |18.0.2              |DONE      |Manifest: eb:c4:b1:67:8b:f9:0c:db:dc:4f:01:b1:8e:61:64:39:4c:10:85:0b:a6:c4:c7:48:f0:fa:95:f2:cb:08:3a:e5
		twilightdelight-2.0.13.jar                        |Twilight's Flavor & Delight   |twilightdelight               |2.0.13              |DONE      |Manifest: NOSIGNATURE
		moreoverlays-1.22.7-mc1.20.2.jar                  |More Overlays Updated         |moreoverlays                  |1.22.7-mc1.20.2     |DONE      |Manifest: NOSIGNATURE
		cupboard-1.20.1-2.7.jar                           |Cupboard utilities            |cupboard                      |1.20.1-2.7          |DONE      |Manifest: NOSIGNATURE
		Voidscape-1.20.1-1.5.389.jar                      |Voidscape                     |voidscape                     |1.20.1-1.5.389      |DONE      |Manifest: NOSIGNATURE
		The_Undergarden-1.20.1-0.8.14.jar                 |The Undergarden               |undergarden                   |0.8.14              |DONE      |Manifest: NOSIGNATURE
		bwncr-forge-1.20.1-3.17.2.jar                     |Bad Wither No Cookie Reloaded |bwncr                         |3.17.2              |DONE      |Manifest: NOSIGNATURE
		caupona-1.20.1-0.4.13.jar                         |Caupona                       |caupona                       |1.20.1-0.4.13       |DONE      |Manifest: NOSIGNATURE
		BetterAdvancements-Forge-1.20.1-0.4.2.25.jar      |Better Advancements           |betteradvancements            |0.4.2.25            |DONE      |Manifest: NOSIGNATURE
		platforms-forge-1.20.1-1.1.jar                    |Platforms                     |platforms                     |1.20.1-1.1          |DONE      |Manifest: NOSIGNATURE
		dyenamics-1.20.1-3.2.0.jar                        |Dyenamics                     |dyenamics                     |1.20.1-3.2.0        |DONE      |Manifest: NOSIGNATURE
		ThermalExtra-3.2.4-1.20.1.jar                     |Thermal Extra                 |thermal_extra                 |3.2.4-1.20.1        |DONE      |Manifest: NOSIGNATURE
		mcw-paintings-1.0.5-1.20.1forge.jar               |Macaw's Paintings             |mcwpaintings                  |1.0.5               |DONE      |Manifest: NOSIGNATURE
		Clumps-forge-1.20.1-12.0.0.4.jar                  |Clumps                        |clumps                        |12.0.0.4            |DONE      |Manifest: NOSIGNATURE
		artifacts-forge-9.5.16.jar                        |Artifacts                     |artifacts                     |9.5.16              |DONE      |Manifest: NOSIGNATURE
		DefaultSettings-1.20.x-4.0.7-Forge.jar            |DefaultSettings               |defaultsettings               |4.0.7               |DONE      |Manifest: NOSIGNATURE
		mininggadgets-1.15.6.jar                          |Mining Gadgets                |mininggadgets                 |1.15.6              |DONE      |Manifest: NOSIGNATURE
		MysticalAgriculture-1.20.1-7.0.18.jar             |Mystical Agriculture          |mysticalagriculture           |7.0.18              |DONE      |Manifest: NOSIGNATURE
		craftingtweaks-forge-1.20.1-18.2.5.jar            |CraftingTweaks                |craftingtweaks                |18.2.5              |DONE      |Manifest: NOSIGNATURE
		endermanoverhaul-forge-1.20.1-1.0.4.jar           |Enderman Overhaul             |endermanoverhaul              |1.0.4               |DONE      |Manifest: NOSIGNATURE
		eccentrictome-1.20.1-1.10.3.jar                   |Eccentric Tome                |eccentrictome                 |1.20.1-1.10.3       |DONE      |Manifest: NOSIGNATURE
		mysterious_mountain_lib-1.5.19-1.20.1.jar         |Mysterious Mountain Lib       |mysterious_mountain_lib       |1.5.19-1.20.1       |DONE      |Manifest: NOSIGNATURE
		EnderIO-1.20.1-6.2.7-beta-all.jar                 |Ender IO                      |enderio                       |6.2.7-beta          |DONE      |Manifest: NOSIGNATURE
		easy-villagers-forge-1.20.1-1.1.27.jar            |Easy Villagers                |easy_villagers                |1.20.1-1.1.27       |DONE      |Manifest: NOSIGNATURE
		reliquary-1.20.1-2.0.45.1248.jar                  |Reliquary                     |reliquary                     |2.0.45.1248         |DONE      |Manifest: NOSIGNATURE
		PigPen-Forge-1.20.1-15.0.2.jar                    |PigPen                        |pigpen                        |15.0.2              |DONE      |Manifest: NOSIGNATURE
		FastWorkbench-1.20.1-8.0.4.jar                    |Fast Workbench                |fastbench                     |8.0.4               |DONE      |Manifest: NOSIGNATURE
		FluxNetworks-1.20.1-7.2.1.15.jar                  |Flux Networks                 |fluxnetworks                  |7.2.1.15            |DONE      |Manifest: NOSIGNATURE
		buildinggadgets2-1.0.7.jar                        |Building Gadgets 2            |buildinggadgets2              |1.0.7               |DONE      |Manifest: NOSIGNATURE
		minecolonies-1.20.1-1.1.873-snapshot.jar          |MineColonies                  |minecolonies                  |1.20.1-1.1.873-snaps|DONE      |Manifest: NOSIGNATURE
		pylons-1.20.1-4.2.1.jar                           |Pylons                        |pylons                        |4.2.1               |DONE      |Manifest: NOSIGNATURE
		functionalstorage-1.20.1-1.2.12.jar               |Functional Storage            |functionalstorage             |1.20.1-1.2.12       |DONE      |Manifest: NOSIGNATURE
		modular-routers-12.1.1+mc1.20.1.jar               |Modular Routers               |modularrouters                |12.1.1+mc1.20.1     |DONE      |Manifest: NOSIGNATURE
		notrample-1.20.1-1.0.1.jar                        |No Trample                    |notrample                     |1.20.1-1.0.1        |DONE      |Manifest: NOSIGNATURE
		justzoom_forge_2.1.1_MC_1.20.1.jar                |Just Zoom                     |justzoom                      |2.1.1               |DONE      |Manifest: NOSIGNATURE
		charmofundying-forge-6.5.0+1.20.1.jar             |Charm of Undying              |charmofundying                |6.5.0+1.20.1        |DONE      |Manifest: NOSIGNATURE
		valhelsia_core-forge-1.20.1-1.1.2.jar             |Valhelsia Core                |valhelsia_core                |1.1.2               |DONE      |Manifest: NOSIGNATURE
		create_enchantment_industry-1.20.1-for-create-0.5.|Create Enchantment Industry   |create_enchantment_industry   |1.2.9.d             |DONE      |Manifest: NOSIGNATURE
		productivetrees-1.20.1-0.2.6.jar                  |Productive Trees              |productivetrees               |1.20.1-0.2.6        |DONE      |Manifest: NOSIGNATURE
		createaddition-1.20.1-1.2.5.jar                   |Create Crafts & Additions     |createaddition                |1.20.1-1.2.5        |DONE      |Manifest: NOSIGNATURE
		smsn-forge-1.2.3+mc1.20.1.jar                     |Save My Shit Network          |smsn                          |1.2.3+mc1.20.1      |DONE      |Manifest: NOSIGNATURE
		saturn-mc1.20.1-0.1.3.jar                         |Saturn                        |saturn                        |0.1.3               |DONE      |Manifest: NOSIGNATURE
		supermartijn642configlib-1.1.8-forge-mc1.20.jar   |SuperMartijn642's Config Libra|supermartijn642configlib      |1.1.8               |DONE      |Manifest: NOSIGNATURE
		AdditionalEnchantedMiner-1.20.1-1201.1.121.jar    |QuarryPlus                    |quarryplus                    |1201.1.121          |DONE      |Manifest: ef:50:af:b3:03:e0:3e:70:a7:ef:78:77:a5:4d:d4:b5:07:ec:df:9d:d6:f3:12:13:c9:3c:cd:9a:0a:3e:6b:43
		player-animation-lib-forge-1.0.2-rc1+1.20.jar     |Player Animator               |playeranimator                |1.0.2-rc1+1.20      |DONE      |Manifest: NOSIGNATURE
		irons_spellbooks-1.20.1-3.4.0.8.jar               |Iron's Spells 'n Spellbooks   |irons_spellbooks              |1.20.1-3.4.0.8      |DONE      |Manifest: NOSIGNATURE
		botarium-forge-1.20.1-2.3.4.jar                   |Botarium                      |botarium                      |2.3.4               |DONE      |Manifest: NOSIGNATURE
		mcw-windows-2.3.0-mc1.20.1forge.jar               |Macaw's Windows               |mcwwindows                    |2.3.0               |DONE      |Manifest: NOSIGNATURE
		create_jetpack-forge-4.3.2.jar                    |Create Jetpack                |create_jetpack                |4.3.2               |DONE      |Manifest: NOSIGNATURE
		IronJetpacks-1.20.1-7.0.8.jar                     |Iron Jetpacks                 |ironjetpacks                  |7.0.8               |DONE      |Manifest: NOSIGNATURE
		everythingcopper-1.20.1-2.3.4.jar                 |Everything is Copper          |everythingcopper              |1.20.1-2.3.4        |DONE      |Manifest: NOSIGNATURE
		Powah-5.0.10.jar                                  |Powah                         |powah                         |5.0.10              |DONE      |Manifest: NOSIGNATURE
		cabletiers-1.20.1-1.2.2.jar                       |Cable Tiers                   |cabletiers                    |1.20.1-1.2.2        |DONE      |Manifest: NOSIGNATURE
		rangedpumps-1.1.0.jar                             |Ranged Pumps                  |rangedpumps                   |1.1.0               |DONE      |Manifest: NOSIGNATURE
		balm-forge-1.20.1-7.3.27-all.jar                  |Balm                          |balm                          |7.3.27              |DONE      |Manifest: NOSIGNATURE
		JustEnoughResources-1.20.1-*********.jar          |Just Enough Resources         |jeresources                   |*********           |DONE      |Manifest: NOSIGNATURE
		shetiphiancore-forge-1.20.1-1.4.jar               |ShetiPhian-Core               |shetiphiancore                |1.20.1-1.4          |DONE      |Manifest: NOSIGNATURE
		MysticalAgradditions-1.20.1-7.0.9.jar             |Mystical Agradditions         |mysticalagradditions          |7.0.9               |DONE      |Manifest: NOSIGNATURE
		[forge]ctov-3.4.13.jar                            |ChoiceTheorem's Overhauled Vil|ctov                          |3.4.13              |DONE      |Manifest: NOSIGNATURE
		athena-forge-1.20.1-3.1.2.jar                     |Athena                        |athena                        |3.1.2               |DONE      |Manifest: NOSIGNATURE
		stylecolonies-1.13-1.20.1.jar                     |stylecolonies mod             |stylecolonies                 |1.13                |DONE      |Manifest: NOSIGNATURE
		novillagerdm-1.20.1-5.0.0.jar                     |No Villager Death Messages    |novillagerdm                  |5.0.0               |DONE      |Manifest: NOSIGNATURE
		alltheores-1.20.1-47.1.3-2.2.4.jar                |AllTheOres                    |alltheores                    |2.2.4               |DONE      |Manifest: NOSIGNATURE
		Glodium-1.20-1.5-forge.jar                        |Glodium                       |glodium                       |1.20-1.5-forge      |DONE      |Manifest: NOSIGNATURE
		ae2insertexportcard-1.20.1-1.3.0.jar              |AE2 Insert Export Card        |ae2insertexportcard           |1.20.1-1.3.0        |DONE      |Manifest: NOSIGNATURE
		torchmaster-20.1.9.jar                            |Torchmaster                   |torchmaster                   |20.1.9              |DONE      |Manifest: NOSIGNATURE
		maidensmerrymaking-1-20.1-7.jar                   |Maiden's MerryMaking          |maidensmerrymaking            |1.0.0               |DONE      |Manifest: NOSIGNATURE
		BotanyTrees-Forge-1.20.1-9.0.18.jar               |BotanyTrees                   |botanytrees                   |9.0.18              |DONE      |Manifest: NOSIGNATURE
		ironfurnaces-1.20.1-4.1.6.jar                     |Iron Furnaces                 |ironfurnaces                  |4.1.6               |DONE      |Manifest: NOSIGNATURE
		mcw-trapdoors-1.1.4-mc1.20.1forge.jar             |Macaw's Trapdoors             |mcwtrpdoors                   |1.1.4               |DONE      |Manifest: NOSIGNATURE
		supermartijn642corelib-1.1.18-forge-mc1.20.1.jar  |SuperMartijn642's Core Lib    |supermartijn642corelib        |1.1.18              |DONE      |Manifest: NOSIGNATURE
		resourcefulconfig-forge-1.20.1-2.1.3.jar          |Resourcefulconfig             |resourcefulconfig             |2.1.3               |DONE      |Manifest: NOSIGNATURE
		Ad-Astra-Giselle-Addon-forge-1.20.1-6.18.jar      |Ad Astra: Giselle Addon       |ad_astra_giselle_addon        |6.18                |DONE      |Manifest: NOSIGNATURE
		curios-forge-5.14.1+1.20.1.jar                    |Curios API                    |curios                        |5.14.1+1.20.1       |DONE      |Manifest: NOSIGNATURE
		Searchables-forge-1.20.1-1.0.3.jar                |Searchables                   |searchables                   |1.0.3               |DONE      |Manifest: NOSIGNATURE
		Measurements-forge-1.20.1-2.0.1.jar               |Measurements                  |measurements                  |2.0.1               |DONE      |Manifest: NOSIGNATURE
		FramedBlocks-9.3.1.jar                            |FramedBlocks                  |framedblocks                  |9.3.1               |DONE      |Manifest: NOSIGNATURE
		angelring-1.20.1-2.3.1.jar                        |Angel Ring 2                  |angelring                     |2.2.3               |DONE      |Manifest: NOSIGNATURE
		sparsestructuresreforged-1.20.1-1.0.0.jar         |SparseStructuresReforged      |sparsestructuresreforged      |1.20.1-1.0.0        |DONE      |Manifest: NOSIGNATURE
		mcw-furniture-3.3.0-mc1.20.1forge.jar             |Macaw's Furniture             |mcwfurnitures                 |3.3.0               |DONE      |Manifest: NOSIGNATURE
		flightlib-forge-2.1.0.jar                         |Flight Lib                    |flightlib                     |2.1.0               |DONE      |Manifest: NOSIGNATURE
		JadeAddons-1.20.1-Forge-5.3.1.jar                 |Jade Addons                   |jadeaddons                    |5.3.1+forge         |DONE      |Manifest: NOSIGNATURE
		infiniverse-1.20.1-1.0.0.5.jar                    |Infiniverse                   |infiniverse                   |1.0.0.5             |DONE      |Manifest: NOSIGNATURE
		CodeChickenLib-1.20.1-4.4.0.516-universal.jar     |CodeChicken Lib               |codechickenlib                |4.4.0.516           |DONE      |Manifest: 31:e6:db:63:47:4a:6e:e0:0a:2c:11:d1:76:db:4e:82:ff:56:2d:29:93:d2:e5:02:bd:d3:bd:9d:27:47:a5:71
		BrandonsCore-1.20.1-3.2.1.302-universal.jar       |Brandon's Core                |brandonscore                  |3.2.1.302           |DONE      |Manifest: 53:bb:a0:11:bd:61:e2:1a:e2:cb:fd:f8:4f:e4:cd:a5:cc:12:f4:43:f0:78:68:3b:e1:62:c6:78:3b:27:ff:fe
		YungsBetterMineshafts-1.20-Forge-4.0.4.jar        |YUNG's Better Mineshafts      |bettermineshafts              |1.20-Forge-4.0.4    |DONE      |Manifest: NOSIGNATURE
		sliceanddice-forge-3.3.0.jar                      |Create Slice & Dice           |sliceanddice                  |3.3.0               |DONE      |Manifest: NOSIGNATURE
		DarkPaintings-Forge-1.20.1-17.0.4.jar             |DarkPaintings                 |darkpaintings                 |17.0.4              |DONE      |Manifest: eb:c4:b1:67:8b:f9:0c:db:dc:4f:01:b1:8e:61:64:39:4c:10:85:0b:a6:c4:c7:48:f0:fa:95:f2:cb:08:3a:e5
		crafting-on-a-stick-1.20.1-1.1.5.jar              |Crafting On A Stick           |crafting_on_a_stick           |1.1.5               |DONE      |Manifest: NOSIGNATURE
		elytraslot-forge-6.4.4+1.20.1.jar                 |Elytra Slot                   |elytraslot                    |6.4.4+1.20.1        |DONE      |Manifest: NOSIGNATURE
		harvestwithease-1.20.1-*******-forge.jar          |Harvest with ease             |harvestwithease               |*******             |DONE      |Manifest: NOSIGNATURE
		multipiston-1.20-1.2.43-RELEASE.jar               |Multi-Piston                  |multipiston                   |1.20-1.2.43-RELEASE |DONE      |Manifest: NOSIGNATURE
		lithostitched-forge-1.20.1-1.4.4.jar              |Lithostitched                 |lithostitched                 |1.4                 |DONE      |Manifest: NOSIGNATURE
		dyenamicsandfriends-1.20.1-1.9.2.jar              |Dyenamics and Friends         |dyenamicsandfriends           |1.20.1-1.9.2        |DONE      |Manifest: NOSIGNATURE
		bdlib-********-mc1.20.1.jar                       |BdLib                         |bdlib                         |********            |DONE      |Manifest: NOSIGNATURE
		travelersbackpack-forge-1.20.1-9.1.36.jar         |Traveler's Backpack           |travelersbackpack             |9.1.36              |DONE      |Manifest: NOSIGNATURE
		NaturesCompass-1.20.1-1.11.2-forge.jar            |Nature's Compass              |naturescompass                |1.20.1-1.11.2-forge |DONE      |Manifest: NOSIGNATURE
		jumpboat-1.20.0-1.0.5.jar                         |Jumpy Boats                   |jumpboat                      |1.20.0-1.0.5        |DONE      |Manifest: NOSIGNATURE
		LibX-1.20.1-5.0.14.jar                            |LibX                          |libx                          |1.20.1-5.0.14       |DONE      |Manifest: NOSIGNATURE
		UtilitiX-1.20.1-0.8.25.jar                        |UtilitiX                      |utilitix                      |1.20.1-0.8.25       |DONE      |Manifest: NOSIGNATURE
		jei-1.20.1-forge-15.20.0.106.jar                  |Just Enough Items             |jei                           |15.20.0.106         |DONE      |Manifest: NOSIGNATURE
		Mekanism-1.20.1-10.4.16.80.jar                    |Mekanism                      |mekanism                      |10.4.16             |DONE      |Manifest: NOSIGNATURE
		GravitationalModulatingAdditionalUnit-1.20.1-3.4.j|Gravitational Modulating Addit|gravitationalmodulatingunittwe|3.4                 |DONE      |Manifest: NOSIGNATURE
		MekanismGenerators-1.20.1-10.4.16.80.jar          |Mekanism: Generators          |mekanismgenerators            |10.4.16             |DONE      |Manifest: NOSIGNATURE
		[通用机械：扩展] mekanism_extras-1.20.1-1.4.3.jar        |Mekanism Extras               |mekanism_extras               |1.20.1-1.4.3        |DONE      |Manifest: NOSIGNATURE
		GlitchCore-forge-1.20.1-0.0.1.1.jar               |GlitchCore                    |glitchcore                    |0.0.1.1             |DONE      |Manifest: NOSIGNATURE
		BiomesOPlenty-forge-1.20.1-19.0.0.96.jar          |Biomes O' Plenty              |biomesoplenty                 |19.0.0.96           |DONE      |Manifest: NOSIGNATURE
		pneumaticcraft-repressurized-6.0.20+mc1.20.1.jar  |PneumaticCraft: Repressurized |pneumaticcraft                |6.0.20+mc1.20.1     |DONE      |Manifest: NOSIGNATURE
		[星光] starlight-1.1.2+forge.1cda73c.jar            |Starlight                     |starlight                     |1.1.2+forge.1cda73c |DONE      |Manifest: NOSIGNATURE
		PackingTape-1.20.1-0.14.3.jar                     |Packing Tape                  |packingtape                   |0.14.3              |DONE      |Manifest: NOSIGNATURE
		forge-1.20.1-47.4.4-universal.jar                 |Forge                         |forge                         |47.4.4              |DONE      |Manifest: 84:ce:76:e8:45:35:e4:0e:63:86:df:47:59:80:0f:67:6c:c1:5f:6e:5f:4d:b3:54:47:1a:9f:7f:ed:5e:f2:90
		cofh_core-1.20.1-11.0.2.56.jar                    |CoFH Core                     |cofh_core                     |11.0.2              |DONE      |Manifest: NOSIGNATURE
		thermal_core-1.20.1-11.0.6.24.jar                 |Thermal Series                |thermal                       |11.0.6              |DONE      |Manifest: NOSIGNATURE
		thermal_integration-1.20.1-11.0.1.27.jar          |Thermal Integration           |thermal_integration           |11.0.1              |DONE      |Manifest: NOSIGNATURE
		redstone_arsenal-1.20.1-8.0.1.24.jar              |Redstone Arsenal              |redstone_arsenal              |8.0.1               |DONE      |Manifest: NOSIGNATURE
		thermal_cultivation-1.20.1-11.0.1.24.jar          |Thermal Cultivation           |thermal_cultivation           |11.0.1              |DONE      |Manifest: NOSIGNATURE
		thermal_innovation-1.20.1-11.0.1.23.jar           |Thermal Innovation            |thermal_innovation            |11.0.1              |DONE      |Manifest: NOSIGNATURE
		silent-gear-1.20.1-3.6.6.jar                      |Silent Gear                   |silentgear                    |3.6.6               |DONE      |Manifest: NOSIGNATURE
		thermal_foundation-1.20.1-11.0.6.70.jar           |Thermal Foundation            |thermal_foundation            |11.0.6              |DONE      |Manifest: NOSIGNATURE
		thermal_locomotion-1.20.1-11.0.1.19.jar           |Thermal Locomotion            |thermal_locomotion            |11.0.1              |DONE      |Manifest: NOSIGNATURE
		thermal_dynamics-1.20.1-11.0.1.23.jar             |Thermal Dynamics              |thermal_dynamics              |11.0.1              |DONE      |Manifest: NOSIGNATURE
		mcw-paths-1.1.0forge-mc1.20.1.jar                 |Macaw's Paths and Pavings     |mcwpaths                      |1.1.0               |DONE      |Manifest: NOSIGNATURE
		alchemistry-1.20.1-2.3.4.jar                      |Alchemistry                   |alchemistry                   |2.3.4               |DONE      |Manifest: NOSIGNATURE
		ZeroCore2-1.20.1-2.1.47.jar                       |Zero CORE 2                   |zerocore                      |1.20.1-2.1.47       |DONE      |Manifest: NOSIGNATURE
		SimpleBackups-1.20.1-3.1.8.jar                    |Simple Backups                |simplebackups                 |1.20.1-3.1.8        |DONE      |Manifest: NOSIGNATURE
		ImmersiveEngineering-1.20.1-10.2.0-183.jar        |Immersive Engineering         |immersiveengineering          |1.20.1-10.2.0-183   |DONE      |Manifest: 44:39:94:cf:1d:8c:be:3c:7f:a9:ee:f4:1e:63:a5:ac:61:f9:c2:87:d5:5b:d9:d6:8c:b5:3e:96:5d:8e:3f:b7
		createoreexcavation-1.20-1.5.3.jar                |Create Ore Excavation         |createoreexcavation           |1.5.3               |DONE      |Manifest: NOSIGNATURE
		NoChatReports-FORGE-1.20.1-v2.2.2.jar             |No Chat Reports               |nochatreports                 |1.20.1-v2.2.2       |DONE      |Manifest: NOSIGNATURE
		allthemodium-1.20.1-47.1.25-2.5.6.jar             |Allthemodium                  |allthemodium                  |2.5.6               |DONE      |Manifest: NOSIGNATURE
		spectrelib-forge-0.13.17+1.20.1.jar               |SpectreLib                    |spectrelib                    |0.13.17+1.20.1      |DONE      |Manifest: NOSIGNATURE
		kffmod-4.11.0.jar                                 |Kotlin For Forge              |kotlinforforge                |4.11.0              |DONE      |Manifest: NOSIGNATURE
		pipez-forge-1.20.1-1.2.21.jar                     |Pipez                         |pipez                         |1.20.1-1.2.21       |DONE      |Manifest: NOSIGNATURE
		IntegratedDynamics-1.20.1-1.25.6.jar              |IntegratedDynamics            |integrateddynamics            |1.25.6              |DONE      |Manifest: NOSIGNATURE
		itemcollectors-1.1.10-forge-mc1.20.jar            |Item Collectors               |itemcollectors                |1.1.10              |DONE      |Manifest: NOSIGNATURE
		Croptopia-1.20.1-FORGE-3.0.4.jar                  |Croptopia                     |croptopia                     |3.0.4               |DONE      |Manifest: NOSIGNATURE
		serverconfigupdater-4.0.2.jar                     |ServerConfig Updater          |serverconfigupdater           |4.0.2               |DONE      |Manifest: NOSIGNATURE
		polymorph-forge-0.49.9+1.20.1.jar                 |Polymorph                     |polymorph                     |0.49.9+1.20.1       |DONE      |Manifest: NOSIGNATURE
		Zeta-1.0-30.jar                                   |Zeta                          |zeta                          |1.0-30              |DONE      |Manifest: NOSIGNATURE
		railcraft-reborn-1.20.1-1.1.9.jar                 |Railcraft Reborn              |railcraft                     |1.1.9               |DONE      |Manifest: NOSIGNATURE
		oceansdelight-1.0.2-1.20.jar                      |Ocean's Delight               |oceansdelight                 |1.0.2-1.20          |DONE      |Manifest: NOSIGNATURE
		connectedglass-1.1.13-forge-mc1.20.1.jar          |Connected Glass               |connectedglass                |1.1.13              |DONE      |Manifest: NOSIGNATURE
		hyperbox-1.20.1-4.0.2.0.jar                       |Hyperbox                      |hyperbox                      |4.0.2.0             |DONE      |Manifest: NOSIGNATURE
		Aquaculture-1.20.1-2.5.5.jar                      |Aquaculture 2                 |aquaculture                   |2.5.5               |DONE      |Manifest: NOSIGNATURE
		cristellib-1.1.6-forge.jar                        |Cristel Lib                   |cristellib                    |1.1.6               |DONE      |Manifest: NOSIGNATURE
		totw_modded-forge-1.20.1-1.0.6.jar                |Towers of the Wild Modded     |totw_modded                   |1.0.6               |DONE      |Manifest: NOSIGNATURE
		CyclopsCore-1.20.1-1.19.10.jar                    |Cyclops Core                  |cyclopscore                   |1.19.10             |DONE      |Manifest: NOSIGNATURE
		blue_skies-1.20.1-1.3.31.jar                      |Blue Skies                    |blue_skies                    |1.3.31              |DONE      |Manifest: NOSIGNATURE
		YungsBetterWitchHuts-1.20-Forge-3.0.3.jar         |YUNG's Better Witch Huts      |betterwitchhuts               |1.20-Forge-3.0.3    |DONE      |Manifest: NOSIGNATURE
		netherportalfix-forge-1.20-13.0.1.jar             |NetherPortalFix               |netherportalfix               |13.0.1              |DONE      |Manifest: NOSIGNATURE
		aiotbotania-1.20.1-4.0.5.jar                      |AIOT Botania                  |aiotbotania                   |1.20.1-4.0.5        |DONE      |Manifest: NOSIGNATURE
		geckolib-forge-1.20.1-4.7.1.2.jar                 |GeckoLib 4                    |geckolib                      |4.7.1.2             |DONE      |Manifest: NOSIGNATURE
		creeperoverhaul-3.0.2-forge.jar                   |Creeper Overhaul              |creeperoverhaul               |3.0.2               |DONE      |Manifest: NOSIGNATURE
		ars_nouveau-1.20.1-4.12.6-all.jar                 |Ars Nouveau                   |ars_nouveau                   |4.12.6              |DONE      |Manifest: NOSIGNATURE
		ars_elemental-1.20.1-0.6.7.8.jar                  |Ars Elemental                 |ars_elemental                 |0.6.7.8             |DONE      |Manifest: NOSIGNATURE
		eidolon_repraised-1.20.1-0.3.8.15.jar             |Eidolon:Repraised             |eidolon                       |0.3.8.15            |DONE      |Manifest: NOSIGNATURE
		aether-1.20.1-1.5.2-neoforge.jar                  |The Aether                    |aether                        |1.20.1-1.5.2-neoforg|DONE      |Manifest: NOSIGNATURE
		lost_aether_content-1.20.1-1.2.3.jar              |Aether: Lost Content          |lost_aether_content           |1.2.3               |DONE      |Manifest: NOSIGNATURE
		morejs-forge-1.20.1-0.10.0.jar                    |MoreJS                        |morejs                        |0.10.0              |DONE      |Manifest: NOSIGNATURE
		naturalist-forge-4.0.3-1.20.1.jar                 |Naturalist                    |naturalist                    |4.0.3               |DONE      |Manifest: NOSIGNATURE
		cookingforblockheads-forge-1.20.1-16.0.13.jar     |CookingForBlockheads          |cookingforblockheads          |16.0.13             |DONE      |Manifest: NOSIGNATURE
		dankstorage-forge-1.20.1-15.jar                   |Dank Storage                  |dankstorage                   |15                  |DONE      |Manifest: NOSIGNATURE
		potionsmaster-1.20.1-47.1.70-0.6.0.jar            |PotionsMaster                 |potionsmaster                 |0.6.0               |DONE      |Manifest: NOSIGNATURE
		Twigs-1.20.1-3.1.0.jar                            |Twigs                         |twigs                         |1.20.1-3.1.1        |DONE      |Manifest: NOSIGNATURE
		create_dragon_lib-1.20.1-1.4.3.jar                |Create: Dragon Lib            |create_dragon_lib             |1.4.3               |DONE      |Manifest: NOSIGNATURE
		generatorgalore-1.20.1-1.2.4.jar                  |Generator Galore              |generatorgalore               |1.20.1-1.2.4        |DONE      |Manifest: NOSIGNATURE
		Steam_Rails-1.6.7+forge-mc1.20.1.jar              |Create: Steam 'n' Rails       |railways                      |1.6.7+forge-mc1.20.1|DONE      |Manifest: NOSIGNATURE
		twilightforest-1.20.1-4.3.2508-universal.jar      |The Twilight Forest           |twilightforest                |4.3.2508            |DONE      |Manifest: NOSIGNATURE
		mob_grinding_utils-1.20.1-1.1.0.jar               |Mob Grinding Utils            |mob_grinding_utils            |1.20.1-1.1.0        |DONE      |Manifest: NOSIGNATURE
		arseng-1.2.0.jar                                  |Ars Énergistique              |arseng                        |1.2.0               |DONE      |Manifest: NOSIGNATURE
		FarmersDelight-1.20.1-1.2.7.jar                   |Farmer's Delight              |farmersdelight                |1.20.1-1.2.7        |DONE      |Manifest: NOSIGNATURE
		corn_delight-1.1.6-1.20.1.jar                     |Corn Delight                  |corn_delight                  |1.1.6-1.20.1        |DONE      |Manifest: NOSIGNATURE
		ends_delight-2.5.1+forge.1.20.1.jar               |End's Delight                 |ends_delight                  |2.5.1+forge.1.20.1  |DONE      |Manifest: NOSIGNATURE
		entangled-1.3.20-forge-mc1.20.4.jar               |Entangled                     |entangled                     |1.3.20              |DONE      |Manifest: NOSIGNATURE
		CommonCapabilities-1.20.1-2.9.4.jar               |CommonCapabilities            |commoncapabilities            |2.9.4               |DONE      |Manifest: NOSIGNATURE
		endersdelight-forge-1.20.1-1.1.2.jar              |Ender's Delight               |endersdelight                 |1.1.2               |DONE      |Manifest: NOSIGNATURE
		noflyzone-1.20.1-1.1.0.jar                        |No-fly Zone                   |noflyzone                     |1.20.1-1.1.0        |DONE      |Manifest: NOSIGNATURE
		mcw-fences-1.2.0-1.20.1forge.jar                  |Macaw's Fences and Walls      |mcwfences                     |1.2.0               |DONE      |Manifest: NOSIGNATURE
		wirelesschargers-1.0.9a-forge-mc1.20.jar          |Wireless Chargers             |wirelesschargers              |1.0.9a              |DONE      |Manifest: NOSIGNATURE
		Patchouli-1.20.1-84.1-FORGE.jar                   |Patchouli                     |patchouli                     |1.20.1-84.1-FORGE   |DONE      |Manifest: NOSIGNATURE
		ars_ocultas-1.20.1-1.2.2-all.jar                  |Ars Ocultas                   |ars_ocultas                   |1.2.2               |DONE      |Manifest: NOSIGNATURE
		allthearcanistgear-1.20.1-20.0.0.jar              |All The Arcanist Gear         |allthearcanistgear            |1.20.1-20.0.0       |DONE      |Manifest: NOSIGNATURE
		thermal_expansion-1.20.1-11.0.1.29.jar            |Thermal Expansion             |thermal_expansion             |11.0.1              |DONE      |Manifest: NOSIGNATURE
		IntegratedTunnels-1.20.1-1.8.36.jar               |IntegratedTunnels             |integratedtunnels             |1.8.36              |DONE      |Manifest: NOSIGNATURE
		GunpowderLib-1.20.2-2.2.2.jar                     |GunpowderLib                  |gunpowderlib                  |1.20.2-2.2.2        |DONE      |Manifest: 2e:cb:db:61:22:2a:6d:79:f4:22:31:8c:34:9b:cf:9f:91:ea:95:c4:bf:bb:8a:de:6e:10:c3:f0:b1:c6:ae:20
		Exchangers-1.20.1-3.5.1.jar                       |Exchangers                    |exchangers                    |1.20.1-3.5.1        |DONE      |Manifest: 2e:cb:db:61:22:2a:6d:79:f4:22:31:8c:34:9b:cf:9f:91:ea:95:c4:bf:bb:8a:de:6e:10:c3:f0:b1:c6:ae:20
		ftb-ultimine-forge-2001.1.5.jar                   |FTB Ultimine                  |ftbultimine                   |2001.1.5            |DONE      |Manifest: NOSIGNATURE
		YungsBetterStrongholds-1.20-Forge-4.0.3.jar       |YUNG's Better Strongholds     |betterstrongholds             |1.20-Forge-4.0.3    |DONE      |Manifest: NOSIGNATURE
		resourcefullib-forge-1.20.1-2.1.29.jar            |Resourceful Lib               |resourcefullib                |2.1.29              |DONE      |Manifest: NOSIGNATURE
		MekanismTools-1.20.1-10.4.16.80.jar               |Mekanism: Tools               |mekanismtools                 |10.4.16             |DONE      |Manifest: NOSIGNATURE
		deeperdarker-forge-1.20.1-1.3.3.jar               |Deeper and Darker             |deeperdarker                  |1.3.3               |DONE      |Manifest: NOSIGNATURE
		architectury-9.2.14-forge.jar                     |Architectury                  |architectury                  |9.2.14              |DONE      |Manifest: NOSIGNATURE
		BambooEverything-forge-3.0.3+mc1.20.1.jar         |Bamboo Everything             |bambooeverything              |3.0.3+mc1.20.1      |DONE      |Manifest: NOSIGNATURE
		findme-3.2.1-forge.jar                            |FindMe                        |findme                        |3.2.1               |DONE      |Manifest: NOSIGNATURE
		observable-4.4.2.jar                              |Observable                    |observable                    |4.4.2               |DONE      |Manifest: NOSIGNATURE
		ftb-library-forge-2001.2.9.jar                    |FTB Library                   |ftblibrary                    |2001.2.9            |DONE      |Manifest: NOSIGNATURE
		ftb-teams-forge-2001.3.1.jar                      |FTB Teams                     |ftbteams                      |2001.3.1            |DONE      |Manifest: NOSIGNATURE
		ftb-ranks-forge-2001.1.5.jar                      |FTB Ranks                     |ftbranks                      |2001.1.5            |DONE      |Manifest: NOSIGNATURE
		ftb-essentials-forge-2001.2.2.jar                 |FTB Essentials                |ftbessentials                 |2001.2.2            |DONE      |Manifest: NOSIGNATURE
		ftb-chunks-forge-2001.3.6.jar                     |FTB Chunks                    |ftbchunks                     |2001.3.6            |DONE      |Manifest: NOSIGNATURE
		cc-tweaked-1.20.1-forge-1.113.1.jar               |CC: Tweaked                   |computercraft                 |1.113.1             |DONE      |Manifest: NOSIGNATURE
		Draconic-Evolution-1.20.1-3.1.2.604-universal.jar |Draconic Evolution            |draconicevolution             |3.1.2.604           |DONE      |Manifest: 53:bb:a0:11:bd:61:e2:1a:e2:cb:fd:f8:4f:e4:cd:a5:cc:12:f4:43:f0:78:68:3b:e1:62:c6:78:3b:27:ff:fe
		energymeter-forge-1.20.1-1.0.0.jar                |Energy Meter                  |energymeter                   |1.20.1-1.0.0        |DONE      |Manifest: NOSIGNATURE
		Stargate Journey-1.20.1-0.6.33 Hotfix.jar         |Stargate Journey              |sgjourney                     |0.6.33              |DONE      |Manifest: NOSIGNATURE
		ExtremeReactors2-1.20.1-2.0.92.jar                |Extreme Reactors              |bigreactors                   |1.20.1-2.0.92       |DONE      |Manifest: NOSIGNATURE
		productivebees-1.20.1-12.6.0.jar                  |Productive Bees               |productivebees                |1.20.1-12.6.0       |DONE      |Manifest: NOSIGNATURE
		trashcans-1.0.18b-forge-mc1.20.jar                |Trash Cans                    |trashcans                     |1.0.18b             |DONE      |Manifest: NOSIGNATURE
		Towns-and-Towers-1.12-Fabric+Forge.jar            |Towns and Towers              |t_and_t                       |0.0NONE             |DONE      |Manifest: NOSIGNATURE
		YeetusExperimentus-Forge-2.3.1-build.6+mc1.20.1.ja|Yeetus Experimentus           |yeetusexperimentus            |2.3.1-build.6+mc1.20|DONE      |Manifest: NOSIGNATURE
		voidtotem-forge-1.20-3.0.1.jar                    |Void Totem                    |voidtotem                     |3.0.1               |DONE      |Manifest: NOSIGNATURE
		rhino-forge-2001.2.3-build.10.jar                 |Rhino                         |rhino                         |2001.2.3-build.10   |DONE      |Manifest: NOSIGNATURE
		kubejs-forge-2001.6.5-build.16.jar                |KubeJS                        |kubejs                        |2001.6.5-build.16   |ERROR     |Manifest: NOSIGNATURE
		Cucumber-1.20.1-7.0.13.jar                        |Cucumber Library              |cucumber                      |7.0.13              |DONE      |Manifest: NOSIGNATURE
		matc-1.6.0.jar                                    |Mystical Agriculture Tiered Cr|matc                          |1.6.0               |DONE      |Manifest: NOSIGNATURE
		trashslot-forge-1.20-15.1.2.jar                   |TrashSlot                     |trashslot                     |15.1.2              |DONE      |Manifest: NOSIGNATURE
		jmi-forge-1.20.1-0.14-48.jar                      |JourneyMap Integration        |jmi                           |1.20.1-0.14-48      |DONE      |Manifest: NOSIGNATURE
		amendments-1.20-1.2.19.jar                        |Amendments                    |amendments                    |1.20-1.2.19         |DONE      |Manifest: NOSIGNATURE
		blueflame-1.20.0-1.0.3.jar                        |Blue Flame Burning            |blueflame                     |1.20.0-1.0.3        |DONE      |Manifest: NOSIGNATURE
		sophisticatedstorage-1.20.1-1.3.33.1128.jar       |Sophisticated Storage         |sophisticatedstorage          |1.3.33.1128         |DONE      |Manifest: NOSIGNATURE
		allthewizardgear-1.20.1-1.1.4.jar                 |All The Wizard Gear           |allthewizardgear              |1.20.1-1.1.4        |DONE      |Manifest: NOSIGNATURE
		additionallanterns-1.1.1a-forge-mc1.20.jar        |Additional Lanterns           |additionallanterns            |1.1.1a              |DONE      |Manifest: NOSIGNATURE
		item-filters-forge-2001.1.0-build.59.jar          |Item Filters                  |itemfilters                   |2001.1.0-build.59   |DONE      |Manifest: NOSIGNATURE
		ftb-quests-forge-2001.4.13.jar                    |FTB Quests                    |ftbquests                     |2001.4.13           |DONE      |Manifest: NOSIGNATURE
		ftb-xmod-compat-forge-2.1.3.jar                   |FTB XMod Compat               |ftbxmodcompat                 |2.1.3               |DONE      |Manifest: NOSIGNATURE
		productivelib-1.20.1-0.0.4.jar                    |Productive Lib                |productivelib                 |1.20.1-0.0.4        |DONE      |Manifest: NOSIGNATURE
		ensorcellation-1.20.1-5.0.2.24.jar                |Ensorcellation                |ensorcellation                |5.0.2               |DONE      |Manifest: NOSIGNATURE
		create-1.20.1-0.5.1.j.jar                         |Create                        |create                        |0.5.1.j             |DONE      |Manifest: NOSIGNATURE
		ars_creo-1.20.1-4.1.0.jar                         |Ars Creo                      |ars_creo                      |4.1.0               |DONE      |Manifest: NOSIGNATURE
		ponderjs-1.20.1-1.4.0.jar                         |PonderJS                      |ponderjs                      |1.4.0               |DONE      |Manifest: NOSIGNATURE
		waystones-forge-1.20.1-14.1.11.jar                |Waystones                     |waystones                     |14.1.11             |DONE      |Manifest: NOSIGNATURE
		Structory_1.20.x_v1.3.5.jar                       |Structory                     |structory                     |1.3.5               |DONE      |Manifest: NOSIGNATURE
		FastSuite-1.20.1-5.1.0.jar                        |Fast Suite                    |fastsuite                     |5.1.0               |DONE      |Manifest: NOSIGNATURE
		journeymap-1.20.1-5.10.3-forge.jar                |Journeymap                    |journeymap                    |5.10.3              |DONE      |Manifest: NOSIGNATURE
		comforts-forge-6.4.0+1.20.1.jar                   |Comforts                      |comforts                      |6.4.0+1.20.1        |DONE      |Manifest: NOSIGNATURE
		alternate_current-mc1.20-1.7.0.jar                |Alternate Current             |alternate_current             |1.7.0               |DONE      |Manifest: NOSIGNATURE
		DimStorage-1.20.1-8.0.1.jar                       |DimStorage                    |dimstorage                    |8.0.1               |DONE      |Manifest: NOSIGNATURE
		Dungeon Crawl-1.20.1-2.3.15.jar                   |Dungeon Crawl                 |dungeoncrawl                  |2.3.15              |DONE      |Manifest: NOSIGNATURE
		charginggadgets-1.11.0.jar                        |Charging Gadgets              |charginggadgets               |1.11.0              |DONE      |Manifest: NOSIGNATURE
		mcjtylib-1.20-8.0.6.jar                           |McJtyLib                      |mcjtylib                      |1.20-8.0.6          |DONE      |Manifest: NOSIGNATURE
		rftoolsbase-1.20-5.0.6.jar                        |RFToolsBase                   |rftoolsbase                   |1.20-5.0.6          |DONE      |Manifest: NOSIGNATURE
		rftoolspower-1.20-6.0.2.jar                       |RFToolsPower                  |rftoolspower                  |1.20-6.0.2          |DONE      |Manifest: NOSIGNATURE
		rftoolsbuilder-1.20-6.0.8.jar                     |RFToolsBuilder                |rftoolsbuilder                |1.20-6.0.8          |DONE      |Manifest: NOSIGNATURE
		deepresonance-1.20-5.0.4.jar                      |DeepResonance                 |deepresonance                 |1.20-5.0.4          |DONE      |Manifest: NOSIGNATURE
		xnet-1.20-6.1.6.jar                               |XNet                          |xnet                          |1.20-6.1.6          |DONE      |Manifest: NOSIGNATURE
		xnetgases-1.20.1-5.1.4.jar                        |XNet Gases                    |xnetgases                     |5.1.4               |DONE      |Manifest: NOSIGNATURE
		rftoolsstorage-1.20-5.0.3.jar                     |RFToolsStorage                |rftoolsstorage                |1.20-5.0.3          |DONE      |Manifest: NOSIGNATURE
		rftoolscontrol-1.20-7.0.3.jar                     |RFToolsControl                |rftoolscontrol                |1.20-7.0.3          |DONE      |Manifest: NOSIGNATURE
		YungsBetterDesertTemples-1.20-Forge-3.0.3.jar     |YUNG's Better Desert Temples  |betterdeserttemples           |1.20-Forge-3.0.3    |DONE      |Manifest: NOSIGNATURE
		mahoutsukai-1.20.1-v1.34.78.jar                   |Mahou Tsukai                  |mahoutsukai                   |1.20.1-v1.34.78     |DONE      |Manifest: NOSIGNATURE
		Terralith_1.20.x_v2.5.4.jar                       |Terralith                     |terralith                     |2.5.4               |DONE      |Manifest: NOSIGNATURE
		bloodmagic-1.20.1-3.3.3-45.jar                    |Blood Magic                   |bloodmagic                    |3.3.3-45            |DONE      |Manifest: NOSIGNATURE
		rftoolsutility-1.20-6.0.6.jar                     |RFToolsUtility                |rftoolsutility                |1.20-6.0.6          |DONE      |Manifest: NOSIGNATURE
		moonlight-1.20-2.13.82-forge.jar                  |Moonlight Library             |moonlight                     |1.20-2.13.82        |DONE      |Manifest: NOSIGNATURE
		configuration-forge-1.20.1-3.1.0.jar              |Configuration                 |configuration                 |3.1.0               |DONE      |Manifest: NOSIGNATURE
		gtceu-1.20.1-7.1.0-SNAPSHOT+20250729-084616-865332|GregTech                      |gtceu                         |7.1.0-SNAPSHOT+86533|DONE      |Manifest: NOSIGNATURE
		ToolBelt-1.20.1-1.20.02.jar                       |Tool Belt                     |toolbelt                      |1.20.02             |DONE      |Manifest: NOSIGNATURE
		titanium-1.20.1-3.8.32.jar                        |Titanium                      |titanium                      |3.8.32              |DONE      |Manifest: NOSIGNATURE
		silent-lib-1.20.1-8.0.0.jar                       |Silent Lib                    |silentlib                     |8.0.0               |DONE      |Manifest: NOSIGNATURE
		mixinsquared-forge-0.1.2-beta.6.jar               |MixinSquared                  |mixinsquared                  |0.1.2-beta.6        |DONE      |Manifest: NOSIGNATURE
		Jade-1.20.1-Forge-11.13.1.jar                     |Jade                          |jade                          |11.13.1+forge       |DONE      |Manifest: NOSIGNATURE
		appliedenergistics2-forge-15.3.4.jar              |Applied Energistics 2         |ae2                           |15.3.4              |DONE      |Manifest: NOSIGNATURE
		AEInfinityBooster-1.20.1-1.0.0+21.jar             |AEInfinityBooster             |aeinfinitybooster             |1.20.1-1.0.0+21     |DONE      |Manifest: NOSIGNATURE
		ae2wtlib-15.2.3-forge.jar                         |AE2WTLib                      |ae2wtlib                      |15.2.3-forge        |DONE      |Manifest: NOSIGNATURE
		ExtendedAE-1.20-1.3.5-forge.jar                   |ExtendedAE                    |expatternprovider             |1.20-1.3.5-forge    |DONE      |Manifest: NOSIGNATURE
		AdvancedAE-1.1.2-1.20.1.jar                       |Advanced AE                   |advanced_ae                   |1.1.2-1.20.1        |DONE      |Manifest: NOSIGNATURE
		AE2-Things-1.2.1.jar                              |AE2 Things                    |ae2things                     |1.2.1               |DONE      |Manifest: NOSIGNATURE
		polyeng-forge-0.1.1-1.20.1.jar                    |Polymorphic Energistics       |polyeng                       |0.1.1-1.20.1        |DONE      |Manifest: NOSIGNATURE
		AppliedFlux-1.20-1.2.1-forge.jar                  |AppliedFlux                   |appflux                       |1.20-1.2.1-forge    |DONE      |Manifest: NOSIGNATURE
		merequester-forge-1.20.1-1.1.5.jar                |ME Requester                  |merequester                   |1.20.1-1.1.5        |DONE      |Manifest: NOSIGNATURE
		forbidden_arcanus-1.20.1-2.2.6.jar                |Forbidden & Arcanus           |forbidden_arcanus             |1.20.1-2.2.6        |DONE      |Manifest: NOSIGNATURE
		theurgy-1.20.1-1.23.4.jar                         |Theurgy                       |theurgy                       |1.23.4              |DONE      |Manifest: NOSIGNATURE
		nethersdelight-1.20.1-4.0.jar                     |Nether's Delight              |nethersdelight                |1.20.1-4.0          |DONE      |Manifest: NOSIGNATURE
		Quark-4.0-462.jar                                 |Quark                         |quark                         |4.0-462             |DONE      |Manifest: NOSIGNATURE
		supplementaries-1.20-3.1.13.jar                   |Supplementaries               |supplementaries               |1.20-3.1.13         |DONE      |Manifest: NOSIGNATURE
		allthecompressed-1.20.1-3.0.2.jar                 |AllTheCompressed              |allthecompressed              |3.0.2               |DONE      |Manifest: NOSIGNATURE
		Delightful-1.20.1-3.7.1.jar                       |Delightful                    |delightful                    |3.7.1               |DONE      |Manifest: NOSIGNATURE
		chemlib-1.20.1-2.0.19.jar                         |ChemLib                       |chemlib                       |2.0.19              |DONE      |Manifest: NOSIGNATURE
		enderchests-forge-1.20.1-1.3.jar                  |EnderChests                   |enderchests                   |1.20.1-1.3          |DONE      |Manifest: NOSIGNATURE
		JustEnoughMekanismMultiblocks-1.20.1-4.10.jar     |Just Enough Mekanism Multibloc|jei_mekanism_multiblocks      |4.10                |DONE      |Manifest: NOSIGNATURE
		inv_view_forge-2.1.0-1.20.1.jar                   |InvView_Forge                 |inv_view_forge                |2.1.0-1.20.1        |DONE      |Manifest: NOSIGNATURE
		Applied-Botanics-forge-1.5.0.jar                  |Applied Botanics              |appbot                        |1.5.0               |DONE      |Manifest: NOSIGNATURE
		modonomicon-1.20.1-forge-1.77.6.jar               |Modonomicon                   |modonomicon                   |1.77.6              |DONE      |Manifest: NOSIGNATURE
		rsinsertexportupgrade-1.20.1-1.4.0.jar            |RS Insert Export Upgrade      |rsinsertexportupgrade         |1.20.1-1.4.0        |DONE      |Manifest: NOSIGNATURE
		solcarrot-1.20.1-1.15.1.jar                       |Spice of Life: Carrot Edition |solcarrot                     |1.15.1              |DONE      |Manifest: NOSIGNATURE
		luminara-1.20.1-1.0.8-a193d85.jar                 |Luminara Mod                  |arclight                      |1.20.1-1.0.8-a193d85|DONE      |Manifest: NOSIGNATURE
		moredragoneggs-4.0.jar                            |More Dragon Eggs              |moredragoneggs                |4.0                 |DONE      |Manifest: NOSIGNATURE
		refinedstorageaddons-0.10.0.jar                   |Refined Storage Addons        |refinedstorageaddons          |0.10.0              |DONE      |Manifest: NOSIGNATURE
		refinedpolymorph-0.1.1-1.20.1.jar                 |Refined Polymorphism          |refinedpolymorph              |0.1.1-1.20.1        |DONE      |Manifest: NOSIGNATURE
		Applied-Mekanistics-1.4.2.jar                     |Applied Mekanistics           |appmek                        |1.4.2               |DONE      |Manifest: NOSIGNATURE
		AEAdditions-1.20.1-5.0.6.jar                      |AE Additions                  |ae2additions                  |5.0.6               |DONE      |Manifest: NOSIGNATURE
		megacells-forge-2.4.6-1.20.1.jar                  |MEGA Cells                    |megacells                     |2.4.6-1.20.1        |DONE      |Manifest: NOSIGNATURE
		packetfixer-forge-2.0.0-1.19-to-1.20.1.jar        |Packet Fixer                  |packetfixer                   |2.0.0               |DONE      |Manifest: NOSIGNATURE
		expandability-forge-9.0.4.jar                     |ExpandAbility                 |expandability                 |9.0.4               |DONE      |Manifest: NOSIGNATURE
	Crash Report UUID: 96408fdf-a5f4-49d2-82bb-92485686fdc3
	FML: 47.4
	Forge: net.minecraftforge:47.4.4