<?xml version="1.0" encoding="UTF-8" ?>
<!--
  Copyright (c) 2006, 2024, Oracle and/or its affiliates.

  This program is free software; you can redistribute it and/or modify it under the terms of the GNU General Public License, version 2.0, as published by
  the Free Software Foundation.

  This program is designed to work with certain software that is licensed under separate terms, as designated in a particular file or component or in
  included license documentation. The authors of MySQL hereby grant you an additional permission to link the program and your derivative works with the
  separately licensed software that they have either included with the program or referenced in the documentation.

  Without limiting anything contained in the foregoing, this file, which is part of MySQL Connector/J, is also subject to the Universal FOSS Exception,
  version 1.0, a copy of which can be found at http://oss.oracle.com/licenses/universal-foss-exception.

  This program is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
  FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License, version 2.0, for more details.

  You should have received a copy of the GNU General Public License along with this program; if not, write to the Free Software Foundation, Inc.,
  51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.mysql</groupId>
  <artifactId>mysql-connector-j</artifactId>
  <version>9.1.0</version>
  <packaging>jar</packaging>

  <name>MySQL Connector/J</name>
  <description>JDBC Type 4 driver for MySQL.</description>
  <url>http://dev.mysql.com/doc/connector-j/en/</url>

  <licenses>
    <license>
      <name>The GNU General Public License, v2 with Universal FOSS Exception, v1.0</name>
      <distribution>repo</distribution>
      <comments>For detailed license information see the LICENSE file in this distribution.</comments>
    </license>
  </licenses>

  <organization>
    <name>Oracle Corporation</name>
    <url>https://www.oracle.com/</url>
  </organization>

  <developers>
    <developer>
      <name>Filipe Silva</name>
      <email><EMAIL></email>
      <organization>Oracle Corporation</organization>
      <organizationUrl>https://www.oracle.com/</organizationUrl>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:**************:mysql/mysql-connector-j.git</connection>
    <url>https://github.com/mysql/mysql-connector-j</url>
  </scm>

  <dependencies>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>4.26.1</version>
    </dependency>

    <dependency>
      <groupId>com.oracle.oci.sdk</groupId>
      <artifactId>oci-java-sdk-common</artifactId>
      <version>3.41.2</version>
      <optional>true</optional>
    </dependency>
  </dependencies>
</project>
