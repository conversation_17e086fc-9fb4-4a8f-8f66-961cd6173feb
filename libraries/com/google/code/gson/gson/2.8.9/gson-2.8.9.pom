<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google.code.gson</groupId>
    <artifactId>gson-parent</artifactId>
    <version>2.8.9</version>
  </parent>

  <artifactId>gson</artifactId>
  <name>Gson</name>

  <properties>
    <proguardVersion>7.1.1</proguardVersion>
  </properties>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <includePackageNames>com.google.gson</includePackageNames>
          <excludePackageNames>com.google.gson.internal:com.google.gson.internal.bind</excludePackageNames>
          <links>
            <link>https://docs.oracle.com/javase/6/docs/api/</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
        <version>6.0.0</version>
        <executions>
          <execution>
            <goals>
              <goal>bnd-process</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>templating-maven-plugin</artifactId>
        <version>1.0.0</version>
        <executions>
          <execution>
            <id>filtering-java-templates</id>
            <goals>
              <goal>filter-sources</goal>
            </goals>
            <configuration>
              <sourceDirectory>${basedir}/src/main/java-templates</sourceDirectory>
              <outputDirectory>${project.build.directory}/generated-sources/java-templates</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.coderplus.maven.plugins</groupId>
        <artifactId>copy-rename-maven-plugin</artifactId>
        <version>1.0.1</version>
        <executions>
          <execution>
            <id>pre-obfuscate-class</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>rename</goal>
            </goals>
            <configuration>
              <fileSets>
                <fileSet>
                  <sourceFile>${project.build.directory}/test-classes/com/google/gson/functional/EnumWithObfuscatedTest.class</sourceFile>
                  <destinationFile>${project.build.directory}/test-classes-obfuscated-injar/com/google/gson/functional/EnumWithObfuscatedTest.class</destinationFile>
                </fileSet>
                <fileSet>
                  <sourceFile>${project.build.directory}/test-classes/com/google/gson/functional/EnumWithObfuscatedTest$Gender.class</sourceFile>
                  <destinationFile>${project.build.directory}/test-classes-obfuscated-injar/com/google/gson/functional/EnumWithObfuscatedTest$Gender.class</destinationFile>
                </fileSet>
              </fileSets>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.wvengen</groupId>
        <artifactId>proguard-maven-plugin</artifactId>
        <version>2.5.1</version>
        <executions>
          <execution>
            <phase>process-test-classes</phase>
            <goals>
              <goal>proguard</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <proguardVersion>${proguardVersion}</proguardVersion>
          <obfuscate>true</obfuscate>
          <injar>test-classes-obfuscated-injar</injar>
          <outjar>test-classes-obfuscated-outjar</outjar>
          <inFilter>**/*.class</inFilter>
          <proguardInclude>${basedir}/src/test/resources/testcases-proguard.conf</proguardInclude>
          <libs>
            <lib>${project.build.directory}/classes</lib>
            <lib>${java.home}/jmods/java.base.jmod</lib>
          </libs>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.guardsquare</groupId>
            <artifactId>proguard-core</artifactId>
            <version>${proguardVersion}</version>
            <scope>runtime</scope>
          </dependency>
          <dependency>
            <groupId>com.guardsquare</groupId>
            <artifactId>proguard-base</artifactId>
            <version>${proguardVersion}</version>
            <scope>runtime</scope>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>post-obfuscate-class</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/test-classes/com/google/gson/functional</outputDirectory>
              <resources>
                <resource>
                  <directory>${project.build.directory}/test-classes-obfuscated-outjar/com/google/gson/functional</directory>
                  <includes>
                    <include>EnumWithObfuscatedTest.class</include>
                    <include>EnumWithObfuscatedTest$Gender.class</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
