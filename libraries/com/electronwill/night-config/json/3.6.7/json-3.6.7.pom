<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.electronwill.night-config</groupId>
  <artifactId>json</artifactId>
  <version>3.6.7</version>
  <name>NightConfig json</name>
  <description>Powerful, easy-to-use and multi-language configuration library for the JVM - json module</description>
  <url>https://github.com/TheElectronWill/Night-Config</url>
  <licenses>
    <license>
      <name>GNU Lesser General Public License v3.0</name>
      <url>https://www.gnu.org/licenses/lgpl-3.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>TheElectronWill</id>
      <url>https://github.com/TheElectronWill</url>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/TheElectronWill/Night-Config.git</connection>
    <developerConnection>scm:git:git://github.com/TheElectronWill/Night-Config.git</developerConnection>
    <url>'https://github.com/TheElectronWill/Night-Config/tree/master'</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.electronwill.night-config</groupId>
      <artifactId>core</artifactId>
      <version>3.6.7</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
