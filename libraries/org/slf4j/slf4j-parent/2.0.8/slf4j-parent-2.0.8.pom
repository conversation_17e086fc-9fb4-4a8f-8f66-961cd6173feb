<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-bom</artifactId>
    <version>2.0.8</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>slf4j-parent</artifactId>
  <packaging>pom</packaging>
  <name>SLF4J Parent POM</name>
  <description>SLF4J project parent pom.xml file</description>


  <organization>
    <name>QOS.ch</name>
    <url>http://www.qos.ch</url>
  </organization>
  <inceptionYear>2005</inceptionYear>



  <properties>
    <!-- yyyy-MM-dd'T'HH:mm:ss'Z' -->
    <project.build.outputTimestamp>2023-08-29T21:48:00Z</project.build.outputTimestamp>
    <latest.1.version>1.7.36</latest.1.version>
    <!-- java.util.ServiceLoader requires Java 6 -->
    <jdk.version>8</jdk.version>
    <maven.compiler.source>${jdk.version}</maven.compiler.source>
    <maven.compiler.target>${jdk.version}</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <!-- used in integration testing -->
    <cal10n.version>0.8.1</cal10n.version>
    <reload4j.version>1.2.22</reload4j.version>    
    <logback.version>1.2.10</logback.version>
    <jcl.version>1.2</jcl.version>
    <junit.version>4.13.1</junit.version>
    <maven-site-plugin.version>3.7.1</maven-site-plugin.version>
    <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
    <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
    <maven-javadoc-plugin.version>3.5.0</maven-javadoc-plugin.version>
    <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
    <maven-deploy-plugin.version>3.0.0-M1</maven-deploy-plugin.version>
    <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
    <maven-jxr-plugin.version>3.1.1</maven-jxr-plugin.version>
    <maven-bundle-plugin.version>5.1.8</maven-bundle-plugin.version>
  </properties>

 

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>ch.qos.reload4j</groupId>
        <artifactId>reload4j</artifactId>
        <version>${reload4j.version}</version>
      </dependency>
      
      <dependency>
        <groupId>ch.qos.cal10n</groupId>
        <artifactId>cal10n-api</artifactId>
        <version>${cal10n.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh</artifactId>
        <version>2.10</version>
      </extension>
    </extensions>

    <resources>
      <resource>
        <directory>${project.basedir}/src/main/resources</directory>
        <filtering>true</filtering>
      </resource>

      <resource>
        <directory>..</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>LICENSE.txt</include>
        </includes>
      </resource>
    </resources>

    <pluginManagement>
      <plugins>

        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.14</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java16</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>

      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <executions>

          <execution>
            <id>default-compile</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>${jdk.version}</source>
              <target>${jdk.version}</target>
            </configuration>
          </execution> 

          <execution>                      
            <id>module-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>          
            <configuration>
              <release>9</release>
              <compileSourceRoots>
                <compileSourceRoot>${project.basedir}/src/main/java9</compileSourceRoot>
              </compileSourceRoots>
              <multiReleaseOutput>true</multiReleaseOutput>
            </configuration>
          </execution>



        </executions>

      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
        <executions>
          <!--  Repeated in slf4j-api/pom.xml -->
          <execution>
            <id>default-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              </archive>
              <skipIfEmpty>true</skipIfEmpty>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>${maven-bundle-plugin.version}</version>
        <configuration>
          <supportIncrementalBuild>true</supportIncrementalBuild>
          <!-- populated by the plugin itself -->
          <instructions>
			  <Bundle-SymbolicName>${replacestring;${project.artifactId};-;.}</Bundle-SymbolicName>
			  <Bundle-Vendor>SLF4J.ORG</Bundle-Vendor>
			  <_snapshot/>
			  <_exportcontents>!META-INF.versions.9,*;-noimport:=true</_exportcontents>
			  <Bundle-Description>${project.description}</Bundle-Description>
			  <Bundle-DocURL>${project.url}</Bundle-DocURL>
			  <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
			  <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
			  <Implementation-Version>${project.version}</Implementation-Version>
			  <Implementation-Title>${project.artifactId}</Implementation-Title>
			  <Multi-Release>true</Multi-Release>
			  <_removeheaders>Private-Package,Bundle-SCM, Bundle-Developers, Include-Resource</_removeheaders>
		  </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
        <configuration>
          <forkCount>1</forkCount>
          <reuseForks>false</reuseForks>
          <reportFormat>plain</reportFormat>
          <trimStackTrace>false</trimStackTrace>
          <excludes>
            <exclude>**/AllTest.java</exclude>
            <exclude>**/PackageTest.java</exclude>
          </excludes>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven-source-plugin.version}</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>${maven-site-plugin.version}</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.0.0</version>
      </plugin>
    </plugins>
  
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${maven-jxr-plugin.version}</version>
        <configuration>
          <aggregate>true</aggregate>
          <javadocDir>target/site/apidocs/</javadocDir>
          <linkJavadoc>true</linkJavadoc>
        </configuration>
      </plugin>
      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin.version}</version>
        <configuration>
          <linkJavadoc>true</linkJavadoc>
          <linksource>true</linksource>
          <aggregate>true</aggregate>
          <additionalOptions>
            <additionalOption>-Xdoclint:none</additionalOption>
          </additionalOptions>
          <excludePackageNames>org.slf4j.migrator:org.slf4j.migrator.*</excludePackageNames>
          <sourceFileExcludes>
            <sourceFileExclude>**/module-info.java</sourceFileExclude>
          </sourceFileExcludes>
          
          <groups>
            <group>
              <title>SLF4J packages</title>
              <packages>org.slf4j:org.slf4j.*</packages>
            </group>
            
            <group>
              <title>SLF4J extensions</title>
              <packages>
                org.slf4j.cal10n:org.slf4j.profiler:org.slf4j.ext:org.slf4j.instrumentation:org.slf4j.agent
              </packages>
            </group>
            
            <group>
              <title>Jakarta Commons Logging packages</title>
              <packages>org.apache.commons.*</packages>
            </group>
            
            <group>
              <title>java.util.logging (JUL) to SLF4J bridge</title>
              <packages>org.slf4j.bridge</packages>
            </group>
            
            <group>
              <title>Apache log4j</title>
              <packages>org.apache.log4j:org.apache.log4j.*</packages>
            </group>
          </groups>
        </configuration>
      </plugin>
      
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>skipTests</id>
      <properties>
        <maven.test.skip>true</maven.test.skip>
      </properties>
    </profile>

    <profile>
      <id>javadocjar</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <additionalparam>-Xdoclint:none</additionalparam>
              <failOnError>false</failOnError>
              <sourceFileExcludes>
                <sourceFileExclude>**/module-info.java</sourceFileExclude>
              </sourceFileExcludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>license</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.google.code.maven-license-plugin</groupId>
            <artifactId>maven-license-plugin</artifactId>
            <configuration>
              <header>src/main/licenseHeader.txt</header>
              <quiet>false</quiet>
              <failIfMissing>true</failIfMissing>
              <aggregate>true</aggregate>
              <includes>
                <include>src/**/*.java</include>
              </includes>
              <useDefaultExcludes>true</useDefaultExcludes>
              <useDefaultMapping>true</useDefaultMapping>
              <properties>
                <year>1999</year>
              </properties>
              <headerDefinitions>
                <headerDefinition>src/main/javadocHeaders.xml</headerDefinition>
              </headerDefinitions>
            </configuration>
          </plugin>
        </plugins>
      </build>

      <pluginRepositories>
      </pluginRepositories>
    </profile>


    <profile>
      <id>generate-osgi-service-loader-mediator-entries</id>
      <activation>
        <file>
          <exists>src/main/resources/META-INF/services/org.slf4j.spi.SLF4JServiceProvider</exists>
        </file>
      </activation>
      <build>
        <!-- Add the OSGi Service Loader Mediator Specification Manifest entries for each module that has a SLF4JServiceProvider Service-Loader file.-->
        <plugins>
          <plugin>
            <groupId>org.apache.felix</groupId>
            <artifactId>maven-bundle-plugin</artifactId>
            <configuration>
              <instructions>
                <Provide-Capability><![CDATA[
                  osgi.service;objectClass:List<String>="org.slf4j.spi.SLF4JServiceProvider";type=${slf4j.provider.type};effective:=active,
                  osgi.serviceloader;osgi.serviceloader="org.slf4j.spi.SLF4JServiceProvider";register:="${slf4j.provider.implementation}";type=${slf4j.provider.type}
                ]]></Provide-Capability>
                <Require-Capability><![CDATA[
                  osgi.extender;filter:="(&(osgi.extender=osgi.serviceloader.registrar)(version>=1.0.0)(!(version>=2.0.0)))"
                ]]></Require-Capability>
              </instructions>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
    <profile>
      <id>travis</id>
      <activation>
        <property>
          <name>env.TRAVIS</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-toolchains-plugin</artifactId>
            <version>1.1</version>
            <executions>
              <execution>
                <id>toolchain</id>
                <phase>none</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    -->
  </profiles>


</project>
