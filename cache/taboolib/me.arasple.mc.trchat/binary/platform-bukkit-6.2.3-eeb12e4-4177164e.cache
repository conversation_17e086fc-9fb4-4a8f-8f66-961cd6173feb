          ;me.arasple.mc.trchat.taboolib.platform.BukkitWorldGenerator      ;me.arasple.mc.trchat.taboolib.platform.BukkitWorldGenerator   BukkitWorldGenerator                  java.lang.Object   Object                           getDefaultWorldGenerator      ;me.arasple.mc.trchat.taboolib.platform.BukkitWorldGenerator   BukkitWorldGenerator         K(Ljava/lang/String;Ljava/lang/String;)Lorg/bukkit/generator/ChunkGenerator;                    "org.jetbrains.annotations.Nullable   Nullable                      "org.jetbrains.annotations.Nullable   Nullable                   java.lang.String   String                java.lang.String   String                  "org.jetbrains.annotations.Nullable   Nullable                #org.bukkit.generator.ChunkGenerator   ChunkGenerator             3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         !          !org.bukkit.plugin.java.JavaPlugin   
JavaPlugin                      :me.arasple.mc.trchat.taboolib.common.platform.PlatformSide   PlatformSide            value               6me.arasple.mc.trchat.taboolib.common.platform.Platform   Platform         BUKKIT         pluginInstance      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         6Lme/arasple/mc/trchat/taboolib/common/platform/Plugin;   
            "org.jetbrains.annotations.Nullable   Nullable                4me.arasple.mc.trchat.taboolib.common.platform.Plugin   Plugin            instance      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         5Lme/arasple/mc/trchat/taboolib/platform/BukkitPlugin;   
          3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin               onLoad      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         ()V                     V   V           onEnable      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         ()V                     V   V           	onDisable      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         ()V                     V   V           getDefaultWorldGenerator      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         K(Ljava/lang/String;Ljava/lang/String;)Lorg/bukkit/generator/ChunkGenerator;                      !org.jetbrains.annotations.NotNull   NotNull                         "org.jetbrains.annotations.Nullable   Nullable                       java.lang.String   String                  !org.jetbrains.annotations.NotNull   NotNull                java.lang.String   String                  "org.jetbrains.annotations.Nullable   Nullable                #org.bukkit.generator.ChunkGenerator   ChunkGenerator            getDefaultBiomeProvider      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         J(Ljava/lang/String;Ljava/lang/String;)Lorg/bukkit/generator/BiomeProvider;                      !org.jetbrains.annotations.NotNull   NotNull                         "org.jetbrains.annotations.Nullable   Nullable                      "org.jetbrains.annotations.Nullable   Nullable                   java.lang.String   String                  !org.jetbrains.annotations.NotNull   NotNull                java.lang.String   String                  "org.jetbrains.annotations.Nullable   Nullable                "org.bukkit.generator.BiomeProvider   
BiomeProvider            getFile      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         ()Ljava/io/File;                   !org.jetbrains.annotations.NotNull   NotNull                    java.io.File   File            getPluginInstance      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         8()Lme/arasple/mc/trchat/taboolib/common/platform/Plugin;   	                "org.jetbrains.annotations.Nullable   Nullable                    4me.arasple.mc.trchat.taboolib.common.platform.Plugin   Plugin            getInstance      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         7()Lme/arasple/mc/trchat/taboolib/platform/BukkitPlugin;   	                !org.jetbrains.annotations.NotNull   NotNull                    3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin            invokeActive      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         ()V                     V   V           lambda$onEnable$1      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         =(Lio/papermc/paper/threadedregions/scheduler/ScheduledTask;)V                   8io.papermc.paper.threadedregions.scheduler.ScheduledTask   
ScheduledTask                V   V           lambda$static$0      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         ()V  
                  V   V              <init>      3me.arasple.mc.trchat.taboolib.platform.BukkitPlugin   BukkitPlugin         ()V                  :me.arasple.mc.trchat.taboolib.platform.BukkitBiomeProvider      :me.arasple.mc.trchat.taboolib.platform.BukkitBiomeProvider   BukkitBiomeProvider                  java.lang.Object   Object                           getDefaultBiomeProvider      :me.arasple.mc.trchat.taboolib.platform.BukkitBiomeProvider   BukkitBiomeProvider         J(Ljava/lang/String;Ljava/lang/String;)Lorg/bukkit/generator/BiomeProvider;                    "org.jetbrains.annotations.Nullable   Nullable                      "org.jetbrains.annotations.Nullable   Nullable                   java.lang.String   String                java.lang.String   String                  "org.jetbrains.annotations.Nullable   Nullable                "org.bukkit.generator.BiomeProvider   
BiomeProvider             4me.arasple.mc.trchat.taboolib.platform.FoliaExecutor      4me.arasple.mc.trchat.taboolib.platform.FoliaExecutor   
FoliaExecutor         !          java.lang.Object   Object                       ASYNC_SCHEDULER      4me.arasple.mc.trchat.taboolib.platform.FoliaExecutor   
FoliaExecutor         ;Lio/papermc/paper/threadedregions/scheduler/AsyncScheduler;   	          9io.papermc.paper.threadedregions.scheduler.AsyncScheduler   AsyncScheduler            REGION_SCHEDULER      4me.arasple.mc.trchat.taboolib.platform.FoliaExecutor   
FoliaExecutor         <Lio/papermc/paper/threadedregions/scheduler/RegionScheduler;   	          :io.papermc.paper.threadedregions.scheduler.RegionScheduler   RegionScheduler            GLOBAL_REGION_SCHEDULER      4me.arasple.mc.trchat.taboolib.platform.FoliaExecutor   
FoliaExecutor         BLio/papermc/paper/threadedregions/scheduler/GlobalRegionScheduler;   	          @io.papermc.paper.threadedregions.scheduler.GlobalRegionScheduler   GlobalRegionScheduler                   <init>      4me.arasple.mc.trchat.taboolib.platform.FoliaExecutor   
FoliaExecutor         ()V                  ,me.arasple.mc.trchat.taboolib.platform.Folia      ,me.arasple.mc.trchat.taboolib.platform.Folia   Folia         !          java.lang.Object   Object                       isFolia      ,me.arasple.mc.trchat.taboolib.platform.Folia   Folia         Z   	          Z   Z                  <init>      ,me.arasple.mc.trchat.taboolib.platform.Folia   Folia         ()V                  4me.arasple.mc.trchat.taboolib.platform.IllegalAccess      4me.arasple.mc.trchat.taboolib.platform.IllegalAccess   
IllegalAccess         !          java.lang.Object   Object                           inject      4me.arasple.mc.trchat.taboolib.platform.IllegalAccess   
IllegalAccess         ()V   	                  V   V           lambda$inject$0      4me.arasple.mc.trchat.taboolib.platform.IllegalAccess   
IllegalAccess         ()V  
                  V   V              <init>      4me.arasple.mc.trchat.taboolib.platform.IllegalAccess   
IllegalAccess         ()V               