          Mme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngineFactory$2      Mme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngineFactory$2   'NashornCompilerKt$scriptEngineFactory$2         0          kotlin2120.jvm.internal.Lambda   Lambda               "kotlin2120.jvm.functions.Function0   	Function0                  kotlin2120.Metadata   Metadata            xi      0   mv   !                k         d1             
 

  0H
¢   d2            <anonymous>      "Ljavax/script/ScriptEngineFactory;      invoke         INSTANCE      Mme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngineFactory$2   'NashornCompilerKt$scriptEngineFactory$2         OLme/arasple/mc/trchat/taboolib/common5/NashornCompilerKt$scriptEngineFactory$2;             Mme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngineFactory$2   'NashornCompilerKt$scriptEngineFactory$2               invoke      Mme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngineFactory$2   'NashornCompilerKt$scriptEngineFactory$2         $()Ljavax/script/ScriptEngineFactory;                   !org.jetbrains.annotations.NotNull   NotNull                     javax.script.ScriptEngineFactory   ScriptEngineFactory            invoke      Mme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngineFactory$2   'NashornCompilerKt$scriptEngineFactory$2         ()Ljava/lang/Object;  A                  java.lang.Object   Object               <init>      Mme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngineFactory$2   'NashornCompilerKt$scriptEngineFactory$2         ()V                   7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt      7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt   NashornCompilerKt         1          java.lang.Object   Object                      +me.arasple.mc.trchat.taboolib.common.Inject   Inject                   <me.arasple.mc.trchat.taboolib.common.env.RuntimeDependencies   RuntimeDependencies            value                  test      5!jdk.nashorn.api.scripting.NashornScriptEngineFactory      value      &!org.openjdk.nashorn:nashorn-core:15.4         kotlin2120.Metadata   Metadata            xi      0   mv   !                k         d1            i 
 






 0*0
" 08FX¢
"08FX¢

   d2            scriptEngine      Ljavax/script/ScriptEngine;      getScriptEngine      ()Ljavax/script/ScriptEngine;      scriptEngine$delegate      Lkotlin2120/Lazy;      scriptEngineFactory      "Ljavax/script/ScriptEngineFactory;      getScriptEngineFactory      $()Ljavax/script/ScriptEngineFactory;      scriptEngineFactory$delegate      	compileJS      Ljavax/script/CompiledScript;             script-javascript         scriptEngineFactory$delegate      7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt   NashornCompilerKt         Lkotlin2120/Lazy;               !org.jetbrains.annotations.NotNull   NotNull                kotlin2120.Lazy   Lazy            scriptEngine$delegate      7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt   NashornCompilerKt         Lkotlin2120/Lazy;               !org.jetbrains.annotations.NotNull   NotNull                kotlin2120.Lazy   Lazy               getScriptEngineFactory      7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt   NashornCompilerKt         $()Ljavax/script/ScriptEngineFactory;                   !org.jetbrains.annotations.NotNull   NotNull                     javax.script.ScriptEngineFactory   ScriptEngineFactory            getScriptEngine      7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt   NashornCompilerKt         ()Ljavax/script/ScriptEngine;                   !org.jetbrains.annotations.NotNull   NotNull                    javax.script.ScriptEngine   ScriptEngine            	compileJS      7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt   NashornCompilerKt         1(Ljava/lang/String;)Ljavax/script/CompiledScript;                      !org.jetbrains.annotations.NotNull   NotNull                      "org.jetbrains.annotations.Nullable   Nullable                   java.lang.String   String                  !org.jetbrains.annotations.NotNull   NotNull                javax.script.CompiledScript   CompiledScript             Fme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngine$2      Fme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngine$2    NashornCompilerKt$scriptEngine$2         0          kotlin2120.jvm.internal.Lambda   Lambda               "kotlin2120.jvm.functions.Function0   	Function0                  kotlin2120.Metadata   Metadata            xi      0   mv   !                k         d1            ! 

 

 
 *00H
¢   d2            <anonymous>      Ljavax/script/ScriptEngine;      kotlin2120.jvm.PlatformType      invoke         INSTANCE      Fme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngine$2    NashornCompilerKt$scriptEngine$2         HLme/arasple/mc/trchat/taboolib/common5/NashornCompilerKt$scriptEngine$2;             Fme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngine$2    NashornCompilerKt$scriptEngine$2               invoke      Fme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngine$2    NashornCompilerKt$scriptEngine$2         ()Ljavax/script/ScriptEngine;                     javax.script.ScriptEngine   ScriptEngine            invoke      Fme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngine$2    NashornCompilerKt$scriptEngine$2         ()Ljava/lang/Object;  A                  java.lang.Object   Object               <init>      Fme.arasple.mc.trchat.taboolib.common5.NashornCompilerKt$scriptEngine$2    NashornCompilerKt$scriptEngine$2         ()V                