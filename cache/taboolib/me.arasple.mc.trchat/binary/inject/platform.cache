      -me.arasple.mc.trchat.taboolib.common5.Common5   6me.arasple.mc.trchat.taboolib.module.database.Database   4me.arasple.mc.trchat.taboolib.module.chat.Components   7me.arasple.mc.trchat.taboolib.common5.NashornCompilerKt   2me.arasple.mc.trchat.taboolib.module.kether.Kether   3me.arasple.mc.trchat.taboolib.expansion.AlkaidRedis   ?me.arasple.mc.trchat.taboolib.module.configuration.ConfigLoader   1me.arasple.mc.trchat.module.internal.TrChatBukkit      Kme.arasple.mc.trchat.taboolib.common.platform.command.SimpleCommandRegister       8me.arasple.mc.trchat.taboolib.module.nms.ProtocolHandler        ;me.arasple.mc.trchat.taboolib.module.kether.StandardChannel        Vme.arasple.mc.trchat.taboolib.platform.compat.PlaceholderExpansion$PlaceholderRegister       Bme.arasple.mc.trchat.taboolib.common.platform.ClassVisitorSchedule       <me.arasple.mc.trchat.taboolib.platform.bukkit.ParallelSystem       4me.arasple.mc.trchat.taboolib.platform.BukkitAdapter       Eme.arasple.mc.trchat.taboolib.common.platform.service.PlatformAdapter   8me.arasple.mc.trchat.taboolib.module.kether.KetherLoader       <me.arasple.mc.trchat.taboolib.common.platform.event.EventBus       :me.arasple.mc.trchat.taboolib.platform.BukkitOpenContainer       Kme.arasple.mc.trchat.taboolib.common.platform.service.PlatformOpenContainer   5me.arasple.mc.trchat.taboolib.platform.BukkitListener       Fme.arasple.mc.trchat.taboolib.common.platform.service.PlatformListener   Cme.arasple.mc.trchat.taboolib.module.configuration.ConfigNodeLoader       4me.arasple.mc.trchat.taboolib.platform.BukkitCommand       Eme.arasple.mc.trchat.taboolib.common.platform.service.PlatformCommand   <me.arasple.mc.trchat.taboolib.module.ui.RawTitleOpenListener        5me.arasple.mc.trchat.taboolib.platform.BukkitExecutor       Fme.arasple.mc.trchat.taboolib.common.platform.service.PlatformExecutor   /me.arasple.mc.trchat.taboolib.platform.BukkitIO       @me.arasple.mc.trchat.taboolib.common.platform.service.PlatformIO   2me.arasple.mc.trchat.taboolib.module.lang.Language        ?me.arasple.mc.trchat.taboolib.module.configuration.ConfigLoader    