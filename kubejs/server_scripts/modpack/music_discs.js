// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
//
// Made by Mitchell52 for AllTheMods

ServerEvents.recipes(allthemods => {
    //Vanilla
    allthemods.shaped('minecraft:music_disc_otherside',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'mysticalagriculture:creeper_essence',
        S: 'mysticalagriculture:skeleton_essence',
        D: 'minecraft:blue_dye'
    }).id('allthemods:minecraft/music_disk_otherside')
  
    //Ars Nouveau
    allthemods.shaped('ars_nouveau:music_disc_aria_biblio',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'ars_nouveau:mendosteen_pod',
        S: 'ars_nouveau:bombegranate_pod',
        D: 'ars_nouveau:source_gem'
    }).id('allthemods:ars_nouveau/music_disc_aria_biblio') 

    //botania
    allthemods.shaped('botania:record_gaia_1',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'botania:manasteel_nugget',
        S: 'mysticalagriculture:mystical_flower_essence',
        D: 'botania:gray_petal'
    }).id('allthemods:botania/record_gaia_1')
    allthemods.shaped('botania:record_gaia_2',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'botania:manasteel_nugget',
        S: 'mysticalagriculture:mystical_flower_essence',
        D: 'botania:black_petal'
    }).id('allthemods:botania/record_gaia_2')

    //Blue skies
    allthemods.shaped('blue_skies:blinding_rage',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'blue_skies:aquite',
        S: 'minecraft:gray_dye',
        D: 'minecraft:blue_dye'
    }).id('allthemods:blue_skies/blinding_rage')
    allthemods.shaped('blue_skies:defying_starlight',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'blue_skies:aquite',
        S: 'minecraft:green_dye',
        D: 'minecraft:yellow_dye'
    }).id('allthemods:blue_skies/defying_starlight')
    allthemods.shaped('blue_skies:venomous_encounter',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'blue_skies:aquite',
        S: 'minecraft:magenta_dye',
        D: 'minecraft:purple_dye'
    }).id('allthemods:blue_skies/venomous_encounter')
    allthemods.shaped('blue_skies:population',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'blue_skies:aquite',
        S: 'minecraft:cyan_dye',
        D: 'minecraft:magenta_dye'
    }).id('allthemods:blue_skies/population')

    //Twilight
    allthemods.shaped('twilightforest:music_disc_radiance',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:yellow_dye'
    }).id('allthemods:twilightforest/music_disc_radiance')
    allthemods.shaped('twilightforest:music_disc_steps',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:cyan_dye'
    }).id('allthemods:twilightforest/music_disc_steps')
    allthemods.shaped('twilightforest:music_disc_superstitious',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:purple_dye'
    }).id('allthemods:twilightforest/music_disc_superstitious')
    allthemods.shaped('twilightforest:music_disc_home',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:pink_dye'
    }).id('allthemods:twilightforest/music_disc_home')
    allthemods.shaped('twilightforest:music_disc_wayfarer',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:blue_dye'
    }).id('allthemods:twilightforest/music_disc_wayfarer')
    allthemods.shaped('twilightforest:music_disc_findings',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:orange_dye'
    }).id('allthemods:twilightforest/music_disc_findings')
    allthemods.shaped('twilightforest:music_disc_maker',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:magenta_dye'
    }).id('allthemods:twilightforest/music_disc_maker')
    allthemods.shaped('twilightforest:music_disc_thread',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:red_dye'
    }).id('allthemods:twilightforest/music_disc_thread')
    allthemods.shaped('twilightforest:music_disc_motion',['MC','SD'],{
        M: 'mysticalagriculture:blank_record',
        C: 'twilightforest:raven_feather',
        S: 'twilightforest:torchberries',
        D: 'minecraft:light_blue_dye'
    }).id('allthemods:twilightforest/music_disc_motion')

})

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
