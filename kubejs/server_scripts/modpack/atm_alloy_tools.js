// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

ServerEvents.recipes(allthemods => {
	// Sword, All recipes are Top-middle and go clockwise
	allthemods.custom({
	  "type": "ars_nouveau:enchanting_apparatus",
	  "keepNbtOfReagent": true,
	  "output": Item.of("allthemodium:alloy_sword").toJson(),
	  "pedestalItems": [
		{"item": Ingredient.of('#forge:ingots/unobtainium').to<PERSON>son()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').to<PERSON>son()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').to<PERSON><PERSON>()},
		{"item": Ingredient.of('#forge:ingots/allthemodium').to<PERSON>son()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium').toJson()},	  
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()}
	  ],
	  "reagent": [Item.of("allthemodium:allthemodium_sword").toJson()],
	  "sourceCost": 5000
	}).id('allthemods:allthemodium/alloy_sword')
	
	// Pickaxe
	allthemods.custom({
	  "type": "ars_nouveau:enchanting_apparatus",
	  "keepNbtOfReagent": true,
	  "output": Item.of("allthemodium:alloy_pick").toJson(),
	  "pedestalItems": [
		{"item": Ingredient.of('#forge:ingots/unobtainium').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/allthemodium').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium').toJson()},	  
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()}
	  ],
	  "reagent": [Item.of("allthemodium:allthemodium_pickaxe").toJson()],
	  "sourceCost": 5000
	}).id('allthemods:allthemodium/alloy_pick')
	
	// Shovel
	allthemods.custom({
	  "type": "ars_nouveau:enchanting_apparatus",
	  "keepNbtOfReagent": true,
	  "output": Item.of("allthemodium:alloy_shovel").toJson(),
	  "pedestalItems": [
		{"item": Ingredient.of('#forge:ingots/unobtainium').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/allthemodium').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium').toJson()},	  
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()}
	  ],
	  "reagent": [Item.of("allthemodium:allthemodium_shovel").toJson()],
	  "sourceCost": 5000
	}).id('allthemods:allthemodium/alloy_shovel')
   
	// Axe
	allthemods.custom({
	  "type": "ars_nouveau:enchanting_apparatus",
	  "keepNbtOfReagent": true,
	  "output": Item.of("allthemodium:alloy_axe").toJson(),
	  "pedestalItems": [
		{"item": Ingredient.of('#forge:ingots/unobtainium').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/allthemodium').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium_allthemodium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/vibranium').toJson()},	  
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()},
		{"item": Ingredient.of('#forge:ingots/unobtainium_vibranium_alloy').toJson()}
	  ],
	  "reagent": [Item.of("allthemodium:allthemodium_axe").toJson()],
	  "sourceCost": 5000
	  }).id('allthemods:allthemodium/alloy_axe')
  
  
	  // Paxel
	allthemods.custom({
	  "type": "ars_nouveau:enchanting_apparatus",
	  "keepNbtOfReagent": true,
	  "output": Item.of("allthemodium:alloy_paxel").toJson(),
	  "pedestalItems": [
		{"item": Ingredient.of('allthemodium:alloy_sword').toJson()},
		{"item": Ingredient.of('allthemodium:alloy_pick').toJson()},
		{"item": Ingredient.of('allthemodium:alloy_shovel').toJson()},
		{"item": Ingredient.of('allthemodium:alloy_axe').toJson()},
	  ],
	  "reagent": [Item.of("mysticalagradditions:awakened_supremium_paxel").toJson()],
	  "sourceCost": 10000
	  }).id('allthemods:allthemodium/alloy_paxel')
   
	
  })
  
  // This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
  // As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.