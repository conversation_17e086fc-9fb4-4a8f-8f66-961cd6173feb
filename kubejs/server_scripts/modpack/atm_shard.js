// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

ServerEvents.recipes(allthemods => {
  let shard = ('allthetweaks:atm_star_shard')

//#SilentGear
  allthemods.shapeless('32x allthetweaks:allthecatalystium', shard)
//#Pipez
//  allthemods.shaped('16x pipez:infinity_upgrade', ['ABA', 'BCB', 'ADA'], {
//   A: 'allthemodium:unobtainium_ingot',
//    B: 'minecraft:redstone_block',
//    C: 'pipez:ultimate_upgrade',
//    D: shard
//  })

})

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
