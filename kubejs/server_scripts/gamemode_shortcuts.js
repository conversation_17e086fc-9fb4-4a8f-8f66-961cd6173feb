// 游戏模式简写指令
// 功能：gm a冒险、gm s生存、gm c创造、gm sp旁观者
// 默认作用于自己，若后面有玩家id则作用在玩家
// 包含tab补全功能

ServerEvents.commandRegistry(allthemods => {
    const { commands: Commands, arguments: Arguments } = allthemods

    console.log('[游戏模式简写] 正在注册 /gm 命令...')
    
    // 游戏模式映射
    const gameModes = {
        'a': 'adventure',      // 冒险模式
        's': 'survival',       // 生存模式
        'c': 'creative',       // 创造模式
        'sp': 'spectator'      // 旁观者模式
    }
    
    // 游戏模式中文名称映射
    const gameModeNames = {
        'adventure': '冒险模式',
        'survival': '生存模式',
        'creative': '创造模式',
        'spectator': '旁观者模式'
    }
    
    // 注册主命令 /gm
    allthemods.register(Commands.literal('gm')
        .requires(s => s.hasPermission(2)) // 需要管理员权限
        .then(Commands.argument('mode', Arguments.STRING.create(allthemods))
            .suggests((_, builder) => {
                // 添加tab补全建议
                builder.suggest('a')
                builder.suggest('s')
                builder.suggest('c')
                builder.suggest('sp')
                return builder.buildFuture()
            })
            .executes(c => {
                // 只有模式参数，作用于命令执行者
                const mode = Arguments.STRING.getResult(c, 'mode')
                const player = c.source.player
                
                if (!player) {
                    c.source.sendFailure('此命令只能由玩家执行')
                    return 0
                }
                
                return changeGameMode(c.source, player, mode)
            })
            .then(Commands.argument('target', Arguments.PLAYER.create(allthemods))
                .executes(c => {
                    // 有目标玩家参数，作用于指定玩家
                    const mode = Arguments.STRING.getResult(c, 'mode')
                    const target = Arguments.PLAYER.getResult(c, 'target')
                    
                    return changeGameMode(c.source, target, mode)
                })
            )
        )
    )
    
    // 游戏模式切换函数
    function changeGameMode(source, player, modeShortcut) {
        console.log(`[游戏模式简写] 玩家 ${player.name} 尝试切换到模式: ${modeShortcut}`)

        // 检查模式简写是否有效
        if (!gameModes[modeShortcut]) {
            console.log(`[游戏模式简写] 无效的模式简写: ${modeShortcut}`)
            source.sendFailure(`无效的游戏模式简写: ${modeShortcut}`)
            source.sendFailure('可用的简写: a(冒险), s(生存), c(创造), sp(旁观者)')
            return 0
        }
        
        const gameMode = gameModes[modeShortcut]
        const modeName = gameModeNames[gameMode]
        
        try {
            // 执行原版gamemode命令
            const gameModeCommand = `gamemode ${gameMode} ${player.name}`
            source.server.runCommandSilent(gameModeCommand)
            
            // 发送成功消息
            if (source.player && source.player.name === player.name) {
                // 自己切换模式
                source.sendSuccess(`已将你的游戏模式设置为 ${modeName}`, false)
            } else {
                // 为其他玩家切换模式
                source.sendSuccess(`已将 ${player.name} 的游戏模式设置为 ${modeName}`, true)
                player.displayClientMessage(`你的游戏模式已被设置为 ${modeName}`, false)
            }
            
            return 1
        } catch (error) {
            source.sendFailure(`切换游戏模式失败: ${error.message}`)
            return 0
        }
    }

    console.log('[游戏模式简写] /gm 命令注册完成')
})

// 添加帮助信息
ServerEvents.customCommand('gm_help', event => {
    const player = event.player
    player.tell('§6=== 游戏模式简写指令帮助 ===')
    player.tell('§e用法: /gm <模式> [玩家]')
    player.tell('§e模式简写:')
    player.tell('§a  a  - 冒险模式 (Adventure)')
    player.tell('§a  s  - 生存模式 (Survival)')
    player.tell('§a  c  - 创造模式 (Creative)')
    player.tell('§a  sp - 旁观者模式 (Spectator)')
    player.tell('§e示例:')
    player.tell('§a  /gm c        - 将自己设置为创造模式')
    player.tell('§a  /gm s Steve  - 将Steve设置为生存模式')
    player.tell('§7注意: 需要管理员权限才能使用此命令')
})
