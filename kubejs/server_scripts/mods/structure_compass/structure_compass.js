// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

ServerEvents.recipes(allthemods => {
 allthemods.remove({id: 'structurecompass:structure_compass'})
 allthemods.shaped('structurecompass:structure_compass', [
    'NAN',
    'ACA',
    'NAN'
  ], {
    A: 'allthemodium:allthemodium_ingot',
    N: 'minecraft:netherite_ingot',
    C: 'naturescompass:naturescompass',
  })
  })

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
