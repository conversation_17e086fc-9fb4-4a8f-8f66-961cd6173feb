// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

ServerEvents.recipes(allthemods => {
  allthemods.remove({ id: 'tiab:time_in_a_bottle' })
  allthemods.shaped('tiab:time_in_a_bottle', ['UUU', 'DCD', 'LBL'], {
    U: '#forge:ingots/unobtainium',
    D: 'mysticalagriculture:speed_iii_augment',
    L: 'minecraft:lapis_lazuli',
    C: 'productivebees:upgrade_time',
    B: 'minecraft:experience_bottle'
  })
})

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
