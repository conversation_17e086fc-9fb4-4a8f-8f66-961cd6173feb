// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
JEIEvents.information(allthemods => {
    allthemods.addItem('enderchests:ender_chest', [
      'Cross-dimensional wireless item transfer to any chest on the same channel.',
      'Use dye on the colored bars to set the channel.',
      'Sneak + right-click with a diamond to switch to private channel.',
      'Sneak + right-click with an ender pearl or eye of ender to increase inventory size.',
    ])

    allthemods.addItem('endertanks:ender_tank', [
      'Cross-dimensional wireless fluid transfer to any tank on the same channel.',
      'Use dye on the colored bars to set the channel.',
      'Sneak + right-click with a diamond to switch to private channel.',
      'Sneak + right-click with an ender pearl or eye of ender to increase tank size.',
    ])

    allthemods.addItem('mekanism:creative_energy_cube',[
      'Needs to be energized in a Powah energizing rod with a ATM star.',
      'Otherwise acts as an expensive energy trash can.',
    ])

    allthemods.addItem('pylons:infusion_pylon',[ 
      'Chunkloads a single chunk. Only one pylon per player will load at a time, and only while the player is online.',
    ])
    
  })

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
