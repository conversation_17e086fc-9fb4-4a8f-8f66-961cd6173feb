// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

const $RotorHolderPartMachine = Java.loadClass('com.gregtechceu.gtceu.common.machine.multiblock.part.RotorHolderPartMachine')
// 注释掉客户端渲染器类，因为在服务器端不可用
// const $RotorHolderMachineRenderer = Java.loadClass('com.gregtechceu.gtceu.client.renderer.machine.RotorHolderMachineRenderer')

StartupEvents.registry('item', allthemods => {
    // G*
    allthemods.create('star_housing').displayName('强化星体容器')
    allthemods.create('absolute_reaction_plating').displayName('绝对反应镀层')
    allthemods.create('star_compression_module').displayName('星体压缩模块')
    allthemods.create('superthermal_transference_coil').displayName('超热传导线圈')
    allthemods.create('cable_of_hyperconductivity').displayName('超导电缆')
    allthemods.create('greg_star_shard').displayName('格雷星碎片').glow(true)

    // Micro Universe Orb
    allthemods.create('micro_universe_catalyst')
        .displayName('微宇宙催化剂')
        .tooltip({ translate: 'item.kubejs.micro_universe_catalyst.tooltip', italic: true, color: 'red' })
    allthemods.create('micro_universe_drill_ship')
        .displayName('微宇宙钻探舰')
})

GTCEuStartupEvents.registry('gtceu:machine', allthemods => {
    allthemods.create('uhv_rotor_holder', 'custom')
        .tiers(GTValues.UHV)
        .definition((tier, builder) => {
            builder.rotationState(RotationState.ALL)
                .abilities(PartAbility.ROTOR_HOLDER)
                .renderer(() => new $RotorHolderMachineRenderer(tier))
        }).machine((holder) => { return new $RotorHolderPartMachine(holder, GTValues.UHV) })

    allthemods.create('uev_rotor_holder', 'custom')
        .tiers(GTValues.UEV)
        .definition((tier, builder) => {
            builder.rotationState(RotationState.ALL)
                .abilities(PartAbility.ROTOR_HOLDER)
                .renderer(() => new $RotorHolderMachineRenderer(tier))
        }).machine((holder) => { return new $RotorHolderPartMachine(holder, GTValues.UEV) })

    allthemods.create('uiv_rotor_holder', 'custom')
        .tiers(GTValues.UIV)
        .definition((tier, builder) => {
            builder.rotationState(RotationState.ALL)
                .abilities(PartAbility.ROTOR_HOLDER)
                .renderer(() => new $RotorHolderMachineRenderer(tier))
        }).machine((holder) => { return new $RotorHolderPartMachine(holder, GTValues.UIV) })
})

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.