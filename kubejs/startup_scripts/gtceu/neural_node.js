// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

GTCEuStartupEvents.registry('gtceu:recipe_type', allthemods => {
    allthemods.create('neural_node')
        .category('neural_node')
        .setEUIO('in')
        .setMaxIOSize(3, 19, 0, 0)
        .setProgressBar(GuiTextures.PROGRESS_BAR_ARROW, FillDirection.LEFT_TO_RIGHT)
        .setSound(GTSoundEntries.COMPUTATION)
})

GTCEuStartupEvents.registry('gtceu:machine', allthemods => {
    allthemods.create('neural_node', 'multiblock')
        .rotationState(RotationState.NON_Y_AXIS)
        .recipeType('neural_node')
        .appearanceBlock(GTBlocks.CASING_STEEL_SOLID)
        .recipeModifiers([GTRecipeModifiers.PARALLEL_HATCH, GTRecipeModifiers.OC_NON_PERFECT])
        .pattern(definition => FactoryBlockPattern.start()
            .aisle('AAAAAAAAAAAAAAAA', 'A              A', 'A              A', 'A              A', 'A              A', 'A              A', 'A              A', 'A              A', 'A              A', 'A              A', 'A              A', 'A  CCCC   CCC  A', 'A              A', 'A              A', 'A              A', 'AAAAAAAAAAAAAAAA')
            .aisle('A              A', ' BBBBCFFFFFFFFB ', ' BBBBBBBBBBBBBB ', ' BBBBBCFFFFFFFB ', ' BBBBBBBBBBBBBB ', ' BFFFFFFFFFFFFB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBCFFFFFF ', ' BBBBBBBBBBBBBB ', ' BFFFFFFBFFFFFB ', ' BBBBBBBBBBBBBB ', ' BB    BBB   BB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBB ', ' BD          DB ', ' F            B ', ' B            B ', ' F            F ', ' B            B ', ' F            B ', ' B            B ', ' F            F ', ' B            B ', ' B            B ', ' B            B ', ' BD          DB ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBB ', ' B            B ', ' F D        D B ', ' B            B ', ' F            F ', ' B            B ', ' F            B ', ' B            B ', ' F            F ', ' B            B ', 'C              C', ' B D        D B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBB ', ' B            B ', ' F            B ', ' B  D      D  B ', ' F            F ', ' B            B ', ' F            B ', ' B            B ', ' F            F ', ' B            B ', 'C   D      D   C', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBC ', ' B            B ', ' F            B ', ' B            B ', ' F   D    D   F ', ' B            B ', ' F            B ', ' B            B ', ' F            F ', ' B   D    D   B ', 'C              C', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBF ', ' B            B ', ' F            C ', ' B            B ', ' F            F ', ' B    GGGG    B ', ' F    GGGG    B ', ' B    GGGG    B ', ' F    GGGG    F ', ' B            B ', ' B             C', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBF ', ' B            B ', ' F            F ', ' B            B ', ' F            F ', ' B    GGGG    B ', ' F    GGGG    C ', ' B    GGGG    B ', ' B    GGGG    F ', ' B            B ', ' B            B ', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBF ', ' B            B ', ' F            F ', ' B            B ', ' F            F ', ' B    GGGG    B ', ' C    GGGG    F ', ' B    GGGG    B ', ' F    GGGG    B ', ' B            B ', ' B            B ', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' FBBBBBBBBBBBBF ', ' B            B ', ' C            F ', ' B            B ', ' F            F ', ' B    GGGG    B ', ' B    GGGG    F ', ' B    GGGG    B ', ' F    GGGG    F ', ' B            B ', 'C             B ', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' CBBBBBBBBBBBBF ', ' B            B ', ' B            F ', ' B            B ', ' F   D    D   F ', ' B            B ', ' B            F ', ' B            B ', ' F            F ', ' B   D    D   B ', 'C              C', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' BBBBBBBBBBBBBF ', ' B            B ', ' B            F ', ' B  D      D  B ', ' F            F ', ' B            B ', ' B            F ', ' B            B ', ' F            F ', ' B            B ', 'C   D      D   C', ' B            B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' BBBBBBBBBBBBBF ', ' B            B ', ' B D        D F ', ' B            B ', ' F            F ', ' B            B ', ' B            F ', ' B            B ', ' F            F ', ' B            B ', 'C              C', ' B D        D B ', ' B            B ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' BBBBBBBBBBBBBF ', ' BD          DB ', ' B            F ', ' B            B ', ' F            F ', ' B            B ', ' B            F ', ' B            B ', ' F            F ', ' B            B ', ' B            B ', ' B            B ', ' BD          DB ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('A              A', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBB     BACBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBEKEBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', ' BBBBBBBBBBBBBB ', 'A              A')
            .aisle('AAAAAAAAAAAAAAAA', 'A              A', 'A CCCCCCCCCCCC A', 'A C          C A', 'A C HHHHH    C A', 'A C          C A', 'A C          C A', 'A C          C A', 'A C          C A', 'A C          C A', 'A C          C A', 'A C          C A', 'A C          C A', 'A CCCCCCCCCCCC A', 'A              A', 'AAAAAAAAAAAAAAAA')
            .where('K', Predicates.controller(Predicates.blocks(definition.get())))
            .where('A', Predicates.blocks(GCYMBlocks.CASING_NONCONDUCTING.get()))
            .where('B', Predicates.blocks(GTBlocks.CASING_PTFE_INERT.get()))
            .where('C', Predicates.blocks(GCYMBlocks.CASING_VIBRATION_SAFE.get()))
            .where('D', Predicates.blocks("gtceu:polytetrafluoroethylene_frame"))
            .where('E', Predicates.blocks(GTBlocks.CASING_STAINLESS_CLEAN.get())
                .or(Predicates.abilities(PartAbility.INPUT_ENERGY).setExactLimit(1))
                .or(Predicates.abilities(PartAbility.PARALLEL_HATCH).setMaxGlobalLimited(1))
            )
            .where('F', Predicates.blocks(GCYMBlocks.CASING_WATERTIGHT.get()))
            .where('G', Predicates.blocks("hostilenetworks:sim_chamber"))
            .where('H', Predicates.blocks(GCYMBlocks.CASING_WATERTIGHT.get())
                .or(Predicates.abilities(PartAbility.IMPORT_ITEMS, PartAbility.EXPORT_ITEMS))
            )
            .where(' ', Predicates.any())
            .build()
        )
        .workableCasingRenderer('gtceu:block/casings/solid/machine_casing_clean_stainless_steel', 'gtceu:block/multiblock/fusion_reactor', false)
})

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.