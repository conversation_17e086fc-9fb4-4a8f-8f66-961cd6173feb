// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

const $EnergyHatchPartMachine = Java.loadClass('com.gregtechceu.gtceu.common.machine.multiblock.part.EnergyHatchPartMachine')
const $IO = Java.loadClass('com.gregtechceu.gtceu.api.capability.recipe.IO')

StartupEvents.registry('block', allthemods => {
    allthemods.create('micro_universe_energy_transmitter')
        .displayName('微宇宙能量发射器')
    allthemods.create('micro_universe_focus_lens')
        .displayName('微宇宙聚焦透镜')
})

GTCEuStartupEvents.registry('gtceu:recipe_type', allthemods => {
    allthemods.create('micro_universe_reactor')
        .category('gregstar')
        .setEUIO('out')
        .setMaxIOSize(12, 0, 6, 0)
        .setSlotOverlay(false, false, GuiTextures.SOLIDIFIER_OVERLAY)
        .setProgressBar(GuiTextures.PROGRESS_BAR_ARROW, FillDirection.LEFT_TO_RIGHT)
        .setSound(GTSoundEntries.ARC);
    allthemods.create('micro_universe_collector')
        .category('gregstar')
        .setEUIO('in')
        .setMaxIOSize(12, 12, 6, 6)
        .setSlotOverlay(false, false, GuiTextures.SOLIDIFIER_OVERLAY)
        .setProgressBar(GuiTextures.PROGRESS_BAR_ARROW, FillDirection.LEFT_TO_RIGHT)
        .setSound(GTSoundEntries.ARC);
})

GTCEuStartupEvents.registry('gtceu:machine', allthemods => {
    allthemods.create('micro_universe_orb', 'multiblock')
        .rotationState(RotationState.NON_Y_AXIS)
        .recipeTypes([GTRecipeTypes.get('micro_universe_collector'), GTRecipeTypes.get('micro_universe_reactor')])
        .recipeModifiers([GTRecipeModifiers.PARALLEL_HATCH, GTRecipeModifiers.OC_NON_PERFECT])
        .appearanceBlock(GCYMBlocks.CASING_ATOMIC)
        .pattern(definition => FactoryBlockPattern.start()
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CIC                         ", "                         III                         ", "                       CIFFFIC                       ", "                       CIFFFIC                       ", "                       CIFFFIC                       ", "                         III                         ", "                         CIC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                    CC   CTC   CC                    ", "                    CCC  CTC  CCC                    ", "                     CCC CTC CCC                     ", "                      C       C                      ", "                      C       C                      ", "                     CC  GGG  CC                     ", "                     CC  GGG  CC                     ", "                     CC  GGG  CC                     ", "                      C       C                      ", "                      C       C                      ", "                     CCC CTC CCC                     ", "                    CCC  CTC  CCC                    ", "                    CC   CTC   CC                    ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                   CC    CTC    CC                   ", "                   CCCC  CTC  CCCC                   ", "                    CCC  CTC  CCC                    ", "                    CCCC CTC CCCC                    ", "                      C       C                      ", "                      C GGGGG C                      ", "                     CC GGGGG CC                     ", "                     CC GGGGG CC                     ", "                     CC GGGGG CC                     ", "                      C GGGGG C                      ", "                      C       C                      ", "                    CCCC CTC CCCC                    ", "                    CCC  CTC  CCC                    ", "                   CCCC  CTC  CCCC                   ", "                   CC    CTC    CC                   ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                  CC     CTC     CC                  ", "                  CCC    CTC    CCC                  ", "                   CCC   CTC   CCC                   ", "                    CCC  CTC  CCC                    ", "                     CC  CTC  CC                     ", "                         GGG                         ", "                        GGGGG                        ", "                       GGGGGGG                       ", "                       GGGSGGG                       ", "                       GGGGGGG                       ", "                        GGGGG                        ", "                         GGG                         ", "                     CC  CTC  CC                     ", "                    CCC  CTC  CCC                    ", "                   CCC   CTC   CCC                   ", "                  CCC    CTC    CCC                  ", "                  CC     CTC     CC                  ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                CC       CTC       CC                ", "                CCC               CCC                ", "                 C                 C                 ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         GGG                         ", "                        GGGGG                        ", "                       GGGSGGG                       ", "                       GGSSSGG                       ", "                       GGGSGGG                       ", "                        GGGGG                        ", "                         GGG                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                 C                 C                 ", "                CCC               CCC                ", "                CC       CTC       CC                ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "               CC        CTC        CC               ", "               CC                   CC               ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         GGG                         ", "                        GGGGG                        ", "                    EEEGGGGGGGEEE                    ", "                    CCCGGGSGGGCCC                    ", "                    EEEGGGGGGGEEE                    ", "                        GGGGG                        ", "                         GGG                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "               CC                   CC               ", "               CC        CTC        CC               ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "              CC                     CC              ", "             CCC                     CCC             ", "             CCCC                   CCCC             ", "               C                     C               ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                        GGGGG                        ", "                  E     GGGGG     E                  ", "                  CCCCC GGGGG CCCCC                  ", "                  E     GGGGG     E                  ", "                        GGGGG                        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "               C                     C               ", "             CCCC                   CCCC             ", "             CCC                     CCC             ", "              CC                     CC              ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "             CCC                     CCC             ", "             CC                       CC             ", "             C                         C             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                E   E    GGG    E   E                ", "                CCCCC    GGG    CCCCC                ", "                E   E    GGG    E   E                ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             C                         C             ", "             CC                       CC             ", "             CCC                     CCC             ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                CCC      CTC      CCC                ", "           C    CC        T        CC    C           ", "                                                     ", "                                                     ", "           CC                           CC           ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             E  E                   E  E             ", "             CCCC                   CCCC             ", "             E  E                   E  E             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "           CC                           CC           ", "                                                     ", "                                                     ", "           C    CC        T        CC    C           ", "                CCC      CTC      CCC                ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                CCC      CTC      CCC                ", "               CCC        T        CCC               ", "            C CCC                   CCC C            ", "                                                     ", "                                                     ", "            C                           C            ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             CCC                     CCC             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "            C                           C            ", "                                                     ", "                                                     ", "            C CCC                   CCC C            ", "               CCC        T        CCC               ", "                CCC      CTC      CCC                ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                CC        T        CC                ", "              CCC                   CCC              ", "             CCC                     CCC             ", "           G                             G           ", "           G                             G           ", "             C                         C             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "            E  E                     E  E            ", "            CCCC                     CCCC            ", "            E  E                     E  E            ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             C                         C             ", "           G                             G           ", "           G                             G           ", "             CCC                     CCC             ", "              CCC                   CCC              ", "                CC        T        CC                ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "        C    C                         C    C        ", "          GHG                           GHG          ", "          GHG                           GHG          ", "        C    C                         C    C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "           CC                           CC           ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C    C                         C    C        ", "          GHG                           GHG          ", "          GHG                           GHG          ", "        C    C                         C    C        ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "         C                                 C         ", "           GG                           GG           ", "           GG                           GG           ", "        CC                                 CC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "          E                               E          ", "          CC                             CC          ", "          E                               E          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CC                                 CC        ", "           GG                           GG           ", "           GG                           GG           ", "         C                                 C         ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "          CC                             CC          ", "                                                     ", "                                                     ", "       C  CC                             CC  C       ", "      CC                                     CC      ", "      CC                                     CC      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        E                                   E        ", "        CCC                               CCC        ", "        E                                   E        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      CC                                     CC      ", "      CC                                     CC      ", "       C  CC                             CC  C       ", "                                                     ", "                                                     ", "          CC                             CC          ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "          C                               C          ", "         CC                               CC         ", "                                                     ", "                                                     ", "      CC                                     CC      ", "      CC                                     CC      ", "      C                                       C      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CCC                               CCC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      C                                       C      ", "      CC                                     CC      ", "      CC                                     CC      ", "                                                     ", "                                                     ", "         CC                               CC         ", "          C                               C          ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "         CC                               CC         ", "         CC                               CC         ", "                                                     ", "                                                     ", "      CC                                     CC      ", "      C                                       C      ", "     CC                                       CC     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "          E                               E          ", "        CCC                               CCC        ", "          E                               E          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "     CC                                       CC     ", "      C                                       C      ", "      CC                                     CC      ", "                                                     ", "                                                     ", "         CC                               CC         ", "         CC                               CC         ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "         CC                               CC         ", "        CCC                               CCC        ", "        CC                                 CC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "     CC                                       CC     ", "     CC                                       CC     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "       EE                                   EE       ", "       CC                                   CC       ", "       EE                                   EE       ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "     CC                                       CC     ", "     CC                                       CC     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CC                                 CC        ", "        CCC                               CCC        ", "         CC                               CC         ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ")
            .aisle("                                                     ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "        CCC                               CCC        ", "        CC                                 CC        ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    CC                                         CC    ", "    C                                          C     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "       C                                     C       ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                          C     ", "    CC                                         CC    ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "        CC                                 CC        ", "        CCC                               CCC        ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                                                     ")
            .aisle("                          T                          ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CC                                 CC        ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                           C    ", "    C                                          C     ", "    C                                          C     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      E                                       E      ", "      CC                                     CC      ", "      E                                       E      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                          C     ", "    C                                          C     ", "    C                                           C    ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "        CC                                 CC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                          T                          ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                          C     ", "   C                                            C    ", "   C                                             C   ", "  CC                                             CC  ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      CC                                     CC      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "  CC                                             CC  ", "   C                                             C   ", "   C                                            C    ", "    C                                          C     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "   C                                            C    ", "  CC                                             CC  ", " CCC                                             CCC ", " CCC                                             CCC ", "  C                                               C  ", "                                                     ", "                                                     ", "     E E                                     E E     ", "     CCC                                     CCC     ", "     E E                                     E E     ", "                                                     ", "                                                     ", "  C                                               C  ", " CCC                                             CCC ", " CCC                                             CCC ", "  CC                                             CC  ", "   C                                            C    ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "  CC                                             CC  ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", "                                                     ", "                                                     ", " CC  E                                         E  CC ", " CC  CC                                       CC  CC ", " CC  E                                         E  CC ", "                                                     ", "                                                     ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", "  CC                                             CC  ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "  C                                               C  ", " CCC                                             CCC ", " CCC                                             CCC ", " CC                                               CC ", " CC                       W                       CC ", " CC  E                   WWW                   E  CC ", " CC  CC                 DWWWD                 CC  CC ", " CC  E                   WWW                   E  CC ", " CC                       W                       CC ", " CC                                               CC ", " CCC                                             CCC ", " CCC                                             CCC ", "  C                                               C  ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", " CC                                               CC ", "                          D                          ", "                        DD#DD                        ", "C  GGG                 DD###DD                 GGG  C", "C  GGG                 D#####D                 GGG  C", "C  GGG                 DD###DD                 GGG  C", "                        DD#DD                        ", "                          D                          ", " CC                                               CC ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          D                          ", "                         DDD                         ", "  GGGGG                DD###DD                GGGGG  ", "I GGGGG                D#####D                GGGGG I", "I GGGGG               D#######D               GGGGG I", "I GGGGG                D#####D                GGGGG I", "  GGGGG                DD###DD                GGGGG  ", "                         DDD                         ", "                          D                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                   CCCCCCCTCCCCCCC                   ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                CCCCCCCCCCTCCCCCCCCCC                ", "             CCCC                   CCCC             ", "           CCCCC                     CCCCC           ", "          CCC                           CCC          ", "        CCCC                             CCCC        ", "       CCC                                 CCC       ", "       CC                                   CC       ", "      CC                                     CC      ", "      CC                                     CC      ", "     CC                                       CC     ", "    CC                                         CC    ", "    CC                                         CC    ", "    CC                                         CC    ", "   CC                                           CC   ", "  CC                                             CC  ", "  CC                                             CC  ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                     DDD                     CCC ", "C  GGG                  D###D                  GGG  C", "I GGGGG                D#####D                GGGGG I", "FGGGGGGG              W#######W              GGGGGGGF", "FGGGSGGG              W#######W              GGGSGGGF", "FGGGGGGG              W#######W              GGGGGGGF", "I GGGGG                D#####D                GGGGG I", "C  GGG                  D###D                  GGG  C", " CCC                     DDD                     CCC ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", "  CC                                             CC  ", "  CC                                             CC  ", "   CC                                           CC   ", "    CC                                         CC    ", "    CC                                         CC    ", "    CC                                         CC    ", "     CC                                       CC     ", "      CC                                     CC      ", "      CC                                     CC      ", "       CC                                   CC       ", "       CCC                                 CCC       ", "        CCCC                             CCCC        ", "          CCC                           CCC          ", "           CCCCC                     CCCCC           ", "             CCCC                   CCCC             ", "                CCCCCCCCCCTCCCCCCCCCC                ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                   CCCCCCCTCCCCCCC                   ")
            .aisle("                  TTTTTTTTTTTTTTTTT                  ", "                TTTTTTTTTTTTTTTTTTTTT                ", "                TTTTTTTTTTTTTTTTTTTTT                ", "            TTTTTT                 TTTTTT            ", "           TTTTT                     TTTTT           ", "         TTTT                           TTTT         ", "        TTTT                             TTTT        ", "       TTTT                               TTTT       ", "      TTTT                                 TTTT      ", "      TTT                                   TTT      ", "     TTT                                     TTT     ", "    TTT                                       TTT    ", "    TT                                         TT    ", "    TT                                         TT    ", "    TT                                         TT    ", "  TTT                                           TTT  ", "  TT                                             TT  ", " TTT                                             TTT ", " TTT                                             TTT ", " TTT                                             TTT ", " TTT                                             TTT ", " TTT                    DDDDD                    TTT ", "I  GGG                 DD###DD                 GGG  I", "I GGGGG               W#######W               GGGGG I", "FGGGSGGG              W#######W              GGGSGGGF", "FGGSSSGG              W#######W              GGSSSGGF", "FGGGSGGG              W#######W              GGGSGGGF", "I GGGGG               W#######W               GGGGG I", "I  GGG                 DD###DD                 GGG  I", " TTT                    DDDDD                    TTT ", " TTT                                             TTT ", " TTT                                             TTT ", " TTT                                             TTT ", " TTT                                             TTT ", "  TT                                             TT  ", "  TTT                                           TTT  ", "    TT                                         TT    ", "    TT                                         TT    ", "    TT                                         TT    ", "    TTT                                       TTT    ", "     TTT                                     TTT     ", "      TTT                                   TTT      ", "      TTTT                                 TTTT      ", "       TTTT                               TTTT       ", "        TTTT                             TTTT        ", "         TTTT                           TTTT         ", "           TTTTT                     TTTTT           ", "            TTTTTT                 TTTTTT            ", "                TTTTTTTTTTTTTTTTTTTTT                ", "                TTTTTTTTTTTTTTTTTTTTT                ", "                  TTTTTTTTTTTTTTTTT                  ")
            .aisle("                   CCCCCCCTCCCCCCC                   ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                CCCCCCCCCCTCCCCCCCCCC                ", "             CCCC                   CCCC             ", "           CCCCC                     CCCCC           ", "          CCC                           CCC          ", "        CCCC                             CCCC        ", "       CCC                                 CCC       ", "       CC                                   CC       ", "      CC                                     CC      ", "      CC                                     CC      ", "     CC                                       CC     ", "    CC                                         CC    ", "    CC                                         CC    ", "    CC                                         CC    ", "   CC                                           CC   ", "  CC                                             CC  ", "  CC                                             CC  ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                     DDD                     CCC ", "C  GGG                  D###D                  GGG  C", "I GGGGG                D#####D                GGGGG I", "FGGGGGGG              W#######W              GGGGGGGF", "FGGGSGGG              W#######W              GGGSGGGF", "FGGGGGGG              W#######W              GGGGGGGF", "I GGGGG                D#####D                GGGGG I", "C  GGG                  D###D                  GGG  C", " CCC                     DDD                     CCC ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", "  CC                                             CC  ", "  CC                                             CC  ", "   CC                                           CC   ", "    CC                                         CC    ", "    CC                                         CC    ", "    CC                                         CC    ", "     CC                                       CC     ", "      CC                                     CC      ", "      CC                                     CC      ", "       CC                                   CC       ", "       CCC                                 CCC       ", "        CCCC                             CCCC        ", "          CCC                           CCC          ", "           CCCCC                     CCCCC           ", "             CCCC                   CCCC             ", "                CCCCCCCCCCTCCCCCCCCCC                ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                   CCCCCCCTCCCCCCC                   ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          D                          ", "                         DDD                         ", "  GGGGG                DD###DD                GGGGG  ", "I GGGGG                D#####D                GGGGG I", "I GGGGG               D#######D               GGGGG I", "I GGGGG                D#####D                GGGGG I", "  GGGGG                DD###DD                GGGGG  ", "                         DDD                         ", "                          D                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", " CC                                               CC ", "                          D                          ", "                        DD#DD                        ", "C  GGG                 DD###DD                 GGG  C", "C  GGG                 D#####D                 GGG  C", "C  GGG                 DD###DD                 GGG  C", "                        DD#DD                        ", "                          D                          ", " CC                                               CC ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "  C                                               C  ", " CCC                                             CCC ", " CCC                                             CCC ", " CC                                               CC ", " CC                       W                       CC ", " CC  E                   WWW                   E  CC ", " CC  CC                 DWWWD                 CC  CC ", " CC  E                   WWW                   E  CC ", " CC                       W                       CC ", " CC                                               CC ", " CCC                                             CCC ", " CCC                                             CCC ", "  C                                               C  ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", "                                                     ", "                                                     ", " CC  E                                         E  CC ", " CC  CC                                       CC  CC ", " CC  E                                         E  CC ", "                                                     ", "                                                     ", " CCC                                             CCC ", " CCC                                             CCC ", " CCC                                             CCC ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "  CC                                             CC  ", " CCC                                             CCC ", " CCC                                             CCC ", "  C                                               C  ", "                                                     ", "                                                     ", "     E E                                     E E     ", "     CCC                                     CCC     ", "     E E                                     E E     ", "                                                     ", "                                                     ", "  C                                               C  ", " CCC                                             CCC ", " CCC                                             CCC ", "  CC                                             CC  ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "   C                                             C   ", "  CC                                             CC  ", "  CC                                             CC  ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      CC                                     CC      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "  CC                                             CC  ", "  CC                                             CC  ", "   C                                             C   ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ")
            .aisle("                          T                          ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CC                                 CC        ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                           C    ", "   C                                             C   ", "   C                                             C   ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      E                                       E      ", "      CC                                     CC      ", "      E                                       E      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "   C                                             C   ", "   C                                             C   ", "    C                                           C    ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "        CC                                 CC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                          T                          ")
            .aisle("                                                     ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "        CCC                               CCC        ", "        CC                                 CC        ", "        C                                   C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                           C    ", "    C                                           C    ", "    C                                           C    ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "       C                                     C       ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                           C    ", "    C                                           C    ", "    C                                           C    ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C                                   C        ", "        CC                                 CC        ", "        CCC                               CCC        ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                                                     ")
            .aisle("                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "         CC                               CC         ", "        CCC                               CCC        ", "        CC                                 CC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "     CC                                       CC     ", "    CC                                         CC    ", "    C                                           C    ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "       EE                                   EE       ", "       CC                                   CC       ", "       EE                                   EE       ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "    C                                           C    ", "    CC                                         CC    ", "     CC                                       CC     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CC                                 CC        ", "        CCC                               CCC        ", "         CC                               CC         ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "         CC                               CC         ", "         CC                               CC         ", "                                                     ", "                                                     ", "      CC                                     CC      ", "      C                                       C      ", "     CC                                       CC     ", "     CC                                       CC     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "          E                               E          ", "        CCC                               CCC        ", "          E                               E          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "     CC                                       CC     ", "     CC                                       CC     ", "      C                                       C      ", "      CC                                     CC      ", "                                                     ", "                                                     ", "         CC                               CC         ", "         CC                               CC         ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "          C                               C          ", "         CC                               CC         ", "                                                     ", "                                                     ", "      CC                                     CC      ", "      CC                                     CC      ", "      C                                       C      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CCC                               CCC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      C                                       C      ", "      CC                                     CC      ", "      CC                                     CC      ", "                                                     ", "                                                     ", "         CC                               CC         ", "          C                               C          ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "          CC                             CC C        ", "                                                     ", "                                                     ", "       C  CC                             CC  C       ", "      CC                                     CC      ", "      CC                                     CC      ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        E                                   E        ", "        CCC                               CCC        ", "        E                                   E        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "      CC                                     CC      ", "      CC                                     CC      ", "       C  CC                             CC  C       ", "                                                     ", "                                                     ", "          CC                             CC C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "         C                                   C       ", "           GG                           GG           ", "           GG                           GG           ", "        CC                                 CC        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "          E                               E          ", "          CC                             CC          ", "          E                               E          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        CC                                 CC        ", "           GG                           GG           ", "           GG                           GG           ", "         C                                   C       ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "        C    C                         C             ", "          GHG                           GHG          ", "          GHG                           GHG          ", "        C    C                         C    C        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "           CC                           CC           ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "        C    C                         C    C        ", "          GHG                           GHG          ", "          GHG                           GHG          ", "        C    C                         C             ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                CC        T        CC                ", "              CCC                   CCC              ", "             CCC                     CCC             ", "           G                             G           ", "           G                             G           ", "             C                         C             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "            E  E                     E  E            ", "            CCCC                     CCCC            ", "            E  E                     E  E            ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             C                         C             ", "           G                             G           ", "           G                             G           ", "             CCC                     CCC             ", "              CCC                   CCC              ", "                CC        T        CC                ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                CCC      CTC      CCC                ", "               CCC        T        CCC               ", "            C CCC                   CCC C            ", "                                                     ", "                                                     ", "            C                           C            ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             CCC                     CCC             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "            C                           C            ", "                                                     ", "                                                     ", "            C CCC                   CCC C            ", "               CCC        T        CCC               ", "                CCC      CTC      CCC                ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                CCC      CTC      CCC                ", "           C    CC        T        CC    C           ", "                                                     ", "                                                     ", "           CC                           CC           ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             E  E                   E  E             ", "             CCCC                   CCCC             ", "             E  E                   E  E             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "           CC                           CC           ", "                                                     ", "                                                     ", "           C    CC        T        CC    C           ", "                CCC      CTC      CCC                ", "                 CCCCCCCCCTCCCCCCCCC                 ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "             CCC                     CCC             ", "             CC                       CC             ", "             C                         C             ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                E   E    GGG    E   E                ", "                CCCCC    GGG    CCCCC                ", "                E   E    GGG    E   E                ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "             C                         C             ", "             CC                       CC             ", "             CCC                     CCC             ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "              CC                     CC              ", "             CCC                     CCC             ", "             CCCC                   CCCC             ", "               C                     C               ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                        GGGGG                        ", "                  E     GGGGG     E                  ", "                  CCCCC GGGGG CCCCC                  ", "                  E     GGGGG     E                  ", "                        GGGGG                        ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "               C                     C               ", "             CCCC                   CCCC             ", "             CCC                     CCC             ", "              CC                     CC              ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "               CC        CTC        CC               ", "               CC                   CC               ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         GGG                         ", "                        GGGGG                        ", "                    EEEGGGGGGGEEE                    ", "                    CCCGGGSGGGCCC                    ", "                    EEEGGGGGGGEEE                    ", "                        GGGGG                        ", "                         GGG                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "               CC                   CC               ", "               CC        CTC        CC               ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                CC       CTC       CC                ", "                CCC               CCC                ", "                 C                 C                 ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         GGG                         ", "                        GGGGG                        ", "                       GGGSGGG                       ", "                       GGSSSGG                       ", "                       GGGSGGG                       ", "                        GGGGG                        ", "                         GGG                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                 C                 C                 ", "                CCC               CCC                ", "                CC       CTC       CC                ", "                         CTC                         ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CTC                         ", "                         CTC                         ", "                  CC     CTC     CC                  ", "                  CCC    CTC    CCC                  ", "                   CCC   CTC   CCC                   ", "                    CCC  CTC  CCC                    ", "                     CC  CTC  CC                     ", "                         GGG                         ", "                        GGGGG                        ", "                       GGGGGGG                       ", "                       GGGSGGG                       ", "                       GGGGGGG                       ", "                        GGGGG                        ", "                         GGG                         ", "                     CC  CTC  CC                     ", "                    CCC  CTC  CCC                    ", "                   CCC   CTC   CCC                   ", "                  CCC    CTC    CCC                  ", "                  CC     CTC     CC                  ", "                         CTC                         ", "                         CTC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                         CTC                         ", "                   CC    CTC    CC                   ", "                   CCCC  CTC  CCCC                   ", "                    CCC  CTC  CCC                    ", "                    CCCC CTC CCCC                    ", "                      C       C                      ", "                      C GGGGG C                      ", "                     CC GGGGG CC                     ", "                     CC GGGGG CC                     ", "                     CC GGGGG CC                     ", "                      C GGGGG C                      ", "                      C       C                      ", "                    CCCC CTC CCCC                    ", "                    CCC  CTC  CCC                    ", "                   CCCC  CTC  CCCC                   ", "                   CC    CTC    CC                   ", "                         CTC                         ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                          T                          ", "                         CTC                         ", "                    CC   CTC   CC                    ", "                    CCC  CTC  CCC                    ", "                     CCC CTC CCC                     ", "                      C       C                      ", "                      C       C                      ", "                     CC  GGG  CC                     ", "                     CC  GGG  CC                     ", "                     CC  GGG  CC                     ", "                      C       C                      ", "                      C       C                      ", "                     CCC CTC CCC                     ", "                    CCC  CTC  CCC                    ", "                    CC   CTC   CC                    ", "                         CTC                         ", "                          T                          ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .aisle("                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                         CIC                         ", "                         III                         ", "                       CIFFFIC                       ", "                       CIFXFIC                       ", "                       CIFFFIC                       ", "                         III                         ", "                         CIC                         ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ", "                                                     ")
            .where('X', Predicates.controller(Predicates.blocks(definition.get())))
            .where('C', Predicates.blocks('gtceu:atomic_casing'))
            .where("T", Predicates.blocks(GTBlocks.SUPERCONDUCTING_COIL.get()).setMinGlobalLimited(600)
                .or(Predicates.abilities(PartAbility.EXPORT_ITEMS, PartAbility.EXPORT_FLUIDS, PartAbility.OUTPUT_LASER /*change to OUTPUT_LASER after that's released*/)))
            .where('G', Predicates.blocks("connectedglass:clear_glass_black"))
            .where('H', Predicates.blocks("allthecompressed:atm_star_block_2x"))
            .where('I', Predicates.abilities(PartAbility.IMPORT_ITEMS, PartAbility.IMPORT_FLUIDS, PartAbility.INPUT_LASER /*change to INPUT_LASER after that's released*/)
                .or(Predicates.blocks('gtceu:atomic_casing')))
            .where('F', Predicates['autoAbilities(boolean,boolean,boolean)'](true, false, true)
                .or(Predicates.blocks('gtceu:atomic_casing')))
            .where('D', Predicates.blocks('gtceu:fusion_casing_mk3'))
            .where('W', Predicates.blocks('gtceu:fusion_glass'))
            .where('E', Predicates.blocks('kubejs:micro_universe_energy_transmitter'))
            .where('S', Predicates.blocks('kubejs:micro_universe_focus_lens'))
            .where(' ', Predicates.any())
            .where('#', Predicates.air())
            .build())
        // 暂时移除渲染器调用以解决兼容性问题
        // .workableCasingRenderer("gtceu:block/casings/gcym/atomic_casing",
        //     "gtceu:block/multiblock/assembly_line", false)

        allthemods.create('energy_input_hatch', 'custom').tiers(GTValues.MAX)
            .definition((tier, builder) => {
                builder.rotationState(RotationState.ALL)
                    .abilities(PartAbility.INPUT_ENERGY)
                    .overlayTieredHullRenderer("energy_hatch.input")
            }).machine((holder) => { return new $EnergyHatchPartMachine(holder, GTValues.MAX, $IO.IN, 2) })
        
    })

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.