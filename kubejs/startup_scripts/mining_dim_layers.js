// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

GTCEuStartupEvents.registry('gtceu:world_gen_layer', allthemods => {
  GTWorldGenLayers.STONE.levels = [new ResourceLocation("minecraft", "overworld"), new ResourceLocation("allthemodium", "mining")];
  GTWorldGenLayers.DEEPSLATE.levels = [new ResourceLocation("minecraft", "overworld"), new ResourceLocation("allthemodium", "mining")];
  GTWorldGenLayers.NETHERRACK.levels = [new ResourceLocation("minecraft", "the_nether"), new ResourceLocation("allthemodium", "mining")];
  GTWorldGenLayers.ENDSTONE.levels = [new ResourceLocation("minecraft", "the_end"), new ResourceLocation("allthemodium", "mining")];
})

// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
