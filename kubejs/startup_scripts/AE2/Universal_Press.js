// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.

StartupEvents.registry('item', allthemods => {
    allthemods
        .create('universal_press')
        .texture('kubejs:item/universal_press')
        .maxStackSize(64)
        .displayName('通用冲压模具');

    allthemods
        .create('universal_addon_press')
        .texture('kubejs:item/universal_addon_press')
        .maxStackSize(64)
        .displayName('通用扩展冲压模具');

    allthemods
        .create('ultimate_universal_press')
        .texture('kubejs:item/ultimate_universal_press')
        .maxStackSize(64)
        .displayName('终极通用冲压模具');
})


// This File has been authored by AllTheMods Staff, or a Community contributor for use in AllTheMods - AllTheMods 9.
// As all AllTheMods packs are licensed under All Rights Reserved, this file is not allowed to be used in any public packs not released by the AllTheMods Team, without explicit permission.
