{"atm9.modpack.title": "All The Mods 9", "atm9.chapters.1.title": "Bienvenue", "atm9.chapters.2.title": "Allthemodium", "atm9.chapters.3.title": "Tableau des primes", "atm9.chapters.4.title": "Astuces et Conseils", "atm9.chapters.group.1.": "Ligne principale des quêtes", "atm9.chapters.5.title": "&aChapitre 1&r: &bLe Commencement", "atm9.chapters.6.title": "&aChapitre 2&r: &6L'Étoile ATM", "atm9.chapters.7.title": "&aChapitre 3&r: &dCréatif ", "atm9.chapters.group.2.": "Outils et Armures", "atm9.chapters.8.title": "Apotheosis Gear", "atm9.chapters.9.title": "Silent Gear", "atm9.chapters.group.3.": "Stockage", "atm9.chapters.10.title": "Stockage de base", "atm9.chapters.11.title": "Applied Energistics 2", "atm9.chapters.12.title": "Refined Storage", "atm9.chapters.group.4.": "Resources", "atm9.chapters.13.title": "Toute la Puissance", "atm9.chapters.14.title": "Apotheosis", "atm9.chapters.15.title": "Nourriture et Agriculture", "atm9.chapters.16.title": "Mystical Agriculture", "atm9.chapters.17.title": "Productive Bees", "atm9.chapters.group.5.": "Technologie", "atm9.chapters.18.title": "Create", "atm9.chapters.19.title": "Extreme Reactors", "atm9.chapters.20.title": "Industrial Foregoing", "atm9.chapters.21.title": "Mekanism", "atm9.chapters.22.title": "Mekanism: &dAdvanced", "atm9.chapters.23.title": "Powah", "atm9.chapters.24.title": "Thermal Series", "atm9.chapters.group.6.": "GregTech™", "atm9.chapters.25.title": "<PERSON><PERSON><PERSON><PERSON>", "atm9.chapters.26.title": "<PERSON><PERSON> <PERSON> vapeur", "atm9.chapters.27.title": "Basse Tension", "atm9.chapters.28.title": "Moyenne Tension", "atm9.chapters.29.title": "Haute Tension", "atm9.chapters.30.title": "Tension Extrême", "atm9.chapters.31.title": "Tension Insensée", "atm9.chapters.32.title": "Étapes", "atm9.chapters.group.7.": "<PERSON><PERSON>", "atm9.chapters.33.title": "Enchantement d'Apotheosis", "atm9.chapters.34.title": "Ars Nouveau", "atm9.chapters.35.title": "Blood Magic", "atm9.chapters.36.title": "Botania", "atm9.chapters.37.title": "EvilCraft", "atm9.chapters.38.title": "Forbidden and Arcanus", "atm9.chapters.39.title": "Occultism", "atm9.chapters.group.8.": "Exploration", "atm9.chapters.40.title": "Ad Astra", "atm9.chapters.41.title": "Blue Skies", "atm9.chapters.42.title": "Twilight Forest", "atm9.quest.welcome.team": "C<PERSON>er une Équipe", "atm9.quest.welcome.commands": "Commandes U<PERSON>s", "atm9.quest.welcome.welcome": "&dBienvenue dans All The Mods 9!", "atm9.quest.welcome.quests": "<PERSON><PERSON><PERSON><PERSON>", "atm9.quest.welcome.claims": "Claims des chunks", "atm9.quest.welcome.desc.team": "Si vous souhaitez créer une équipe pour vous et vos amis, utilisez la commande &a/ftbteams party create (nom de l'équipe)&r pour créer l'équipe!", "atm9.quest.welcome.desc.commands1": "Il y a beaucoup de commandes utiles dans ATM. Voici quelques-unes :", "atm9.quest.welcome.desc.commands2": "&e/sethome&r (Nom de la maison) | Vous permet de définir un point de spawn auquel vous pouvez revenir en utilisant /home (nom). Exemple : /sethome ferme - puis téléportez-vous avec /home ferme", "atm9.quest.welcome.desc.commands3": "&e/spawn&r | <PERSON><PERSON> vous téléportera au point d'apparition de votre monde principal.", "atm9.quest.welcome.desc.commands4": "&e/rtp&r | 'Téléportation Aléatoire' vous téléportera à des coordonnées aléatoire dans le monde.", "atm9.quest.welcome.desc.commands5": "Remarque : Ces commandes ont des délais de récupération et des limites. Si vous souhaitez les modifier, vous pouvez éditer le fichier de configuration qui se trouve ici:", "atm9.quest.welcome.desc.commands6": "- Pour le mode solo | &osaves/(Nom de la sauvegarde)/serverconfig/ftbessentials.snbt", "atm9.quest.welcome.desc.commands7": "- Pour les serveurs | &oworld/serverconfig/ftbessentials.snbt", "atm9.quest.welcome.desc.welcome1": "ATM9 est un pack 'kitchensink' qui vous permet d'explorer le monde de Minecraft moddé à votre manière!", "atm9.quest.welcome.desc.welcome2": "&lATM9 est actuellement dans les phases bêta de développement du modpack&r. Des mods seront ajoutés ou supprimés au fur et à mesure de leur mise à jour.", "atm9.quest.welcome.desc.welcome3": "Si vous avez des questions ou des problèmes, n'hésitez pas à rejoindre le discord d'ATM!", "atm9.quest.welcome.desc.quests1": "Dans ce modpack, les quêtes sont facultatives. Les mods ne sont pas bloqués derrière l'achèvement de quêtes!", "atm9.quest.welcome.desc.quests2": "Les lignes de quêtes individuelles en dehors de la ligne principale des quêtes sont censées servir de guides pour les mods. Si vous souhaitez suivre la progression, assurez-vous de consulter la ligne principale des quêtes!", "atm9.quest.welcome.desc.quests3": "La plupart des quêtes dans le pack sont également créées par &2AlfredGG&r. Les quêtes demandent beaucoup de travail, donc si vous souhaitez le soutenir, vous pouvez cliquer sur son image de joueur ci-dessous!", "atm9.quest.welcome.desc.claims1": "Pour revendiquer des chunks, ouvrez votre carte en appuyant sur &6M&r, puis cliquez sur l'icône &aClaim Map&rdans le coin supérieur gauche.", "atm9.quest.welcome.desc.claims2": "Pour revendiquer un chunk, cliquez gauche et faites glisser pour revendiquer.", "atm9.quest.welcome.desc.claims3": "Pour charger de force un chunk, maintenez la touche Maj/Shift enfoncée et cliquez gauche sur le chunk. Si cela est fait correctement, vous verrez des lignes à travers le chunk.", "atm9.quest.allthemodium.intro": "Introduction à Allthemodium!", "atm9.quest.allthemodium.atm_ore": "Minerai d'Allthemodium", "atm9.quest.allthemodium.vib_ore": "Minerai de Vibranium", "atm9.quest.allthemodium.unob_ore": "Minerai d'Unobtainium", "atm9.quest.allthemodium.atm_smith": "&6Améliorations d'AllTheModium", "atm9.quest.allthemodium.vib_smith": "&bAméliorations de Vibranium", "atm9.quest.allthemodium.unob_smith": "&dAméliorations d'Unobtainium", "atm9.quest.allthemodium.atm_armor": "&6Armure en Allthemodium", "atm9.quest.allthemodium.vib_armor": "&bArmure en Vibranium", "atm9.quest.allthemodium.unob_armor": "&dArmure en Unobtainium", "atm9.quest.allthemodium.atm_tools": "&6Outils en Allthemodium", "atm9.quest.allthemodium.vib_tools": "&bOutils en Vibranium", "atm9.quest.allthemodium.unob_tools": "&dOutils en Unobtainium", "atm9.quest.allthemodium.teleport": "Les Dimensions d'AllTheModium", "atm9.quest.allthemodium.mining": "Dimension Minière", "atm9.quest.allthemodium.other": "l'Autre-Monde", "atm9.quest.allthemodium.beyond": "l'Au-Delà", "atm9.quest.allthemodium.furnace": "&dFaster Furnaces", "atm9.quest.allthemodium.bees": "&6Productive ATM Bees", "atm9.quest.allthemodium.carrot": "Carotte d'AllTheModium", "atm9.quest.allthemodium.apple": "Pomme d'AllTheModium", "atm9.quest.allthemodium.atm_vib": "Alliage Vibranium-AllTheModium", "atm9.quest.allthemodium.atm_unob": "Alliage Unobtainium-AllTheModium", "atm9.quest.allthemodium.vib_unob": "Alliage Unobtainium-Vibranium", "atm9.quest.allthemodium.pickaxe": "Pioche en alliage", "atm9.quest.allthemodium.sword": "Lame en alliage", "atm9.quest.allthemodium.axe": "Hache en alliage", "atm9.quest.allthemodium.shovel": "Pelle en alliage", "atm9.quest.allthemodium.paxel": "Paxel en alliage", "atm9.quest.allthemodium.desc.intro1": "&dAllthemodium&r est le mod central dans tous les modpacks Allthemods. Ce mod ajoute des minerais de fin de partie dans le monde, amplifiant ainsi votre expérience moddée.", "atm9.quest.allthemodium.desc.intro2": "Vous pouvez trouver plus d'informations sur le mod dans le livre &9Allthemodium&r.", "atm9.quest.allthemodium.desc.atm_ore1": "Ce minerai lucratif marque le début de votre parcours vers la surpuissance!", "atm9.quest.allthemodium.desc.atm_ore2": "Il se trouve dans les biomes Deep Dark le long des plafonds et des murs, ou dans la Dimension minière au sein de la couche de deepslate.", "atm9.quest.allthemodium.desc.vib_ore1": "La prochaine étape de notre aventure pour devenir (presque) invincible.", "atm9.quest.allthemodium.desc.vib_ore2": "Trouvez ce minerai rare dans le Nether au-dessus Y64 le long des plafonds et des murs de n'importe quel biome.", "atm9.quest.allthemodium.desc.vib_ore3": "Vous pouvez également trouver ce minerai dans n'importe quel biome dans l'Autre-Monde, entre les altitudes Y0 et Y40, le long des parois et des plafonds à l'intérieur des grottes.", "atm9.quest.allthemodium.desc.vib_ore4": "Note: Pour trouver le minerai, il doit être à découvert et exposé à l'air!", "atm9.quest.allthemodium.desc.unob_ore": "Un minerai extrêmement rare que l'on ne peut trouver que dans le biome Highlands de l'End.", "atm9.quest.allthemodium.desc.atm_smith": "<PERSON>la peut être trouvé en &2brossant&r de l'&aArgile <PERSON>e&r dans la &dCité Antique&r.", "atm9.quest.allthemodium.desc.vib_smith": "<PERSON><PERSON> peut être trouvé en &2brossant&r du &aSable d'Âme Suspect&r dans les &dBastions&r du &cNether&r.", "atm9.quest.allthemodium.desc.unob_smith": "Cet objet peut être trouvé en tant que butin à l'intérieur des bibliothèques des &aDonjons&r de l'Autre-Monde.", "atm9.quest.allthemodium.desc.atm_tools": "Note: Bien que le &aModèle d'amélioration&r ne soit pas nécessaire pour créer l'outil initial, il vous fera économiser beaucoup d'&6lingots d'Allthemodium&r!", "atm9.quest.allthemodium.desc.teleport1": "Le Téléporteur est utilisé pour se téléporter vers les 3 nouvelles dimensions ajoutées par le pack ATM.", "atm9.quest.allthemodium.desc.teleport2": "Vous pouvez l'utiliser pour accéder à la &aDimension minière&r en le plaçant dans l'Overworld, puis en faisant un clic droit tout en maintenant la touche Maj/Shift enfoncée avec une main vide.", "atm9.quest.allthemodium.desc.teleport3": "Pour accéder à l'&cAutre-Monde&r, faites la même chose mais dans le Nether.", "atm9.quest.allthemodium.desc.teleport4": "Pour accéder à &5l'Au-Delà&r, utilisez le Téléporteur dans l'End.", "atm9.quest.allthemodium.desc.mining1": "La &9Dimension minière&r possède plusieurs couches pour trouver des minerais!", "atm9.quest.allthemodium.desc.mining2": "Cette dimension est dotée des couches habituelles de &3Pierre&r et de &3Deepslate&r de l'Overworld, ainsi que d'une couche de &cNetherrack&r pour trouver des minerais du Nether, et enfin d'une couche de &ePierre de l'End&r pour les minerais de l'End.", "atm9.quest.allthemodium.desc.other1": "Vous trouverez une tonne de minerais dans l'Autre-Monde. Elle regorge de génération de minerais incroyable, ainsi que de Forêts Anciennes.", "atm9.quest.allthemodium.desc.other2": "Au sein de ces forêts, vous pouvez trouver des Baies Anciennes qui accordent la Vision Nocturne.", "atm9.quest.allthemodium.desc.other3": "C'est également la seule dimension qui possède de la Lave d'Âme et des Pigliches!", "atm9.quest.allthemodium.desc.beyond1": "Situé au-delà de la limite de l'End, se trouve l'Au-Delà, un espace totalement incontournable pour les constructeurs qui recherchent une grande surface dégagée pour travailler.", "atm9.quest.allthemodium.desc.beyond2": "De manière similaire à l'Overworld-><PERSON><PERSON>, il existe un ratio de blocs de 1:50 pour l'End->l'Au-Delà.", "atm9.quest.allthemodium.desc.furnace": "Les &6Métaux Allthemodium&r peuvent être utilisés pour fabriquer des fours extrêmement rapides!", "atm9.quest.allthemodium.desc.bees": "Besoin de plus de &6Métaux ATM&r ? Élevez des abeilles!", "atm9.quest.bounty.board": "Le tableau des primes", "atm9.quest.bounty.zombie": "&l&9Prime dans l'Overworld:&r&e Zombies", "atm9.quest.bounty.skeleton": "&l&9Prime dans l'Overworld :&r&e Squelettes", "atm9.quest.bounty.creeper": "&l&9Prime dans l'Overworld :&r&e Creepers", "atm9.quest.bounty.spider": "&l&9Prime dans l'Overworld :&r&e Araignées", "atm9.quest.bounty.witch": "&l&9Prime dans l'Overworld :&r&e Sorcières", "atm9.quest.bounty.blaze": "&l&cPrime dans le Nether :&r&e Blazes", "atm9.quest.bounty.wither_skeleton": "&l&cPrime dans le Nether :&r&e <PERSON>er <PERSON>", "atm9.quest.bounty.enderman": "&l&9Prime dans l'End :&r&e Enderman", "atm9.quest.bounty.dragon": "<PERSON><PERSON> <PERSON>'<PERSON>", "atm9.quest.bounty.wither": "<PERSON><PERSON>", "atm9.quest.bounty.elder": "<PERSON>er le Gardien Ancien", "atm9.quest.bounty.warden": "<PERSON><PERSON> le Gardien des Profondeurs", "atm9.quest.bounty.trader": "<PERSON><PERSON> et ses Ennuyeux Lamas", "atm9.quest.bounty.chimera": "<PERSON><PERSON> <PERSON>", "atm9.quest.bounty.desc.board1": "<PERSON><PERSON>, vous trouverez toutes les récompenses que vous pouvez obtenir en tuant des ennemis.", "atm9.quest.bounty.desc.board2": "Cette page est en cours d'élaboration!", "atm9.quest.bounty.desc.trader1": "'En créant de nombreuses lignes de quêtes pour l'ATM7, le Villageois Marchand a pensé que ce serait amusant de me pousser constamment dans l'écran de quête.", "atm9.quest.bounty.desc.trader2": "Éliminez-les. Tous.'", "atm9.quest.bounty.desc.trader3": "- AlfredGG", "atm9.quest.bounty.subt.board": "<PERSON>er toutes ces choses", "atm9.quest.bounty.subt.zombie": "<PERSON><PERSON> 5 Zombies", "atm9.quest.bounty.subt.skeleton": "<PERSON><PERSON> 5 <PERSON><PERSON><PERSON>", "atm9.quest.bounty.subt.creeper": "<PERSON><PERSON> 5 Creepers", "atm9.quest.bounty.subt.spider": "Tuer 5 Araignées", "atm9.quest.bounty.subt.witch": "Tuer 5 Sorcières", "atm9.quest.bounty.subt.blaze": "<PERSON><PERSON> 5 Blazes", "atm9.quest.bounty.subt.wither_skeleton": "Tuer 5 <PERSON><PERSON>", "atm9.quest.bounty.subt.enderman": "Tuer 5 Enderman", "atm9.quest.bounty.subt.trader": "'C'est personnel' - AlfredGG", "atm9.quest.bounty.subt.chimera": "Ce n'est même pas ma forme finale.", "atm9.quest.tips.tricks": "Conseils et Astuces!", "atm9.quest.tips.tipped_out": "Conseils pour être bien équipé", "atm9.quest.tips.mobs": "Prévention des apparitions de monstres", "atm9.quest.tips.stick": "l'Artisanat... sur un bâton!", "atm9.quest.tips.exp": "Stockage de l'expérience", "atm9.quest.tips.magnet": "Aimants simples", "atm9.quest.tips.shrink": "Dispositif de rétrécissement personnel", "atm9.quest.tips.wand": "Baguettes de construction", "atm9.quest.tips.compass": "<PERSON><PERSON><PERSON> de la nature", "atm9.quest.tips.sleep": "Conforts", "atm9.quest.tips.belt": "Ceinture à outils", "atm9.quest.tips.sink": "<PERSON><PERSON> infinie", "atm9.quest.tips.spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "atm9.quest.tips.mahou": "<PERSON><PERSON>", "atm9.quest.tips.powah": "Génération de RF", "atm9.quest.tips.desc.tricks": "Sur cette page, vous trouverez quelques objets utiles et des informations pour vous aider dans votre périple!", "atm9.quest.tips.desc.mobs1": "Une fois placée, la &9Méga Torche&r empêche l'apparition naturelle de tous les monstres hostiles dans un rayon de 64 blocs.", "atm9.quest.tips.desc.mobs2": "Idéale pour arrêter les apparitions hostiles dans les parties sombres de votre base!", "atm9.quest.tips.desc.stick1": "Besoin d'une table de craft portable? Et pourquoi pas d'une table de forge portable?", "atm9.quest.tips.desc.stick2": "Avec &9Crafting On A Stick&r, ajoutez des versions portables de certaines de vos tables de craft préférées! Un must-have en début de partie.", "atm9.quest.tips.desc.exp1": "Le &9Cristal d'Expérience&r vous permet de stocker de l'expérience, soit en lui donnant vos niveaux, soit en pompant de l'expérience depuis un réservoir.", "atm9.quest.tips.desc.exp2": "Vous pouvez également transférer l'expérience stockée pour vous monter de niveau d'un simple clic sur un bouton!", "atm9.quest.tips.desc.magnet1": "<PERSON>ci est un aimant simple!", "atm9.quest.tips.desc.magnet2": "Astuce de pro: V<PERSON> pouvez définir un raccourci clavier pour activer et désactiver cela!", "atm9.quest.tips.desc.shrink": "Utilisez cet objet pour rétrécir. Utile pour travailler sur l'automatisation et aussi juste pour s'amuser en général.", "atm9.quest.tips.desc.wand1": "Le mod &9Construction Wand&r ajoute des baguettes pratiques utilisées lors de la construction.", "atm9.quest.tips.desc.wand2": "En faisant un clic droit sur la face d'un bloc avec la baguette, elle prolongera cette face tant que vous aurez les blocs dans votre inventaire.", "atm9.quest.tips.desc.compass1": "Vous donne une liste de biomes que vous pouvez rechercher.", "atm9.quest.tips.desc.compass2": "Sélectionnez un biome, puis appuyez sur 'Rechercher'. Vous verrez des informations en haut à gauche, et la boussole pointera dans la direction du biome.", "atm9.quest.tips.desc.sleep1": "Le sac de couchage vous permet de dormir la nuit.", "atm9.quest.tips.desc.sleep2": "Le hamac vous permet de dormir pendant la journée.", "atm9.quest.tips.desc.sleep3": "Aucun des deux ne réinitialisera votre point de spawn.", "atm9.quest.tips.desc.belt1": "Un moyen rapide de passer entre les outils.", "atm9.quest.tips.desc.belt2": "<PERSON><PERSON><PERSON>rez avec des poches de ceinture dans une enclume pour augmenter la capacité.", "atm9.quest.tips.desc.sink": "Objet simple pour automatiser l'eau infinie. Besoin de plus d'eau? En fabriquer un autre!", "atm9.quest.tips.desc.spawner1": "&6Supprimer l'IA: &r&m&4Fruit de Chorus&r&r Pomme dorée", "atm9.quest.tips.desc.spawner2": "&6Ignorer les joueurs: &r&m&4Étoile du Nether&r&r Conduit", "atm9.quest.tips.desc.spawner3": "&6Augmenter les entités: &rLarmes de Ghast | Max &m&432&r&r 16", "atm9.quest.tips.desc.spawner4": "&6Augmenter le nombre d'apparitions: &r&m&4Œil d'araignée fermenté&r&r <PERSON><PERSON><PERSON> <PERSON> | Max &m&416&r&r 8", "atm9.quest.tips.desc.spawner5": "&6Diminuer le délai d'apparition minimum:&r &m&4Sucre&r&r Lingot d'Allthemodium | Min &m&420&r&r 100", "atm9.quest.tips.desc.spawner6": "&6Diminuer le délai d'apparition maximum:&r &m&4Horloge&r&r Lingot d'Unobtainium | Min &m&420&r&r 100", "atm9.quest.tips.desc.mahou1": "&4Augmenter la capacité innée nécessite de jeter des Orbes d'amélioration de l'Ender (83 au maximum) dans le lac Mahou avec Caliburn", "atm9.quest.tips.desc.mahou2": "&5Convertir Caliburn en Morgan nécessite de tuer le Gardien avec Caliburn", "atm9.quest.tips.desc.powah1": "La génération de puissance a subi quelques ajustements!", "atm9.quest.tips.desc.powah2": "La puissance des melons n'est probablement pas celle que vous cherchiez!", "atm9.quest.tips.desc.powah3": "Les générateurs à gaz brûlant ne sont que d'environ 11% aussi puissants que les normaux.", "atm9.quest.tips.desc.powah4": "Pour compenser, les générateurs de départ Powah ont été améliorés. Les réacteurs extrêmes ont été améliorés. Le générateur bio Mekanism a été amélioré. Generators Galore propose également des générateurs de départ plus puissants!", "atm9.quest.tips.desc.powah5": "&8Entre nous, un générateur thermo nitro Powah avec de la lave d'âme à côté produit 31,5 k RF/t et ne consomme que de l'eau, mais vous ne l'avez pas entendu de ma part. ", "atm9.quest.tips.subt.tricks": "Et d'autres objets utiles!", "atm9.quest.tips.subt.tipped_out": "Terminez tous les conseils!", "atm9.quest.tips.subt.magnet": "Un aimant simple!", "atm9.quest.tips.subt.shrink": "<PERSON><PERSON><PERSON>(e), j'a<PERSON> r<PERSON><PERSON><PERSON><PERSON> les dimensions", "atm9.quest.tips.subt.compass": "Aide à trouver des biomes", "atm9.quest.tips.subt.sleep": "ZZZzzz...", "atm9.quest.tips.subt.sink": "Peut être utilisé comme fluide de refroidissement pour le réacteur", "atm9.quest.tips.subt.spawner": "Changements des spawnners d'Apotheosis", "atm9.quest.tips.subt.mahou": "Changements de Mahou dans ATM9", "atm9.quest.tips.subt.powah": "Puissance??  POWAH!", "atm9.quest.affixes.gems": "Équipement d'Apotheosis", "atm9.quest.affixes.dust": "Poussière de Gemme", "atm9.quest.affixes.smith": "Application de Gemmes (et autres)", "atm9.quest.affixes.gem_cutting": "Améliorer les Gemmes", "atm9.quest.affixes.flawless": "Gemmes sans défaut", "atm9.quest.affixes.affix": "Objets avec des affixes", "atm9.quest.affixes.ancient": "Le meilleur du meilleur", "atm9.quest.affixes.vials_and_sigils": "Fioles et Sigils", "atm9.quest.affixes.sigil": "&5Sigil d'Enchatonnage", "atm9.quest.affixes.superior_sigil": "<PERSON>gi<PERSON> supérieur d'enchatonnage", "atm9.quest.affixes.vialU": "Fiole de Désignation", "atm9.quest.affixes.vialA": "Fiole d'Extraction Arcanique", "atm9.quest.affixes.vialS": "Fiole d'Expulsion Sismique", "atm9.quest.affixes.salvaging_table": "Table de Récupération", "atm9.quest.affixes.common": "&7Matériaux Mystérieux de Récupération&n", "atm9.quest.affixes.uncommon": "&2Tissu Ancien", "atm9.quest.affixes.rare": "&9Éclat de Cristal Lumineux", "atm9.quest.affixes.epic": "&5Graines Arcanes", "atm9.quest.affixes.mythic": "&6Perle Forgée par les Dieux", "atm9.quest.affixes.simple": "Table de Recalibrage Simple", "atm9.quest.affixes.reforge": "(Mei<PERSON><PERSON>) Table de Recalibrage", "atm9.quest.affixes.desc.gems": "Si vous voulez des équipements de qualité, vous aurez besoin des affixes d'Apotheosis, et tout commence avec une Gemme.", "atm9.quest.affixes.desc.dust": "Pour obtenir de la Poussière de Gemme, vous avez besoin d'une Gemme d'Apotheosis et d'une enclume. N'importe quelle Gemme d'Apotheosis (il est recommandé d'utiliser uniquement les communes et les peu communes). Une fois que vous avez vos gemmes, écrasez-les avec une enclume tombante! Ensuite, pour faciliter cela, créez une Table de Récupération.", "atm9.quest.affixes.desc.smith": "Vous avez des outils et des gemmes, alors comment les puis-je les combiner? Tout d'abord, assurez-vous que votre outil a une Encoche ouverte. (Pour plus d'informations sur les Encoches, consultez la section Fioles et Sigils). Si une Encoche est ouverte, vous pouvez combiner votre outil et votre Gemme dans une Table de Forgeron. Si vous n'êtes pas satisfait de vos Gemmes actuelles, vous pourriez avoir besoin d'une...", "atm9.quest.affixes.desc.gem_cutting": "Table Tailleuse de Gemmes! Pour changer la Rareté de votre Gemme, vous devrez utiliser cette table. En utilisant 2 Gemmes identiques et des Matériaux de Rareté, vous pouvez augmenter la rareté de vos Gemmes et cela augmente leur puissance.", "atm9.quest.affixes.desc.flawless": "Plus la Gemme est de qualité, meilleures sont les statistiques! Impeccable représente la seconde meilleure qualité, tandis que Parfait est le summum.", "atm9.quest.affixes.desc.affix": "Les armes avec des affixes peuvent être trouvées de nombreuses manières, mais comment savoir ce qui est affixé ? Les armes avec des affixes auront toujours des noms très longs, généralement avec le type d'arme et le nom de l'ancien propriétaire. Elle sera également colorée selon sa rareté, vert pour peu commune, bleu pour rare, et ainsi de suite. Elle aura également des statistiques bonus ou du moins une augmentation de la capacité d'enchantement.", "atm9.quest.affixes.desc.ancient": "Les affixes mythiques offrent les meilleures statistiques, ainsi, les armes mythiques sont l'objectif à atteindre. Bonne chance pour en acquérir une!", "atm9.quest.affixes.desc.vials_and_sigils": "Les Fioles et Sigils sont des objets utilisés pour modifier les encoches de vos armes, que ce soit pour en ajouter davantage ou les libérer. Tous sont utilisés avec votre objet dans une Table de Forgeron.", "atm9.quest.affixes.desc.sigil": "Pour les novices d'Apotheosis, les encoches peuvent être déconcertantes et agaçantes. Vos armes et armures ont besoin d'encoches pour utiliser des Gemmes avec elles. Mais que faire s'il n'y a pas d'encoche? Alors vous aurez besoin d'un Sigil d'Enchatonnage! Combinez votre objet et le Sigil dans une Table de Forgeron et vous pourrez obtenir jusqu'à 3 encoches.", "atm9.quest.affixes.desc.superior_sigil": "Le Sigil Supérieur d'Enchatonnage fait exactement ce que fait sa version inférieure, mais avec jusqu'à 4 encoches au lieu de 3.", "atm9.quest.affixes.desc.vialU": "La Fiole de Désignation fait ce qu'elle suggère. Lorsque vous avez un objet avec un nom maladroitement long, vous pouvez le combiner avec la fiole dans une table de forgeron pour vous débarrasser de la plupart du nom. (Se<PERSON> le matériau et le type d'arme, ainsi que la couleur de rareté, resteront).", "atm9.quest.affixes.desc.vialA": "La Fiole d'Extraction (Arcane) fait l'inverse de la Fiole d'Expulsion, au lieu de casser la Gemme, elle casse l'objet et vous pouvez conserver la Gemme.", "atm9.quest.affixes.desc.vialS": "La Fiole d'Expulsion (Sismique) peut être utilisée dans une Table de Forgeron pour retirer une Gemme de l'encoche d'un objet. Attention, cela cassera la Gemme et ouvrira simplement une encoche.", "atm9.quest.affixes.desc.salvaging_table": "La Table de Récupération peut enfin vous procurer des matériaux et de la Poussière de Gemme sans utiliser d'enclumes. Vous pouvez recycler des outils et des armures avec des affixes pour obtenir leurs matériaux. Et démonter des armures pour chevaux, pour une raison quelconque.", "atm9.quest.affixes.desc.simple": "La Table de Recalibrage Simple est utilisée comme une Table d'Enchantement mais avec des Affixes. Avec de la Poussière de Gemme, des Matériaux de Rareté et un objet avec des affixes, vous pouvez relancer les affixes moyennant des points d'expérience. Cela peut également être utilisé pour changer la rareté de l'objet affixé jusqu'à Rare.", "atm9.quest.affixes.desc.reforge": "La Table de Recalibrage fait tout ce que fait une table simple mais en mieux, elle peut également ajouter des affixes épiques et mythiques!", "atm9.quest.affixes.subt.common": "<PERSON><PERSON><PERSON>", "atm9.quest.affixes.subt.uncommon": "Peu commun", "atm9.quest.affixes.subt.rare": "Rare", "atm9.quest.affixes.subt.epic": "Épique", "atm9.quest.affixes.subt.mythic": "Mythique", "atm9.quest.spawner.apotheosis": "Apotheosis", "atm9.quest.spawner.changes": "Changements Fondamentaux dans le Jeu", "atm9.quest.spawner.anvil": "Enclume et Ciseaux", "atm9.quest.spawner.cactus": "Les cultures vertes sont maintenant plus hautes.", "atm9.quest.spawner.fletcher": "La Table de Flèches fonctionne désormais!", "atm9.quest.spawner.spawner": "Générateur de Monstres", "atm9.quest.spawner.prismarine": "Portée d'Activation", "atm9.quest.spawner.ghast": "Entités max", "atm9.quest.spawner.atm": "Retard minimum de Spawn", "atm9.quest.spawner.unob": "Retard maximum de Spawn", "atm9.quest.spawner.quartz": "<PERSON>ur oppo<PERSON><PERSON>", "atm9.quest.spawner.lantern": "Ignorer la Lumière", "atm9.quest.spawner.redstone": "Redstone Active", "atm9.quest.spawner.conduit": "Ignorer les Joueurs", "atm9.quest.spawner.dragon": "Ignorer TOUTES les Conditions", "atm9.quest.spawner.wool": "Calmez-vous, vous allez réveiller les monstres !", "atm9.quest.spawner.piglich": "Nombre d'Apparitions", "atm9.quest.spawner.apple": "Pas <PERSON>", "atm9.quest.spawner.egg": "<PERSON><PERSON>", "atm9.quest.spawner.rods": "Portée d'apparition", "atm9.quest.spawner.desc.apotheosis": "Apotheosis est un mod assez volumineux, je le divise donc en 3 chapitres de quêtes. L'enchantement et les affixes seront séparés. Celui-ci concerne les changements normaux dans le jeu.", "atm9.quest.spawner.desc.changes": "Apotheosis apporte quelques petits changements à Minecraft, ne vous inquiétez pas, tous sont bons et utiles!", "atm9.quest.spawner.desc.anvil": "Certains des objets modifiés avec Apotheosis sont les enchantements pour les enclumes et les cisailles. Les cisailles peuvent désormais utiliser les enchantements normaux, mais aussi Fortune et de nouveaux enchantements. Les cisailles peuvent obtenir du Sérum de Croissance, de l'Aberation Chromatique et de l'Exploitation des Travailleurs. Vous pouvez découvrir par vous-même ce qu'ils font. Les enclumes peuvent désormais être enchantées avec Inaltérabilité et de nouveaux enchantements également! Division et Oblitération. Les deux sont à appliqués sur une enclume, ensuite utilisez l'enclume enchantée pour transférer les enchantements sur un livre enchanté.", "atm9.quest.spawner.desc.cactus": "Vous vous êtes déjà lassé des hauteurs 'normales' des cactus, du bambou et de la canne à sucre? Vous avez déjà souhaité voir des gratte-ciel de bambou? Apotheosis peut vous apporter vos gratte-ciel de cultures! Maintenant, les limites de hauteur pour les cactus, le bambou et la canne à sucre ont été augmentées! Jusqu'où? Je ne sais pas, quelle est la hauteur limite du monde?", "atm9.quest.spawner.desc.fletcher": "La Table de Flèches existe depuis la version 1.14 mais son utilisation est limitée à un emploi pour les Villageois! Et pour le joueur?!?! Au lieu de fabriquer des flèches avec des effets de potion dans une table de craft, vous pouvez maintenant utiliser la Table de Flèches. Il est simplement plus élégant et professionnel d'utiliser la table spécialement conçue à cet effet.", "atm9.quest.spawner.desc.spawner": "Un autre changement de jeu qu'apporte Apotheosis concerne les Générateurs. Vous vous souvenez de pouvoir extraire les générateurs avec Silk Touch? Eh bien, c'est de retour grâce à Apotheosis! Il y a aussi de nombreuses nouvelles modifications que vous pouvez ajouter aux Générateurs en cliquant avec le bouton droit sur n'importe lequel de ces objets.", "atm9.quest.spawner.desc.prismarine": "La Portée d'Activation est la distance à laquelle le Joueur (vous) doit être du Générateur pour qu'il fonctionne. Le plus bas qu'il puisse être est de 1 bloc et le plus haut est de 48 blocs. Ils se déplacent en cercle autour du générateur plutôt que d'être placés directement sur des blocs. Le Conduit et l'Œuf de Dragon ignoreront cela.", "atm9.quest.spawner.desc.ghast": "Le nombre maximal d'entités est la quantité de monstres qui peuvent être générés par un générateur et conservés. S'il n'y a que 6 entités maximales et que 6 monstres sont déjà géné<PERSON>, aucun autre ne sera généré tant qu'ils ne seront pas morts ou déplacés. <PERSON>que Larm<PERSON> de <PERSON>t le fait monter ou descendre de 2 entités. Maximum de 16 entités et minimum de 1.", "atm9.quest.spawner.desc.atm": "Pour déterminer quand le Générateur générera, il choisit un nombre aléatoire entre le retard de spawn maximum et minimum. Le minimum peut être aussi bas que 100 et aussi haut que 32 767. <PERSON><PERSON> fait monter ou descendre de 10.", "atm9.quest.spawner.desc.unob": "Le Retard Maximum de Spawn est le temps qu'il pourrait mettre pour générer des monstres. Tout comme le Minimum, il peut être aussi bas que 100 et aussi haut que 32 767. <PERSON><PERSON> don<PERSON> 10. Les chiffres sont en ticks de Minecraft, 20 ticks équivalent à 1 seconde. <PERSON><PERSON>, le délai de spawn le plus rapide serait de 5 secondes entre chaque.", "atm9.quest.spawner.desc.quartz": "Le Quartz fait la même chose que l'autre objet, il fait l'inverse pour le Générateur. Avec le Quartz dans votre main secondaire et l'autre objet de modification du Générateur dans votre main principale, il fera l'inverse de son rôle. Avec du Quartz et des Bâtons de Blaze au lieu d'augmenter la Portée d'Apparition, elle la diminuera. Avec du Quartz et des Larmes de Ghast, elle diminuera le nombre d'Entités Max.", "atm9.quest.spawner.desc.lantern": "Certains mobs (principalement des monstres) ont besoin de certains niveaux de lumière pour apparaître. Ceux hostiles ont besoin de niveaux plus bas et les pacifiques ont besoin de niveaux plus élevés. L'utilisation d'une Lanterne d'Âme fait en sorte que vous n'ayez jamais à vous soucier des niveaux de lumière car elle les ignore! Cela n'ignore pas les autres conditions de génération telles que les animaux d'élevage ayant besoin d'herbe. C'est une autre condition qui est négligée par l'Œuf de Dragon.", "atm9.quest.spawner.desc.redstone": "L'Activation Redstone donne à votre générateur un interrupteur marche/arrêt. Sans alimentation Redstone, il ne produira pas de spawn.", "atm9.quest.spawner.desc.conduit": "Le Conduit fera ce que font les éclats de Prismarine, mais en mieux. Les joueurs n'ont plus besoin d'être près du générateur. Tant qu'il est chargé en chunk, il générera.", "atm9.quest.spawner.desc.dragon": "Le must-have pour tous les Générateurs. Lorsqu'il mentionne qu'il ignore toutes les conditions, cela signifie qu'il néglige toutes les conditions (bien que pas vraiment toutes). Ignore les niveaux de lumière, les blocs nécessaires à la génération et les joueurs à proximité. Les conditions d'espace sont toujours nécessaires, comme les slimes ayant besoin d'une zone de 3x3 pour apparaître.", "atm9.quest.spawner.desc.wool": "La seule fonction de la Laine pour le générateur est de le rendre silencieux. Vous n'aimez pas entendre les bruits stupides du Générateur? Alors utilisez de la Laine! Indépendamment de la couleur, du fil ou du motif!", "atm9.quest.spawner.desc.piglich": "Le Cœur de Piglich est largué par... eh bien, le Piglich. Il peut être utilisé pour augmenter la quantité de monstres qui PEUVENT apparaître d'un générateur. Les monstres générés sont aléatoires avec une quantité maximale déterminée par les Cœurs de Piglich. Il monte ou descend d'un pour chaque cœur, jusqu'à un maximum de 8.", "atm9.quest.spawner.desc.apple": "En appliquant une Pomme d'Or sur un générateur, vous extrayez les âmes des monstres qui seront générées, ne laissant qu'une coquille vide de leur forme originale. Les monstres perdront toute IA, ils feront donc essentiellement ce qu'un support d'armure fait. Ils ne peuvent pas vous frapper, ne peuvent pas se téléporter, ne peuvent pas se déplacer d'eux-mêmes. Ils restent là, prêts à être tués, quelle excitation!", "atm9.quest.spawner.desc.egg": "Ceci pourrait sembler nouveau pour ceux qui reviennent des versions précédentes. En utilisant un œuf de tortue sur un générateur, il ne générera que des versions bébé des monstres en lui-même. Cela ne fonctionne qu'avec les versions bébé vanilla des monstres, pas avec ceux des mods.", "atm9.quest.spawner.desc.rods": "La Portée d'Apparition est la zone où les monstres peuvent apparaître. Plus la zone est grande, plus il y a de place pour qu'ils apparaissent. Plus la zone est petite, moins cher est l'usine.", "atm9.quest.enchant.enchant": "Enchantement avec Apotheosis", "atm9.quest.enchant.book": "La limite maximale de Vanilla n'est qu'un début", "atm9.quest.enchant.hellshelf": "Bibliothèques de l'Enfer", "atm9.quest.enchant.seashelf": "Bibliothèques Marines", "atm9.quest.enchant.infusion": "Enchantement par Infusion", "atm9.quest.enchant.arcana": "Arcana", "atm9.quest.enchant.quanta": "Quanta", "atm9.quest.enchant.eterna": "Eterna", "atm9.quest.enchant.negative": "Quantités négatives", "atm9.quest.enchant.other": "Autres objets d'infusion", "atm9.quest.enchant.charms": "Rendre les charmes d'Apotheosis incassables", "atm9.quest.enchant.trident": "Création d'un vrai trident", "atm9.quest.enchant.library": "Bibliothèque d'enchantement", "atm9.quest.enchant.alexandria": "Bibliothèque d'Alexandrie", "atm9.quest.enchant.infused_hellshelf": "Bibliothèque de l'Enfer infusée", "atm9.quest.enchant.infused_seashelf": "Bibliothèque Marine infusée", "atm9.quest.enchant.sight": "Indices d'enchantement", "atm9.quest.enchant.retification": "Rectification", "atm9.quest.enchant.blazing": "Bibliothèque de l'Enfer Flamboyante", "atm9.quest.enchant.glowing": "Bibliothèque de l'Enfer Lumineuse", "atm9.quest.enchant.crystalline": "Bibliothèque Marine Cristalline", "atm9.quest.enchant.heart-forged": "Bibliothèque Marine Forgée par le Cœur", "atm9.quest.enchant.deepshelf": "Bibliothèque Profonde", "atm9.quest.enchant.Soul_deep": "Bibliothèque Profonde touchée par l'Âme", "atm9.quest.enchant.Soul_sculk": "Bibliothèque Skulk touchée par l'Âme", "atm9.quest.enchant.echo_deep": "Bibliothèque Profonde résonnante", "atm9.quest.enchant.echo_sculk": "Bibliothèque Skulk résonnante", "atm9.quest.enchant.endshelf": "Bibliothèque de l'End", "atm9.quest.enchant.pearlescent": "Bibliothèque de l'End Nacrée", "atm9.quest.enchant.draconic": "Bibliothèque de l'End Draconique", "atm9.quest.enchant.perfect": "Meilleure configuration d'enchantement", "atm9.quest.enchant.desc.enchant": "L'enchantement subit quelques modifications avec Apotheosis. <PERSON><PERSON> le résumer, 15 bibliothèques ne suffiront plus maintenant. De nouvelles bibliothèques ont été introduites, et des fonctionnalités supplémentaires sont désormais disponibles avec les tables d'enchantement. Nous espérons que ces quêtes vous fourniront les informations nécessaires pour une meilleure compréhension.", "atm9.quest.enchant.desc.book": "Les bibliothèques sont votre point de départ, mais certainement pas votre point final. <PERSON> moi<PERSON>, pas avec les bibliothèques classique. Avec seulement des bibliothèques normales, vous ne pouvez obtenir qu'Eterna jusqu'à un maximum de 15. (Je vais expliquer les niveaux d'enchantement bientôt, mais sachez simplement que vous en avez besoin)", "atm9.quest.enchant.desc.hellshelf": "Les bibliothèques de l'Enfer sont votre introduction à Quanta, elles donnent 3% de Quanta et 1,5 Eterna. Mieux que les bibliothèques classique, n'est-ce pas? Vous allez en avoir besoin d'au moins 6 pour la prochaine étape.", "atm9.quest.enchant.desc.seashelf": "Les bibliothèques marines sont votre introduction à l'Arcana, elles donnent 2% d'Arcana et 1,5 Eterna. Mieux que les bibliothèques classique, n'est-ce pas? Vous allez en avoir besoin d'au moins 6 pour la prochaine étape.", "atm9.quest.enchant.desc.infusion": "L'infusion est une version spéciale de l'enchantement qui est ironiquement utilisée pour de meilleurs enchantements. Lorsque la bonne quantité d'Eterna, Quanta et Arcana est atteinte, les enchantements offriront l'infusion. (Pour connaître les niveaux nécessaires, vous pouvez consulter JEI ou suivre ces quêtes)", "atm9.quest.enchant.desc.arcana": "L'Arcana est une quantité très importante, elle augmente le nombre d'enchantements que vous obtenez et rend les enchantements rares plus courants. Un exemple serait avec les épées, châtiment est un enchantement très courant, mais le pillage est beaucoup plus rare. La valeur par défaut de l'Arcana est de 0% et le maximum est de 100%.", "atm9.quest.enchant.desc.quanta": "Quanta détermine la randomité des enchantements que vous obtenez. <PERSON><PERSON> peut être utilisé contre vous en fonction de la Rectification. Si le Quanta est élevé et la Rectification est faible, vous avez plus de chances d'obtenir de mauvais enchantements et des malédictions. Le Quanta par défaut est de 15% et son maximum est de 100%. La Rectification par défaut est de 0% et le maximum est de 100%.", "atm9.quest.enchant.desc.eterna": "Eterna définit le niveau d'enchantement qui détermine les enchantements que vous pouvez obtenir ou obtiendrez. Sa valeur par défaut est de 0 et le maximum est de 50.", "atm9.quest.enchant.desc.negative": "Certaines infusions nécessitent des quantités très exactes d'Eterna, de Quanta ou d'Arcana. Pour les obtenir, vous pourriez avoir besoin de l'une de ces bibliothèques. Chacune diminue la quantité correspondante de manière proportionnelle.", "atm9.quest.enchant.desc.other": "Ce ne sont pas seulement les bibliothèques qui peuvent être infusées!", "atm9.quest.enchant.desc.charms": "Les charmes sont de nouvelles composantes avec Apotheosis qui vous permettent d'obtenir des effets de potion pendant beaucoup plus longtemps. Vous pouvez les infuser pour les rendre incassables. Il vous faut 50 Eterna, entre 8,5 et 13,5 Quanta, et entre 32,5 et 37,5 Arcana. Une façon de le faire est avec 5 Bibliothèques de l'End Draconique, 6 Bibliothèques de l'Enfer Lumineuse, 1 Bibliothèque l'Enfer Ardente, 1 Bibliothèque Marine Forgée par le Cœur et 2 Bibliothèques de Melons. {Au fait, j'ai utilisé n'importe quelle balise de Cha<PERSON><PERSON>, donc certains objets qui peuvent être utilisés dans la quête ne peuvent pas être rendus incassables, seuls les charmes d'Apotheosis le peuvent. )", "atm9.quest.enchant.desc.trident": "Apotheosis sait à quel point il peut être ennuyeux d'obtenir des Trident, alors ils l'ont facilité... enfin, un peu plus facile. Vous pouvez maintenant fabriquer un trident inerte et l'infuser pour obtenir un trident normal. Le trident nécessite entre 20 et 30 Eterna, entre 20% et 50% de Quanta, et au moins 35% d'Arcana. Vous pouvez obtenir cela avec 4 Bibliothèques Skulk résonnantes ou 2 Bibliothèques Marines Cristallines et 6 Bibliothèques Marine Forgée par le Cœur.", "atm9.quest.enchant.desc.library": "C'est sans doute l'un des blocs les plus importants ajoutés par Apotheosis, la bibliothèque d'enchantement. Vous placez des livres, ils s'accumulent au fil du temps et peuvent être retirés à tout moment. Attention 1. Il a des limites, des limites élevées mais des limites 2. Il ne peut extraire que le niveau le plus élevé placé, indépendamment de la quantité.", "atm9.quest.enchant.desc.alexandria": "La Bibliothèque d'Alexandrie est une meilleure bibliothèque d'enchantement. Elle peut contenir plus, c'est tout. V<PERSON> devez infuser une bibliothèque d'enchantement. Il lui faut exactement 50 Eterna, entre 45 % et 50 % de Quanta, et 100 % d'Arcana. Cela peut être fait avec 7 Bibliothèques Skulk résonnantes et 2 Bibliothèques Draconiques.", "atm9.quest.enchant.desc.infused_hellshelf": "Pour obtenir des enchantements et niveaux max plus élevés, vous aurez besoin de Bibliothèques Infusées. Pour les obtenir, vous aurez besoin de 22,5 Eterna et de 30% de Quanta. La meilleure façon de les obtenir serait avec 15 bibliothèques classiques et 5 Bibliothèques de l'Enfer.", "atm9.quest.enchant.desc.infused_seashelf": "Si vous voulez plus d'Eterna et d'Arcana, vous aurez besoin de Bibliothèques marines infusées. <PERSON><PERSON> les obtenir, vous avez besoin d'au moins 22,5 Eterna, 15% de Quanta et 10% d'Arcana. La meilleure façon de les obtenir serait avec 15 bibliothèques et 5 Bibliothèques marine. (15% Quanta est la valeur par défaut, vous n'aurez besoin d'aucune Bibliothèque de l'Enfer pour cela).", "atm9.quest.enchant.desc.sight": "Vous avez déjà passé 3 mois à étudier le Code Galactique pour comprendre enfin le langage de la Table d'enchantement juste pour que cela soit du charabia? Non? De même pour moi, cependant, les indices d'enchantement servent de véritable interprète. Chaque indice d'enchantement vous révélera un enchantement avant de l'appliquer.", "atm9.quest.enchant.desc.retification": "La Rectification est une quantité qui fonctionne avec Quanta, elle dé<PERSON>mine si les enchantements seront bons ou mauvais. Plus il y a de Rectification, meilleurs sont les enchantements. Probablement nécessaire pour ceux qui veulent de bons équipements.", "atm9.quest.enchant.desc.blazing": "La Bibliothèque de l'Enfer Flamboyante est une amélioration de la Bibliothèque de l'Enfer Infusée. Elle augmente l'Eterna max à 30. L'indice négatif d'enchantement la rend un peu moins bonne pour l'enchantement normal, mais nous pouvons l'utiliser pour une meilleure infusion.", "atm9.quest.enchant.desc.glowing": "La Bibliothèque de l'Enfer Lumineuse est une amélioration de la Bibliothèque de l'Enfer Infusée. Elle augmente également l'Eterna max, mais ce ne sont pas les statistiques que nous recherchons pour l'enchantement. Indubitablement utile pour l'enchantement ordinaire toutefois!", "atm9.quest.enchant.desc.crystalline": "La Bibliothèque Marine Cristalline est une amélioration de la Bibliothèque de l'Enfer Infusée. Elle est très bonne pour les enchantements normaux, mais ne nous donne pas assez d'Arcana pour la prochaine infusion dont nous aurons besoin. Toujours également de bonnes statistiques pour l'enchantement normal!", "atm9.quest.enchant.desc.heart-forged": "La Bibliothèque Marine Forgée par le Cœur est une autre amélioration de la Bibliothèque Marine Infusée. Elle est un peu chère mais donne l'Arcana dont nous aurons besoin pour l'infusion et plus tard l'enchantement. La Rectification négative la rend un peu moins bonne pour l'enchantement normal, car elle est importante pour obtenir de bons enchantements.", "atm9.quest.enchant.desc.deepshelf": "La Bibliothèque Profonde (non dormant) est votre prochaine étape pour l'enchantement. Comme tout le reste, il a besoin d'infusion. La Bibliothèque Profonde a besoin de 30 Eterna, 40% de Quanta et 40% d'Arcana. Vous pouvez obtenir cela avec 5 Bibliothèques Flamboyante et 4 Bibliothèques Marine Forgée par le Cœur.", "atm9.quest.enchant.desc.Soul_deep": "Comme une Bibliothèque améliorée de l'Enfer, la Bibliothèque Profonde touchée par l'Âmef donne beauco<PERSON> de Quanta, cependant elle augmente le maximum d'Eterna à 37,5! Ce qui signifie de meilleurs enchantements!", "atm9.quest.enchant.desc.Soul_sculk": "Étonnam<PERSON>, vous n'avez pas besoin d'infusion pour celles-ci! Vous devez simplement tuer le boss le plus puissant de Minecraft vanilla! Les étagères de Skulk augmentent l'Eterna à 40, ce qui nous permettra d'infuser le prochain objet.", "atm9.quest.enchant.desc.echo_deep": "Un peu cher, mais la Bibliothèque Profonde résonnante est comme une bibliothèque bien meilleure que la Bibliothèque Marine. (Au fait, vous devrez tuer pas mal de gardiens pour progresser davantage dans l'enchantement... j'aurais dû vous avertir plus tôt). Augmente également l'Eterna max à 37,5.", "atm9.quest.enchant.desc.echo_sculk": "Il se peut que vous ayez besoin d'éliminer quelques gardiens pour les obtenir, mais Apotheosis facilite la tâche! Cette bibliothèque est principalement destinée à l'Arcana, mais c'est une bonne source pour toutes les quantités. Elle sera nécessaire pour la prochaine infusion.", "atm9.quest.enchant.desc.endshelf": "Les derniers ensembles de bibliothèques dont vous aurez besoin sont les Bibliothèque de l'End, pour les obtenir, vous avez besoin de Souffle du Dragon infusé. Celle-ci s'est avérée être un défi à acquérir, cependant vous avez besoin d'au moins 40% d'Eterna, entre 15% et 25% de Quanta, et au moins 60% d'Arcana. Il doit être entre 15% et 25% de Quanta pour obtenir cela, vous pouvez essayer avec 9 Bibliothèques Skulk résonnantes et 4 Bibliothèques à Melons ou 2 Bibliothèques Skulk résonnantes et 10 Bibliothèque Marine Forgée par le Cœur.", "atm9.quest.enchant.desc.pearlescent": "La Bibliothèque de l'End Nacrée est la bibliothèque la plus polyvalente. Elle donne également un maximum d'Eterna de 45, mais ce n'est pas la bibliothèque que nous recherchons pour obtenir la configuration parfaite.", "atm9.quest.enchant.desc.draconic": "La Bibliothèque Draconique est la dernière bibliothèque dont nous aurons besoin pour une configuration parfaite. Il ne donne peut-être que de l'Eterna, mais il a un maximum d'Eterna de 50.", "atm9.quest.enchant.desc.perfect": "Vous voulez LES enchantements les plus parfaits? Alors voici la configuration dont vous avez besoin, pour 100% de tout. 7 Bibliothèques Skulk résonnantes, 4 Bibliothèque Profonde touchée par l'Âme et 5 Bibliothèque Draconique vous donneront 50 Eterna, 100% de Quanta, 100% d'Arcana et 8 indices d'enchantement. 4 étagères de Rectification infusées donneront 100% de Rectification. Et la Bibliothèque Profonde des Trésors Arcanes couronnera le tout avec des enchantements de trésor.", "item.kubejs.micro_universe_catalyst.tooltip": "Forgé dans le feu de mille soleils."}