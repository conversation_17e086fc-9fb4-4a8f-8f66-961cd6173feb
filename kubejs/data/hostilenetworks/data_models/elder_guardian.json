{"entity": "minecraft:elder_guardian", "variants": [], "name": "entity.minecraft.elder_guardian", "name_color": "#CBB1A5", "gui_scale": 0.5, "gui_x_offset": 0, "gui_y_offset": 0.5, "gui_z_offset": 0, "sim_cost": 1024, "input": {"item": "hostilenetworks:prediction_matrix"}, "base_drop": {"item": "hostilenetworks:overworld_prediction"}, "trivia": "hostilenetworks.trivia.elder_guardian", "fabricator_drops": [{"item": "minecraft:cod", "count": 16}, {"item": "minecraft:salmon", "count": 2}, {"item": "minecraft:pufferfish", "count": 2}, {"item": "minecraft:tropical_fish", "count": 2}, {"item": "minecraft:prismarine_crystals", "count": 8}, {"item": "minecraft:prismarine_shard", "count": 24}, {"item": "minecraft:wet_sponge", "count": 32}, {"item": "oceansdelight:elder_guardian_slab", "count": 8}], "data_per_kill": [3, 12, 30, 45]}