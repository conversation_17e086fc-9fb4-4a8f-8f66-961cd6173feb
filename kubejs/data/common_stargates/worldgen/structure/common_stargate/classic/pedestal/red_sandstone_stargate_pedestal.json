{"type": "sgjourney:common_stargate", "start_pool": "common_stargates:common_stargate/classic/pedestal/red_sandstone_stargate_pedestal", "size": 1, "max_distance_from_center": 80, "biomes": "#common_stargates:has_structure/common_stargate/classic/pedestal/classic_common_stargate_pedestal_badlands_biomes", "step": "strongholds", "start_height": {"absolute": 0}, "project_start_to_heightmap": "OCEAN_FLOOR_WG", "use_expansion_hack": false, "spawn_overrides": {"monster": {"bounding_box": "piece", "spawns": []}}, "terrain_adaptation": "beard_thin"}