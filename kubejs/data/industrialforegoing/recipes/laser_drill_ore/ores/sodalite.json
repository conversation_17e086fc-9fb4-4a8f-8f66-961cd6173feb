{"type": "forge:conditional", "recipes": [{"conditions": [{"type": "forge:not", "value": {"type": "forge:tag_empty", "tag": "forge:ores/sodalite"}}], "recipe": {"type": "industrialforegoing:laser_drill_ore", "catalyst": {"item": "industrialforegoing:laser_lens11"}, "output": {"item": "gtceu:deepslate_sodalite_ore"}, "pointer": 0, "rarity": [{"blacklist": {"type": "minecraft:worldgen/biome", "values": ["minecraft:the_end", "minecraft:the_void", "minecraft:small_end_islands", "minecraft:end_barrens", "minecraft:end_highlands", "minecraft:end_midlands"]}, "depth_max": 70, "depth_min": 30, "weight": 6, "whitelist": {}}, {"blacklist": {"type": "minecraft:worldgen/biome", "values": ["minecraft:the_end", "minecraft:the_void", "minecraft:small_end_islands", "minecraft:end_barrens", "minecraft:end_highlands", "minecraft:end_midlands"]}, "depth_max": 255, "depth_min": 0, "weight": 1, "whitelist": {}}]}}]}