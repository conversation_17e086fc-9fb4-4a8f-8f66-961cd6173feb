{"type": "gtceu:electrolyzer", "duration": 480, "inputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"type": "forge:nbt", "item": "productivebees:spawn_egg_configurable_bee", "nbt": "{\"EntityTag\": {\"type\": \"productivebees:apatite\"}}"}}, "chance": 10000, "tierChanceBoost": 0}]}, "outputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"type": "forge:nbt", "item": "productivebees:spawn_egg_configurable_bee", "nbt": "{\"EntityTag\": {\"type\": \"productivebees:pyrochlore\"}}"}}, "chance": 10000, "tierChanceBoost": 0}]}, "tickInputs": {"eu": [{"content": 60, "chance": 10000, "tierChanceBoost": 0}]}, "tickOutputs": {}, "conditions": [{"type": "forge:mod_loaded", "modid": "gtceu"}, {"type": "productivebees:bee_exists", "bee": "productivebees:apatite"}, {"type": "productivebees:bee_exists", "bee": "productivebees:pyrochlore"}]}