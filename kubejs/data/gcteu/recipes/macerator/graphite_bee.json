{"type": "gtceu:macerator", "duration": 200, "inputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"type": "forge:nbt", "item": "productivebees:spawn_egg_configurable_bee", "nbt": "{\"EntityTag\": {\"type\": \"productivebees:coal\"}}"}}, "chance": 10000, "tierChanceBoost": 0}]}, "outputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"type": "forge:nbt", "item": "productivebees:spawn_egg_configurable_bee", "nbt": "{\"EntityTag\": {\"type\": \"productivebees:graphite\"}}"}}, "chance": 10000, "tierChanceBoost": 0}]}, "tickInputs": {"eu": [{"content": 4, "chance": 10000, "tierChanceBoost": 0}]}, "tickOutputs": {}, "conditions": [{"type": "forge:mod_loaded", "modid": "gtceu"}, {"type": "productivebees:bee_exists", "bee": "productivebees:coal"}, {"type": "productivebees:bee_exists", "bee": "productivebees:gtceu/graphite"}]}