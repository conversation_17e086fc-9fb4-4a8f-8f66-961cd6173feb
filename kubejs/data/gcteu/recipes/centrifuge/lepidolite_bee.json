{"type": "gtceu:centrifuge", "duration": 1600, "inputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"item": "productivebees:spawn_egg_mason_bee"}}, "chance": 10000, "tierChanceBoost": 0}]}, "outputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"type": "forge:nbt", "item": "productivebees:spawn_egg_configurable_bee", "nbt": "{\"EntityTag\": {\"type\": \"productivebees:lepidolite\"}}"}}, "chance": 10000, "tierChanceBoost": 0}]}, "tickInputs": {"eu": [{"content": 7, "chance": 10000, "tierChanceBoost": 0}]}, "tickOutputs": {}, "conditions": [{"type": "forge:mod_loaded", "modid": "gtceu"}, {"type": "productivebees:bee_exists", "bee": "productivebees:lepidolite"}]}