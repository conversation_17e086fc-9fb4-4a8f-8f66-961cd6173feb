{"type": "gtceu:alloy_smelter", "duration": 100, "inputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"type": "forge:nbt", "item": "productivebees:spawn_egg_configurable_bee", "nbt": "{\"EntityTag\": {\"type\": \"productivebees:sulfur\"}}"}}, "chance": 10000, "tierChanceBoost": 0}, {"content": {"type": "gtceu:sized", "count": 3, "ingredient": {"tag": "forge:dusts/barium"}}, "chance": 10000, "tierChanceBoost": 0}]}, "outputs": {"item": [{"content": {"type": "gtceu:sized", "count": 1, "ingredient": {"type": "forge:nbt", "item": "productivebees:spawn_egg_configurable_bee", "nbt": "{\"EntityTag\": {\"type\": \"productivebees:barite\"}}"}}, "chance": 10000, "tierChanceBoost": 0}]}, "tickInputs": {"eu": [{"content": 7, "chance": 10000, "tierChanceBoost": 0}]}, "tickOutputs": {}, "conditions": [{"type": "forge:mod_loaded", "modid": "gtceu"}, {"type": "productivebees:bee_exists", "bee": "productivebees:sulfur"}, {"type": "productivebees:bee_exists", "bee": "productivebees:barite"}]}