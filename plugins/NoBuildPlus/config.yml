#############################################################################
##                                                                         ##
##                                                                         ##
##                                                                         ##
##                           No Build Plus                                 ##
##                          made by p1xEL_mc                               ##
##                                                                         ##
##                                                                         ##
##                                                                         ##
#############################################################################
## How to use the plugin
##
## worlds.yml is the worlds data file. You can change each world's flag and deny message.
## You can change the flag for world with command. However, you have to change the deny message by yourself. (Or it will use the default settings')
##
## flags.yml is a flags manager file. You can enable or disable the flag here.
## There is a type for the flag. type: all (some flags are unavailable for type: all) type: list (select the objects from the list.)
##
## Sorry for my bad english.
##
## Thanks for using my plugin
## My name on SpigotMC is p1xEL_mc

# The version of the plugin.
Version: Check plugin.yml
type: Release

# The version of configuration.
# DO NOT CHANGE THIS!!
Configuration: 4

# Language of the file.
# Languages list: en(English) zh_CN(Simplified Chinese) zh_TW(Traditional Chinese).
# It only changes the texts from language file if you change this.
Language: zh_CN

# Check update when the plugin loaded.
check-update: true

hook:
  Residence: true
  Dominion: true

# Set to true if you want to enable deny-message
deny-message-enable: true

# Two types of message sending to the player
# MESSAGE - normal message
# ACTIONBAR - actionbar message (!! Only available at 1.12 and above!!)
deny-message-type: ACTIONBAR
deny-message-sound:
  name: ENTITY_VILLAGER_NO

