Language: zh_TW
plugin-name: NoBuildPlus
commands-plugin-name: nbp
commands:
  top: '&8&m                                                   '
  plugin: '                &2&lNoBuildPlus %version%'
  space-1: '&f'
  help: '&7/%prefix% help &6查看幫助'
  list: '&7/%prefix% list &6查看啟用本插件的世界列表'
  add: '&7/%prefix% add <世界> &6將世界加至列表中'
  remove: '&7/%prefix% remove <世界> &6從列表中移除世界'
  clear: '&7/%prefix% clear &6清空列表'
  flag: '&7/%prefix% flag <世界> <規則> <true/false> &6設定世界的規則'
  flaglist: '&7/%prefix% flag list &6查看規則列表'
  setspawn: '&7/%prefix% setspawn &6設定世界的出生點'
  tp: '&7/%prefix% tp &6前往當前世界的出生點'
  open: '&7/%prefix% open <world> &6打開世界的GUI菜單'
  reload: '&7/%prefix% reload &6重載配置文件'
  space-8: '&f'
  bottom: '&8&m                                                   '
update-check:
  invalid: '無法檢查更新: '
  latest: 暫時沒有任何更新 你的版本為最新版!
  outdate: 有版本更新 請前往MineBBS或SpigotMC下載!
list-1: '&7&l%prefix% &8&l>> &7啟用本插件的世界:'
list-2: '&9%list%'
no-perm: '&7&l%prefix% &8&l>> &c你沒有權限這麼做!'
not-allow: '&7&l%prefix% &8&l>> &c&l! &7你不能在這個世界這麼做'
already-exists: '&7&l%prefix% &8&l>> &c這個世界已經在列表裡了!'
not-in-list: '&7&l%prefix% &8&l>> &c這個世界不在列表裡!'
clear-success: '&7&l%prefix% &8&l>> &a你清空了啟用本插件的世界列表!'
reload-success: '&7&l%prefix% &8&l>> &a配置文件重載成功!'
add-success: '&7&l%prefix% &8&l>> &a你將 &9%world% &a加入至列表中'
remove-success: '&7&l%prefix% &8&l>> &a你將 &9%world% &a從列表中移出'
cant-find-world: '&7&l%prefix% &8&l>> &c無法找到世界 (有可能是世界未啟用本插件)'
flags-list: '&7&l%prefix% &8&l>> &c錯誤規則, /nbp flag list 查看所有規則'
invalid-boolean: '&7&l%prefix% &8&l>> &c錯誤, 請輸入true或false代表是否開啟'
flag-set-success: '&7&l%prefix% &8&l>> &a你成功將 &9%world% &a的規則 &e%flag% &a設定為 &b%boolean%'
loc-not-set: '&7&l%prefix% &8&l>> &c你沒有為你的世界設定出生點'
loc-set-success: '&7&l%prefix% &8&l>> &a你成功為 &9%world%&a 設定了出生點!'
not-player: '&c你必須為一名玩家'
tp-success: '&7&l%prefix% &8&l>> &a你傳送至 &9%world% &a的出生點!'
name-not-allow: '&7&l%prefix% &8&l>> &c該名稱不被允許!'
wrong-arg: '&7&l%prefix% &8&l>> &c參數錯誤'
gui-not-support: '&7&l%prefix% &8&l> &c你的伺服器版本並不兼容GUI功能'

gui:
  title: '世界規則 '
  display_name: '&e%flag% &7(%bool%&7)'
  lore:
  - ''
  - '&7%description%'
  - ''
  - '&a點擊切換'
  page_next: '&a下一頁'
  page_previous: '&a上一頁'
  world: '&7規則設置菜單'
  page: '&7規則設置菜單'
  description:
    break: 玩家破壞方塊
    build: 玩家放置方塊
    use: 玩家使用功能性方塊(如工作台)
    container: 玩家開啟容器
    mob-damage: 對生物造成傷害
    mob-explode: 生物爆炸
    pvp: 玩家PVP
    shoot: 玩家射箭
    tnt: TNT爆炸
    tnt-damage: TNT爆炸傷害
    frame: 與展示框互動
    bed: 與床互動
    voidtp: 墜入虛空時傳送
    villager: 與村民互動
    command: 輸入指令
    chat: 玩家聊天
    leaf-decay: 樹葉消失
    melt: 冰塊融化
    fall-damage: 跌倒傷害
    armorstand: 與盔甲架互動
    farmbreak: 耕地被踩踏
    ride: 騎馬
    painting: 與畫互動
    bucket-place: 放置桶子
    bucket-fill: 填充桶
    boat: 與船互動
    button: 玩家使用按鈕
    door-interact: 玩家使用門
    lever: 玩家使用拉桿
    trapdoor-interact: 玩家使用活板門
    fencegate-interact: 玩家使用柵欄門
    drop-item: 玩家丟棄物品
    egg-throw: 玩家丟雞蛋
    snowball-throw: 玩家丟雪球
    水-spread: 水擴散
    lava-spread: 岩漿擴散
    fly: 玩家飛行
    teleport: 玩家傳送
    mob-spawn: 生物生成
    minecart: 與礦車互動
    item-pickup: 玩家拾取物品
    potion: 玩家使用藥水
    bonemeal: 玩家使用骨粉
    elytra: 玩家使用鞘翅
    nether: 地獄門的創建
    coral-decay: 珊瑚失活
    fire-spawn: 火焰生成
    sign-change: 修改告示牌
    dye: 羊毛染色
    piston: 活塞觸發
    ice-form: 冰的形成
    hook: 使用魚竿鈎住玩家
    fish: 釣魚
    crystal: 與終界水晶互動
    flower-pot: ' 與花盆互動'
    books-interact: 與浮雕書櫃互動
    hunger: 使玩家感受飢餓
    berries: 采集螢光莓
    craft: 玩家合成
    heal: 玩家治療
    turtle-egg: 海龜蛋踩踏
    lightning: 閃電

'true': '&a允許'
'false': '&c禁止'
