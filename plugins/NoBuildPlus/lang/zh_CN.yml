Language: zh_CN
plugin-name: NoBuildPlus
commands-plugin-name: nbp
commands:
  top: '&8&m                                                   '
  plugin: '                &2&lNoBuildPlus %version%'
  space-1: '&f'
  help: '&7/%prefix% help &6查看帮助'
  list: '&7/%prefix% list &6查看启用本插件的世界列表'
  add: '&7/%prefix% add <世界> &6将世界加至列表中'
  remove: '&7/%prefix% remove <世界> &6从列表中移除世界'
  clear: '&7/%prefix% clear &6清空列表'
  flag: '&7/%prefix% flag <世界> <规则> <true/false> &6设置世界的规则'
  flaglist: '&7/%prefix% flag list &6查看规则列表'
  setspawn: '&7/%prefix% setspawn &6设置世界的出生点'
  tp: '&7/%prefix% tp &6前往当前世界的出生点'
  open: '&7/%prefix% open <world> &6打开世界的GUI菜单'
  reload: '&7/%prefix% reload &6重载配置文件'
  space-8: '&f'
  bottom: '&8&m                                                   '
update-check:
  invalid: '无法检查更新: '
  latest: 暂时没有任何更新 你的版本为最新版!
  outdate: 有版本更新 请前往MineBBS或SpigotMC下载!
list-1: '&6枫影轻语 &7>> &7启用本插件的世界:'
list-2: '&9%list%'
no-perm: '&6枫影轻语 &7>> &c你没有权限这么做!'
not-allow: '&6枫影轻语 &7>> &c&l! &7你不能在这个世界这么做'
already-exists: '&6枫影轻语 &7>> &c这个世界已经在列表里了!'
not-in-list: '&6枫影轻语 &7>> &c这个世界不在列表里!'
clear-success: '&6枫影轻语 &7>> &a你清空了启用本插件的世界列表!'
reload-success: '&6枫影轻语 &7>> &a配置文件重载成功!'
add-success: '&6枫影轻语 &7>> &a你将 &9%world% &a加入至列表中'
remove-success: '&6枫影轻语 &7>> &a你将 &9%world% &a从列表中移出'
cant-find-world: '&6枫影轻语 &7>> &c无法找到世界 (有可能是世界未启用本插件)'
flags-list: '&6枫影轻语 &7>>   &c错误规则, /nbp flag list 查看所有规则'
invalid-boolean: '&6枫影轻语 &7>> &c错误, 请输入true或false代表是否开启'
flag-set-success: '&6枫影轻语 &7>> &a你成功将 &9%world% &a的规则 &e%flag% &a设置为 &b%boolean%'
loc-not-set: '&6枫影轻语 &7>> &c你没有为你的世界设置出生点'
loc-set-success: '&6枫影轻语 &7>> &a你成功为 &9%world%&a 设置了出生点!'
not-player: '&c你必须为一名玩家'
tp-success: '&6枫影轻语 &7>> &a你传送至 &9%world% &a的出生点!'
name-not-allow: '&6枫影轻语 &7>> &c该名称不被允许!'
wrong-arg: '&6枫影轻语 &7>  &c参数错误'
gui-not-support: '&7&l%prefix% &8&l> &c你的服务器版本并不支持GUI功能'

gui:
  title: '世界规则 '
  display_name: '&e%flag% &7(%bool%&7)'
  lore:
  - ''
  - '&7%description%'
  - ''
  - '&a点击切换'
  page_next: '&a下一页'
  page_previous: '&a上一页'
  world: '&7规则设置菜单'
  page: '&7规则设置菜单'
  description:
    break: 玩家破坏方块
    build: 玩家放置方块
    use: 玩家使用功能性方块(如工作台)
    container: 玩家开启容器
    mob-damage: 对生物造成伤害
    mob-explode: 生物爆炸
    pvp: 玩家PVP
    shoot: 玩家射箭
    tnt: TNT爆炸
    tnt-damage: TNT爆炸伤害
    frame: 与展示框互动
    bed: 与床互动
    voidtp: 坠入虚空时传送
    villager: 与村民互动
    command: 输入指令
    chat: 玩家聊天
    leaf-decay: 树叶消失
    melt: 冰块融化
    fall-damage: 摔落伤害
    armorstand: 与盔甲架互动
    farmbreak: 耕地被踩踏
    ride: 骑马
    painting: 与画互动
    bucket-place: 放置桶
    bucket-fill: 填充桶
    boat: 与船互动
    button: 玩家使用按钮
    door-interact: 玩家使用门
    lever: 玩家使用拉杆
    trapdoor-interact: 玩家使用活板门
    fencegate-interact: 玩家使用栅栏门
    drop-item: 玩家丢弃物品
    egg-throw: 玩家丢鸡蛋
    snowball-throw: 玩家丢雪球
    water-spread: 水扩散
    lava-spread: 岩浆扩散
    fly: 玩家飞行
    teleport: 玩家传送
    mob-spawn: 生物生成
    minecart: 与矿车互动
    item-pickup: 玩家拾取物品
    potion: 玩家使用药水
    bonemeal: 玩家使用骨粉
    elytra: 玩家使用鞘翅
    nether: 地狱门的创建
    coral-decay: 珊瑚失活
    fire-spawn: 火焰生成
    sign-edit: 修改告示牌
    dye: 羊毛染色
    piston: 活塞触发
    ice-form: 冰的形成
    hook: 使用鱼竿钩住玩家
    fish: 钓鱼
    crystal: 与末地水晶互动
    flower-pot: ' 与花盆互动'
    books-interact: 与雕纹书架互动
    hunger: 使玩家感受饥饿
    berries: 采集发光浆果
    craft: 玩家合成
    heal: 玩家治疗
    turtle-egg: 海龟蛋踩踏
    lightning: 闪电

'true': '&a允许'
'false': '&c禁止'
