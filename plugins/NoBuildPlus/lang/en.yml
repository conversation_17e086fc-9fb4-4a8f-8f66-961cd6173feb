Language: en
plugin-name: NoBuildPlus
commands-plugin-name: nbp
commands:
  top: '&8&m                                                   '
  plugin: '                &2&lNoBuildPlus %version%'
  space-1: '&f'
  help: '&7/%prefix% help &6View help.'
  list: '&7/%prefix% list &6List of the enable worlds.'
  add: '&7/%prefix% add <world> &6Add world name to list.'
  remove: '&7/%prefix% remove <world> &6Remove world name from list.'
  clear: '&7/%prefix% clear &6Clear worlds from list.'
  flag: '&7/%prefix% flag <world> <flag> <true/false> &6Set world''s flag.'
  flaglist: '&7/%prefix% flag list &6View flags list.'
  setspawn: '&7/%prefix% setspawn &6Set the spawn location of current world.'
  tp: '&7/%prefix% tp &6Teleport to the spawn of current world.'
  open: '&7/%prefix% open <world> &6Open GUI of world.'
  reload: '&7/%prefix% reload &6Reload the config.'
  space-8: '&f'
  bottom: '&8&m                                                   '
update-check:
  invalid: 'Unable to check for updates: '
  latest: There is not a new update available.
  outdate: There is a new update available.
list-1: '&7&l%prefix% &8&l>> &7Enable worlds:'
list-2: '&9%list%'
no-perm: '&7&l%prefix% &8&l>> &cYou do not have permission to do that!'
not-allow: '&7&l%prefix% &8&l>> &c&l! &7You cannot do that here.'
already-exists: '&7&l%prefix% &8&l>> &cThe world is already exists!'
not-in-list: '&7&l%prefix% &8&l>> &cThe world is not in the list!'
clear-success: '&7&l%prefix% &8&l>> &aYou cleared the enable worlds list!'
reload-success: '&7&l%prefix% &8&l>> &aConfiguration reloaded successful!'
add-success: '&7&l%prefix% &8&l>> &aYou added &9%world% &ato the list.'
remove-success: '&7&l%prefix% &8&l>> &aYou removed &9%world% &afrom the list.'
cant-find-world: '&7&l%prefix% &8&l>> &cCan''t find world. (Or the world is disabled)'
flags-list: '&7&l%prefix% &8&l>> &cInvalid flag. Check /nbp flag list To view flags.'
invalid-boolean: '&7&l%prefix% &8&l>> &cInvalid, please type true or false for the
  flag'
flag-set-success: '&7&l%prefix% &8&l>> &aYou set &9%world% &a''s flag &e%flag% &ato
  &b%boolean%&a.'
loc-not-set: '&7&l%prefix% &8&l>> &cYou didn''t set the spawn location for your world!'
loc-set-success: '&7&l%prefix% &8&l>> &aYou set the spawn location for &9%world%&a
  !'
not-player: '&cYou must be a player to do that.'
tp-success: '&7&l%prefix% &8&l>> &aYou teleport to spawn of &9%world%&a.'
name-not-allow: '&7&l%prefix% &8&l>> &cThis name is not allowed!'
wrong-arg: '&7&l%prefix% &8&l>> &cWrong argument!'
gui-not-support: '&7&l%prefix% &8&l> &cYour server version does not support GUI feature.'

gui:
  title: World flags of
  display_name: '&e%flag% &7(%bool%&7)'
  lore:
  - ''
  - '&7%description%'
  - ''
  - '&aClick to switch'
  page_next: '&aNext Page'
  page_previous: '&aPrevious Page'
  world: '&7Flags Setting Menu'
  page: '&7Flags Setting Menu'
  description:
    break: Block destroying
    build: Block place
    use: Block using
    container: Using the containers
    mob-damage: Damage taking to the bob
    mob-explode: Explosion caused by the mob
    pvp: Taking damage with other players
    shoot: Player shooting arrow
    tnt: TNT explosion
    tnt-damage: TNT explosion damage
    frame: Frame interaction
    bed: Interaction with bed
    voidtp: Teleporting to the spawn location when falling into the void
    villager: interaction with villager
    command: Command executing
    chat: Chatting
    leaf-decay: Leaf decaying
    melt: Block Melting
    fall-damage: Fall damage
    armorstand: Armor stand interaction
    farmbreak: Farm stepping
    ride: Riding on entity
    painting: Painting Destroying
    bucket-place: Liquid bucket placing
    bucket-fill: Bucket filling liquid
    boat: Boat interacting
    button: Button interacting
    door-interact: Door interacting
    lever: Lever interacting
    trapdoor-interact: Trapdoor interacting
    fencegate-interact: Fence gate interacting
    drop-item: Item dropping by player
    egg-throw: Egg throwing
    snowball-throw: Snowball throwing
    water-spread: Water spreading
    lava-spread: Lava spreading
    fly: Player flying
    teleport: Player teleporting
    mob-spawn: Mob spawning
    minecart: Minecart placing
    item-pickup: Item picking up by player
    potion: Potion throwing and drinking by player
    bonemeal: Bone meal using on blocks
    elytra: Gliding from using elytra
    nether: Nether portal creation
    coral-decay: Coral decaying
    fire-spawn: Fire spawning
    sign-edit: Sign text editing
    dye: Sheep wool dyeing
    piston: Piston triggering
    ice-form: Ice formation
    hook: Hooking player with fishing rod
    fish: Fishing
    crystal: Ender crystal interacting
    flower-pot: Flower pot interacting
    books-interact: Chiseled bookshelf interacting
    hunger: Player being hungry
    berries: Harvesting Glow Berries
    craft: Player crafting
    heal: Player healing
    turtle-egg: Turtle egg stepping
    lightning: Lightning

'true': '&aALLOW'
'false': '&cPREVENT'
