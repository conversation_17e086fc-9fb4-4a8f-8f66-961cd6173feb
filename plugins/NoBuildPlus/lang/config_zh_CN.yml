#
# 如果你需要使用本配置文件作为主要的 config.yml
# 请将本文件命名为 config.yml
# 一切更新以原本的 config.yml 为准
# 此文件上次更新于 v1.4.9
#
#############################################################################
##                                                                         ##
##                                                                         ##
##                                                                         ##
##                           No Build Plus                                 ##
##                          由 p1xEL_mc 制作                                ##
##                                                                         ##
##                                                                         ##
##                                                                         ##
#############################################################################
## 如何使用该插件
##
## worlds.yml 储存了每个使用本插件的世界, 你可以在里面更改每个世界的规则设定及否定消息显示。
## 你也可以通过指令 /nbp flag 更改, 否定消息显示需自行在 worlds.yml 中更改。
##
## flags.yml 是规则管理文件, 你可以直接在此文件中开关规则, 关闭某规则后, 无论worlds.yml内该规则是true还是false，插件都不会检测。
## 某些规则拥有数据类型，比如 list (列表) 及 all (所有), 在list的规则数据中, 你需要写入会被检测的物品/生物ID。
##
## 感谢使用本插件
## SpigotMC ID: p1xEL_mc
## MCBBS ID: Ez4p1xEL

# 插件版本 请勿更改
Version: Check plugin.yml
type: Release

# 配置文件版本
# 请勿更改
Configuration: 4

# 消息显示语言
# 语言列表: en(英文) zh_CN(简体中文) zh_TW(繁体中文).
Language: en

# 更新提示
check-update: true

hook:
  Residence: true
  Dominion: true

# 设置为 false 如果你想关闭否定消息显示（或直接在worlds.yml删除deny-message项)
deny-message-enable: true

# 两种发送给玩家的消息类型
# MESSAGE - 普通消息
# ACTIONBAR - 物品栏上方的消息 (!! 仅支持1.12及以上!!)
deny-message-type: ACTIONBAR
