{"citizens.changed-implementation": "Citizens 实现已更改，正在禁用插件", "citizens.commands.citizens.description": "显示插件的基本信息", "citizens.commands.citizens.reload.description": "在不保存现有数据的情况下从磁盘重新加载 Citizens", "citizens.commands.citizens.save.description": "保存 NPC", "citizens.commands.citizens.save.help": "使用 -a 标记进行异步保存.", "citizens.commands.console-error": "请查看控制台信息并报告此错误.", "citizens.commands.errors.missing-world": "没有找到世界.", "citizens.commands.errors.unknown-registry": "未知的 NPC 注册表 [[{0}]].", "citizens.commands.help.command-missing": "命令 /{0} 不存在.", "citizens.commands.help.header": "帮助", "citizens.commands.id-not-found": "找不到任何 ID 为 [[{0}]] 的 NPC .", "citizens.commands.invalid.class": "无效的外部命令类.", "citizens.commands.invalid-mobtype": "[[{0}]] 不是有效的生物类型.", "citizens.commands.invalid-number": "这不是有效数字.", "citizens.commands.npc.activationrange.description": "设置激活范围", "citizens.commands.npc.activationrange.set": "激活范围设置为 [[{0}]].", "citizens.commands.npc.age.cannot-be-aged": "这种生物类型 {0} 不能设置年龄.", "citizens.commands.npc.age.description": "设置 NPC 的年龄", "citizens.commands.npc.age.help": "仅可用于有婴儿变体的实体. 使用 [[-l]] 标记来锁定年龄(注意: 可能需要重新登录才能看到效果).", "citizens.commands.npc.age.invalid-age": "无效年龄. 有效的年龄可以是 adult(成年), baby(幼年) 或者从 -24000 到 0 的数字.", "citizens.commands.npc.age.locked": "年龄已锁定.", "citizens.commands.npc.age.set": "[[{0}]] 年龄已设定为 [[{1}]].", "citizens.commands.npc.age.set-adult": "[[{0}]] 年龄已设定为成年.", "citizens.commands.npc.age.set-baby": "[[{0}]] 年龄已设定为幼年.", "citizens.commands.npc.age.set-normal": "[[{0}]] 年龄已设定为 [[{1}]].", "citizens.commands.npc.age.unlocked": "年龄已解锁.", "citizens.commands.npc.aggressive.description": "设置实体是否主动攻击", "citizens.commands.npc.ai.description": "设置 NPC 是否要使用原版 AI", "citizens.commands.npc.ai.started": "现在 NPC 使用 Minecraft 原版 AI.", "citizens.commands.npc.ai.stopped": "现在 NPC 不再使用 Minecraft 原版 AI.", "citizens.commands.npc.allay.dancing-set": "[[{0}]] 正在跳舞.", "citizens.commands.npc.allay.dancing-unset": "[[{0}]] 停止跳舞.", "citizens.commands.npc.allay.description": "设置悦灵的参数", "citizens.commands.npc.anchor.added": "已添加锚点.", "citizens.commands.npc.anchor.already-exists": "锚点 [[{0}]] 已经存在.", "citizens.commands.npc.anchor.description": "管理 NPC 的锚点坐标", "citizens.commands.npc.anchor.invalid-name": "无效的锚点名称.", "citizens.commands.npc.anchor.missing": "锚点 [[{0}]] 不存在.", "citizens.commands.npc.anchor.removed": "锚点已删除.", "citizens.commands.npc.armadillo.state-set": "状态设置为 [[{0}]]", "citizens.commands.npc.armorstand.description": "修改盔甲架属性", "citizens.commands.npc.attribute.description": "设置各种 NPC 属性", "citizens.commands.npc.attribute.not-found": "[[{0}]] 是无效的属性.", "citizens.commands.npc.attribute.reset": "属性 [[{0}]] 重置为默认值.", "citizens.commands.npc.attribute.set": "属性 [[{0}]] 的基础值设置为 [[{1}]].", "citizens.commands.npc.axolotl.description": "设置美西螈的参数", "citizens.commands.npc.axolotl.invalid-variant": "无效的变种类型. 有效的变种类型有 [[{0}]].", "citizens.commands.npc.axolotl.playing-dead": "[[{0}]] 现在伪装为死亡.", "citizens.commands.npc.axolotl.playing-dead-stopped": "[[{0}]] 不再伪装为死亡.", "citizens.commands.npc.axolotl.variant-set": "变种类型设置为 [[{0}]].", "citizens.commands.npc.bat.awake-set": "[[{0}]] 现在清醒.", "citizens.commands.npc.bat.awake-unset": "[[{0}]] 现在不再清醒.", "citizens.commands.npc.bat.description": "设置蝙蝠的参数", "citizens.commands.npc.bee.anger-set": "愤怒 设置为 [[{0}]].", "citizens.commands.npc.bee.bee-stung": "[[{0}]] 现在会蜇刺.", "citizens.commands.npc.bee.description": "设置蜜蜂的参数", "citizens.commands.npc.bee.has-nectar": "[[{0}]] 有花蜜.", "citizens.commands.npc.bee.invalid-anger": "愤怒值应该大于 0 .", "citizens.commands.npc.bee.no-nectar": "[[{0}]] 没有花蜜.", "citizens.commands.npc.bee.not-stung": "[[{0}]] 不再会蜇刺.", "citizens.commands.npc.boat.description": "设置船的参数", "citizens.commands.npc.boat.type-set": "船的种类设置为 [[{0}]].", "citizens.commands.npc.bossbar.description": "修改 Boss 栏的属性", "citizens.commands.npc.camel.description": "设置骆驼的参数", "citizens.commands.npc.camel.pose-set": "姿势设置为 [[{0}]].", "citizens.commands.npc.cat.collar-color-set": "猫的颜色设置为 [[{0}]].", "citizens.commands.npc.cat.description": "设置猫的参数", "citizens.commands.npc.cat.invalid-collar-color": "指定的猫颜色无效. 有效类型为 [[{0}]].", "citizens.commands.npc.cat.invalid-type": "指定的类型无效. 有效类型为 [[{0}]].", "citizens.commands.npc.cat.lying-start": "[[{0}]] 躺下了.", "citizens.commands.npc.cat.lying-stop": "[[{0}]] 起来了.", "citizens.commands.npc.cat.sitting-start": "[[{0}]] 坐下了.", "citizens.commands.npc.cat.sitting-stop": "[[{0}]] 起来了.", "citizens.commands.npc.cat.type-set": "类型设置为 [[{0}]].", "citizens.commands.npc.chicken.description": "管理鸡的参数", "citizens.commands.npc.chunkload.description": "切换 NPC 是否使其所在周边区块保持加载", "citizens.commands.npc.chunkload.set": "[[{0}]] 现在将会使其所在周边区块保持加载.", "citizens.commands.npc.chunkload.unset": "[[{0}]] 现在不再会使其所在周边区块保持加载.", "citizens.commands.npc.collidable.description": "切换 NPC 是否可以产生碰撞", "citizens.commands.npc.collidable.fluid-set": "[[{0}]] 现在可以被流体推动.", "citizens.commands.npc.collidable.fluid-unset": "[[{0}]] 现在不再可以被流体推动.", "citizens.commands.npc.collidable.set": "[[{0}]] 将能够与实体碰撞.", "citizens.commands.npc.collidable.unset": "[[{0}]] 将不再与实体碰撞.", "citizens.commands.npc.command.all-errors-cleared": "[[{0}]] 清除了所有的 [[{1}]] 个错误.", "citizens.commands.npc.command.all-players-forgotten": "[[{0}]] 忘记了所有的玩家命令历史.", "citizens.commands.npc.command.cleared": "[[{0}]] 的命令已清空.", "citizens.commands.npc.command.command-added": "添加了命令 [[{0}]], ID是 [[{1}]].", "citizens.commands.npc.command.command-removed": "删除了命令 [[{0}]].", "citizens.commands.npc.command.cost-missing": "未设置开销.", "citizens.commands.npc.command.cost-set": "设置每次点击的开销为 [[{0}]].", "citizens.commands.npc.command.cycle-set": "[[{0}]] 现在会在玩家点击时循环切换命令", "citizens.commands.npc.command.cycle-unset": "[[{0}]] 不再会在玩家点击时循环切换命令", "citizens.commands.npc.command.describe-format": "<br> - {0} [{1}s] [cost:{2}] [exp:{3}] [<click:run_command:/npc cmd remove {4}><hover:show_text:移除这条命令><red><u>-</hover></click>]", "citizens.commands.npc.command.description": "控制 NPC 被点击时将会运行的命令", "citizens.commands.npc.command.errors-cleared": "[[{0}]] 的错误已清空", "citizens.commands.npc.command.experience-cost-set": "设置单次点击消耗的经验等级为 [[{0}]].", "citizens.commands.npc.command.help": "<br>使用 [[-l]] 标记使命令在左键单击时运行，[[-r]] 在右键时运行（默认）。<br>[[执行]]已分配的当玩家点击 NPC 时会运行的命令。<br>使用 [[--cooldown]] 设置每个玩家的冷却时间（以 [[秒]]） 为单位。<br>使用 [[--gcooldown]] 设置服务器范围的冷却时间（以秒为单位）。<br>[[--delay]] 将在执行命令之前等待 [[ticks]] 中指定的量。<br>[[--permissions]] 会将命令设置为需要特定权限（用逗号分隔多个）。<br>[[--n]] 将只允许玩家运行该次数的命令。<br>使用 [[-o]] 使命令临时地以管理员权限执行，使用 [[-p]] 以点击玩家而不是服务器的身份运行命令。<br>若要授予玩家临时权限而不是管理员权限，请使用 [[/npc command permissions]]。<br>使用 [[/npc command cost/expcost/itemcost]] 设置每次点击的成本。<br>命令可以一个一个地执行，而不是通过使用 [[/npc command sequential]] 或 [[/npc command cycle]] 一次执行。", "citizens.commands.npc.command.hide-error-messages-set": "现在将会隐藏错误消息.", "citizens.commands.npc.command.hide-error-messages-unset": "现在不会隐藏错误消息.", "citizens.commands.npc.command.individual-cost-set": "设置 ID 为 [[{1}]] 的命令每次点击的消耗为 [[{0}]].", "citizens.commands.npc.command.individual-experience-cost-set": "设置 ID 为 [[{1}]] 的命令每次点击消耗的经验等级为 [[{0}]].", "citizens.commands.npc.command.invalid-error-message": "无效的错误消息. 有效的消息有 [[{0}]].", "citizens.commands.npc.command.invalid-hand": "无效的手 [[{0}]], 有效值为: [[{1}]].", "citizens.commands.npc.command.invalid-player": "[[{0}]] 无法作为玩家.", "citizens.commands.npc.command.left-hand-header": "[[左键点击]] 执行命令:", "citizens.commands.npc.command.none-added": "尚未添加任何命令.", "citizens.commands.npc.command.persist-sequence-set": "命令序列将会被保存.", "citizens.commands.npc.command.persist-sequence-unset": "命令序列不再会被保存.", "citizens.commands.npc.command.player-forgotten": "忘记了 [[{0}]] 的玩家命令历史记录.", "citizens.commands.npc.command.right-hand-header": "[[右键点击]] 执行命令:", "citizens.commands.npc.commands.random-set": "命令将会随机执行.", "citizens.commands.npc.commands.random-unset": "命令不再会随机执行.", "citizens.commands.npc.commands.sequential-set": "命令将会按顺序执行.", "citizens.commands.npc.commands.sequential-unset": "命令不再会按顺序执行.", "citizens.commands.npc.command.temporary-permissions-set": "临时权限设置为 [[{0}]] ，有效时长为 [[{1}}] 个游戏刻.", "citizens.commands.npc.command.unknown-id": "此NPC的命令ID [[{0}]] 未知.", "citizens.commands.npc.configgui.description": "显示 NPC 配置 GUI", "citizens.commands.npc.controllable.controls-set": "控制器设置为 [[{0}]].", "citizens.commands.npc.controllable.description": "切换 NPC 是否能被骑乘和控制", "citizens.commands.npc.controllable.not-controllable": "[[{0}]] 不能控制.", "citizens.commands.npc.controllable.removed": "[[{0}]] 不再受控制.", "citizens.commands.npc.controllable.set": "[[{0}]] 现在可以控制.", "citizens.commands.npc.copier.description": "切换到 NPC 复制机.", "citizens.commands.npc.copy.copied": "[[{0}]] 已复制.", "citizens.commands.npc.copy.description": "复制 NPC", "citizens.commands.npc.cow.description": "管理牛的参数", "citizens.commands.npc.create.description": "创建一个新的 NPC", "citizens.commands.npc.create.invalid-location": "无法解析或没有找到出生点.", "citizens.commands.npc.create.invalid-mobtype": "[[{0}]] 不是有效的生物类型.", "citizens.commands.npc.create.mobtype-missing": "[[{0}]] 不是支持的 NPC 生物类型.", "citizens.commands.npc.create.no-player-for-spawn": "找不到用来生成NPC的玩家名.", "citizens.commands.npc.create.npc-name-too-long": "NPC 名称不能超过 16 个字符, 这个名称已被截断.", "citizens.commands.npc.debug.description": "显示调试信息", "citizens.commands.npc.description": "显示 NPC 的基本信息", "citizens.commands.npc.deselect": "已取消选择 NPC.", "citizens.commands.npc.deselect.description": "取消选择当前已选择的 NPC", "citizens.commands.npc.despawn.description": "取消生成 NPC", "citizens.commands.npc.despawn.despawned": "[[{0}]] 被取消生成.", "citizens.commands.npc.display.description": "设置展示实体的各种参数", "citizens.commands.npc.drops.description": "修改 NPC 的掉落物.", "citizens.commands.npc.endercrystal.description": "修改末影水晶的参数", "citizens.commands.npc.endercrystal.not-showing-bottom": "[[{0}]] 不再显示它的底座.", "citizens.commands.npc.endercrystal.showing-bottom": "[[{0}]] 现在显示它的底座.", "citizens.commands.npc.enderdragon.description": "设置末影龙的参数", "citizens.commands.npc.enderman.angry-set": "[[{0}]] 现在是生气的.", "citizens.commands.npc.enderman.angry-unset": "[[{0}]] 现在不再是生气的.", "citizens.commands.npc.enderman.description": "设置末影人的参数", "citizens.commands.npc.entitypose.description": "控制实体姿势", "citizens.commands.npc.entitypose.set": "设置实体姿势为 [[{0}]]", "citizens.commands.npc.equip.description": "打开装备编辑器", "citizens.commands.npc.flyable.description": "切换或设置 NPC 是否可以飞行", "citizens.commands.npc.flyable.set": "[[{0}]] 现在可以飞行.", "citizens.commands.npc.flyable.unset": "[[{0}]] 不能飞行了.", "citizens.commands.npc.follow.description": "切换 NPC 是否跟随你", "citizens.commands.npc.follow.margin-set": "[[{0}]] 将与目标保持 [[{1}]] 格距离.", "citizens.commands.npc.follow.set": "[[{0}]] 正在跟随 [[{1}]].", "citizens.commands.npc.follow.unset": "[[{0}]] 已停止跟随.", "citizens.commands.npc.forcefield.describe": "[[{0}]] 具有一个高度为 [[{1}]], 宽度为 [[{2}]], 强度为 [[{3}]] 的力场.", "citizens.commands.npc.forcefield.description": "创建一个力场，将靠近 NPC 的玩家推开", "citizens.commands.npc.forcefield.height-set": "力场高度设置为 [[{0}]] 格方块.", "citizens.commands.npc.forcefield.strength-set": "力场强度设置为每刻 [[{0}]] 格方块。默认为每刻 0.1 格方块.", "citizens.commands.npc.forcefield.vertical-strength-set": "力场垂直强度设置为每刻 [[{0}]] 格方块。默认为每刻 0 格方块。", "citizens.commands.npc.forcefield.width-set": "力场宽度设置为 [[{0}]] 格方块.", "citizens.commands.npc.fox.crouching-set": "[[{0}]] 蹲下了.", "citizens.commands.npc.fox.crouching-unset": "[[{0}]] 不再蹲下.", "citizens.commands.npc.fox.description": "设置狐狸的参数", "citizens.commands.npc.fox.faceplanted-set": "[[{0}]] 现在面部向地.", "citizens.commands.npc.fox.faceplanted-unset": "[[{0}]] 不再面部向地.", "citizens.commands.npc.fox.fox-type-set": "类型设置为 [[{0}]].", "citizens.commands.npc.fox.interested-set": "[[{0}]] 现在表现出感兴趣.", "citizens.commands.npc.fox.interested-unset": "[[{0}]] 不再表现出感兴趣.", "citizens.commands.npc.fox.invalid-fox-type": "指定的狐狸类型无效, 有效的类型为 [[{0}]].", "citizens.commands.npc.fox.pouncing-set": "[[{0}]] 正在猛扑.", "citizens.commands.npc.fox.pouncing-unset": "[[{0}]] 停止猛扑.", "citizens.commands.npc.fox.sitting-set": "[[{0}]] 坐下了.", "citizens.commands.npc.fox.sitting-unset": "[[{0}]] 不再坐下.", "citizens.commands.npc.fox.sleeping-set": "[[{0}]] 现在在睡觉.", "citizens.commands.npc.fox.sleeping-unset": "[[{0}]] 醒了.", "citizens.commands.npc.frog.description": "设置青蛙的参数", "citizens.commands.npc.frog.invalid-variant": "无效的变种类型. 有效的变种类型有 [[{0}]].", "citizens.commands.npc.frog.variant-set": "变种类型设置为 [[{0}]].", "citizens.commands.npc.gamemode.describe": "{0} 的游戏模式为 [[{1}]].", "citizens.commands.npc.gamemode.description": "改变游戏模式", "citizens.commands.npc.gamemode.invalid": "{0} 不是有效的游戏模式.", "citizens.commands.npc.gamemode.set": "游戏模式设置为 [[{0}]].", "citizens.commands.npc.glowing.color-set": "[[{0}]] 的高亮颜色已设置为 {1}]].", "citizens.commands.npc.glowing.description": "修改 NPC 的高亮状态", "citizens.commands.npc.glowing.player-only": "只能为玩家类型的NPC更改发光颜色.", "citizens.commands.npc.glowing.set": "[[{0}]] 已高亮.", "citizens.commands.npc.glowing.unset": "[[{0}]] 不再高亮.", "citizens.commands.npc.goat.description": "设置山羊的参数", "citizens.commands.npc.goat.horns-set": "[[{0}]] 的号角设置为 左：[[{1}]] 右：[[{2}]]", "citizens.commands.npc.gravity.description": "切换是否应用重力", "citizens.commands.npc.gravity.disabled": "重力 [[已禁用]].", "citizens.commands.npc.gravity.enabled": "重力 [[已启用]].", "citizens.commands.npc.guardian.elder-set": "[[{0}]] 现在是远古守卫者.", "citizens.commands.npc.guardian.elder-unset": "[[{0}]] 不再是远古守卫者.", "citizens.commands.npc.hitbox.description": "设置 NPC 的判定箱", "citizens.commands.npc.hitbox.set": "判定箱设置为 [[{0}]].", "citizens.commands.npc.hologram.background-color-set": "第 [[{0}]] 行的背景颜色设置为 [[{1}]].", "citizens.commands.npc.hologram.cleared": "悬浮文字已清除.", "citizens.commands.npc.hologram.default-background-color-set": "默认背景颜色设置为 [[{0}]].", "citizens.commands.npc.hologram.default-shadow-set": "[[{0}]] 的悬浮文字现在默认会显示阴影.", "citizens.commands.npc.hologram.default-shadow-unset": "[[{0}]] 的悬浮文字不再会默认显示阴影.", "citizens.commands.npc.hologram.description": "控制 NPC 悬浮文字", "citizens.commands.npc.hologram.direction-set": "方向设置为 [[{0}]].", "citizens.commands.npc.hologram.invalid-text-id": "无效行号.", "citizens.commands.npc.hologram.line-add": "添加了新的一行悬浮文字: [[{0}]].", "citizens.commands.npc.hologram.line-height-set": "行高设置为 [[{0}]].", "citizens.commands.npc.hologram.line-removed": "移除了行 [[{0}]].", "citizens.commands.npc.hologram.margin-missing": "缺少要设置的边距.", "citizens.commands.npc.hologram.margin-set": "将第 [[{0}]] 行悬浮文字的边距从 [[{1}]] 设置为 [[{2}]]。", "citizens.commands.npc.hologram.shadow-set": "第 [[{0}]] 行文字现在有阴影.", "citizens.commands.npc.hologram.shadow-unset": "第 [[{0}]] 行文字现在不再有阴影.", "citizens.commands.npc.hologram.text-describe-header": "[[{0}]] 的悬浮文字 (从下至上排序):", "citizens.commands.npc.hologram.text-missing": "缺少要添加的文本.", "citizens.commands.npc.hologram.text-removed": "移除了悬浮文字.", "citizens.commands.npc.hologram.text-set": "设置悬浮文字行 [[{0}]] 为 [[{1}]].", "citizens.commands.npc.hologram.view-range-set": "[[{0}]] 的悬浮文字视距设置为 [[{1}]] 格方块.", "citizens.commands.npc.home.delay-set": "返回家前的延迟设置为 [[{0}]] tick.", "citizens.commands.npc.home.description": "控制家的位置", "citizens.commands.npc.home.distance-set": "返回家前的距离设置为 [[{0}]] 格方块.", "citizens.commands.npc.home.home-set": "家设置为 [[{0}]].", "citizens.commands.npc.home.pathfind-set": "[[{0}]] 将会尝试寻找回家的路.", "citizens.commands.npc.home.teleport-set": "[[{0}]] 将会传送到家.", "citizens.commands.npc.horse.chest-set": "这匹马现在托着箱子.", "citizens.commands.npc.horse.chest-unset": "这匹马现在没有箱子了.", "citizens.commands.npc.horse.color-set": "这匹马的颜色已设置为 [[{0}]].", "citizens.commands.npc.horse.describe": "这匹马的颜色是 [[{0}]], 类型是 [[{1}]], 样式是 [[{2}]].", "citizens.commands.npc.horse.description": "设置马以及类似马的实体的参数", "citizens.commands.npc.horse.help": "使用 -c 使 NPC 拥有一个箱子，或者使用 -b 使它们不再拥有箱子.", "citizens.commands.npc.horse.invalid-color": "指定的马匹颜色无效, 有效的颜色是: [[{0}]].", "citizens.commands.npc.horse.invalid-style": "指定的马匹样式无效, 有效的样式是: [[{0}]].", "citizens.commands.npc.horse.invalid-type": "指定的马匹类型无效, 有效的类型是: [[{0}]].", "citizens.commands.npc.horse.style-set": "这匹马的样式已设置为 [[{0}]].", "citizens.commands.npc.horse.tamed-set": "[[{0}]] 现在是被驯服状态.", "citizens.commands.npc.horse.tamed-unset": "[[{0}]] 现在不再是被驯服状态.", "citizens.commands.npc.hurt.description": "向 NPC 施加伤害", "citizens.commands.npc.hurt.not-damageable": "NPC 类型 [[{0}]] 不会受伤.", "citizens.commands.npc.hurt.protected": "NPC 受保护且不会受伤. <click:run_command:/npc vulnerable>[[<u>点击以取消保护</click>", "citizens.commands.npc.id.description": "将已选择的 NPC 的 ID 发送给命令发送者", "citizens.commands.npc.inventory.description": "显示 NPC 的物品栏", "citizens.commands.npc.item.description": "设置 NPC 的物品", "citizens.commands.npc.itemframe.blockface-set": "朝向的方块面设置为 [[{0}]]", "citizens.commands.npc.itemframe.description": "设置物品展示框的参数", "citizens.commands.npc.itemframe.fixed-set": "固定状态 (物品是否能被旋转) 设置为 [[{0}]]", "citizens.commands.npc.itemframe.item-set": "物品设置为 [[{0}]]", "citizens.commands.npc.itemframe.rotation-set": "转向设置为 [[{0}]]", "citizens.commands.npc.itemframe.visible-set": "可见性设置为 [[{0}]]", "citizens.commands.npc.item.item-set": "[[{0}]] 持有的物品设置为 [[{1}]].", "citizens.commands.npc.item.unknown-material": "未知材料.", "citizens.commands.npc.jump.description": "使 NPC 跳跃", "citizens.commands.npc.knockback.description": "切换 NPC 是否可以被击退", "citizens.commands.npc.knockback.set": "[[{0}]] 将可以被击退.", "citizens.commands.npc.knockback.unset": "[[{0}]] 不再可以被击退.", "citizens.commands.npc.leashable.description": "切换 NPC 是否能被拴绳牵引", "citizens.commands.npc.leashable.set": "[[{0}]] 现在可以牵引.", "citizens.commands.npc.leashable.stopped": "[[{0}]] 不再可以牵引.", "citizens.commands.npc.list.description": "显示 NPC 列表", "citizens.commands.npc.llama.color-set": "羊驼颜色设置为 [[{0}]].", "citizens.commands.npc.llama.description": "设置羊驼的参数", "citizens.commands.npc.llama.invalid-color": "指定了无效的羊驼颜色. 有效的颜色是: [[{0}]].", "citizens.commands.npc.llama.strength-set": "羊驼负重设置为 [[{0}]].", "citizens.commands.npc.lookclose.description": "切换 NPC 是否会看向靠近的玩家", "citizens.commands.npc.lookclose.disable-when-navigating": "[[{0}]] 不会再在寻路时看向最近的玩家.", "citizens.commands.npc.lookclose.enable-when-navigating": "[[{0}]] 将会在寻路时看向最近的玩家.", "citizens.commands.npc.lookclose.error-random-range": "范围 [[{0}]] 无效. 使用格式: `最小值,最大值`.", "citizens.commands.npc.lookclose.headonly-set": "[[{0}]] 将只会旋转它的头.", "citizens.commands.npc.lookclose.headonly-unset": "[[{0}]] 将会旋转它的身体.", "citizens.commands.npc.lookclose.linkedbody-set": "[[{0}]] 的头和身体将会使用相同的偏航角.", "citizens.commands.npc.lookclose.linkedbody-unset": "[[{0}]] 的头和身体不再会使用相同的偏航角.", "citizens.commands.npc.lookclose.perplayer-set": "[[{0}]] 现在会按玩家管理视线.", "citizens.commands.npc.lookclose.perplayer-unset": "[[{0}]] 不再会按玩家管理视线.", "citizens.commands.npc.lookclose.random-look-delay-set": "[[{0}]] 的随机视线延迟设置为 [[{1}]] 刻.", "citizens.commands.npc.lookclose.random-pitch-range-set": "[[{0}]] 的随机俯仰角选择范围设置为 [{1}].", "citizens.commands.npc.lookclose.random-set": "[[{0}]] 现在将随机环顾四周.", "citizens.commands.npc.lookclose.random-stopped": "[[{0}]] 不再会随机环顾四周.", "citizens.commands.npc.lookclose.random-target-switch-disabled": "[[{0}]] 不再会随机改变目标.", "citizens.commands.npc.lookclose.random-target-switch-enabled": "[[{0}]] 将会在随机环顾延迟的基础上随机改变目标.", "citizens.commands.npc.lookclose.random-yaw-range-set": "[[{0}]] 的随机偏航角选择范围设置为 [{1}].", "citizens.commands.npc.lookclose.range-set": "[[{0}]] 看向最近玩家的距离设置为 [[{1}]] 格方块.", "citizens.commands.npc.lookclose.rl-set": "[[{0}]] 不再会在视线要穿过方块时看向玩家.", "citizens.commands.npc.lookclose.rl-unset": "[[{0}]] 将会总是看着玩家，即使视线要穿过方块.", "citizens.commands.npc.lookclose.set": "[[{0}]] 现在可以看向附近的玩家.", "citizens.commands.npc.lookclose.stopped": "[[{0}]] 现在不会看向任何玩家.", "citizens.commands.npc.lookclose.target-npcs-set": "[[{0}]] 现在会看向其他 NPC.", "citizens.commands.npc.lookclose.target-npcs-unset": "[[{0}]] 不再会看向其他 NPC.", "citizens.commands.npc.metadata.description": "管理 NPC 的元数据", "citizens.commands.npc.metadata.set": "[[{0}]] 已设置为 [[{1}]].", "citizens.commands.npc.metadata.unset": "[[{{0}}]] 已从 [[{1}]] 删除.", "citizens.commands.npc.minecart.description": "设置矿车的物品", "citizens.commands.npc.minecart.set": "[[{0}]] 现在有偏移量 [[{1}]].", "citizens.commands.npc.mirror.description": "控制 NPC 皮肤和其他事物的镜像", "citizens.commands.npc.mirror.namemirror-set": "[[{0}]] 现在将会镜像玩家名称.", "citizens.commands.npc.mirror.namemirror-unset": "[[{0}]] 不再会镜像玩家名称.", "citizens.commands.npc.mirror.set": "[[{0}]] 现在会镜像玩家皮肤.", "citizens.commands.npc.mirror.unset": "[[{0}]] 不再会镜像玩家皮肤.", "citizens.commands.npc.mount.description": "骑乘一个可控制的 NPC", "citizens.commands.npc.mount.failed": "无法骑乘 [[{0}]].", "citizens.commands.npc.mount.mount-on-itself": "不可以使 NPC 骑乘它自己.", "citizens.commands.npc.mount.must-be-spawned": "无法骑乘 [[{0}]]. 请确保目标 NPC 的 ID 正确且已生成.", "citizens.commands.npc.moveto.description": "将 NPC 传送到指定的位置", "citizens.commands.npc.moveto.format": "坐标格式为: x:y:z(:世界名称) 或者 x y z( 世界名称).", "citizens.commands.npc.moveto.teleported": "[[{0}]] 已传送到 [[{1}]]", "citizens.commands.npc.mushroomcow.description": "设置哞菇的参数", "citizens.commands.npc.mushroomcow.invalid-variant": "无效的变种类型, 有效值为: [[{0}]].", "citizens.commands.npc.mushroomcow.variant-set": "[[{0}]] 的变种类型设置为 [[{1}]].", "citizens.commands.npc.name.description": "切换名牌可见性，或仅在悬停时显示名称", "citizens.commands.npc.nameplate.set": "名牌可见性设置为 [[{0}]].", "citizens.commands.npc.ocelot.deprecated": "豹猫类型在此版本的 Minecraft 中不起作用. 如果你想改变颜色，请使用 [[/npc type cat]].", "citizens.commands.npc.ocelot.description": "设置 NPC 的豹猫类型以及它是否坐着", "citizens.commands.npc.ocelot.invalid-type": "无效的豹猫类型, 有效的类型是: [[{0}]].", "citizens.commands.npc.owner.already-owner": "{0} 已经是 [[{1}]] 的所有者.", "citizens.commands.npc.owner.description": "设置 NPC 的所有者", "citizens.commands.npc.owner.owner": "[[{0}]] 的所有者是 [[{1}]].", "citizens.commands.npc.owner.set": "[[{1}]] 现在是 [[{0}]] 的所有者.", "citizens.commands.npc.owner.set-server": "[[The server]] 现在是 [[{0}]] 的所有者.", "citizens.commands.npc.packet.description": "控制基于网络包的 NPC 的设置", "citizens.commands.npc.packet.disabled": "[[{0}]] 不再是基于网络包的 NPC.", "citizens.commands.npc.packet.enabled": "[[{0}]] 现在是基于网络包的 NPC.", "citizens.commands.npc.painting.art-set": "[[{0}]] 的画作设置为 [[{1}]].", "citizens.commands.npc.painting.description": "设置画的参数", "citizens.commands.npc.panda.description": "设置熊猫的参数", "citizens.commands.npc.panda.eating-set": "[[{0}]] 开始吃.", "citizens.commands.npc.panda.eating-unset": "[[{0}]] 停止吃.", "citizens.commands.npc.panda.hidden-gene-set": "隐性基因设置为 [[{0}]].", "citizens.commands.npc.panda.invalid-gene": "无效的基因, 有效的基因为 [[{0}]].", "citizens.commands.npc.panda.main-gene-set": "显性基因设置为 [[{0}]].", "citizens.commands.npc.panda.rolling": "[[{0}]] 开始滚动.", "citizens.commands.npc.panda.rolling-stopped": "[[{0}]] 停止滚动.", "citizens.commands.npc.panda.sitting": "[[{0}]] 坐下了.", "citizens.commands.npc.panda.sneezing": "[[{0}]] 开始打喷嚏.", "citizens.commands.npc.panda.sneezing-stopped": "[[{0}]] 停止打喷嚏.", "citizens.commands.npc.panda.stopped-sitting": "[[{0}]] 起来了.", "citizens.commands.npc.panimate.description": "播放玩家动作动画.", "citizens.commands.npc.panimate.unknown-animation": "无效的动画 [[{0}]]. 有效的动画是 {1}.", "citizens.commands.npc.parrot.description": "设置鹦鹉的参数", "citizens.commands.npc.parrot.invalid-variant": "无效的鹦鹉变种类型, 有效的鹦鹉变种类型是 [[{0}]].", "citizens.commands.npc.parrot.variant-set": "变种类型设置为 [[{0}]].", "citizens.commands.npc.passive.description": "切换 NPC 是否主动攻击其他实体", "citizens.commands.npc.passive.set": "[[{0}]] 现在是被动攻击型.", "citizens.commands.npc.passive.unset": "[[{0}]] 现在是主动攻击型.", "citizens.commands.npc.path.description": "切换到路径点编辑器", "citizens.commands.npc.pathfindingrange.set": "寻路范围设置为 [[{0}]].", "citizens.commands.npc.pathopt.attack-range-set": "[[{0}]] 的攻击范围设置为 [[{1}]].", "citizens.commands.npc.pathopt.avoid-water-set": "[[{0}]] 现在会避开水.", "citizens.commands.npc.pathopt.avoid-water-unset": "[[{0}]] 不再避开水.", "citizens.commands.npc.pathopt.description": "设置 NPC 的寻路参数", "citizens.commands.npc.pathopt.distance-margin-set": "[[{0}]] 的寻路距离容差设置为 [[{1}]].", "citizens.commands.npc.pathopt.falling-distance-set": "[[{0}]] 的下落距离设置为 [[{1}]].", "citizens.commands.npc.pathopt.open-doors-set": "[[{0}]] 现在会尝试打开门.", "citizens.commands.npc.pathopt.open-doors-unset": "[[{0}]] 不再会尝试打开门.", "citizens.commands.npc.pathopt.path-distance-margin-set": "[[{0}]] 的寻路路径裕度设置为 [[{1}]].", "citizens.commands.npc.pathopt.pathfinder-type-set": "[[{0}]] 的寻路器类型设置为 [[{1}]].", "citizens.commands.npc.pathopt.stationary-ticks-set": "[[{0}]] 的静止时间已设置为 [[{1}]] 刻.", "citizens.commands.npc.pathopt.use-new-finder": "[[{0}]] 使用新寻路器的设置更改为 [[{1}]].", "citizens.commands.npc.pathto.description": "开始到指定位置的寻路", "citizens.commands.npc.pausepathfinding.description": "设置是否暂停寻路", "citizens.commands.npc.pausepathfinding.lockout-duration-set": "[[{0}} 现在会在下一次暂停前等待 [[{1}]] 个游戏刻。", "citizens.commands.npc.pausepathfinding.pause-range-set": "[{0}]] 将在 [[{1}]] 格内有玩家时暂停寻路.", "citizens.commands.npc.pausepathfinding.pause-ticks-set": "[[{0}]] 现在将一次暂停 [[{1}]] tick 的寻路.", "citizens.commands.npc.pausepathfinding.rightclick-set": "[[{0}]] 将在被右键时暂停寻路.", "citizens.commands.npc.pausepathfinding.rightclick-unset": "[[{0}]] 不再会在被右键时暂停寻路.", "citizens.commands.npc.phantom.description": "设置幻翼的参数", "citizens.commands.npc.phantom.phantom-set": "尺寸设置为 [[{0}]].", "citizens.commands.npc.pickupitems.description": "允许 NPC 拾取物品", "citizens.commands.npc.pickupitems.set": "[[{0}]] 现在会拾取物品.", "citizens.commands.npc.pickupitems.unset": "[[{0}]] 不再会拾取物品.", "citizens.commands.npc.pig.description": "管理猪的参数", "citizens.commands.npc.pig.invalid-variant": "无效的变种类型. 有效的变种类型有 [[{0}]].", "citizens.commands.npc.piglin.dancing-set": "[[{0}]] 正在跳舞.", "citizens.commands.npc.piglin.dancing-unset": "[[{0}]] 停止跳舞.", "citizens.commands.npc.piglin.description": "设置猪灵的参数", "citizens.commands.npc.pig.variant-set": "变种类型设置为 [[{0}]].", "citizens.commands.npc.playerfilter.added": "[[{0}]] 已被添加至 [[{1}]] 的玩家筛选器列表.", "citizens.commands.npc.playerfilter.allowlist-set": "[[{0}]] 的筛选器模式设置为允许列表.", "citizens.commands.npc.playerfilter.applyrange-set": "[[{0}]] 的筛选器现在将应用在与其距离 [[{1}]] 格方块内的玩家.", "citizens.commands.npc.playerfilter.cleared": "[[{0}]] 的筛选器已被清除.", "citizens.commands.npc.playerfilter.denylist-set": "[[{0}]] 的筛选器模式设置为拒绝列表.", "citizens.commands.npc.playerfilter.description": "管理 NPC 的玩家筛选器", "citizens.commands.npc.playerfilter.emptied": "[[{0}]] 的筛选器已被清空.", "citizens.commands.npc.playerfilter.group-added": "[[{0}]] 已被添加到 [[{1}]] 的筛选器组.", "citizens.commands.npc.playerfilter.group-removed": "[[{0}]] 已从 [[{1}]] 的筛选器组中移除.", "citizens.commands.npc.playerfilter.permission-added": "[[{0}]] 已被添加至 [[{1}]] 的权限筛选器列表.", "citizens.commands.npc.playerfilter.permission-removed": "[[{0}]] 已从 [[{1}]] 的权限筛选器列表中移除.", "citizens.commands.npc.playerfilter.removed": "[[{0}]] 已从 [[{1}]] 的玩家筛选器列表中移除.", "citizens.commands.npc.playerlist.added": "已把 [[{0}]] 添加到玩家列表.", "citizens.commands.npc.playerlist.description": "设置 NPC 是否在玩家列表中.", "citizens.commands.npc.playerlist.removed": "已从玩家列表中删除了 [[{0}]].", "citizens.commands.npc.playsound.description": "在 NPC 的位置播放一个声音", "citizens.commands.npc.polarbear.description": "设置北极熊的参数", "citizens.commands.npc.polarbear.rearing-set": "[[{0}]] 现在站立.", "citizens.commands.npc.polarbear.rearing-unset": "[[{0}]] 不再站立.", "citizens.commands.npc.pose.added": "姿态已添加.", "citizens.commands.npc.pose.already-exists": "姿态 [[{0}]] 已存在.", "citizens.commands.npc.pose.default-pose-set": "默认姿势设置为 [[{0}]].", "citizens.commands.npc.pose.description": "管理 NPC 的姿势", "citizens.commands.npc.pose.invalid-name": "无效的姿态名称.", "citizens.commands.npc.pose.missing": "姿态 [[{0}]] 不存在.", "citizens.commands.npc.pose.removed": "姿态已删除.", "citizens.commands.npc.potioneffects.description": "管理药水效果", "citizens.commands.npc.potioneffects.effect-added": "药水效果 [[{0}]] 已添加到 [[{1}]].", "citizens.commands.npc.potioneffects.effect-removed": "药水效果 [[{{0}}]] 已从 [[{1}]] 删除.", "citizens.commands.npc.potioneffects.help": "管理 NPC 具有的药水效果。药水效果可以是 [[临时的]] (添加后会被删除的) 或者 [[持久化的]] (每次生成 NPC 时都会添加的已命名的效果). <br>你可以使用参数 -t 来添加一个临时的效果，或者提供一个名字作为一个持久化的效果的名字. <br>将持续时间设置为 -1 或者使用参数 -i 使效果无限持续。", "citizens.commands.npc.powered.description": "切换苦力怕 NPC 是否被充能", "citizens.commands.npc.powered.set": "[[{0}]] 现在已充能.", "citizens.commands.npc.powered.stopped": "[[{0}]] 现在不再被充能.", "citizens.commands.npc.profession.description": "设置 NPC 的职业", "citizens.commands.npc.profession.invalid-profession": "[[{0}]] 无效的职业, 有效的职业是: [[{1}]].", "citizens.commands.npc.profession.set": "[[{0}]] 现在是 [[{1}]].", "citizens.commands.npc.pufferfish.description": "设置河豚的参数", "citizens.commands.npc.pufferfish.state-set": "状态设置为 [[{0}]].", "citizens.commands.npc.rabbittype.description": "设置兔子 NPC 的类型", "citizens.commands.npc.rabbittype.invalid-type": "无效的兔子类型, 请尝试设置为以下之一: [[{0}]].", "citizens.commands.npc.rabbittype.type-set": "[[{0}]] 的兔子类型已设置为 [[{1}]]", "citizens.commands.npc.remove.description": "移除 NPC", "citizens.commands.npc.remove.npcs-removed": "一些 NPC 已被删除.", "citizens.commands.npc.remove.removed": "已永久删除 [[{0}]].", "citizens.commands.npc.remove.removed-all": "已永久删除所有 NPC.", "citizens.commands.npc.rename.description": "重命名一个 NPC.", "citizens.commands.npc.rename.renamed": "已把 [[{0}]] 的名称改为 [[{1}]].", "citizens.commands.npc.respawn.delay-set": "重生延迟设置为 [[{0}]].", "citizens.commands.npc.respawn.describe": "当前重生延迟是 [[{0}]].", "citizens.commands.npc.respawn.description": "设置 NPC 的重生延迟", "citizens.commands.npc.rotate.description": "旋转 NPC", "citizens.commands.npc.rotationsettings.describe": "当前的朝向设置: [[{0}]]", "citizens.commands.npc.rotationsettings.description": "编辑 NPC 的朝向设置", "citizens.commands.npc.rotationsettings.help": "控制 NPC 的各种头部和身体旋转设置。<br>目前，NPC 看起来使用简单的 Minecraft 风格旋转，其中头部/身体每刻向目标移动一定的角度<br>接受以下参数：<br>[[--link_body]]：头部和身体的旋转是否应直接链接<br>[[--head_only]]：是否只有头部应该<br>[[--max_yaw_per_tick]]：每刻改变偏航多少度（默认为 40）<br>[[--max_pitch_per_tick]]：每刻改变俯仰多少度（默认为 10）<br>[[--pitch_range]]：可以采取的俯仰范围（默认为 -180,180）<br>[[--yaw_range]]：可以采取的偏航范围（默认为 -180,180）", "citizens.commands.npc.scaledmaxhealth.description": "控制 NPC 的生命值上限，可以使用大数字。", "citizens.commands.npc.scaledmaxhealth.set": "生命值上限设置为 [[(0}]] 。", "citizens.commands.npc.select.already-selected": "你已经选择了这个 NPC.", "citizens.commands.npc.select.description": "根据提供的 ID 或名称选取一个 NPC", "citizens.commands.npc.setequipment.cosmetic-set": "设置 [[{0}]] (饰品) 为 [[{1}]] 。", "citizens.commands.npc.setequipment.description": "通过命令设置装备", "citizens.commands.npc.setequipment.set": "设置 [[{0}]] 为 [[{1}]].", "citizens.commands.npc.sheep.color-set": "羊的颜色设置为 [[{0}]].", "citizens.commands.npc.sheep.description": "设置羊的参数", "citizens.commands.npc.sheep.invalid-color": "无效的颜色, 有效的颜色有: [[{0}]].", "citizens.commands.npc.shop.deleted": "商店 [[{0}]] 已删除.", "citizens.commands.npc.shop.description": "编辑/显示 NPC 商店", "citizens.commands.npc.shop.shop-not-found": "找不到商店 [[{0}]].", "citizens.commands.npc.shop.show-player-not-found": "玩家 [[{0}]] 不在线，因此不能显示商店.", "citizens.commands.npc.shulker.color-set": "[[{0}]] 的颜色设置为 [[{1}]].", "citizens.commands.npc.shulker.description": "设置潜影贝的参数", "citizens.commands.npc.shulker.invalid-color": "无效的潜影贝颜色值, 有效的颜色是: [[{0}]].", "citizens.commands.npc.shulker.peek-set": "[[{0}]] 外壳打开度设置为 [[{1}]].", "citizens.commands.npc.sitting.description": "设置 NPC 是否坐下", "citizens.commands.npc.sitting.set": "[[{0}]] 现在坐在 [[{1}]]", "citizens.commands.npc.sitting.unset": "[[{0}]] 现在不再坐着.", "citizens.commands.npc.size.describe": "[[{0}]] 的尺寸为 [[{1}]].", "citizens.commands.npc.size.description": "设置 NPC 的大小", "citizens.commands.npc.size.set": "[[{0}]] 的尺寸设置为 [[{1}]].", "citizens.commands.npc.skeletontype.invalid-type": "无效的骨架类型. 请尝试以下值之一: [[{0}]].", "citizens.commands.npc.skeletontype.set": "{0} 骨架类型设置为 [[{1}]].", "citizens.commands.npc.skin.cleared": "[[{0}]] 的皮肤名称已清除.", "citizens.commands.npc.skin.description": "设置 NPC 的皮肤名称. 使用 -l 使皮肤始终保持最新.", "citizens.commands.npc.skin.error-setting-url": "从 [[{0}]] 下载皮肤材质时出错. 你的 URL 是否有效?", "citizens.commands.npc.skin.exported": "已将皮肤导出到 [[{0}]]", "citizens.commands.npc.skin.fetching": "正在尝试用 https://mineskin.org 生成皮肤数据", "citizens.commands.npc.skin.invalid-file": "找不到皮肤文件 [[{0}]]. 必须是一个在 plugins/Citizens/skins/<file.png> 的文件.", "citizens.commands.npc.skin.latest-set": "[[{0}]] 现在使用最新的皮肤. 当前的皮肤名称为 [[{1}]].", "citizens.commands.npc.skinlayers.description": "设置 NPC 皮肤层可见性", "citizens.commands.npc.skin.layers-set": "[[{0}]] 的皮肤层: 肩胛 - [[{1}]], 头部 - [[{2}]], 上衣 - [[{3}]], 袖子 - [[{4}]], 裤子 - [[{5}]].", "citizens.commands.npc.skin.missing-skin": "必须提供皮肤名称.", "citizens.commands.npc.skin.set": "[[{0}]] 的皮肤名称设置为 [[{1}]].", "citizens.commands.npc.skin.skin-url-set": "已从 [[{1}]] 为 [[{0}]] 下载皮肤.", "citizens.commands.npc.sniffer.description": "设置嗅探兽的参数", "citizens.commands.npc.sniffer.state-set": "[[{0}]] 的状态设置为 [[{1}]]", "citizens.commands.npc.snowman.derp-set": "[[{0}]] 现在不再头戴南瓜.", "citizens.commands.npc.snowman.derp-stopped": "[[{0}]] 现在头戴南瓜.", "citizens.commands.npc.snowman.description": "设置雪傀儡的参数", "citizens.commands.npc.snowman.form-snow-set": "[[{0}] 现在将会在走过的路径下留下雪.", "citizens.commands.npc.snowman.form-snow-stopped": "[[{0}] 不再会在走过的路径下留下雪.", "citizens.commands.npc.sound.description": "设置 NPC 播放的声音", "citizens.commands.npc.sound.info": "[[{0}]] 的声音: 环境 - [[{1}]], 被攻击 - [[{2}]], 死亡 - [[{3}]].\r\n有效的声音是 {4}.", "citizens.commands.npc.sound.invalid-sound": "无效的声音.", "citizens.commands.npc.sound.set": "[[{0}]] 现在的声音是: 环境 - [[{1}]], 被攻击 - [[{2}]], 死亡 - [[{3}]].", "citizens.commands.npc.spawn.already-spawned": "[[{0}]] 已经在另一个位置生成. 使用 ''/npc tphere'' 把它传送到这里来.", "citizens.commands.npc.spawn.description": "生成一个已存在的 NPC", "citizens.commands.npc.spawn.missing-npc-id": "不存在 ID 为 {0} 的 NPC.", "citizens.commands.npc.spawn.no-location": "没有可用的位置信息 - 必须在游戏中使用这个命令.", "citizens.commands.npc.spawn.spawned": "你生成了 [[{0}]].", "citizens.commands.npc.speak.description": "以 NPC 的身份发送一条消息", "citizens.commands.npc.speed.description": "设置 NPC 的移动速度百分比", "citizens.commands.npc.speed.set": "NPC 速度设置为 [[{0}]].", "citizens.commands.npc.spellcaster.description": "设置幻术师的参数", "citizens.commands.npc.spellcaster.spell-set": "幻术动作设置为 [[{0}]].", "citizens.commands.npc.swim.description": "设置 NPC 是否在游泳", "citizens.commands.npc.swim.set": "[[{0}]] 现在可以在水里游泳.", "citizens.commands.npc.swim.unset": "[[{0}]] 现在不能在水里游泳.", "citizens.commands.npc.targetable.description": "切换 NPC 是否可以作为其他生物的目标", "citizens.commands.npc.targetable.playerlist-warning": "把 NPC 添加到玩家列表会使其可以被其他生物当作目标. 这也许会导致插件冲突. 你可以随时使用 /npc playerlist 为这个 NPC 关闭这个功能.", "citizens.commands.npc.targetable.set": "[[{0}]] 现在可以被生物作为目标(可以吸引仇恨).", "citizens.commands.npc.targetable.unset": "[[{0}]] 现在不能作为生物的目标(不能吸引仇恨).", "citizens.commands.npc.target.description": "将提供的实体设置为目标", "citizens.commands.npc.text.description": "打开文本编辑器", "citizens.commands.npc.text.invalid-speech-bubbles-duration": "无效的语言气泡时长.", "citizens.commands.npc.text.speech-bubbles-duration-set": "语言气泡时长设置为 [[{0}]].", "citizens.commands.npc.tp.description": "传送至一个 NPC", "citizens.commands.npc.tphere.description": "将 NPC 传送到你所在的位置", "citizens.commands.npc.tphere.missing-cursor-block": "请看向一个方块以指定传送的目标位置", "citizens.commands.npc.tphere.teleported": "[[{0}]] 被传送到 {1}.", "citizens.commands.npc.tp.location-not-found": "找不到目标NPC的位置.", "citizens.commands.npc.tp.teleported": "传送到 [[{0}]].", "citizens.commands.npc.tpto.description": "传送一个 NPC (或者玩家) 到另一个 NPC (或者玩家)", "citizens.commands.npc.tpto.from-not-found": "找不到源实体.", "citizens.commands.npc.tpto.success": "传送成功.", "citizens.commands.npc.tpto.to-not-found": "找不到目标实体.", "citizens.commands.npc.trackingrange.description": "设置追踪范围", "citizens.commands.npc.trackingrange.set": "追踪范围设置为 [[{0}]].", "citizens.commands.npc.tropicalfish.body-color-set": "身体颜色设置为 [[{0}]].", "citizens.commands.npc.tropicalfish.description": "设置热带鱼的参数", "citizens.commands.npc.tropicalfish.invalid-color": "无效的鱼颜色, 有效的颜色是: [[{0}]]", "citizens.commands.npc.tropicalfish.invalid-pattern": "无效的鱼图案, 有效的图案是: [[{0}]]", "citizens.commands.npc.tropicalfish.pattern-color-set": "图案颜色设置为 [[{0}]].", "citizens.commands.npc.tropicalfish.pattern-set": "图案设置为 [[{0}]].", "citizens.commands.npc.type.description": "设置 NPC 的实体类型", "citizens.commands.npc.type.invalid": "[[{0}]] 是无效的类型.", "citizens.commands.npc.type.set": "[[{0}]] 的类型设置为 [[{1}]].", "citizens.commands.npc.undo.description": "撤销上一个操作 (目前只支持创建/删除)", "citizens.commands.npc.undo.successful": "撤销成功.", "citizens.commands.npc.undo.unsuccessful": "没有可以撤销的命令.", "citizens.commands.npc.useitem.description": "设置 NPC 的状态为正在使用它们手持的物品.", "citizens.commands.npc.useitem.held-item-toggled": "使用手持物品的状态设置为 [[{0}]].", "citizens.commands.npc.useitem.offhand-item-toggled": "使用副手物品的状态设置为 [[{0}]].", "citizens.commands.npc.vex.charging-set": "[[{0}]] 的尖叫状态已设置为 [[{1}]].", "citizens.commands.npc.vex.description": "设置恼鬼的参数", "citizens.commands.npc.villager.description": "设置村民的参数", "citizens.commands.npc.villager.invalid-type": "无效的村民类型. 有效的类型是: [[{0}]].", "citizens.commands.npc.villager.level-set": "等级设置为 [[{0}]].", "citizens.commands.npc.villager.type-set": "类型设置为 [[{0}]].", "citizens.commands.npc.vulnerable.description": "设置 NPC 是否容易受伤", "citizens.commands.npc.vulnerable.set": "[[{0}]] 现在容易受伤.", "citizens.commands.npc.vulnerable.stopped": "[[{0}]] 不再容易受伤.", "citizens.commands.npc.wander.description": "设置 NPC 是否随机游荡", "citizens.commands.npc.warden.anger-added": "添加了对 [[{0}]] 的 [[{1}]] 愤怒.", "citizens.commands.npc.warden.description": "设置监守者的参数", "citizens.commands.npc.warden.pose-set": "[[{0}]] 的姿势已设置为 [[{1}]].", "citizens.commands.npc.wither.description": "设置凋灵的参数", "citizens.commands.npc.wolf.collar-color-unsupported": "[[{0}]] 不是可以在狼项圈上使用的RGB颜色代码.", "citizens.commands.npc.wolf.description": "设置狼的参数", "citizens.commands.npc.wolf.unknown-collar-color": "[[{0}]] 不是RGB颜色格式或有效的项圈颜色.", "citizens.commands.page-missing": "页 [[{0}]] 不存在.", "citizens.commands.requirements.disallowed-mobtype": "这个命令不能使用在生物类型为 [[{0}]] 的NPC上.", "citizens.commands.requirements.living-entity": "NPC必须是有生命的实体.", "citizens.commands.requirements.missing-permission": "你无权执行这个命令.", "citizens.commands.requirements.missing-required-trait": "缺少必须的特征 [[{0}]].", "citizens.commands.requirements.must-be-ingame": "必须在游戏里才能使用这个命令.", "citizens.commands.requirements.must-be-owner": "你必须是这个NPC的所有者才能执行这个命令.", "citizens.commands.requirements.must-have-selected": "你必须选择一个NPC才能执行这个命令.", "citizens.commands.requirements.too-few-arguments": "参数太少.", "citizens.commands.requirements.too-many-arguments": "参数太多.", "citizens.commands.template.applied": "[[{0}]] 模板已应用于 [[{1}]].", "citizens.commands.template.apply.description": "将一个模板应用到选择的 NPC", "citizens.commands.template.conflict": "这个模板名称已存在.", "citizens.commands.template.created": "模板已创建.", "citizens.commands.template.create.description": "使用选择的 NPC 创建一个模板", "citizens.commands.template.delete.deleted": "模板 [[{0}]] 已删除.", "citizens.commands.template.delete.description": "删除一个模板", "citizens.commands.template.generate.description": "使用选择的 NPC 生成一个模板", "citizens.commands.template.generate.generated": "已使用 [[{0}]] 生成模板", "citizens.commands.template.list.description": "列出可用的模板", "citizens.commands.template.list.header": "]]可用模板:", "citizens.commands.template.missing": "找不到模板.", "citizens.commands.template.namespace-already-exists": "命名空间 [[{0}]] 已经存在", "citizens.commands.template.qualified-template-required": "重复的模板名称 [[{0}]]. 请从以下选项中选择: [[{1}]].", "citizens.commands.trait.add.description": "向 NPC 添加特征", "citizens.commands.trait.added": "已成功添加 {0}.", "citizens.commands.traitc.*.description": "配置一个特征", "citizens.commands.trait.clearsaves.cleared": "已从 NPC 存档中移除 [[{0}]].", "citizens.commands.trait.clearsaves.description": "从 NPC 存档中移除特定特征的信息", "citizens.commands.traitc.missing": "找不到特征.", "citizens.commands.traitc.not-configurable": "该特征是不可配置的.", "citizens.commands.traitc.not-on-npc": "没有这个特征.", "citizens.commands.trait.*.description": "切换 NPC 的特征状态", "citizens.commands.trait.failed-to-add": "<7>无法添加 {0}.", "citizens.commands.trait.failed-to-remove": "<7>无法删除 {0}.", "citizens.commands.trait.removed": "已成功删除 {0}.", "citizens.commands.trait.remove.description": "移除 NPC 的行为", "citizens.commands.unknown-command": "未知命令.", "citizens.commands.waypoints.add.description": "在一个点添加路径点", "citizens.commands.waypoints.add.waypoint-added": " 向 ({0}) 添加了一个路径点: (索引 [[{1}]])", "citizens.commands.waypoints.disableteleport.description": "禁用阻塞时传送", "citizens.commands.waypoints.disableteleporting.disabled": "[[{0}]] 不再会在寻路阻塞时传送.", "citizens.commands.waypoints.disableteleporting.enabled": "[[{0}]] 现在会在寻路阻塞时传送.", "citizens.commands.waypoints.hpa": "仅为 Citizens 开发者用于测试新寻路的命令.", "citizens.commands.waypoints.opendoors.disabled": "[[{0}]] 寻路时不再打开门.", "citizens.commands.waypoints.opendoors.enabled": "[[{0}]] 现在将在寻路时打开门.", "citizens.commands.waypoints.provider.description": "设置当前的路径点提供者", "citizens.commands.waypoints.remove.description": "在一个点添加路径点", "citizens.commands.waypoints.waypoint-removed": "移除了位于 [[{0}]] 的路径点.", "citizens.commands.wolf.traits-updated": "[[{0}]] 的特征已更新. 愤怒:[[{1}]], 坐着:[[{2}]], 已驯服:[[{3}]], 项圈颜色:[[{4}]]", "citizens.contribute-to-translations-prompt": "检测到系统语言 [[{0}]]. 如果您有兴趣，欢迎通过我们的 Discord 为 Citizens 贡献翻译！https://discord.gg/Q6pZGSR", "citizens.conversations.selection.invalid-choice": "[[{0}]] 不是有效的选项.", "citizens.economy.loaded": "已通过 Vault 加载经济处理程序.", "citizens.economy.minimum-cost-required": "至少需要 [[{0}]].", "citizens.economy.money-withdrawn": "为你的NPC收回 [[{0}]].", "citizens.editors.already-in-editor": "你已经打开了编辑器!", "citizens.editors.copier.begin": "<aqua>进入了NPC复印机!<br>单击任意位置以复制当前选定的NPC.", "citizens.editors.copier.end": "退出了NPC复印机.", "citizens.editors.equipment.begin": "进入装备编辑器! <br>[[右键单击]] 装备 NPC 或 [[蹲伏并右键单击]] 更改手上的物品. <br>在聊天中输入 [[offhand]], [[chestplate]], [[helmet]] 等槽位名称来用你持有的物品装备特定的槽位.", "citizens.editors.equipment.end": "退出了装备编辑器.", "citizens.editors.equipment.saddled-set": "[[{0}]] 已经装备了马鞍.", "citizens.editors.equipment.saddled-stopped": "[[{0}]] 不再装备马鞍.", "citizens.editors.equipment.sheared-set": "[[{0}]] 身上光秃秃的.", "citizens.editors.equipment.sheared-stopped": "[[{0}]] 身上不再是光秃秃的.", "citizens.editors.equipment.sheep-coloured": "[[{0}]] 现在是彩色的 [[{1}]].", "citizens.editors.selection.start-prompt": "有多个相同名称的NPC.<br>请输入下列编号以选择这个NPC.", "citizens.editors.text.added-entry": "[[添加了]] 条目 [[{0}]].", "citizens.editors.text.add-prompt": "请输入文字以添加到NPC.", "citizens.editors.text.begin": "<aqua>进入了文本编辑器! 输入 ''exit'' 退出编辑器.", "citizens.editors.text.change-page-prompt": "输入页码查看更多文本条目.", "citizens.editors.text.close-talker-set": "[[近距离说话]] 设置为 [[{0}]].", "citizens.editors.text.delay-set": "[[延迟]] 设置为 [[{0}]] 刻.", "citizens.editors.text.end": "退出了文本编辑器.", "citizens.editors.text.invalid-delay": "无效延迟.", "citizens.editors.text.invalid-edit-type": "无效的编辑类型.", "citizens.editors.text.invalid-index": "[[{0}]] 无效索引!", "citizens.editors.text.invalid-input": "无效输入.", "citizens.editors.text.invalid-page": "无效页码.", "citizens.editors.text.invalid-range": "无效范围.", "citizens.editors.text.missing-item-set-pattern": "缺少手持物品模式.", "citizens.editors.text.random-talker-set": "[[随机说话]] 设置为 [[{0}]].", "citizens.editors.text.range-set": "[[范围]] 设置为 [[{0}]].", "citizens.editors.text.realistic-looking-set": "[[仿真视线]] 设置为 [[{0}]].", "citizens.editors.text.speech-bubbles-set": "[[语言气泡]] 设置为 [[{0}]].", "citizens.editors.text.start-prompt": "<click:suggest_command:add ><yellow><u>添加文本</u></click> | <click:suggest_command:item ><hover:show_text:\"设置手中的谈话物品模式（设置为 <yellow>默认</yellow> 来清除）\"><yellow><u>物品</hover></click> | <click:suggest_command:range ><hover:show_text:设置谈话范围（以方块为单位）\"><yellow><u>范围</hover></click> | <click:suggest_command:delay ><hover:show_text:设置谈话延迟（以刻为单位）\"><yellow><u>延迟</u></yellow></hover></click> | <click:run_command:/npc text close><hover:show_text:切换玩家接近时发送消息的设置>{0}<u>关闭谈话</hover></click><br><click:run_command:/npc text random><hover:show_text:切换随机谈话>{1}<u>随机</hover></click> | <click:suggest_command:/npc text speech bubbles duration ><hover:show_text:设置气泡文本的持续时间>{2}<u>气泡文本持续时间</hover></click> | <click:run_command:/npc text speech bubbles><hover:show_text:切换显示为全息图而非聊天消息>{2}<u>气泡文本</hover></click> | <click:run_command:/npc text realistic looking><hover:show_text:切换需要视线才能开始讲话>{3}<u>真实</hover></click> | <click:run_command:/npc text send text to chat><hover:show_text:切换是否发送文本到聊天>{4}<u>发送到聊天</hover></click>", "citizens.editors.text.talk-item-set": "[[Talk item pattern]] 设置为 [[{0}]].", "citizens.editors.text.text-list-header": "当前文本:", "citizens.editors.waypoints.guided.added-available": "添加一个 [[目标]] 航点. 这将为NPC提供行动路径.", "citizens.editors.waypoints.guided.added-guide": "添加一个 [[导航]] 航点. 这将引导NPC到达目的地.", "citizens.editors.waypoints.guided.already-taken": "这里已经有一个航路点.", "citizens.editors.waypoints.guided.begin": "<aqua>进入了导航点编辑器!<br>    [[左键点击]] 添加一个导航点, [[右键点击]] 一个现存的导航点以删除.<br>    [[Shift+左键]] 添加一个目标航点.<br>    输入 [[toggle path]] 打开或关闭在导航点上显示实体.", "citizens.editors.waypoints.guided.distance-set": "导航之间的距离设置为 [[{0}]].", "citizens.editors.waypoints.guided.end": "退出了导航点编辑器.", "citizens.editors.waypoints.linear.added-waypoint": " 向 ({0}) [[添加了]] 一个导航点: ([[{1}]], [[{2}]])", "citizens.editors.waypoints.linear.begin": "<aqua>进入线性航点编辑器!<br>    [[左键点击]] 添加一个航点, [[右键点击]] 删除一个航点.<br>    输入 [[toggle path]] 切换航点上的实体显示, [[triggers]] 进入触发器编辑器, [[clear]] 清除所有航点.", "citizens.editors.waypoints.linear.cycle-set": "现在在路径点之间 [[循环]].", "citizens.editors.waypoints.linear.cycle-unset": "现在不再在路径点之间 [[循环]].", "citizens.editors.waypoints.linear.end": "退出了线性航点编辑器.", "citizens.editors.waypoints.linear.not-showing-markers": "[[停止了]] 显示航点标记.", "citizens.editors.waypoints.linear.range-exceeded": "上一个航路点距离 {0} 个块, 但距离限制为 {1}.", "citizens.editors.waypoints.linear.removed-waypoint": "[[删除了]] 一个航点 ([[{0}]] 剩余) ([[{1}]])", "citizens.editors.waypoints.linear.selected-waypoint": "选择了位于 {0} 的路径点. 再次蹲下并右键以移除这个路径点.", "citizens.editors.waypoints.linear.showing-markers": "[[显示]] 航点标记.", "citizens.editors.waypoints.linear.waypoints-cleared": "已清除路径点.", "citizens.editors.waypoints.triggers.add.added": "<aqua>成功添加了航点触发器({0}).", "citizens.editors.waypoints.triggers.add.invalid-trigger": "无法创建这个名称的航点触发器 [[{0}]].", "citizens.editors.waypoints.triggers.add.prompt": "输入名称以添加一个触发器, 或者输入 [[back]] 返回到编辑提示. 有效的触发器名称是 {0}.", "citizens.editors.waypoints.triggers.animation.added": "添加了动画 [[{0}]].", "citizens.editors.waypoints.triggers.animation.at-set": "动画位置设置为 [[{0}]].", "citizens.editors.waypoints.triggers.animation.invalid-animation": "无效的动画 [[{0}]]. 有效的动画是 {1}.", "citizens.editors.waypoints.triggers.animation.prompt": "输入要执行的动画 - 有效的动画是 {0}.<br>输入 [[finish]] 完成动画触发器, 或者输入 [[back]] 返回到上一个提示.", "citizens.editors.waypoints.triggers.chat.invalid-radius": "半径必须是一个数字.", "citizens.editors.waypoints.triggers.chat.message-added": "消息已添加: [[{0}]].", "citizens.editors.waypoints.triggers.chat.missing-radius": "没有提供半径.", "citizens.editors.waypoints.triggers.chat.prompt": "输入聊天内容.<br>输入 [[radius (radius)]] 设置半径以广播消息.<br>输入 [[finish]] 完成聊天触发器, 或者输入 [[back]] 返回到上一个提示.", "citizens.editors.waypoints.triggers.chat.radius-set": "半径设置为 [[{0}]] 格方块.", "citizens.editors.waypoints.triggers.command.added": "命令 [[{0}]] 已添加.", "citizens.editors.waypoints.triggers.command.prompt": "输入一条或多条命令 (不带开头的斜杠).", "citizens.editors.waypoints.triggers.delay.prompt": "输入延迟 [[服务器刻]]. (20 刻 = 1 秒)", "citizens.editors.waypoints.triggers.list": "当前触发器是:{0}", "citizens.editors.waypoints.triggers.main.exit": "<aqua>退出了航点触发器编辑器.", "citizens.editors.waypoints.triggers.main.missing-waypoint": "没有编辑航点.", "citizens.editors.waypoints.triggers.main.prompt": "<aqua>- 航点触发器编辑器 -<br>    输入 [[add]] 或者 [[remove]] 编辑触发器.<br>    输入 [[triggers]] 或者 [[exit]] 退出编辑器.<br>    当前触发器是:{0}", "citizens.editors.waypoints.triggers.speed.prompt": "输入基本速度的 [[百分比]] 以修改速度.", "citizens.editors.waypoints.triggers.teleport.invalid-format": "指定的位置无效. 格式是 [[world]]:[[x]]:[[y]]:[[z]].", "citizens.editors.waypoints.triggers.teleport.prompt": "输入格式为 world:x:y:z 的目的位置. 输入 [[here]] 使用你当前的位置. 输入 [[back]] 返回到编辑提示.", "citizens.editors.waypoints.wander.added-region": "[[添加了]]漫游区域 ({0}) ([[{1}]]).", "citizens.editors.waypoints.wander.begin": "<aqua>进入了漫游航点编辑器.<br>    输入 [[xrange <number>]] 或 [[yrange <number>]] 修改随机漫游范围. 输入 [[regions]] 进入区域编辑器.", "citizens.editors.waypoints.wander.delay-set": "漫游器之间的延迟时间设置为 [[{{0}]] 刻.", "citizens.editors.waypoints.wander.editing-regions": "现在编辑区域!<br>    [[左键点击]] 添加一个以这个方块中心, xrange/yrange 为范围的的漫游区域.<br>    [[右键点击]] 现有标记以删除该区域.<br>    输入 [[regions]] 停止或退出编辑器. 区域应该相互重叠.", "citizens.editors.waypoints.wander.editing-regions-stop": "退出了区域编辑器.", "citizens.editors.waypoints.wander.end": "退出了漫游航点编辑器.", "citizens.editors.waypoints.wander.invalid-delay": "指定了无效的延迟.", "citizens.editors.waypoints.wander.range-set": "漫游范围设置为 x范围[[{{0}]] 和 y范围[[{{1}]].", "citizens.editors.waypoints.wander.removed-region": "[[删除了]]漫游区域 ({0}) ([[{1}]] 剩余的).", "citizens.editors.waypoints.wander.worldguard-region-not-found": "找不到 WorldGuard 区域.", "citizens.editors.waypoints.wander.worldguard-region-set": "WorldGuard 区域设置为 [[{0}]].", "citizens.limits.over-npc-limit": "超过NPC限制 {0}.", "citizens.load-task-error": "无法安排NPC加载任务，正在禁用...", "citizens.nms-errors.clearing-goals": "无法清除目标: {0}.", "citizens.nms-errors.error-setting-persistent": "无法把NPC设置为永久的: {0}. NPC实体可能会消失.", "citizens.nms-errors.getting-field": "无法获取NMS字段 {0}: [[{1}.", "citizens.nms-errors.getting-id-mapping": "无法获取实体ID映射字段: {0}.", "citizens.nms-errors.getting-method": "无法获取NMS方法 {0}: [[{1}.", "citizens.nms-errors.restoring-goals": "无法还原目标: {0}.", "citizens.nms-errors.spawning-custom-entity": "无法生成自定义实体: {0}.", "citizens.nms-errors.stopping-network-threads": "无法停止网络线程: {0}.", "citizens.nms-errors.updating-land-modifier": "无法更新地面速度修改器: {0}.", "citizens.nms-errors.updating-navigation-world": "无法更新导航世界: {0}.", "citizens.nms-errors.updating-pathfinding-range": "无法更新寻路范围: {0}.", "citizens.notifications.error-reloading": "重新加载时发生错误, 请参阅控制台.", "citizens.notifications.exception-updating-npc": "更新时发生异常 {0}: {1}.", "citizens.notifications.incompatible-version": "v{0} 与 Minecraft v{1}不兼容 - 请尝试更换 Minecraft 或 Citizens 的版本。正在禁用插件。", "citizens.notifications.locale": "使用地区 {0}.", "citizens.notifications.metrics-load-error": "无法启动指标系统: {0}.", "citizens.notifications.missing-translations": "缺少 {0} 的语言翻译文件.", "citizens.notifications.npc-name-not-found": "找不到ID为 {0} 的名称.", "citizens.notifications.npc-not-found": "找不到NPC.", "citizens.notifications.npcs-loaded": "已载入 {0} 个NPC.", "citizens.notifications.reloaded": "Citizens 已重载.", "citizens.notifications.reloading": "正在重载 Citizens...", "citizens.notifications.reload-warning": "警告：此命令将从磁盘加载所有数据，而不先保存。请重新输入 /citizens reload 进行确认。您可以在设置中禁用此警告。", "citizens.notifications.saved": "Citizens 已保存.", "citizens.notifications.saving": "保存 Citizens...", "citizens.notifications.skipping-invalid-pose": "跳过姿态 {0} - 无效的偏航度/俯仰角 ({1}).", "citizens.notifications.trait-load-failed": "无法载入特征 {0} 到 NPC ID: {1}.", "citizens.notifications.trait-onspawn-failed": "生成特征到 {0} NPC ID {1} 时发生异常.", "citizens.notifications.unknown-npc-type": "无法识别NPC类型 {0}. 拼写是否正确?", "citizens.saves.load-failed": "无法载入存档, 禁用中...", "citizens.settings.writing-default": "写入默认设置: {0}", "citizens.traits.age-description": "{0} 的年龄是 [[{1}]]. 锁定为 [[{2}]].", "citizens.waypoints.available-providers-header": "<aqua>可用的提供者列表", "citizens.waypoints.current-provider": "当前的航点提供者是 [[{0}]].", "citizens.waypoints.set-provider": "将航点提供者设置为 [[{0}]]."}