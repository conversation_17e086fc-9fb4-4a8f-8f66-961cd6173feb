## TPA 插件配置文件
## Github：https://github.com/WarSkyGod/TPA

# 插件版本，请勿自行修改
version: 3.2.5

# 服务端显示语言，默认为简体中文（zh_CN）,支持多种语言
# 客户端会自动根据客户端的语言选择合适的语言脚本，如果不存在对应的语言脚本默认使用这里设置的语言
language: zh_CN

# 开启后将在后台显示详细的报错信息以便提供给开发者排错
# 如果没有任何问题请勿开启，会在后台产生大量垃圾信息
debug: false

# 插件更新检查
# 关闭后服务器后台和拥有 tpa.version 或 tpa.admin 权限的玩家将不会收到插件更新提醒
update_check: true

# 是否每次进入服务器或死亡都强制传送到 spawn
force_spawn: false

# 是否启用 title 传送消息（1.12 以下版本将自动关闭）
enable_title_message: true

# 是否启用传送音效（需要 enable_title_message 为 true）
enable_sound: true

# 延迟设置（单位：秒）
delay:
  # 对方最晚可以在多长时间内接受？（最低不能小于3秒）
  accept: 30

  # 是否启用传送前间隔（传送前需要等待指定时间才能传送）
  enable_teleport: true

  # 是否启用命令执行间隔（使用命令后需要等待指定时间才能再次使用命令）
  enable_command: true

  # 使用 warp，home，spawn，back 命令是否绕过等待时间检测
  non_tpa_or_tphere_disable_check: false

  # 没有任何权限的玩家
  default:
    # 传送前需要等待多长时间？（为 0 关闭）
    teleport: 5

    # 使用命令后需要等待多长时间才能再次使用命令？（为 0 关闭）
    command: 30

  # 拥有 tpa.vip 权限的玩家
  vip:
    # 传送前需要等待多长时间？（为 0 关闭）
    teleport: 5

    # 使用命令后需要等待多长时间才能再次使用命令？（为 0 关闭）
    command: 25

  # 拥有 tpa.vip+ 权限的玩家
  vip+:
    # 传送前需要等待多长时间？（为 0 关闭）
    teleport: 5

    # 使用命令后需要等待多长时间才能再次使用命令？（为 0 关闭）
    command: 20

  # 拥有 tpa.mvp 权限的玩家
  mvp:
    # 传送前需要等待多长时间？（为 0 关闭）
    teleport: 5

    # 使用命令后需要等待多长时间才能再次使用命令？（为 0 关闭）
    command: 15

  # 拥有 tpa.mvp+ 权限的玩家
  mvp+:
    # 传送前需要等待多长时间？（为 0 关闭）
    teleport: 5

    # 使用命令后需要等待多长时间才能再次使用命令？（为 0 关闭）
    command: 10

  # 拥有 tpa.mvp++ 权限的玩家
  mvp++:
    # 传送前需要等待多长时间？（为 0 关闭）
    teleport: 5

    # 使用命令后需要等待多长时间才能再次使用命令？（为 0 关闭）
    command: 5

  # 拥有 tpa.admin 权限的玩家
  admin:
    # 传送前需要等待多长时间？（为 0 关闭）
    teleport: 0

    # 使用命令后需要等待多长时间才能再次使用命令？（为 0 关闭）
    command: 0

# tpa 命令设置
tpa:
  # 是否开启 tpa 命令
  # 如果 tpa 和 tphere 命令都未启用，将自动关闭 /tpaccept，/tpdeny，/denys 命令
  enable: true

  # 是否检测权限
  # permission: tpa.tpa
  permission: false

# tphere 命令设置
tphere:
  # 是否开启 tphere 命令
  # 如果 tpa 和 tphere 命令都未启用，将自动关闭 /tpaccept，/tpdeny，/denys 命令
  enable: true

  # 是否检测权限
  # permission: tpa.tphere
  permission: false

# denys 命令设置
denys:
  # 是否检测权限
  # permission: tpa.denys
  permission: false

# rtp 命令设置
rtp:
  # 是否开启 rtp 命令
  enable: true

  # 是否检测权限
  # permission: tpa.rtp
  permission: false

  # 在哪些世界无法使用该命令
  disable_worlds:
    - disableWorld1

  # 随机传送的最大范围（半径）
  # 以使用命令的玩家为中心
  limit:
    x: 500
    z: 500

# warp 命令设置
warp:
  # 是否开启 warp 命令
  # 影响 /warp，/setwarp，/delwarp 的使用
  enable: true

  # 是否检测权限
  # permission: tpa.warp
  permission: false

# home 命令设置
home:
  # 是否开启 home 命令
  # 影响 /home，/homes，/sethome，/setdefaulthome，/delhome 的使用
  enable: true

  # 是否检测权限
  # permission: tpa.home
  permission: false

  # 最大可设置多少个家（-1为不限制）
  amount:
    # 没有任何权限的玩家
    default: 10

    # 拥有 tpa.vip 权限的玩家
    vip: 5

    # 拥有 tpa.vip+ 权限的玩家
    vip+: 10

    # 拥有 tpa.mvp 权限的玩家
    mvp: 15

    # 拥有 tpa.mvp+ 权限的玩家
    mvp+: 20

    # 拥有 tpa.mvp++ 权限的玩家
    mvp++: 25

    # 拥有 tpa.admin 权限的玩家
    admin: -1

# spawn 命令设置
spawn:
  # 是否开启 spawn 命令
  # 影响 /spawn，/setspawn，/delspawn 的使用
  enable: true

  # 是否检测权限
  # permission: tpa.spawn
  permission: false

# back 命令设置
back:
  # 是否开启 back 命令
  enable: true

  # 是否检测权限
  # permission: tpa.back
  permission: false