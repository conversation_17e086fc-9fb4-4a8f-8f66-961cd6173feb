global:
  # Settings for the automatic version updater
  auto updater:
    notify: true
    download: false
    
    # Number of seconds to wait until a new update is downloaded
    delay: 43200 # 12 hours
 
  metrics: true

  # Prints certain warnings to players with the protocol.info permission
  chat warnings: true
  
  # Automatically compile structure modifiers 
  background compiler: true
  
  # Disable version checking for the given Minecraft version. Backup your world first!
  ignore version check: 

  # Whether or not to enable the filter command
  debug: false
  
  # Whether or not to print a stack trace for every warning
  detailed error: false
  
  # The engine used by the filter command
  script engine: JavaScript
  
  suppressed reports: