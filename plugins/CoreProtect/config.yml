# CoreProtect Config

# CoreProtect is donationware. Obtain a donation key from coreprotect.net/donate/
donation-key: 

# MySQL is optional and not required.
# If you prefer to use MySQL, enable the following and fill out the fields.
use-mysql: false
table-prefix: co_
mysql-host: 127.0.0.1
mysql-port: 3306
mysql-database: database
mysql-username: root
mysql-password: 

# If modified, will automatically attempt to translate languages phrases.
# List of language codes: https://coreprotect.net/languages/
language: zh

# If enabled, CoreProtect will check for updates when your server starts up.
# If an update is available, you'll be notified via your server console.
check-updates: true

# If enabled, other plugins will be able to utilize the CoreProtect API.
api-enabled: true

# If enabled, extra data is displayed during rollbacks and restores.
# Can be manually triggered by adding "#verbose" to your rollback command.
verbose: true

# If no radius is specified in a rollback or restore, this value will be
# used as the radius. Set to "0" to disable automatically adding a radius.
default-radius: 10

# The maximum radius that can be used in a command. Set to "0" to disable.
# To run a rollback or restore without a radius, you can use "r:#global".
max-radius: 100

# If enabled, items taken from containers (etc) will be included in rollbacks.
rollback-items: true

# If enabled, entities, such as killed animals, will be included in rollbacks.
rollback-entities: true

# If enabled, generic data, like zombies burning in daylight, won't be logged.
skip-generic-data: true

# Logs blocks placed by players.
block-place: true

# Logs blocks broken by players.
block-break: true

# Logs blocks that break off of other blocks; for example, a sign or torch
# falling off of a dirt block that a player breaks. This is required for
# beds/doors to properly rollback.
natural-break: true

# Properly track block movement, such as sand or gravel falling.
block-movement: true

# Properly track blocks moved by pistons.
pistons: true

# Logs blocks that burn up in a fire.
block-burn: true

# Logs when a block naturally ignites, such as from fire spreading.
block-ignite: true

# Logs explosions, such as TNT and Creepers.
explosions: true

# Track when an entity changes a block, such as an Enderman destroying blocks.
entity-change: true

# Logs killed entities, such as killed cows and enderman.
entity-kills: true

# Logs text on signs. If disabled, signs will be blank when rolled back.
sign-text: true

# Logs lava and water sources placed/removed by players who are using buckets.
buckets: true

# Logs natural tree leaf decay.
leaf-decay: true

# Logs tree growth. Trees are linked to the player who planted the sapling.
tree-growth: true

# Logs mushroom growth.
mushroom-growth: true

# Logs natural vine growth.
vine-growth: true

# Logs the spread of sculk blocks from sculk catalysts.
sculk-spread: true

# Logs when portals such as Nether portals generate naturally.
portals: true

# Logs water flow. If water destroys other blocks, such as torches,
# this allows it to be properly rolled back.
water-flow: true

# Logs lava flow. If lava destroys other blocks, such as torches,
# this allows it to be properly rolled back.
lava-flow: true

# Allows liquid to be properly tracked and linked to players.
# For example, if a player places water which flows and destroys torches,
# it can all be properly restored by rolling back that single player.
liquid-tracking: true

# Track item transactions, such as when a player takes items from
# a chest, furnace, or dispenser.
item-transactions: true

# Logs items dropped by players.
item-drops: true

# Logs items picked up by players.
item-pickups: true

# Track all hopper transactions, such as when a hopper removes items from a
# chest, furnace, or dispenser.
hopper-transactions: true

# Track player interactions, such as when a player opens a door, presses
# a button, or opens a chest. Player interactions can't be rolled back.
player-interactions: true

# Logs messages that players send in the chat.
player-messages: true

# Logs all commands used by players.
player-commands: true

# Logs the logins and logouts of players.
player-sessions: true

# Logs when a player changes their Minecraft username.
username-changes: true

# Logs changes made via the plugin "WorldEdit" if it's in use on your server.
worldedit: true
