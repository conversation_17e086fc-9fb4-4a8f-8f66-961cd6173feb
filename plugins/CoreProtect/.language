# CoreProtect v22.4 Language Cache (zh)

ACTION_NOT_SUPPORTED: "该命令不支持该操作。"
AMOUNT_BLOCK: "{0}个{方块|方块}"
AMOUNT_CHUNK: "{0}个{区块|区块}"
AMOUNT_ENTITY: "{0}个{实体|实体}"
AMOUNT_ITEM: "{0}个{物品|物品}"
API_TEST: "API 测试成功。"
CACHE_ERROR: "警告: 验证缓存 {0} 时出错。"
CACHE_RELOAD: "正在强制从数据库中重新加载{映射|世界}的缓存。"
CHECK_CONFIG: "请检查 config.yml 文件"
COMMAND_CONSOLE: "请在控制台中运行此命令。"
COMMAND_NOT_FOUND: "找不到命令 \"{0}\""
COMMAND_THROTTLED: "请稍等片刻，然后重试。"
CONSUMER_ERROR: "数据处理队列已经{暂停|恢复}。"
CONSUMER_TOGGLED: "数据处理队列现已{暂停|恢复}。"
CONTAINER_HEADER: "容器物品变更记录"
DATABASE_BUSY: "数据库繁忙，请稍后重试。"
DATABASE_INDEX_ERROR: "无法验证数据库索引。"
DATABASE_LOCKED_1: "数据库已被锁定，请等待至少15秒后重试..."
DATABASE_LOCKED_2: "数据库已在使用，请再试一次。"
DATABASE_LOCKED_3: "禁用数据库锁定，请在配置文件中设置 \"database-lock: false\"。"
DATABASE_LOCKED_4: "禁用数据库锁定可能会导致数据损坏。"
DATABASE_UNREACHABLE: "数据库无法访问。正在丢弃数据并关闭。"
DEVELOPMENT_BRANCH: "检测到正在使用开发分支的版本，跳过使用补丁脚本。"
DISABLE_SUCCESS: "禁用 {0} 成功！"
ENABLE_FAILED: "无法启用 {0}！"
ENABLE_SUCCESS: "{0} 已成功启用！"
ENJOY_COREPROTECT: "对 {0} 感到满意吗？请加入我们的 Discord 频道！"
FINISHING_CONVERSION: "正在完成数据转换，请稍等..."
FINISHING_LOGGING: "正在完成数据记录，请稍等..."
FIRST_VERSION: "初始化数据库: {0}"
GLOBAL_LOOKUP: "请勿在执行全局查询时指定半径。"
GLOBAL_ROLLBACK: "使用 \"{0}\" 进行全局{回滚|恢复}操作"
HELP_ACTION_1: "将查询限制为一个动作。"
HELP_ACTION_2: "例子: [a:block], [a:+block], [a:-block] [a:click], [a:container], [a:inventory], [a:item], [a:kill], [a:chat], [a:command], [a:sign], [a:session], [a:username]"
HELP_COMMAND: "显示附加信息。"
HELP_EXCLUDE_1: "排除方块/玩家。"
HELP_EXCLUDE_2: "例子: [e:stone], [e:Notch], [e:stone,Notch]"
HELP_HEADER: "{0} 帮助"
HELP_INCLUDE_1: "包括方块/实体。"
HELP_INCLUDE_2: "例子: [i:stone], [i:zombie], [i:stone,wood,bedrock]"
HELP_INSPECT_1: "启用检查器后，你可以:"
HELP_INSPECT_2: "左键单击以查看方块放置记录。"
HELP_INSPECT_3: "右键单击以查看相邻方块破坏记录。"
HELP_INSPECT_4: "放置一个方块以查看放置位置的破坏记录。"
HELP_INSPECT_5: "在液体中放置一个方块以查看液体变更记录。"
HELP_INSPECT_6: "右键单击门、箱子等以查看交互记录。"
HELP_INSPECT_7: "提示: 你可以使用 \"/co i\" 进行快速查询。"
HELP_INSPECT_COMMAND: "开启或关闭方块检查状态。"
HELP_LIST: "显示所有命令的列表。"
HELP_LOOKUP_1: "命令快捷方式。"
HELP_LOOKUP_2: "在查询记录后使用以查看日志"
HELP_LOOKUP_COMMAND: "高级方块数据查询。"
HELP_NO_INFO: "命令的信息 \"{0}\" 未找到。"
HELP_PARAMETER: "请参阅 \"{0}\" 了解详细的参数信息。"
HELP_PARAMS_1: "执行{查找|回滚|恢复}操作。"
HELP_PARAMS_2: "指定{查找|回滚|恢复}的玩家。"
HELP_PARAMS_3: "指定{查找|回滚|恢复}的时间。"
HELP_PARAMS_4: "限制{查找|回滚|恢复}的作用范围。"
HELP_PARAMS_5: "限制{查找|回滚|恢复}的动作。"
HELP_PARAMS_6: "{查找|回滚|恢复}时包括方块/实体。"
HELP_PARAMS_7: "{查找|回滚|恢复}时排除方块/玩家。"
HELP_PURGE_1: "删除指定时间的数据。"
HELP_PURGE_2: "例如，\"{0}\" 将删除超过一个月的所有数据，只保留最近30天的数据。"
HELP_PURGE_COMMAND: "删除旧的方块数据。"
HELP_RADIUS_1: "指定半径区域。"
HELP_RADIUS_2: "例子: [r:10] （只会影响你周围十格方块的范围）"
HELP_RELOAD_COMMAND: "重新加载配置文件。"
HELP_RESTORE_COMMAND: "恢复方块数据。"
HELP_ROLLBACK_COMMAND: "回滚方块数据。"
HELP_STATUS: "查看插件状态与版本信息。"
HELP_STATUS_COMMAND: "显示插件状态。"
HELP_TELEPORT: "传送到一个地点。"
HELP_TIME_1: "指定时间。"
HELP_TIME_2: "例子: [t:2w,5d,7h,2m,10s], [t:5d2h], [t:2.50h]"
HELP_USER_1: "指定玩家。"
HELP_USER_2: "例子: [u:Notch], [u:Notch,#enderman]"
INCOMPATIBLE_ACTION: "\"{0}\" 不能与那个动作一起使用。"
INSPECTOR_ERROR: "检查器已经{启用|禁用}了。"
INSPECTOR_TOGGLED: "检查器已{启用|禁用}。"
INTEGRATION_ERROR: "无法{初始化|禁用}与插件 {0} 的交互。"
INTEGRATION_SUCCESS: "与 {0} 的交互已成功{初始化|禁用}。"
INTEGRATION_VERSION: "发现 {0} 的版本不兼容。"
INTERACTIONS_HEADER: "玩家交互"
INVALID_ACTION: "这不是有效的动作。"
INVALID_BRANCH_1: "插件版本无效（未设置分支）。"
INVALID_BRANCH_2: "要继续，请将项目分支设置为 \"开发\"。"
INVALID_BRANCH_3: "运行开发版本的插件可能会导致数据损坏。"
INVALID_CONTAINER: "请检查有效的容器。"
INVALID_DONATION_KEY: "无效的捐赠密钥。"
INVALID_INCLUDE: "\"{0}\" 是无效的方块/实体名称。"
INVALID_INCLUDE_COMBO: "那是无效的方块/实体组合。"
INVALID_RADIUS: "请输入有效的半径。"
INVALID_SELECTION: "没有找到 {0} 的选择。"
INVALID_USERNAME: "玩家名 \"{0}\" 不存在或已离线。"
INVALID_WORLD: "请指定一个有效的世界。"
LATEST_VERSION: "最新版本: {0}"
LINK_DISCORD: "Discord: {0}"
LINK_DOWNLOAD: "下载: {0}"
LINK_PATREON: "Patreon 页面: {0}"
LINK_WIKI_BLOCK: "方块名称: {0}"
LINK_WIKI_ENTITY: "实体名称: {0}"
LOGGING_ITEMS: "{0} 物品等待记录，请稍等..."
LOGGING_TIME_LIMIT: "已达到数据记录时间限制。正在丢弃剩余的数据并关闭。"
LOOKUP_BLOCK: "{0} {放置|破坏} {1}。"
LOOKUP_CONTAINER: "{0} {放入|取出} {1} {2}。"
LOOKUP_HEADER: "{0} 查询结果"
LOOKUP_INTERACTION: "{0} {点击|击杀} {1}."
LOOKUP_ITEM: "{0} {拾起|丢弃} {1} {2}."
LOOKUP_LOGIN: "{0} {登入|离开}."
LOOKUP_PAGE: "第 {0} 页"
LOOKUP_PROJECTILE: "{0} {扔出|发射} {1} {2}."
LOOKUP_ROWS_FOUND: "已找到 {0}{行|行}。"
LOOKUP_SEARCHING: "正在搜索，请稍等..."
LOOKUP_STORAGE: "{0} {放入|取出} {1} {2}."
LOOKUP_TIME: "{0} 前"
LOOKUP_USERNAME: "{0} 已登入为 {1}."
MAXIMUM_RADIUS: "最大{查找|回滚|恢复}半径是 {0}."
MISSING_ACTION_USER: "要使用该标识符，请指定玩家名。"
MISSING_LOOKUP_TIME: "请指定{查找|回滚|恢复}时间."
MISSING_LOOKUP_USER: "请指定查找玩家或查找{方块|半径}."
MISSING_PARAMETERS: "请使用 \"{0}\"。"
MISSING_ROLLBACK_RADIUS: "你没有指定{回滚|恢复}半径。"
MISSING_ROLLBACK_USER: "你没有指定{回滚|恢复}的玩家操作。"
MYSQL_UNAVAILABLE: "无法连接到 MySQL 服务器。"
NETWORK_CONNECTION: "连接方式 {0} {成功|失败}。 使用 {1} {2}。"
NETWORK_TEST: "网络测试数据已成功发送。"
NO_DATA: "没有在 {0} 中找到数据"
NO_DATA_LOCATION: "此位置找不到任何{数据|存取|交互|消息}的记录。"
NO_PERMISSION: "你没有权限执行此操作。"
NO_RESULTS: "未找到结果。"
NO_RESULTS_PAGE: "没有在指定页面找到{结果|数据}。"
NO_ROLLBACK: "没有找到{等待的|上一个}回滚/恢复。"
PATCH_INTERRUPTED: "升级已中断，将在服务器重新启动时重试。"
PATCH_OUTDATED_1: "无法升级早于 {0} 的数据库。"
PATCH_OUTDATED_2: "请升级到受支持的 CoreProtect 版本。"
PATCH_PROCESSING: "正在处理新数据，请稍等..."
PATCH_SKIP_UPDATE: "在 {0} 跳过{表|索引}的{更新|创建|删除}。"
PATCH_STARTED: "正在准备更新到版本 {0}，请稍等..."
PATCH_SUCCESS: "成功升级到版本 {0}。"
PATCH_UPGRADING: "正在进行数据库升级，请稍等..."
PLEASE_SELECT: "请选择: \"{0}\"或\"{1}\"。"
PREVIEW_CANCELLED: "预览已取消。"
PREVIEW_CANCELLING: "正在取消预览..."
PREVIEW_IN_GAME: "你只能在游戏中预览回滚操作。"
PREVIEW_TRANSACTION: "你不能预览{容器|背包}物品交换。"
PURGE_ABORTED: "清除失败，数据库可能已损坏。"
PURGE_ERROR: "无法处理 {0} 数据！"
PURGE_FAILED: "数据清除失败，请稍后再试。"
PURGE_IN_PROGRESS: "正在清除数据，请稍后再试。"
PURGE_MINIMUM_TIME: "你只能清除早于 {0} {天|小时}的数据."
PURGE_NOTICE_1: "请注意，这可能需要一些时间。"
PURGE_NOTICE_2: "完成前不要重新启动服务器。"
PURGE_OPTIMIZING: "正在优化数据库，请稍等..."
PURGE_PROCESSING: "正在处理表 {0} 中的数据..."
PURGE_REPAIRING: "正在尝试修复，这可能需要一定的时间..."
PURGE_ROWS: "已删除 {0} {行|行}数据。"
PURGE_STARTED: "数据清除开始于\"{0}\"。"
PURGE_SUCCESS: "数据清除成功。"
RELOAD_STARTED: "正在重新加载配置文件 - 请稍候。"
RELOAD_SUCCESS: "配置文件已重新加载成功。"
ROLLBACK_ABORTED: "回滚或恢复已中止。"
ROLLBACK_CHUNKS_FOUND: "找到 {0} 个{区块|区块}需要更改。"
ROLLBACK_CHUNKS_MODIFIED: "已修改 {0}/{1} 个{区块|区块}."
ROLLBACK_COMPLETED: "{回滚|恢复|预览}已对以下对象完成: {0}。"
ROLLBACK_EXCLUDED_USERS: "已排除{玩家|玩家}: \"{0}\"。"
ROLLBACK_INCLUDE: "{包括|不包括}{方块|实体|目标}{类型|类型}: \"{0}\"。"
ROLLBACK_IN_PROGRESS: "一个回滚/恢复操作已在进行。"
ROLLBACK_LENGTH: "花费时间: {0}{秒|秒}."
ROLLBACK_MODIFIED: "{已修改|正在修改}{0}."
ROLLBACK_RADIUS: "半径: {0}个{方块|方块}."
ROLLBACK_SELECTION: "半径设置为\"{0}\"。"
ROLLBACK_STARTED: "{回滚|恢复|预览}于 \"{0}\" 开始。"
ROLLBACK_TIME: "时间范围: {0}."
ROLLBACK_WORLD_ACTION: "限制{世界|动作}为 \"{0}\"。"
SIGN_HEADER: "木牌消息"
STATUS_CONSUMER: "数据处理器: {0}个{数据|数据}待处理。"
STATUS_DATABASE: "数据库: 正在使用 {0}."
STATUS_INTEGRATION: "{0}: 合并{启用|禁用}."
STATUS_LICENSE: "协议: {0}"
STATUS_VERSION: "版本: {0}"
TELEPORTED: "传送到 {0}."
TELEPORTED_SAFETY: "将你传送到安全地点。"
TELEPORT_PLAYERS: "只有玩家才能使用传送命令。"
TIME_DAYS: "{0}{天|天}"
TIME_HOURS: "{0}{小时|小时}"
TIME_MINUTES: "{0}{分钟|分钟}"
TIME_SECONDS: "{0}{秒|秒}"
TIME_WEEKS: "{0}{周|周}"
UPDATE_ERROR: "检查更新时出错。"
UPDATE_HEADER: "{0} 更新"
UPDATE_NOTICE: "注意: {0} 现在可用。"
UPGRADE_IN_PROGRESS: "正在进行升级，请稍后再试。"
USER_NOT_FOUND: "玩家\"{0}\"未找到。"
USER_OFFLINE: "玩家\"{0}\"不在线。"
USING_MYSQL: "使用 MySQL 用于数据存储。"
USING_SQLITE: "使用 SQLite 用于数据存储。"
VALID_DONATION_KEY: "有效的捐赠密钥。"
VERSION_NOTICE: "版本 {0} 现在可用。"
VERSION_REQUIRED: "{0} 需要使用 {1} 或更高版本。"
WORLD_NOT_FOUND: "找不到世界 \"{0}\"。"