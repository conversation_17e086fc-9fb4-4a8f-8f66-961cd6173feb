# SuperVanish v6.2.20 - 消息配置
#
# 信息说明:
# <..> 表示 .. 是必需的; [..] 表示 .. 是可选的，可以省略; [] 或 <> 内的 | 表示 '或者'
# 你可以使用 & 字符来表示颜色代码; 例如: '&c这段文字的颜色是红色!'
# 你可以使用 #XXXXXX 来表示十六进制颜色代码。例如: '#663EF6这段文字是紫色的!'
# 你可以使用 %p% 来获取玩家的名字; 例如: '&4&l%p%, 你没有权限执行这个命令!'
# 你可以使用 %other% 来获取目标或原因的名称
# 你可以使用 %d% 来获取玩家的显示名称; 如果你使用 Essentials，显示名称也包含前缀
# 你可以使用 %tab% 来获取玩家在玩家列表(TAB)中的名称
# 你可以使用 %prefix% 来获取玩家的前缀 (需要 Vault)
# 你可以使用 %suffix% 来获取玩家的后缀 (需要 Vault)
# 你可以使用 %group% 来获取玩家的组 (需要 Vault)
# 你可以使用 %nick% 来获取玩家的昵称 (需要 Essentials)
# 某些消息也允许使用不同的、独特的变量
#
# 注意: 通过安装 PlaceholderAPI 或 MVdWPlaceholderAPI 可以获得更多占位符
#
# 空消息不会被发送
# 使用 \n 开始新行
#
# 重要提示:
# 如果你想在消息中使用单引号，必须使用双单引号。
# 你不能在此文件中使用任何制表符(缩进键)，否则 YAML 会在控制台中报错!
# !! 如果编辑此文件后控制台出现错误，请将其粘贴到在线 YAML 解析器中查看是否
# 有任何 YAML 语法错误 !!
Messages:
  NoPermission: '&4拒绝访问! 你没有权限执行此操作。'
  InvalidUsage: '&c用法错误，你可以使用 &6/sv help&c 查看命令列表。'
  VanishMessage: '&e%p% 离开了游戏'
  ReappearMessage: '&e%p% 加入了游戏'
  VanishMessageWithPermission: '&a&6枫影轻语隐身&7 >> %p% 隐身了。'
  ReappearMessageWithPermission: '&a&6枫影轻语隐身&7 >> %p% 现身了。'
  OnVanish: '&a你现在隐身了!'
  OnReappear: '&a你不再隐身了!'
  OnVanishCausedByOtherPlayer: '&a%other% 隐藏了你，你现在隐身了!'
  OnReappearCausedByOtherPlayer: '&a%other% 显示了你，你现在可见了!'
  AlreadyVanishedError: '&c你已经隐身了!'
  NotVanishedError: '&c你没有隐身!'
  SilentJoinMessageForAdmins: '&a&6枫影轻语隐身&7 >> %p% 静默加入了。'
  SilentQuitMessageForAdmins: '&a&6枫影轻语隐身&7 >> %p% 静默离开了。'
  SilentDeathMessage: '&a&6枫影轻语隐身&7 >> %deathmsg%'
  RemindingMessage: '&a你仍然处于隐身状态!'
  ListMessagePrefix: '&a隐身玩家:&f %l'
  ActionBarMessage: '&a你对其他玩家隐身!'
  HideOtherMessage: '&a玩家 &e%other%&a 现在隐身了!'
  ShowOtherMessage: '&a玩家 &e%other%&a 现在可见了!'
  CannotHideOtherPlayer: '&c你没有权限改变 %other% 的可见性!'
  AlreadyInvisibleMessage: '&c玩家 &e%other%&c 已经隐身了!'
  AlreadyVisibleMessage: '&c玩家 &e%other%&c 已经可见了!'
  PluginReloaded: '&a成功重载 SuperVanish (%time%ms)!'
  InvalidSender: '&c你必须是玩家才能执行此命令!'
  PlayerNotOnline: '&c该玩家不在线!'
  PlayerNonExistent: '&c该玩家不存在!'
  ToggledPickingUpItemsOn: '&e拾取物品现在已 &a开启&e。'
  ToggledPickingUpItemsOff: '&e拾取物品现在已 &c关闭&e。'
  UpdateWarning: '&c警告! 重新创建 %updates% 会重置 %changes%。如果你想继续，请使用 &e/sv recreatefiles confirm&c。'
  RecreatedConfig: '&a成功重新创建 %updates%! 请检查 %changes%。'
  NoConfigUpdateAvailable: '&e你的 SuperVanish 文件已是最新版本!'
  RecreationRequiredMsg: '&c&6枫影轻语隐身&7 >> 你的 SuperVanish 文件不是最新版本，你可以使用 &e/sv recreatefiles&c 来重新创建它们
  或使用 /sv recreatefiles dismiss 来忽略此消息。
  重新创建 SuperVanish 的文件可以让你使用插件的新设置和功能。'
  DismissedRecreationWarning: '&e你不再接收关于此重新创建的通知。'
  UndismissedRecreationWarning: '&e你现在接收关于此重新创建的通知。'
  PrintedStacktrace: '&e成功创建堆栈跟踪，请查看控制台!'
  EntityHitDenied: '&6枫影轻语隐身&7 >> &c你在隐身时不能攻击玩家或生物!'
  BlockPlaceDenied: '&6枫影轻语隐身&7 >> &c你在隐身时不能放置方块!'
  BlockBreakDenied: '&6枫影轻语隐身&7 >> &c你在隐身时不能破坏方块!'
  PluginOutdated: '&6枫影轻语隐身&7 >> &c你当前的 SuperVanish 版本已过时。新版本: ''%new%''; 当前版本: ''%current%'''
  DynmapFakeJoin: '%d% 加入了'
  DynmapFakeQuit: '%d% 退出了'
  HelpHeader: "&a<---------------&e 隐身指令-帮助 &a--------------->"
  HelpFormat: "&6%usage% &7- &b%description% &c(%permission%)"
  PlaceholderIsVanishedYes: "是"
  PlaceholderIsVanishedNo: "否"
  PlaceholderVanishPrefix: '&a[V] &r'
  PlaceholderVanishSuffix: ' &r&a[V]'
MessagesVersion: 6.2.20
