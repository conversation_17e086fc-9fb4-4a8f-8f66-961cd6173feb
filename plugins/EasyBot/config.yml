# EasyBot Service Configuration
service:
  # 请参考EasyBot主程序日志输出的地址
  url: "ws://192.168.5.47:26990/bridge"
  # 身份标识,请在服务器生成
  token: "2uhd#rGGIlkZXN7AMjx5KCc79mWBLXlh"
  # 忽略错误
  # 通常情况下 插件无法连接到EasyBot软件时会禁止所有玩家登陆游戏
  # 忽略错误后,玩家可以正常登陆且不进行任何检查(就算服务器处于强制绑定)
  ignore_error: false
  # 是否在服务端接收插件更新推送
  update_notify: true

# 命令设置
command:
  # 允许在此服务器进行绑定
  allow_bind: true

# 消息设置
message:
  # 玩家使用命令开始绑定
  bind_start: |
    §f[§a!§f] 开始绑定,请在群 §e123456 §f输入 '绑定 #code' 进行绑定!
    §f[§c!§f] 请在§a #time §f前完成验证,到时将自动取消绑定!
  bind_success: "§f[§a!§f] 绑定§f §a#account §f(§a#name§f) 成功!"
  bind_fail: "§f[§c!§f] §c绑定失败 #why"
  sync_success: "§f[§a!§f] §f发送成功!"

event:
  # 玩家绑定成功
  # 玩家绑定成功后可以执行命令,以控制台身份执行
  # 可以使用 $player 获取绑定成功的玩家名
  # 可以使用 $account 获取绑定成功的玩家账号 (如:QQ号)
  # 可以使用 $name 获取玩家绑定的账号名(如:QQ名称)
  enable_success_event: false # 如果要在玩家绑定成功时执行命令,请启用此选项
  bind_success:
    - "say $player 绑定成功! QQ: $account 昵称: $name"
  # 当消息同步的消息中含有@消息,且被@的人(绑定了账号的QQ)在线,则显示Title提示
  on_at:
    enable: true
    title: "§a有人@你"
    sub_title: "§a请及时处理"
    # 寻找模式
    # 关闭时: 仅监测消息中真@消息 (如果@的人绑定了账号才触发)
    # 开启时: 消息中包含某个在线玩家的名字就认定为@了该玩家
    find: true
    # 被@时给玩家播放声音
    play_sound: true
    # 声音类型 (如果你设置错了,那就没有声音哦)
    # 只能播放EasyBot预设的声音
    # 0: 铁砧落地
    # 1：咚 (高版本,也好听~)
    # 2: 紫水晶 (高版本,挺好听的~)
    sound: 0

# 调试模式,返回BUG时请将此选项设置为true,然后复现BUG操作再将日志给群主
debug: false

# 可以跳过一些事件
# 跳过一些选项后就算EasyBot那边启用对应功能也无法使用
skip_options:
  # 玩家加入服务器时不将消息发送给EasyBot
  skip_join: false
  # 玩家离开服务器时不将消息发送给EasyBot
  skip_quit: false
  # 玩家聊天时不将消息发送给EasyBot
  skip_chat: false
  # 玩家死亡时不将消息发送给EasyBot
  skip_death: false

# 一些兼容方面的问题
adapter:
  native_rcon:
    # EasyBot执行命令利用了Bukkit的内部rcon接口,一些修改版本的混合端直接基于Fabric或Forge服务器,Rcon相关代码索性直接不实现
    # 如果你的服务器无法使用执行命令模式,请考虑使用NativeRcon,也就是插件连接到服务器开启的RCON
    # 启用后会尝试使用本地RCON连接,而不是借用Bukkit的内部rcon接口
    use_native_rcon: true
    address: "127.0.0.1"
    port: 25575
    password: "huyuze121" # 服务端的RCON密码

# 间歇泉配置
geyser:
  # 是否忽略由 Floodgate 设置的基岩版玩家前缀
  # 注意：
  # - 如果之前未启用但现在启用，一些已经绑定的基岩版玩家可能需要重新绑定。（为什么你自己想）
  # - 启用时（True），如果基岩版玩家的前缀为 BE_。
  #   示例：BE_MiuxuE -> MiuxuE
  ignore_prefix: false