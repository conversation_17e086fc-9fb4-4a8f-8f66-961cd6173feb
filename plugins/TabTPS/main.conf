# TabTPS main plugin settings

# Should the plugin check GitHub for updates on startup?
update-checker=true
# How many milliseconds in between updates
update-rates {
    tab=250
    action-bar=250
    boss-bar=250
}
# These memory pools will not be displayed in the '/memory' command
ignored-memory-pools=[
    "CodeHeap 'profiled nmethods'",
    "Code Cache",
    "Non-Heap Memory Usage",
    "CodeHeap 'non-profiled nmethods'",
    "Compressed Class Space",
    Metaspace,
    "CodeHeap 'non-nmethods'"
]
# A player may only have a single display config active at once, even if they have permissions for multiple
# This list allows defining the order in which permissions will be checked
permission-priorities=[
    "tabtps.defaultdisplay"
]
# Colors used in the command help menus
help-colors {
    primary="#00a3ff"
    highlight=white
    alternate-highlight="#284fff"
    text=gray
    accent="dark_gray"
}
