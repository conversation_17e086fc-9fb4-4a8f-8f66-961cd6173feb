# TabTPS display configuration
# 
#   Available modules: [tps, mspt, memory, ping, cpu, players]
#   Modules are configured in comma separated format, i.e. "cpu,tps,mspt", "ping", or "" (no modules)

# The permission required to use this display config
# Can be an empty string ("") to require no permission
permission="tabtps.defaultdisplay"
action-bar-settings {
    allow=true
    enable-on-login=false
    modules="tps,mspt,ping"
    theme=default
    # The text used to separate modules. Accepts MiniMessage (i.e. " <gray>|</gray> ")
    separator=" "
}
boss-bar-settings {
    allow=true
    enable-on-login=false
    modules="tps,mspt,ping"
    theme=default
    # Available colors: [PINK, RED, GREEN, BLUE, YELLOW, PURPLE, WHITE]
    colors {
        low-performance=RED
        medium-performance=YELLOW
        good-performance=GREEN
    }
    # Set the mode for determining boss bar fill.
    # Possible values: [MSPT, TPS, REVERSE_MSPT, REVERSE_TPS]
    fill-mode=MSPT
    # What kind of overlay should be used for the boss bar?
    # Must be one of: [PROGRESS, NOTCHED_6, NOTCHED_10, NOTCHED_12, NOTCHED_20]
    overlay="NOTCHED_20"
    # The text used to separate modules. Accepts MiniMessage (i.e. " <gray>|</gray> ")
    separator=" "
}
tab-settings {
    allow=true
    enable-on-login=false
    header-modules=""
    footer-modules="tps,mspt"
    theme=default
    # The text used to separate modules. Accepts MiniMessage (i.e. " <gray>|</gray> ")
    separator=" "
}
