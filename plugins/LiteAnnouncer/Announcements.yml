#是否随机播报公告
#启用后优先级列表中的公告将随机播报
Random-Broadcast: false

#公告的优先级，顺序是从上往下
Priority:
- example-announcement

Announcements:
    example-announcement:
        #公告的名字
        Name: '示例公告'
        #接收本公告的权限（删除掉本选项即为无需权限）
        Permission: liteannouncer.announcement.example
        #延迟公告（单位: 秒 允许使用小数）
        Delay: 3600
        #公告的消息内容
        Messages:
          - '&f'
          - '&e各位枫影轻语玩家，请自觉遵守玩家管理守则%rule_link%'
          - '&61.请勿通过非正常手段刷物品'
          - '&62.在交流过程中，严禁出现辱骂、恶意攻击等不友善言论。'
          - '&63.管理员应当秉持公正原则，不得滥用权力对玩家施加不合理的惩罚。'
          - '&64.不得擅自篡改枫影轻语客户端程序。'
          - '&f'
        #标题信息
        Titles:
            #是否启用
            Enabled: false
            #标题设置
            Titles-Setting:
                'example_1':
                    #显示延迟（秒）
                    Delay: 0.3
                    #淡入时间
                    Fade-In: 0
                    #停留时间
                    Stay: 0.4
                    #淡出时间
                    Fade-Out: 0
                    #主标题
                    Title: '&a&l这里是公告的标题信息~'
                    #子标题
                    Sub-Title: '&e&l这里是公告的子标题~'
                'example_2':
                    Delay: 0.3
                    Fade-In: 0
                    Stay: 0.4
                    Fade-Out: 0
                    Title: '&b&l这里是公告的标题信息~'
                    Sub-Title: '&f&l这里是公告的子标题~'
            #任务顺序
            Task-Sequence:
            - 'example_1'
            - 'example_2'
            - 'example_1'
            - 'example_2'
            - 'example_1'
            - 'example_2'
        #动作栏消息
        ActionBars: 
            Enabled: false
            #任务顺序
            Task-Sequence:
            #本文消息列表（格式： [消息]:[延迟]）
            - '&a&l这是动作栏消息的示范本文': 0.2
            - '&e&l这&a&l是动作栏消息的示范本文': 0.2
            - '&a&l这&e&l是&a&l动作栏消息的示范本文': 0.2
            - '&a&l这是&e&l动&a&l作栏消息的示范本文': 0.2
            - '&a&l这是动&e&l作&a&l栏消息的示范本文': 0.2
            - '&a&l这是动作&e&l栏&a&l消息的示范本文': 0.2
            - '&a&l这是动作栏&e&l消&a&l息的示范本文': 0.2
            - '&a&l这是动作栏消&e&l息&a&l的示范本文': 0.2
            - '&a&l这是动作栏消息&e&l的&a&l示范本文': 0.2
            - '&a&l这是动作栏消息的&e&l示&a&l范本文': 0.2
            - '&a&l这是动作栏消息的示&e&l范&a&l本文': 0.2
            - '&a&l这是动作栏消息的示范&e&l本&a&l文': 0.2
            - '&a&l这是动作栏消息的示范本&e&l文': 0.2
            - '&a&l这是动作栏消息的示范本文': 0.2
            - '&a&l这是动作栏消息的示范本&e&l文': 0.2
            - '&a&l这是动作栏消息的示范&e&l本&a&l文': 0.2
            - '&a&l这是动作栏消息的示&e&l范&a&l本文': 0.2
            - '&a&l这是动作栏消息的&e&l示&a&l范本文': 0.2
            - '&a&l这是动作栏消息&e&l的&a&l示范本文': 0.2
            - '&a&l这是动作栏消&e&l息&a&l的示范本文': 0.2
            - '&a&l这是动作栏&e&l消&a&l息的示范本文': 0.2
            - '&a&l这是动作&e&l栏&a&l消息的示范本文': 0.2
            - '&a&l这是动&e&l作&a&l栏消息的示范本文': 0.2
            - '&a&l这是&e&l动&a&l作栏消息的示范本文': 0.2
            - '&a&l这&e&l是&a&l动作栏消息的示范本文': 0.2
            - '&e&l这&a&l是动作栏消息的示范本文': 0.2
            - '&a&l这是动作栏消息的示范本文': 0.2
            - '&r': 0.2