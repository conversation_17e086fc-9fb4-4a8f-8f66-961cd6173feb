ConfigVersion: 19

#插件是否应该检查新更新，如果设置为false，UpdateDownload将不起作用
UpdateCheck: true
#插件是否应该在有新版本可用时自动下载和安装
UpdateDownload: true

#插件是否应该记录玩家总共AFK时长的统计信息
EnableStatistics: true

#如果您不想使用某个命令，请将其留空，这些命令在将玩家设置为AFK的过程中运行
#如果您尝试传送或移动玩家等，这可能会导致问题
#动作命令必须使玩家不再AFK或将其从服务器中移除
#可以通过用分号分隔来输入多个命令
#例如 "tp [PLAYER] 0 100 0;msg [PLAYER] you have been teleported while AFK!"
Commands:
  Action: ""
  AFKStart: ""
  AFKStop: ""
  Warn: ""

#服务器必须有这么多玩家才会运行动作命令
#当服务器达到此限制时，AFK时间超过正常动作时间的玩家将被踢出
#还建议您编辑警告消息，告诉玩家当服务器达到x个玩家时他们将被踢出
#设置为0意味着玩家在达到动作时间时总是会被执行动作
ActionPlayerRequirement: 0

#会话长度是指玩家需要离线多长时间才能重置其AFK状态
#如果玩家在AFK时退出并在此分钟数之前重新加入，他们将在加入时自动设置为AFK
#此值以分钟为单位，您可以输入分钟的小数，例如 0.5 = 30秒
SessionLength: 5.0

#启用此设置将使AFKPlus更新玩家在essentials中的AFK状态以匹配其AFKPlus AFK状态
#这对于通过检查Essentials来检查玩家是否AFK的其他插件可能很有用
EssentialsAFKHook: false

#玩家收到警告时播放的声音
#如果您不想要声音，请设置为空白，例如 ""
#最新spigot版本的声音列表可在以下位置找到
#https://github.com/CryptoMorin/XSeries/blob/master/src/main/java/com/cryptomorin/xseries/XSound.java#L64
WarningSound: "ENTITY_PLAYER_LEVELUP"

#玩家变为AFK时播放的声音
#如果您不想要声音，请设置为空白
AFKStartSound: "BLOCK_ANVIL_HIT"

#如果玩家不与世界互动，将被设置为AFK
#什么应该被算作互动？
#设置为false以忽略
EnabledDetections:
  #发送聊天消息
  Chat: true
  #玩家头部/身体原地旋转
  Look: true
  #在x,y,z方向移动
  Move: true
  #攻击实体
  Attack: true
  #运行命令
  Command: true
  #点击（空气或方块）
  ClickInteract: true
  #踩踏物品（红石矿、绊线、压力板或在土壤上跳跃）
  PhysicalInteract: true
  #与物品栏或实体GUI交互
  GUI: true
  #放置方块
  BlockPlace: true
  #破坏方块
  BlockBreak: true

#移动幅度意味着玩家需要移动或查看一定距离才能被标记为非AFK
MovementMagnitude:
  #是否应该启用移动幅度系统
  Enabled: false
  #玩家每秒需要移动多远才能被算作移动
  PositionTrigger: 1.0
  #玩家每秒需要查看什么角度才能被算作查看
  LookTrigger: 3.0

Protections:
  #启用时，AFK玩家在AFK时将无法在x和z方向移动
  #这可以防止他们被其他玩家撞击而脱离AFK状态
  #这在AFKPlusPrefix中实现得更好，但该插件与所有服务器不兼容
  #所以这是修复的不同实现
  Bump: false
  #启用时，AFK玩家将对其他玩家的攻击无敌
  HurtByPlayer: true
  #启用时，AFK玩家将对怪物的攻击无敌
  HurtByMob: true
  #与HurtByPlayer相同，但阻止任何非玩家伤害
  HurtByOther: true
  #启用时，如果AFK玩家是生成的原因，怪物将不会生成
  #这仅适用于自然生成和刷怪笼生成
  #这可能会在高端服务器上造成轻微延迟
  MobSpawning: false
  #启用时，AFK玩家不会被怪物锁定
  MobTargeting: false

#当玩家AFK状态改变时，谁应该收到消息
#Self = 状态已改变的玩家
#Vanish = 如果状态已改变的玩家处于隐身状态
Broadcast:
  Console: true
  OtherPlayers: true
  Self: true
  Vanish: false
  DiscordSRV:
    Enabled: false
    Channel: "global"

#激进AFK检测是试图阻止人们使用AFK农场或机器的尝试
#它通过检查玩家是否在查看（使用鼠标环顾四周）和变换（使用wasd移动）两方面都在移动来做到这一点
#如果玩家只在其中一种方式移动，他们将停止触发在"EnabledDetections"中启用的检测
#这将强制他们进入AFK状态，除非他们开始在查看和变换两方面都移动
#这可能过于严格，只有在您知道有人试图通过在仍然AFK时触发检测来绕过AFKPlus时才应启用
AggressiveAFKDetection: true

#将此设置为true意味着在计算是否应该跳过夜晚时将忽略AFK玩家
#有关其工作原理的信息，请参阅此页面
#https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/Player.html#setSleepingIgnored(boolean)
IgnoreAFKPlayersForSleep: true

#权限可以是true/false/value
#0 = false, 1 = true 或任何整数值
#例如 AFKSelf = 0 或 1，而TimeToAFK可以是任何秒数
#动作和警告时间是玩家AFK的时长，而不是自上次交互以来的时长

#您不再需要列出与下面默认值不同的值
# Default=0, Priority=0, AFKSelf=1, AFKOthers=0, FakeAFK=0, TimeToAFK=30, TimeToWarning=90, TimeToAction=120, CanUpdate=0 and CanReload=0
Permissions:
  afkplus,player:
    #谁应该在不被给予的情况下获得此权限，0 = 没有人，1 = 所有人，2 = 管理员
    Default: 1
    #如果玩家有多个权限，他们将获得优先级最高的权限
    Priority: 1
    #玩家是否可以使用/afk命令将自己设置为AFK
    AFKSelf: 1
    #玩家是否可以使用/afk命令将其他人设置为AFK
    AFKOthers: 0
    #在自动设置为AFK之前需要多少秒的不活动时间
    #如果玩家不应该自动置于AFK状态，请设置为-1
    TimeToAFK: 600
    #在警告玩家将要采取行动之前需要多少秒的AFK时间
    #设置为-1以禁用此权限的警告
    TimeToWarning: 90
    #在对玩家采取行动之前需要多少秒的AFK时间
    #设置为-1以禁用此权限的行动
    TimeToAction: 120
    #此玩家是否可以使用更新命令安装新版本
    CanUpdate: 0
    #此玩家是否可以使用重载命令重新加载配置文件
    CanReload: 0
  afkplus,vip:
    #如果玩家有多个权限，他们将获得优先级最高的权限
    Priority: 2
    #在自动设置为AFK之前需要多少秒的不活动时间
    #如果玩家不应该自动置于AFK状态，请设置为-1
    TimeToAFK: 60
    #在警告玩家将要采取行动之前需要多少秒的AFK时间
    #设置为-1以禁用此权限的警告
    TimeToWarning: 120
    #在对玩家采取行动之前需要多少秒的AFK时间
    #设置为-1以禁用此权限的行动
    TimeToAction: 150
  afkplus,admin:
    #谁应该在不被给予的情况下获得此权限，0 = 没有人，1 = 所有人，2 = 管理员
    Default: 2
    #如果玩家有多个权限，他们将获得优先级最高的权限
    Priority: 3
    #玩家是否可以使用/afk命令将其他人设置为AFK
    AFKOthers: 1
    #玩家是否可以启用FakeAFK状态以通过显示为AFK来隐藏自己
    FakeAFK: 1
    #在自动设置为AFK之前需要多少秒的不活动时间
    #如果玩家不应该自动置于AFK状态，请设置为-1
    TimeToAFK: 90
    #在警告玩家将要采取行动之前需要多少秒的AFK时间
    #设置为-1以禁用此权限的警告
    TimeToWarning: -1
    #在对玩家采取行动之前需要多少秒的AFK时间
    #设置为-1以禁用此权限的行动
    TimeToAction: -1
    #此玩家是否可以使用更新命令安装新版本
    CanUpdate: 1
    #此玩家是否可以使用重载命令重新加载配置文件
    CanReload: 1