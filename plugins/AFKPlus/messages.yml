ConfigVersion: 7

#主要和次要颜色将替换任何消息中的 &p 和 &s
PrimaryColor: "&6"
SecondaryColor: "&e"

#此前缀将替换所有消息中的 {PREFIX}
Prefix: "&6枫影轻语&7 >> "

#用于时间值的语言，如"从现在开始60分钟"
#可接受的值可在 http://www.ocpsoft.org/prettytime/#i18n 找到
#请使用语言旁边的缩写，例如 DE 表示德语，FR 表示法语
PrettyTimeLocale: "en"

#当AFK开始和停止时发送给所有玩家的消息
Broadcast:
  Start: "{PREFIX} &s{PLAYER}&p 挂机了"
  #您也可以添加 {TIME} 变量，它将被玩家AFK的时长替换
  Stop: "{PREFIX} &s{PLAYER}&p 回来了"

#这些消息发送给AFK状态正在改变的个人
Self:
  Start: "{PREFIX} &s您&p 挂机了 按空格键取消挂机"
  #您也可以添加 {TIME} 变量，它将被玩家AFK的时长替换
  Stop: "{PREFIX} &s您&p 回来了"
  #当玩家加入并立即设置为AFK时发送的消息
  Resume: "{PREFIX} &p您的挂机状态已恢复"

Updater:
  NoUpdate: "&p没有可用的更新"
  UpdateFound: "&pAFKPlus有可用的更新"
  UpdateDownloading: "&p发现更新并正在下载，将在下次服务器重启时安装"

Reload: "&p配置文件已重新加载，任何错误将打印到控制台"

#这些字符串用于 "/afkplus player name" 命令
Player:
  AFK: "&s{PLAYER}&p 自 &s{TIME} 以来一直处于AFK状态"
  NotAFK: "&s{PLAYER}&p 不在AFK状态"
  NotOnline: "&s{PLAYER}&p 当前不在线"
  Stats: "&p他们总共花费了 &s{TIME}&p 在AFK状态"
  #分配给此玩家的当前权限将附加到此字符串
  Permission: "&p玩家权限：&s"
  #只有在启用AggressiveAFKDetection时才会显示这两个字符串
  Active: "&p根据激进AFK检测，此玩家没有表现出可疑行为"
  Inactive: "&s此玩家的行为方式可能意味着他们试图避免AFK检测，已被标记为非活跃状态"

#当玩家AFK时间足够长以发送警告时发送
Warning: "{PREFIX}&p 您将因AFK而被踢出！"

Help:
  Help: "以下是命令列表及其用途：\n"
  AFKPlus: "\"/afkplus\"\n显示插件信息"
  AFKPlusHelp: "\"/afkplus [help]\"\n显示所有命令及其用途"
  AFKPlusUpdate: "\"/afkplus [update]\"\n检查并安装任何可用的更新"
  AFKPlusPlayer: "\"/afkplus player [玩家名]\"\n告诉您玩家的AFK状态，如果他们处于AFK状态，还会告诉您他们AFK了多长时间"
  AFK: "\"/afk [玩家名]\"\n不带玩家名将切换您的AFK状态\n带名字将尝试切换该玩家的AFK状态"

Error:
  MustBePlayer: "&s您必须是玩家才能执行此操作！"
  PlayerNotFound: "&s未找到玩家"
  NotPermitted: "&s您没有权限执行此操作"