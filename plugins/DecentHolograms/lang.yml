prefix: '&6枫影轻语全息图 &7>> '
no_perm: '{prefix}&c您没有权限使用此功能。'
only_player: '{prefix}&c此操作只能由玩家执行。'
reloaded: '{prefix}&a成功重载，耗时 %1$d 毫秒！'
new_version_available: '&f有新版本的 &3DecentHolograms &f可用。请从以下地址下载：'
command:
  use_help: '{prefix}使用 &b/holograms help&7 查看可用命令。'
  usage: '{prefix}用法：&b%1$s'
  unknown_sub_command: '{prefix}未知的子命令。'
hologram:
  does_not_exist: '{prefix}&c不存在该名称的全息图。'
  already_exists: '{prefix}&c该名称的全息图已存在。'
  invalid_name: '{prefix}&c无效名称 ''%1$s''，只能使用字母数字字符、下划线和短横线。'
  created: '{prefix}全息图已创建！'
  cloned: '{prefix}全息图已克隆！'
  deleted: '{prefix}全息图已删除！'
  updated: '{prefix}全息图已更新！'
  renamed: '{prefix}全息图已重命名！ &7(&b%1$s&7 -> &b%2$s&7)'
  teleported: '{prefix}传送成功！'
  moved: '{prefix}全息图已移动！'
  aligned: '{prefix}全息图已对齐！'
  align_self: '{prefix}无法将全息图对齐到自身！'
  align_axis: '{prefix}该轴不存在！'
  down_origin_set: '{prefix}原点已设置为 &b''%1$s''&7！'
  down_origin_does_not_exist: '{prefix}向下原点值必须为 true 或 false！'
  facing_set: '{prefix}朝向已设置！'
  flag_add: '{prefix}标志 &b"%1$s"&7 已添加！'
  flag_remove: '{prefix}标志 &b"%1$s"&7 已移除！'
  permission_set: '{prefix}权限已设置！'
  permission_removed: '{prefix}权限已移除！'
  display_range_set: '{prefix}显示范围已设置！'
  update_range_set: '{prefix}更新范围已设置！'
  update_interval_set: '{prefix}更新间隔已设置！'
  disabled: '{prefix}全息图已禁用！'
  already_disabled: '{prefix}全息图已经被禁用！'
  enabled: '{prefix}全息图已启用！'
  already_enabled: '{prefix}全息图已经被启用！'
page:
  added: '{prefix}页面已添加！'
  add_failed: '{prefix}页面添加失败！'
  inserted: '{prefix}页面已插入！'
  insert_failed: '{prefix}页面插入失败！'
  deleted: '{prefix}页面已删除！'
  swapped: '{prefix}页面已交换！'
  swap_self: '{prefix}&c无法将页面与自身交换！'
  swap_failed: '{prefix}&c页面交换失败。'
  does_not_exist: '{prefix}&c该页面不存在。'
action:
  click_type_does_not_exist: '{prefix}&c该点击类型不存在。'
  does_not_exist: '{prefix}&c该动作不存在。'
  added: '{prefix}动作已添加。'
  removed: '{prefix}动作已移除。'
  cleared: '{prefix}动作已清除。'
  no_actions: '{prefix}该点击类型上没有设置动作。'
line:
  added: '{prefix}行已添加！'
  add_failed: '{prefix}&c添加行失败。'
  set: '{prefix}行已设置！'
  edit: '{prefix}&a&l&n点击编辑该行！'
  edit_hover: '&r%1$s'
  inserted: '{prefix}行已插入！'
  insert_failed: '{prefix}&c插入行失败。'
  removed: '{prefix}行已移除！'
  swapped: '{prefix}行已交换！'
  swap_self: '{prefix}&c无法将行与自身交换！'
  swap_failed: '{prefix}&c行交换失败。'
  aligned: '{prefix}行已对齐！'
  align_self: '{prefix}无法将行对齐到自身！'
  align_axis: '{prefix}该轴不存在！'
  height_set: '{prefix}行高度已设置！'
  offsetx_set: '{prefix}行X偏移已设置！'
  offsetz_set: '{prefix}行Z偏移已设置！'
  flag_added: '{prefix}标志 &b"%1$s"&7 已添加！'
  flag_removed: '{prefix}标志 &b"%1$s"&7 已移除！'
  does_not_exist: '{prefix}&c该行不存在。'
  permission_set: '{prefix}权限已设置！'
  permission_removed: '{prefix}权限已移除！'
  facing_set: '{prefix}朝向已设置！'
feature:
  does_not_exist: '{prefix}&c功能 "%1$s" 不存在。'
  enabled: '{prefix}功能 &b"%1$s"&7 已启用！'
  already_enabled: '{prefix}&c功能 "%1$s" 已经被启用！'
  disabled: '{prefix}功能 &b"%1$s"&7 已禁用！'
  already_disabled: '{prefix}&c功能 "%1$s" 已经被禁用！'
  reloaded: '{prefix}功能 &b"%1$s"&7 已重载！'
