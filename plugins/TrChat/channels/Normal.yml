Options:
  Target: ALL
  Always-Listen: true

Formats:
  - condition: ~
    priority: 100
    msg:
      default-color: '&r&l'
      hover: '&6发送日期: &e%server_time_HH:mm:ss%'
    prefix:
      world:
        text: '&7[ %playertitle_use% &7] &7[ &e%multiverse_world_alias% &7]&r'
        hover: |-
          &f
          &8▪ &e位置: &6%multiverse_world_alias%, &2%player_x%/%player_y%/%player_z%
          &8▪ &eIP地址：&6%potatoipdisplay_province% %potatoipdisplay_city%
          &6▶ &e点我发送TPA请求
          &f
        command: '/tpa %player_name%'
      part-before-player:
        text: ' '
      player:
        - condition: 'player op'
          text: '<rainbow>%player_name%'
          hover: |-
            &f
            &8▪ &7Ping: &3%player_ping% ms
            &8▪ &7血量: &c%player_health_rounded% ❤
            &f
            &6▶ &e点我私聊OP
            &f
          suggest: '/tell %player_name% '
        - text: '<g:#d9afd9:#97d9e1>%player_name%'
          hover: |-
            &f
            &8▪ &7Ping: &3%player_ping% ms
            &8▪ &7血量: &c%player_health_rounded% ❤
            &f
            &6▶ &e点我私聊该玩家
            &f
          suggest: '/tell %player_name% '
      part-before-msg:
        text: ' &7>> '
    suffix:
      example:
        text: ''