{"skinsrestorer.help_skin": "Modifie ton propre skin.", "skinsrestorer.help_skins": "Ouvre l'interface graphique utilisateur des skins.", "skinsrestorer.help_sr": "Commandes admin pour SkinsRestorer.", "skinsrestorer.help_skin_help": "Affiche cette commande d'aide.", "skinsrestorer.help_skin_set": "Changez votre skin.", "skinsrestorer.help_skin_set_other": "Définit le skin d'un joueur ciblé.", "skinsrestorer.help_skin_set_url": "Change votre skin depuis une URL.", "skinsrestorer.help_skin_clear": "Effacez votre skin.", "skinsrestorer.help_skin_clear_other": "Efface le skin d'un joueur ciblé.", "skinsrestorer.help_skin_random": "<PERSON>ne un skin aléatoire.", "skinsrestorer.help_skin_random_other": "Définit un skin aléatoire pour un joueur ciblé.", "skinsrestorer.help_skin_search": "Recherchez un skin que vous voulez.", "skinsrestorer.help_skin_update": "Met à jour votre skin.", "skinsrestorer.help_skin_update_other": "Met à jour le skin d'un joueur cible.", "skinsrestorer.help_skin_undo": "R<PERSON>tablir votre skin sur le skin précédent.", "skinsrestorer.help_skin_undo_other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le skin d'un joueur ciblé sur le skin précédent.", "skinsrestorer.help_skin_favourite": "Enregistre votre skin en tant que favori.", "skinsrestorer.help_skin_favourite_other": "Enregistre le skin d'un joueur ciblé comme favori.", "skinsrestorer.help_sr_reload": "Recharge le fichier de configuration.", "skinsrestorer.help_sr_status": "Vérifie les services d'API requis pour le plugin.", "skinsrestorer.help_sr_drop": "Supprime les données du joueur ou du skin de la base de données.", "skinsrestorer.help_sr_info": "Affiche des informations sur un joueur ou un skin.", "skinsrestorer.help_sr_apply_skin": "Réappliquer le skin pour l'utilisateur ciblé.", "skinsrestorer.help_sr_create_custom": "<PERSON><PERSON><PERSON> un skin personnalisé à l'échelle du serveur.", "skinsrestorer.help_sr_purge_old_data": "Supprimer les anciennes données de skin datant de plus de x jours avant.", "skinsrestorer.help_sr_dump": "Téléverser les données de support sur bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "Les URLs doivent être entre guillemets. Exemple : <yellow>/skin set \"https://example.com/skin.png\"</yellow> (<PERSON><PERSON> pouvez appuyer sur la touche MAJ pour compléter automatiquement les guillemets)", "skinsrestorer.success_skin_change": "Votre skin a été changé.", "skinsrestorer.success_skin_change_other": "Vous avez changé le skin de <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "Votre skin <yellow><skin></yellow> a été restauré à un skin du <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "Le skin <yellow><skin></yellow> de <yellow><name></yellow> a été restaurée à un skin du <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "Votre skin <yellow><skin></yellow> a été défini comme favori.", "skinsrestorer.success_skin_favourite_other": "Le skin <yellow><skin></yellow> de <yellow><name></yellow> a été défini comme favori.", "skinsrestorer.success_skin_unfavourite": "Votre skin préféré <yellow><skin></yellow> du <yellow><timestamp></yellow> a été retiré.", "skinsrestorer.success_skin_unfavourite_other": "Le skin préféré <yellow><skin></yellow> de <yellow><name></yellow> du <yellow><timestamp></yellow> a été retiré.", "skinsrestorer.success_skin_clear": "Votre skin a été effacé.", "skinsrestorer.success_skin_clear_other": "Skin effacé pour le joueur <yellow><name></yellow>.", "skinsrestorer.success_updating_skin": "Votre skin a été mis à jour.", "skinsrestorer.success_updating_skin_other": "Skin mis à jour pour le joueur <yellow><name></yellow>.", "skinsrestorer.success_admin_applyskin": "Le skin du joueur a été actualisé !", "skinsrestorer.success_admin_createcustom": "Le skin <yellow><skin></yellow> a été créé !", "skinsrestorer.success_admin_setcustomname": "Le nom du skin <yellow><skin></yellow> a été défini sur <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "<type> données supprimées pour <target>.", "skinsrestorer.success_admin_reload": "La configuration et les traductions ont bien été rafraîchies !", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green>Cliquez pour utiliser <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> du <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green>Cliquez pour utiliser <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> du <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Erreur<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "Une erreur s'est produite lors de la demande de données du skin, veuillez réessayer plus tard !", "skinsrestorer.error_no_undo": "Vous n'avez pas de skin à restaurer !", "skinsrestorer.error_no_skin_to_favourite": "Vous n'avez pas skin à mettre en favori !", "skinsrestorer.error_skin_disabled": "Ce skin est désactivé par un administrateur.", "skinsrestorer.error_skinurl_disallowed": "Ce domaine n'a pas été autorisé par l'administrateur.", "skinsrestorer.error_updating_skin": "Une erreur est survenue pendant l'actualisation de votre skin. Veuillez réessayer plus tard !", "skinsrestorer.error_updating_url": "Vous ne pouvez pas mettre à jour les skins d'URL personnalisés ! <newline><red>Demander à nouveau en utilisant  /skin <URL>", "skinsrestorer.error_updating_customskin": "Le skin ne peut pas être mis à jour parce qu'il est personnalisé.", "skinsrestorer.error_admin_applyskin": "Le skin du joueur n'a PAS pu être rafraîchie !", "skinsrestorer.error_ms_full": " L'API MineSkin n'a pas réussi à envoyer votre skin. Veuillez réessayer plus tard.", "skinsrestorer.error_ms_api_failed": "L'API de MineSKin est surchargé, Veuillez réessayer plus tard !", "skinsrestorer.error_ms_api_key_invalid": "Clé d'API MineSkin invalide ! Contactez l'administrateur du serveur !", "skinsrestorer.error_ms_unknown": "<PERSON><PERSON><PERSON> de MineSkin inconnu !", "skinsrestorer.error_no_history": "Vous n'avez pas d'historique de skin !", "skinsrestorer.error_no_favourites": "Vous n'avez pas skin favori !", "skinsrestorer.not_connected_to_server": "<red>Vous n'êtes connecté à aucun serveur.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Vérification des services nécessaires au bon fonctionnement de SR...", "skinsrestorer.admincommand_status_uuid_api": "<gray>UUID API Opérationnelle : <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>Profil API Opérationnelle : <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>Le plugin est actuellement dans un état de fonctionnement.", "skinsrestorer.admincommand_status_degraded": "<green>Le plugin est dans un état dégradé, certaines fonctionnalités peuvent ne pas fonctionner pleinement.", "skinsrestorer.admincommand_status_broken": "<red>Le plugin ne peut actuellement pas récupérer de nouveaux skins.<newline>La connexion est probablement bloquée à cause d'un pare-feu.<newline>S'il vous plait, consultez : https://skinsrestorer.net/firewall pour plus d'informations", "skinsrestorer.admincommand_status_firewall": "<red>Les connexions sont probablement bloquées à cause d'un pare-feu.<newline>Veuillez lire https://skinsrestorer.net/firewall pour plus d'informations.", "skinsrestorer.admincommand_status_summary_server": "<gray>Serveur : <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Mode du Proxy : <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Commit : <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON><PERSON> <yellow><player></yellow> non trouvé.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> non trouvé.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Nous n'avons pas été capable de contacter Mojang afin d'obtenir l'UUID du joueur", "skinsrestorer.admincommand_info_checking": "<gray>Collecte des données demandées...", "skinsrestorer.admincommand_info_player": "<gray>UUID du joueur : <gold><uuid><newline><gray>Identifiant de skin : <gold><gold><identifier><newline><gray>Variante de skin : <gold><variant><newline><gray>Type de skin : <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red><PERSON><PERSON> spécifier un UUID d'un joueur.", "skinsrestorer.admincommand_info_no_set_skin": "<red>Le joueur n'a pas de skin défini.", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Skin codé en dur : <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Skin personnalisé : <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>Skin du Joueur : <gold><skin><newline><gray><PERSON><PERSON> : <gold><timestamp><newline><gray>Expire : <gold><expires>", "skinsrestorer.admincommand_purgeolddata_success": "<green>Suppression des skins avec succès !", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>Une erreur est survenue lors de la suppression des anciens skins !", "skinsrestorer.admincommand_dump_uploading": "<green>Téléversement des données vers bytebin.lucko.me...", "skinsrestorer.admincommand_dump_error": "<red>Une erreur est survenue lors du téléversement des données vers bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red>Les commandes ont été désactivées pour ce serveur <server>.", "skinsrestorer.command_unknown_player": "<PERSON><PERSON><PERSON> inconnu : <name>", "skinsrestorer.command_no_targets_supplied": "Aucun joueur ciblé n'a été fourni.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON><PERSON><PERSON><dark_gray> : <red>Vous n'avez pas les permissions pour mettre ce skin.", "skinsrestorer.player_has_no_permission_url": "<dark_red><PERSON><PERSON><PERSON><dark_gray> : <red>Vous n'avez pas les permissions de mettre des skins par URL.", "skinsrestorer.not_premium": "<dark_red><PERSON><PERSON><PERSON><dark_gray> : <red>Aucun joueur Premium avec ce nom existe.", "skinsrestorer.only_allowed_on_console": "<dark_red><PERSON><PERSON>ur<dark_gray> : <red>Seulement la console peut exécuter cette commande !", "skinsrestorer.only_allowed_on_player": "<dark_red><PERSON><PERSON><PERSON><dark_gray> : <red>Seulement les joueurs peuvent exécuter cette commande !", "skinsrestorer.invalid_player": "<dark_red>Erreur<dark_gray> : <red><input> n'est pas un URL ou identifiant valide.", "skinsrestorer.skin_cooldown": "<dark_red>Erreur<dark_gray> : <red>Vous pourrez changer votre skin dans <yellow><time></yellow>", "skinsrestorer.ms_uploading_skin": "<dark_green>T<PERSON>léversement du skin, Veuillez patienter... (<PERSON><PERSON> peut prendre du temps)", "skinsrestorer.wait_a_minute": "<dark_red><PERSON><PERSON><PERSON><dark_gray> : <red>S'il vous plait, attendez une minute avant de réessayer de nouveau. (<PERSON><PERSON>)", "skinsrestorer.skinsmenu_open": "<dark_green>Ouverture du menu des skins...", "skinsrestorer.skinsmenu_title_select": "<blue>Sélection des menus", "skinsrestorer.skinsmenu_title_main": "<blue><PERSON><PERSON> - <PERSON> <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue>Menu de l'Historique - Page <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue> <PERSON><PERSON> - Page <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Prochaine Page</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Page Précédente</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Retirer le Skin</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>Clique pour choisir ce skin", "skinsrestorer.skinsmenu_history_lore": "<blue>Skin du <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>MAJ + cliquez pour définir comme favori", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>MAJ + cliquez pour supprimer des favoris", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>Favori depuis le <time>", "skinsrestorer.skinsmenu_no_permission": "<red>Vous n'avez pas les permissions pour mettre ce skin.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON> des Skins</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>Menu de l'Historique</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON> Favoris</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>Page Précédente</gray> <bold>«</bold>", "skinsrestorer.no_skin_data": "<dark_red><PERSON><PERSON><PERSON><dark_gray> : <red>Au<PERSON>ne donnée de skin trouvée ! Ce joueur a-t-il un skin ?", "skinsrestorer.unsupported_java": "<dark_red>La version Java (<version>) de votre <platform> n'est pas prise en charge par SkinsRestorer!<newline><red>Veuillez mettre à jour Java 17 ou supérieur pour utiliser SkinsRestorer sans problème. Les nouvelles versions de Java peuvent également utiliser des serveurs plus anciens, donc un serveur Minecraft 1.8 peut fonctionner sur Java 17. Lisez les infos de la console pour plus de détails.", "skinsrestorer.permission_player_wildcard": "Toutes les permissions des joueurs", "skinsrestorer.permission_command": "Autorise l'accès aux commandes \"/skin\" principales.", "skinsrestorer.permission_command_set": "Autorise l'accès pour changer votre skin.", "skinsrestorer.permission_command_set_url": "Autorise à changer votre skin avec une URL.", "skinsrestorer.permission_command_clear": "Autorise l'accès pour supprimer votre skin.", "skinsrestorer.permission_command_random": "Autorise l'accès pour mettre un skin aléatoire.", "skinsrestorer.permission_command_update": "Autorise l'accès pour mettre à jour votre skin.", "skinsrestorer.permission_command_undo": "Permet l'accès à la réinitialisation de votre skin précédent.", "skinsrestorer.permission_command_favourite": "Permet l'accès pour mettre un skin comme favori.", "skinsrestorer.permission_command_search": "Autorise l'accès pour chercher votre skin.", "skinsrestorer.permission_command_gui": "Autorise l'accès pour ouvrir l'interface des skins.", "skinsrestorer.permission_admin_wildcard": "Toutes les permissions pour les admins", "skinsrestorer.permission_admincommand": "Autorise l'accès pour les commandes \"/sr\" principales.", "skinsrestorer.permission_command_set_other": "Autorise à changer le skin d'un autre joueur.", "skinsrestorer.permission_command_clear_other": "Autorise à effacer le skin de n'importe quel joueur.", "skinsrestorer.permission_command_random_other": "Autorise l'accès à définir un skin aléatoire d'un autre joueur.", "skinsrestorer.permission_command_update_other": "Autorise à metter à jour le skin d'un autre joueur.", "skinsrestorer.permission_command_favourite_other": "Autorise l'accès à définir un skin comme favori pour un autre joueur.", "skinsrestorer.permission_command_undo_other": "Permet l'accès à la réinitialisation de votre skin précédent.", "skinsrestorer.permission_admincommand_reload": "Permet l'accès au \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Permet l'accès au \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Autorise l'accès de supprimer un fichier .SKIN.", "skinsrestorer.permission_admincommand_info": "Permet d'accéder à des informations sur le skin d'un joueur ou d'un skin.", "skinsrestorer.permission_admincommand_applyskin": "Autorise à ré-appliquer le skin d'un autre joueur.", "skinsrestorer.permission_admincommand_createcustom": "Autorise à créer un skin global à partir d'une URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Autorise l'accès à l'élimination des anciennes données de skin.", "skinsrestorer.permission_admincommand_dump": "Autorise à importer les informations du serveur avec \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Outrepasse tous les délais des commandes définis dans la configuration.", "skinsrestorer.permission_bypassdisabled": "Outrepasse tous les skins désactivés définis dans la configuration.", "skinsrestorer.permission_ownskin": "Autorise l'accès de mettre votre propre skin.", "skinsrestorer.duration_day": "jour", "skinsrestorer.duration_days": "jours", "skinsrestorer.duration_hour": "heure", "skinsrestorer.duration_hours": "heures", "skinsrestorer.duration_minute": "minute", "skinsrestorer.duration_minutes": "minutes", "skinsrestorer.duration_second": "seconde", "skinsrestorer.duration_seconds": "secondes"}