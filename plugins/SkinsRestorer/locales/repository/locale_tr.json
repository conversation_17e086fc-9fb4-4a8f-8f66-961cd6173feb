{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Kendi skin'in<PERSON><PERSON>.", "skinsrestorer.help_skins": "Kostümler MENÜ's<PERSON>n<PERSON> açar.", "skinsrestorer.help_sr": "SkinsRestorer için yönetici komutları.", "skinsrestorer.help_skin_help": "Bu yardım komutunu gösterir.", "skinsrestorer.help_skin_set": "Skin'inizi <PERSON>.", "skinsrestorer.help_skin_set_other": "<PERSON><PERSON><PERSON> i<PERSON> bir <PERSON> ayarlar.", "skinsrestorer.help_skin_set_url": "Kostümünüzü bir URL'den değiştirir.", "skinsrestorer.help_skin_clear": "<PERSON>'inizi temi<PERSON>r.", "skinsrestorer.help_skin_clear_other": "<PERSON><PERSON><PERSON> o<PERSON>n <PERSON>'ini temizler.", "skinsrestorer.help_skin_random": "<PERSON><PERSON><PERSON><PERSON> bir skin verir.", "skinsrestorer.help_skin_random_other": "Seçilen oyuncuya rasgele bir skin ayarlar.", "skinsrestorer.help_skin_search": "İstediğiniz bir skin'i arayın.", "skinsrestorer.help_skin_edit": "Mevcut skinini çevrimiçi olarak düzenle.", "skinsrestorer.help_skin_update": "<PERSON>'in<PERSON><PERSON>.", "skinsrestorer.help_skin_update_other": "<PERSON><PERSON><PERSON> o<PERSON>n <PERSON>'ini g<PERSON>.", "skinsrestorer.help_skin_undo": "Kostümünüzü bir önceki kostüme geri döndü<PERSON><PERSON>r.", "skinsrestorer.help_skin_undo_other": "<PERSON><PERSON><PERSON> o<PERSON>n kostümünü bir önceki kostümüne geri döndürür.", "skinsrestorer.help_skin_favourite": "<PERSON>ini favori o<PERSON>ak ka<PERSON>.", "skinsrestorer.help_skin_favourite_other": "<PERSON><PERSON><PERSON> o<PERSON>n skinini favori o<PERSON>ak ka<PERSON>.", "skinsrestorer.help_sr_reload": "Yapılandırma dosyasını yeniden yükler.", "skinsrestorer.help_sr_status": "Gerekli eklenti API hizmetlerini kontrol eder.", "skinsrestorer.help_sr_drop": "Veritabanından oyuncu veya skin verilerini kaldırır.", "skinsrestorer.help_sr_info": "Bir oyuncu veya kostümü hakkında bilgi gö<PERSON><PERSON><PERSON>.", "skinsrestorer.help_sr_apply_skin": "<PERSON><PERSON><PERSON> i<PERSON>'i yeniden uygular.", "skinsrestorer.help_sr_create_custom": "<PERSON><PERSON> sunucu genelinde bir skin oluştur.", "skinsrestorer.help_sr_purge_old_data": "x <PERSON><PERSON><PERSON> ait eski skin verilerini temizler.", "skinsrestorer.help_sr_dump": "Destek verilerini bytebin.lucko.me'e yükle.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URL'ler tırnak içine alınmalıdır. Örnek: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (Tırnakları otomatik tamamlamak için sekmeye basabilirsiniz)", "skinsrestorer.success_skin_change": "<PERSON><PERSON>.", "skinsrestorer.success_skin_change_other": "<yellow><name></yellow> adlı oyuncunun skinini değiştirdin.", "skinsrestorer.success_skin_undo": "<yellow><skin></yellow> skinin <yellow><timestamp></yellow> tari<PERSON><PERSON>i bir skine geri dö<PERSON><PERSON><PERSON><PERSON><PERSON>.", "skinsrestorer.success_skin_undo_other": "<yellow><name></yellow> adlı oyuncunun <yellow><skin></yellow> skini <yellow><timestamp></yellow> tarihindeki bir skine geri dö<PERSON>.", "skinsrestorer.success_skin_favourite": "<yellow><skin></yellow> skinin favori olarak ayarlandı.", "skinsrestorer.success_skin_favourite_other": "<yellow><name></yellow> adlı oyuncunun <yellow><skin></yellow> skini favori olarak ayarlandı.", "skinsrestorer.success_skin_clear": "<PERSON>'iniz temi<PERSON>.", "skinsrestorer.success_updating_skin": "<PERSON>'in<PERSON> g<PERSON>.", "skinsrestorer.success_admin_applyskin": "Oyun<PERSON>nun skin'i yenilendi!", "skinsrestorer.success_admin_drop": "<target> için <type> veris<PERSON> d<PERSON>.", "skinsrestorer.success_admin_reload": "<PERSON><PERSON><PERSON> ve <PERSON>rell<PERSON>ş<PERSON>rme yeniden yüklendi!", "skinsrestorer.error_generic": "<dark_red><PERSON>a<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "Skin verileri istenirken bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin!", "skinsrestorer.error_skin_disabled": "Bu skin bir yönetici tarafından devre dışı bırakıldı.", "skinsrestorer.error_skinurl_disallowed": "<PERSON>u alan yönetici tarafından izin verilmemiştir.", "skinsrestorer.error_updating_skin": "Skin'inizi güncellerken bir hata olu<PERSON>. Lütfen daha sonra tekrar deneyin!", "skinsrestorer.error_updating_url": "Özel URL skinlerini güncelleyemezsiniz! <newline><red>/skin url komutunu kullanarak tekrar isteyin.", "skinsrestorer.error_updating_customskin": "<PERSON>, <PERSON><PERSON> old<PERSON> i<PERSON> g<PERSON>emiyor.", "skinsrestorer.error_admin_applyskin": "Oyuncunun skin'i YENİLENEMEDİ!", "skinsrestorer.error_ms_api_failed": "MineSkin API aşırı yüklü, lütfen daha sonra tekrar deneyin!", "skinsrestorer.error_ms_api_key_invalid": "Geçersiz MineSkin API anahtarı! Bu konuda sunucu sahibi ile iletişime geçin!", "skinsrestorer.error_ms_unknown": "Bilinmeyen MineSkin Hatası!", "skinsrestorer.not_connected_to_server": "<red><PERSON><PERSON><PERSON> bir sun<PERSON><PERSON>a bağl<PERSON> değilsiniz.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>SR'nin düzgün çalışması için gereken hizmetlerin kontrol edilmesi...", "skinsrestorer.admincommand_status_uuid_api": "<gray>Çalışan UUID API'leri: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>Çalışan Profil API'leri: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>Eklenti şu anda <PERSON>ı<PERSON>ı<PERSON> durumda.", "skinsrestorer.admincommand_status_degraded": "<green>Eklenti bozulmuş <PERSON>, bazı özellikler tam olarak çalışmayabilir.", "skinsrestorer.admincommand_status_broken": "<red><PERSON><PERSON> anda eklenti yeni skin'leri al<PERSON>.<newline> Bağlantı muhtemelen güvenlik duvarı nedeniyle engellenmiş.<newline> Daha fazla bilgi için https://skinsrestorer.net/firewall adresine bakın", "skinsrestorer.admincommand_status_firewall": "<red>Bağlantılar muhtemelen güvenlik duvarı ayarı nedeniyle engellenmiştir.<newline>Daha fazla bilgi için lütfen https://skinsrestorer.net/firewall adresini okuyun.", "skinsrestorer.admincommand_status_summary_server": "<gray><PERSON><PERSON><PERSON>: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Proxy Modu: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray><PERSON><PERSON><PERSON>: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON><PERSON> <yellow><player></yellow> bulunamadı.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> bulunamadı.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Oyuncunun UUID'sini almak için <PERSON>g ile iletişim kuramadık.", "skinsrestorer.admincommand_info_checking": "<gray><PERSON><PERSON><PERSON> veriler <PERSON>...", "skinsrestorer.admincommand_info_player": "<gray><PERSON><PERSON><PERSON>: <gold><player>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>Bir oyuncunun UUID'sini belirtmelisiniz.", "skinsrestorer.admincommand_info_no_set_skin": "<red>Oyuncunun belirlenmiş bir kostümü yoktur.", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Sabit kodlu Kostüm: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray><PERSON><PERSON>: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray><PERSON><PERSON><PERSON>: <gold><skin>", "skinsrestorer.admincommand_purgeolddata_success": "<green><PERSON><PERSON> skinler ba<PERSON><PERSON><PERSON><PERSON> te<PERSON>!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>Eski skinleri temizlerken bir hata o<PERSON>!", "skinsrestorer.admincommand_dump_uploading": "<green>Veri bytebin.lucko.me'a yükleniyor...", "skinsrestorer.admincommand_dump_error": "<red>bytebin.lucko.me'a veri yüklenirken hata o<PERSON>.", "skinsrestorer.command_server_not_allowed_message": "<red>Komutlar <server> <PERSON><PERSON><PERSON><PERSON> için devre dışı bırakıldı.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON><PERSON><dark_gray>: <red>Bu skin'i ayarlama i<PERSON> yok.", "skinsrestorer.player_has_no_permission_url": "<dark_red><PERSON><PERSON><dark_gray>: <red>URL ile skin ayarlama izniniz yok.", "skinsrestorer.not_premium": "<dark_red>Hata<dark_gray>: <red>O isimde premium oyuncu mevcut değil.", "skinsrestorer.only_allowed_on_console": "<dark_red><PERSON>a<dark_gray>: <red>Bu komutu sadece konsol çalıştırabilir!", "skinsrestorer.only_allowed_on_player": "<dark_red>Hata<dark_gray>: <red>Bu komutu yalnızca oyuncular çalıştırabilir!", "skinsrestorer.invalid_player": "<dark_red>Hata<dark_gray>: <red><input> geçerli bir kullanıcı adı veya URL değil.", "skinsrestorer.ms_uploading_skin": "<dark_green><PERSON> yü<PERSON><PERSON>, l<PERSON><PERSON><PERSON> be<PERSON>... (Bu biraz zaman alabilir)", "skinsrestorer.wait_a_minute": "<dark_red><PERSON>a<dark_gray>: <red>Lütfen bu skin'i tekrar istemeden önce bir dakika bekleyin. (Saat sınırlı)", "skinsrestorer.skinsmenu_open": "<dark_green>Skin menüsü açılıyor...", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray><PERSON><PERSON><PERSON></gray> <bold>»</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Skin'i Kaldır</gray> <bold>]</bold>", "skinsrestorer.no_skin_data": "<dark_red>Hata<dark_gray>: <red>Skin verisi bulunamadı! Bu oyuncunun bir skin'i var mı?", "skinsrestorer.unsupported_java": "<dark_red><platform>'unuzun Java sürümü (<version>) SkinsRestorer tarafından desteklenmiyor!<newline><red>SkinsRestorer'ı sorunsuz kullanmak için lütfen Java 17 veya daha yüksek bir sürüme güncelleyin. Daha yeni Java sürümleri eski sunucuları da çalıştırabilir, bu nedenle Minecraft 1.8 sunucusu Java 17'de çalışabilir. Daha fazla ayrıntı için konsol bilgilerini okuyun.", "skinsrestorer.permission_player_wildcard": "Oyuncular için <PERSON> izni.", "skinsrestorer.permission_command": "\"/skin\" komutlarının ana erişimine izin verir.", "skinsrestorer.permission_command_set": "Skininizi değiştirmeye erişim <PERSON>.", "skinsrestorer.permission_command_set_url": "URL kullanarak skininizi değiştirmeye erişim <PERSON>.", "skinsrestorer.permission_command_clear": "Skin'inizi temizlemeye er<PERSON><PERSON>.", "skinsrestorer.permission_command_random": "Rasgele skin düzenleme izni ver.", "skinsrestorer.permission_command_update": "Skininizi güncellemeye erişim <PERSON>.", "skinsrestorer.permission_command_search": "<PERSON><PERSON>zi arama<PERSON><PERSON>.", "skinsrestorer.permission_command_gui": "Skin arayüzünü açmaya eriş<PERSON>.", "skinsrestorer.permission_admin_wildcard": "Yöneticiler i<PERSON><PERSON>", "skinsrestorer.permission_admincommand": "<PERSON> \"/sr\" komut<PERSON><PERSON>na er<PERSON><PERSON>.", "skinsrestorer.permission_command_set_other": "Başka bir oyuncunun skinini ayarlamaya eri<PERSON>.", "skinsrestorer.permission_command_clear_other": "Başka bir oyuncunun skin'ini temizlemeye eri<PERSON><PERSON>.", "skinsrestorer.permission_command_random_other": "Başka bir oyuncunun skin'ini yeniden ayarlamaya eri<PERSON><PERSON>.", "skinsrestorer.permission_command_update_other": "Başka bir oyuncunun skin'ini güncellemeye eri<PERSON><PERSON>.", "skinsrestorer.permission_admincommand_reload": "\"/sr reload\" komutuna erişimi <PERSON>.", "skinsrestorer.permission_admincommand_status": "\"/sr status\" komutuna erişimi <PERSON>.", "skinsrestorer.permission_admincommand_drop": ".SKIN dosyasını kaldırmaya erişim sağlar.", "skinsrestorer.permission_admincommand_info": "Bir oyuncunun veya kostümün kostüm bilgisini almak için er<PERSON><PERSON>.", "skinsrestorer.permission_admincommand_applyskin": "Başka bir oyuncunun skin'ini yeniden uygulamaya er<PERSON><PERSON><PERSON>.", "skinsrestorer.permission_admincommand_createcustom": "URL ile özel bir global skin oluşturmaya erişim <PERSON>.", "skinsrestorer.permission_admincommand_purgeolddata": "Eski skin verilerini temizlemeye eriş<PERSON>.", "skinsrestorer.permission_admincommand_dump": "\"/sr dump\" komutu aracılığıyla sunucu bilgilerini yüklemeye erişim sağ<PERSON>.", "skinsrestorer.permission_bypasscooldown": "Yapılandırmada ayarlanmış olan herhangi bir komut bekleme süresini atlar.", "skinsrestorer.permission_bypassdisabled": "Yapılandırmada devre dışı bırakılmış olan herhangi bir skin'i atlar.", "skinsrestorer.permission_ownskin": "Kendi skin'inizi a<PERSON>a er<PERSON><PERSON><PERSON>.", "skinsrestorer.duration_day": " g<PERSON>n", "skinsrestorer.duration_days": " g<PERSON>n", "skinsrestorer.duration_hour": " saat", "skinsrestorer.duration_hours": " saat", "skinsrestorer.duration_minute": " <PERSON><PERSON><PERSON>", "skinsrestorer.duration_minutes": " <PERSON><PERSON><PERSON>", "skinsrestorer.duration_second": " saniye", "skinsrestorer.duration_seconds": " saniye"}