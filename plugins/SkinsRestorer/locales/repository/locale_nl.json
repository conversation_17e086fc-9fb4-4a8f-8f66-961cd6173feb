{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinHersteller</dark_green>] <message>", "skinsrestorer.help_skin": "Pas je skin aan.", "skinsrestorer.help_skins": "Opent de skins GUI.", "skinsrestorer.help_sr": "Admin opdrachten voor SkinsRestorer.", "skinsrestorer.help_skin_help": "Toont deze hulp commando.", "skinsrestorer.help_skin_set": "Werkt je skin bij.", "skinsrestorer.help_skin_set_other": "<PERSON><PERSON><PERSON> de skin van een andere speler in.", "skinsrestorer.help_skin_set_url": "<PERSON><PERSON><PERSON> je skin door middel van een URL.", "skinsrestorer.help_skin_clear": "<PERSON>er<PERSON><PERSON><PERSON>t je skin.", "skinsrestorer.help_skin_clear_other": "<PERSON>er<PERSON><PERSON><PERSON><PERSON> de skin van een andere speler.", "skinsrestorer.help_skin_random": "Geeft een willekeurige skin.", "skinsrestorer.help_skin_random_other": "Stelt een willekeurige skin in voor een geselecteerde speler.", "skinsrestorer.help_skin_search": "<PERSON>k een skin die je wilt.", "skinsrestorer.help_skin_edit": "Bewerk je huidige skin online.", "skinsrestorer.help_skin_update": "Werkt je skin bij.", "skinsrestorer.help_skin_update_other": "Werkt de skin van een doelspeler bij.", "skinsrestorer.help_skin_undo": "Her<PERSON><PERSON> je skin naar de vorige skin.", "skinsrestorer.help_skin_undo_other": "Herstelt de skin van een geselecteerde speler terug naar de vorige skin.", "skinsrestorer.help_skin_favourite": "<PERSON><PERSON><PERSON> je skin als favoriet.", "skinsrestorer.help_skin_favourite_other": "<PERSON><PERSON><PERSON> de skin van een geselecteerde speler als favoriet.", "skinsrestorer.help_skull": "Geeft je een schedel.", "skinsrestorer.help_skull_help": "Schedel commando's voor SkinsRestorer.", "skinsrestorer.help_skull_get": "Geeft je een schedel.", "skinsrestorer.help_skull_get_other": "<PERSON>f een schedel aan een andere speler.", "skinsrestorer.help_skull_get_url": "Geeft de schedel op basis van een skin URL.", "skinsrestorer.help_skull_random": "Geeft je een willekeurige schedel.", "skinsrestorer.help_skull_random_other": "Geeft een willekeurige schedel aan een andere speler.", "skinsrestorer.help_sr_reload": "<PERSON><PERSON><PERSON>t het configuratiebestand.", "skinsrestorer.help_sr_status": "Controle vereist plugin API services.", "skinsrestorer.help_sr_drop": "Verwij<PERSON>t speler- of skingegevens uit de database.", "skinsrestorer.help_sr_info": "Geeft info weer over een speler of skin.", "skinsrestorer.help_sr_apply_skin": "Pas de skin opnieuw toe voor de doelgebruiker.", "skinsrestorer.help_sr_create_custom": "Maak een aangepaste server brede skin.", "skinsrestorer.help_sr_purge_old_data": "Ver<PERSON>jder oude skin gegevens van meer dan x dagen geleden.", "skinsrestorer.help_sr_dump": "Upload ondersteuningsgegevens naar bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URL's moeten worden geciteerd. Voorbeeld: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (U kunt op tabblad drukken om de aanhalingstekens automatisch aan te vullen)", "skinsrestorer.success_skin_change": "Je skin is aangepast.", "skinsrestorer.success_skin_change_other": "Je hebt de skin van <yellow><name></yellow> veranderd.", "skinsrestorer.error_skinurl_disallowed": "Fout: <PERSON><PERSON> domein is niet toe<PERSON><PERSON>an door de beheerder.", "skinsrestorer.not_connected_to_server": "<red>Je <PERSON> niet verbonden met een server.", "skinsrestorer.admincommand_status_checking": "<gray>Benodigde diensten controleren om SR correct te laten functioneren...", "skinsrestorer.admincommand_status_summary_server": "<gray>Server: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>ProxyModus: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Commit: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON><PERSON> <yellow><player></yellow> niet gevonden.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> niet gevonden.", "skinsrestorer.admincommand_purgeolddata_success": "<green>Met succes oude skins verwijderd!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>Er is een fout opgetreden bij het verwijderen van oude skins!", "skinsrestorer.admincommand_dump_uploading": "<green><PERSON><PERSON><PERSON> van gegevens naar bytebin.lucko.me...", "skinsrestorer.admincommand_dump_error": "<red>Fout tijdens het uploaden van gegevens naar bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red>Opdrachten zijn uitgeschakeld voor de server <server>.", "skinsrestorer.player_has_no_permission_skin": "<dark_red>Fout<dark_gray>: <red>Je hebt geen toestemming om deze skin in te stellen.", "skinsrestorer.player_has_no_permission_url": "<dark_red>Fout<dark_gray>: <red>Je hebt geen toestemming om skins in te stellen via URL.", "skinsrestorer.not_premium": "<dark_red>Fout<dark_gray>: <red>Premium speler met die naam bestaat niet.", "skinsrestorer.only_allowed_on_console": "<dark_red>Fout<dark_gray>: <red>Alleen console kan deze opdracht uitvoeren!", "skinsrestorer.only_allowed_on_player": "<dark_red>Fout<dark_gray>: <red>Alleen spelers mogen deze opdracht uitvoeren!", "skinsrestorer.invalid_player": "<dark_red>Fout<dark_gray>: <red><input> is geen geldige g<PERSON><PERSON><PERSON>na<PERSON> of URL.", "skinsrestorer.wait_a_minute": "<dark_red>Fout<dark_gray>: <red>Wacht alstublieft een minuut voordat je die skin opnieuw aanvraagt (rate limted)", "skinsrestorer.skinsmenu_open": "<dark_green>Open het skins menu...", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Volgende pagina</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Verwijder Skin</gray> <bold>]</bold>", "skinsrestorer.permission_player_wildcard": "<PERSON><PERSON><PERSON> met jokertekens voor spelers", "skinsrestorer.permission_command": "Geeft toegang tot de \"/skin\" hub commando.", "skinsrestorer.permission_command_set": "<PERSON>a toegang toe om je skin te wijzigen.", "skinsrestorer.permission_command_clear": "<PERSON>a toegang toe om je skin te wissen.", "skinsrestorer.permission_command_update": "Geeft toegang tot het bijwerken van je skin.", "skinsrestorer.permission_command_search": "<PERSON>a toegang toe om je skin te zoeken.", "skinsrestorer.permission_command_gui": "Geeft toegang tot skins GUI.", "skinsrestorer.permission_admin_wildcard": "Joker rechten voor administratoren", "skinsrestorer.permission_admincommand": "Geeft toegang tot de \"/sr\" hub commando.", "skinsrestorer.permission_ownskin": "<PERSON>a toegang toe om je eigen skin in te stellen."}