{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "내 스킨을 변경해요.", "skinsrestorer.help_skins": "스킨 GUI를 열어요.", "skinsrestorer.help_sr": "SkinsRestorer용 관리자 명령어예요.", "skinsrestorer.help_skin_help": "이 도움말 명령어를 보여줘요.", "skinsrestorer.help_skin_set": "스킨을 변경해요.", "skinsrestorer.help_skin_set_other": "대상 플레이어의 스킨을 설정해요.", "skinsrestorer.help_skin_set_url": "URL로 내 스킨을 변경해요.", "skinsrestorer.help_skin_clear": "스킨을 재설정해요.", "skinsrestorer.help_skin_clear_other": "대상 플레이어의 스킨을 재설정해요.", "skinsrestorer.help_skin_random": "무작위 스킨을 줘요.", "skinsrestorer.help_skin_random_other": "대상 플레이어에게 무작위 스킨을 설정해요.", "skinsrestorer.help_skin_search": "원하는 스킨을 검색해 보세요.", "skinsrestorer.help_skin_edit": "현재 스킨을 온라인에서 편집해요.", "skinsrestorer.help_skin_update": "스킨을 갱신해요.", "skinsrestorer.help_skin_update_other": "대상 플레이어의 스킨을 갱신해요.", "skinsrestorer.help_skin_undo": "이전 스킨으로 되돌려요.", "skinsrestorer.help_skin_undo_other": "대상 플레이어를 이전 스킨으로 되돌려요.", "skinsrestorer.help_skin_favourite": "선호 스킨으로 저장해요.", "skinsrestorer.help_skin_favourite_other": "대상 플레이어의 선호 스킨으로 저장해요.", "skinsrestorer.help_skull": "내 스킨이 적용된 머리를 지급 받습니다.", "skinsrestorer.help_skull_help": "Skull 명령어는 SkinRestorer 플러그인의 전용 명령어 입니다.", "skinsrestorer.help_skull_get": "내 스킨이 적용된 머리를 지급 받습니다.", "skinsrestorer.help_skull_get_other": "다른 플레이어에게 머리를 지급합니다.", "skinsrestorer.help_skull_get_url": "스킨 URL을 입력해야 두개골을 지급 받을수 있습니다.", "skinsrestorer.help_skull_random": "랜덤한 머리를 지급 받습니다.", "skinsrestorer.help_skull_random_other": "랜덤한 머리를 다른플레이어에게 지급합니다.", "skinsrestorer.help_sr_reload": "구성 파일을 다시 불러와요.", "skinsrestorer.help_sr_status": "API 서비스가 필요한 플러그인을 확인해요.", "skinsrestorer.help_sr_drop": "데이터베이스에서 플레이어나 스킨 데이터를 제거해요.", "skinsrestorer.help_sr_info": "플레이어나 스킨에 관한 정보를 표시해요.", "skinsrestorer.help_sr_apply_skin": "대상 플레이어에게 스킨을 다시 적용해요.", "skinsrestorer.help_sr_create_custom": "서버 전체의 맞춤 스킨을 생성해요.", "skinsrestorer.help_sr_purge_old_data": "x일 지난 예전 스킨 데이터를 정리해요.", "skinsrestorer.help_sr_dump": "bytebin.lucko.me로 지원 데이터를 업로드해요.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URL은 따옴표로 묶어야 해요. 예: <yellow>/skin set \"https://example.com/skin.png\"</yellow>(tab 키를 눌러 따옴표를 자동 완성할 수 있어요)", "skinsrestorer.success_skin_change": "스킨을 변경했어요.", "skinsrestorer.success_skin_change_other": "<yellow><name></yellow>님의 스킨을 변경했어요.", "skinsrestorer.success_skin_undo": "<yellow><timestamp></yellow>에 <yellow><skin></yellow> 스킨을 되돌렸어요.", "skinsrestorer.success_skin_undo_other": "<yellow><timestamp></yellow>에 <yellow><name></yellow>님의 <yellow><skin></yellow> 스킨을 되돌렸어요.", "skinsrestorer.success_skin_favourite": "<yellow><skin></yellow> 스킨을 선호 스킨으로 지정했어요.", "skinsrestorer.success_skin_favourite_other": "<yellow><name></yellow>님의 <yellow><skin></yellow> 스킨을 선호 스킨으로 지정했어요.", "skinsrestorer.success_skin_unfavourite": "<yellow><timestamp></yellow>에 선호하던 <yellow><skin></yellow> 스킨의 선호 지정을 해제했어요.", "skinsrestorer.success_skin_unfavourite_other": "<yellow><timestamp></yellow>에 선호하던 <yellow><skin></yellow> 스킨의 선호 지정이 해제됐어요.", "skinsrestorer.success_skin_clear": "스킨을 재설정했어요.", "skinsrestorer.success_skin_clear_other": "<yellow><name></yellow>님의 스킨을 재설정했어요.", "skinsrestorer.success_updating_skin": "스킨을 갱신했어요.", "skinsrestorer.success_updating_skin_other": "<yellow><name></yellow>님의 스킨을 갱신했어요.", "skinsrestorer.success_skull_get": "머리를 받았습니다.", "skinsrestorer.success_skull_get_other": "<yellow><name></yellow>님의 머리를 지급 받았습니다.", "skinsrestorer.success_admin_applyskin": "플레이어 스킨을 새로고침했어요!", "skinsrestorer.success_admin_createcustom": "<yellow><skin></yellow> 스킨을 생성했어요!", "skinsrestorer.success_admin_setcustomname": "<yellow><skin></yellow>의 스킨 이름을 <yellow><display_name></yellow>으(로) 설정했어요.", "skinsrestorer.success_admin_drop": "<type> 유형의 <target>의 데이터를 드롭했어요.", "skinsrestorer.success_admin_reload": "구성과 언어를 다시 불러왔어요!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green>눌러서 <yellow><skin></yellow><dark_green>스킨 사용'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover>\n시간: <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green>눌러서 <yellow><skin></yellow><dark_green>스킨 사용'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover>\n시간: <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>오류<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "스킨 데이터를 요청하던 도중 오류가 발생했어요. 나중에 다시 시도해 주세요!", "skinsrestorer.error_no_undo": "되돌릴 스킨이 없어요!", "skinsrestorer.error_no_skin_to_favourite": "선호 스킨으로 지정한 스킨이 없어요!", "skinsrestorer.error_skin_disabled": "이 스킨은 관리자가 사용하지 못하도록 했어요.", "skinsrestorer.error_skinurl_disallowed": "이 도메인은 관리자가 허용하지 않았어요.", "skinsrestorer.error_updating_skin": "스킨을 갱신하던 도중 오류가 발생했어요! 나중에 다시 시도해 주세요!", "skinsrestorer.error_updating_url": "맞춤 URL 스킨을 갱신할 수 없어요!<newline><red>/skin url 명령어를 사용해서 다시 요청하세요", "skinsrestorer.error_updating_customskin": "맞춤 스킨은 갱신할 수 없어요.", "skinsrestorer.error_invalid_urlskin": "잘못된 스킨 URL이거나 형식입니다. <newline><red> 안내: <red><underlined><hover:show_text:'<dark_green>클릭하여 열기'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>.", "skinsrestorer.error_admin_applyskin": "플레이어 스킨은 새로고침할 수 없어요!", "skinsrestorer.error_ms_full": "스킨을 업로드하던 도중 MineSkin API의 시간이 초과됐어요. 나중에 다시 시도해 주세요.", "skinsrestorer.error_ms_api_failed": "MineSkin API가 과부하됐어요. 나중에 다시 시도해 주세요!", "skinsrestorer.error_ms_api_key_invalid": "잘못된 MineSkin API 키예요! 서버 소유자에게 이걸 말해주세요!", "skinsrestorer.error_ms_unknown": "알 수 없는 MineSkin 오류예요!", "skinsrestorer.error_no_history": "스킨 기록이 없어요!", "skinsrestorer.error_no_favourites": "선호하는 스킨이 없어요!", "skinsrestorer.error_player_refresh_no_mapping": "이 Minecraft 버전은 SkinRestorer에서 지원하지 않기 때문에 스킨을 새로고침하지 못했어요. 서버 어드민에게 SkinRestorer를 업데이트하라고 이야기해 주세요.", "skinsrestorer.not_connected_to_server": "<red>어떤 서버에도 연결하지 않았어요.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>작동 중인 서비스를 확인 중이에요...", "skinsrestorer.admincommand_status_uuid_api": "<gray>작동하는 UUID API: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>작동하는 프로필 API: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>플러그인이 현재 작동 중인 상태예요.", "skinsrestorer.admincommand_status_degraded": "<green>플러그인이 현재 성능 저하 상태예요. 일부 기능이 제대로 작동하지 않을 수도 있어요.", "skinsrestorer.admincommand_status_broken": "<red>플러그인이 작동 중지된 상태예요. 어떠한 새 스킨도 요청할 수 없어요.", "skinsrestorer.admincommand_status_firewall": "<red>방화벽으로 인해 연결이 차단된 것 같아요.<newline>더 알아보려면 다음을 확인해 주세요. https://skinsrestorer.net/firewall", "skinsrestorer.admincommand_status_summary_server": "<gray>서버: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>프록시 모드: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>커밋: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>플레이어 <yellow><player></yellow>님을 찾지 못했어요.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>스킨 <yellow><skin></yellow>을(를) 찾지 못했어요.", "skinsrestorer.admincommand_drop_uuid_error": "<red>플레이어 UUID를 가져올 Mojang에 연결하지 못했어요", "skinsrestorer.admincommand_info_checking": "<gray>요청한 데이터를 수집 중이에요...", "skinsrestorer.admincommand_info_player": "<gray>플레이어 UUID: <gold><uuid><newline><gray>스킨 식별자: <gold><identifier><newline><gray>스킨 다른 버전: <gold><variant><newline><gray>스킨 유형: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>플레이어의 UUID를 입력해야 해요.", "skinsrestorer.admincommand_info_no_set_skin": "<red>플레이어가 명시적으로 지정된 스킨이 없어요.", "skinsrestorer.admincommand_info_url_skin": "<gray>스킨 URL: <gold><click:open_url:'<url><click:open_url:'<url> > <url></click><newline><gray> MineSkin ID: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>하드코드된 스킨: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>사용자 지정 스킨: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>플레이어 스킨: <gold><skin><newline><gray>시간: <gold><timestamp><newline><gray>만료 기간: <gold><expires>", "skinsrestorer.admincommand_info_generic": "<gray> 텍스쳐 URL: <gold><click:open_url:'<url><click:open_url:'<url> > <url></click><newline><gray> Variant: <gold><variant><newline><gray> 프로필 UUID: <gold><uuid><newline><gray> 프로필명: <gold><name><newline><gray> 소요시간: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>예전 스킨을 모두 정리했어요!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>예전 스킨을 정리하던 도중 오류가 발생했어요!", "skinsrestorer.admincommand_dump_uploading": "<green>bytebin.lucko.me로 데이터를 업로드하고 있어요....", "skinsrestorer.admincommand_dump_success": "<green> 성공적으로 업로드 했습니다! <yellow><click:open_url:'<url><click:open_url:'<url> > <url></click>", "skinsrestorer.admincommand_dump_error": "<red>bytebin.lucko.me에 데이터를 업로드하던 도중 오류가 발생했어요", "skinsrestorer.command_server_not_allowed_message": "<red><server> 서버에서는 명령어를 사용할 수 없어요.", "skinsrestorer.command_unknown_player": "알 수 없는 플레이어: <name>", "skinsrestorer.command_no_targets_supplied": "대상 플레이어가 제공되지 않았어요.", "skinsrestorer.player_has_no_permission_skin": "<dark_red>오류<dark_gray>: <red>이 스킨을 설정할 권한이 없어요.", "skinsrestorer.player_has_no_permission_url": "<dark_red>오류<dark_gray>: <red>URL로 스킨을 설정할 권한이 없어요.", "skinsrestorer.not_premium": "<dark_red>오류<dark_gray>: <red>그 이름을 지닌 프리미엄 플레이어가 존재하지 않아요.", "skinsrestorer.only_allowed_on_console": "<dark_red>오류<dark_gray>: <red>콘솔에서만 이 명령어를 사용할 수 있어요!", "skinsrestorer.only_allowed_on_player": "<dark_red>오류<dark_gray>: <red>플레이어만 이 명령어를 사용할 수 있어요!", "skinsrestorer.invalid_player": "<dark_red>오류<dark_gray>: <red><input>은(는) 올바르지 않은 사용자 이름이거나 URL이에요.", "skinsrestorer.skin_cooldown": "<dark_red>오류<dark_gray>: <red>스킨을 <yellow><time></yellow> 후 다시 변경할 수 있어요", "skinsrestorer.ms_uploading_skin": "<dark_green>스킨을 업로드하는 중이에요. 기다려주세요...(잠시 시간이 걸릴 수 있어요)", "skinsrestorer.wait_a_minute": "<dark_red>오류<dark_gray>: <red>그 스킨을 다시 요청하기 전 1분을 기다려주세요.(속도 제한)", "skinsrestorer.skinsmenu_open": "<dark_green>스킨 메뉴를 여는 중이에요...", "skinsrestorer.skinsmenu_title_select": "<blue>메뉴 선택", "skinsrestorer.skinsmenu_title_main": "<blue>스킨 메뉴 - <page_number>페이지", "skinsrestorer.skinsmenu_title_history": "<blue>기록 메뉴 - <page_number>페이지", "skinsrestorer.skinsmenu_title_favourites": "<blue>선호 스킨 메뉴 - <page_number>페이지", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>다음 페이지</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>이전 페이지</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>스킨 제거</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>눌러서 이 스킨 선택", "skinsrestorer.skinsmenu_history_lore": "<blue>다음 시간에서의 스킨: <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + 클릭으로 선호 스킨으로 지정", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + 클릭으로 선호 스킨에서 제거", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue><time> 이후로 선호 중", "skinsrestorer.skinsmenu_no_permission": "<red>이 스킨을 사용할 권한이 없어요.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>스킨 메뉴</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>기록 메뉴</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>선호 스킨 메뉴</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>메뉴 선택</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<green><search></green><dark_green>와(과) 일치하는 스킨을 아래에서 찾아보세요.<newline><green><hover:show_text:'<dark_green>눌러서 열기'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>어떠한 스킨도 찾지 못한 경우, https://namemc.com/minecraft-skins/tag에서 언제든지 스킨을 찾을 수 있어요.<newline>스킨 링크를 사용해서 스킨을 설정할 수도 있어요.<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<u><aqua><hover:show_text:'<dark_green>에서 스킨을 편집할 수 있어요.<hover:show_text:'<dark_green>눌러서 열기'><click:open_url:'<url>링크</click></hover></aqua></u> <newline><dark_green>편집한 스킨을 적용하는 법을 배우시려면, <newline><green><hover:show_text:'<dark_green>눌러서 열기'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit을 방문하세요.</click></hover>", "skinsrestorer.no_skin_data": "<dark_red>오류<dark_gray>: <red>스킨 데이터를 찾지 못했어요!", "skinsrestorer.outdated": "<dark_red> <platform>에서 과거버전의 SkinRestorer를 사용중입니다.<newline><red>해당 링크에서 최신버전으로 업데이트가 가능합니다. <newline>Modrinth:<yellow><hover:show_text:'<dark_green><hover:show_text:'<dark_green>[클릭하여 열기]<click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red><platform>의 Java 버전(<version>)이 SkinsRestorer와 호환되지 않아요!<newline><red>SkinsRestorer를 원활히 사용하려면 Java 17 이상으로 업데이트해 주세요. 최신 Java v버전은 오래된 서버를 돌릴 수 있어요. 즉, Minecraft 1.8 서버도 Java 17에서 돌릴 수 있어요. 더 알아보려면 콘솔 정보를 확인하세요.", "skinsrestorer.permission_player_wildcard": "플레이어에 대한 와일드카드 권한", "skinsrestorer.permission_command": "주요 \"/skin\" 명령어를 사용하도록 허용해요.", "skinsrestorer.permission_command_set": "스킨을 변경하도록 허용해요.", "skinsrestorer.permission_command_set_url": "URL로 스킨을 변경하도록 허용해요.", "skinsrestorer.permission_command_clear": "스킨을 재설정하도록 허용해요.", "skinsrestorer.permission_command_random": "무작위 스킨에 접근하도록 허용해요.", "skinsrestorer.permission_command_update": "스킨을 갱신하도록 허용해요.", "skinsrestorer.permission_command_undo": "이전 스킨으로 되돌리도록 허용해요.", "skinsrestorer.permission_command_favourite": "선호하는 스킨을 지정하도록 허용해요.", "skinsrestorer.permission_command_search": "스킨을 검색하도록 허용해요.", "skinsrestorer.permission_command_edit": "스킨을 편집하도록 허용해요.", "skinsrestorer.permission_command_gui": "스킨 GUI 창을 열도록 허용해요.", "skinsrestorer.permission_admin_wildcard": "관리자에 대한 와일드카드 권한", "skinsrestorer.permission_admincommand": "주요 \"/sr\" 명령어를 사용하도록 허용해요.", "skinsrestorer.permission_command_set_other": "플레이어의 스킨을 설정하도록 허용해요.", "skinsrestorer.permission_command_clear_other": "플레이어의 스킨을 재설정하도록 허용해요.", "skinsrestorer.permission_command_random_other": "다른 플레이어에게 무작위 스킨을 설정하도록 허용해요.", "skinsrestorer.permission_command_update_other": "플레이어의 스킨을 갱신하도록 허용해요.", "skinsrestorer.permission_command_favourite_other": "다른 플레이어의 선호하는 스킨을 지정하도록 허용해요.", "skinsrestorer.permission_command_undo_other": "플레이어를 이전 스킨으로 되돌리도록 허용해요.", "skinsrestorer.permission_admincommand_skull": "\"/skull\" 명령어를 사용할수 있습니다.", "skinsrestorer.permission_admincommand_skull_get": "머리를 얻을수 있는 권한을 허용해주세요.", "skinsrestorer.permission_admincommand_skull_get_url": "URL로 머리를 얻을수 있게 권한을 허용해 주세요.", "skinsrestorer.permission_admincommand_skull_random": "랜덤으로 머리를 얻을수 있게 권한을 허용해주세요.", "skinsrestorer.permission_admincommand_skull_get_other": "다른 플레이어에게 머리를 지급할수있게 권한을 허용해주세요.", "skinsrestorer.permission_admincommand_skull_random_other": "다른 플레이어에게 랜덤한 두개골을 지급할수 있게 권한을 허용해주세요.", "skinsrestorer.permission_admincommand_reload": "\"/sr reload\" 명령어를 사용하도록 허용해요.", "skinsrestorer.permission_admincommand_status": "\"/sr status\" 명령어를 사용하도록 허용해요.", "skinsrestorer.permission_admincommand_drop": ".SKIN 파일을 제거하도록 허용해요.", "skinsrestorer.permission_admincommand_info": "플레이어의 스킨 정보를 확인하도록 허용해요.", "skinsrestorer.permission_admincommand_applyskin": "다른 플레이어에게 스킨을 다시 적용하도록 허용해요.", "skinsrestorer.permission_admincommand_createcustom": "URL로 맞춤 글로벌 스킨을 생성하도록 허용해요.", "skinsrestorer.permission_admincommand_purgeolddata": "예전 스킨 데이터를 정리하도록 허용해요.", "skinsrestorer.permission_admincommand_dump": "\"/sr dump\" 명령어로 서버 정보를 올리도록 허용해요.", "skinsrestorer.permission_bypasscooldown": "구성 파일에서 설정한 대기 시간을 무시해요.", "skinsrestorer.permission_bypassdisabled": "구성 파일에서 설정한 사용할 수 없는 스킨을 무시해요.", "skinsrestorer.permission_ownskin": "자신의 스킨을 설정하도록 허용해요.", "skinsrestorer.duration_day": " 일", "skinsrestorer.duration_days": " 일", "skinsrestorer.duration_hour": " 시간", "skinsrestorer.duration_hours": " 시간", "skinsrestorer.duration_minute": " 분", "skinsrestorer.duration_minutes": " 분", "skinsrestorer.duration_second": " 초", "skinsrestorer.duration_seconds": " 초"}