{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Cambia tu skin.", "skinsrestorer.help_skins": "Abre el menú de Skins.", "skinsrestorer.help_sr": "Comandos de administrador para SkinsRestorer.", "skinsrestorer.help_skin_help": "Muestra este comando de ayuda.", "skinsrestorer.help_skin_set": "Cambia tu skin.", "skinsrestorer.help_skin_set_other": "Establece la skin para un jugador específico.", "skinsrestorer.help_skin_set_url": "Cambia tu skin desde un URL.", "skinsrestorer.help_skin_clear": "Borra tu skin.", "skinsrestorer.help_skin_clear_other": "Borra la skin a un jugador específico.", "skinsrestorer.help_skin_random": "Da una skin aleatoria.", "skinsrestorer.help_skin_random_other": "Establece una skin aleatoria para un jugador específico.", "skinsrestorer.help_skin_search": "Busca una skin que quieras", "skinsrestorer.help_skin_edit": "Edita tu skin actual en línea.", "skinsrestorer.help_skin_update": "Actualiza tu skin.", "skinsrestorer.help_skin_update_other": "Actualiza la skin a un jugador específico.", "skinsrestorer.help_skin_undo": "Invierte tu skin de vuelta a la skin anterior.", "skinsrestorer.help_skin_undo_other": "Rvierte la skin de un jugador objetivo de vuelta a la skin anterior.", "skinsrestorer.help_skin_favourite": "Guarda tu skin como favorita.", "skinsrestorer.help_skin_favourite_other": "Guarda la skin de un jugador objetivo como favorito.", "skinsrestorer.help_skull": "Te da una cabeza.", "skinsrestorer.help_skull_help": "Comandos de cabezas para SkinRestorer.", "skinsrestorer.help_skull_get": "Te da una cabeza.", "skinsrestorer.help_skull_get_other": "Le da una cabeza a otro jugador.", "skinsrestorer.help_skull_get_url": "Da una cabeza basada en la URL de una skin.", "skinsrestorer.help_skull_random": "Da una cabeza aleatoria.", "skinsrestorer.help_skull_random_other": "Da una cabeza aleatoria a otro jugador.", "skinsrestorer.help_sr_reload": "Recarga el archivo de configuración.", "skinsrestorer.help_sr_status": "Comprueba la servicios API requeridos por el plugin.", "skinsrestorer.help_sr_drop": "Elimina el jugador o la skin de la base de datos.", "skinsrestorer.help_sr_info": "Muestra información sobre un jugador o skin.", "skinsrestorer.help_sr_apply_skin": "Reaplica la skin para el jugador seleccionado.", "skinsrestorer.help_sr_create_custom": "Cree una skin personalizada para todo el servidor.", "skinsrestorer.help_sr_purge_old_data": "Purga los datos de skins antiguos de hace más de x días.", "skinsrestorer.help_sr_dump": "Sube datos de soporte a bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "Las URLs deben ser citadas. Ejemplo: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (Puedes presionar pestaña para completar automáticamente las comillas)", "skinsrestorer.success_skin_change": "Tu skin ha sido cambiada.", "skinsrestorer.success_skin_change_other": "Has cambiado la skin de <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "Tu skin <yellow><skin></yellow> ha sido revertida a una skin de <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "La skin <yellow><skin></yellow> de <yellow><name></yellow> ha sido devuelta a una skin de <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "Tu skin <yellow><skin></yellow> se ha establecido como favorita.", "skinsrestorer.success_skin_favourite_other": "La skin <yellow><skin></yellow> de <yellow><name></yellow> se ha establecido como favorita.", "skinsrestorer.success_skin_unfavourite": "Tu skin favorita <yellow><skin></yellow> de <yellow><timestamp></yellow> ha sido desfavoritada.", "skinsrestorer.success_skin_unfavourite_other": "La skin favorita <yellow><skin></yellow> de <yellow><name></yellow> de <yellow><timestamp></yellow> ha sido desfavoritada.", "skinsrestorer.success_skin_clear": "Tu skin ha sido limpiada.", "skinsrestorer.success_skin_clear_other": "Has limpiado la skin de <yellow><name></yellow>.", "skinsrestorer.success_updating_skin": "Tu skin ha sido actualizada.", "skinsrestorer.success_updating_skin_other": "Skin actualizada para el jugador <yellow><name></yellow>.", "skinsrestorer.success_skull_get": "Has recibido una cabeza.", "skinsrestorer.success_skull_get_other": "<PERSON> has dado una cabeza a <yellow><name></yellow>.", "skinsrestorer.success_admin_applyskin": "La skin del jugador ha sido actualizada!", "skinsrestorer.success_admin_createcustom": "La skin <yellow><skin></yellow> ha sido creada!", "skinsrestorer.success_admin_setcustomname": "El nombre de la skin <yellow><skin></yellow> se ha establecido en <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "<type> de datos descartados para <target>.", "skinsrestorer.success_admin_reload": "La configuración y el idioma se han recargado!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green>Haz clic para usar <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> de <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green>Haz clic para usar <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> de <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Error<dark_gray><red><message>", "skinsrestorer.error_generic_skin": "Se produjo un error al solicitar los datos de la skin, por favor inténtalo de nuevo más tarde!", "skinsrestorer.error_no_undo": "No tienes ninguna skin para revertir!", "skinsrestorer.error_no_skin_to_favourite": "No tienes ninguna skin para establecer como favorita!", "skinsrestorer.error_skin_disabled": "Esta skin está desactivada por un administrador.", "skinsrestorer.error_skinurl_disallowed": "Este dominio no ha sido permitido por el administrador.", "skinsrestorer.error_updating_skin": "Se produjo un error al actualizar tu skin. Por favor, inténtalo de nuevo más tarde!", "skinsrestorer.error_updating_url": "No puedes actualizar skins con URL personalizadas! <newline><red>Solicita de nuevo usando /skin url", "skinsrestorer.error_updating_customskin": "La skin no puede ser actualizada porque es personalizada.", "skinsrestorer.error_invalid_urlskin": "La URL de la skin es inválida o tiene un formato incorrecto, <newline><red>Intenta subiendo tu skin a Imgur, dar clic derecho y seleccionar 'copiar dirección de imagen'. <newline><red>Para guiarte entra a: <red><underlined><hover:show_text:'<dark_green>Clic para abrir'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>.", "skinsrestorer.error_admin_applyskin": "La skin del jugador NO pudo ser actualizada!", "skinsrestorer.error_ms_full": "MineSkin API ha agotado el tiempo de espera al cargar su skin. Por favor, inténtelo de nuevo más tarde.", "skinsrestorer.error_ms_api_failed": "La API de MineSkin está sobrecargada, por favor inténtalo de nuevo más tarde.", "skinsrestorer.error_ms_api_key_invalid": "Clave de API de MineSkin inválida!, Contacta al dueño del servidor sobre esto!", "skinsrestorer.error_ms_unknown": "Error desconocido de MineSkin!", "skinsrestorer.error_no_history": "No tienes historial de skin!", "skinsrestorer.error_no_favourites": "No tienes ninguna skin favorita!", "skinsrestorer.error_player_refresh_no_mapping": "No se pudo actualizar tu aspecto porque esta versión de Minecraft no es compatible con Restaurador de aspectos. Por favor, indica al administrador del servidor que actualice el plug-in Restaurador de aspectos.", "skinsrestorer.not_connected_to_server": "<red>No estás conectado a un servidor.", "skinsrestorer.divider": "<dark_aqua>--------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Comprobando los servicios necesarios para el correcto funcionamiento de SR...", "skinsrestorer.admincommand_status_uuid_api": "<gray>Trabajando UUID APIs: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>Perfil de trabajo APIs: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>El plugin se encuentra actualmente en funcionamiento.", "skinsrestorer.admincommand_status_degraded": "<green>El plugin está en estado degradado, algunas características pueden no funcionar completamente.", "skinsrestorer.admincommand_status_broken": "<red>El plugin actualmente no puede obtener nuevas skins.<newline>La conexión este probablemente bloqueada por el cortafuegos.<newline>Por favor, lee https://skinsrestorer.net/firewall para mas información.", "skinsrestorer.admincommand_status_firewall": "<red>Es probable que las conexiones estén bloqueadas debido a un cortafuegos.<newline>Por favor, lea https://skinsrestorer.net/firewall para más información.", "skinsrestorer.admincommand_status_summary_server": "<gray>Servidor: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Modo proxy: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Acción: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>Jugador <yellow><player></yellow> no encontrado.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>La skin <yellow><skin></yellow> no ha sido encontrada.", "skinsrestorer.admincommand_drop_uuid_error": "No hemos podido contactar con Mojang para obtener el UUID del jugador", "skinsrestorer.admincommand_info_checking": "<gray><PERSON><PERSON><PERSON><PERSON>o datos solicitados...", "skinsrestorer.admincommand_info_player": "<gray>UUID de jugador: <gold><uuid><newline><gray>Identificador de skin: <gold><identifier><newline><gray>Variante de skin: <gold><variant><newline><gray>Tipo de skin: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>Debe especificar el UUID del jugador.", "skinsrestorer.admincommand_info_no_set_skin": "<red>El jugador no tiene ningúna skin establecida explícitamente.", "skinsrestorer.admincommand_info_url_skin": "<gray>Piel URL: <gold><click:open_url:'<url>'><url></click><newline><gray>Piel de Minecraft Id: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Código de skin: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Skin personalizada: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>Skin de jugador: <gold><skin><newline><gray><PERSON><PERSON> de tiempo: <gold><timestamp><newline><gray>Expira: <gold><expires>", "skinsrestorer.admincommand_purgeolddata_success": "<green>¡Se han purgado exitosamente las skins antiguas!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>¡Ha ocurrido un error mientras se purgaban las skins antiguas!", "skinsrestorer.admincommand_dump_uploading": "<green>Subiendo los datos a bytebin.lucko.me...", "skinsrestorer.admincommand_dump_error": "<red>Ocurrió un error mientras se subían los datos a bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<dark_red>Error<dark_gray>: <red>El comando han sido deshabilitado para el servidor <server>.", "skinsrestorer.command_unknown_player": "Jugador desconocido: <name>", "skinsrestorer.command_no_targets_supplied": "No se ha suministrado ningún jugador objetivo.", "skinsrestorer.player_has_no_permission_skin": "<dark_red>Error<dark_gray>: <red>No tenés permisos para ponerte esta skin.", "skinsrestorer.player_has_no_permission_url": "<dark_red>Error<dark_gray>: <red>No tienes permisos para establecerte una skin por URL.", "skinsrestorer.not_premium": "<dark_red>Error<dark_gray>: <red>No existe un jugador premium con ese nombre.", "skinsrestorer.only_allowed_on_console": "<dark_red>Error<dark_gray>: <red>Solo la consola puede ejecutar este comando!", "skinsrestorer.only_allowed_on_player": "<dark_red>Error<dark_gray>: <red>Solo los jugadores pueden ejecutar este comando!", "skinsrestorer.invalid_player": "<dark_red>Error<dark_gray>: <red><input> no es una URL válida.", "skinsrestorer.skin_cooldown": "<dark_red>Error<dark_gray>: <red><PERSON><PERSON>es cambiar tu skin de nuevo en: <yellow><time></yellow>", "skinsrestorer.ms_uploading_skin": "<dark_green>Subiendo skin, por favor espera... (<PERSON>sto puede tardar un poco)", "skinsrestorer.wait_a_minute": "<dark_red>Error<dark_gray>: <red>Espere un minuto antes de volver a solicitar ese aspecto. (Tarifa limitada)", "skinsrestorer.skinsmenu_open": "<dark_green>Abriendo el menu de skins...", "skinsrestorer.skinsmenu_title_select": "<blue><PERSON><PERSON> se<PERSON>", "skinsrestorer.skinsmenu_title_main": "<blue><PERSON><PERSON> - Página <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue><PERSON><PERSON> historial - Página <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue><PERSON><PERSON>tos - Página <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»<gray> Próxima pagina</gray><bold> »</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Página anterior</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[ <gray>Eliminar Skin</gray><bold> ]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>Click para seleccionar esta skin", "skinsrestorer.skinsmenu_history_lore": "<blue>Skin de <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + clic para establecer como favorita", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + clic para eliminar de favoritos", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>F<PERSON><PERSON><PERSON> <time>", "skinsrestorer.skinsmenu_no_permission": "<red>No tienes permiso para usar esta skin.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON> de Skins</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON></gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON> de Favoritos</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>Menú de Selección</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green>Puedes encontrar skins que coincidan con <green><search></green> en: <newline><green><hover:show_text:'<dark_green>Clic para abrir'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>Si no se encuentran skins, siempre podrás encontrar algunas en https://namemc.com/minecraft-skins/tag <newline>Puedes establecer la skin usando el enlace de la misma, así: <newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green>Puedes editar tu skin en <u><aqua><hover:show_text:'<dark_green>Clic para abrir'><click:open_url:'<url>'>este enlace</click></hover></aqua></u>. <newline><dark_green>Para aprender a cómo establecer la skin editada, visita: <newline><green><hover:show_text:'<dark_green>Clic para abrir'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover>", "skinsrestorer.no_skin_data": "<dark_red>Error<dark_gray>: <red>¡No se encontraron datos de skin! ¿Este jugador tiene un skin?", "skinsrestorer.outdated": "<dark_red>¡Estás utilizando una versión antigua de SkinsRestorer en <platform>!<newline><red>Por favor, actualízate a la última versión en Modrinth: <newline><yellow><hover:show_text:'<dark_green>Clic para abrir'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red>La versión de Java (<version>) de tu <platform> no es compatible con SkinsRestorer!<newline><red>Por favor, actualice a Java 17 o superior para utilizar SkinsRestorer sin problemas. Las nuevas versiones de Java también pueden ejecutar servidores más antiguos, por lo que un servidor de Minecraft 1.8 puede funcionar con Java 17. Lee la información de la consola para más detalles.", "skinsrestorer.permission_player_wildcard": "Permiso wildcard para jugadores.", "skinsrestorer.permission_command": "Permite el acceso a los comandos principales \"/skin\".", "skinsrestorer.permission_command_set": "Permite el acceso para cambiar tu skin.", "skinsrestorer.permission_command_set_url": "Permite el acceso para cambiar su skin por URL.", "skinsrestorer.permission_command_clear": "Permite el acceso para eliminar tu skin.", "skinsrestorer.permission_command_random": "Permite el acceso para establecer una skin aleatoria.", "skinsrestorer.permission_command_update": "Permite el acceso para actualizar tu skin.", "skinsrestorer.permission_command_undo": "Permite el acceso a revertir la skin a la anterior.", "skinsrestorer.permission_command_favourite": "Permite el acceso a establecer una skin como favorita.", "skinsrestorer.permission_command_search": "Permite el acceso para buscar tu skin.", "skinsrestorer.permission_command_edit": "<dark_red>La versión de Java (<version>) de tu <platform> no es compatible con SkinsRestorer!<newline><red>Por favor, actualiza a Java 17 o superior para utilizar SkinsRestorer sin problemas. Las nuevas versiones de Java también pueden ejecutar servidores de versiones antiguas, por lo que un servidor de Minecraft 1.8 puede funcionar con Java 17. Lee la información de la consola para más detalles.", "skinsrestorer.permission_command_gui": "Permite el acceso a la GUI de skins.", "skinsrestorer.permission_admin_wildcard": "Permiso wildcard para administradores.", "skinsrestorer.permission_admincommand": "Permite el acceso a los comandos principales \"/sr\".", "skinsrestorer.permission_command_set_other": "Permite el acceso para cambiar la skin de otro jugador.", "skinsrestorer.permission_command_clear_other": "Permite el acceso para eliminar la skin de otro jugador.", "skinsrestorer.permission_command_random_other": "Permite el acceso para establecer la skin aleatoria de otro jugador.", "skinsrestorer.permission_command_update_other": "Permite el acceso para actualizar la skin de otro jugador.", "skinsrestorer.permission_command_favourite_other": "Permite el acceso a establecer una skin como favorita para otro jugador.", "skinsrestorer.permission_command_undo_other": "Permitir el acceso a revertir la skin de un jugador a su skin anterior.", "skinsrestorer.permission_admincommand_skull": "Permite el uso del comando principal \"/skull\".", "skinsrestorer.permission_admincommand_skull_get": "Permite obtener una cabeza.", "skinsrestorer.permission_admincommand_skull_get_url": "Permite obtener una cabeza mediante una URL.", "skinsrestorer.permission_admincommand_skull_random": "Permite obtener una cabeza aleatoria.", "skinsrestorer.permission_admincommand_skull_get_other": "Permite darle una cabeza a otro jugador.", "skinsrestorer.permission_admincommand_skull_random_other": "Permite darle una cabeza aleatoria a otro jugador.", "skinsrestorer.permission_admincommand_reload": "Permite el acceso a \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Permite el acceso a \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Permite el acceso a eliminar un archivo .SKIN.", "skinsrestorer.permission_admincommand_info": "Permite el acceso para obtener la información de una skin.", "skinsrestorer.permission_admincommand_applyskin": "Permite el acceso para reaplicar la skin de otro jugador.", "skinsrestorer.permission_admincommand_createcustom": "Permite el acceso a crear una skin global personalizada por URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Permite el acceso para purgar datos de skins antiguos.", "skinsrestorer.permission_admincommand_dump": "Permite el acceso a información de subida del servidor mediante \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Salta cualquier tiempo de espera para comandos establecido en la configuración.", "skinsrestorer.permission_bypassdisabled": "Desbloquea cualquier skin desactivado en la configuración.", "skinsrestorer.permission_ownskin": "Permite el acceso para establecer tu propia skin.", "skinsrestorer.duration_day": " día", "skinsrestorer.duration_days": " días", "skinsrestorer.duration_hour": " hora", "skinsrestorer.duration_hours": " horas", "skinsrestorer.duration_minute": " minuto", "skinsrestorer.duration_minutes": " minutos", "skinsrestorer.duration_second": " segundo", "skinsrestorer.duration_seconds": " segundos"}