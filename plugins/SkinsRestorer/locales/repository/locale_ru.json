{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Изменяет ваш собственный скин.", "skinsrestorer.help_skins": "Открывает интерфейс скинов.", "skinsrestorer.help_sr": "Команды администратора для SkinsRestorer.", "skinsrestorer.help_skin_help": "Показывает эту команду справки.", "skinsrestorer.help_skin_set": "Изменяет ваш скин.", "skinsrestorer.help_skin_set_other": "Устанавливает скин для выбранного игрока.", "skinsrestorer.help_skin_set_url": "Изменяет ваш скин по URL.", "skinsrestorer.help_skin_clear": "Сбросить скин.", "skinsrestorer.help_skin_clear_other": "Сбросить скин выбранного игрока.", "skinsrestorer.help_skin_random": "Выдает случайный скин.", "skinsrestorer.help_skin_random_other": "Устанавливает случайный скин для выбранного игрока.", "skinsrestorer.help_skin_search": "Найти нужный вам скин.", "skinsrestorer.help_skin_edit": "Внесите изменения в текущий скин онлайн.", "skinsrestorer.help_skin_update": "Обновляет ваш скин.", "skinsrestorer.help_skin_update_other": "Обновляет скин выбранного игрока.", "skinsrestorer.help_skin_undo": "Возвращает ваш скин к предыдущему скину.", "skinsrestorer.help_skin_undo_other": "Возвращает скин выбранного игрока к предыдущему скину.", "skinsrestorer.help_skin_favourite": "Сохраняет скин в избранные.", "skinsrestorer.help_skin_favourite_other": "Сохраняет скин указанного игрока в избранные.", "skinsrestorer.help_skull": "", "skinsrestorer.help_skull_help": "Команды для выдачи какой-либо головы.", "skinsrestorer.help_skull_get": "Выдаёт вам голову игрока.", "skinsrestorer.help_skull_get_other": "Выдать голову другому игроку.", "skinsrestorer.help_skull_get_url": "Выдать голову игрока используя URL-ссылку на скин.", "skinsrestorer.help_skull_random": "Выдать случайную голову.", "skinsrestorer.help_skull_random_other": "Выдать случайную голову другому игроку.", "skinsrestorer.help_sr_reload": "Перезагружает конфигурационный файл.", "skinsrestorer.help_sr_status": "Проверяет необходимые API службы плагинов.", "skinsrestorer.help_sr_drop": "Удаляет данные игрока или скина из базы данных.", "skinsrestorer.help_sr_info": "Отображ<PERSON>ет информацию об игроке или скине.", "skinsrestorer.help_sr_apply_skin": "Повторно применить скин для выбранного игрока.", "skinsrestorer.help_sr_create_custom": "Создаёт пользовательский скин для всего сервера.", "skinsrestorer.help_sr_purge_old_data": "Удаляет старые скины, полученные более x дней назад.", "skinsrestorer.help_sr_dump": "Загрузка вспомогательных данных на bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URL-адрес должен быть в кавычках. Например: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (вы можете нажать Tab для автозаполнения кавычек)", "skinsrestorer.success_skin_change": "Ваш скин был изменен.", "skinsrestorer.success_skin_change_other": "Вы изменили скин игрока <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "<PERSON><PERSON><PERSON> скин <yellow><skin></yellow> изменён на предыдущий скин от <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "<PERSON><PERSON><PERSON><PERSON> <yellow><skin></yellow> игрока <yellow><name></yellow> изменён на предыдущий скин от <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "<PERSON><PERSON><PERSON> скин <yellow><skin></yellow> установлен как избранный.", "skinsrestorer.success_skin_favourite_other": "<PERSON><PERSON><PERSON><PERSON> <yellow><skin></yellow> игрока <yellow><name></yellow> установлен как избранный.", "skinsrestorer.success_skin_unfavourite": "Ваш избран<PERSON><PERSON>й скин <yellow><skin></yellow> от <yellow><timestamp></yellow> удален из избранного.", "skinsrestorer.success_skin_unfavourite_other": "Избран<PERSON><PERSON>й скин <yellow><skin></yellow> игрока <yellow><name></yellow> от <yellow><timestamp></yellow> удален из избранного.", "skinsrestorer.success_skin_clear": "<PERSON>а<PERSON> скин был удален.", "skinsrestorer.success_skin_clear_other": "<PERSON><PERSON>ин игрока <yellow><name></yellow> сброшен.", "skinsrestorer.success_updating_skin": "Ва<PERSON> скин был обновлен.", "skinsrestorer.success_updating_skin_other": "Скин игрока <yellow><name></yellow> обновлен.", "skinsrestorer.success_skull_get": "Голова выдана.", "skinsrestorer.success_skull_get_other": "Вам выдана голова <yellow><name></yellow>.", "skinsrestorer.success_admin_applyskin": "Скин игрока был обновлен!", "skinsrestorer.success_admin_createcustom": "С<PERSON>ин <yellow><skin></yellow> создан!", "skinsrestorer.success_admin_setcustomname": "Имя скина <yellow><skin></yellow> изменено на <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "Данные <type> были сброшены у <target>.", "skinsrestorer.success_admin_reload": "Конфиг и Язык были перезагружены!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green>Нажмите, чтобы использовать <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> от <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green>Нажмите, чтобы установить <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> от <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Име<PERSON>т ошибку <dark_gray>:<red><message>", "skinsrestorer.error_generic_skin": "Произошла ошибка когда вы выполняли запрос скина, попробуйте позже!", "skinsrestorer.error_no_undo": "У вас не было никаких предыдущих скинов, чтобы вернуться к ним!", "skinsrestorer.error_no_skin_to_favourite": "У вас нет скина для добавления в избранное!", "skinsrestorer.error_skin_disabled": "Этот скин был отключен администратором сервера.", "skinsrestorer.error_skinurl_disallowed": "Этот домен не был разрешен администратором сервера.", "skinsrestorer.error_updating_skin": "Произошла ошибка при попытке обновлении скина. Пожалуйста, попробуйте позже!", "skinsrestorer.error_updating_url": "Вы не можете обновлять ссылку скинов!<newline><red> Попробуйте снова использовать команду: /skin url", "skinsrestorer.error_updating_customskin": "Скин не может быть обновлен потому что он пользовательский.", "skinsrestorer.error_invalid_urlskin": "Неверно указана URL-ссылка или формат, <newline><red>Попробуйте загрузить ваш скин на imgur и используя ПКМ 'скопировать адрес картинки' <newline><red>Гайд: <red><underlined><hover:show_text:'<dark_green>Кликните для открытия'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>.", "skinsrestorer.error_admin_applyskin": "Скин игрока НЕ МОЖЕТ БЫТЬ обновлен!", "skinsrestorer.error_ms_full": "Загрузка вашего скина через MineSkin API заняла слишком долгое время. Пожалуйста, повторите попытку позже.", "skinsrestorer.error_ms_api_failed": "MineSkincSkinData API перезагружено. Пожалуйста, попробуйте позже!", "skinsrestorer.error_ms_api_key_invalid": "Ошибка: Неправильный Mineskin API ключ! Сообщите об этом владельцу сервера!", "skinsrestorer.error_ms_unknown": "Неизвестная ошибка MineSkin!", "skinsrestorer.error_no_history": "У вас нет истории скинов!", "skinsrestorer.error_no_favourites": "У вас нет избранных скинов!", "skinsrestorer.error_player_refresh_no_mapping": "Не удалось обновить ваш скин т.к. данная версия Minecraft не поддерживается SkinsRestorer. Пожалуйста, сообщите администратору сервера обновить плагин SkinsRestorer.", "skinsrestorer.not_connected_to_server": "<red>Вы не подключены ни к одному серверу.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Запуск проверки службы...", "skinsrestorer.admincommand_status_uuid_api": "<gray>Рабочие API UUID: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>API рабочего профиля: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>Пла<PERSON><PERSON>н сейчас в рабочем состоянии.", "skinsrestorer.admincommand_status_degraded": "<green>Плагин находится в деградированном состоянии, некоторые функции могут работать не в полной мере.", "skinsrestorer.admincommand_status_broken": "<red>Плагин сейчас не может быть совместим с новыми скинами.<newline> Соединение, вероятно, заблокировано из-за брандмауэра.<newline> Пожалуйста, посетите https://skinsrestorer.net/firewal для информации", "skinsrestorer.admincommand_status_firewall": "<red>Соединения, вероятно, заблокированы из-за брандмауэра.<newline>Пожалуйста, прочитайте https://skinsrestorer.net/firewall для получения дополнительной информации.", "skinsrestorer.admincommand_status_summary_server": "<gray>Сервер: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Прокси-режим: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Коммит: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>Игрок <yellow><player></yellow> не найден.", "skinsrestorer.admincommand_drop_skin_not_found": "<red><PERSON><PERSON><PERSON><PERSON> <yellow><skin></yellow> не найден.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Нам не удалось связаться с Mojang-ом для UUID игрока", "skinsrestorer.admincommand_info_checking": "<gray>Сбор запрошенных данных...", "skinsrestorer.admincommand_info_player": "<gray>UUID игрока: <gold><uuid><newline><gray>Идентификатор скина: <gold><identifier><newline><gray>Вариант скина: <gold><variant><newline><gray>Тип скина: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>Необходимо указать UUID игрока.", "skinsrestorer.admincommand_info_no_set_skin": "<red>У игрока нет установленного скина.", "skinsrestorer.admincommand_info_url_skin": "<gray>URL-ссылка скина: <gold><click:open_url:'<url>'><url></click><newline><gray>MineSkin ID: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Жестко закодированный скин: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Пользовательский скин: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>С<PERSON>ин игрока: <gold><skin><newline><gray>Метка времени: <gold><timestamp><newline><gray>Истекает: <gold><expires>", "skinsrestorer.admincommand_info_generic": "<gray>URL-ссылка: <gold><click:open_url:'<url>'><url></click><newline><gray>Вариант: <gold><variant><newline><gray>Профиль UUID: <gold><uuid><newline><gray>Имя профиля: <gold><name><newline><gray>Время запроса: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>Старые скины успешно удалены!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>Произошла ошибка во время удаления старых скинов!", "skinsrestorer.admincommand_dump_uploading": "<green>Загрузка данных на bytebin.lucko.me...", "skinsrestorer.admincommand_dump_success": "<green>Выгружено! <yellow><click:open_url:'<url>'><url></click>", "skinsrestorer.admincommand_dump_error": "<red>Ошибка при загрузке данных на bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<dark_red>Ош<PERSON>бк<PERSON><dark_gray>: <red>Команды отключены для сервера <server>.", "skinsrestorer.command_unknown_player": "Неизвестный игрок: <name>", "skinsrestorer.command_no_targets_supplied": "Не указаны целевые игроки.", "skinsrestorer.player_has_no_permission_skin": "<dark_red>Ошибка<dark_gray>: <red>У вас нет разрешения на установку этого скина.", "skinsrestorer.player_has_no_permission_url": "<dark_red>Ош<PERSON>бка<dark_gray>: <red>У вас недостаточно прав для установки скинов по URL.", "skinsrestorer.not_premium": "<dark_red>Ошибка<dark_gray>: <red>Премиум игрока с таким именем не существует.", "skinsrestorer.only_allowed_on_console": "<dark_red>Ошибка<dark_gray>: <red>Эту команду может выполнить только консоль!", "skinsrestorer.only_allowed_on_player": "<dark_red>Ошибка<dark_gray>: <red>Только игроки могут выполнить эту команду!", "skinsrestorer.invalid_player": "<dark_red>Ош<PERSON>бка: <dark_gray>: <red><input> не является допустимым именем пользователя или URL-адресом.", "skinsrestorer.skin_cooldown": "<dark_red>Ошибка<dark_gray>: <red>Вы сможете изменить свой скин только через: <yellow><time></yellow>", "skinsrestorer.ms_uploading_skin": "<dark_green>Загрузка скина, пожалуйста подождите... (Это может занять некоторое время)", "skinsrestorer.wait_a_minute": "<dark_red>Ошибка<dark_gray>: <red>Пожалуйста, подождите минуту перед повторным запросом этого скина. (Лимит Ограничен)", "skinsrestorer.skinsmenu_open": "<dark_green>Открытие меню скинов...", "skinsrestorer.skinsmenu_title_select": "<blue>Главное меню скинов", "skinsrestorer.skinsmenu_title_main": "<blue>Ка<PERSON><PERSON><PERSON><PERSON><PERSON> скинов - страница <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue>История скинов -- страница <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue>Избранное - Страница <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»<gray> Следующая страница</gray><bold> »</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Предыдущая страница</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[ <gray> Удалить скин</gray><bold> ]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>Нажмите, чтобы выбрать скин", "skinsrestorer.skinsmenu_history_lore": "<blue>С<PERSON>ин от <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Клик + Shift чтобы добавить в избранное", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Клик + Shift чтобы удалить из избранного", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>В избранных скинах с <time>", "skinsrestorer.skinsmenu_no_permission": "<red>У Вас не прав на установку этого скина.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>Каталог скинов </gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>История скинов</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>Избранные скины</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>Вернуться в Главное Меню</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green>Вы можете найти скины, соответствующие <green><search></green>, здесь: <newline><green><hover:show_text:'<dark_green>Нажмите, чтобы открыть'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover></green><newline><newline><aqua>Если скины не найдены, вы всегда можете найти их по адресу: https://namemc.com/minecraft-skins/tag <newline>Чтобы установить скин, используйте ссылку:<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green>Вы можете изменить свой скин по <u><aqua><hover:show_text:'<dark_green>Нажмите, чтобы открыть'><click:open_url:'<url>'>этой ссылке</click></hover></aqua></u><newline><dark_green>Чтобы узнать, как применить изменённый скин, посетите: <newline><green><hover:show_text:'<dark_green>Нажмите, чтобы открыть'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover></green>", "skinsrestorer.no_skin_data": "<dark_red>Ошибка<dark_gray>: <red>Не найдены данные скина! Имеет ли этот игрок скин?", "skinsrestorer.outdated": "<dark_red>Вы используете устаревшую версию SkinsRestorer на <platform>!\n<newline><red>Пожалуйста, обновитесь до последней версии используя Modrinth: \n<newline><yellow><hover:show_text:'<dark_green>Кликните для открытия'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red>Версия Java (<version>) на вашем <platform> не поддерживается SkinsRestorer!<newline><red>Обновите версию Java до версии 17 или выше, чтобы использовать SkinsRestorer без проблем. Более новые версии Java также могут работать на старых серверах, поэтому сервер Minecraft 1.8 может работать на Java 17. Для получения более подробной информации прочтите информацию в консоли.", "skinsrestorer.permission_player_wildcard": "Полный доступ для игроков", "skinsrestorer.permission_command": "Разрешает доступ к основным командам \"/skin\".", "skinsrestorer.permission_command_set": "Разрешает изменить ваш скин.", "skinsrestorer.permission_command_set_url": "Позволяет изменить свой скин с помощью URL.", "skinsrestorer.permission_command_clear": "Разрешает очистить ваш скин.", "skinsrestorer.permission_command_random": "Разреша<PERSON>т устанавливать случайный скин.", "skinsrestorer.permission_command_update": "Разрешает обновлять ваш скин.", "skinsrestorer.permission_command_undo": "Разрешает возвращать ваш скин к предыдущему скину.", "skinsrestorer.permission_command_favourite": "Позволяет установить скин в качестве избранного.", "skinsrestorer.permission_command_search": "Разрешает искать ваш скин.", "skinsrestorer.permission_command_edit": "Позволяет редактировать ваш скин.", "skinsrestorer.permission_command_gui": "Разрешает открывать меню скинов.", "skinsrestorer.permission_admin_wildcard": "Полный доступ для администра<PERSON>оров", "skinsrestorer.permission_admincommand": "Разрешает доступ к основным командам \"/skin\".", "skinsrestorer.permission_command_set_other": "Позволяет изменить скин другому игроку.", "skinsrestorer.permission_command_clear_other": "Позволяет удалить скин другого игрока.", "skinsrestorer.permission_command_random_other": "Разрешает установить случайный скин другому игроку.", "skinsrestorer.permission_command_update_other": "Позволяет обновить скин другого игрока.", "skinsrestorer.permission_command_favourite_other": "Позволяет добавить скин в избранное другому игроку.", "skinsrestorer.permission_command_undo_other": "Разрешает возвращать скин другого игрока к предыдущему скину.", "skinsrestorer.permission_admincommand_skull": "Выдать разрешение на использование команд \"/skull\".", "skinsrestorer.permission_admincommand_skull_get": "Выдать разрешение на получение головы игрока.", "skinsrestorer.permission_admincommand_skull_get_url": "Выдать разрешение на получение головы игрока по URL-ссылке на скин.", "skinsrestorer.permission_admincommand_skull_random": "Выдать разрешение на получение случайной головы.", "skinsrestorer.permission_admincommand_skull_get_other": "Выдать разрешение на выдачу головы другому игроку.", "skinsrestorer.permission_admincommand_skull_random_other": "Выдать разрешение на выдачу случайной головы другому игроку.", "skinsrestorer.permission_admincommand_reload": "Позволяет получить доступ к команде \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Позволяет получить доступ к команде \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Разрешает удалять .SKIN файл.", "skinsrestorer.permission_admincommand_info": "Позволяет получить доступ к информации об игроке или скине.", "skinsrestorer.permission_admincommand_applyskin": "Разрешает обновить скин другого игрока.", "skinsrestorer.permission_admincommand_createcustom": "Разрешает доступ к созданию собственного глобального скина через URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Позволяет удалять старые данные о скине.", "skinsrestorer.permission_admincommand_dump": "Разрешает загрузку информации сервера через \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Обходит любые задержки для команд в файле конфигураций.", "skinsrestorer.permission_bypassdisabled": "Обходит выключенные скины в файле конфигураций.", "skinsrestorer.permission_ownskin": "Разрешает установить ваш собственный скин.", "skinsrestorer.duration_day": " день", "skinsrestorer.duration_days": " <PERSON><PERSON><PERSON><PERSON>", "skinsrestorer.duration_hour": " час", "skinsrestorer.duration_hours": " <PERSON><PERSON><PERSON><PERSON>", "skinsrestorer.duration_minute": " минута", "skinsrestorer.duration_minutes": " минут", "skinsrestorer.duration_second": " секунда", "skinsrestorer.duration_seconds": " секунд"}