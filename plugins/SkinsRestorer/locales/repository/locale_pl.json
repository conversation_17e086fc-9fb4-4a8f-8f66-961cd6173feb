{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Zmienia twoją skórkę.", "skinsrestorer.help_skins": "Otwiera interfejs skinów.", "skinsrestorer.help_sr": "<PERSON><PERSON><PERSON> administracy<PERSON>e dla SkinsRestorer.", "skinsrestorer.help_skin_help": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to polecenie pomocy.", "skinsrestorer.help_skin_set": "Zmienia twojego skina.", "skinsrestorer.help_skin_set_other": "<PERSON><PERSON><PERSON><PERSON> skina dla wybranego gracza.", "skinsrestorer.help_skin_set_url": "Zmienia skórkę z adresu URL.", "skinsrestorer.help_skin_clear": "<PERSON><PERSON><PERSON> two<PERSON>a.", "skinsrestorer.help_skin_clear_other": "<PERSON><PERSON><PERSON> skina danego gracza.", "skinsrestorer.help_skin_random": "<PERSON><PERSON> skórkę.", "skinsrestorer.help_skin_random_other": "Ustawia losową skórkę dla wybranego gracza.", "skinsrestorer.help_skin_search": "<PERSON><PERSON><PERSON><PERSON> skina, którego chcesz", "skinsrestorer.help_skin_edit": "Edytuj bieżącą skórkę online.", "skinsrestorer.help_skin_update": "Aktualizuje two<PERSON>a.", "skinsrestorer.help_skin_update_other": "Aktualizuje skina danego gracza.", "skinsrestorer.help_skin_undo": "Przywraca skórę z powrotem do poprzedniej skórki.", "skinsrestorer.help_skin_undo_other": "Przywraca skórkę wybranego gracza z powrotem do poprzedniej skóry.", "skinsrestorer.help_skin_favourite": "Zapisuje skórkę jako ulubioną.", "skinsrestorer.help_skin_favourite_other": "Zapisuje skórkę wybranego gracza jako ulubioną.", "skinsrestorer.help_skull": "Daje ci czaszkę.", "skinsrestorer.help_skull_help": "Polecenia czaszki dla SkinsRestorer.", "skinsrestorer.help_skull_get": "Daje ci czaszkę.", "skinsrestorer.help_skull_get_other": "<PERSON><PERSON> innemu grac<PERSON>.", "skinsrestorer.help_skull_get_url": "Daje czaszkę na podstawie adresu URL skórki.", "skinsrestorer.help_skull_random": "Daje ci losową czaszkę.", "skinsrestorer.help_skull_random_other": "<PERSON><PERSON> czaszkę innemu graczowi.", "skinsrestorer.help_sr_reload": "Ponownie ładuje plik konfiguracyjny.", "skinsrestorer.help_sr_status": "Sprawdza wymagane usługi API pluginów.", "skinsrestorer.help_sr_drop": "Usuwa dane gracza lub skórkę z bazy danych.", "skinsrestorer.help_sr_info": "Wyświetla informacje o graczu lub skórce.", "skinsrestorer.help_sr_apply_skin": "Ponownie zastosuj skina dla wskazanego gracza.", "skinsrestorer.help_sr_create_custom": "Utwórz niestandardowego skina dla całego serwera.", "skinsrestorer.help_sr_purge_old_data": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć stare dane skina z x dni temu.", "skinsrestorer.help_sr_dump": "Prześlij dane pomocnicze na bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "Adresy URL muszą być cytowane. Przykład: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (<PERSON>ż<PERSON><PERSON> nacis<PERSON> zakład<PERSON>, aby automatycznie uzupełnić cytaty)", "skinsrestorer.success_skin_change": "Twoja skórka została zmieniona.", "skinsrestorer.success_skin_change_other": "Zmieniłeś skórkę <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "<PERSON>ja sk<PERSON> <yellow><skin></yellow> została przywrócona do skórki od <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "S<PERSON><PERSON><PERSON> <yellow><skin></yellow> z <yellow><name></yellow> została przywrócona do skórki z <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "<PERSON><PERSON> s<PERSON> <yellow><skin></yellow> została ustawiona jako ulubiona.", "skinsrestorer.success_skin_favourite_other": "<PERSON><PERSON><PERSON><PERSON> <yellow><skin></yellow> użytkownika <yellow><name></yellow> została ustawiona jako ulubi<PERSON>.", "skinsrestorer.success_skin_unfavourite": "Twoja ulubiona skórka <yellow><skin></yellow> od <yellow><timestamp></yellow> została usunięta z ulubionych.", "skinsrestorer.success_skin_unfavourite_other": "Ulubiona skórka <yellow><skin></yellow> użytkownika <yellow><name></yellow> z <yellow><timestamp></yellow> została usunięta z ulubionych.", "skinsrestorer.success_skin_clear": "Twoja skórka została zresetowana.", "skinsrestorer.success_skin_clear_other": "Skórka wyczyszczona dla gracza <yellow><name></yellow>.", "skinsrestorer.success_updating_skin": "Twoja skórka została zaktualizowana.", "skinsrestorer.success_updating_skin_other": "Skórka zaktualizowana dla gracza <yellow><name></yellow>.", "skinsrestorer.success_skull_get": "Otr<PERSON><PERSON><PERSON><PERSON><PERSON> czaszkę.", "skinsrestorer.success_skull_get_other": "<PERSON><PERSON><PERSON><PERSON> <yellow><name></yellow> czasz<PERSON><PERSON>.", "skinsrestorer.success_admin_applyskin": "Skórka gracza została odświeżona!", "skinsrestorer.success_admin_createcustom": "<PERSON><PERSON><PERSON><PERSON> <yellow><skin></yellow> została utworzona!", "skinsrestorer.success_admin_setcustomname": "Nazwa skórki <yellow><skin></yellow> została ustawiona na <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "Dane <type> zostały usunięte z <target>.", "skinsrestorer.success_admin_reload": "Konfiguracja i ustawienia zostały załadowane ponownie!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green><PERSON><PERSON><PERSON><PERSON> a<PERSON> <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> z <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green><PERSON><PERSON><PERSON><PERSON> a<PERSON> <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> z <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Błąd<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "Wystąpił błąd podczas żądania danych skórki, spróbuj ponownie później!", "skinsrestorer.error_no_undo": "<PERSON>e masz skórek, aby p<PERSON><PERSON><PERSON><PERSON>!", "skinsrestorer.error_no_skin_to_favourite": "<PERSON><PERSON> masz <PERSON>adnej s<PERSON>órki, aby usta<PERSON><PERSON> jako ulubi<PERSON>!", "skinsrestorer.error_skin_disabled": "Ta skórka jest wyłączona przez administratora.", "skinsrestorer.error_skinurl_disallowed": "Ta domena nie jest dozwolona przez administratora.", "skinsrestorer.error_updating_skin": "Wystąpił błąd podczas aktualizacji skórki. Spróbuj ponownie później!", "skinsrestorer.error_updating_url": "Nie można aktualizować niestandardowych skórek adresów URL! <newline><red>Użyj ponownie /skin url", "skinsrestorer.error_updating_customskin": "Skórka nie może <PERSON><PERSON>, poniew<PERSON>ż jest niestandardowa.", "skinsrestorer.error_invalid_urlskin": "Nieprawidłowy adres lub format <newline><red>Spróbuj przesłać skórkę do imgur i kliknij prawym przyciskiem myszy na 'skopiuj adres obrazu' <newline><red>Aby uzyskać poradnik: <red><underlined><hover:show_text:'<dark_green>Kliknij aby otworzyć'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>.", "skinsrestorer.error_admin_applyskin": "NIE można odświeżyć skórki gracza!", "skinsrestorer.error_ms_full": "Upłynął limit czasu połączenia z MineSkin API podczas przesyłania twojej skórki. Spróbuj ponownie później.", "skinsrestorer.error_ms_api_failed": "MineSkin API jest prz<PERSON><PERSON><PERSON>, spróbuj ponownie później!", "skinsrestorer.error_ms_api_key_invalid": "Nieprawidłowy klucz MineSkin API!, skontaktuj się z właścicielem serwera!", "skinsrestorer.error_ms_unknown": "Nieznany błąd MineSkin!", "skinsrestorer.error_no_history": "Nie masz historii skórek!", "skinsrestorer.error_no_favourites": "Nie masz ulubionych skórek!", "skinsrestorer.error_player_refresh_no_mapping": "Nie można odświ<PERSON>żyć skórki, ponieważ ta wersja Minecraft nie jest obsługiwana przez SkinsRestorer. Poinformuj administratora serwera o aktualizacji wtyczki SkinsRestorer.", "skinsrestorer.not_connected_to_server": "<red><PERSON><PERSON> j<PERSON> połączony z żadnym serwerem.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Sprawdzanie usług potrzebnych do prawidłowego działania SR...", "skinsrestorer.admincommand_status_uuid_api": "<gray>Pracujące UUID API: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>API profilu pracy: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green><PERSON><PERSON><PERSON><PERSON> jest obecnie w stanie roboczym.", "skinsrestorer.admincommand_status_degraded": "<green><PERSON><PERSON><PERSON><PERSON> jest w stanie awary<PERSON>ym, niektóre funkcje mogą nie d<PERSON>ła<PERSON> w pełni.", "skinsrestorer.admincommand_status_broken": "<red>Wtyczka obecnie nie może pobrać nowych skórek.<newline>Połączenie jest prawdopodobnie zablokowane z powodu zapory ogniowej.<newline>Więcej informacji można znaleźć na stronie https://skinsretorer.net/firewall", "skinsrestorer.admincommand_status_firewall": "<red>Połączenia są prawdopodobnie zablokowane ze względu na zaporę.<newline>Przeczytaj https://skinsrestorer.net/firewall aby uzyskać więcej informacji.", "skinsrestorer.admincommand_status_summary_server": "<gray>Serwer: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Tryb proxy: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray><PERSON><PERSON><PERSON><PERSON><PERSON>: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON>z <yellow><player></yellow> nie znaleziony.", "skinsrestorer.admincommand_drop_skin_not_found": "<red><PERSON><PERSON><PERSON><PERSON> <yellow><skin></yellow> nie znaleziona.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Nie udało nam się skontaktować z Mojangiem w celu uzyskania identyfikatora UUID gracza", "skinsrestorer.admincommand_info_checking": "<gray><PERSON><PERSON><PERSON><PERSON> da<PERSON>...", "skinsrestorer.admincommand_info_player": "<gray>G<PERSON><PERSON>: <gold><player>", "skinsrestorer.admincommand_info_invalid_uuid": "<red><PERSON><PERSON><PERSON> UUID gracza.", "skinsrestorer.admincommand_info_no_set_skin": "<red><PERSON><PERSON><PERSON> nie ma wyraźnie ustawionej skóry.", "skinsrestorer.admincommand_info_url_skin": "<gray><PERSON><PERSON> s<PERSON>: <gold><click:open_url:'<url>'><url></click><newline><gray>MineSkin ID: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray><PERSON><PERSON><PERSON><PERSON> zak<PERSON>: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Niestandardowa skórka: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray><PERSON><PERSON><PERSON><PERSON>: <gold><skin>", "skinsrestorer.admincommand_info_generic": "<gray><PERSON><PERSON> tekstury: <gold><click:open_url:'<url>'><url></click><newline><gray>Wariant: <gold><variant><newline><gray>Profil UUID: <gold><uuid><newline><gray>Nazwa profilu: <gold><name><newline><gray>Czas żądania: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>Pomyślnie wyczyszczono stare skórki!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>Wys<PERSON>ą<PERSON>ł błąd podczas czyszczenia starych skórek!", "skinsrestorer.admincommand_dump_uploading": "<green>Przesyłanie danych do bytebin.lucko.me...", "skinsrestorer.admincommand_dump_success": "<green>Przesyłanie zakończone pomyślnie! <yellow><click:open_url:'<url>'><url></click>", "skinsrestorer.admincommand_dump_error": "<red>Błąd podczas przesyłania danych do bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<dark_red>Błąd<dark_gray>: <red>Komendy zostały wyłączone dla serwera <server>.", "skinsrestorer.command_unknown_player": "<PERSON><PERSON><PERSON><PERSON> gracz: <name>", "skinsrestorer.command_no_targets_supplied": "<PERSON><PERSON> dos<PERSON>ono żadnych docelowych graczy.", "skinsrestorer.player_has_no_permission_skin": "<dark_red>Błąd<dark_gray>: <red>Nie posiadasz uprawnień do ustawienia tego skina.", "skinsrestorer.player_has_no_permission_url": "<dark_red>Błąd<dark_gray>: <red>Nie posiadasz uprawnień do ustawiania skinów przez adres URL.", "skinsrestorer.not_premium": "<dark_red>Błąd<dark_gray>: <red>Gracz premium z takim nickiem nie istnieje.", "skinsrestorer.only_allowed_on_console": "<dark_red>Error<dark_gray>: <red><PERSON><PERSON><PERSON> k<PERSON> może wyk<PERSON> to polecenie!", "skinsrestorer.only_allowed_on_player": "<dark_red>Error<dark_gray>: <red><PERSON><PERSON><PERSON> gracze mogą wykonać tą komendę!", "skinsrestorer.invalid_player": "<dark_red>Błąd<dark_gray>: <red><input> nie jest prawidłową nazwą użytkownika ani adresem URL.", "skinsrestorer.skin_cooldown": "<dark_red>Błąd<dark_gray>: <red>Możesz ponownie zmienić swoją skórkę w: <yellow><time></yellow>", "skinsrestorer.ms_uploading_skin": "<dark_green><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> czeka<PERSON>...(To moż<PERSON> zaj<PERSON> trochę czasu)", "skinsrestorer.wait_a_minute": "<dark_red>Błąd<dark_gray>: <red>Odczekaj chwilę zanim znów wyśleś zapytanie odnośnie tego skina. (Ograniczenie czasowe)", "skinsrestorer.skinsmenu_open": "<dark_green>Otwieranie menu skinów...", "skinsrestorer.skinsmenu_title_select": "<blue>Wybierz menu", "skinsrestorer.skinsmenu_title_main": "<blue><PERSON><PERSON> - St<PERSON> <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue><PERSON><PERSON> His<PERSON>ii - Strona <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue><PERSON><PERSON> - St<PERSON> <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»<gray> Następna Strona</gray><bold> »</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Poprzednia strona</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[ <gray>Usuń Skina</gray><bold> ]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray><PERSON><PERSON><PERSON><PERSON> aby wybrać tą skórkę", "skinsrestorer.skinsmenu_history_lore": "<blue>Skórka z <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + klik<PERSON>j aby usta<PERSON> jako ul<PERSON>one", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + k<PERSON><PERSON><PERSON>, aby <PERSON> z ulubionych", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>Ulubione od <time>", "skinsrestorer.skinsmenu_no_permission": "<red>Nie masz uprawnień do używania tej skórki.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON></gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>Menu Historii</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON></gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray><PERSON>u wyboru</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green><PERSON><PERSON><PERSON><PERSON> znale<PERSON> pasujące skórki <green><search></green> tutaj: <newline><green><hover:show_text:'<dark_green><PERSON><PERSON><PERSON><PERSON>, aby otwo<PERSON><PERSON><PERSON>'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>Je<PERSON><PERSON> nie znajd<PERSON>sz żadnych skórek, zaws<PERSON> możesz znaleźć jakieś skórki na https://namemc.com/minecraft-skins/tag <newline>Moż<PERSON>z ustawić skórkę korzystając z linku do skórki:<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green><PERSON><PERSON><PERSON><PERSON> edyt<PERSON> swoją skórkę na <u><aqua><hover:show_text:'<dark_green><PERSON><PERSON><PERSON><PERSON>, aby otwo<PERSON><PERSON><PERSON>'><click:open_url:'<url>'>ten link</click></hover></aqua></u> <newline><dark_green>Aby dowiedzieć się jak zastosować edytowaną skórkę, odwied<PERSON>: <newline><green><hover:show_text:'<dark_green>K<PERSON><PERSON>j, aby otworzy<PERSON>'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover>", "skinsrestorer.no_skin_data": "<dark_red>Błąd<dark_gray>: <red>Nie znaleziono danych skórki! C<PERSON> ten gracz ma skórkę?", "skinsrestorer.outdated": "<dark_red>Używasz przestarzałej wersji SkinsRestorer na swoim <platform>!<newline><red>Zaktualizuj do najnowszej wersji na Modrinth: <newline><yellow><hover:show_text:'<dark_green>Kliknij aby ot<PERSON>'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red>Wersja Java (<version>) twojego <platform> nie jest obsługiwana przez SkinsRestorer!<newline><red>Aby móc korzystać z SkinsRestorer bez problemów, zaktualizuj Java do wersji 17 lub nowszej. Nowsze wersje Java mogą również obsługiwać starsze serwery, więc serwer Minecraft 1.8 może działać na Javie 17. Przeczytaj informacje w konsoli, aby uzyskać więcej szczegółów.", "skinsrestorer.permission_player_wildcard": "Permisje dla graczy", "skinsrestorer.permission_command": "Umożliwia dostęp do głównych poleceń „/skin”.", "skinsrestorer.permission_command_set": "Umożliwia dostęp do zmiany skórki.", "skinsrestorer.permission_command_set_url": "Umożliwia dostęp do zmiany skórki za pomocą adresu URL.", "skinsrestorer.permission_command_clear": "Umożliwia dostęp w celu zresetowania skórki.", "skinsrestorer.permission_command_random": "Umożliwia dostęp do ustawiania losowej skórki.", "skinsrestorer.permission_command_update": "Umożliwia dostęp do aktualizacji skórki.", "skinsrestorer.permission_command_undo": "Zezwala na dostęp do przywrócenia skórki z powrotem do poprzedniej skórki.", "skinsrestorer.permission_command_favourite": "Zezwala na dostęp do ustawiania skórki jako ulubionej.", "skinsrestorer.permission_command_search": "Umożliwia dostęp do wyszukiwania twojej skórki.", "skinsrestorer.permission_command_edit": "Umożliwia dostęp do edycji skóry.", "skinsrestorer.permission_command_gui": "Umożliwia dostęp do GUI otwartych skórek.", "skinsrestorer.permission_admin_wildcard": "Permisje dla administratorów", "skinsrestorer.permission_admincommand": "Umożliwia dostęp do głównych poleceń „/sr”.", "skinsrestorer.permission_command_set_other": "Umożliwia dostęp do ustawienia skórki innego gracza.", "skinsrestorer.permission_command_clear_other": "Umożliwia dostęp do usunięcia skórki innego gracza.", "skinsrestorer.permission_command_random_other": "Umożliwia dostęp do ustawiania losowej skórki innemu graczowi.", "skinsrestorer.permission_command_update_other": "Umożliwia dostęp do aktualizacji skórki innego gracza.", "skinsrestorer.permission_command_favourite_other": "Zezwala na dostęp do ustawiania skórki jako ulubionej dla innego gracza.", "skinsrestorer.permission_command_undo_other": "Zezwala na dostęp do przywrócenia skórki gracza do poprzedniej skórki.", "skinsrestorer.permission_admincommand_skull": "Umożliwia dostęp do głównych poleceń \"/skull\".", "skinsrestorer.permission_admincommand_skull_get": "Umożliwia dostęp do otrzymania czaszki.", "skinsrestorer.permission_admincommand_skull_get_url": "Umożliwia dostęp do otrzymania czaszki przez adres URL.", "skinsrestorer.permission_admincommand_skull_random": "Umożliwia dostęp do losowej czaszki.", "skinsrestorer.permission_admincommand_skull_get_other": "Umożliwia dostęp do nadania innemu graczowi czaszki.", "skinsrestorer.permission_admincommand_skull_random_other": "Zezwala na dostęp do losowej czaszki innego gracza.", "skinsrestorer.permission_admincommand_reload": "Umożliwia dostęp do „/sr reload”.", "skinsrestorer.permission_admincommand_status": "Umożliwia dostęp do „/sr status”.", "skinsrestorer.permission_admincommand_drop": "Zezwala na dostęp do usuwania pliku .SKIN.", "skinsrestorer.permission_admincommand_info": "Umożliwia dostęp do informacji o skórce gracza lub skórce.", "skinsrestorer.permission_admincommand_applyskin": "Umożliwia dostęp do ponownego nałożenia skórki innego gracza.", "skinsrestorer.permission_admincommand_createcustom": "Umożliwia dostęp do tworzenia niestandardowej globalnej skórki według adresu URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Umożliwia dostęp do usuwania starych danych skórki.", "skinsrestorer.permission_admincommand_dump": "Umożliwia dostęp do przesyłania informacji o serwerze poprzez „/sr dump”.", "skinsrestorer.permission_bypasscooldown": "Pomija wszelkie czasy odnowienia poleceń ustawione w konfiguracji.", "skinsrestorer.permission_bypassdisabled": "Pomija wszystkie wyłączone skórki ustawione w konfiguracji.", "skinsrestorer.permission_ownskin": "Umożliwia dostęp do ustawienia własnej skórki.", "skinsrestorer.duration_day": " dzień", "skinsrestorer.duration_days": " dni", "skinsrestorer.duration_hour": " <PERSON><PERSON><PERSON>", "skinsrestorer.duration_hours": " godzin", "skinsrestorer.duration_minute": " minuta", "skinsrestorer.duration_minutes": " minut", "skinsrestorer.duration_second": " sekunda", "skinsrestorer.duration_seconds": " sekund"}