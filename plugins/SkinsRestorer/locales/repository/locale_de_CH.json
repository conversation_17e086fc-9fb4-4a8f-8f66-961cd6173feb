{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Ändert deinen Skin.", "skinsrestorer.help_skins": "Öffnet das Skin-GUI.", "skinsrestorer.help_sr": "Admin-Befehle für SkinsRestorer.", "skinsrestorer.help_skin_help": "<PERSON><PERSON><PERSON> diesen Hilfebefehl an.", "skinsrestorer.help_skin_set": "Ändert deinen Skin.", "skinsrestorer.help_skin_set_other": "Ändert den Skin eines bestimmten Spielers.", "skinsrestorer.help_skin_set_url": "<PERSON><PERSON><PERSON> den Skin von einer URL.", "skinsrestorer.help_skin_clear": "Entfernt deinen Skin.", "skinsrestorer.help_skin_clear_other": "Löscht den Skin eines bestimmten Spielers.", "skinsrestorer.help_skin_random": "Gibt einen zufälligen Skin.", "skinsrestorer.help_skin_random_other": "Ändert den Skin eines bestimmten Spielers.", "skinsrestorer.help_skin_search": "<PERSON>e einen Skin den du haben möchtest.", "skinsrestorer.help_skin_edit": "Bearbeiten Sie Ihr aktuellen Skin online.", "skinsrestorer.help_skin_update": "Aktualisiert deinen Skin.", "skinsrestorer.help_skin_update_other": "Aktualisiert den Skin eines bestimmten Spielers.", "skinsrestorer.help_skin_undo": "Ändert deinen Skin auf den vorherigen zurück.", "skinsrestorer.help_skin_undo_other": "Ändert den Skin eines bestimmten Spielers auf den vorherigen zurück.", "skinsrestorer.help_skin_favourite": "Speichert deinen Skin als Favorit.", "skinsrestorer.help_skin_favourite_other": "Speichert den Skin eines bestimmten Spielers als Favorit.", "skinsrestorer.help_skull": "<PERSON><PERSON><PERSON> dir ein <PERSON>.", "skinsrestorer.help_skull_help": "Befehle für Köpfe in SkinsRestorer.", "skinsrestorer.help_skull_get": "<PERSON><PERSON><PERSON> dir ein <PERSON>.", "skinsrestorer.help_skull_get_other": "<PERSON><PERSON>t einen Kopf an einen anderen Spieler.", "skinsrestorer.help_skull_get_url": "G<PERSON><PERSON> den Kopf basierend auf einer Skin-URL aus.", "skinsrestorer.help_skull_random": "<PERSON><PERSON>t dir ein <PERSON>.", "skinsrestorer.help_skull_random_other": "Vergibt einen zufälligen Schädel an einen anderen Spieler.", "skinsrestorer.help_sr_reload": "<PERSON><PERSON><PERSON> die Konfigurations-Datei neu.", "skinsrestorer.help_sr_status": "Prüft die benötigten Plugin-API-Dienste.", "skinsrestorer.help_sr_drop": "Entfernt Spieler- oder Skin-Daten aus der Datenbank.", "skinsrestorer.help_sr_info": "Zeigt Informationen über einen Spieler oder Skin an.", "skinsrestorer.help_sr_apply_skin": "Lege den Skin für einen Spieler deiner Wahl erneut an.", "skinsrestorer.help_sr_create_custom": "<PERSON><PERSON><PERSON> einen benutzerdefinierten Server-weiten Skin.", "skinsrestorer.help_sr_purge_old_data": "Lösche alte Skin-Daten von vor über x Tage lang.", "skinsrestorer.help_sr_dump": "Lade Support Daten zu bytebin.lucko.me hoch.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URLs müssen zitiert werden. Beispiel: <yellow>/skin setzen \"https://beispiel.com/skin.png\"</yellow> (<PERSON><PERSON> können Tab drücken, um die Anführungszeichen automatisch zu vervollständigen)", "skinsrestorer.success_skin_change": "<PERSON>in <PERSON> wurde geändert.", "skinsrestorer.success_skin_change_other": "<PERSON> hast den <PERSON> von <yellow><name></yellow> ge<PERSON><PERSON><PERSON>.", "skinsrestorer.success_skin_undo": "<PERSON>hr Skin <yellow><skin></yellow> wurde auf den <PERSON> von <yellow><timestamp></yellow> zurückgesetzt.", "skinsrestorer.success_skin_undo_other": "<PERSON> Skin <yellow><skin></yellow> von <yellow><name></yellow> wurde zurückgesetzt auf den Skin von <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "Dein Skin <yellow><skin></yellow> wurde als Favorit gespeichert.", "skinsrestorer.success_skin_favourite_other": "Der Skin <yellow><skin></yellow> von <yellow><name></yellow> wurde als Favorit gespeichert.", "skinsrestorer.success_skin_unfavourite": "Dein favorisierter Skin <yellow><skin></yellow> von <yellow><timestamp></yellow> wurde von den <PERSON> entfernt.", "skinsrestorer.success_skin_unfavourite_other": "Der favorisierte Skin <yellow><skin></yellow> des S<PERSON>lers <yellow><name></yellow> von <yellow><timestamp></yellow> wurde von den <PERSON>n entfernt.", "skinsrestorer.success_skin_clear": "Dein Skin wurde entfernt.", "skinsrestorer.success_skin_clear_other": "Der Skin vom Spieler <yellow><name></yellow> wurde entfernt.", "skinsrestorer.success_updating_skin": "<PERSON><PERSON> Skin wurde aktualisiert.", "skinsrestorer.success_updating_skin_other": "Der Skin vom Spieler <yellow><name></yellow> wurde aktualisiert.", "skinsrestorer.success_skull_get": "Sie haben einen Kopf erhalten.", "skinsrestorer.success_skull_get_other": "<PERSON> hast <yellow><name></yellow> einen <PERSON> g<PERSON>.", "skinsrestorer.success_admin_applyskin": "S<PERSON>ler-Skin wurde aktualisiert!", "skinsrestorer.success_admin_createcustom": "Der Skin <yellow><skin></yellow> wurde erstellt!", "skinsrestorer.success_admin_setcustomname": "Der Name vom Skin <yellow><skin></yellow> wurde zu <yellow><display_name></yellow> geändert.", "skinsrestorer.success_admin_drop": "<type>-Daten für <target> gelöscht.", "skinsrestorer.success_admin_reload": "Konfiguration und Lokale wurden neu geladen!", "skinsrestorer.success_history_line": "<dark_green><hover:show_text:'<dark_green><hover:show_text:'<dark_green> Klicken zum Benutzen<yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> vo<yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green><hover:show_text:'<dark_green> Klicken zum Benutzen<yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> von<yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red><PERSON><PERSON><dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "Ein Fehler ist bei der Anforderung von Skin-Daten aufgetreten, bitte versuche es später nochmal!", "skinsrestorer.error_no_undo": "Du hast keinen Skin, auf den du zurückgehen kannst!", "skinsrestorer.error_no_skin_to_favourite": "Du hast keinen Skin als Favorit gesetzt!", "skinsrestorer.error_skin_disabled": "Dieser Skin ist von einem Administrator gesperrt.", "skinsrestorer.error_skinurl_disallowed": "Diese Domain wurde vom Administrator nicht zu<PERSON>en.", "skinsrestorer.error_updating_skin": "Ein Fehler ist beim Aktualisieren deines Skins aufgetreten. Bitte versuche es später nochmal!", "skinsrestorer.error_updating_url": "Du kannst benutzerdefinierte URL-Skins nicht aktualisieren! <newline><red>Frage erneut mit /skin URL ab", "skinsrestorer.error_updating_customskin": "Der Skin kann nicht aktualisiert werden, weil er benutzerdefiniert ist.", "skinsrestorer.error_invalid_urlskin": "Ungültige Skin-URL oder ungültiges Format.", "skinsrestorer.error_admin_applyskin": "Der Skin des Spielers konnte NICHT aktualisiert werden!", "skinsrestorer.error_ms_full": "Bei MineSkin API ist eine Zeitüberschreitung während des Hochladens deines Skins aufgetreten. Bitte versuche es später erneut.", "skinsrestorer.error_ms_api_failed": "Die MineSkin API ist überlastet, bitte versuche es später erneut!", "skinsrestorer.error_ms_api_key_invalid": "Ungültiger MineSkin API Schlüssel! Kontaktiere den Serverbesitzer hierüber!", "skinsrestorer.error_ms_unknown": "Unbekannter MineSkin Fehler!", "skinsrestorer.error_no_history": "Du hast keine Skin-Historie!", "skinsrestorer.error_no_favourites": "Du hast keine favorisierten Skins!", "skinsrestorer.error_player_refresh_no_mapping": "Dein Skin konnte nicht aktualisiert werden, da diese Minecraft-Version von SkinsRestorer nicht unterstützt wird. Bitte informiere den Server-Admin, dass er das SkinsRestorer-Plugin updaten muss.", "skinsrestorer.not_connected_to_server": "<red><PERSON>e sind mit keinem Server verbunden.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Überprüfe benötigte Dienste damit SR richtig funktioniert...", "skinsrestorer.admincommand_status_uuid_api": "<gray>Arbeitende UUID-APIs: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>Arbeitende Profil APIs: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>Das Plugin befindet sich derzeit in einem Arbeitszustand.", "skinsrestorer.admincommand_status_degraded": "<green>Das Plugin befindet sich in einem schlechten Zustand, einige Funktionen funktionieren möglicherweise nicht.", "skinsrestorer.admincommand_status_broken": "<red>Das Plugin kann derzeit keine neuen Skins abrufen.<newline>Die Verbindung ist wahrscheinlich wegen einer Firewall gesperrt.<newline>Weitere Informationen finden Sie unter https://skinsrestorer.net/firewall", "skinsrestorer.admincommand_status_firewall": "<red>Verbindungen werden wahrscheinlich wegen einer Firewall blockiert.<newline> Bitte lesen Sie https://skinsrestorer.net/firewall für weitere Informationen.", "skinsrestorer.admincommand_status_summary_server": "<gray>Server: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Proxy Modus: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Commit: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON><PERSON> <yellow><player></yellow> nicht gefunden.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> nicht gefunden.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Wir konnten Mojang nicht kontaktieren, um die UUID des Spielers zu erhalten", "skinsrestorer.admincommand_info_checking": "<gray><PERSON><PERSON><PERSON><PERSON> ange<PERSON>e Daten...", "skinsrestorer.admincommand_info_player": "<gray> Spieler UUID: <gold><uuid><newline><gray> Skin Identifizierung: <gold><identifier><newline><gray> Skin Variante:<gold><variant><newline><gray> Skin Art: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>Du musst eine UUID eines Spielers angeben.", "skinsrestorer.admincommand_info_no_set_skin": "<red>Spieler hat keinen explizit gesetzten Skin.", "skinsrestorer.admincommand_info_url_skin": "<gray>Skin-URL: <gold><click:open_url:'<url>'><url></click><newline><gray>MineSkin-ID: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Hardcoded Skin: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Benutzerdefinierter Skin: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>Spieler Skin:<gold><skin><newline><gray>Zeitstempel: <gold><timestamp><newline><gray>L<PERSON>uft ab:<gold><expires>", "skinsrestorer.admincommand_info_generic": "<gray>Textur-URL: <gold><click:open_url:'<url>'><url></click><newline><gray>Variante: <gold><variant><newline><gray>Profil-UUID: <gold><uuid><newline><gray>Profilname: <gold><name><newline><gray>Anfragezeit: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>Alte Skins erfolgreich bereinigt!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red><PERSON><PERSON>inigen der alten Skins ist ein Fehler aufgetreten!", "skinsrestorer.admincommand_dump_uploading": "<green>Daten werden auf bytebin.lucko.me hochgeladen...", "skinsrestorer.admincommand_dump_success": "Hochladen Erfolgreich!", "skinsrestorer.admincommand_dump_error": "<red><PERSON><PERSON> beim Ho<PERSON>laden der Daten auf bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red>Be<PERSON>hle wurden für den Server <server> deaktiviert.", "skinsrestorer.command_unknown_player": "Unbekann<PERSON> Spieler: <name>", "skinsrestorer.command_no_targets_supplied": "<PERSON><PERSON> Z<PERSON>spieler bereitgestellt.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON><PERSON><dark_gray>: <red>Sie haben nicht die Berechtigung, diesen Skin zu setzen.", "skinsrestorer.player_has_no_permission_url": "<dark_red><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> haben keine Berechtigung, Skins via URL zu setzen.", "skinsrestorer.not_premium": "<dark_red><PERSON><PERSON><dark_gray>: <red>Premium Spieler mit diesem Namen existiert nicht.", "skinsrestorer.only_allowed_on_console": "<dark_red><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> darf diesen Befehl ausführen!", "skinsrestorer.only_allowed_on_player": "<dark_red><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> Spieler dürfen diesen Befehl ausführen!", "skinsrestorer.invalid_player": "<dark_red><PERSON><PERSON><dark_gray>: <red><input> ist kein gültiger Benutzername oder keine URL.", "skinsrestorer.skin_cooldown": "<dark_red><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> kö<PERSON><PERSON> in: <yellow><time></yellow> erne<PERSON> ändern", "skinsrestorer.ms_uploading_skin": "<dark_green>Skin wird hoch<PERSON>aden, bitte warten... (Dies kann einige Zeit in Anspruch nehmen)", "skinsrestorer.wait_a_minute": "<dark_red><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> warten <PERSON> e<PERSON>, be<PERSON> er<PERSON>ut an<PERSON> (Limitiert)", "skinsrestorer.skinsmenu_open": "<dark_green><PERSON><PERSON><PERSON> das Skinsmenü...", "skinsrestorer.skinsmenu_title_select": "<blue><PERSON><PERSON><PERSON><PERSON><PERSON>", "skinsrestorer.skinsmenu_title_main": "<blue>Skinmenü - Seite <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue>Verlaufsmenü - Seite <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue>Favoritenmenü - Seite <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Nächste Seite</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Vorherige Seite</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Skin entfernen</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray><PERSON><PERSON><PERSON>, um diesen Skin auszuwählen", "skinsrestorer.skinsmenu_history_lore": "<blue><PERSON> von <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray><PERSON><PERSON> + <PERSON><PERSON><PERSON>, um als Favorit zu setzen", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray><PERSON><PERSON> + <PERSON><PERSON><PERSON>, um von den Favoriten zu entfernen", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>Favorit seit <time>", "skinsrestorer.skinsmenu_no_permission": "<red><PERSON>hler: <PERSON><PERSON> haben nicht die Berechtigung, diesen <PERSON> zu benutzen.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>Skins Menü</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>Verlaufsmenü</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>Favoritenmenü</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray><PERSON><PERSON> w<PERSON>hl<PERSON></gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green>Du kannst hier Skins finden, die zu <green><search></green> passen: <newline><green><hover:show_text:'<dark_green>Klick zum <PERSON>'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>Falls keine Skins gefunden werden, findest du immer welche unter https://namemc.com/minecraft-skins/tag <newline>Du kannst den Skin mit diesem Skin-Link setzen:<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "Du kannst deinen Skin über diesen Link bearbeiten:\nUm zu erfahren, wie du den bearbeiteten Skin anwendest, besuche:\nhttps://skinsrestorer.net/skinedit", "skinsrestorer.no_skin_data": "<dark_red><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> Skin Daten gefunden!", "skinsrestorer.outdated": "Du verwendest eine veraltete Version von SkinsRestorer auf deiner Plattform!\nBitte aktualisiere auf die neueste Version bei Modrinth:\nhttps://modrinth.com/plugin/skinsrestorer", "skinsrestorer.unsupported_java": "<dark_red>Die Java-Version (<version>) Deiner<platform> wird von SkinsRestorer nicht unterstützt!<newline><red>Bitte aktualisiere auf Java 17 oder höher, um SkinsRestorer ohne Probleme zu verwenden. Neuere Java-Versionen können auch ältere Server ausführen, so dass ein Minecraft 1.8 Server auf Java 17 laufen kann. Lesen Sie die Konsoleninfo für weitere Details.", "skinsrestorer.permission_player_wildcard": "Platzhalter Berechtigung für Spieler", "skinsrestorer.permission_command": "<PERSON><PERSON><PERSON><PERSON> den Zugriff auf die Haupt-\"/skin\"-<PERSON><PERSON><PERSON><PERSON>.", "skinsrestorer.permission_command_set": "Ermöglicht den Zugriff auf das Ändern des Skins.", "skinsrestorer.permission_command_set_url": "Ermöglicht den Zugriff auf das Ändern des Skins via URL.", "skinsrestorer.permission_command_clear": "Ermöglicht den Zugriff auf das Löschen deines Skins.", "skinsrestorer.permission_command_random": "Erlaubt den Zugriff einen zufälligen Skin zu setzen.", "skinsrestorer.permission_command_update": "Ermöglicht den Zugriff auf das Ändern des Skins.", "skinsrestorer.permission_command_undo": "Ermöglicht den Zugriff auf das Zurücksetzen des Skins auf den vorherigen Skin.", "skinsrestorer.permission_command_favourite": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, Skins als Favoriten zu markieren.", "skinsrestorer.permission_command_search": "Ermöglicht Zugriff auf die Suche nach Ihrem Skin.", "skinsrestorer.permission_command_edit": "Ermöglicht die Bearbeitung deines Skins.", "skinsrestorer.permission_command_gui": "<PERSON>rl<PERSON><PERSON> den Zugriff, das Skins-Menü zu öffnen.", "skinsrestorer.permission_admin_wildcard": "Platzhalter-Berechtigung für Admins", "skinsrestorer.permission_admincommand": "<PERSON>rlau<PERSON> den Zugriff auf die Haupt-\"/sr\"-<PERSON><PERSON><PERSON><PERSON>.", "skinsrestorer.permission_command_set_other": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, <PERSON><PERSON> von anderen Spielern zu ändern.", "skinsrestorer.permission_command_clear_other": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, <PERSON><PERSON> von anderen Spielern zurückzusetzen.", "skinsrestorer.permission_command_random_other": "<PERSON>rl<PERSON><PERSON> den Zugriff, auf zufällige Skins von anderen S<PERSON>n zu setzen.", "skinsrestorer.permission_command_update_other": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, <PERSON><PERSON> von anderen Spielern zu aktualisieren.", "skinsrestorer.permission_command_favourite_other": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, Skins als Favoriten von anderen Spielern zu setzen.", "skinsrestorer.permission_command_undo_other": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, den <PERSON> von e<PERSON> anderen Spieler auf den vorherigen zurückzusetzen.", "skinsrestorer.permission_admincommand_skull": "Ermöglicht Zugriff auf die Hauptbefehle '/skull'.", "skinsrestorer.permission_admincommand_skull_get": "Ermöglicht das Erhalten eines Schädels.", "skinsrestorer.permission_admincommand_skull_get_url": "Ermöglicht das Abrufen eines Kopfes über eine URL.", "skinsrestorer.permission_admincommand_skull_random": "Ermöglicht das Erhalten eines zufälligen Schädels.", "skinsrestorer.permission_admincommand_skull_get_other": "Ermöglicht es, einem anderen Spieler einen Kopf zu geben.", "skinsrestorer.permission_admincommand_skull_random_other": "Ermöglicht es, einem anderen Spieler einen zufälligen Kopf zu geben.", "skinsrestorer.permission_admincommand_reload": "Erlaubt den Zugriff auf \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Erlaubt den Zugriff auf \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Ermöglicht den Zugriff eine .SKIN Datei zu entfernen.", "skinsrestorer.permission_admincommand_info": "Erlaubt den Zugriff auf die Informationen von anderen Spielern oder Skins.", "skinsrestorer.permission_admincommand_applyskin": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, <PERSON><PERSON> von anderen Spielern erneut zu festzusetzen.", "skinsrestorer.permission_admincommand_createcustom": "<PERSON>rl<PERSON><PERSON> den Zugriff, benutzerdefinierte Skins mithilfe von URLs zu erstellen.", "skinsrestorer.permission_admincommand_purgeolddata": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, alse Skin-Daten zu löschen.", "skinsrestorer.permission_admincommand_dump": "<PERSON><PERSON><PERSON><PERSON> den Zugriff, Server-Information<PERSON> mithilfe von \"/sr dump\" hochzuladen.", "skinsrestorer.permission_bypasscooldown": "Umgeht alle Befehlcooldowns aus der Konfiguration.", "skinsrestorer.permission_bypassdisabled": "Umgeht alle deaktivierten Skins aus der Konfiguration.", "skinsrestorer.permission_ownskin": "Ermöglicht den Zugriff den eigenen Skin zu setzen.", "skinsrestorer.duration_day": "Tag", "skinsrestorer.duration_days": " Tage", "skinsrestorer.duration_hour": " Stunde", "skinsrestorer.duration_hours": " Stunden", "skinsrestorer.duration_minute": " Minute ", "skinsrestorer.duration_minutes": " Minuten", "skinsrestorer.duration_second": " Sekunde", "skinsrestorer.duration_seconds": " Sekunden"}