{"skinsrestorer.help_skin": "Pakeičia jūsų išvaizdą.", "skinsrestorer.help_skins": "Atidaro išvaizdų GUI.", "skinsrestorer.help_sr": "SkinsRestorer administratorių komandos.", "skinsrestorer.help_skin_set": "Pakeičia jūsų išvaizdą.", "skinsrestorer.help_skin_set_other": "Pakeičia nurodyto žaidėjo išvaizdą.", "skinsrestorer.help_skin_set_url": "Pakeičia jūsų išvaizdą pagal nuorodą.", "skinsrestorer.help_skin_clear": "Atkuria jūsų išvaizdą.", "skinsrestorer.help_skin_clear_other": "<PERSON><PERSON><PERSON><PERSON> nurodyto žaidėjo išvaizdą.", "skinsrestorer.help_skin_search": "Suraskite norimą išvaizdą.", "skinsrestorer.help_skin_update": "Atnaujina jūsų išvaizdą.", "skinsrestorer.help_skin_update_other": "Atnaujina nurodyto žaidėjo išvaizdą.", "skinsrestorer.help_sr_reload": "<PERSON><PERSON> įkelia konfigūracijos failą.", "skinsrestorer.help_sr_status": "<PERSON><PERSON><PERSON> įskiepio API procesus.", "skinsrestorer.help_sr_drop": "<PERSON><PERSON><PERSON><PERSON> arba išvaizdos duomenis iš duomenų bazėss.", "skinsrestorer.help_sr_apply_skin": "<PERSON>š naujo pritaikykite nurodyto žaidėjo išvaizdą.", "skinsrestorer.help_sr_create_custom": "Sukurkite išvaizdą, kurią galėtų pasiekti visi.", "skinsrestorer.help_sr_purge_old_data": "<PERSON><PERSON><PERSON><PERSON><PERSON> senus, daugiau nei x dienų senumo odos duomenis.", "skinsrestorer.help_sr_dump": "Įkelkite duomenis į bytebin.lucko.me.", "skinsrestorer.not_connected_to_server": "<red><PERSON><PERSON><PERSON> ne<PERSON> p<PERSON>ę prie jokio serverio.", "skinsrestorer.admincommand_status_checking": "<gray>Tikrina<PERSON>, kad SR veiktų tinkamai...", "skinsrestorer.admincommand_status_working": "<green>Įskiepis šiuo metu veikia.", "skinsrestorer.admincommand_status_broken": "<red>Įskiepis šiuo metu negali gauti naujų išvaizdų.<newline><PERSON><PERSON><PERSON><PERSON>, kad ryš<PERSON> užblo<PERSON>ota<PERSON> dėl sistemos ugniasienės.<newline>Praš<PERSON> per<PERSON> https://skinsrestorer.net/firewall dėl daugiau informacijos", "skinsrestorer.admincommand_status_summary_server": "<gray>Serveris: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Tarpinio serverio režimas: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray><PERSON><PERSON><PERSON> k<PERSON>: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON><PERSON><PERSON> <yellow><player></yellow> ne<PERSON><PERSON>.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Išvaiz<PERSON> <yellow><skin></yellow> nerasta.", "skinsrestorer.admincommand_drop_uuid_error": "<red><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> su <PERSON>, kad gaut<PERSON> UUID", "skinsrestorer.admincommand_purgeolddata_success": "<green>Sėkmingai išvalytos senos išvaizdos!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red><PERSON><PERSON> senas i<PERSON> įvyko klaida!", "skinsrestorer.admincommand_dump_uploading": "<green><PERSON><PERSON><PERSON> įkeliami į bytebin.lucko.me...", "skinsrestorer.admincommand_dump_error": "<red>Įvyko klaida bandant įkelti duomenis į bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red>Serverio <serveris> komandos buvo išjungtos.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON><PERSON> leid<PERSON> na<PERSON>.", "skinsrestorer.player_has_no_permission_url": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Neturite leidimo nustatyti išvaizdas pagal URL.", "skinsrestorer.not_premium": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> tokiu pavadin<PERSON>u ne<PERSON>.", "skinsrestorer.only_allowed_on_console": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Tik konsolė gali vykdyti šią komandą!", "skinsrestorer.only_allowed_on_player": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Tik žaidėjai gali vykdyti šią komandą!", "skinsrestorer.invalid_player": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><input> nėra galio<PERSON> vartotojo vardas arba URL.", "skinsrestorer.wait_a_minute": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Palaukite minutę prieš vėl prašydami tos išvaizdos. (Pasiektas veiksmų limitas)", "skinsrestorer.skinsmenu_open": "<dark_green>Atidaromas išvaizdų meniu...", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray><PERSON><PERSON> pu<PERSON></gray> <bold>»</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Pa<PERSON>linti iš<PERSON>izdą</gray> <bold>]</bold>", "skinsrestorer.no_skin_data": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Negauta jokių išvaizdos duomenų! Ar šis žaidėjas turi išvaizdą?", "skinsrestorer.permission_player_wildcard": "Pakaitos simbolių leidimas žaidėjams", "skinsrestorer.permission_command": "<PERSON><PERSON><PERSON><PERSON> naudoti pagrindines \"/skin\" komandas.", "skinsrestorer.permission_command_set": "Leidžia pakeisti savo išvaizdą.", "skinsrestorer.permission_command_set_url": "Suteikia leidimą pakeisti savo išvaizdą pagal nuorodą.", "skinsrestorer.permission_command_clear": "Leidžia iš naujo nustatyti savo išvaizdą.", "skinsrestorer.permission_command_update": "Leidžia atnaujinti savo išvaizdą.", "skinsrestorer.permission_command_search": "Leidžia ieškoti išvaizdos.", "skinsrestorer.permission_command_gui": "Leidžia atidaryti išvaizdų GUI.", "skinsrestorer.permission_admin_wildcard": "Pakaitos simbolių leidimas administratoriams", "skinsrestorer.permission_admincommand": "Leidžia naudoti pagrindines \"/sr\" koman<PERSON>.", "skinsrestorer.permission_command_set_other": "Leidžia pakeisti kito žaidėjo išvaizdą.", "skinsrestorer.permission_command_clear_other": "Leidžia pakeisti kito žaidėjo išvaizdą.", "skinsrestorer.permission_command_update_other": "Leidžia atnaujinti kito žaidėjo išvaizdą.", "skinsrestorer.permission_admincommand_reload": "Leidžia naudoti \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Leidžia naudoti \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Leidžia pašalinti .SKIN failą.", "skinsrestorer.permission_admincommand_applyskin": "Leidžia iš naujo pritaikyti kito žaidėjo išvaizdą.", "skinsrestorer.permission_admincommand_createcustom": "Leidžia susikurti savo savadarbę išvaizdą pagal nuorodą.", "skinsrestorer.permission_admincommand_purgeolddata": "Suteikia prieigą prie senų išvaizdų duomenų išvalymo.", "skinsrestorer.permission_admincommand_dump": "Leidžia įkelti serverio informaciją naudojant \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Apeina bet kokį tarp komandos laukimo laikotarpį nustatytą konfigūracijoje.", "skinsrestorer.permission_bypassdisabled": "Apeina draudi<PERSON>ą naudoti išjungtas išvaizdas nustaytas konfigūracijoje.", "skinsrestorer.permission_ownskin": "Leidžia nustatyti savo išvaizdą."}