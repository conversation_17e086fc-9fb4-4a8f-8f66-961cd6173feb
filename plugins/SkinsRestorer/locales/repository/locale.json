{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Changes your own skin.", "skinsrestorer.help_skins": "Opens the skins GUI.", "skinsrestorer.help_sr": "Admin commands for SkinsRestorer.", "skinsrestorer.help_skin_help": "Shows this help command.", "skinsrestorer.help_skin_set": "Changes your skin.", "skinsrestorer.help_skin_set_other": "Sets the skin for a target player.", "skinsrestorer.help_skin_set_url": "Changes your skin from a URL.", "skinsrestorer.help_skin_clear": "Clears your skin.", "skinsrestorer.help_skin_clear_other": "Clears the skin of a target player.", "skinsrestorer.help_skin_random": "Gives a random skin.", "skinsrestorer.help_skin_random_other": "Sets a random skin for a target player.", "skinsrestorer.help_skin_search": "Search up a skin that you want.", "skinsrestorer.help_skin_edit": "Edit your current skin online.", "skinsrestorer.help_skin_update": "Updates your skin.", "skinsrestorer.help_skin_update_other": "Updates the skin of a target player.", "skinsrestorer.help_skin_undo": "Reverts your skin back to the previous skin.", "skinsrestorer.help_skin_undo_other": "Reverts the skin of a target player back to the previous skin.", "skinsrestorer.help_skin_favourite": "Saves your skin as a favourite.", "skinsrestorer.help_skin_favourite_other": "Saves the skin of a target player as a favourite.", "skinsrestorer.help_skull": "Gives you a skull.", "skinsrestorer.help_skull_help": "Skull commands for SkinsRestorer.", "skinsrestorer.help_skull_get": "Gives you a skull.", "skinsrestorer.help_skull_get_other": "Give a skull to another player.", "skinsrestorer.help_skull_get_url": "Gives the skull based on a skin URL.", "skinsrestorer.help_skull_random": "Gives you a random skull.", "skinsrestorer.help_skull_random_other": "Gives a random skull to another player.", "skinsrestorer.help_sr_reload": "Reloads the configuration file.", "skinsrestorer.help_sr_status": "Checks required plugin API services.", "skinsrestorer.help_sr_drop": "Removes player or skin data from the database.", "skinsrestorer.help_sr_info": "Displays info about a player or skin.", "skinsrestorer.help_sr_apply_skin": "Re-apply the skin for target player.", "skinsrestorer.help_sr_create_custom": "Create a custom server wide skin.", "skinsrestorer.help_sr_purge_old_data": "Purge old skin data from over x days ago.", "skinsrestorer.help_sr_dump": "Upload support data to bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URLs must be quoted. Example: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (You can press tab to autocomplete the quotes)", "skinsrestorer.success_skin_change": "Your skin has been changed.", "skinsrestorer.success_skin_change_other": "You changed the skin of <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "Your skin <yellow><skin></yellow> has been reverted back to a skin from <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "The skin <yellow><skin></yellow> of <yellow><name></yellow> has been reverted back to a skin from <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "Your skin <yellow><skin></yellow> has been set as a favourite.", "skinsrestorer.success_skin_favourite_other": "The skin <yellow><skin></yellow> of <yellow><name></yellow> has been set as a favourite.", "skinsrestorer.success_skin_unfavourite": "Your favourite skin <yellow><skin></yellow> from <yellow><timestamp></yellow> has been unfavourited.", "skinsrestorer.success_skin_unfavourite_other": "The favourite skin <yellow><skin></yellow> of <yellow><name></yellow> from <yellow><timestamp></yellow> has been unfavourited.", "skinsrestorer.success_skin_clear": "Your skin has been cleared.", "skinsrestorer.success_skin_clear_other": "Skin cleared for player <yellow><name></yellow>.", "skinsrestorer.success_updating_skin": "Your skin has been updated.", "skinsrestorer.success_updating_skin_other": "Skin updated for player <yellow><name></yellow>.", "skinsrestorer.success_skull_get": "You have received a skull.", "skinsrestorer.success_skull_get_other": "You have given <yellow><name></yellow> a skull.", "skinsrestorer.success_admin_applyskin": "Player skin has been refreshed!", "skinsrestorer.success_admin_createcustom": "Skin <yellow><skin></yellow> has been created!", "skinsrestorer.success_admin_setcustomname": "The name of skin <yellow><skin></yellow> has been set to <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "<type> data dropped for <target>.", "skinsrestorer.success_admin_reload": "Config and Locale has been reloaded!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green>Click to use <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> from <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green>Click to use <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> from <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Error<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "An error occurred while requesting skin data, please try again later!", "skinsrestorer.error_no_undo": "You have no skin to revert back to!", "skinsrestorer.error_no_skin_to_favourite": "You have no skin to set as a favourite!", "skinsrestorer.error_skin_disabled": "This skin is disabled by an administrator.", "skinsrestorer.error_skinurl_disallowed": "This domain has not been allowed by the administrator.", "skinsrestorer.error_updating_skin": "An error occurred while updating your skin. Please try again later!", "skinsrestorer.error_updating_url": "You cannot update custom URL skins! <newline><red>Request again using /skin url", "skinsrestorer.error_updating_customskin": "Skin cannot be updated because its custom.", "skinsrestorer.error_invalid_urlskin": "Invalid skin URL or format, <newline><red>Try uploading your skin to imgur and right click 'copy image address' <newline><red>For guide see: <red><underlined><hover:show_text:'<dark_green>Click to open'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>.", "skinsrestorer.error_admin_applyskin": "Player's skin could NOT be refreshed!", "skinsrestorer.error_ms_full": "MineSkin API timed out while uploading your skin. Please try again later.", "skinsrestorer.error_ms_api_failed": "MineSkin API is overloaded, please try again later!", "skinsrestorer.error_ms_api_key_invalid": "Invalid MineSkin API key!, contact the server owner about this!", "skinsrestorer.error_ms_unknown": "Unknown MineSkin Error!", "skinsrestorer.error_no_history": "You have no skin history!", "skinsrestorer.error_no_favourites": "You have no favourite skins!", "skinsrestorer.error_player_refresh_no_mapping": "Could not refresh your skin because this Minecraft version is not supported by SkinsRestorer. Please tell the server admin to update the plugin SkinsRestorer.", "skinsrestorer.not_connected_to_server": "<red>You are not connected to any server.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Running service checks...", "skinsrestorer.admincommand_status_uuid_api": "<gray>Working UUID APIs: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>Working Profile APIs: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>The plugin is currently in a working state.", "skinsrestorer.admincommand_status_degraded": "<green>The plugin is in a degraded state, some features may not work fully.", "skinsrestorer.admincommand_status_broken": "<red>The plugin is currently in a broken state, no new skins can be requested.", "skinsrestorer.admincommand_status_firewall": "<red>Connections are likely blocked because of a firewall.<newline>Please read https://skinsrestorer.net/firewall for more info.", "skinsrestorer.admincommand_status_summary_version": "<gray>SkinsRestorer <gold>v<version>", "skinsrestorer.admincommand_status_summary_server": "<gray>Server: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Proxy Mode: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Commit: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>Player <yellow><player></yellow> not found.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> not found.", "skinsrestorer.admincommand_drop_uuid_error": "<red>We were not able to contact Mojang to get the player's UUID", "skinsrestorer.admincommand_info_checking": "<gray>Collecting requested data...", "skinsrestorer.admincommand_info_player": "<gray>Player UUID: <gold><uuid><newline><gray>Skin Identifier: <gold><identifier><newline><gray>Skin Variant: <gold><variant><newline><gray>Skin Type: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>You must specify a UUID of a player.", "skinsrestorer.admincommand_info_no_set_skin": "<red>Player has no explicitly set skin.", "skinsrestorer.admincommand_info_url_skin": "<gray>URL Skin: <gold><click:open_url:'<url>'><url></click><newline><gray>MineSkin ID: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Hardcoded Skin: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Custom Skin: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>Player Skin: <gold><skin><newline><gray>Timestamp: <gold><timestamp><newline><gray>Expires: <gold><expires>", "skinsrestorer.admincommand_info_generic": "<gray>Texture URL: <gold><click:open_url:'<url>'><url></click><newline><gray>Variant: <gold><variant><newline><gray>Profile UUID: <gold><uuid><newline><gray>Profile Name: <gold><name><newline><gray>Request time: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>Successfully purged old skins!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>A error occurred while purging old skins!", "skinsrestorer.admincommand_dump_uploading": "<green>Uploading data to bytebin.lucko.me...", "skinsrestorer.admincommand_dump_success": "<green>Upload successful! <yellow><click:open_url:'<url>'><url></click>", "skinsrestorer.admincommand_dump_error": "<red>Error while uploading data to bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red>Commands have been disabled for the server <server>.", "skinsrestorer.command_unknown_player": "Unknown player: <name>", "skinsrestorer.command_no_targets_supplied": "No target players supplied.", "skinsrestorer.player_has_no_permission_skin": "<dark_red>Error<dark_gray>: <red>You don't have permission to set this skin.", "skinsrestorer.player_has_no_permission_url": "<dark_red>Error<dark_gray>: <red>You don't have permission to set skins by URL.", "skinsrestorer.not_premium": "<dark_red>Error<dark_gray>: <red>Premium player with that name does not exist.", "skinsrestorer.only_allowed_on_console": "<dark_red>Error<dark_gray>: <red>Only console may execute this command!", "skinsrestorer.only_allowed_on_player": "<dark_red>Error<dark_gray>: <red>Only players may execute this command!", "skinsrestorer.invalid_player": "<dark_red>Error<dark_gray>: <red><input> is not a valid username or URL.", "skinsrestorer.skin_cooldown": "<dark_red>Error<dark_gray>: <red>You can change your skin again in: <yellow><time></yellow>", "skinsrestorer.ms_uploading_skin": "<dark_green>Uploading skin, please wait... (This may take up some time)", "skinsrestorer.wait_a_minute": "<dark_red>Error<dark_gray>: <red>Please wait a minute before requesting that skin again. (Rate Limited)", "skinsrestorer.skinsmenu_open": "<dark_green>Opening the skins menu...", "skinsrestorer.skinsmenu_title_select": "<blue>Menu Select", "skinsrestorer.skinsmenu_title_main": "<blue>Skins Menu - Page <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue>History Menu - Page <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue>Favourites Menu - Page <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Next Page</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Previous Page</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Remove Skin</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>Click to select this skin", "skinsrestorer.skinsmenu_history_lore": "<blue>Skin from <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + click to set as favourite", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + click to remove from favourites", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>Favourite since <time>", "skinsrestorer.skinsmenu_no_permission": "<red>You don't have permission to use this skin.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>Skins Menu</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>History Menu</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>Favourites Menu</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>Menu select</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green>You can find skins matching <green><search></green> here: <newline><green><hover:show_text:'<dark_green>Click to open'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>If no skins are found, you can always find some skins at https://namemc.com/minecraft-skins/tag <newline>You can set the skin using the skin link:<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green>You can edit your skin at <u><aqua><hover:show_text:'<dark_green>Click to open'><click:open_url:'<url>'>this link</click></hover></aqua></u> <newline><dark_green>To learn how to apply the edited skin, visit: <newline><green><hover:show_text:'<dark_green>Click to open'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover>", "skinsrestorer.no_skin_data": "<dark_red>Error<dark_gray>: <red>No skin data found!", "skinsrestorer.outdated": "<dark_red>You are running an outdated version of SkinsRestorer on your <platform>!<newline><red>Please update to the latest version on Modrinth: <newline><yellow><hover:show_text:'<dark_green>Click to open'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red>The Java version (<version>) of your <platform> is not supported by SkinsRestorer!<newline><red>Please update to Java 17 or higher to use SkinsRestorer without issues. Newer Java versions can also run older servers, so a Minecraft 1.8 server can run on Java 17. Read console info for more details.", "skinsrestorer.permission_player_wildcard": "Wildcard permission for players", "skinsrestorer.permission_command": "Allows access to the main \"/skin\" commands.", "skinsrestorer.permission_command_set": "Allows access to change your skin.", "skinsrestorer.permission_command_set_url": "Allows access to change your skin by URL.", "skinsrestorer.permission_command_clear": "Allows access to clear your skin.", "skinsrestorer.permission_command_random": "Allows access to set a random skin.", "skinsrestorer.permission_command_update": "Allows access to update your skin.", "skinsrestorer.permission_command_undo": "Allows access to revert your skin back to your previous skin.", "skinsrestorer.permission_command_favourite": "Allows access to set a skin as a favourite.", "skinsrestorer.permission_command_search": "Allows access to search your skin.", "skinsrestorer.permission_command_edit": "Allows access to edit your skin.", "skinsrestorer.permission_command_gui": "Allows access to open skins GUI.", "skinsrestorer.permission_admin_wildcard": "Wildcard permission for admins", "skinsrestorer.permission_admincommand": "Allows access to the main \"/sr\" commands.", "skinsrestorer.permission_command_set_other": "Allows access to set another player's skin.", "skinsrestorer.permission_command_clear_other": "Allows access to clear another player's skin.", "skinsrestorer.permission_command_random_other": "Allows access to set a random skin of another player.", "skinsrestorer.permission_command_update_other": "Allows access to update another player's skin.", "skinsrestorer.permission_command_favourite_other": "Allows access to set a skin as a favourite for another player.", "skinsrestorer.permission_command_undo_other": "Allow access to revert a player's skin back to their previous skin.", "skinsrestorer.permission_admincommand_skull": "Allows access to the main \"/skull\" commands.", "skinsrestorer.permission_admincommand_skull_get": "Allows access to get a skull.", "skinsrestorer.permission_admincommand_skull_get_url": "Allows access to get a skull by URL.", "skinsrestorer.permission_admincommand_skull_random": "Allows access to get random a skull.", "skinsrestorer.permission_admincommand_skull_get_other": "Allows access to give another player a skull", "skinsrestorer.permission_admincommand_skull_random_other": "Allows access to give another player a random skull.", "skinsrestorer.permission_admincommand_reload": "Allows access to \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Allows access to \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Allows access to removes a .SKIN file.", "skinsrestorer.permission_admincommand_info": "Allows access to get the skin info of a player or a skin.", "skinsrestorer.permission_admincommand_applyskin": "Allows access to re-apply the skin of another player.", "skinsrestorer.permission_admincommand_createcustom": "Allows access to create a custom global skin by URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Allows access to purge old skin data.", "skinsrestorer.permission_admincommand_dump": "Allows access to upload server information via \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Bypasses any command cooldown set in the config.", "skinsrestorer.permission_bypassdisabled": "Bypasses any disabled skins set in the config.", "skinsrestorer.permission_ownskin": "Allows access to set your own skin.", "skinsrestorer.duration_day": " day", "skinsrestorer.duration_days": " days", "skinsrestorer.duration_hour": " hour", "skinsrestorer.duration_hours": " hours", "skinsrestorer.duration_minute": " minute", "skinsrestorer.duration_minutes": " minutes", "skinsrestorer.duration_second": " second", "skinsrestorer.duration_seconds": " seconds"}