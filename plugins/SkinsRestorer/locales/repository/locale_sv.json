{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "<PERSON><PERSON><PERSON> ditt eget skin.", "skinsrestorer.help_skins": "Öppnar skins-GUI:n.", "skinsrestorer.help_sr": "Administratörskommandon för SkinsRestorer.", "skinsrestorer.help_skin_help": "<PERSON><PERSON> <PERSON><PERSON>mma<PERSON>.", "skinsrestorer.help_skin_set": "<PERSON><PERSON><PERSON> ditt skin.", "skinsrestorer.help_skin_set_other": "<PERSON><PERSON><PERSON> in skinnet för en målspelare.", "skinsrestorer.help_skin_set_url": "<PERSON><PERSON><PERSON> ditt skin från en URL.", "skinsrestorer.help_skin_clear": "<PERSON><PERSON> ditt skin.", "skinsrestorer.help_skin_clear_other": "<PERSON><PERSON> skinnet för en målspelare.", "skinsrestorer.help_skin_random": "<PERSON><PERSON> ett slumpmässigt skin.", "skinsrestorer.help_skin_random_other": "<PERSON><PERSON><PERSON> in ett slumpmässigt skin för en målspelare.", "skinsrestorer.help_skin_search": "<PERSON><PERSON><PERSON> efter ett skin som du vill ha.", "skinsrestorer.help_skin_edit": "<PERSON><PERSON>a ditt nuvarande skin online.", "skinsrestorer.help_skin_update": "Upp<PERSON><PERSON> ditt skin.", "skinsrestorer.help_skin_update_other": "Uppdaterar skinnet för en målspelare.", "skinsrestorer.help_skin_undo": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt skin till det föregående skinnet.", "skinsrestorer.help_skin_undo_other": "Återställer skinnet för en målspelare till det föregående skinnet.", "skinsrestorer.help_skin_favourite": "<PERSON><PERSON> ditt skin som en favorit.", "skinsrestorer.help_skin_favourite_other": "Sparar skinnet för en målspelare som en favorit.", "skinsrestorer.help_skull": "Ger dig en skalle.", "skinsrestorer.help_skull_help": "Skalle-kommandon för SkinsRestorer.", "skinsrestorer.help_skull_get": "Ger dig en skalle.", "skinsrestorer.help_skull_get_other": "Ge en skalle till en annan spelare.", "skinsrestorer.help_skull_get_url": "Ger skallen baserat på en skin-URL.", "skinsrestorer.help_skull_random": "Ger dig en slumpmässig skalle.", "skinsrestorer.help_skull_random_other": "Ger en slumpmässig skalle till en annan spelare.", "skinsrestorer.help_sr_reload": "Laddar om konfigurationsfilen.", "skinsrestorer.help_sr_status": "Kontrollerar nödvändiga plugin API-tjänster.", "skinsrestorer.help_sr_drop": "Tar bort spelar- eller skin-data från databasen.", "skinsrestorer.help_sr_info": "Visar information om en spelare eller ett skin.", "skinsrestorer.help_sr_apply_skin": "Applicera om skinnet för m<PERSON>.", "skinsrestorer.help_sr_create_custom": "Skapa ett anpassat serveromfattande skin.", "skinsrestorer.help_sr_purge_old_data": "Rensa gammal skin-data från mer än x dagar sedan.", "skinsrestorer.help_sr_dump": "Laddar upp supportdata till bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URL:er måste vara inom citattecken.\nExempel: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (Du kan trycka på tab för att autokomplettera citattecknen)", "skinsrestorer.success_skin_change": "<PERSON><PERSON> skin har ändrats.", "skinsrestorer.success_skin_change_other": "<PERSON> skinnet för <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "Ditt skin <yellow><skin></yellow> har <PERSON><PERSON>ts till ett skin från <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "Skinnet <yellow><skin></yellow> för <yellow><name></yellow> har <PERSON>ts till ett skin från <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "Ditt skin <yellow><skin></yellow> har angetts som en favorit.", "skinsrestorer.success_skin_favourite_other": "Skinnet <yellow><skin></yellow> för <yellow><name></yellow> har angetts som en favorit.", "skinsrestorer.success_skin_unfavourite": "<PERSON><PERSON> <yellow><skin></yellow> fr<PERSON>n <yellow><timestamp></yellow> har tagits bort från favoriter.", "skinsrestorer.success_skin_unfavourite_other": "Favoritskinnet <yellow><skin></yellow> för <yellow><name></yellow> fr<PERSON>n <yellow><timestamp></yellow> har tagits bort från favoriter.", "skinsrestorer.success_skin_clear": "<PERSON>tt skin har rensats.", "skinsrestorer.success_skin_clear_other": "Skin rensat för spelare <yellow><name></yellow>.", "skinsrestorer.success_updating_skin": "<PERSON><PERSON> skin har uppdaterats.", "skinsrestorer.success_updating_skin_other": "Skin uppdaterat för spelare <yellow><name></yellow>.", "skinsrestorer.success_skull_get": "Du har fått en skalle.", "skinsrestorer.success_skull_get_other": "<PERSON> har gett <yellow><name></yellow> en skalle.", "skinsrestorer.success_admin_applyskin": "Spelarens skin har uppdaterats!", "skinsrestorer.success_admin_createcustom": "Skin <yellow><skin></yellow> har skapats!", "skinsrestorer.success_admin_setcustomname": "Namnet på skin <yellow><skin></yellow> har satts till <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "<type>-data borttagen för <target>.", "skinsrestorer.success_admin_reload": "Konfiguration och språk har laddats om!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green><PERSON><PERSON><PERSON> för att använda <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> fr<PERSON>n <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green><PERSON><PERSON><PERSON> för att använda <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> fr<PERSON>n <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Fel<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "Ett fel inträffade vid begäran av skin-data, försök igen senare!", "skinsrestorer.error_no_undo": "Du har inget skin att återgå till!", "skinsrestorer.error_no_skin_to_favourite": "Du har inget skin att ange som favorit!", "skinsrestorer.error_skin_disabled": "<PERSON>ta skin är inaktiverat av en administratör.", "skinsrestorer.error_skinurl_disallowed": "<PERSON>na domän har inte tillåtits av administratören.", "skinsrestorer.error_updating_skin": "Ett fel inträffade vid uppdatering av ditt skin.\nFörsök igen senare!", "skinsrestorer.error_updating_url": "Du kan inte uppdatera anpassade URL-skins!\n<newline><red>Begär igen med /skin url", "skinsrestorer.error_updating_customskin": "Skin kan inte uppdateras eftersom det är anpassat.", "skinsrestorer.error_invalid_urlskin": "Ogiltig skin-URL eller format,<newline><red><PERSON>va att ladda upp ditt skin till imgur och högerklicka 'kopiera bildadress'<newline><red>För guide, se: <red><underlined><hover:show_text:'<dark_green><PERSON><PERSON><PERSON> för att öppna'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>.", "skinsrestorer.error_admin_applyskin": "Spelarens skin kunde INTE uppdateras!", "skinsrestorer.error_ms_full": "MineSkin API timeout vid uppladdning av ditt skin.\nFörsök igen senare.", "skinsrestorer.error_ms_api_failed": "MineSkin API är överbelastad, försök igen senare!", "skinsrestorer.error_ms_api_key_invalid": "Ogiltig MineSkin API-nyckel!, kontakta serverägaren om detta!", "skinsrestorer.error_ms_unknown": "<PERSON><PERSON>nt <PERSON>kin-fel!", "skinsrestorer.error_no_history": "Du har ingen skin-historik!", "skinsrestorer.error_no_favourites": "Du har inga favor<PERSON><PERSON>!", "skinsrestorer.error_player_refresh_no_mapping": "Kunde inte uppdatera ditt skin eftersom denna Minecraft-version inte stöds av SkinsRestorer.\nVänligen be serveradministratören att uppdatera pluginet SkinsRestorer.", "skinsrestorer.not_connected_to_server": "<red>Du är inte ansluten till någon server.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray><PERSON><PERSON><PERSON> t<PERSON>ekontroller...", "skinsrestorer.admincommand_status_uuid_api": "<gray>Fungerande UUID API:er: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>Fungerande Profil API:er: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>Pluginet är för närvarande i fungerande skick.", "skinsrestorer.admincommand_status_degraded": "<green>Pluginet är i ett förs<PERSON><PERSON><PERSON> skick, vissa funktioner kanske inte fungerar fullt ut.", "skinsrestorer.admincommand_status_broken": "<red>Pluginet är för nä<PERSON> i ett trasigt skick, inga nya <PERSON> kan begäras.", "skinsrestorer.admincommand_status_firewall": "<red>Anslutningar blockeras troligen på grund av en brandvägg.<newline><PERSON><PERSON><PERSON> https://skinsrestorer.net/firewall för mer information.", "skinsrestorer.admincommand_status_summary_server": "<gray>Server: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Proxy-läge: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Commit: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>S<PERSON>are <yellow><player></yellow> hittades inte.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> hittades inte.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Vi kunde inte kontakta Mojang för att få spelarens UUID", "skinsrestorer.admincommand_info_checking": "<gray><PERSON><PERSON> in begärd data...", "skinsrestorer.admincommand_info_player": "<gray>Spelar-UUID: <gold><uuid><newline><gray>Skin-identifierare: <gold><identifier><newline><gray>Skin-variant: <gold><variant><newline><gray>Skin-typ: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>Du måste ange en spelares UUID.", "skinsrestorer.admincommand_info_no_set_skin": "<red>S<PERSON><PERSON>n har inget uttryckligen angivet skin.", "skinsrestorer.admincommand_info_url_skin": "<gray>URL-skin: <gold><click:open_url:'<url>'><url></click><newline><gray>MineSkin ID: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Hårdkodat skin: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Anpassat skin: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>Spelar-skin: <gold><skin><newline><gray>Tidsstämpel: <gold><timestamp><newline><gray><PERSON><PERSON><PERSON>r att gälla: <gold><expires>", "skinsrestorer.admincommand_info_generic": "<gray>Textur-URL: <gold><click:open_url:'<url>'><url></click><newline><gray>Variant: <gold><variant><newline><gray>Profil-UUID: <gold><uuid><newline><gray>Profilnamn: <gold><name><newline><gray>Begäranstid: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>Lyckades rensa gamla skins!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>Ett fel inträffade vid rensning av gamla skins!", "skinsrestorer.admincommand_dump_uploading": "<green>Laddar upp data till bytebin.lucko.me...", "skinsrestorer.admincommand_dump_success": "<green>Uppladdning lyckades!\n<yellow><click:open_url:'<url>'><url></click>", "skinsrestorer.admincommand_dump_error": "<red>Fel vid uppladdning av data till bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red><PERSON><PERSON><PERSON> har inaktiverats för servern <server>.", "skinsrestorer.command_unknown_player": "Okänd spelare: <name>", "skinsrestorer.command_no_targets_supplied": "Inga målspelare angivna.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON><PERSON><dark_gray>: <red>Du har inte behörighet att ställa in detta skin.", "skinsrestorer.player_has_no_permission_url": "<dark_red><PERSON><PERSON><dark_gray>: <red>Du har inte behörighet att ställa in skins via URL.", "skinsrestorer.not_premium": "<dark_red>Fel<dark_gray>: <red>Premiumspelare med det namnet finns inte.", "skinsrestorer.only_allowed_on_console": "<dark_red><PERSON><PERSON><dark_gray>: <red>Endast konsolen får utföra detta kommando!", "skinsrestorer.only_allowed_on_player": "<dark_red><PERSON><PERSON><dark_gray>: <red>Endast spelare får utföra detta kommando!", "skinsrestorer.invalid_player": "<dark_red>Fel<dark_gray>: <red><input> är inte ett giltigt användarnamn eller URL.", "skinsrestorer.skin_cooldown": "<dark_red>Fel<dark_gray>: <red>Du kan ändra ditt skin igen om: <yellow><time></yellow>", "skinsrestorer.ms_uploading_skin": "<dark_green><PERSON><PERSON><PERSON> upp skin, vänligen vänta... (<PERSON>ta kan ta lite tid)", "skinsrestorer.wait_a_minute": "<dark_red>Fe<PERSON><dark_gray>: <red>Vänta en minut innan du begär det skinnet igen. (Ratelimiterad)", "skinsrestorer.skinsmenu_open": "<dark_green><PERSON><PERSON><PERSON> skins-menyn...", "skinsrestorer.skinsmenu_title_select": "<blue><PERSON><PERSON><PERSON>", "skinsrestorer.skinsmenu_title_main": "<blue><PERSON><PERSON>-<PERSON><PERSON> - <PERSON><PERSON> <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue><PERSON><PERSON><PERSON>-<PERSON><PERSON> - <PERSON><PERSON> <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue>Fav<PERSON><PERSON>-<PERSON><PERSON> - <PERSON><PERSON> <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>N<PERSON><PERSON> sida</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Föregående sida</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Ta bort skin</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray><PERSON><PERSON><PERSON> för att välja detta skin", "skinsrestorer.skinsmenu_history_lore": "<blue><PERSON> från <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + klicka för att ange som favorit", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + klicka för att ta bort från favoriter", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>Favorit sedan <time>", "skinsrestorer.skinsmenu_no_permission": "<red>Du har inte behörighet att använda detta skin.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>Skins-meny</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>Historik-meny</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>Favorit-meny</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>Menyval</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green>Du kan hitta skins som matchar <green><search></green> här: <newline><green><hover:show_text:'<dark_green><PERSON><PERSON><PERSON> för att öppna'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>Om inga skins hittas kan du alltid hitta några skins på https://namemc.com/minecraft-skins/tag <newline>Du kan ställa in skinnet med skin-länken:<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green>Du kan redigera ditt skin på <u><aqua><hover:show_text:'<dark_green><PERSON><PERSON><PERSON> för att öppna'><click:open_url:'<url>'>denna länk</click></hover></aqua></u> <newline><dark_green>F<PERSON>r att lära dig hur du applicerar det redigerade skinnet, besök: <newline><green><hover:show_text:'<dark_green><PERSON><PERSON>a för att öppna'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover>", "skinsrestorer.no_skin_data": "<dark_red>Fel<dark_gray>: <red>Ingen skin-data hittades!", "skinsrestorer.outdated": "<dark_red>Du kör en föråldrad version av SkinsRestorer på din <platform>!<newline><red>Uppdatera till den senaste versionen på Modrinth: <newline><yellow><hover:show_text:'<dark_green><PERSON>licka för att öppna'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red>Java-versionen (<version>) på din <platform> stöds inte av SkinsRestorer!<newline><red>Uppdatera till Java 17 eller högre för att använda SkinsRestorer utan problem. Nyare Java-versioner kan också köra äldre servrar, så en Minecraft 1.8-server kan köras på Java 17. Läs konsolinformationen för mer information.", "skinsrestorer.permission_player_wildcard": "Wildcard-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> för spelare", "skinsrestorer.permission_command": "<PERSON><PERSON><PERSON> till huvudkommandona \"/skin\".", "skinsrestorer.permission_command_set": "<PERSON><PERSON><PERSON>t att ändra ditt skin.", "skinsrestorer.permission_command_set_url": "<PERSON><PERSON><PERSON>mst att ändra ditt skin via URL.", "skinsrestorer.permission_command_clear": "<PERSON><PERSON><PERSON>t att rensa ditt skin.", "skinsrestorer.permission_command_random": "<PERSON><PERSON><PERSON>t att ställa in ett slumpmässigt skin.", "skinsrestorer.permission_command_update": "<PERSON><PERSON><PERSON>t att uppdatera ditt skin.", "skinsrestorer.permission_command_undo": "<PERSON><PERSON><PERSON>komst att återställa ditt skin till ditt föregående skin.", "skinsrestorer.permission_command_favourite": "<PERSON><PERSON><PERSON>t att ställa in ett skin som favorit.", "skinsrestorer.permission_command_search": "<PERSON><PERSON><PERSON>t att söka efter ditt skin.", "skinsrestorer.permission_command_edit": "<PERSON><PERSON><PERSON>t att redigera ditt skin.", "skinsrestorer.permission_command_gui": "Tillåter åtkomst att öppna skins-GUI.", "skinsrestorer.permission_admin_wildcard": "Wildcard-beh<PERSON>rig<PERSON>t för administratörer", "skinsrestorer.permission_admincommand": "<PERSON><PERSON><PERSON> till huvudkommandona \"/sr\".", "skinsrestorer.permission_command_set_other": "<PERSON><PERSON><PERSON>t att ställa in en annan spelares skin.", "skinsrestorer.permission_command_clear_other": "<PERSON><PERSON><PERSON>t att rensa en annan spelares skin.", "skinsrestorer.permission_command_random_other": "<PERSON><PERSON><PERSON>t att ställa in ett slumpmässigt skin för en annan spelare.", "skinsrestorer.permission_command_update_other": "<PERSON><PERSON><PERSON>t att uppdatera en annan spelares skin.", "skinsrestorer.permission_command_favourite_other": "<PERSON><PERSON><PERSON>t att ställa in ett skin som favorit för en annan spelare.", "skinsrestorer.permission_command_undo_other": "<PERSON><PERSON><PERSON> åtkomst att återställa en spelares skin till deras föregående skin.", "skinsrestorer.permission_admincommand_skull": "<PERSON><PERSON><PERSON> till huvudkommandona \"/skull\".", "skinsrestorer.permission_admincommand_skull_get": "<PERSON><PERSON><PERSON> å<PERSON>komst att få en skalle.", "skinsrestorer.permission_admincommand_skull_get_url": "Till<PERSON>ter åtkomst att få en skalle via URL.", "skinsrestorer.permission_admincommand_skull_random": "<PERSON><PERSON><PERSON>komst att få en slumpmässig skalle.", "skinsrestorer.permission_admincommand_skull_get_other": "<PERSON><PERSON><PERSON>mst att ge en annan spelare en skalle.", "skinsrestorer.permission_admincommand_skull_random_other": "<PERSON><PERSON><PERSON>mst att ge en annan spelare en slumpmässig skalle.", "skinsrestorer.permission_admincommand_reload": "<PERSON><PERSON><PERSON> till \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "<PERSON><PERSON><PERSON> till \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "<PERSON><PERSON><PERSON>t att ta bort en .SKIN-fil.", "skinsrestorer.permission_admincommand_info": "<PERSON><PERSON><PERSON>t att få skin-informationen för en spelare eller ett skin.", "skinsrestorer.permission_admincommand_applyskin": "<PERSON><PERSON><PERSON>t att applicera om skinnet för en annan spelare.", "skinsrestorer.permission_admincommand_createcustom": "Till<PERSON>ter åtkomst att skapa ett anpassat globalt skin via URL.", "skinsrestorer.permission_admincommand_purgeolddata": "<PERSON><PERSON><PERSON>t att rensa gammal skin-data.", "skinsrestorer.permission_admincommand_dump": "<PERSON><PERSON><PERSON>t att ladda upp serverinformation via \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Går förbi eventuell kommandonedkylning angiven i konfigurationen.", "skinsrestorer.permission_bypassdisabled": "Går förbi eventuella inaktiverade skins angivna i konfigurationen.", "skinsrestorer.permission_ownskin": "<PERSON><PERSON><PERSON>t att ställa in ditt eget skin.", "skinsrestorer.duration_day": " dag", "skinsrestorer.duration_days": " dagar", "skinsrestorer.duration_hour": " timme", "skinsrestorer.duration_hours": " timmar", "skinsrestorer.duration_minute": " minut", "skinsrestorer.duration_minutes": " minuter", "skinsrestorer.duration_second": " sekund", "skinsrestorer.duration_seconds": " <PERSON><PERSON>nder"}