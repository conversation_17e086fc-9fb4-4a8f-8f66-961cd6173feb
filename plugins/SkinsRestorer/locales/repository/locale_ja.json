{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "自分のスキンを変更します。", "skinsrestorer.help_skins": "スキンGUIを開きます。", "skinsrestorer.help_sr": "SkinsRestorerの管理者コマンド。", "skinsrestorer.help_skin_help": "このヘルプコマンドを表示します。", "skinsrestorer.help_skin_set": "あなたのスキンを変更します。", "skinsrestorer.help_skin_set_other": "対象プレイヤーのスキンを設定します。", "skinsrestorer.help_skin_set_url": "URLからスキンを変更します。", "skinsrestorer.help_skin_clear": "あなたのスキンをクリアします。", "skinsrestorer.help_skin_clear_other": "対象プレイヤーのスキンをクリアします。", "skinsrestorer.help_skin_random": "ランダムなスキンを適用します。", "skinsrestorer.help_skin_random_other": "対象プレイヤーにランダムなスキンを設定します。", "skinsrestorer.help_skin_search": "希望のスキンを検索します。", "skinsrestorer.help_skin_edit": "現在のスキンをオンラインで編集します。", "skinsrestorer.help_skin_update": "あなたのスキンを更新します。", "skinsrestorer.help_skin_update_other": "対象プレイヤーのスキンを更新します。", "skinsrestorer.help_skin_undo": "スキンを前のスキンに戻します。", "skinsrestorer.help_skin_undo_other": "対象プレイヤーのスキンを前のスキンに戻します。", "skinsrestorer.help_skin_favourite": "スキンをお気に入りに保存します。", "skinsrestorer.help_skin_favourite_other": "対象プレイヤーのスキンをお気に入りに保存します。", "skinsrestorer.help_skull": "スカルをあなたに与えます。", "skinsrestorer.help_skull_help": "SkinsRestorerのスカルコマンド。", "skinsrestorer.help_skull_get": "スカルをあなたに与えます。", "skinsrestorer.help_skull_get_other": "他のプレイヤーにスカルを与えます。", "skinsrestorer.help_skull_get_url": "スキンURLに基づいてスカルを与えます。", "skinsrestorer.help_skull_random": "ランダムなスカルをあなたに与えます。", "skinsrestorer.help_skull_random_other": "他のプレイヤーにランダムなスカルを与えます。", "skinsrestorer.help_sr_reload": "設定ファイルをリロードします。", "skinsrestorer.help_sr_status": "必須プラグインAPIサービスを確認します。", "skinsrestorer.help_sr_drop": "データベースからプレイヤーまたはスキンのデータを削除します。", "skinsrestorer.help_sr_info": "プレイヤーまたはスキンの情報を表示します。", "skinsrestorer.help_sr_apply_skin": "対象プレイヤーのスキンを再適用します。", "skinsrestorer.help_sr_create_custom": "カスタムサーバーワイドスキンを作成します。", "skinsrestorer.help_sr_purge_old_data": "X日以上前の古いスキンデータを削除します。", "skinsrestorer.help_sr_dump": "サポートデータをbytebin.lucko.meにアップロードします。", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URLは引用符で囲む必要があります。\n例： <yellow>/skin set \"https://example.com/skin.png\"</yellow> (Tabキーを押して引用符をオートコンプリートできます)", "skinsrestorer.success_skin_change": "スキンが変更されました。", "skinsrestorer.success_skin_change_other": "<yellow><name></yellow>のスキンを変更しました。", "skinsrestorer.success_skin_undo": "あなたのスキン<yellow><skin></yellow>は<yellow><timestamp></yellow>のスキンに戻されました。", "skinsrestorer.success_skin_undo_other": "<yellow><name></yellow>のスキン<yellow><skin></yellow>は<yellow><timestamp></yellow>のスキンに戻されました。", "skinsrestorer.success_skin_favourite": "あなたのスキン<yellow><skin></yellow>がお気に入りに設定されました。", "skinsrestorer.success_skin_favourite_other": "<yellow><name></yellow>のスキン<yellow><skin></yellow>がお気に入りに設定されました。", "skinsrestorer.success_skin_unfavourite": "あなたのお気に入りスキン<yellow><skin></yellow>（<yellow><timestamp></yellow>）がお気に入りから解除されました。", "skinsrestorer.success_skin_unfavourite_other": "<yellow><name></yellow>のお気に入りスキン<yellow><skin></yellow>（<yellow><timestamp></yellow>）がお気に入りから解除されました。", "skinsrestorer.success_skin_clear": "スキンがクリアされました。", "skinsrestorer.success_skin_clear_other": "プレイヤー<yellow><name></yellow>のスキンがクリアされました。", "skinsrestorer.success_updating_skin": "スキンが更新されました。", "skinsrestorer.success_updating_skin_other": "プレイヤー<yellow><name></yellow>のスキンが更新されました。", "skinsrestorer.success_skull_get": "スカルを受け取りました。", "skinsrestorer.success_skull_get_other": "<yellow><name></yellow>にスカルを与えました。", "skinsrestorer.success_admin_applyskin": "プレイヤーのスキンが更新されました！", "skinsrestorer.success_admin_createcustom": "スキン<yellow><skin></yellow>が作成されました！", "skinsrestorer.success_admin_setcustomname": "スキン<yellow><skin></yellow>の名前が<yellow><display_name></yellow>に設定されました。", "skinsrestorer.success_admin_drop": "<target>の<type>データを削除しました。", "skinsrestorer.success_admin_reload": "設定とロケールがリロードされました！", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green><yellow><skin></yellow>を使用するにはクリック'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> (<yellow><timestamp></yellow>)", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green><yellow><skin></yellow>を使用するにはクリック'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> (<yellow><timestamp></yellow>)", "skinsrestorer.error_generic": "<dark_red>エラー<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "スキンデータの要求中にエラーが発生しました。しばらくしてからもう一度お試しください！", "skinsrestorer.error_no_undo": "元に戻すスキンがありません！", "skinsrestorer.error_no_skin_to_favourite": "お気に入りに設定するスキンがありません！", "skinsrestorer.error_skin_disabled": "このスキンは管理者によって無効にされています。", "skinsrestorer.error_skinurl_disallowed": "このドメインは管理者によって許可されていません。", "skinsrestorer.error_updating_skin": "スキンの更新中にエラーが発生しました。\nしばらくしてからもう一度お試しください！", "skinsrestorer.error_updating_url": "カスタムURLスキンは更新できません！\n<newline><red>/skin urlを使用して再度要求してください", "skinsrestorer.error_updating_customskin": "カスタムスキンのため、スキンは更新できません。", "skinsrestorer.error_invalid_urlskin": "無効なスキンURLまたは形式です。<newline><red>スキンをimgurにアップロードし、右クリックして「画像アドレスをコピー」してみてください<newline><red>ガイドはこちら： <red><underlined><hover:show_text:'<dark_green>クリックして開く'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>。", "skinsrestorer.error_admin_applyskin": "プレイヤーのスキンを更新できませんでした！", "skinsrestorer.error_ms_full": "スキンのアップロード中にMineSkin APIがタイムアウトしました。\nしばらくしてからもう一度お試しください。", "skinsrestorer.error_ms_api_failed": "MineSkin APIが過負荷です。しばらくしてからもう一度お試しください！", "skinsrestorer.error_ms_api_key_invalid": "無効なMineSkin APIキーです！サーバー管理者に連絡してください！", "skinsrestorer.error_ms_unknown": "不明なMineSkinエラー！", "skinsrestorer.error_no_history": "スキン履歴がありません！", "skinsrestorer.error_no_favourites": "お気に入りスキンがありません！", "skinsrestorer.error_player_refresh_no_mapping": "このMinecraftバージョンはSkinsRestorerでサポートされていないため、スキンを更新できませんでした。\nサーバー管理者にSkinsRestorerプラグインの更新を依頼してください。", "skinsrestorer.not_connected_to_server": "<red>どのサーバーにも接続されていません。", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>サービスチェックを実行中...", "skinsrestorer.admincommand_status_uuid_api": "<gray>動作中のUUID API: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>動作中のProfile API: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>プラグインは現在動作中です。", "skinsrestorer.admincommand_status_degraded": "<green>プラグインは機能低下状態です。一部の機能が完全には動作しない可能性があります。", "skinsrestorer.admincommand_status_broken": "<red>プラグインは現在壊れた状態です。新しいスキンは要求できません。", "skinsrestorer.admincommand_status_firewall": "<red>ファイアウォールによって接続がブロックされている可能性があります。<newline>詳細については https://skinsrestorer.net/firewall をお読みください。", "skinsrestorer.admincommand_status_summary_server": "<gray>サーバー: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>プロキシモード：<gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>コミット: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>プレイヤー<yellow><player></yellow>が見つかりません。", "skinsrestorer.admincommand_drop_skin_not_found": "<red>スキン<yellow><skin></yellow>が見つかりません。", "skinsrestorer.admincommand_drop_uuid_error": "<red>プレイヤーのUUIDを取得するためにMojangに接続できませんでした", "skinsrestorer.admincommand_info_checking": "<gray>要求されたデータを収集中...", "skinsrestorer.admincommand_info_player": "<gray>プレイヤーUUID: <gold><uuid><newline><gray>スキン識別子: <gold><identifier><newline><gray>スキンバリアント: <gold><variant><newline><gray>スキンタイプ: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>プレイヤーのUUIDを指定する必要があります。", "skinsrestorer.admincommand_info_no_set_skin": "<red>プレイヤーには明示的に設定されたスキンがありません。", "skinsrestorer.admincommand_info_url_skin": "<gray>URLスキン: <gold><click:open_url:'<url>'><url></click><newline><gray>MineSkin ID: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>ハードコードされたスキン: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>カスタムスキン: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>プレイヤースキン: <gold><skin><newline><gray>タイムスタンプ: <gold><timestamp><newline><gray>有効期限: <gold><expires>", "skinsrestorer.admincommand_info_generic": "<gray>テクスチャURL: <gold><click:open_url:'<url>'><url></click><newline><gray>バリアント: <gold><variant><newline><gray>プロファイルUUID: <gold><uuid><newline><gray>プロファイル名: <gold><name><newline><gray>要求時間: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>古いスキンが正常に削除されました！", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>古いスキンの削除中にエラーが発生しました！", "skinsrestorer.admincommand_dump_uploading": "<green>bytebin.lucko.meにデータをアップロード中...", "skinsrestorer.admincommand_dump_success": "<green>アップロード成功！\n<yellow><click:open_url:'<url>'><url></click>", "skinsrestorer.admincommand_dump_error": "<red>bytebin.lucko.meへのデータアップロード中にエラーが発生しました", "skinsrestorer.command_server_not_allowed_message": "<red>サーバー<server>ではコマンドが無効になっています。", "skinsrestorer.command_unknown_player": "不明なプレイヤー: <name>", "skinsrestorer.command_no_targets_supplied": "対象プレイヤーが指定されていません。", "skinsrestorer.player_has_no_permission_skin": "<dark_red>エラー<dark_gray>: <red>このスキンを設定する権限がありません。", "skinsrestorer.player_has_no_permission_url": "<dark_red>エラー<dark_gray>: <red>URLでスキンを設定する権限がありません。", "skinsrestorer.not_premium": "<dark_red>エラー<dark_gray>: <red>その名前のプレミアムプレイヤーは存在しません。", "skinsrestorer.only_allowed_on_console": "<dark_red>エラー<dark_gray>: <red>コンソールのみがこのコマンドを実行できます！", "skinsrestorer.only_allowed_on_player": "<dark_red>エラー<dark_gray>: <red>プレイヤーのみがこのコマンドを実行できます！", "skinsrestorer.invalid_player": "<dark_red>エラー<dark_gray>: <red><input>は有効なユーザー名またはURLではありません。", "skinsrestorer.skin_cooldown": "<dark_red>エラー<dark_gray>: <red>次にスキンを変更できるのは： <yellow><time></yellow> 後です", "skinsrestorer.ms_uploading_skin": "<dark_green>スキンをアップロード中です、お待ちください... (時間がかかる場合があります)", "skinsrestorer.wait_a_minute": "<dark_red>エラー<dark_gray>: <red>再度そのスキンを要求する前に1分待ってください。(レート制限)", "skinsrestorer.skinsmenu_open": "<dark_green>スキンメニューを開いています...", "skinsrestorer.skinsmenu_title_select": "<blue>メニュー選択", "skinsrestorer.skinsmenu_title_main": "<blue>スキンメニュー - ページ <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue>履歴メニュー - ページ <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue>お気に入りメニュー - ページ <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>次のページ</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>前のページ</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>スキンを削除</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>クリックしてこのスキンを選択", "skinsrestorer.skinsmenu_history_lore": "<blue><time>からのスキン", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + クリックでお気に入りに設定", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + クリックでお気に入りから削除", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue><time>からのお気に入り", "skinsrestorer.skinsmenu_no_permission": "<red>このスキンを使用する権限がありません。", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>スキンメニュー</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>履歴メニュー</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>お気に入りメニュー</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>メニュー選択</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green><green><search></green>に一致するスキンはこちらで見つけられます: <newline><green><hover:show_text:'<dark_green>クリックして開く'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>スキンが見つからない場合は、https://namemc.com/minecraft-skins/tag でいつでも見つけることができます<newline>スキンリンクを使用してスキンを設定できます:<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green>あなたのスキンは<u><aqua><hover:show_text:'<dark_green>クリックして開く'><click:open_url:'<url>'>このリンク</click></hover></aqua></u>で編集できます<newline><dark_green>編集したスキンの適用方法については、以下をご覧ください: <newline><green><hover:show_text:'<dark_green>クリックして開く'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover>", "skinsrestorer.no_skin_data": "<dark_red>エラー<dark_gray>: <red>スキンデータが見つかりません！", "skinsrestorer.outdated": "<dark_red><platform>で実行中のSkinsRestorerは古いバージョンです！<newline><red>Modrinthの最新バージョンに更新してください: <newline><yellow><hover:show_text:'<dark_green>クリックして開く'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red><platform>のJavaバージョン(<version>)はSkinsRestorerでサポートされていません！<newline><red>問題なくSkinsRestorerを使用するには、Java 17以上に更新してください。\n新しいJavaバージョンは古いサーバーも実行できるため、Minecraft 1.8サーバーはJava 17で実行できます。詳細についてはコンソール情報を読んでください。", "skinsrestorer.permission_player_wildcard": "プレイヤーのワイルドカード権限", "skinsrestorer.permission_command": "メインの「/skin」コマンドへのアクセスを許可します。", "skinsrestorer.permission_command_set": "自分のスキンを変更するアクセスを許可します。", "skinsrestorer.permission_command_set_url": "URLでスキンを変更するアクセスを許可します。", "skinsrestorer.permission_command_clear": "自分のスキンをクリアするアクセスを許可します。", "skinsrestorer.permission_command_random": "ランダムなスキンを設定するアクセスを許可します。", "skinsrestorer.permission_command_update": "自分のスキンを更新するアクセスを許可します。", "skinsrestorer.permission_command_undo": "スキンを前のスキンに戻すアクセスを許可します。", "skinsrestorer.permission_command_favourite": "スキンをお気に入りに設定するアクセスを許可します。", "skinsrestorer.permission_command_search": "スキンを検索するアクセスを許可します。", "skinsrestorer.permission_command_edit": "スキンを編集するアクセスを許可します。", "skinsrestorer.permission_command_gui": "スキンGUIを開くアクセスを許可します。", "skinsrestorer.permission_admin_wildcard": "管理者のワイルドカード権限", "skinsrestorer.permission_admincommand": "メインの「/sr」コマンドへのアクセスを許可します。", "skinsrestorer.permission_command_set_other": "他のプレイヤーのスキンを設定するアクセスを許可します。", "skinsrestorer.permission_command_clear_other": "他のプレイヤーのスキンをクリアするアクセスを許可します。", "skinsrestorer.permission_command_random_other": "他のプレイヤーにランダムなスキンを設定するアクセスを許可します。", "skinsrestorer.permission_command_update_other": "他のプレイヤーのスキンを更新するアクセスを許可します。", "skinsrestorer.permission_command_favourite_other": "他のプレイヤーのスキンをお気に入りに設定するアクセスを許可します。", "skinsrestorer.permission_command_undo_other": "プレイヤーのスキンを前のスキンに戻すアクセスを許可します。", "skinsrestorer.permission_admincommand_skull": "メインの「/skull」コマンドへのアクセスを許可します。", "skinsrestorer.permission_admincommand_skull_get": "スカルを取得するアクセスを許可します。", "skinsrestorer.permission_admincommand_skull_get_url": "URLでスカルを取得するアクセスを許可します。", "skinsrestorer.permission_admincommand_skull_random": "ランダムなスカルを取得するアクセスを許可します。", "skinsrestorer.permission_admincommand_skull_get_other": "他のプレイヤーにスカルを与えるアクセスを許可します", "skinsrestorer.permission_admincommand_skull_random_other": "他のプレイヤーにランダムなスカルを与えるアクセスを許可します。", "skinsrestorer.permission_admincommand_reload": "「/sr reload」へのアクセスを許可します。", "skinsrestorer.permission_admincommand_status": "「/sr status」へのアクセスを許可します。", "skinsrestorer.permission_admincommand_drop": ".SKINファイルを削除するアクセスを許可します。", "skinsrestorer.permission_admincommand_info": "プレイヤーまたはスキンのスキン情報を取得するアクセスを許可します。", "skinsrestorer.permission_admincommand_applyskin": "他のプレイヤーのスキンを再適用するアクセスを許可します。", "skinsrestorer.permission_admincommand_createcustom": "URLでカスタムグローバルスキンを作成するアクセスを許可します。", "skinsrestorer.permission_admincommand_purgeolddata": "古いスキンデータを削除するアクセスを許可します。", "skinsrestorer.permission_admincommand_dump": "「/sr dump」経由でサーバー情報をアップロードするアクセスを許可します。", "skinsrestorer.permission_bypasscooldown": "設定で設定されたコマンドのクールダウンをバイパスします。", "skinsrestorer.permission_bypassdisabled": "設定で設定された無効なスキンをバイパスします。", "skinsrestorer.permission_ownskin": "自分のスキンを設定するアクセスを許可します。", "skinsrestorer.duration_day": " 日", "skinsrestorer.duration_days": " 日", "skinsrestorer.duration_hour": " 時間", "skinsrestorer.duration_hours": " 時間", "skinsrestorer.duration_minute": " 分", "skinsrestorer.duration_minutes": " 分", "skinsrestorer.duration_second": " 秒", "skinsrestorer.duration_seconds": " 秒"}