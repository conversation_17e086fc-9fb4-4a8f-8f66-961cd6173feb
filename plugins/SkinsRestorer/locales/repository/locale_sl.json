{"skinsrestorer.help_skin": "Spremeni tvoj skin.", "skinsrestorer.help_skins": "Odpre grafični vmesnik.", "skinsrestorer.help_sr": "Administrativni ukazi za SkinsRestorer.", "skinsrestorer.help_skin_set": "Spremeni tvoj skin.", "skinsrestorer.help_skin_set_other": "Spremeni skin drugega igralca.", "skinsrestorer.help_skin_set_url": "Spremeni tvoj skin preko linka.", "skinsrestorer.help_skin_clear": "Vrnes svoj skin nazaj na privzeto.", "skinsrestorer.help_skin_clear_other": "V<PERSON>s skin izbranega igralca na prevzeto.", "skinsrestorer.help_skin_search": "Poisci skin katerega zelis.", "skinsrestorer.help_skin_update": "Posodobi tvoj skin.", "skinsrestorer.help_skin_update_other": "Posodobi skin izbranega igralca.", "skinsrestorer.help_sr_reload": "Ponovno naloži konfiguracijo.", "skinsrestorer.help_sr_status": "Preveri potrebne storitve API vticnika.", "skinsrestorer.help_sr_drop": "Odstrani igralca ali skin iz database.", "skinsrestorer.help_sr_apply_skin": "<PERSON><PERSON><PERSON> <PERSON> izbrane<PERSON> i<PERSON>.", "skinsrestorer.help_sr_create_custom": "Ustvari skin za vse na strezniku.", "skinsrestorer.help_sr_purge_old_data": "<PERSON><PERSON>i skine katere so imeli igralci pred x dnevi.", "skinsrestorer.help_sr_dump": "Naloži informacije za podporo na bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.success_skin_clear": "<PERSON><PERSON><PERSON> skin je bil ponastavljen.", "skinsrestorer.success_updating_skin": "<PERSON><PERSON><PERSON> skin je bil posodobljen.", "skinsrestorer.success_admin_applyskin": "Igralcev skin je bil osvežen!", "skinsrestorer.success_admin_drop": "<type> podatek odstranjen za <target>.", "skinsrestorer.success_admin_reload": "Konfiguracija ter prevodi so bili ponovno naloženi!", "skinsrestorer.error_generic": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "<PERSON>d zah<PERSON> podatkov o skinu je prislo do napake, poskusite znova pozneje!", "skinsrestorer.error_skin_disabled": "Ta skin je bil prepovedan s strani administracije.", "skinsrestorer.error_skinurl_disallowed": "Ta domena ni dovoljena s strani administracije.", "skinsrestorer.error_updating_skin": "<PERSON><PERSON><PERSON> je do napake ko si poskusal nastaviti svoj skin. Poskusi znova kasneje!", "skinsrestorer.error_updating_url": "URL skin-ov ni mogoce posodobiti! <newline><red>Uporabi ukaz /skin URL", "skinsrestorer.error_updating_customskin": "Skina ni bilo mogoce posodobiti zato ker je custom.", "skinsrestorer.error_admin_applyskin": "Igralcev skin NI bilo mogoce osveziti!", "skinsrestorer.error_ms_api_failed": "MineSkin API je preobremenjen, prosim poskusi kasneje!", "skinsrestorer.error_ms_api_key_invalid": "Neveljaven \"API MineSkin key\", glede tega se obrnite na lastnika streznika!", "skinsrestorer.error_ms_unknown": "<PERSON>eznana napaka na MineSkin!", "skinsrestorer.not_connected_to_server": "<red><PERSON><PERSON> povezan na noben streznik.", "skinsrestorer.admincommand_status_checking": "<gray>Preverjanje potrebnih storitev za pravilno delovanje SR...", "skinsrestorer.admincommand_status_working": "<green>Plugin trenutno deluje brezhibno.", "skinsrestorer.admincommand_status_broken": "<red>Plugin trenutno ne more pridobiti novih skinov.<newline>Zgleda da povezavo blokera firewall.<newline>Poglej si https://skinsrestorer.net/firewall za vec informacij", "skinsrestorer.admincommand_status_summary_server": "<gray><PERSON><PERSON><PERSON>: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>ProxyMode: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Commit: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>Igralec <yellow><player></yellow> ni bil najden/a.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> ni bil najden/a.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Nismo mogli stopiti v stik z Mojangom, da bi pridobili UUID igralca", "skinsrestorer.admincommand_purgeolddata_success": "<green>Uspesno si pocistil vse stare skine!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red><PERSON><PERSON><PERSON> je do napake ko si poskusal pocisti stare skine!", "skinsrestorer.admincommand_dump_uploading": "<green>Nalaganje podatkov na bytebin.lucko.me...", "skinsrestorer.admincommand_dump_error": "<red>Napaka pri nalaganju podatkov na bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red><PERSON><PERSON><PERSON> so bile onemogocene za streznik <server>.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> da <PERSON> skin.", "skinsrestorer.player_has_no_permission_url": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> da <PERSON> skin.", "skinsrestorer.not_premium": "<dark_red>Napaka<dark_gray>: <red>Premium igralec s tem imenom ne obstaja.", "skinsrestorer.only_allowed_on_console": "<dark_red>Napaka<dark_gray>: <red>Samo v konzoli se lahko uporablja ta komanda!", "skinsrestorer.only_allowed_on_player": "<dark_red>Napaka<dark_gray>: <red><PERSON><PERSON> igralci la<PERSON>ko i<PERSON> to komando!", "skinsrestorer.invalid_player": "<dark_red>Napaka<dark_gray>: <red><input> ni validno uporabnis<PERSON> ime ali URL.", "skinsrestorer.ms_uploading_skin": "<dark_green><PERSON><PERSON><PERSON><PERSON>, prosim pocakaj... (To lahko traja nekaj casa)", "skinsrestorer.wait_a_minute": "<dark_red>Napaka<dark_gray>: <red><PERSON><PERSON><PERSON><PERSON>uto, preden znova zahtevas ta skin. (Rate Limited)", "skinsrestorer.skinsmenu_open": "<dark_green>Odperas skin menu...", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Naslednja stran</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Odstrani skin</gray> <bold>]</bold>", "skinsrestorer.no_skin_data": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Nobenega skina ni bilo najdeno! Ali ima igralec sploh skin?", "skinsrestorer.permission_player_wildcard": "Wildcard permission for players", "skinsrestorer.permission_command": "Omogoca dostop do komande \"/skin\".", "skinsrestorer.permission_command_set": "Omogoca dostop do spreminjanja skina.", "skinsrestorer.permission_command_set_url": "Omogoca dostop do spreminjanja skina po URL-ju.", "skinsrestorer.permission_command_clear": "Omogoca dostop do ciscenja skina.", "skinsrestorer.permission_command_update": "Omogoca dostop do posodobitve svoje skina.", "skinsrestorer.permission_command_search": "Omogoca dostop do iskanja svojega skina.", "skinsrestorer.permission_command_gui": "Omogoca dostop do GUI menuja z skini.", "skinsrestorer.permission_admin_wildcard": "Wildcard permission za osebje", "skinsrestorer.permission_admincommand": "Omogoca dostop do glavnih ukazov \"/sr\".", "skinsrestorer.permission_command_set_other": "Omogoca dostop do nastavitve skina za drugega igralca.", "skinsrestorer.permission_command_clear_other": "Omogoca dostop da izbrises skin drugemu igralcu.", "skinsrestorer.permission_command_update_other": "Omogoca dostop do posodobitve skina za drugega igralca.", "skinsrestorer.permission_admincommand_reload": "Omogoca dostop do \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Omogoca dostop do \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Omogoca dostop do odstranitve datoteke .SKIN.", "skinsrestorer.permission_admincommand_applyskin": "Omogoca dostop do ponovne uporabe starega skina za drugega igralca.", "skinsrestorer.permission_admincommand_createcustom": "Omogoca dostop do ustvarjanja globalnega skina po URL-ju.", "skinsrestorer.permission_admincommand_purgeolddata": "Omogoca dostop do brisanja starih skinov.", "skinsrestorer.permission_admincommand_dump": "Omogoca dostop do informacij o strezniku za nalaganje prek »/sr dump«.", "skinsrestorer.permission_bypasscooldown": "Zaobide vse ohlajanje komand, nastavljene v konfiguraciji.", "skinsrestorer.permission_bypassdisabled": "Zaobide vse onemogocene skine, nastavljene v konfiguraciji.", "skinsrestorer.permission_ownskin": "Omogoca dostop da lahko nastavis svoj skin."}