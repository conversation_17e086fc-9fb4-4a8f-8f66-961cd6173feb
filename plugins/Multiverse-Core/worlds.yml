worlds:
  lantea:
    ==: MVWorld
    hidden: 'false'
    alias: 兰蒂亚星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_LANTEA
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  the_other:
    ==: MVWorld
    hidden: 'false'
    alias: 异界
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 11.5
      y: 63.0
      z: 29.5
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: ALLTHEMODIUM_THE_OTHER
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/aether/the_aether:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AETHER_THE_AETHER
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  cavum_tenebrae:
    ==: MVWorld
    hidden: 'false'
    alias: 暗穴星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.5
      y: 64.0
      z: -183.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_CAVUM_TENEBRAE
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  chulak:
    ==: MVWorld
    hidden: 'false'
    alias: 丘拉克星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -127.5
      y: 63.0
      z: -168.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_CHULAK
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  venus_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: 金星轨道
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_VENUS_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  everbright:
    ==: MVWorld
    hidden: 'false'
    alias: 永昼之地
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: BLUE_SKIES_EVERBRIGHT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/voidscape/void:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -3.5
      y: 66.0
      z: -43.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: VOIDSCAPE_VOID
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/blue_skies/everdawn:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -11.5
      y: 66.0
      z: -36.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: BLUE_SKIES_EVERDAWN
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/bloodmagic/dungeon:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: BLOODMAGIC_DUNGEON
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/venus:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 66.0
      z: -45.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_VENUS
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  compact_world:
    ==: MVWorld
    hidden: 'false'
    alias: 紧凑世界
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: HARD
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'false'
    spawnLocation:
      ==: MVSpawnLocation
      x: 13.0
      y: 63.0
      z: 31.0
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: 'null'
    seed: '-6532533544110948267'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  earth_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: 地球轨道
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_EARTH_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/glacio:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 66.0
      z: -45.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_GLACIO
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/moon:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 66.0
      z: -45.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MOON
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/sgjourney/lantea:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_LANTEA
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/twilightforest/twilight_forest:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 25.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: TWILIGHTFOREST_TWILIGHT_FOREST
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  twilight_forest:
    ==: MVWorld
    hidden: 'false'
    alias: 暮色森林
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 4.5
      y: 78.0
      z: 1.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: TWILIGHTFOREST_TWILIGHT_FOREST
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/blue_skies/everbright:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.5
      y: 69.0
      z: -43.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: BLUE_SKIES_EVERBRIGHT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  void:
    ==: MVWorld
    hidden: 'false'
    alias: 虚空
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -134.5
      y: 65.0
      z: -182.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: VOIDSCAPE_VOID
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  alfheim:
    ==: MVWorld
    hidden: 'false'
    alias: 精灵之乡
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: MYTHICBOTANY_ALFHEIM
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  starlight:
    ==: MVWorld
    hidden: 'false'
    alias: 星光世界
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: HARD
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'false'
    spawnLocation:
      ==: MVSpawnLocation
      x: 13.0
      y: 63.0
      z: 31.0
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: 'null'
    seed: '-6532533544110948267'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  mining:
    ==: MVWorld
    hidden: 'false'
    alias: ATM挖矿维度
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 13.0
      y: 63.0
      z: 31.0
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: ALLTHEMODIUM_MINING
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/allthemodium/the_beyond:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: ALLTHEMODIUM_THE_BEYOND
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  lostcity:
    ==: MVWorld
    hidden: 'false'
    alias: 失落之城
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.5
      y: 64.0
      z: -178.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: LOSTCITIES_LOSTCITY
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/mercury:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 66.0
      z: -45.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MERCURY
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  '1':
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: 'null'
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  moon:
    ==: MVWorld
    hidden: 'false'
    alias: 月球
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MOON
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  mars_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: 火星轨道
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MARS_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/mercury_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MERCURY_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/DIM1:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '16.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -9.5
      y: 67.0
      z: -47.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: THE_END
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/mars:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 66.0
      z: -45.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MARS
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/venus_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_VENUS_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/earth_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_EARTH_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  reality_marble:
    ==: MVWorld
    hidden: 'false'
    alias: 固有结界
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 71.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: MAHOUTSUKAI_REALITY_MARBLE
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  the_beyond:
    ==: MVWorld
    hidden: 'false'
    alias: 彼界
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 13.0
      y: 63.0
      z: 31.0
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: ALLTHEMODIUM_THE_BEYOND
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  mercury:
    ==: MVWorld
    hidden: 'false'
    alias: 水星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MERCURY
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/sgjourney/abydos:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 1.5
      y: 73.0
      z: -39.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_ABYDOS
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/deeperdarker/otherside:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 2.5
      y: 63.0
      z: -38.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: DEEPERDARKER_OTHERSIDE
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  pocket_dimension:
    ==: MVWorld
    hidden: 'false'
    alias: 口袋维度
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: HARD
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'false'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 81.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: 'null'
    seed: '-6532533544110948267'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  glacio:
    ==: MVWorld
    hidden: 'false'
    alias: 冰川星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_GLACIO
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  venus:
    ==: MVWorld
    hidden: 'false'
    alias: 金星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_VENUS
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world:
    ==: MVWorld
    hidden: 'false'
    alias: 主世界
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -3.4374182436231906
      y: 66.0
      z: -44.19929098249326
      pitch: 13.799956
      yaw: -2.2502747
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/mythicbotany/alfheim:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 66.0
      z: -45.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: MYTHICBOTANY_ALFHEIM
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  dread_land:
    ==: MVWorld
    hidden: 'false'
    alias: 恐惧之地
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: HARD
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'false'
    spawnLocation:
      ==: MVSpawnLocation
      x: 13.0
      y: 63.0
      z: 31.0
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: 'null'
    seed: '-6532533544110948267'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/moon_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MOON_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/undergarden/undergarden:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 1.5
      y: 65.0
      z: -52.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: UNDERGARDEN_UNDERGARDEN
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  moon_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: 月球轨道
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MOON_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  glacio_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: 冰川星轨道
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_GLACIO_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  mars:
    ==: MVWorld
    hidden: 'false'
    alias: 火星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MARS
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/mars_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MARS_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  DIM1:
    ==: MVWorld
    hidden: 'false'
    alias: 末地
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '16.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 64.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: THE_END
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  DIM-1:
    ==: MVWorld
    hidden: 'false'
    alias: 下界
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '8.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 8.5
      y: 75.0
      z: 8.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NETHER
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/lostcities/lostcity:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: LOSTCITIES_LOSTCITY
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/DIM-1:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '8.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -1.5
      y: 66.0
      z: -43.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NETHER
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  dungeon:
    ==: MVWorld
    hidden: 'false'
    alias: 血魔法地牢
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: BLOODMAGIC_DUNGEON
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/allthemodium/the_other:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 66.0
      z: -45.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: ALLTHEMODIUM_THE_OTHER
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  undergarden:
    ==: MVWorld
    hidden: 'false'
    alias: 地下花园
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 11.5
      y: 63.0
      z: 33.5
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: UNDERGARDEN_UNDERGARDEN
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  the_bumblezone:
    ==: MVWorld
    hidden: 'false'
    alias: 蜜蜂领域
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: HARD
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'false'
    spawnLocation:
      ==: MVSpawnLocation
      x: 21.5
      y: 58.0
      z: 36.5
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: 'null'
    seed: '-6532533544110948267'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  abydos:
    ==: MVWorld
    hidden: 'false'
    alias: 阿比多斯星
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.5
      y: 62.0
      z: -169.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_ABYDOS
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  mercury_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: 水星轨道
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_MERCURY_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  the_aether:
    ==: MVWorld
    hidden: 'false'
    alias: 天境
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -126.5
      y: 72.0
      z: -167.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AETHER_THE_AETHER
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  otherside:
    ==: MVWorld
    hidden: 'false'
    alias: 彼岸
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 18.5
      y: 63.0
      z: 37.5
      pitch: 0.0
      yaw: 179.90009
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: DEEPERDARKER_OTHERSIDE
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/sgjourney/cavum_tenebrae:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -5.5
      y: 60.0
      z: -39.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_CAVUM_TENEBRAE
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/ad_astra/glacio_orbit:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.0
      y: 66.0
      z: -45.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: AD_ASTRA_GLACIO_ORBIT
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  everdawn:
    ==: MVWorld
    hidden: 'false'
    alias: 永晓之地
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -128.0
      y: 64.0
      z: -176.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: BLUE_SKIES_EVERDAWN
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/allthemodium/mining:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -0.5
      y: 65.0
      z: -50.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: ALLTHEMODIUM_MINING
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/mahoutsukai/reality_marble:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -2.5
      y: 71.0
      z: -44.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: MAHOUTSUKAI_REALITY_MARBLE
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world/sgjourney/chulak:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -4.5
      y: 66.0
      z: -43.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: SGJOURNEY_CHULAK
    seed: '7308142861528490543'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
