{"prefix": "&6枫影轻语FAWE&7 >> {0}", "fawe.worldedit.history.find.element": "&2{0} {1} &7前 &3{2}分钟 &6{3} &c/{4}", "fawe.worldedit.history.find.element.more": " - 更改: {0}\n - 边界: {1} -> {2}\n - 额外: {3}\n - 磁盘大小: {4}", "fawe.worldedit.history.find.hover": "{0} 个方块已更改，点击查看更多信息", "fawe.worldedit.history.distr.summary_null": "无法找到输入的编辑摘要。", "fawe.info.lighting.propagate.selection": "光照已在 {0} 个区块中传播。", "fawe.info.updated.lighting.selection": "光照已在 {0} 个区块中更新。（数据包发送可能需要一秒钟）", "fawe.info.set.region": "选择已设置为您当前允许的区域", "fawe.info.worldedit.toggle.tips.on": "已禁用 FAWE 提示。", "fawe.info.worldedit.toggle.tips.off": "已启用 FAWE 提示。", "fawe.info.worldedit.bypassed": "当前正在绕过 FAWE 限制。", "fawe.info.worldedit.restricted": "您的 FAWE 编辑现在受到限制。", "fawe.info.worldedit.oom.admin": "可能的选项：\n - //fast\n - 进行较小的编辑\n - 分配更多内存\n - 禁用 `max-memory-percent`", "fawe.info.temporarily-not-working": "暂时无法工作", "fawe.info.light-blocks": "光源方块比光源更可靠，请使用方块。此命令已弃用，将在未来版本中移除。", "fawe.info.update-available": "FastAsyncWorldEdit 有可用更新。您落后 {0} 个构建版本。\n您正在运行构建 {1}，最新版本是构建 {2}。\n在 {3} 更新", "fawe.web.generating.link": "正在上传 {0}，请稍候...", "fawe.web.generating.link.failed": "生成下载链接失败！", "fawe.web.download.link": "{0}", "fawe.web.image.load.timeout": "图像加载尝试超时，最大时间：{0}秒。请尝试较小分辨率的图像。", "fawe.web.image.load.size.too-large": "图像尺寸过大！最大允许大小（宽 x 高）：{0} 像素。", "fawe.worldedit.general.texture.disabled": "纹理已重置", "fawe.worldedit.general.texture.set": "纹理设置为 {1}", "fawe.worldedit.general.source.mask.disabled": "全局源掩码已禁用", "fawe.worldedit.general.source.mask": "全局源掩码已设置", "fawe.worldedit.general.transform.disabled": "全局变换已禁用", "fawe.worldedit.general.transform": "全局变换已设置", "fawe.worldedit.copy.command.copy": "{0} 个方块已复制。", "fawe.worldedit.cut.command.cut.lazy": "{0} 个方块将在粘贴时移除", "fawe.worldedit.paste.command.paste": "剪贴板已粘贴到 {0}", "fawe.worldedit.history.command.undo.disabled": "撤销已禁用，使用：//fast", "fawe.worldedit.selection.selection.count": "计数 {0} 个方块。", "fawe.worldedit.anvil.world.is.loaded": "执行时世界不应在使用中。卸载世界，或使用 -f 覆盖（先保存）", "fawe.worldedit.brush.brush.reset": "重置您的笔刷。（SHIFT + 点击）", "fawe.worldedit.brush.brush.none": "您没有拿着笔刷！", "fawe.worldedit.brush.brush.scroll.action.set": "滚动动作设置为 {0}", "fawe.worldedit.brush.brush.scroll.action.unset": "已移除滚动动作", "fawe.worldedit.brush.brush.visual.mode.set": "视觉模式设置为 {0}", "fawe.worldedit.brush.brush.target.mode.set": "目标模式设置为 {0}", "fawe.worldedit.brush.brush.target.offset.set": "目标偏移设置为 {0}", "fawe.worldedit.brush.brush.equipped": "已装备笔刷 {0}", "fawe.worldedit.brush.brush.try.other": "还有其他更合适的笔刷，例如：\n - //br height [radius=5] [#clipboard|file=null] [rotation=0] [yscale=1.00]", "fawe.worldedit.brush.brush.copy": "左键点击对象底部进行复制，右键点击进行粘贴。如有必要，增加笔刷半径。", "fawe.worldedit.brush.brush.height.invalid": "无效的高度图文件 ({0})", "fawe.worldedit.brush.brush.spline": "点击添加一个点，点击同一位置完成", "fawe.worldedit.brush.brush.line.primary": "已添加点 {0}，点击另一个位置创建线条", "fawe.worldedit.brush.brush.catenary.direction": "已添加点 {0}，点击您想要创建样条的方向", "fawe.worldedit.brush.brush.line.secondary": "已创建样条", "fawe.worldedit.brush.spline.primary.2": "已添加位置，点击同一位置连接！", "fawe.worldedit.brush.brush.spline.secondary.error": "设置的位置不够！", "fawe.worldedit.brush.spline.secondary": "已创建样条", "fawe.worldedit.brush.brush.source.mask.disabled": "笔刷源掩码已禁用", "fawe.worldedit.brush.brush.source.mask": "笔刷源掩码已设置", "fawe.worldedit.brush.brush.transform.disabled": "笔刷变换已禁用", "fawe.worldedit.brush.brush.transform": "笔刷变换已设置", "fawe.worldedit.rollback.rollingback.index": "正在撤销 {0} ...", "fawe.worldedit.rollback.rollback.element": "{0} 已撤销。", "fawe.worldedit.tool.tool.inspect": "检查工具绑定到 {0}。", "fawe.worldedit.tool.tool.inspect.info": "{0} 在 {3} 前将 {1} 更改为 {2}", "fawe.worldedit.tool.tool.inspect.info.footer": "总计：{0} 次更改", "fawe.worldedit.tool.tool.range.error": "最大范围：{0}。", "fawe.worldedit.tool.tool.lrbuild.info": "左键设置为 {0}；右键设置为 {1}。", "fawe.worldedit.utility.nothing.confirmed": "您没有待确认的操作。", "fawe.worldedit.schematic.schematic.move.exists": "{0} 已存在", "fawe.worldedit.schematic.schematic.move.success": "{0} -> {1}", "fawe.worldedit.schematic.schematic.move.failed": "{0} 未移动：{1}", "fawe.worldedit.schematic.schematic.loaded": "{0} 已加载。使用 //paste 粘贴", "fawe.worldedit.schematic.schematic.saved": "{0} 已保存。", "fawe.worldedit.schematic.schematic.none": "未找到文件。", "fawe.worldedit.schematic.schematic.load-failure": "无法读取文件或文件不存在：{0}。如果您指定了格式，可能没有指定正确的格式。Sponge 原理图 v2 和 v3 都使用 .schem 文件扩展名。要让 FAWE 选择格式，请不要指定格式。如果您使用的是 litematica 原理图，则不受支持！", "fawe.worldedit.clipboard.clipboard.uri.not.found": "您没有加载 {0}", "fawe.worldedit.clipboard.clipboard.cleared": "剪贴板已清除", "fawe.worldedit.clipboard.clipboard.invalid.format": "未知的剪贴板格式：{0}", "fawe.worldedit.visitor.visitor.block": "{0} 个方块受影响", "fawe.worldedit.selector.selector.fuzzy.pos1": "区域已设置并从 {0} {1} 扩展。", "fawe.worldedit.selector.selector.fuzzy.pos2": "已添加 {0} {1} 的扩展。", "fawe.progress.progress.message": "{1}/{0} ({2}%) @{3}cps 剩余 {4}秒", "fawe.progress.progress.finished": "[ 完成！ ]", "fawe.error.command.syntax": "用法：{0}", "fawe.error.no-perm": "您缺少权限节点：{0}", "fawe.error.block.not.allowed": "您不被允许使用：{0}", "fawe.error.setting.disable": "缺少设置：{0}", "fawe.error.brush.not.found": "可用笔刷：{0}", "fawe.error.brush.incompatible": "笔刷与此版本不兼容", "fawe.error.no.region": "您没有当前允许的区域", "fawe.error.player.not.found": "未找到玩家：{0}", "fawe.error.worldedit.some.fails": "{0} 个方块未放置，因为它们在您允许的区域之外。", "fawe.error.worldedit.some.fails.blockbag": "缺少方块：{0}", "fawe.error.mask.angle": "不能将度数与方块步骤结合", "fawe.error.invalid-flag": "标志 {0} 在此处不适用", "fawe.error.lighting": "尝试光照时出错。您可能需要重新加载区块才能看到编辑。", "fawe.error.parser.invalid-data": "无效数据：{0}", "fawe.error.unsupported": "不支持！", "fawe.error.invalid-block-type": "不匹配有效的方块类型：{0}", "fawe.error.invalid-block-state-property": "无法解析属性 `{1}` 的值 `{0}`，方块状态：`{2}`", "fawe.error.nbt.forbidden": "您不被允许使用 nbt。缺少权限：{0}", "fawe.error.invalid-arguments": "参数数量无效。期望：{0}", "fawe.error.unrecognised-tag": "无法识别的标签：{0} {1}", "fawe.error.unknown-block-tag": "未知方块标签：{0}", "fawe.error.block-tag-no-blocks": "方块标签 '{0}' 没有方块。", "fawe.error.no-block-found": "未找到 '{0}' 的方块。", "fawe.error.invalid-states": "无效状态：{0}", "fawe.error.no-session": "没有可用的会话，因此没有可用的剪贴板。", "fawe.error.empty-clipboard": "要使用 '{0}'，请先将某些内容复制到剪贴板", "fawe.error.selection-expand": "选择无法扩展。", "fawe.error.selection-contract": "选择无法扩展。", "fawe.error.selection-shift": "选择无法移动。", "fawe.error.invalid-user": "必须提供用户。", "fawe.error.radius-too-small": "半径必须 >=0", "fawe.error.time-too-less": "时间必须 >=0", "fawe.error.invalid-image": "无效图像：{0}", "fawe.error.image-dimensions": "图像尺寸过大，最大允许大小（宽 x 高）：{0} 像素。", "fawe.error.file-not-found": "文件未找到：{0}", "fawe.error.file-is-invalid-directory": "文件是一个目录：{0}", "fawe.error.stacktrace": "===============---=============", "fawe.error.no-failure": "这不应该导致任何失败", "fawe.error.invalid-bracketing": "无效的括号，您是否缺少 '{0}'。", "fawe.error.too-simple": "复杂度必须在 0-100 范围内", "fawe.error.outside-range": "参数 {0} 超出范围 {1}-{2}。", "fawe.error.outside-range-lower": "参数 {0} 不能小于 {1}", "fawe.error.outside-range-upper": "参数 {0} 不能大于 {1}", "fawe.error.argument-size-mismatch": "参数 {0} 不能大于参数 {1}", "fawe.error.input-parser-exception": "无效的空字符串而不是布尔值。", "fawe.error.invalid-boolean": "无效的布尔值 {0}", "fawe.error.schematic.not.found": "未找到原理图 {0}。", "fawe.error.parse.invalid-dangling-character": "无效的悬挂字符 {0}。", "fawe.error.parse.unknown-mask": "未知掩码：{0}，参见：{1}", "fawe.error.parse.unknown-pattern": "未知图案：{0}，参见：{1}", "fawe.error.parse.unknown-transform": "未知变换：{0}，参见：{1}", "fawe.error.parse.no-clipboard": "要使用 {0}，请先将某些内容复制到剪贴板", "fawe.error.parse.no-clipboard-source": "在给定源处未找到剪贴板：{0}", "fawe.error.clipboard.invalid": "====== 无效剪贴板 ======", "fawe.error.clipboard.invalid.info": "文件：{0}（长度：{1}）", "fawe.error.clipboard.load.failure": "从磁盘加载剪贴板时意外失败！", "fawe.error.clipboard.on.disk.version.mismatch": "剪贴板版本不匹配：期望 {0} 但得到 {1}。建议您删除剪贴板文件夹并重启服务器。\n您的剪贴板文件夹位于 {2}。", "fawe.error.limit.disallowed-block": "您的限制不允许使用方块 '{0}'", "fawe.error.limit.disallowed-property": "您的限制不允许使用属性 '{0}'", "fawe.error.region-mask-invalid": "无效的区域掩码：{0}", "fawe.error.occurred-continuing": "编辑期间发生可忽略的错误：{0}", "fawe.error.limit.max-brush-radius": "限制中的最大笔刷半径：{0}", "fawe.error.limit.max-radius": "限制中的最大半径：{0}", "fawe.error.no-valid-on-hotbar": "快捷栏上没有有效的方块类型", "fawe.error.no-process-non-synchronous-edit": "未找到处理器持有者，但编辑是非同步的", "fawe.cancel.count": "已取消 {0} 次编辑。", "fawe.cancel.reason.confirm": "使用 //confirm 执行 {0}", "fawe.cancel.reason.confirm.region": "您的选择很大（{0} -> {1}，包含 {3} 个方块）。使用 //confirm 执行 {2}", "fawe.cancel.reason.confirm.radius": "您的半径很大（{0} > {1}）。使用 //confirm 执行 {2}", "fawe.cancel.reason.confirm.limit": "您超出了此操作的限制（{0} > {1}）。使用 //confirm 执行 {2}", "fawe.cancel.reason": "您的 WorldEdit 操作已取消：{0}。", "fawe.cancel.reason.manual": "手动取消", "fawe.cancel.reason.low.memory": "内存不足", "fawe.cancel.reason.max.changes": "更改的方块太多", "fawe.cancel.reason.max.checks": "方块检查太多", "fawe.cancel.reason.max.fails": "失败太多", "fawe.cancel.reason.max.tiles": "方块实体太多", "fawe.cancel.reason.max.entities": "实体太多", "fawe.cancel.reason.max.iterations": "最大迭代次数", "fawe.cancel.reason.outside.level": "世界外部", "fawe.cancel.reason.outside.region": "允许区域外部（使用 /wea 绕过，或在 config.yml 中禁用 `region-restrictions`）", "fawe.cancel.reason.outside.safe.region": "安全编辑区域外部 +/- 30,000,000 方块。", "fawe.cancel.reason.no.region": "没有允许的区域（使用 /wea 绕过，或在 config.yml 中禁用 `region-restrictions`）", "fawe.cancel.reason.no.region.reason": "没有允许的区域：{0}", "fawe.cancel.reason.no.region.plot.noworldeditflag": "地皮标志 NoWorldeditFlag 已设置", "fawe.cancel.reason.no.region.plot.owner.offline": "区域所有者离线", "fawe.cancel.reason.no.region.plot.owner.only": "只有区域所有者可以编辑它们", "fawe.cancel.reason.no.region.not.added": "未添加到区域", "fawe.cancel.reason.player-only": "此操作需要玩家，不能从控制台执行，或没有执行者。", "fawe.cancel.reason.actor-required": "此操作需要执行者。", "fawe.cancel.reason.world.limit": "此操作无法在 y={0} 执行，因为它超出了世界限制。", "fawe.cancel.worldedit.failed.load.chunk": "跳过加载区块：{0};{1}。尝试增加 chunk-wait。", "fawe.navigation.no.block": "视线中没有方块！（或太远）", "fawe.selection.sel.max": "最多 {0} 个点。", "fawe.selection.sel.fuzzy": "模糊选择器：左键选择所有相邻方块，右键添加。要选择空气洞穴，使用 //pos1。", "fawe.selection.sel.fuzzy-instruction": "选择所有连接的方块（魔法棒）", "fawe.selection.sel.convex.polyhedral": "凸多面体选择器：左键=第一个顶点，右键添加更多。", "fawe.selection.sel.polyhedral": "选择一个空心多面体", "fawe.selection.sel.list": "要获取选择类型列表，请使用：//sel list", "fawe.tips.tip.sel.list": "提示：使用 //sel list 查看不同的选择模式", "fawe.tips.tip.select.connected": "提示：使用 //sel fuzzy 选择所有连接的方块", "fawe.tips.tip.set.pos1": "提示：使用 //set pos1 将 pos1 用作图案", "fawe.tips.tip.farwand": "提示：使用 //farwand 选择远距离点", "fawe.tips.tip.discord": "需要使用 FAWE 的帮助？https://discord.gg/intellectualsites", "fawe.tips.tip.lazycut": "提示：使用 //lazycut 更安全", "fawe.tips.tip.fast": "提示：使用 //fast 设置快速且无撤销", "fawe.tips.tip.cancel": "提示：您可以 //cancel 正在进行的编辑", "fawe.tips.tip.mask": "提示：使用 /gmask 设置全局目标掩码", "fawe.tips.tip.mask.angle": "提示：使用 //replace /[-20][-3] bedrock 替换 3-20 方块的向上斜坡", "fawe.tips.tip.set.linear": "提示：使用 //set #l3d[wood,bedrock] 线性设置方块", "fawe.tips.tip.surface.spread": "提示：使用 //set #surfacespread[5][0][5][#existing] 展开平坦表面", "fawe.tips.tip.set.hand": "提示：使用 //set hand 使用您当前手中的物品", "fawe.tips.tip.replace.regex": "提示：使用正则表达式替换：//replace .*_log <pattern>", "fawe.tips.tip.replace.regex.2": "提示：使用正则表达式替换：//replace .*stairs[facing=(north|south)] <pattern>", "fawe.tips.tip.replace.regex.3": "提示：使用运算符替换：//replace water[level>2] sand", "fawe.tips.tip.replace.regex.4": "提示：使用运算符替换：//replace true *[waterlogged=false]", "fawe.tips.tip.replace.regex.5": "提示：使用运算符替换：//replace true *[level-=1]", "fawe.tips.tip.replace.id": "提示：仅替换方块 ID：//replace woodenstair #id[cobblestair]", "fawe.tips.tip.replace.light": "提示：使用 //replace #brightness[1][15] 0 移除光源", "fawe.tips.tip.tab.complete": "提示：replace 命令支持 Tab 补全", "fawe.tips.tip.flip": "提示：使用 //flip 镜像", "fawe.tips.tip.deform": "提示：使用 //deform 重塑", "fawe.tips.tip.transform": "提示：使用 //gtransform 设置变换", "fawe.tips.tip.copypaste": "提示：使用 //br copypaste 点击粘贴", "fawe.tips.tip.source.mask": "提示：使用 /gsmask <mask> 设置源掩码", "fawe.tips.tip.replace.marker": "提示：使用 //replace wool #fullcopy 用完整剪贴板替换方块", "fawe.tips.tip.paste": "提示：使用 //paste 放置", "fawe.tips.tip.lazycopy": "提示：lazycopy 更快", "fawe.tips.tip.download": "提示：尝试 //download", "fawe.tips.tip.rotate": "提示：使用 //rotate 定向", "fawe.tips.tip.copy.pattern": "提示：要用作图案，请尝试 #copy", "fawe.tips.tip.regen.0": "提示：使用 /regen [biome] 使用生物群系", "fawe.tips.tip.regen.1": "提示：使用 /regen [biome] [seed] 使用种子", "fawe.tips.tip.biome.pattern": "提示：#biome[forest] 图案可以在任何命令中使用", "fawe.tips.tip.biome.mask": "提示：使用 `$jungle` 掩码限制到生物群系", "fawe.regen.time": "正在重新生成区域，这可能需要一段时间！", "worldedit.expand.description.vert": "垂直扩展选择到世界限制。", "worldedit.expand.expanded": "区域扩展了 {0} 个方块", "worldedit.expand.expanded.vert": "区域扩展了 {0} 个方块（从上到下）。", "worldedit.biomeinfo.lineofsight": "视线点的生物群系：{0}", "worldedit.biomeinfo.position": "您位置的生物群系：{0}", "worldedit.biomeinfo.selection": "您选择中的生物群系：{0}", "worldedit.biomeinfo.not-locatable": "命令发送者必须在世界中才能使用 -p 标志。", "worldedit.error.disabled": "此功能已禁用（请参阅 WorldEdit 配置）。", "worldedit.error.no-match": "没有匹配 '{0}'。", "worldedit.error.unknown": "发生未知错误：{0}", "worldedit.error.parser.player-only": "输入 '{0}' 需要玩家！", "worldedit.error.parser.bad-state-format": "{0} 中的状态格式错误", "worldedit.error.parser.unknown-property": "方块 '{1}' 的未知属性 '{0}'", "worldedit.error.parser.duplicate-property": "重复属性：{0}", "worldedit.error.parser.unknown-value": "属性 '{1}' 的未知值 '{0}'", "worldedit.error.parser.invalid-colon": "无效冒号。", "worldedit.error.parser.hanging-lbracket": "无效格式。在 '{0}' 处悬挂括号。", "worldedit.error.parser.missing-rbracket": "状态缺少尾随 ']'", "worldedit.error.incomplete-region": "请先进行区域选择。", "worldedit.error.not-a-block": "此物品不是方块。", "worldedit.error.unknown-entity": "实体名称 '{0}' 无法识别。", "worldedit.error.unknown-mob": "生物名称 '{0}' 无法识别。", "worldedit.error.parser.clipboard.missing-offset": "使用 @ 指定了偏移但未给出偏移。使用 '#copy@[x,y,z]'。", "worldedit.error.parser.clipboard.missing-coordinates": "剪贴板偏移需要 x,y,z 坐标。", "worldedit.error.unknown-item": "物品名称 '{0}' 无法识别。", "worldedit.error.parser.invalid-expression": "无效表达式：{0}", "worldedit.error.parser.negate-nothing": "无法否定空值！", "worldedit.error.invalid-page": "无效页码", "worldedit.error.missing-extent": "未知 Extent", "worldedit.error.missing-session": "未知 LocalSession", "worldedit.error.missing-world": "您需要提供一个世界（尝试 //world）", "worldedit.error.missing-actor": "未知执行者", "worldedit.error.missing-player": "未知玩家", "worldedit.error.no-file-selected": "未选择文件。", "worldedit.error.file-resolution.outside-root": "路径在允许的根目录之外", "worldedit.error.file-resolution.resolve-failed": "解析路径失败", "worldedit.error.invalid-filename.invalid-characters": "无效字符或缺少扩展名", "worldedit.error.invalid-number.matches": "期望数字；给出字符串 \"{0}\"。", "worldedit.error.invalid-number": "期望数字；给出字符串。", "worldedit.error.unknown-block": "方块名称 '{0}' 无法识别。", "worldedit.error.disallowed-block": "方块 '{0}' 不被允许（请参阅 WorldEdit 配置）。", "worldedit.error.max-changes": "操作中更改的最大方块数已达到（{0}）。", "worldedit.error.max-brush-radius": "最大笔刷半径（在 worldedit-config.yml 中）：{0}", "worldedit.error.max-radius": "最大半径（在 worldedit-config.yml 中）：{0}", "worldedit.error.unknown-direction": "未知方向：{0}", "worldedit.error.empty-clipboard": "您的剪贴板为空。请先使用 //copy。", "worldedit.error.invalid-filename": "文件名 '{0}' 无效：{1}", "worldedit.error.file-resolution": "文件 '{0}' 解析错误：{1}", "worldedit.tool.error.cannot-bind": "无法将工具绑定到 {0}：{1}", "worldedit.error.file-aborted": "文件选择已中止。", "worldedit.error.world-unloaded": "世界已被卸载。", "worldedit.error.named-world-unloaded": "世界 '{0}' 已被卸载。", "worldedit.error.blocks-cant-be-used": "方块无法使用", "worldedit.error.unknown-tag": "标签名称 '{0}' 无法识别。", "worldedit.error.empty-tag": "标签名称 '{0}' 没有内容。", "worldedit.error.unknown-biome": "生物群系名称 '{0}' 无法识别。", "worldedit.brush.radius-too-large": "允许的最大笔刷半径：{0}", "worldedit.brush.apply.description": "应用笔刷，对每个方块应用函数", "worldedit.brush.apply.radius": "笔刷的大小", "worldedit.brush.apply.shape": "区域的形状", "worldedit.brush.apply.type": "要使用的笔刷类型", "worldedit.brush.apply.item.warning": "此笔刷模拟物品使用。其效果可能不适用于所有平台，可能无法撤销，并可能与其他模组/插件产生奇怪的交互。使用风险自负。", "worldedit.brush.paint.description": "绘画笔刷，对表面应用函数", "worldedit.brush.paint.size": "笔刷的大小", "worldedit.brush.paint.shape": "区域的形状", "worldedit.brush.paint.density": "笔刷的密度", "worldedit.brush.paint.type": "要使用的笔刷类型", "worldedit.brush.paint.item.warning": "此笔刷模拟物品使用。其效果可能不适用于所有平台，可能无法撤销，并可能与其他模组/插件产生奇怪的交互。使用风险自负。", "worldedit.brush.sphere.equip": "球体笔刷形状已装备（{0}）。", "worldedit.brush.cylinder.equip": "圆柱体笔刷形状已装备（{0} x {1}）。", "worldedit.brush.clipboard.equip": "剪贴板笔刷形状已装备。", "worldedit.brush.smooth.equip": "平滑笔刷已装备（{0} x {1}x 使用 {2}）。", "worldedit.brush.smooth.nofilter": "任何方块", "worldedit.brush.smooth.filter": "过滤器", "worldedit.brush.snowsmooth.equip": "雪平滑笔刷已装备（{0} x {1}x 使用 {2}），{3} 个雪方块。", "worldedit.brush.snowsmooth.nofilter": "任何方块", "worldedit.brush.snowsmooth.filter": "过滤器", "worldedit.brush.extinguish.equip": "灭火器已装备（{0}）。", "worldedit.brush.gravity.equip": "重力笔刷已装备（{0}）。", "worldedit.brush.butcher.equip": "屠夫笔刷已装备（{0}）。", "worldedit.brush.operation.equip": "笔刷设置为 {0}。", "worldedit.brush.none.equip": "笔刷已从您当前物品解绑。", "worldedit.brush.none.equipped": "您当前物品没有绑定笔刷。尝试 /brush sphere 获取基本笔刷。", "worldedit.setbiome.changed": "生物群系在 {0} 列中已更改。您可能需要重新加入游戏（或关闭并重新打开世界）才能看到更改。", "worldedit.setbiome.not-locatable": "命令发送者必须在世界中才能使用 -p 标志。", "worldedit.drawsel.disabled": "服务器 CUI 已禁用。", "worldedit.drawsel.enabled": "服务器 CUI 已启用。这仅支持立方体区域，最大大小为 {0}x{1}x{2}。", "worldedit.drawsel.disabled.already": "服务器 CUI 已经禁用。", "worldedit.drawsel.enabled.already": "服务器 CUI 已经启用。", "worldedit.limit.too-high": "您的最大允许限制是 {0}。", "worldedit.limit.set": "方块更改限制设置为 {0}。", "worldedit.limit.return-to-default": "（使用 //limit 返回默认值。）", "worldedit.timeout.too-high": "您的最大允许超时时间是 {0}毫秒。", "worldedit.timeout.set": "超时时间设置为 {0}毫秒。", "worldedit.timeout.return-to-default": "（使用 //timeout 返回默认值。）", "worldedit.fast.disabled": "快速模式已禁用。", "worldedit.fast.enabled": "快速模式已启用。更改不会写入历史记录（//undo 已禁用）。受影响区块中的光照可能错误和/或您可能需要重新加入才能看到更改。", "worldedit.fast.disabled.already": "快速模式已经禁用。", "worldedit.fast.enabled.already": "快速模式已经启用。", "worldedit.perf.sideeffect.set": "副作用 \"{0}\" 设置为 {1}", "worldedit.perf.sideeffect.get": "副作用 \"{0}\" 设置为 {1}", "worldedit.perf.sideeffect.already-set": "副作用 \"{0}\" 已经是 {1}", "worldedit.perf.sideeffect.set-all": "所有副作用设置为 {0}", "worldedit.reorder.current": "重排序模式是 {0}", "worldedit.reorder.set": "重排序模式现在是 {0}", "worldedit.gmask.disabled": "全局掩码已禁用。", "worldedit.gmask.set": "全局掩码已设置。", "worldedit.toggleplace.pos1": "现在在位置 #1 放置。", "worldedit.toggleplace.player": "现在在您站立的方块放置。", "worldedit.toggleplace.not-locatable": "无法在此上下文中切换放置。", "worldedit.searchitem.too-short": "输入更长的搜索字符串（长度 > 2）。", "worldedit.searchitem.either-b-or-i": "您不能同时使用 'b' 和 'i' 标志。", "worldedit.searchitem.searching": "（请稍候...正在搜索物品。）", "worldedit.watchdog.no-hook": "此平台没有看门狗钩子。", "worldedit.watchdog.active.already": "看门狗钩子已经激活。", "worldedit.watchdog.inactive.already": "看门狗钩子已经不活跃。", "worldedit.watchdog.active": "看门狗钩子现在激活。", "worldedit.watchdog.inactive": "看门狗钩子现在不活跃。", "worldedit.world.remove": "已移除世界覆盖。", "worldedit.world.set": "世界覆盖设置为 {0}。（使用 //world 返回默认值）", "worldedit.undo.undone": "撤销了 {0} 个可用编辑。", "worldedit.undo.none": "没有可撤销的内容。", "worldedit.redo.redone": "重做了 {0} 个可用编辑。", "worldedit.redo.none": "没有可重做的内容。", "worldedit.clearhistory.cleared": "历史记录已清除。", "worldedit.raytrace.noblock": "视线中没有方块！", "worldedit.raytrace.require-player": "射线追踪命令需要玩家！", "worldedit.restore.not-configured": "快照/备份恢复未配置。", "worldedit.restore.not-available": "该快照不存在或不可用。", "worldedit.restore.failed": "加载快照失败：{0}", "worldedit.restore.loaded": "快照 '{0}' 已加载；现在恢复中...", "worldedit.restore.restored": "已恢复；{0} 个缺失区块和 {1} 个其他错误。", "worldedit.restore.none-for-specific-world": "未找到世界 '{0}' 的快照。", "worldedit.restore.none-for-world": "未找到此世界的快照。", "worldedit.restore.none-found": "未找到快照。", "worldedit.restore.none-found-console": "未找到快照。详情请查看控制台。", "worldedit.restore.chunk-not-present": "快照中不存在区块。", "worldedit.restore.chunk-load-failed": "无法加载区块。（损坏的存档？）", "worldedit.restore.block-place-failed": "错误阻止了任何方块的恢复。", "worldedit.restore.block-place-error": "最后错误：{0}", "worldedit.snapshot.use.newest": "现在使用最新快照。", "worldedit.snapshot.use": "快照设置为：{0}", "worldedit.snapshot.none-before": "无法找到 {0} 之前的快照。", "worldedit.snapshot.none-after": "无法找到 {0} 之后的快照。", "worldedit.snapshot.index-above-0": "无效索引，必须大于或等于 1。", "worldedit.snapshot.index-oob": "无效索引，必须在 1 和 {0} 之间。", "worldedit.schematic.unknown-format": "未知原理图格式：{0}。", "worldedit.schematic.load.does-not-exist": "原理图 {0} 不存在！", "worldedit.schematic.load.loading": "（请稍候...正在加载原理图。）", "worldedit.schematic.load.unsupported-version": "此原理图不受支持。版本：{0}。如果您使用的是 litematica 原理图，则不受支持！", "worldedit.schematic.save.already-exists": "该原理图已存在。使用 -f 标志覆盖它。", "worldedit.schematic.save.failed-directory": "无法为原理图创建文件夹！", "worldedit.schematic.save.saving": "（请稍候...正在保存原理图。）", "worldedit.schematic.save.still-saving": "（请稍候...仍在保存原理图。）", "worldedit.schematic.share.unsupported-format": "原理图共享目标 \"{0}\" 不支持 \"{1}\" 格式。", "worldedit.schematic.share.response.arkitektonika.download": "下载：{0}", "worldedit.schematic.share.response.arkitektonika.delete": "删除：{0}", "worldedit.schematic.share.response.arkitektonika.click-here": "[点击这里]", "worldedit.schematic.delete.empty": "原理图 {0} 未找到！", "worldedit.schematic.delete.does-not-exist": "原理图 {0} 不存在！", "worldedit.schematic.delete.failed": "删除 {0} 失败！是否为只读？", "worldedit.schematic.delete.deleted": "{0} 已被删除。", "worldedit.schematic.formats.title": "可用剪贴板格式（名称：查找名称）", "worldedit.schematic.load.symbol": "[L]", "worldedit.schematic.plus.symbol": "[+]", "worldedit.schematic.minus.symbol": "[-]", "worldedit.schematic.x.symbol": "[X]", "worldedit.schematic.0.symbol": "[O]", "worldedit.schematic.dash.symbol": " - ", "worldedit.schematic.click-to-load": "点击加载", "worldedit.schematic.load": "加载", "worldedit.schematic.list": "列表", "worldedit.schematic.available": "可用原理图", "worldedit.schematic.unload": "卸载", "worldedit.schematic.delete": "删除", "worldedit.schematic.visualize": "可视化", "worldedit.schematic.clipboard": "添加到（多）剪贴板", "worldedit.schematic.unknown-filename": "未知文件名：{0}", "worldedit.schematic.file-not-exist": "无法读取文件或文件不存在：{0}", "worldedit.schematic.already-exists": "该原理图已存在！", "worldedit.schematic.failed-to-save": "保存原理图失败", "worldedit.schematic.directory-does-not-exist": "目录 '{0}' 不存在！", "worldedit.schematic.file-perm-fail": "创建 '{0}' 失败！检查文件权限。", "worldedit.schematic.sorting-old-new": "无法按最旧和最新排序。", "worldedit.schematic.unsupported-minecraft-version": "此版本的 WorldEdit 不支持您的 Minecraft 版本。在解决此问题之前，原理图将无法工作。", "worldedit.pos.already-set": "位置已设置。", "worldedit.pos.console-require-coords": "您必须在控制台中提供坐标。", "worldedit.hpos.no-block": "视线中没有方块！", "worldedit.hpos.already-set": "位置已设置。", "worldedit.chunk.selected-multiple": "已选择区块：({0}, {1}, {2}) - ({3}, {4}, {5})", "worldedit.chunk.selected": "已选择区块：{0}, {1}, {2}", "worldedit.wand.invalid": "魔法棒物品配置错误或已禁用。", "worldedit.wand.selwand.info": "左键：选择位置 #1；右键：选择位置 #2", "worldedit.wand.selwand.now.tool": "选择魔法棒现在是普通工具。您可以使用 {0} 禁用它，使用 {1} 将其重新绑定到任何物品，或使用 {2} 获取新魔法棒。", "worldedit.wand.navwand.info": "左键：跳转到位置；右键：穿墙", "worldedit.contract.contracted": "区域收缩了 {0} 个方块。", "worldedit.shift.shifted": "区域已移动。", "worldedit.outset.outset": "区域外扩。", "worldedit.inset.inset": "区域内缩。", "worldedit.size.offset": "{0}：{1} @ {2}（{3} 个方块）", "worldedit.size.type": "类型：{0}", "worldedit.size.size": "大小：{0}", "worldedit.size.distance": "立方体距离：{0}", "worldedit.size.blocks": "方块数：{0}", "worldedit.count.counted": "计数：{0}", "worldedit.distr.no-blocks": "没有计数方块。", "worldedit.distr.no-previous": "没有之前的分布。", "worldedit.distr.total": "总方块数：{0}", "worldedit.select.cleared": "选择已清除。", "worldedit.select.cuboid.message": "立方体：左键点击点 1，右键点击点 2", "worldedit.select.cuboid.description": "选择立方体的两个角", "worldedit.select.extend.message": "立方体：左键点击起始点，右键扩展", "worldedit.select.extend.description": "快速立方体选择模式", "worldedit.select.poly.message": "2D 多边形选择器：左/右键点击添加点。", "worldedit.select.poly.limit-message": "最多 {0} 个点。", "worldedit.select.poly.description": "选择带高度的 2D 多边形", "worldedit.select.ellipsoid.message": "椭球选择器：左键=中心，右键扩展", "worldedit.select.ellipsoid.description": "选择椭球", "worldedit.select.sphere.message": "球体选择器：左键=中心，右键设置半径", "worldedit.select.sphere.description": "选择球体", "worldedit.select.cyl.message": "圆柱选择器：左键=中心，右键扩展", "worldedit.select.cyl.description": "选择圆柱", "worldedit.select.convex.message": "凸多面体选择器：左键=第一个顶点，右键添加更多。", "worldedit.select.convex.limit-message": "最多 {0} 个点。", "worldedit.select.convex.description": "选择凸多面体", "worldedit.select.default-set": "您的默认区域选择器现在是 {0}。", "worldedit.chunkinfo.chunk": "区块：{0}, {1}", "worldedit.chunkinfo.old-filename": "旧格式：{0}", "worldedit.chunkinfo.mcregion-filename": "McRegion：region/{0}", "worldedit.listchunks.listfor": "列出区块：{0}", "worldedit.drain.drained": "{0} 个方块已排水。", "worldedit.fill.created": "{0} 个方块已填充。", "worldedit.fillr.created": "{0} 个方块已填充。", "worldedit.fixlava.fixed": "{0} 个方块已修复。", "worldedit.fixwater.fixed": "{0} 个方块已修复。", "worldedit.removeabove.removed": "{0} 个方块已移除。", "worldedit.removebelow.removed": "{0} 个方块已移除。", "worldedit.removenear.removed": "{0} 个方块已移除。", "worldedit.replacenear.replaced": "{0} 个方块已替换。", "worldedit.snow.created": "{0} 个表面已覆盖。", "worldedit.thaw.removed": "{0} 个方块已融化。", "worldedit.green.changed": "{0} 个方块已绿化。", "worldedit.extinguish.removed": "{0} 个火焰已熄灭。", "worldedit.butcher.killed": "在半径 {1} 内杀死了 {0} 个生物。", "worldedit.butcher.explain-all": "使用 -1 移除已加载区块中的所有生物", "worldedit.remove.removed": "{0} 个实体已标记为移除。", "worldedit.remove.explain-all": "使用 -1 移除已加载区块中的所有实体", "worldedit.calc.invalid": "'{0}' 无法解析为有效表达式", "worldedit.calc.invalid.with-error": "'{0}' 无法解析为有效表达式：'{1}'", "worldedit.paste.pasted": "剪贴板已粘贴到 {0}", "worldedit.paste.selected": "已选择剪贴板粘贴区域。", "worldedit.rotate.no-interpolation": "注意：尚不支持插值，因此建议使用 90 的倍数角度。", "worldedit.rotate.rotated": "剪贴板副本已旋转。", "worldedit.flip.flipped": "剪贴板副本已翻转。", "worldedit.clearclipboard.cleared": "剪贴板已清除。", "worldedit.set.done": "操作完成（{0}）。", "worldedit.set.done.verbose": "操作完成（{0}）。", "worldedit.line.changed": "{0} 个方块已更改。", "worldedit.line.invalid-type": "//line 仅适用于立方体选择或凸多面体选择", "worldedit.line.cuboid-only": "//line 仅适用于立方体选择", "worldedit.curve.changed": "{0} 个方块已更改。", "worldedit.curve.invalid-type": "//curve 仅适用于凸多面体选择", "worldedit.curve.convex-only": "//curve 仅适用于凸多面体选择", "worldedit.replace.replaced": "{0} 个方块已替换。", "worldedit.stack.changed": "{0} 个方块已更改。使用 //undo 撤销", "worldedit.stack.intersecting-region": "使用方块单位时，堆叠偏移不得与区域碰撞", "worldedit.regen.regenerated": "区域已重新生成。", "worldedit.regen.failed": "无法重新生成区块。详情请查看控制台。", "worldedit.walls.changed": "{0} 个方块已更改。", "worldedit.faces.changed": "{0} 个方块已更改。", "worldedit.overlay.overlaid": "{0} 个方块已覆盖。", "worldedit.naturalize.naturalized": "{0} 个方块已自然化。", "worldedit.center.changed": "中心已设置。（{0} 个方块已更改）", "worldedit.smooth.changed": "地形高度图已平滑。{0} 个方块已更改。", "worldedit.snowsmooth.changed": "雪的高度图已平滑。{0} 个方块已更改。", "worldedit.move.moved": "{0} 个方块已移动。", "worldedit.deform.deformed": "{0} 个方块已变形。", "worldedit.hollow.changed": "{0} 个方块已更改。", "worldedit.forest.created": "创建了 {0} 棵树。", "worldedit.flora.created": "创建了 {0} 个植物。", "worldedit.unstuck.moved": "好了！", "worldedit.ascend.obstructed": "在您上方未找到空闲位置。", "worldedit.ascend.moved": "上升了 {0} 层。", "worldedit.descend.obstructed": "在您下方未找到空闲位置。", "worldedit.descend.moved": "下降了 {0} 层。", "worldedit.ceil.obstructed": "在您上方未找到空闲位置。", "worldedit.ceil.moved": "嗖！", "worldedit.thru.obstructed": "在您前方未找到空闲位置。", "worldedit.thru.moved": "嗖！", "worldedit.jumpto.moved": "噗！", "worldedit.jumpto.none": "视线中没有方块（或太远）！", "worldedit.up.obstructed": "您会撞到上方的东西。", "worldedit.up.moved": "嗖！", "worldedit.cone.invalid-radius": "您必须指定 1 或 2 个半径值。", "worldedit.cone.created": "已创建 {0} 个方块。", "worldedit.cyl.invalid-radius": "您必须指定 1 或 2 个半径值。", "worldedit.cyl.created": "已创建 {0} 个方块。", "worldedit.hcyl.thickness-too-large": "厚度不能大于 x 或 z 半径。", "worldedit.sphere.invalid-radius": "您必须指定 1 或 3 个半径值。", "worldedit.sphere.created": "已创建 {0} 个方块。", "worldedit.forestgen.created": "创建了 {0} 棵树。", "worldedit.pumpkins.created": "创建了 {0} 个南瓜田。", "worldedit.pyramid.created": "已创建 {0} 个方块。", "worldedit.generate.created": "已创建 {0} 个方块。", "worldedit.generatebiome.changed": "影响了 {0} 个生物群系。", "worldedit.reload.config": "配置已重新加载！", "worldedit.report.written": "FAWE 报告已写入 {0}", "worldedit.report.error": "写入报告失败：{0}", "worldedit.report.callback": "FAWE 报告：{0}.report", "worldedit.timezone.invalid": "无效时区", "worldedit.timezone.set": "此会话的时区设置为：{0}", "worldedit.timezone.current": "该时区的当前时间是：{0}", "worldedit.version.version": "FAWE 版本：\n - 日期 {0}\n - 提交 {1}\n - 构建 {2}\n - 平台 {3}", "worldedit.trace.no-tracing-extents": "跟踪：未使用范围。", "worldedit.trace.action-failed": "跟踪：在 {1} 的操作 {0} 被范围 {2} 丢弃", "worldedit.trace.active.already": "跟踪模式已经激活。", "worldedit.trace.inactive.already": "跟踪模式已经不活跃。", "worldedit.trace.active": "跟踪模式现在激活。", "worldedit.trace.inactive": "跟踪模式现在不活跃。", "worldedit.command.time-elapsed": "耗时 {0}秒（历史：{1} 已更改；{2} 方块/秒）。", "worldedit.command.permissions": "您没有权限执行此操作。您是否在正确的模式下？", "worldedit.command.player-only": "此命令必须由玩家使用。", "worldedit.command.error.report": "&c请报告此错误：[查看控制台]", "worldedit.command.deprecation": "此命令已弃用。", "worldedit.command.deprecation-message": "请改用 '{0}'。", "worldedit.pastebin.uploading": "（请稍候...正在发送输出到粘贴服务...）", "worldedit.session.cant-find-session": "无法找到 {0} 的会话", "worldedit.platform.no-file-dialog": "您的环境不支持文件对话框。", "worldedit.tool.max-block-changes": "已达到最大方块更改限制。", "worldedit.tool.no-block": "视线中没有方块！", "worldedit.tool.repl.equip": "方块替换工具绑定到 {0}。", "worldedit.tool.repl.switched": "替换工具切换到：{0}", "worldedit.tool.data-cycler.equip": "方块数据循环工具绑定到 {0}。", "worldedit.tool.data-cycler.block-not-permitted": "您没有权限循环该方块的数据值。", "worldedit.tool.data-cycler.cant-cycle": "该方块的数据无法循环！", "worldedit.tool.data-cycler.new-value": "{0} 的值现在是 {1}。", "worldedit.tool.data-cycler.cycling": "现在循环 {0}。", "worldedit.tool.deltree.equip": "浮动树移除工具绑定到 {0}。", "worldedit.tool.deltree.not-tree": "那不是树。", "worldedit.tool.deltree.not-floating": "那不是浮动树。", "worldedit.tool.tree.equip": "树工具绑定到 {0}。", "worldedit.tool.tree.obstructed": "树无法放在那里。", "worldedit.tool.info.equip": "信息工具绑定到 {0}。", "worldedit.tool.inspect.equip": "检查工具绑定到 {0}。", "worldedit.tool.info.blockstate.hover": "方块状态", "worldedit.tool.info.internalid.hover": "内部 ID", "worldedit.tool.info.legacy.hover": "旧版 id:data", "worldedit.tool.info.light.hover": "方块光照/上方光照", "worldedit.tool.none.equip": "工具已从您当前物品解绑。", "worldedit.tool.selwand.equip": "选择魔法棒绑定到 {0}。", "worldedit.tool.navwand.equip": "导航魔法棒绑定到 {0}。", "worldedit.tool.floodfill.equip": "方块洪水填充工具绑定到 {0}。", "worldedit.tool.farwand.equip": "远程魔法棒工具绑定到 {0}。", "worldedit.tool.lrbuild.equip": "远程建造工具绑定到 {0}。", "worldedit.tool.lrbuild.set": "左键设置为 {0}；右键设置为 {1}。", "worldedit.tool.stack.equip": "堆叠工具绑定到 {0}。", "worldedit.tool.unbind-instruction": "拿着物品运行 {0} 来解绑它。", "worldedit.tool.superpickaxe.mode.single": "模式现在是单个。用镐左键点击。// 禁用。", "worldedit.tool.superpickaxe.mode.area": "模式现在是区域。用镐左键点击。// 禁用。", "worldedit.tool.superpickaxe.mode.recursive": "模式现在是递归。用镐左键点击。// 禁用。", "worldedit.tool.superpickaxe.max-range": "最大范围是 {0}。", "worldedit.tool.superpickaxe.enabled.already": "超级镐已经启用。", "worldedit.tool.superpickaxe.disabled.already": "超级镐已经禁用。", "worldedit.tool.superpickaxe.enabled": "超级镐已启用。", "worldedit.tool.superpickaxe.disabled": "超级镐已禁用。", "worldedit.tool.mask.set": "笔刷掩码已设置。", "worldedit.tool.mask.disabled": "笔刷掩码已禁用。", "worldedit.tool.material.set": "笔刷材料已设置。", "worldedit.tool.range.set": "笔刷范围已设置。", "worldedit.tool.size.set": "笔刷大小已设置。", "worldedit.tool.tracemask.set": "跟踪掩码已设置。", "worldedit.tool.tracemask.disabled": "跟踪掩码已禁用。", "worldedit.execute.script-permissions": "您没有权限使用该脚本。", "worldedit.executelast.no-script": "请先使用 /cs 和脚本名称。", "worldedit.script.read-error": "脚本读取错误：{0}", "worldedit.script.unsupported": "目前仅支持 .js 脚本", "worldedit.script.file-not-found": "脚本不存在：{0}", "worldedit.script.no-script-engine": "找不到已安装的脚本引擎。\n请参阅 https://worldedit.enginehub.org/en/latest/usage/other/craftscripts/", "worldedit.script.failed": "执行失败：{0}", "worldedit.script.failed-console": "执行失败（查看控制台）：{0}", "worldedit.operation.affected.biome": "影响了 {0} 个生物群系", "worldedit.operation.affected.block": "影响了 {0} 个方块", "worldedit.operation.affected.column": "影响了 {0} 列", "worldedit.operation.affected.entity": "影响了 {0} 个实体", "worldedit.operation.deform.expression": "使用 {0} 变形", "worldedit.error.parser.invalid-nbt": "输入中的无效 NBT 数据：'{0}'。错误：{1}", "worldedit.selection.convex.info.vertices": "顶点：{0}", "worldedit.selection.convex.info.triangles": "三角形：{0}", "worldedit.selection.convex.explain.primary": "开始新选择，顶点 {0}。", "worldedit.selection.convex.explain.secondary": "将顶点 {0} 添加到选择。", "worldedit.selection.cuboid.info.pos1": "位置 1：{0}", "worldedit.selection.cuboid.info.pos2": "位置 2：{0}", "worldedit.selection.cuboid.explain.primary": "第一个位置设置为 {0}。", "worldedit.selection.cuboid.explain.primary-area": "第一个位置设置为 {0}（{1}）。", "worldedit.selection.cuboid.explain.secondary": "第二个位置设置为 {0}。", "worldedit.selection.cuboid.explain.secondary-area": "第二个位置设置为 {0}（{1}）。", "worldedit.selection.extend.explain.primary": "在 {0}（{1}）开始选择。", "worldedit.selection.extend.explain.secondary": "扩展选择以包含 {0}（{1}）。", "worldedit.selection.ellipsoid.info.center": "中心：{0}", "worldedit.selection.ellipsoid.info.radius": "X/Y/Z 半径：{0}", "worldedit.selection.ellipsoid.explain.primary": "中心位置设置为 {0}。", "worldedit.selection.ellipsoid.explain.primary-area": "中心位置设置为 {0}（{1}）。", "worldedit.selection.ellipsoid.explain.secondary": "半径设置为 {0}。", "worldedit.selection.ellipsoid.explain.secondary-area": "半径设置为 {0}（{1}）。", "worldedit.selection.cylinder.info.center": "中心：{0}", "worldedit.selection.cylinder.info.radius": "半径：{0}", "worldedit.selection.cylinder.explain.primary": "在 {0} 开始新的圆柱选择。", "worldedit.selection.cylinder.explain.secondary": "半径设置为 {0}/{1} 方块。（{2}）", "worldedit.selection.cylinder.explain.secondary-missing": "您必须在设置半径之前选择中心点。", "worldedit.selection.polygon2d.info": "点数：{0}", "worldedit.selection.polygon2d.explain.primary": "在 {0} 开始新多边形。", "worldedit.selection.polygon2d.explain.secondary": "在 {1} 添加点 #{0}。", "worldedit.selection.sphere.explain.secondary": "半径设置为 {0}。", "worldedit.selection.sphere.explain.secondary-defined": "半径设置为 {0}（{1}）。", "worldedit.sideeffect.history": "历史", "worldedit.sideeffect.history.description": "写入更改历史", "worldedit.sideeffect.heightmaps": "高度图", "worldedit.sideeffect.heightmaps.description": "更新高度图", "worldedit.sideeffect.lighting": "光照", "worldedit.sideeffect.lighting.description": "更新方块光照", "worldedit.sideeffect.neighbors": "邻居", "worldedit.sideeffect.neighbors.description": "更新编辑中方块的形状", "worldedit.sideeffect.update": "更新", "worldedit.sideeffect.update.description": "通知已更改的方块", "worldedit.sideeffect.validation": "验证", "worldedit.sideeffect.validation.description": "验证并修复不一致的世界状态，如断开的方块", "worldedit.sideeffect.entity_ai": "实体 AI", "worldedit.sideeffect.entity_ai.description": "为方块更改更新实体 AI 路径", "worldedit.sideeffect.events": "模组/插件事件", "worldedit.sideeffect.events.description": "在适用时告知其他模组/插件这些更改", "worldedit.sideeffect.state.on": "开启", "worldedit.sideeffect.state.delayed": "延迟", "worldedit.sideeffect.state.off": "关闭", "worldedit.sideeffect.box.current": "当前", "worldedit.sideeffect.box.change-to": "点击设置为 {0}", "worldedit.help.command-not-found": "找不到命令 '{0}'。", "worldedit.help.no-subcommands": "'{0}' 没有子命令。（也许 '{1}' 是参数？）", "worldedit.help.subcommand-not-found": "找不到 '{1}' 下的子命令 '{0}'。", "worldedit.cli.stopping": "正在停止！", "worldedit.cli.unknown-command": "未知命令！", "worldedit.version.bukkit.unsupported-adapter": "此 FastAsyncWorldEdit 版本不完全支持您的 Bukkit 版本。方块实体（如箱子）将为空，方块属性（如旋转）将丢失，其他功能可能无法工作。更新 FastAsyncWorldEdit 以恢复此功能：\n{0}", "worldedit.bukkit.no-edit-without-adapter": "在不受支持的版本上编辑已禁用。"}