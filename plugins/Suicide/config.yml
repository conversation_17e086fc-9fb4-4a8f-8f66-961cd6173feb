# /////////////////////////////////////////////////////////////
# /                                                           /
# /                      自杀插件                              /
# /                                                           /
# /////////////////////////////////////////////////////////////
#
# 感谢您使用自杀插件！如果您遇到任何问题
# 或有任何建议/功能希望在插件中实现
# 欢迎加入支持 Discord 服务器！
#
# Spigot: Prism3
# Discord: .prism3 | ID: 403667971089760257
# Discord Server: https://discord.gg/MfR5mcpVfX


# 执行命令时是否向玩家发送消息？
Message: true

# 是否向服务器广播自杀消息？
Broadcast: true

# 是否在玩家死亡时禁用烟花？
# 类型可以是以下之一 [ BALL_LARGE, BALL, STAR, BURST, CREEPER ]
# 爆炸的颜色（RGB）
# 爆炸的渐变颜色（RGB）
# 烟花是否有轨迹
# 烟花是否有闪烁效果
# 爆炸威力，设置过高可能导致FPS下降
Firework:
  Enabled: true
  Type: BALL_LARGE
  Color:
    RED: 0
    GREEN: 255
    BLUE: 0
  Fade:
    RED: 255
    GREEN: 0
    BLUE: 0
  Trail: true
  Flicker: true
  Power: 2

# 是否禁用死亡位置坐标？
Coords: true

# 是否在执行命令时禁用声音？
# 玩家死亡时播放的声音。
# 声音列表：https://www.spigotmc.org/wiki/cc-sounds-list/。使用 1.9 或更高版本的声音！
# 对于 1.12- 服务器使用：MOB_ZOMBIE_HURT
# 对于 1.13+ 服务器使用：entity.zombie.hurt
# 音量和音调可以在 0 -> 100 之间变化。
Sound:
  Enabled: true
  Sound: 'entity.zombie.hurt'
  Volume: 100
  Pitch: 50

# 是否禁用自杀冷却时间？
# 计时器（秒）
Cooldown:
  Enabled: true
  Timer: 15

# 在指定世界中禁用自杀命令
Disabled-Worlds:
  - 'world_name'

# 命令别名
Aliases:
  - meow

Messages:
  No-Permission: "&c您没有权限执行此命令。"
  Reload: "&a配置已重新加载。"
  Invalid-Syntax: "&c无效语法！"
  Disabled: "&c在此世界中自杀功能已禁用。"
  On-Suicide: "&c&l您自杀了！"
  On-Cooldown: "&6您必须等待 %time% 秒才能执行此命令！"
  Broadcast:
    Random: true
    Messages:
    - "&c&l%player% 自杀了！"
    - "&c&l%player% 被击倒了。"
    - "&c&l%player% 没了。"
