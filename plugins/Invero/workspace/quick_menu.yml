title: '&e&l快速功能'

layout: |-
  #########
  |A B C D|
  |E F G H|
  |I J K L|
  #########

events:
  post_open: "sound BLOCK_NOTE_BLOCK_PLING by 1 2"
  close: "sound BLOCK_CHEST_CLOSE by 1 2"

bindings:
  item: "minecraft:compass"
  command:
    name: 'quick'
    permission: 'invero.use'
    usage: '/quick - 打开快速菜单'

icons:
  '#':
    material: cyan stained glass pane
    name: '&8...'

  # 回到出生点
  'A':
    material: bed
    name: '&a&l出生点'
    lore: |-
      &r
      &7快速回到出生点
      &8命令: /spawn
      &r
      &a→ 点击传送
    action: |-
      command spawn
      menu close

  # 设置家园
  'B':
    material: oak_door
    name: '&e&l设置家园'
    lore: |-
      &r
      &7在当前位置设置家园
      &8命令: /sethome
      &r
      &e→ 点击设置
    action: |-
      command sethome
      msg '&a家园设置成功!'
      menu close

  # 传送到家园
  'C':
    material: emerald
    name: '&b&l回家'
    lore: |-
      &r
      &7传送到默认家园
      &8命令: /home
      &r
      &b→ 点击传送
    action: |-
      command home
      menu close

  # 查看家园列表
  'D':
    material: ender_pearl
    name: '&d&l家园列表'
    lore: |-
      &r
      &7查看所有家园
      &8命令: /homes
      &r
      &d→ 点击查看
    action: |-
      command homes
      menu close

  # AFK状态
  'E':
    material: clock
    name: '&6&lAFK状态'
    lore: |-
      &r
      &7切换AFK状态
      &8命令: /afk
      &8当前: %AFKPlus_Status%
      &r
      &6→ 点击切换
    action: |-
      command afk
      menu close

  # 皮肤设置
  'F':
    material: leather_helmet
    name: '&d&l皮肤设置'
    lore: |-
      &r
      &7更换您的皮肤
      &8命令: /skin set <玩家名>
      &r
      &d→ 点击设置
    action: |-
      msg '&a皮肤设置命令:'
      msg '&e/skin set <玩家名> - 设置皮肤'
      msg '&e/skin clear - 清除皮肤'
      msg '&e/skin update - 更新皮肤'
      msg '&7请在聊天框中输入命令'

  # 称号管理
  'G':
    material: name_tag
    name: '&6&l称号管理'
    lore: |-
      &r
      &7管理您的称号
      &8命令: /playertitle gui
      &8当前: %playertitle_use%
      &r
      &6→ 点击打开
    action: |-
      command playertitle gui
      menu close

  # 查看坐标
  'H':
    material: compass
    name: '&3&l当前坐标'
    lore: |-
      &r
      &7查看当前位置
      &8坐标: %player_x%, %player_y%, %player_z%
      &8世界: %multiverse_world_alias%
      &r
      &3→ 点击查看
    action: |-
      msg '&a当前坐标: &e%player_x%, %player_y%, %player_z%'
      msg '&a世界: &e%multiverse_world_alias%'
      msg '&a生物群系: &e%player_biome%'

  # 经济信息
  'I':
    material: emerald
    name: '&a&l经济信息'
    lore: |-
      &r
      &7查看经济相关信息
      &8余额: &6%xconomy_balance%
      &8命令: /balance, /pay
      &r
      &a→ 点击查看
    action: |-
      msg '&a经济信息:'
      msg '&e当前余额: &6%xconomy_balance%'
      msg '&e转账命令: &f/pay <玩家> <金额>'
      msg '&e富豪榜: &f/baltop'
      msg '&r'
      msg '&6💡 保护财产: 在箱子旁放告示牌自动上锁!'

  # 自杀功能
  'J':
    material: skeleton_skull
    name: '&c&l自杀'
    lore: |-
      &r
      &7立即结束生命
      &8命令: /suicide
      &c注意: 会掉落物品!
      &r
      &4→ 点击确认
    action: |-
      command suicide
      menu close

  # 方块查询
  'K':
    material: stick
    name: '&6&l方块查询'
    lore: |-
      &r
      &7查询方块信息
      &8命令: /co i
      &8右键方块查看记录
      &r
      &6→ 点击获取查询工具
    action: |-
      command co i
      msg '&a已开启方块查询模式!'
      msg '&e右键点击方块查看记录'
      msg '&e再次输入 &f/co i &e关闭查询'
      menu close

  # 返回主菜单
  'L':
    material: arrow
    name: '&8&l返回主菜单'
    lore: |-
      &r
      &7返回到主菜单
      &r
      &8→ 点击返回
    action: |-
      menu close
      sleep 0.2s
      menu open main_menu
