title: '&b&l服务器主菜单'

layout: |-
  #########
  |   A   |
  | B C D |
  |   E   |
  #########

events:
  pre_open:
    if_not: perm invero.use
    then: [
      "msg '&c您没有权限使用此菜单'",
      false
    ]
  post_open: "sound BLOCK_CHEST_OPEN by 1 2"
  close: "sound BLOCK_CHEST_CLOSE by 1 2"

bindings:
  command:
    name: 'menu'
    permission: 'invero.use'
    usage: '/menu - 打开主菜单'

icons:
  '#':
    material: black stained glass pane
    name: '&8...'

  # 快速功能
  'A':
    material: nether_star
    name: '&e&l快速功能'
    lore: |-
      &r
      &7常用功能快速访问
      &8• 传送功能 (/spawn, /home)
      &8• 玩家工具 (/afk, /skin)
      &8• 实用命令
      &r
      &e→ 点击打开
    action: |-
      menu close
      sleep 0.2s
      menu open quick_menu

  # 新手指南
  'B':
    material: enchanted_book
    name: '&a&l新手指南'
    lore: |-
      &r
      &7新玩家必看指南
      &8• 基础命令教学
      &8• 系统功能介绍
      &8• 服务器规则
      &r
      &a→ 点击查看
    action: |-
      menu close
      sleep 0.2s
      menu open newbie_guide

  # 服务器信息
  'C':
    material: beacon
    name: '&6&l服务器信息'
    lore: |-
      &r
      &7查看服务器状态
      &8TPS: &f%server_tps%
      &8在线: &f%server_online%/%server_max_players%
      &8您的延迟: &f%player_ping%ms
      &r
      &6→ 点击查看详情
    action: |-
      msg '&a服务器信息:'
      msg '&eTPS: &f%server_tps%'
      msg '&e在线玩家: &f%server_online%/%server_max_players%'
      msg '&e服务器版本: &f%server_version%'
      msg '&e您的延迟: &f%player_ping%ms'
      msg '&r'
      msg '&6💡 小贴士: 在箱子旁放告示牌可自动上锁保护物品!'

  # 玩家信息
  'D':
    material: player_head
    head: '%player_name%'
    name: '&b&l我的信息'
    lore: |-
      &r
      &7玩家: &e%player_name%
      &7金币: &6%xconomy_balance%
      &7称号: &d%playertitle_use%
      &7在线时间: &a%AFKPlus_TotalTimeAFK%
      &r
      &b→ 点击查看详情
    action: |-
      msg '&a玩家信息:'
      msg '&e姓名: &f%player_name%'
      msg '&e金币: &6%xconomy_balance%'
      msg '&e称号: &d%playertitle_use%'
      msg '&e在线时间: &a%AFKPlus_TotalTimeAFK%'

  # 管理员菜单
  'E':
    material: redstone_block
    name: '&c&l管理员菜单'
    lore: |-
      &r
      &c管理员专用功能
      &8• 玩家管理
      &8• 权限管理
      &8• 服务器管理
      &r
      &4→ 点击打开 (需要权限)
    action: |-
      menu close
      sleep 0.2s
      menu open admin_menu
