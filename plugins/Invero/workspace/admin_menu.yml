title: '&c&l管理员工具'

layout: |-
  #########
  |A B C D|
  |E F G H|
  |I J K L|
  #########

events:
  pre_open:
    if_not: perm invero.admin
    then: [
      "msg '<red>您没有权限使用管理员菜单'",
      false
    ]
  post_open: "sound BLOCK_ANVIL_USE by 1 1.5"
  close: "sound BLOCK_CHEST_CLOSE by 1 2"

bindings:
  command:
    name: 'admin'
    permission: 'invero.admin'
    usage: '/admin - 打开管理员菜单'

icons:
  '#':
    material: red stained glass pane
    name: '<dark_gray>...'

  # 玩家管理
  'A':
    material: player_head
    head: '<PERSON><PERSON>_<PERSON>'
    name: '<yellow><bold>玩家管理'
    lore: |-
      &r
      &7管理在线玩家
      &8• /kick <玩家> <原因> - 踢出玩家
      &8• /ban <玩家> <原因> - 封禁玩家
      &8• /tp <玩家1> <玩家2> - 传送玩家
      &8• /gamemode <模式> <玩家> - 切换游戏模式
      &r
      &e→ 点击查看
    action: |-
      msg '<green>玩家管理命令:'
      msg '<yellow>/kick <玩家> <原因> - 踢出玩家'
      msg '<yellow>/ban <玩家> <原因> - 封禁玩家'
      msg '<yellow>/tp <玩家1> <玩家2> - 传送玩家'
      msg '<yellow>/gamemode <模式> <玩家> - 切换游戏模式'

  # 权限管理
  'B':
    material: golden_apple
    name: '<gold><bold>权限管理'
    lore: |-
      &r
      &7LuckPerms权限管理
      &8• 用户权限
      &8• 权限组管理
      &r
      &6→ 点击查看
    action: |-
      msg '<green>权限管理命令:'
      msg '<yellow>/lp user <玩家> permission set <权限> true'
      msg '<yellow>/lp group <组名> permission set <权限> true'
      msg '<yellow>/lp user <玩家> parent add <组名>'

  # 世界管理
  'C':
    material: grass_block
    name: '<green><bold>世界管理'
    lore: |-
      &r
      &7Multiverse世界管理
      &8• 创建世界
      &8• 删除世界
      &8• 世界设置
      &r
      &a→ 点击查看
    action: |-
      msg '<green>世界管理命令:'
      msg '<yellow>/mv create <世界名> <类型> - 创建世界'
      msg '<yellow>/mv delete <世界名> - 删除世界'
      msg '<yellow>/mv list - 查看所有世界'

  # 经济管理
  'D':
    material: emerald
    name: '<aqua><bold>经济管理'
    lore: |-
      &r
      &7经济管理
      &8• 给予金币
      &8• 扣除金币
      &8• 查看余额
      &r
      &b→ 点击查看
    action: |-
      msg '<green>经济管理命令:'
      msg '<yellow>/xconomy give <玩家> <金额> - 给予金币'
      msg '<yellow>/xconomy take <玩家> <金额> - 扣除金币'
      msg '<yellow>/xconomy balance <玩家> - 查看余额'

  # 隐身管理
  'E':
    material: potion
    name: '&d<bold>隐身管理'
    lore: |-
      &r
      &7SuperVanish隐身管理
      &8• 开启隐身
      &8• 关闭隐身
      &8• 查看隐身列表
      &r
      &d→ 点击查看
    action: |-
      msg '<green>隐身管理命令:'
      msg '<yellow>/sv on - 开启隐身'
      msg '<yellow>/sv off - 关闭隐身'
      msg '<yellow>/sv list - 查看隐身玩家'

  # 假人管理
  'F':
    material: armor_stand
    name: '<cyan><bold>假人管理'
    lore: |-
      &r
      &7FakePlayer假人管理
      &8• 创建假人
      &8• 删除假人
      &8• 假人列表
      &r
      &3→ 点击查看
    action: |-
      msg '<green>假人管理命令:'
      msg '<yellow>/fakeplayer add <名称> - 创建假人'
      msg '<yellow>/fakeplayer remove <名称> - 删除假人'
      msg '<yellow>/fakeplayer list - 查看假人列表'

  # 全息图管理
  'G':
    material: end_crystal
    name: '<white><bold>全息图管理'
    lore: |-
      &r
      &7DecentHolograms全息图管理
      &8• 创建全息图
      &8• 编辑全息图
      &8• 删除全息图
      &r
      &f→ 点击查看
    action: |-
      msg '<green>全息图管理命令:'
      msg '<yellow>/dh create <名称> - 创建全息图'
      msg '<yellow>/dh edit <名称> - 编辑全息图'
      msg '<yellow>/dh delete <名称> - 删除全息图'

  # 服务器管理
  'H':
    material: command_block
    name: '<red><bold>服务器管理'
    lore: |-
      &r
      &7服务器基础管理
      &8• 重载插件
      &8• 停止服务器
      &8• 查看性能
      &r
      &c→ 点击查看
    action: |-
      msg '<green>服务器管理命令:'
      msg '<yellow>/reload - 重载服务器'
      msg '<yellow>/stop - 停止服务器'
      msg '<yellow>/spark profiler - 性能分析'

  # 世界编辑
  'I':
    material: golden_axe
    name: '<gold><bold>世界编辑'
    lore: |-
      &r
      &7WorldEdit管理工具
      &8• //wand - 获取选择工具
      &8• //set <方块> - 填充选区
      &8• //copy - 复制选区
      &8• //paste - 粘贴选区
      &r
      &6→ 点击查看
    action: |-
      msg '<green>WorldEdit命令:'
      msg '<yellow>//wand - 获取选择工具'
      msg '<yellow>//set <方块> - 填充选区'
      msg '<yellow>//copy - 复制选区'
      msg '<yellow>//paste - 粘贴选区'
      msg '<yellow>//undo - 撤销操作'

  # 聊天管理
  'J':
    material: writable_book
    name: '<orange><bold>聊天管理'
    lore: |-
      &r
      &7TrChat聊天管理
      &8• 频道管理
      &8• 消息过滤
      &r
      &6→ 点击查看
    action: |-
      msg '<green>聊天管理命令:'
      msg '<yellow>/trchat reload - 重载聊天'
      msg '<yellow>/trchat mute <玩家> - 禁言玩家'

  # 称号管理
  'K':
    material: name_tag
    name: '<light_purple><bold>称号管理'
    lore: |-
      &r
      &7PlayerTitle称号管理
      &8• 给予称号
      &8• 删除称号
      &r
      &5→ 点击查看
    action: |-
      msg '<green>称号管理命令:'
      msg '<yellow>/playertitle give <玩家> <称号> - 给予称号'
      msg '<yellow>/playertitle remove <玩家> <称号> - 删除称号'

  # 返回主菜单
  'L':
    material: arrow
    name: '<gray><bold>返回主菜单'
    lore: |-
      &r
      &7返回到主菜单
      &r
      &8→ 点击返回
    action: |-
      menu close
      sleep 0.2s
      menu open main_menu
