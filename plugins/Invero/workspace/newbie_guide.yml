title: '&a&l新手指南'

layout: |-
  #########
  |A B C D|
  |E F G H|
  |   I   |
  #########

events:
  post_open: "sound ENTITY_EXPERIENCE_ORB_PICKUP by 1 1.5"
  close: "sound BLOCK_CHEST_CLOSE by 1 2"

bindings:
  command:
    name: 'guide'
    permission: 'invero.use'
    usage: '/guide - 打开新手指南'

icons:
  '#':
    material: lime stained glass pane
    name: '<dark_gray>...'

  # 基础命令
  'A':
    material: command_block
    name: '<yellow><bold>基础命令'
    lore: |-
      &r
      &7学习基础游戏命令
      &8• /spawn - 回到出生点
      &8• /menu - 打开主菜单
      &8• /afk - 切换AFK状态
      &r
      &e→ 点击查看更多
    action: |-
      msg '<green>基础命令指南:'
      msg '<yellow>/spawn - 回到服务器出生点'
      msg '<yellow>/menu - 打开主菜单'
      msg '<yellow>/afk - 切换AFK(离开)状态'
      msg '<yellow>/suicide - 自杀(会掉落物品)'
      msg '<yellow>/tools - 打开工具箱'

  # 传送系统
  'B':
    material: ender_pearl
    name: '<aqua><bold>传送系统'
    lore: |-
      &r
      &7了解传送功能
      &8• 设置家园
      &8• 传送到家园
      &8• 回到出生点
      &r
      &b→ 点击查看详情
    action: |-
      msg '<green>传送系统指南:'
      msg '<yellow>/sethome <名称> - 设置家园'
      msg '<yellow>/home <名称> - 传送到家园'
      msg '<yellow>/homes - 查看所有家园'
      msg '<yellow>/delhome <名称> - 删除家园'
      msg '<yellow>/spawn - 回到出生点'

  # 聊天系统
  'C':
    material: writable_book
    name: '<green><bold>聊天系统'
    lore: |-
      &r
      &7学习聊天功能
      &8• 公共聊天
      &8• 私人消息
      &8• 聊天频道
      &r
      &a→ 点击查看详情
    action: |-
      msg '<green>聊天系统指南:'
      msg '<yellow>直接输入消息 - 公共聊天'
      msg '<yellow>/msg <玩家> <消息> - 私聊'
      msg '<yellow>/r <消息> - 回复私聊'
      msg '<yellow>/ch <频道> - 切换聊天频道'

  # 经济系统
  'D':
    material: emerald
    name: '<dark_green><bold>经济系统'
    lore: |-
      &r
      &7了解服务器经济
      &8• 查看余额
      &8• 转账功能
      &8• 赚钱方法
      &r
      &2→ 点击查看详情
    action: |-
      msg '<green>经济系统指南:'
      msg '<yellow>/balance - 查看余额'
      msg '<yellow>/pay <玩家> <金额> - 转账'
      msg '<yellow>/baltop - 查看富豪榜'
      msg '<yellow>通过挖矿、建造等活动赚取金币'

  # 皮肤系统
  'E':
    material: leather_helmet
    name: '<light_purple><bold>皮肤系统'
    lore: |-
      &r
      &7自定义角色外观
      &8• 设置皮肤
      &8• 清除皮肤
      &r
      &d→ 点击查看详情
    action: |-
      msg '<green>皮肤系统指南:'
      msg '<yellow>/skin set <玩家名> - 设置皮肤'
      msg '<yellow>/skin clear - 清除皮肤'
      msg '<yellow>输入任何正版玩家名来使用其皮肤'

  # 称号系统
  'F':
    material: name_tag
    name: '<gold><bold>称号系统'
    lore: |-
      &r
      &7个性化称号展示
      &8• 查看可用称号
      &8• 设置称号
      &r
      &6→ 点击查看详情
    action: |-
      msg '<green>称号系统指南:'
      msg '<yellow>/playertitle gui - 打开称号界面'
      msg '<yellow>通过完成任务或活动获得称号'
      msg '<yellow>称号会显示在您的名字前面'

  # 箱子上锁
  'G':
    material: chest
    name: '&6&l箱子上锁'
    lore: |-
      &r
      &7保护您的物品安全
      &8• 在箱子旁边放告示牌
      &8• 系统自动添加 [Private]
      &8• 自动显示您的名字
      &8• 第三四行可添加朋友
      &r
      &6→ 点击查看详情
    action: |-
      msg '&a箱子上锁教程:'
      msg '&e1. 在箱子旁边的方块上放置告示牌'
      msg '&e2. BlockLocker会自动在第一行添加[Private]标签'
      msg '&e3. 第二行会自动添加您的名字'
      msg '&e4. 第三四行可以添加朋友的名字'
      msg '&e5. 右键告示牌输入 /bl <行号> <文本> 编辑'
      msg '&e6. 特殊标签: [Everyone] [Redstone] [More Users]'

  # 服务器规则
  'H':
    material: book
    name: '<blue><bold>服务器规则'
    lore: |-
      &r
      &7重要的服务器规则
      &8• 禁止作弊
      &8• 禁止恶意破坏
      &8• 尊重其他玩家
      &r
      &9→ 点击查看详情
    action: |-
      msg '<green>服务器规则:'
      msg '<red>1. 禁止使用任何形式的作弊工具'
      msg '<red>2. 禁止恶意破坏他人建筑'
      msg '<red>3. 禁止在聊天中使用不当言论'
      msg '<red>4. 尊重其他玩家，友好相处'
      msg '<yellow>违反规则将面临处罚'

  # 返回主菜单
  'I':
    material: arrow
    name: '<gray><bold>返回主菜单'
    lore: |-
      &r
      &7返回到主菜单
      &r
      &8→ 点击返回
    action: |-
      menu close
      sleep 0.2s
      menu open main_menu
