# 服务器菜单系统

本目录包含了服务器的所有自定义菜单文件。

## 菜单架构

### 🏠 主菜单 (`main_menu.yml`)
- **命令**: `/menu`
- **权限**: `invero.use`
- **功能**: 导航中心，简洁的5按钮布局
- **包含**: 快速功能入口、新手指南、服务器信息、玩家信息、管理员菜单

### ⚡ 快速菜单 (`quick_menu.yml`)
- **命令**: `/quick`
- **物品绑定**: 指南针
- **权限**: `invero.use`
- **功能**: 所有常用功能集中在一个菜单
- **包含**: 传送、AFK、皮肤、称号、坐标、经济、自杀、时间等12个功能

### ⚙️ 管理员菜单 (`admin_menu.yml`)
- **命令**: `/admin`
- **权限**: `invero.admin`
- **功能**: 管理员专用工具
- **包含**: 玩家管理、权限管理、世界管理、经济管理等

### 📚 新手指南 (`newbie_guide.yml`)
- **命令**: `/guide`
- **权限**: `invero.use`
- **功能**: 新玩家教程和帮助
- **包含**: 基础命令教学、系统功能介绍、服务器规则

## 权限设置

```bash
# 给予普通玩家使用菜单的权限
/lp group default permission set invero.use true

# 给予管理员使用管理菜单的权限
/lp group admin permission set invero.admin true
```

## 重载菜单

修改菜单文件后，使用以下命令重新加载：
```bash
/invero reload
```

## 颜色代码说明

所有菜单使用标准的Minecraft颜色代码格式：
- `&a` = 绿色
- `&b` = 青色
- `&c` = 红色
- `&d` = 淡紫色
- `&e` = 黄色
- `&f` = 白色
- `&l` = 粗体
- `&r` = 重置格式

## 注意事项

1. 所有菜单都包含了具体的命令说明在lore中
2. 管理员功能需要相应的权限才能使用
3. 菜单支持PlaceholderAPI变量显示动态信息
4. 建议定期备份菜单文件
