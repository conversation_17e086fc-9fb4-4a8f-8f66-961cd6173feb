title: '&a&l测试菜单'

layout: |-
  #########
  |   A   |
  | B C D |
  |   E   |
  #########

events:
  post_open: "sound BLOCK_CHEST_OPEN by 1 2"
  close: "sound BLOCK_CHEST_CLOSE by 1 2"

bindings:
  command:
    name: 'test'
    permission: 'invero.use'
    usage: '/test - 打开测试菜单'

icons:
  '#':
    material: green stained glass pane
    name: '&8...'

  # 简单消息测试
  'A':
    material: emerald
    name: '&a&l消息测试'
    lore: |-
      &r
      &7点击发送测试消息
      &r
      &a→ 点击测试
    action: |-
      msg '&a测试消息: 菜单功能正常!'

  # 命令测试
  'B':
    material: command_block
    name: '&e&l命令测试'
    lore: |-
      &r
      &7测试执行命令
      &8命令: /spawn
      &r
      &e→ 点击测试
    action: |-
      command spawn

  # 音效测试
  'C':
    material: note_block
    name: '&b&l音效测试'
    lore: |-
      &r
      &7播放测试音效
      &r
      &b→ 点击测试
    action: |-
      sound ENTITY_EXPERIENCE_ORB_PICKUP by 1 2
      msg '&b音效测试完成!'

  # 菜单跳转测试
  'D':
    material: compass
    name: '&d&l跳转测试'
    lore: |-
      &r
      &7跳转到主菜单
      &r
      &d→ 点击测试
    action: |-
      menu close
      sleep 0.2s
      menu open main_menu

  # 关闭菜单测试
  'E':
    material: barrier
    name: '&c&l关闭测试'
    lore: |-
      &r
      &7关闭菜单
      &r
      &c→ 点击关闭
    action: |-
      msg '&c菜单关闭测试!'
      menu close
