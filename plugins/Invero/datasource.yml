# HikariCP comes with sane defaults that perform well in most deployments without additional tweaking
# https://github.com/brettwooldridge/HikariCP#gear-configuration-knobs-baby
DefaultSettings:
  # Hikari<PERSON> will attempt to resolve a driver through the DriverManager based solely on the jdbcUrl,
  # but for some older drivers the driverClassName must also be specified.
  # Omit this property unless you get an obvious error message indicating that the driver was not found. Default: none
  DriverClassName: 'com.mysql.jdbc.Driver'
  # This property controls the default auto-commit behavior of connections returned from the pool.
  # It is a boolean value. Default: true
  AutoCommit: true
  # This property controls the minimum number of idle connections that Hikari<PERSON> tries to maintain in the pool.
  # If the idle connections dip below this value and total connections in the pool are less than maximumPoolSize,
  # Hikari<PERSON> will make a best effort to add additional connections quickly and efficiently.
  # However, for maximum performance and responsiveness to spike demands, we recommend not setting this value and instead allowing HikariCP to act as a fixed size connection pool.
  # Default: same as maximumPoolSize
  MinimumIdle: 10
  # This property controls the maximum size that the pool is allowed to reach, including both idle and in-use connections.
  # Basically this value will determine the maximum number of actual connections to the database backend.
  # A reasonable value for this is best determined by your execution environment.
  # When the pool reaches this size, and no idle connections are available, calls to getConnection() will block for up to connectionTimeout milliseconds before timing out.
  # Please read about pool sizing. Default: 10
  MaximumPoolSize: 10
  # This property controls the maximum amount of time that a connection will be tested for aliveness.
  # This value must be less than the connectionTimeout. Lowest acceptable validation timeout is 250 ms. Default: 5000
  ValidationTimeout: 5000
  # This property controls the maximum number of milliseconds that a client (that's you) will wait for a connection from the pool.
  # If this time is exceeded without a connection becoming available, a SQLException will be thrown.
  # Lowest acceptable connection timeout is 250 ms. Default: 30000 (30 seconds)
  ConnectionTimeout: 30000
  # This property controls the maximum amount of time that a connection is allowed to sit idle in the pool.
  # This setting only applies when minimumIdle is defined to be less than maximumPoolSize.
  # Idle connections will not be retired once the pool reaches minimumIdle connections.
  # Whether a connection is retired as idle or not is subject to a maximum variation of +30 seconds, and average variation of +15 seconds.
  # A connection will never be retired as idle before this timeout.
  # A value of 0 means that idle connections are never removed from the pool.
  # The minimum allowed value is 10000ms (10 seconds). Default: 600000 (10 minutes)
  IdleTimeout: 600000
  # This property controls the maximum lifetime of a connection in the pool.
  # An in-use connection will never be retired, only when it is closed will it then be removed.
  # On a connection-by-connection basis, minor negative attenuation is applied to avoid mass-extinction in the pool.
  # We strongly recommend setting this value, and it should be several seconds shorter than any database or infrastructure imposed connection time limit.
  # A value of 0 indicates no maximum lifetime (infinite lifetime), subject of course to the idleTimeout setting.
  # The minimum allowed value is 30000ms (30 seconds). Default: 1800000 (30 minutes)
  MaxLifetime: 1800000
  # If your driver supports JDBC4 we strongly recommend not setting this property.
  # This is for "legacy" drivers that do not support the JDBC4 Connection.isValid() API.
  # This is the query that will be executed just before a connection is given to you from the pool to validate that the connection to the database is still alive.
  # Again, try running the pool without this property, HikariCP will log an error if your driver is not JDBC4 compliant to let you know. Default: ~
  ConnectionTestQuery: ~
  # MySQL Configuration
  # In order to get the best performance out of MySQL, these are some of our recommended settings.
  # There are many other performance related settings available in MySQL and we recommend reviewing them all to ensure you are getting the best performance for your application.
  # http://dev.mysql.com/doc/connector-j/en/connector-j-reference-configuration-properties.html
  DataSourceProperty:
    # This sets the number of prepared statements that the MySQL driver will cache per connection.
    # The default is a conservative 25. We recommend setting this to between 250-500.
    prepStmtCacheSize: 250
    # This is the maximum length of a prepared SQL statement that the driver will cache. The MySQL default is 256.
    # In our experience, especially with ORM frameworks like Hibernate, this default is well below the threshold of generated statement lengths.
    # Our recommended setting is 2048.
    prepStmtCacheSqlLimit: 2048
    # Neither of the above parameters have any effect if the cache is in fact disabled, as it is by default.
    # You must set this parameter to true
    cachePrepStmts: true
    # Newer versions of MySQL support server-side prepared statements, this can provide a substantial performance boost.
    # Set this property to true.
    useServerPrepStmts: true