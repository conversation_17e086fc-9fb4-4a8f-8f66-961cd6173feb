# What is this directory?

* In order to perform certain functions, spark sometimes needs to write temporary data to the disk. 
* Previously, a temporary directory provided by the operating system was used for this purpose. 
* However, this proved to be unreliable in some circumstances, so spark now stores temporary data here instead!

spark will automatically cleanup the contents of this directory. 
(but if for some reason it doesn't, if the server is stopped, you can freely delete any files ending in .tmp)

tl;dr: spark uses this folder to store some temporary data.
