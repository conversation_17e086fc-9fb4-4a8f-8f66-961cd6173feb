# gui标题
title: "&f[&e称号奖励&f]"
viewTitle: "&f[&e称号奖励管理&f]"
size: 54
sound: ""

# 奖励设置
reward:
  # 所显示的位置
  index: 20,21,22,23,24,29,30,31,32,33
  # 是否附魔
  isEnchant: true
  sound: ""
  lore:
    - ''
    - '&f[ &c称号奖励说明 &f]'
    - '&8▪ &a需求数量: &f${number}'
    - ''
    - '&f[ &c称号奖励 &f]'
    - ''
    - '&8▪ &a${rewardType}: ${amount}'
    - ''
    - '&f[ &c拥有数量 &f]'
    - '&8▪ &a${myTitleNumber}'
    - ''
  viewLore:
    - ''
    - '&f[ &c称号奖励说明 &f]'
    - '&8▪ &a奖励编号: &f${id}'
    - '&8▪ &a需求数量: &f${number}'
    - ''
    - '&f[ &c称号奖励 &f]'
    - ''
    - '&8▪ &a${rewardType}:${amount}'
    - ''
    - '&8[&a✔&8] &a点击删除'

useButton: "&8[&a✔&8] &a已领取"
useNotButton: "&8[&a✔&8] &a点击领取"
unableButton: "&8[&c✘&8] &7数量不足"

# 上一页
previousPage:
  enable: true
  index: 47
  sound: ""
  name: "    &8[&a上一页&8]"
  lore:
    - '&8▪ &7点击打开上一页'
    - '&8▪ &7当前页码 &a${pageNum}'
    - '&8▪ &7总页码数 &a${count}'
  # 材质
  material: PAPER
  isEnchant: false
  custom-model-data: 0

# 商城
shop:
  enable: true
  index: 48
  sound: ""
  name: "    &8[&a称号商城&8]"
  lore:
    - "&f- &7点击前往称号商城"
  # 材质
  material: ENDER_CHEST
  isEnchant: false
  custom-model-data: 0

# 仓库
open:
  enable: true
  index: 50
  sound: ""
  name: "    &8[&a称号仓库&8]"
  lore:
    - "&f- &7点击前往称号仓库"
  # 材质
  material: CHEST
  isEnchant: false
  custom-model-data: 0

# 返回按钮
back:
  enable: true
  index: 49
  name: "   &8[&a返回&8]"
  material: PLAYER_HEAD
  headBase: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNjllYTFkODYyNDdmNGFmMzUxZWQxODY2YmNhNmEzMDQwYTA2YzY4MTc3Yzc4ZTQyMzE2YTEwOThlNjBmYjdkMyJ9fX0="
  isEnchant: false
  lore:
    - '&f- &7点击返回'
  custom-model-data: 0

# 下一页
nextPage:
  enable: true
  index: 51
  sound: ""
  name: "    &8[&a下一页&8]"
  lore:
    - '&8▪ &7点击打开下一页'
    - '&8▪ &7当前页码 &a${pageNum}'
    - '&8▪ &7总页码数 &a${count}'
  # 材质
  material: PAPER
  isEnchant: false
  # 自定义模型材质 1.14+生效 0为不生效
  custom-model-data: 0

# 自定义按钮，你可以在这无限扩展你想要的按钮，格式跟这个例子返回一样就好
custom:
  # 分隔板
  pane:
    enable: true
    index: 0,1,2,3,5,6,7,8,9,18,27,36,45,17,26,35,44,46,52,53
    material: black_stained_glass_pane
    isEnchant: false
    sound: ""
    name: "        &8[&7分割板&8]"
    lore:
      - "&7哎呀,不要随便戳人家啦"
    custom-model-data: 0
  # 介绍信息
  info:
    enable: true
    index: 4
    name: "      &8[&e称号奖励&8]"
    sound: ""
    material: PAINTING
    isEnchant: true
    custom-model-data: 0
    lore:
      - '&8▪ &7收集数量 获取奖励'
      - '&8▪ &7收集的多 奖励越多'