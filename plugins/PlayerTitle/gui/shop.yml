# gui标题
title: "&f[&e称号商城&f]"
size: 54
sound: ""

# 默认称号的样式
shop:
  # 称号默认显示名称
  name : "${titleName}"
  # 称号所显示的位置
  index: 10,11,12,13,14,15,16,19,20,21,22,23,24,25,28,29,30,31,32,33,34,37,38,39,40,41,42,43
  # 称号默认材质
  material: NAME_TAG
  # 是否附魔
  isEnchant: true
  # 称号默认自定义材质
  custom-model-data: 0
  # 音效
  sound: ""
  # 称号loree-多层级，某部分有会显示，没有不会显示出来，留空表示不显示这个层级
  lore:
    info:
      - ''
      - '&f[ &c称号说明 &f]'
      - '&8▪ &a显示称号: &f${titleName}'
      - '&8▪ &a购买天数: &f${day}'
    description:
      - ''
      - '&f[ &c称号描述 &f]'
      - '&8▪ &a${description}'
    buff:
      - ''
      - '&f[ &c称号BUFF &f]'
      - '&8▪ &a${buff}'
    particle:
      - ''
      - '&f[ &c称号粒子 &f]'
      - '&8▪ &a${particle}'
    price:
      - ''
      - '&f[ &c称号价格 &f]'
      - '&8▪ &a${buyType} : &f${price}'
    me:
      - ''
      - '&f[ &c个人经济 &f]'
      - '&8▪ &a${buyType} : &f${playerMoney}'
    button:
      - ''
      - '${buyButton}'

buyButton: "&8[&a✔&8] &a点击购买"
buyNoButton: "&8[&c✘&8] &7无法购买"

# 搜索
search:
  enable: true
  index: 48
  name: "    &8[&a根据类型搜索&8]"
  lore:
    - '&8▪ &7当前类型: &a${type}'
    - '&8▪ &7点击切换: &a${nextType}'
  # 材质
  material: COMPASS
  isEnchant: false
  custom-model-data: 0

# 仓库
open:
  enable: true
  index: 50
  name: "    &8[&a称号仓库&8]"
  lore:
    - "&f- &7点击前往称号仓库"
  # 材质
  material: CHEST
  isEnchant: false
  custom-model-data: 0

# 自定义称号
titleCustom:
  enable: true
  index: 46
  name: "    &8[&a自定义称号&8]"
  lore:
    - "&f- &7点击前往自定义称号"
  # 材质
  material: CHEST
  isEnchant: false
  custom-model-data: 0

# 上一页
previousPage:
  enable: true
  index: 47
  name: "    &8[&a上一页&8]"
  lore:
    - '&8▪ &7点击打开上一页'
    - '&8▪ &7当前页码 &a${pageNum}'
    - '&8▪ &7总页码数 &a${count}'
  # 材质
  material: PAPER
  isEnchant: false
  custom-model-data: 0

# 下一页
nextPage:
  enable: true
  index: 51
  name: "    &8[&a下一页&8]"
  lore:
    - '&8▪ &7点击打开下一页'
    - '&8▪ &7当前页码 &a${pageNum}'
    - '&8▪ &7总页码数 &a${count}'
  # 材质
  material: PAPER
  isEnchant: false
  # 自定义模型材质 1.14+生效 0为不生效
  custom-model-data: 0

# 自定义按钮 你可以在这无限扩展你想要的按钮，格式跟这个例子返回一样就好
custom:
  # 返回按钮
  back:
    enable: true
    index: 49
    name: "   &8[&a返回&8]"
    material: PLAYER_HEAD
    headBase: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNjllYTFkODYyNDdmNGFmMzUxZWQxODY2YmNhNmEzMDQwYTA2YzY4MTc3Yzc4ZTQyMzE2YTEwOThlNjBmYjdkMyJ9fX0="
    isEnchant: false
    lore:
      - '&f- &7点击返回'
    custom-model-data: 0
    # 执行的命令
    command: "plm open menu"
  # 分隔板
  pane:
    enable: true
    index: 0,1,2,3,5,6,7,8,9,17,18,26,27,35,36,44,45,52,53
    material: black_stained_glass_pane
    isEnchant: false
    name: "        &8[&7分割板&8]"
    lore:
      - "&7哎呀,不要随便戳人家啦"
    custom-model-data: 0
  # 介绍信息
  info:
    enable: true
    index: 4
    name: "      &8[&e称号介绍&8]"
    material: PAINTING
    isEnchant: true
    custom-model-data: 0
    lore:
      - '&8▪ &7获得称号 展示自我'
      - '&8▪ &7收集数量 获取奖励'
      - '&8▪ &7购买成功 会放仓库'
      - '&8▪ &7前往仓库 点击使用'