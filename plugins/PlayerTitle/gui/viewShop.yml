# gui标题
title: "&f[&e称号商城管理&f]"
size: 54
sound: ""

# 默认称号的样式
shop:
  # 称号所显示的位置
  index: 10,11,12,13,14,15,16,19,20,21,22,23,24,25,28,29,30,31,32,33,34,37,38,39,40,41,42,43
  # 称号默认材质
  material: NAME_TAG
  # 是否附魔
  isEnchant: true
  # 称号默认自定义材质
  custom-model-data: 0
  sound: ""
  # 称号lore
  lore:
    - ''
    - '&f[ &c称号说明 &f]'
    - '&8▪ &a称号编号: &f${id}'
    - '&8▪ &a显示称号: &f${titleName}'
    - '&8▪ &a显示状态: &f${status}'
    - '&8▪ &a购买数量: &f${haveCount}'
    - '&8▪ &a购买天数: &f${day}'
    - ''
    - '&f[ &c称号描述 &f]'
    - '&8▪ &a${description}'
    - ''
    - '&f[ &c称号BUFF &f]'
    - '&8▪ &a${buff}'
    - ''
    - '&f[ &c称号粒子 &f]'
    - '&8▪ &a${particle}'
    - ''
    - '&f[ &c称号价格 &f]'
    - '&8▪ &a${buyType} : ${price}'
    - ''
    - '&8[&a✔&8] &a点击管理'

hide: "&4隐藏中"
show: "&a显示中"

# 搜索
search:
  enable: true
  index: 49
  name: "    &8[&a根据类型搜索&8]"
  lore:
    - '&8▪ &7当前类型: &a${type}'
    - '&8▪ &7点击切换: &a${nextType}'
  # 材质
  material: COMPASS
  isEnchant: false
  custom-model-data: 0

# 上一页
previousPage:
  enable: true
  index: 47
  name: "    &8[&a上一页&8]"
  lore:
    - '&8▪ &7点击打开上一页'
    - '&8▪ &7当前页码 &a${pageNum}'
    - '&8▪ &7总页码数 &a${count}'
  # 材质
  material: PAPER
  isEnchant: false
  custom-model-data: 0

# 下一页
nextPage:
  enable: true
  index: 51
  name: "    &8[&a下一页&8]"
  lore:
    - '&8▪ &7点击打开下一页'
    - '&8▪ &7当前页码 &a${pageNum}'
    - '&8▪ &7总页码数 &a${count}'
  # 材质
  material: PAPER
  isEnchant: false
  # 自定义模型材质 1.14+生效 0为不生效
  custom-model-data: 0

# 自定义按钮 你可以在这无限扩展你想要的按钮，格式跟这个例子返回一样就好
custom:
  # 分隔板
  pane:
    enable: true
    index: 0,1,2,3,4,5,6,7,8,9,17,18,26,27,35,36,44,45,46,48,50,52,53
    material: black_stained_glass_pane
    isEnchant: false
    name: "        &8[&7分割板&8]"
    lore:
      - "&7哎呀,不要随便戳人家啦"
    custom-model-data: 0