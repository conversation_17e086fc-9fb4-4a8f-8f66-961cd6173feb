# gui标题
title: "&0自定义称号"
size: 27
sound: ""

# 点券
point:
  # 是否使用
  enable: true
  # 在gui中的坐标
  index: 11
  # 在gui中的材质名称
  material: DIAMOND
  # 是否附魔效果
  isEnchant: false
  # 在gui中的名称
  name: "   &8[&5ℑ&8] ▪ &8[&d点券购买&8]"
  # 自定义模型材质 1.14+生效 0为不生效
  custom-model-data: 0
  sound: ""
  # 在gui中的lore
  lore:
    - ''
    - '&8▪ &7购买 ${title_name} &7所需费用'
    - ''
    - '&8▪ &7需支付 &a${price} &7点券'
    - '&8▪ &7你拥有 &a${money} &7点券'
    - ''
    - '${button}'
  # 价格
  price: 5000
  # 默认拥有天数 0为永久
  day: 0

# 称号币
coin:
  enable: true
  index: 13
  material: bread
  name: "   &8[&6ℑ&8] ▪ &8[&e称号币购买&8]"
  custom-model-data: 0
  isEnchant: false
  lore:
    - ''
    - '&8▪ &7购买 ${title_name} &7所需费用'
    - ''
    - '&8▪ &7需支付 &a${price} &7称号币'
    - '&8▪ &7你拥有 &a${money} &7称号币'
    - ''
    - '${button}'
  price: 10000
  # 默认拥有天数 0为永久
  day: 0

# 金币
vault:
  enable: true
  index: 15
  material: GOLD_INGOT
  isEnchant: false
  name: "   &8[&6ℑ&8] ▪ &8[&e金币购买&8]"
  custom-model-data: 0
  lore:
    - ''
    - '&8▪ &7购买 ${title_name} &7所需费用'
    - ''
    - '&8▪ &7需支付 &a${price} &7金币'
    - '&8▪ &7你拥有 &a${money} &7金币'
    - ''
    - '${button}'
  price: 666666
  # 默认拥有天数 0为永久
  day: 0

# ${button}对应的变量
yesButton: " &8[&a✔&8] &a点击购买"
noButton: " &8[&c✘&8] &7余额不足"

# 返回按钮
back:
  enable: true
  index: 22
  name: "   &8[&a返回&8]"
  material: PLAYER_HEAD
  headBase: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNjllYTFkODYyNDdmNGFmMzUxZWQxODY2YmNhNmEzMDQwYTA2YzY4MTc3Yzc4ZTQyMzE2YTEwOThlNjBmYjdkMyJ9fX0="
  isEnchant: false
  lore:
    - '&f- &7点击取消定制'
  custom-model-data: 0

# 自定义按钮 你可以在这无限扩展你想要的按钮，格式跟这个例子返回一样就好
custom:
  # 分隔板
  pane:
    enable: true
    index: 0,1,2,3,4,5,6,7,8,9,17,18,19,20,21,23,24,25,26
    material: black_stained_glass_pane
    isEnchant: false
    name: "        &8[&7分割板&8]"
    lore:
      - "&7哎呀,不要随便戳人家啦"
    custom-model-data: 0

# 创建提醒语言文件
notMoneyFailureMsg: "&8[&c✘&8] &7余额不足"
customMsg: "&8[&a✔&8] &7请在聊天框输入&a称号名称，&7输入 &aT &7退出操作"
cancelMsh: "&8[&a✔&8] &a操作已取消"
nameBlackListFailureMsg: "&8[&c✘&8] &4称号名包含非法字符 &a${name}"
buyMsg: "&8[&a✔&8] &a购买成功"

# 称号名称黑名单
nameBlackList:
  - "%"
  - "草"
  - "傻"
  - "服主"
  - "&k"