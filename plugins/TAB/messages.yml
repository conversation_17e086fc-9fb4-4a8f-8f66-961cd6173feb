announce-command-usage: "用法：/tab announce <类型> <名称> <时长>\n目前支持的类型：&lbar, scoreboard"
bossbar-feature-not-enabled: "&c此命令需要启用 bossbar 功能。"
bossbar-announce-command-usage: "用法: /tab announce bar <血条名称> <长度>"
bossbar-not-found: "&c未找到名为 \"%name%\" 的血条"
bossbar-already-announced: "&c此血条已经在公告中"
group-data-removed: "&3&6枫影轻语&7 >> 已成功从组 &e%group% &3中移除所有数据"
group-value-assigned: "&3&6枫影轻语&7 >> %property% '&r%value%&r&3' 已成功分配给组 &e%group%"
group-value-removed: "&3&6枫影轻语&7 >> %property% 已成功从组 &e%group% &3中移除"
user-data-removed: "&3&6枫影轻语&7 >> 已成功从玩家 &e%player% &3中移除所有数据"
user-value-assigned: "&3&6枫影轻语&7 >> %property% '&r%value%&r&3' 已成功分配给玩家 &e%player%"
user-value-removed: "&3&6枫影轻语&7 >> %property% 已成功从玩家 &e%player% &3中移除"
parse-command-usage: "用法: /tab parse <玩家> <占位符>"
send-command-usage: "用法: /tab send <类型> <玩家> <Bar 名称> <长度>\n目前支持的类型: &lbar"
send-bar-command-usage: "用法: /tab send bar <玩家> <Bar 名称> <长度>"
team-feature-required: "此命令需要启用记分板队伍功能"
collision-command-usage: "用法: /tab setcollision <玩家> <true/false>"
no-permission: "&c很抱歉，您没有权限执行此命令。如果您认为这是一个错误，请联系服务器管理员。"
command-only-from-game: "&c此命令必须在游戏中运行"
player-not-online: "&c找不到名为 \"%player%\" 的在线玩家"
invalid-number: "\"%input%\" 不是一个数字！"
scoreboard-feature-not-enabled: "&4此命令需要启用记分板功能。"
scoreboard-announce-command-usage: "用法: /tab scoreboard announce <记分板名称> <长度>"
scoreboard-not-found: "&c找不到名为 \"%name%\" 的记分板"
reload-success: "&3&6枫影轻语&7 >> 重新加载成功"
reload-fail-file: "&3&6枫影轻语&7 >> &4重新加载失败，文件 %file% 语法错误。请检查控制台以获取更多信息。"
scoreboard-toggle-on: "&2记分板已启用"
scoreboard-toggle-off: "&7记分板已禁用"
bossbar-toggle-on: "&2Bossbar 现在可见"
bossbar-toggle-off: "&7Bossbar 不再可见"
scoreboard-show-usage: "用法: /tab scoreboard show <记分板> [玩家]"
bossbar-not-marked-as-announcement: "&c此 Bossbar 未标记为公告 Bar，因此已永久显示（如果满足显示条件）"
bossbar-announcement-success: "&a正在公告 Bossbar &6%bossbar% &a，持续 %length% 秒。"
bossbar-send-success: "&a正在向玩家 &6%player% &a发送 Bossbar &6%bossbar% &a，持续 %length% 秒。"
help-menu:
  - "&m                                                                                "
  - " &8>> &3&l/tab reload"
  - "      - &7重新加载插件和配置"
  - " &8>> &3&l/tab &9group&3/&9player &3<名称> &9<属性> &3<值...>"
  - "      - &7输入 &8/tab group/player &7以显示属性"
  - " &8>> &3&l/tab parse <玩家> <占位符> "
  - "      - &7测试占位符是否有效"
  - " &8>> &3&l/tab debug [玩家]"
  - "      - &7显示有关玩家的调试信息"
  - " &8>> &3&l/tab cpu"
  - "      - &7显示插件的 CPU 使用率"
  - " &8>> &3&l/tab group/player <名称> remove"
  - "      - &7清除有关玩家/组的所有数据"
  - "&m                                                                                "
mysql-help-menu:
  - "&6/tab mysql upload - 将数据从文件上传到 MySQL"
  - "&6/tab mysql download - 将数据从 MySQL 下载到文件"
mysql-fail-not-enabled: "&c无法从/向 MySQL 下载/上传数据，因为它已被禁用。"
mysql-fail-error: "由于错误，MySQL 下载失败。请检查控制台以获取更多信息。"
mysql-download-success: "&aMySQL 数据下载成功。"
mysql-upload-success: "&aMySQL 数据上传成功。"
scoreboard-help-menu:
  - "/tab scoreboard [on/off/toggle] [玩家] [选项]"
  - "/tab scoreboard show <名称> [玩家]"
  - "/tab scoreboard announce <名称> <长度>"
bossbar-help-menu:
  - "/tab bossbar [on/off/toggle] [玩家] [选项]"
  - "/tab bossbar send <名称> [玩家]"
  - "/tab bossbar announce <名称> <长度>"
nametag:
  help-menu:
    - "/tab nametag <show/hide/toggle> [玩家] [-s] - 切换指定玩家的头衔显示"
    - "/tab nametag <showview/hideview/toggleview> [玩家] [查看者] [-s] - 切换指定玩家对其他玩家的头衔显示"
  feature-not-enabled: "&c此命令需要启用头衔功能。"
  view-hidden: "&a所有玩家的头衔已对您隐藏"
  view-shown: "&a所有玩家的头衔已对您显示"
  player-hidden: "&a您的头衔已被隐藏"
  player-shown: "&a您的头衔已被显示"
  no-arg-from-console: "&c如果从控制台运行此命令，则需要指定玩家"
