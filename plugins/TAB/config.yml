# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Header-&-Footer
header-footer:
  enabled: true
  header:
    - ""
    - "%animation:ServerName%"
    - "&r&e在线人数 &6%server_online%/%server_max_players%"
  footer:
    - "&e&l　TPS监控 %server_tps%&7丨&e&l内存监控 &6%server_ram_used% MB / %server_ram_max% MB &f　"
    - "&e&l  祝你游玩愉快~"
    - "&e"
  disable-condition: '%world%=disabledworld'
  per-world:
    world1:
      header:
        - "an example of world with custom"
      footer:
        - "header/footer and prefix/suffix"
    world2;world3:
      header:
        - "This is a shared header for"
        - "world2 and world3"
  per-server:
    server1:
      header:
        - "an example of server with custom header"

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Tablist-name-formatting
tablist-name-formatting:
  enabled: true
  disable-condition: '%world%=disabledworld'

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Nametags
scoreboard-teams:
  enabled: true
  enable-collision: true
  invisible-nametags: false
  # https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Sorting-players-in-tablist
  sorting-types:
    - "GROUPS:owner,admin,mod,helper,builder,vip,default"
    - "PLACEHOLDER_A_TO_Z:%player%"
  case-sensitive-sorting: true
  can-see-friendly-invisibles: false
  disable-condition: '%world%=disabledworld'

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Playerlist-Objective
playerlist-objective:
  enabled: true
  value: "%ping%"
  fancy-value: "&e%condition:ping_color%ms"
  title: "TAB" # Only visible on Bedrock Edition
  render-type: INTEGER
  disable-condition: '%world%=disabledworld'

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Belowname
belowname-objective:
  enabled: true
  value: "%health%"
  title: "&c"
  fancy-value: "&c%health% ♥️"
  fancy-value-default: "NPC"
  disable-condition: '%world%=disabledworld'

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Spectator-fix
prevent-spectator-effect:
  enabled: false

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Bossbar
bossbar:
  enabled: false
  toggle-command: /bossbar
  remember-toggle-choice: false
  hidden-by-default: false
  bars:
    ServerInfo:
      style: "PROGRESS" # for 1.9+: PROGRESS, NOTCHED_6, NOTCHED_10, NOTCHED_12, NOTCHED_20
      color: "%animation:barcolors%" # for 1.9+: BLUE, GREEN, PINK, PURPLE, RED, WHITE, YELLOW
      progress: "100" # in %
      text: "&fWebsite: &bwww.domain.com"

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Scoreboard
scoreboard:
  enabled: true
  toggle-command: /sb
  remember-toggle-choice: false
  hidden-by-default: false
  use-numbers: true
  static-number: 0
  delay-on-join-milliseconds: 0
  scoreboards:
    scoreboard-1.20.3+:
      title: "<#FC835C>枫影轻语ABREEZE</#FCD05C>"
      display-condition: "%player-version-id%>=765;%bedrock%=false" # Only display it to players using 1.20.3+ AND NOT bedrock edition
      lines:
        - "&7%server_time_yyyy年MM月dd日% %server_time_HH:mm:ss% %server_time_E%"
        - ""
        - "&e当前在线||&6%server_online%&7/&6%server_max_players%"
        - ""
        - "&b&l你的信息||&6%player%"
        - "&e你的状态||&c♥️%health%&7 🍗&6%player_food_level%"
        - "&e你的坐标||&6%multiverse_world_alias% %player_x%&7, &6%player_y%&7, &6%player_z%"
        - "&e你的FY||&6%playerpoints_points%"
        - ""
        - "&b&l统计数据"
        - "&e在线时长||&6%statistic_time_played%"
        - "&e死亡次数||&6%statistic_deaths%"
        - "&e击杀怪物||&6%statistic_mob_kills%"
        - ""
        - "&7play.abreeze.icu"

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Layout
layout:
  enabled: false
  direction: COLUMNS
  default-skin: mineskin:383747683
  enable-remaining-players-text: true
  remaining-players-text: '... and %s more'
  empty-slot-ping-value: 1000
  layouts:
    default:
      fixed-slots:
        - '1|&3Website&f:'
        - '2|&bmyserver.net'
        - '3|&8&m                       '
        - '4|&3Name&f:'
        - '5|&b%player%'
        - '7|&3Rank&f:'
        - '8|Rank: %group%'
        - '10|&3World&f:'
        - '11|&b%world%'
        - '13|&3Time&f:'
        - '14|&b%time%'
        - '21|&3Teamspeak&f:'
        - '22|&bts.myserver.net'
        - '23|&8&m                       '
        - '41|&3Store&f:'
        - '42|&bshop.myserver.net'
        - '43|&8&m                       '
      groups:
        staff:
          condition: permission:tab.staff
          slots:
            - 24-40
        players:
          slots:
            - 44-80

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Ping-Spoof
ping-spoof:
  enabled: false
  value: 0

placeholders:
  date-format: "dd.MM.yyyy"
  time-format: "[HH:mm:ss / h:mm a]"
  time-offset: 0
  register-tab-expansion: false

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Placeholder-output-replacements
placeholder-output-replacements:
  "%essentials_vanished%":
    "yes": "&7| Vanished"
    "no": ""

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Conditional-placeholders
conditions:
  nick: # use it with %condition:nick%
    conditions:
      - "%player%=%essentials_nickname%"
    yes: "%player%"
    no: "~%essentials_nickname%"
  ping_color: # use it with %condition:ping_color%
    conditions:
      - "%player_ping%<50"
    yes: "&a%player_ping%"
    no: "%condition:ping_medium%"
  ping_medium: # helper condition for medium/high ping
    conditions:
      - "%player_ping%<=100"
    yes: "&e%player_ping%"
    no: "&c%player_ping%"

placeholder-refresh-intervals:
  default-refresh-interval: 500
  "%server_uptime%": 1000
  "%server_tps_1_colored%": 1000
  "%server_unique_joins%": 5000
  "%player_health%": 200
  "%player_ping%": 1000
  "%vault_prefix%": 1000
  "%rel_factionsuuid_relation_color%": 1000

# assigning groups by permission nodes instead of taking them from permission plugin
assign-groups-by-permissions: false

# if the option above is true, all groups are taken based on permissions and the one higher in this list is used as primary
# Warning! This is not sorting list and has nothing to do with sorting players in tablist!
primary-group-finding-list:
  - Owner
  - Admin
  - Mod
  - Helper
  - default

# Refresh interval (in milliseconds) of:
# - Permission checks in conditions / sorting
# - Group retrieving from permission plugin for sorting / per-group properties
# - Prefix/suffix placeholders taking data from permission plugin
permission-refresh-interval: 1000

# Unlocks extra console messages
debug: false

# https://github.com/NEZNAMY/TAB/wiki/MySQL
mysql:
  enabled: false
  host: 127.0.0.1
  port: 3306
  database: tab
  username: user
  password: password
  useSSL: true

proxy-support:
  enabled: true
  # Supported types: PLUGIN, REDIS, RABBITMQ
  type: PLUGIN
  plugin:
    # Compatible plugins: RedisBungee
    # If enabled and compatible plugin is found, hook is enabled to work with proxied players
    name: RedisBungee
  redis:
    url: 'redis://:password@localhost:6379/0'
  rabbitmq:
    exchange: 'plugin'
    url: 'amqp://guest:guest@localhost:5672/%2F'

########################################################################
# BUKKIT ONLY - THE FOLLOWING SECTION IS ONLY FOR BACKEND INSTALLATION #
########################################################################

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Per-world-playerlist
per-world-playerlist:
  enabled: false
  # players with tab.staff will always see all players
  allow-bypass-permission: false
  # players in these worlds will always see all players
  ignore-effect-in-worlds:
    - ignoredworld
    - build
  shared-playerlist-world-groups:
    lobby:
      - lobby1
      - lobby2
    minigames:
      - paintball
      - bedwars

compensate-for-packetevents-bug: false

#####################################################################
# PROXY ONLY - THE FOLLOWING SECTION IS ONLY FOR PROXY INSTALLATION #
#####################################################################

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Global-playerlist
global-playerlist:
  enabled: false
  display-others-as-spectators: false
  display-vanished-players-as-spectators: true
  isolate-unlisted-servers: false
  update-latency: false
  spy-servers:
    - spyserver1
    - spyserver2
  server-groups:
    lobbies:
      - lobby1
      - lobby2
    group2:
      - server1
      - server2

# Take permissions and groups from backend server instead of proxy
use-bukkit-permissions-manager: false

# Sometimes server might be using offline uuids in tablist instead of online, such as disabling waterfall's tablist rewrite option
# If you experience tablist formatting not working, toggle this option (set it to opposite value)
# Only affects proxies with online mode enabled
use-online-uuid-in-tablist: true