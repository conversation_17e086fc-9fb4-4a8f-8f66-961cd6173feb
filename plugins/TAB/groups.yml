#Owner:
#  tabprefix: "&0&l[&a&lOwner&0&l] &a"
#  tagprefix: "&2&lOwner &a"
Player:
  tabprefix: "&0&l[&7&lPlayer&0&l] &3"
  tagprefix: "&2&lPlayer &3"
example_group:
  header:
    - "This is an example of per-group header/footer"
  footer:
    - "applied to a group"

 # default settings for all groups, all groups will take properties from this section unless player's primary group overrides a specific setting
_DEFAULT_:
  tabprefix: "&7[ %AFKPlus_Status%&7 ]&r &7[ %playertitle_use% &7] &7[ &a%multiverse_world_alias%&7 ] [ &e%potatoipdisplay_province% %potatoipdisplay_city% &7] "
  tagprefix: "&7[ %AFKPlus_Status%&7 ]&r &7[ %playertitle_use% &7] &e "
  customtabname: "&f%player%"
  tabsuffix: "%luckperms-suffix%"
  tagsuffix: "%luckperms-suffix%"

per-world:
  world1:
    Owner:
      tabprefix: "&0&l[&a&lOwner&0&l] &a"
      tagprefix: "&2&lOwner &a"