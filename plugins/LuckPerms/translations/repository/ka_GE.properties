luckperms.logs.actionlog-prefix=ჯურნალი
luckperms.logs.verbose-prefix=მრავალმეტყველი
luckperms.logs.export-prefix=ჩამოტვირთე
luckperms.commandsystem.available-commands=გამოიყენე {0} რომ ნახო ბრძანებები
luckperms.commandsystem.command-not-recognised=ბრძანება ვერ იქნა ამოცნობილი
luckperms.commandsystem.no-permission=თქვენთ არ გაქვთ უფლება გამოიყენოთ ეს ბრძანება\!
luckperms.commandsystem.no-permission-subcommands=თქვენ არ გაქვთ გამოიყენოთ ეს ბრძანება ან Sub-ბრძანება
luckperms.commandsystem.already-executing-command=მიმდინარეობს სხვა ბრძანების შესრულება, დაველოდოთ მის დასრულებას...
luckperms.commandsystem.usage.sub-commands-header=Sub-ბრძანება
luckperms.commandsystem.usage.usage-header=ბრძანების გამოყენება
luckperms.commandsystem.usage.arguments-header=არგუმენტი
luckperms.first-time.no-permissions-setup=როგორც ჩანს არც ერთი ბრძანბა არ დაყენებულა\!
luckperms.first-time.use-console-to-give-access=სანამ გამოიყენებთ LuckPerms-ს იქამდე გამოიყენთ კონსოლი რათა თქვენ თავს მისცეთ უფლება გამოიყენოს LuckPerms-ი
luckperms.first-time.console-command-prompt=გახსენი კონსოლი და გაუშვით
luckperms.first-time.next-step=როდესაც ამას მორჩებით ამას თქვენ შეგიძლიათ დაიწყოთ ყველაფერი
luckperms.first-time.wiki-prompt=არ იცი საიდან დაიწყო? დააწირე აქ {0}
luckperms.login.try-again=გთხოვთ სცადეთ მოგვიანებით
luckperms.login.loading-database-error=მონაცემთა ბაზაში დაფიქსირდა შეცდომა
luckperms.login.server-admin-check-console-errors=თუ სერვერის ადმინი ხარ შეამოწმე კონსოლი ერორების დასაფიქსირებლად
luckperms.login.server-admin-check-console-info=გთხოვთ შეამოწმეთ სერვერის კონსოლი დამატებითი ინფორმაციისთვის
luckperms.login.data-not-loaded-at-pre=თქვენი მომხმარებლის ნებართვებათა მონაცემები არ ჩაიტვირთა წინასწარი შესვლის ეტაპზე
luckperms.login.unable-to-continue=გაგრძელება ვერ ხერხდება
luckperms.login.craftbukkit-offline-mode-error=ეს სავარაუდოდ CraftBukkit''სა და ონლაინ რეჟიმის პარამეტრს შორის კონფლიქტის გამოა გამოწვეული
luckperms.login.unexpected-error=ნებართვათა მონაცემების დაყენებისას გამოვლინდა მოულოდნელი შეცდომა
luckperms.opsystem.disabled=ამ სერვერზე გამორთულია ვანილას OP სისტემა
luckperms.opsystem.sponge-warning=გთხოვთ გაითვალისწინოთ, რომ სერვერის ოპერატორის სტატუსი გავლენას არ ახდენს ღრუბლის ნებართვის შემოწმებაზე, როდესაც ნებართვის მოდულია აქტიური. თქვენ პირდაპირ უნდა შეცვალოთ მომხმარებლის.
luckperms.duration.unit.years.plural={0} წელი
luckperms.duration.unit.years.singular={0} წელი
luckperms.duration.unit.years.short={0}წ
luckperms.duration.unit.months.plural={0} თვე
luckperms.duration.unit.months.singular={0} თვე
luckperms.duration.unit.months.short={0}თვ
luckperms.duration.unit.weeks.plural={0} კვირა
luckperms.duration.unit.weeks.singular={0} კვირა
luckperms.duration.unit.weeks.short={0}კვ
luckperms.duration.unit.days.plural={0} დღე
luckperms.duration.unit.days.singular={0} დღე
luckperms.duration.unit.days.short={0}დ
luckperms.duration.unit.hours.plural={0} საათი
luckperms.duration.unit.hours.singular={0} საათი
luckperms.duration.unit.hours.short={0}სთ
luckperms.duration.unit.minutes.plural={0} წუთი
luckperms.duration.unit.minutes.singular={0} წუთი
luckperms.duration.unit.minutes.short={0}წთ
luckperms.duration.unit.seconds.plural={0} წამი
luckperms.duration.unit.seconds.singular={0} წამი
luckperms.duration.unit.seconds.short={0}წმ
luckperms.duration.since={0} უკან
luckperms.command.misc.invalid-code=არასწორი კოდი
luckperms.command.misc.response-code-key=საპასუხო კოდი
luckperms.command.misc.error-message-key=შეტყობინება
luckperms.command.misc.bytebin-unable-to-communicate=ბაითბინთან კომუნიკაცია ვერ ხერხდება
luckperms.command.misc.webapp-unable-to-communicate=ვებ-აპლიკაციასთან კომუნიკაცია ვერ ხერხდება
luckperms.command.misc.check-console-for-errors=შეამოწმეთ კონსოლი შეცდომებისთვის
luckperms.command.misc.file-must-be-in-data=ფაილი {0} უნდა იყოს მონაცემთა კატალოგის პირდაპირი შვილობილი
luckperms.command.misc.wait-to-finish=გთხოვთ, დაელოდოთ მის დასრულებას და სცადოთ ხელახლა
luckperms.command.misc.invalid-priority=არასწორი პრიორიტეტი {0}
luckperms.command.misc.expected-number=მოსალოდნელი იყო რიცხვი
luckperms.command.misc.date-parse-error=თარიღის გარჩევა ვერ მოხერხდა {0}
luckperms.command.misc.date-in-past-error=თქვენ არ შეგიძლიათ თარიღის წარსულში დანიშვნა\!
luckperms.command.misc.page=გვერდი {0} / {1}''დან
luckperms.command.misc.page-entries={0} ჩანაწერი
luckperms.command.misc.none=არცერთი
luckperms.command.misc.loading.error.unexpected=წარმოიშვა გაუთვალისწინებელი ხარვეზი
luckperms.command.misc.loading.error.user=მომხმარებელი არ არის ჩატვირთული
luckperms.command.misc.loading.error.user-specific=მომხმარებელი {0}''ის ჩატვირთვა ვერ ხერხდება
luckperms.command.misc.loading.error.user-not-found=მომხმარებელი {0}''ის პოვნა ვერ მოხერხდა
luckperms.command.misc.loading.error.user-save-error=მომხმარებელი {0}''ის მონაცემების შენახვისას წარმოიშვა შეცდომა
luckperms.command.misc.loading.error.user-not-online=იუზერი {0} არ არის ონლაინში
luckperms.command.misc.loading.error.user-invalid={0} არ არის მისაღები ნიკნეიმი/uuid
luckperms.command.misc.loading.error.user-not-uuid=სამიზნე მომხმარებელი {0} არ არის სწორი uuid
luckperms.command.misc.loading.error.group=ჯგუფი არაა დატვირთული
luckperms.command.misc.loading.error.all-groups=შეუძლებელია ყველა ჯგუფების ჩატვირთვა
luckperms.command.misc.loading.error.group-not-found=ჯგუფი სახელად {0} ვერ მოიძებნა
luckperms.command.misc.loading.error.group-save-error={0} ჯგუფის მონაცემები შენახვისას გამოვლინდა შეცდომა
luckperms.command.misc.loading.error.group-invalid={0} არ არის დასაშვები ჯგუფი
luckperms.command.misc.loading.error.track=ტრასა არ არის ჩატვირთული
luckperms.command.misc.loading.error.all-tracks=ტრასის ჩატვირთვა ვერ მოხერხდა
luckperms.command.misc.loading.error.track-not-found=ტრასა სახელად {0} ვერ მოიძებნა
luckperms.command.misc.loading.error.track-save-error=ტრასის {0} შენახვისას გამოვლინდა შეცდომა
luckperms.command.misc.loading.error.track-invalid={0} არ არის ტრასის დასაშვები დასახელება
luckperms.command.editor.no-match=ვერ გახსნა რედაქტორი, არცერთი ობიექტი არ შეესაბამება სასურველ ტიპს
luckperms.command.editor.start=მზადდება ახალი რედაქტორის სესია, გთხოვთ დაელოდეთ...
luckperms.command.editor.url=რედაქტორის გასახსნელად დააჭირეთ ქვემოთ მოცემულ ბმულს
luckperms.command.editor.unable-to-communicate=ვერ ხერხდება კომუნიკაცია
luckperms.command.editor.apply-edits.success=ვებ რედაქტორის მონაცემები წარმატებით იქნა გამოყენებული {0} {1} 
luckperms.command.editor.apply-edits.success-summary={0} {1} და {2} {3}
luckperms.command.editor.apply-edits.success.additions=დანამატები
luckperms.command.editor.apply-edits.success.additions-singular=დანამატი
luckperms.command.editor.apply-edits.success.deletions=წაშლები
luckperms.command.editor.apply-edits.success.deletions-singular=წაშლა
luckperms.command.info.running-plugin=გაშვებულია
luckperms.command.info.platform-key=პლატფორმა
luckperms.command.info.server-brand-key=სერვერის ბრენდი
luckperms.command.info.server-version-key=სერვერის ვერსია
luckperms.command.info.storage-key=სათავსო
luckperms.command.info.storage-type-key=ტიპი
luckperms.command.info.storage.meta.split-types-key=ტიპი
luckperms.command.info.storage.meta.ping-key=პინგი
luckperms.command.info.storage.meta.connected-key=დაკავშირებულია
luckperms.command.info.storage.meta.file-size-key=ფაილის ზომა
luckperms.command.info.extensions-key=გაფართოებები
luckperms.command.info.messaging-key=შეტყობინებები
luckperms.command.info.instance-key=მაგალითი
luckperms.command.info.static-contexts-key=უძრავი კონტექსტები
luckperms.command.info.online-players-key=ხაზზე მყოფი მოთამაშეები
luckperms.command.info.online-players-unique={0} უნიკალური
luckperms.command.info.uptime-key=მოქმედების დრო
luckperms.command.info.local-data-key=ადგილობრივი მონაცემები
luckperms.command.info.local-data={0} მომხმარებელი, {1} ჯგუფი, {2} ბილიკი
luckperms.command.generic.create.success={0} წარმატებით იქნა შექმნილი
luckperms.command.generic.create.error={0}''ის შექმნისას წარმოიშვა შეცდომა
luckperms.command.generic.create.error-already-exists={0} უკვე არსებობს\!
luckperms.command.generic.delete.success={0} წარმატებით იქნა წაშლილი
luckperms.command.generic.delete.error={0}''ის წაშლისას წარმოიშვა შეცდომა
luckperms.command.generic.delete.error-doesnt-exist={0} არ არსებობს\!
luckperms.command.generic.rename.success={0}''ს წარმატებით გადაერქვა სახელი {1}''ზე
luckperms.command.generic.clone.success={0} წარმატებით იქნა კლონირებული {1}''ზე
luckperms.command.generic.info.parent.title=მშობელი ჯგუფები
luckperms.command.generic.info.parent.temporary-title=დროებითი მშობელი ჯგუფები
luckperms.command.generic.info.expires-in=ვადა გასდის
luckperms.command.generic.info.inherited-from=მემკვიდრეობა
luckperms.command.generic.info.inherited-from-self=თვითონ
luckperms.command.generic.show-tracks.title={0}''ის ჩანაწერები
luckperms.command.generic.show-tracks.empty={0}''ს არ აქვს ჩანაწერები
luckperms.command.generic.clear.node-removed={0} კვანძი წაიშალა
luckperms.command.generic.clear.node-removed-singular={0} კვანძი წაიშალა
luckperms.command.generic.permission.info.title={0}''ის ნებართვები
luckperms.command.generic.permission.info.empty={0}''ის არ აქვს ნებართვები დაყენებული
luckperms.usage.sponge-option-clear.argument.contexts=კონტექსტები ვარიანტების გასასუფთავებლად
