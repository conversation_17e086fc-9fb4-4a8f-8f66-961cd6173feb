luckperms.logs.actionlog-prefix=DZIENNIK
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EKSPORT
luckperms.commandsystem.available-commands=Użyj {0} aby wyświ<PERSON><PERSON>ć dostępne polecenia
luckperms.commandsystem.command-not-recognised=Komenda nie została rozpoznana
luckperms.commandsystem.no-permission=Nie masz uprawnień potrzebnych do używania tej komendy\!
luckperms.commandsystem.no-permission-subcommands=Nie masz uprawnień do używania żadnych poleceń podrzędnych
luckperms.commandsystem.already-executing-command=Inna komenda jest wykonywana, czekanie na jej zakończenie...
luckperms.commandsystem.usage.sub-commands-header=Pod komendą
luckperms.commandsystem.usage.usage-header=Użycie komend
luckperms.commandsystem.usage.arguments-header=Argumenty
luckperms.first-time.no-permissions-setup=Wygląda na to, że żadne uprawnienia nie zostały jeszcze skonfigurowane\!
luckperms.first-time.use-console-to-give-access=<PERSON><PERSON><PERSON> bę<PERSON>sz mógł uż<PERSON> jakichkolwiek poleceń LuckPerms w grze, musisz użyć konsoli aby dać sobie dostęp
luckperms.first-time.console-command-prompt=Otwórz konsolę i uruchom
luckperms.first-time.next-step=Po tym jak to zrobisz, możesz zacząć definiować uprawnienia i grupy
luckperms.first-time.wiki-prompt=Nie wiesz gdzie zacząć? Sprawdź tutaj\: {0}
luckperms.login.try-again=Spróbuj ponownie później
luckperms.login.loading-database-error=Wystąpił błąd bazy danych podczas wczytywania uprawnień
luckperms.login.server-admin-check-console-errors=Jeśli jesteś administratorem serwera, sprawdź konsolę pod kątem błędów
luckperms.login.server-admin-check-console-info=Sprawdź konsolę serwera, aby uzyskać więcej informacji
luckperms.login.data-not-loaded-at-pre=Dane uprawnień dla użytkownika nie zostały załadowane na etapie wstępnego logowania
luckperms.login.unable-to-continue=nie można kontynuować
luckperms.login.craftbukkit-offline-mode-error=jest to prawdopodobnie spowodowane konfliktem między CraftBukkit a ustawieniem trybu online
luckperms.login.unexpected-error=Wystąpił nieoczekiwany błąd podczas konfigurowania uprawnień
luckperms.opsystem.disabled=System Vanilla OP jest wyłączony na tym serwerze
luckperms.opsystem.sponge-warning=Pamiętaj, że status operatora serwera nie ma wpływu na sprawdzanie uprawnień Sponge po zainstalowaniu wtyczki uprawnień, musisz bezpośrednio edytować dane użytkownika
luckperms.duration.unit.years.plural={0} lat
luckperms.duration.unit.years.singular={0} rok
luckperms.duration.unit.years.short={0}r
luckperms.duration.unit.months.plural={0} miesięcy
luckperms.duration.unit.months.singular={0} miesiąc
luckperms.duration.unit.months.short={0}mies
luckperms.duration.unit.weeks.plural={0} tygodni
luckperms.duration.unit.weeks.singular={0} tydzień
luckperms.duration.unit.weeks.short={0}tyg
luckperms.duration.unit.days.plural={0} dni
luckperms.duration.unit.days.singular={0} dni
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} godz
luckperms.duration.unit.hours.singular={0} godz
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minut
luckperms.duration.unit.minutes.singular={0} minuta
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} sekund
luckperms.duration.unit.seconds.singular={0} sekundę
luckperms.duration.unit.seconds.short={0} s
luckperms.duration.since={0} temu
luckperms.command.misc.invalid-code=Błędny kod
luckperms.command.misc.response-code-key=kod odpowiedzi
luckperms.command.misc.error-message-key=wiadomość
luckperms.command.misc.bytebin-unable-to-communicate=Nie można połączyć z bytebin
luckperms.command.misc.webapp-unable-to-communicate=Nie można połączyć z aplikacją internetową
luckperms.command.misc.check-console-for-errors=Sprawdź konsolę pod kątem błędów
luckperms.command.misc.file-must-be-in-data=Plik {0} musi być bezpośrednim podrzędnym katalogu danych
luckperms.command.misc.wait-to-finish=Poczekaj na zakończenie i spróbuj ponownie
luckperms.command.misc.invalid-priority=Nieprawidłowy priorytet {0}
luckperms.command.misc.expected-number=Oczekiwano liczby
luckperms.command.misc.date-parse-error=Nie można przetworzyć daty {0}
luckperms.command.misc.date-in-past-error=Nie możesz wybrać minionej daty\!
luckperms.command.misc.page=strona {0} z {1}
luckperms.command.misc.page-entries={0} wpisów
luckperms.command.misc.none=Brak
luckperms.command.misc.loading.error.unexpected=Wystąpił nieoczekiwany błąd
luckperms.command.misc.loading.error.user=Użytkownik nie jest załadowany
luckperms.command.misc.loading.error.user-specific=Nie można załadować docelowego użytkownika {0}
luckperms.command.misc.loading.error.user-not-found=Nie można odnaleźć użytkownika {0}
luckperms.command.misc.loading.error.user-save-error=Wystąpił błąd podczas zapisywania danych użytkownika {0}
luckperms.command.misc.loading.error.user-not-online=Użytkownik {0} nie jest dostępny
luckperms.command.misc.loading.error.user-invalid={0} nie jest prawidłową nazwą użytkownika/uuid
luckperms.command.misc.loading.error.user-not-uuid=Docelowy użytkownik {0} nie jest prawidłowym uuuid
luckperms.command.misc.loading.error.group=Grupa nie załadowana
luckperms.command.misc.loading.error.all-groups=Nie można załadować wszystkich grup
luckperms.command.misc.loading.error.group-not-found=Nie można odnaleźć grupy o nazwie {0}
luckperms.command.misc.loading.error.group-save-error=Wystąpił błąd podczas zapisywania danych grupy {0}
luckperms.command.misc.loading.error.group-invalid={0} nie jest prawidłową nazwą grupy
luckperms.command.misc.loading.error.track=Ścieżka nie załadowana
luckperms.command.misc.loading.error.all-tracks=Nie można załadować wszystkich ścieżek
luckperms.command.misc.loading.error.track-not-found=Nie znaleziono ścieżki o nazwie {0}
luckperms.command.misc.loading.error.track-save-error=Wystąpił błąd podczas zapisywania danych ścieżki {0}
luckperms.command.misc.loading.error.track-invalid={0} nie jest prawidłową nazwą ścieżki
luckperms.command.editor.no-match=Nie można otworzyć edytora, żaden obiekt nie pasuje do pożądanego typu
luckperms.command.editor.start=Przygotowywanie nowej sesji edytora, proszę czekać...
luckperms.command.editor.url=Kliknij poniższy link, aby otworzyć edytor
luckperms.command.editor.unable-to-communicate=Nie można połączyć z serwerem edytora
luckperms.command.editor.apply-edits.success=Dane edytora zostały pomyślnie zastosowane dla {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} i {2} {3}
luckperms.command.editor.apply-edits.success.additions=elementy dodane
luckperms.command.editor.apply-edits.success.additions-singular=element dodany
luckperms.command.editor.apply-edits.success.deletions=elementów usuniętych
luckperms.command.editor.apply-edits.success.deletions-singular=element usunięty
luckperms.command.editor.apply-edits.no-changes=Nie zastosowano żadnych zmian z edytora internetowego, zwrócone dane nie zawierały żadnych zmian
luckperms.command.editor.apply-edits.unknown-type=Nie można zastosować zmian dla określonego typu obiektu
luckperms.command.editor.apply-edits.unable-to-read=Nie można odczytać danych używając podanego kodu
luckperms.command.search.searching.permission=Wyszukiwanie użytkowników i grup z {0}
luckperms.command.search.searching.inherit=Wyszukiwanie użytkowników i grup, które dziedziczą {0}
luckperms.command.search.result=Znaleziono {0} wpisów od {1} użytkowników i {2} grup
luckperms.command.search.result.default-notice=Uwaga\: podczas wyszukiwania członków grupy domyślnej, nie będą wyświetlani gracze offline bez innych uprawnień\!
luckperms.command.search.showing-users=Wyświetlanie wpisów użytkownika
luckperms.command.search.showing-groups=Wyświetlanie wpisów grup
luckperms.command.tree.start=Generowanie drzewa uprawnień, proszę czekać...
luckperms.command.tree.empty=Nie można wygenerować drzewa, nie znaleziono wyników
luckperms.command.tree.url=URL drzewa uprawnień
luckperms.command.verbose.invalid-filter={0} nie jest prawidłowym filtrem wyświetlania
luckperms.command.verbose.enabled=Zacznij rejestrować {0} w celu sprawdzenia pasującego do {1}
luckperms.command.verbose.command-exec=Wymuszanie na {0} wykonania komendy {1} i wyświetlanie wszystkich sprawdzeń...
luckperms.command.verbose.off=Rozszerzone zbieranie informacji o błędach {0}
luckperms.command.verbose.command-exec-complete=Wykonanie polecenia zakończone
luckperms.command.verbose.command.no-checks=Wykonanie komendy zakończone, ale nie przeprowadzono kontroli uprawnień
luckperms.command.verbose.command.possibly-async=Może to być spowodowane tym, że wtyczka uruchamia polecenia w tle (async)
luckperms.command.verbose.command.try-again-manually=Nadal możesz ręcznie użyć szczegółowych informacji, aby wykryć takie kontrole
luckperms.command.verbose.enabled-recording=Zacznij rejestrować {0} w celu sprawdzenia pasującego do {1}
luckperms.command.verbose.uploading=Rejestrowanie {0}, przesyłanie wyników...
luckperms.command.verbose.url=Adres URL wyników
luckperms.command.verbose.enabled-term=włączone
luckperms.command.verbose.disabled-term=wyłączone
luckperms.command.verbose.query-any=WSZYSTKO
luckperms.command.info.running-plugin=W działaniu
luckperms.command.info.platform-key=Platforma
luckperms.command.info.server-brand-key=Marka Serwera
luckperms.command.info.server-version-key=Wersja serwera
luckperms.command.info.storage-key=Pamięć
luckperms.command.info.storage-type-key=Typ
luckperms.command.info.storage.meta.split-types-key=Typy
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Połączono
luckperms.command.info.storage.meta.file-size-key=Rozmiar pliku
luckperms.command.info.extensions-key=Rozszerzenia
luckperms.command.info.messaging-key=Komunikacja
luckperms.command.info.instance-key=Instancja
luckperms.command.info.static-contexts-key=Kontekst statyczny
luckperms.command.info.online-players-key=Gracze Online
luckperms.command.info.online-players-unique={0} unikalnych
luckperms.command.info.uptime-key=Czas pracy
luckperms.command.info.local-data-key=Dane Lokalne
luckperms.command.info.local-data={0} użytkowników, {1} grupy, {2} ścieżki
luckperms.command.generic.create.success={0} utworzono pomyślnie
luckperms.command.generic.create.error=Wystąpił błąd podczas tworzenia {0}
luckperms.command.generic.create.error-already-exists={0} już istnieje\!
luckperms.command.generic.delete.success={0} usunięto pomyślnie
luckperms.command.generic.delete.error=Wystąpił błąd podczas usuwania {0}
luckperms.command.generic.delete.error-doesnt-exist={0} nie istnieje\!
luckperms.command.generic.rename.success=Nazwa {0} została zmieniona na {1}
luckperms.command.generic.clone.success={0} została pomyślnie sklonowana na {1}
luckperms.command.generic.info.parent.title=Grupy nadrzędne
luckperms.command.generic.info.parent.temporary-title=Tymczasowe Grupy Nadrzędne
luckperms.command.generic.info.expires-in=wygasa za
luckperms.command.generic.info.inherited-from=odziedziczone z
luckperms.command.generic.info.inherited-from-self=siebie
luckperms.command.generic.show-tracks.title=Ścieżki {0}
luckperms.command.generic.show-tracks.empty={0} nie jest na żadnej ścieżce
luckperms.command.generic.clear.node-removed={0} elementów zostało usunięte
luckperms.command.generic.clear.node-removed-singular={0} element został usunięty
luckperms.command.generic.clear=usunięto elementy {0} w kontekście {1}
luckperms.command.generic.permission.info.title=Uprawnienia {0}
luckperms.command.generic.permission.info.empty={0} nie posiada ustawionych uprawnień
luckperms.command.generic.permission.info.click-to-remove=Kliknij, aby usunąć ten ten element z {0}
luckperms.command.generic.permission.check.info.title=Informacje o uprawnieniach dla {0}
luckperms.command.generic.permission.check.info.directly={0} posiada uprawnienie {1} ustawione na {2} w kontekście {3}
luckperms.command.generic.permission.check.info.inherited={0} dziedziczy {1} ustawione na {2} z {3} w kontekście {4}
luckperms.command.generic.permission.check.info.not-directly={0} nie posiada ustawionego {1}
luckperms.command.generic.permission.check.info.not-inherited={0} nie dziedziczy {1}
luckperms.command.generic.permission.check.result.title=Sprawdzanie uprawnień dla {0}
luckperms.command.generic.permission.check.result.result-key=Rezultat
luckperms.command.generic.permission.check.result.processor-key=Procesor
luckperms.command.generic.permission.check.result.cause-key=Przyczyna
luckperms.command.generic.permission.check.result.context-key=Kontekst
luckperms.command.generic.permission.set=Ustawiono {0} na {1} dla {2} w kontekście {3}
luckperms.command.generic.permission.already-has={0} już posiada {1} ustawiony w kontekście {2}
luckperms.command.generic.permission.set-temp=Ustaw {0} na {1} dla {2} na okres {3} w kontekście {4}
luckperms.command.generic.permission.already-has-temp={0} już posiada {1} ustawiony tymczasowo w kontekście {2}
luckperms.command.generic.permission.unset=Usunięto {0} dla {1} w kontekście {2}
luckperms.command.generic.permission.doesnt-have={0} nie posiada ustawionego {1} w kontekście {2}
luckperms.command.generic.permission.unset-temp=Usunięto tymczasowe uprawnienie {0} dla {1} w kontekście {2}
luckperms.command.generic.permission.subtract=Ustawiono {0} na {1} dla {2} na czas {3} w kontekście {4}, {5} mniej niż wcześniej
luckperms.command.generic.permission.doesnt-have-temp={0} nie ma ustawionego czasowo {1} w kontekście {2}
luckperms.command.generic.permission.clear=Uprawnienia {0} zostały wyczyszczone w kontekście {1}
luckperms.command.generic.parent.info.title=Rodzice {0}
luckperms.command.generic.parent.info.empty={0} nie ma zdefiniowanego żadnego rodzica
luckperms.command.generic.parent.info.click-to-remove=Kliknij aby usunąć tego rodzica z {0}
luckperms.command.generic.parent.add={0} od teraz dziedziczy uprawnienia z {1} w kontekście {2}
luckperms.command.generic.parent.add-temp={0} od teraz dziedziczy uprawnienia z {1} na czas {2} w kontekście {3}
luckperms.command.generic.parent.set=Wyczyszczono rodziców {0}, od teraz dziedziczy tylko z {1} w kontekście {2}
luckperms.command.generic.parent.set-track=Wyczyszczono rodziców {0} na torze {1}, od teraz dziedziczy tylko z {2} w kontekście {3}
luckperms.command.generic.parent.remove={0} nie dziedziczy już uprawnień z {1} w kontekście {2}
luckperms.command.generic.parent.remove-temp={0} nie dziedziczy już tymczasowych uprawnień z {1} w kontekście {2}
luckperms.command.generic.parent.subtract={0} odziedziczy uprawnienia z {1} na czas {2} w kontekście {3}, {4} krócej niż wcześniej
luckperms.command.generic.parent.clear=Rodzice {0} zostali wyczyszczeni w kontekście {1}
luckperms.command.generic.parent.clear-track=Rodzice {0} na ścieżce {1} zostali wyczyszczeni w kontekście {2}
luckperms.command.generic.parent.already-inherits={0} już dziedziczy z {1} w kontekście {2}
luckperms.command.generic.parent.doesnt-inherit={0} nie dziedziczy z {1} w kontekście {2}
luckperms.command.generic.parent.already-temp-inherits={0} już dziedziczy tymczasowo z {1} w kontekście {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} nie dziedziczy tymczasowo z {1} w kontekście {2}
luckperms.command.generic.chat-meta.info.title-prefix=Przedrostki {0}
luckperms.command.generic.chat-meta.info.title-suffix=Przyrostki {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} nie ma przedrostków
luckperms.command.generic.chat-meta.info.none-suffix={0} nie ma przyrostków
luckperms.command.generic.chat-meta.info.click-to-remove=Kliknij, aby usunąć {0} z {1}
luckperms.command.generic.chat-meta.already-has={0} już ma ustawione {1} {2} z priorytetem {3} w kontekście {4}
luckperms.command.generic.chat-meta.already-has-temp={0} już ma ustawione tymczasowe {1} {2} z priorytetem {3} w kontekście {4}
luckperms.command.generic.chat-meta.doesnt-have={0} nie ma ustawionego {1} {2} z priorytetem {3} w kontekście {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} nie ma ustawionego tymczasowego {1} {2} z priorytetem {3} w kontekście {4}
luckperms.command.generic.chat-meta.add={0} ma ustawione {1} {2} z priorytetem {3} w kontekście {4}
luckperms.command.generic.chat-meta.add-temp={0} ma ustawione tymczasowe {1} {2} z priorytetem {3} na czas {4} w kontekście {5}
luckperms.command.generic.chat-meta.remove=Usunięto {1} {2} z priorytetem {3} w kontekście {4} dla {0}
luckperms.command.generic.chat-meta.remove-bulk=Usunięto wszystkie {1} z priorytetem {2} w kontekście {3} dla {0}
luckperms.command.generic.chat-meta.remove-temp=Usunięto tymczasowe {1} {2} z priorytetem {3} w kontekście {4} dla {0}
luckperms.command.generic.chat-meta.remove-temp-bulk=Usunięto wszystkie tymczasowe {1} z priorytetem {2} w kontekście {3} dla {0}
luckperms.command.generic.meta.info.title=Metadata {0}
luckperms.command.generic.meta.info.none={0} nie ma metadanych
luckperms.command.generic.meta.info.click-to-remove=Kliknij aby usunąć tą metadatę z {0}
luckperms.command.generic.meta.already-has={0} już ma ustawiony meta-klucz {1} z wartością {2} w kontekście {3}
luckperms.command.generic.meta.already-has-temp={0} już ma ustawiony tymczasowy meta-klucz {1} z wartością {2} w kontekście {3}
luckperms.command.generic.meta.doesnt-have={0} nie posiada meta-klucza {1} w kontekście {2}
luckperms.command.generic.meta.doesnt-have-temp={0} nie posiada tymczasowego meta-klucza {1} w kontekście {2}
luckperms.command.generic.meta.set=Ustawiono klucz meta {0} na {1} dla {2} w kontekście {3}
luckperms.command.generic.meta.set-temp=Ustawiono klucz meta {0} na {1} dla {2} na czas {3} w kontekście {4}
luckperms.command.generic.meta.unset=Usunięto meta-klucz {0} dla {1} w kontekście {2}
luckperms.command.generic.meta.unset-temp=Usunięto tymczasowy meta-klucz {0} dla {1} w kontekście {2}
luckperms.command.generic.meta.clear=Metadata pasująca do {1} została wyczyszczona w kontekście {2} dla {0}
luckperms.command.generic.contextual-data.title=Dane kontekstowe
luckperms.command.generic.contextual-data.mode.key=tryb
luckperms.command.generic.contextual-data.mode.server=serwer
luckperms.command.generic.contextual-data.mode.active-player=aktywny gracz
luckperms.command.generic.contextual-data.contexts-key=Konteksty
luckperms.command.generic.contextual-data.prefix-key=Przedrostek
luckperms.command.generic.contextual-data.suffix-key=Przyrostek
luckperms.command.generic.contextual-data.primary-group-key=Grupa Podstawowa
luckperms.command.generic.contextual-data.meta-key=Metadane
luckperms.command.generic.contextual-data.null-result=Brak
luckperms.command.user.info.title=Informacje o użytkowniku
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=typ
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Nie można usunąć użytkownika z jego grupy podstawowej
luckperms.command.user.primarygroup.not-member={0} nie był członkiem {1}, dodawanie
luckperms.command.user.primarygroup.already-has={0} już posiada {1} ustawiony jako podstawowa grupa
luckperms.command.user.primarygroup.warn-option=Ostrzeżenie\: Podstawowa metoda obliczania grupy używana przez ten serwer ({0}) może nie odzwierciedlać tej zmiany
luckperms.command.user.primarygroup.set=Podstawowa grupa {0} została ustawiona na {1}
luckperms.command.user.track.error-not-contain-group={0} nie jest jeszcze w żadnej grupie w {1}
luckperms.command.user.track.unsure-which-track=Nie można określić ścieżki, sprecyzuj ją jako argument
luckperms.command.user.track.missing-group-advice=Utwórz grupę lub usuń ją ze ścieżki i spróbuj ponownie
luckperms.command.user.promote.added-to-first={0} nie jest w żadnej grupie w {1}, więc został dodany do pierwszej grupy, {2} w kontekście {3}
luckperms.command.user.promote.not-on-track={0} nie jest w żadnej grupie w {1}, więc nie został awansowany
luckperms.command.user.promote.success=Awansowanie {0} wzdłuż ścieżki {1} z {2} do {3} w kontekście {4}
luckperms.command.user.promote.end-of-track=Osiągnięto koniec ścieżki {0}, nie można awansować {1}
luckperms.command.user.promote.next-group-deleted=Następna grupa w ścieżce, {0}, już nie istnieje
luckperms.command.user.promote.unable-to-promote=Nie można awansować użytkownika
luckperms.command.user.demote.success=Degradacja {0} wzdłuż ścieżki {1} z {2} do {3} w kontekście {4}
luckperms.command.user.demote.end-of-track=Koniec ścieżki {0} został osiągnięty, więc {1} został usunięty z {2}
luckperms.command.user.demote.end-of-track-not-removed=Osiągnięto koniec ścieżki {0}, ale {1} nie został usunięty z pierwszej grupy
luckperms.command.user.demote.previous-group-deleted=Poprzednia grupa w ścieżce, {0}, już nie istnieje
luckperms.command.user.demote.unable-to-demote=Nie można zdegradować użytkownika
luckperms.command.group.list.title=Grupy
luckperms.command.group.delete.not-default=Nie możesz usunąć domyślnej grupy
luckperms.command.group.info.title=Informacje o grupie
luckperms.command.group.info.display-name-key=Nazwa wyświetlana
luckperms.command.group.info.weight-key=Waga
luckperms.command.group.setweight.set=Ustawiono wagę na {0} dla grupy {1}
luckperms.command.group.setdisplayname.doesnt-have={0} nie ma ustawionej nazwy wyświetlania
luckperms.command.group.setdisplayname.already-has={0} ma już nazwę wyświetlaną {1}
luckperms.command.group.setdisplayname.already-in-use=Wyświetlana nazwa {0} jest już używana przez {1}
luckperms.command.group.setdisplayname.set=Ustaw nazwę wyświetlaną na {0} dla grupy {1} w kontekście {2}
luckperms.command.group.setdisplayname.removed=Usunięto nazwę wyświetlaną grupy {0} w kontekście {1}
luckperms.command.track.list.title=Ścieżki
luckperms.command.track.path.empty=Brak
luckperms.command.track.info.showing-track=Pokazywanie ścieżki
luckperms.command.track.info.path-property=Ścieżka
luckperms.command.track.clear=Ścieżka grupy {0} została usunięta
luckperms.command.track.append.success=Grupa {0} została dołączona do ścieżki {1}
luckperms.command.track.insert.success=Grupa {0} została wstawiona do ścieżki {1} na pozycji {2}
luckperms.command.track.insert.error-number=Oczekiwana liczba, a zamiast tego odebrana\: {0}
luckperms.command.track.insert.error-invalid-pos=Nie można wstawić na pozycję {0}
luckperms.command.track.insert.error-invalid-pos-reason=nieprawidłowa pozycja
luckperms.command.track.remove.success=Grupa {0} została usunięta ze ścieżki {1}
luckperms.command.track.error-empty={0} nie może być użyty ponieważ jest pusty lub zawiera tylko jedną grupę
luckperms.command.track.error-multiple-groups={0} jest członkiem wielu grup na tej ścieżce
luckperms.command.track.error-ambiguous=Nie można określić ich lokalizacji
luckperms.command.track.already-contains={0} już zawiera {1}
luckperms.command.track.doesnt-contain={0} nie zawiera {1}
luckperms.command.log.load-error=Nie można załadować wpisu
luckperms.command.log.invalid-page=Nieprawidłowy numer strony
luckperms.command.log.invalid-page-range=Wprowadź wartość pomiędzy {0} a {1}
luckperms.command.log.empty=Brak wpisów do wyświetlenia
luckperms.command.log.notify.error-console=Nie można przełączyć powiadomień dla konsoli
luckperms.command.log.notify.enabled-term=Włączono
luckperms.command.log.notify.disabled-term=Wyłączono
luckperms.command.log.notify.changed-state={0} dane wyjściowe logowania
luckperms.command.log.notify.already-on=Otrzymujesz już powiadomienia
luckperms.command.log.notify.already-off=Obecnie nie otrzymujesz powiadomień
luckperms.command.log.notify.invalid-state=Stan nieznany. Oczekiwanie {0} lub {1}
luckperms.command.log.show.search=Wyświetlanie ostatnich działań dla zapytania {0}
luckperms.command.log.show.recent=Pokazuje ostatnie działania
luckperms.command.log.show.by=Wyświetlanie ostatnich działań przez {0}
luckperms.command.log.show.history=Wyświetlanie historii dla {0} {1}
luckperms.command.export.error-term=Błąd
luckperms.command.export.already-running=Inny proces eksportu jest już uruchomiony
luckperms.command.export.file.already-exists=Plik {0} już istnieje
luckperms.command.export.file.not-writable=Plik {0} nie jest zapisywalny
luckperms.command.export.file.success=Pomyślnie wyeksportowano do {0}
luckperms.command.export.file-unexpected-error-writing=Wystąpił nieoczekiwany błąd podczas zapisywania piku
luckperms.command.export.web.export-code=Eksportuj kod
luckperms.command.export.web.import-command-description=Użyj następującej komendy aby zimportować 
luckperms.command.import.term=Importuj
luckperms.command.import.error-term=Błąd
luckperms.command.import.already-running=Inny proces importu jest już uruchomiony
luckperms.command.import.file.doesnt-exist=Plik {0} nie istnieje
luckperms.command.import.file.not-readable=Plik {0} nie może być odczytany
luckperms.command.import.file.unexpected-error-reading=Wystąpił nieoczekiwany błąd podczas odczytywania importowanego pliku
luckperms.command.import.file.correct-format=czy jest on w prawidłowym formacie?
luckperms.command.import.web.unable-to-read=Nie można odczytać danych używając podanego kodu
luckperms.command.import.progress.percent=Ukończono {0}%
luckperms.command.import.progress.operations=Zakończono {0}/{1} operacji
luckperms.command.import.starting=Rozpoczynanie procesu importu
luckperms.command.import.completed=ZAKOŃCZONO
luckperms.command.import.duration=zajęło {0} sekund
luckperms.command.bulkupdate.must-use-console=Komenda masowej aktualizacji może być użyta tylko z konsoli
luckperms.command.bulkupdate.invalid-data-type=Nieprawidłowy typ, oczekiwano {0}
luckperms.command.bulkupdate.invalid-constraint=Nieprawidłowe ograniczenie {0}
luckperms.command.bulkupdate.invalid-constraint-format=Ograniczenia powinny być w formacie {0}
luckperms.command.bulkupdate.invalid-comparison=Nieprawidłowy operator porównania {0}
luckperms.command.bulkupdate.invalid-comparison-format=Oczekiwano jednego z następujących\: {0}
luckperms.command.bulkupdate.queued=Masowa operacja aktualizacji została umieszczona w kolejce
luckperms.command.bulkupdate.confirm=Wpisz {0} aby wykonać aktualizację
luckperms.command.bulkupdate.unknown-id=Operacja o id {0} nie istnieje lub wygasła
luckperms.command.bulkupdate.starting=Uruchamianie aktualizacji zbiorczej
luckperms.command.bulkupdate.success=Aktualizacja zbiorcza zakończona pomyślnie
luckperms.command.bulkupdate.success.statistics.nodes=Łącznie dotknięte węzły
luckperms.command.bulkupdate.success.statistics.users=Łącznie dotkniętych użytkowników 
luckperms.command.bulkupdate.success.statistics.groups=Łącznie dotkniętych grup
luckperms.command.bulkupdate.failure=Aktualizacja zbiorcza nie powiodła się, sprawdź konsolę pod kątem błędów
luckperms.command.update-task.request=Zażądano zadania aktualizacji, proszę czekać
luckperms.command.update-task.complete=Aktualizacja zakończona
luckperms.command.update-task.push.attempting=Próba przekazania zmian innym serwerom
luckperms.command.update-task.push.complete=Inne serwery zostały pomyślnie powiadomione przez {0}
luckperms.command.update-task.push.error=Błąd podczas wprowadzania zmian na inne serwery
luckperms.command.update-task.push.error-not-setup=Nie można przesłać zmian do innych serwerów ponieważ usługa wysyłania wiadomości nie została skonfigurowana
luckperms.command.reload-config.success=Plik konfiguracyjny został przeładowany
luckperms.command.reload-config.restart-note=niektóre opcje zostaną zastosowane dopiero po ponownym uruchomieniu serwera
luckperms.command.translations.searching=Wyszukiwanie dostępnych tłumaczeń, proszę czekać...
luckperms.command.translations.searching-error=Nie można uzyskać listy dostępnych tłumaczeń
luckperms.command.translations.installed-translations=Zainstalowane tłumaczenia
luckperms.command.translations.available-translations=Dostępne Tłumaczenia
luckperms.command.translations.percent-translated=Przetłumaczone w {0}%
luckperms.command.translations.translations-by=przez
luckperms.command.translations.installing=Instalacja tłumaczeń, proszę czekać...
luckperms.command.translations.download-error=Nie można pobrać tłumaczenia dla {0}
luckperms.command.translations.installing-specific=Instalowanie języka {0}...
luckperms.command.translations.install-complete=Instalacja zakończona
luckperms.command.translations.download-prompt=Użyj {0} aby pobrać i zainstalować aktualne wersje tych tłumaczeń dostarczone przez społeczność
luckperms.command.translations.download-override-warning=Pamiętaj, że to nadpisze wszelkie zmiany wprowadzone dla tych języków
luckperms.usage.user.description=Zestaw poleceń do zarządzania użytkownikami w ramach LuckPerms. (''Użytkownik'' w LuckPerms jest tylko graczem, i może odnosić się do UUID lub nazwy użytkownika)
luckperms.usage.group.description=Zestaw komend do zarządzania grupami w ramach LuckPerms. Grupy są tylko kolekcjami uprawnień które mogą być przekazywane użytkownikom. Nowe grupy są tworzone za pomocą komendy ''creategroup''.
luckperms.usage.track.description=Zestaw komend do zarządzania utworami w LuckPerms. Ścieżki są uporządkowaną kolekcją grup, która może być użyta do definiowania promocji i demoacji.
luckperms.usage.log.description=Zestaw komend do zarządzania funkcją logowania w LuckPerms.
luckperms.usage.sync.description=Przeładuje wszystkie dane z pamięci wtyczek do pamięci i wprowadza wszelkie wykryte zmiany.
luckperms.usage.info.description=Wyświetla ogólne informacje o aktywnej instancji wtyczki.
luckperms.usage.editor.description=Tworzy nową sesję edytora sieci web
luckperms.usage.editor.argument.type=typy do załadowania do edytora. (''wszystkie'', ''użytkownicy'' lub ''grupy'')
luckperms.usage.editor.argument.filter=uprawnienie według którego będą filtrowani gracze
luckperms.usage.verbose.description=Kontroluje system werbowania sprawdzania uprawnień pluginów.
luckperms.usage.verbose.argument.action=włączyć/wyłączyć logowanie, czy przesłać logowane dane wyjściowe
luckperms.usage.verbose.argument.filter=filtr do dopasowania wpisów
luckperms.usage.verbose.argument.commandas=gracz/polecenie do wykonania
luckperms.usage.tree.description=Generuje widok drzewa (uporządkowana hierarchia listy) wszystkich uprawnień znanych LuckPerms.
luckperms.usage.tree.argument.scope=główny pierwiastek drzewa. określ "." aby uwzględnić wszystkie uprawnienia
luckperms.usage.tree.argument.player=imię i nazwisko gracza online do sprawdzenia
luckperms.usage.search.description=Wyszukuje wszystkich użytkowników/grup z określonym uprawnieniem
luckperms.usage.search.argument.permission=uprawnienie do szukania
luckperms.usage.search.argument.page=strona do wyświetlenia
luckperms.usage.network-sync.description=Synchronizuj zmiany z pamięcią i poproś o to, aby wszystkie pozostałe serwery w sieci zrobiły to samo
luckperms.usage.import.description=Importuj dane z (wcześniej utworzonego) pliku eksportu
luckperms.usage.import.argument.file=plik z którego ma zostać wykonany import
luckperms.usage.import.argument.replace=zastąp istniejące dane zamiast łączenia
luckperms.usage.import.argument.upload=prześlij dane z poprzedniego eksportu
luckperms.usage.export.description=Eksportuje wszystkie dane uprawnień do pliku ''eksport''. Może być ponownie zaimportowany w późniejszym czasie.
luckperms.usage.export.argument.file=plik do którego ma zostać wykonany eksport
luckperms.usage.export.argument.without-users=wyklucz graczy z eksportu
luckperms.usage.export.argument.without-groups=wyklucz grupy z eksportu
luckperms.usage.export.argument.upload=Eksportuje wszystkie dane uprawnień do pliku ''eksport''. Może być ponownie zaimportowany w późniejszym czasie.
luckperms.usage.reload-config.description=Przeładuj część konfiguracji
luckperms.usage.bulk-update.description=Wykonaj masową zmianę zapytania na wszystkich danych
luckperms.usage.bulk-update.argument.data-type=typ może być zmienny (''wszystko'', ''użytkownicy'' lub ''grupy'')
luckperms.usage.bulk-update.argument.action=akcja to przetwarzania danych. (''zaktualizuj'' lub ''usuń'')
luckperms.usage.bulk-update.argument.action-field=pole do działania. Wymagane do ''aktualizacji''. (''permisje'', ''serwer'' lub ''świat'')
luckperms.usage.bulk-update.argument.action-value=wartość do zastąpienia. Wymagana jest tylko dla ''aktualizacji''.
luckperms.usage.bulk-update.argument.constraint=ograniczenia wymagane dla aktualizacji
luckperms.usage.translations.description=Zarządzaj tłumaczeniami
luckperms.usage.translations.argument.install=podkomenda do instalacji tłumaczeń
luckperms.usage.apply-edits.description=Zastosuj zmienione permisje zrobione w edytorze web
luckperms.usage.apply-edits.argument.code=unikalny kod dla danych
luckperms.usage.apply-edits.argument.target=dla kogo należy zastosować dane
luckperms.usage.create-group.description=Utwórz nową grupę
luckperms.usage.create-group.argument.name=nazwa grupy
luckperms.usage.create-group.argument.weight=waga grupy
luckperms.usage.create-group.argument.display-name=nazwa wyświetlana grupy
luckperms.usage.delete-group.description=Usuń grupę
luckperms.usage.delete-group.argument.name=nazwa grupy
luckperms.usage.list-groups.description=Wyświetl wszystkie grupy na platformie
luckperms.usage.create-track.description=Utwórz nową ścieżkę
luckperms.usage.create-track.argument.name=nazwa ścieżki
luckperms.usage.delete-track.description=Usuń ścieżkę
luckperms.usage.delete-track.argument.name=nazwa ścieżki
luckperms.usage.list-tracks.description=Wyświetl wszystkie ścieżki na platformie
luckperms.usage.user-info.description=Wyświetla informacje o użytkowniku
luckperms.usage.user-switchprimarygroup.description=Przełącza podstawową grupę użytkownika
luckperms.usage.user-switchprimarygroup.argument.group=grupa do przełączenia
luckperms.usage.user-promote.description=Promuje użytkownika w ścieżce
luckperms.usage.user-promote.argument.track=ścieżka w której użytkownik ma awansować
luckperms.usage.user-promote.argument.context=konteksty do awansowania użytkownika w
luckperms.usage.user-promote.argument.dont-add-to-first=promuj użytkownika tylko wtedy, gdy jest już na ścieżce
luckperms.usage.user-demote.description=Degraduje użytkownika w ścieżce
luckperms.usage.user-demote.argument.track=świeżka w której użytkownik ma zostać zdegradowany
luckperms.usage.user-demote.argument.context=konteksty do zdegradowania użytkownika w
luckperms.usage.user-demote.argument.dont-remove-from-first=zapobiegaj usunięciu użytkownika z pierwszej grupy
luckperms.usage.user-clone.description=Klonuj użytkownika
luckperms.usage.user-clone.argument.user=nazwa/uuid użytkownika do sklonowania
luckperms.usage.group-info.description=Wyświetla informacje na temat grupy
luckperms.usage.group-listmembers.description=Pokaż użytkowników/grupy, które dziedziczą z tej grupy
luckperms.usage.group-listmembers.argument.page=strona do wyświetlenia
luckperms.usage.group-setweight.description=Ustaw wagę grupy
luckperms.usage.group-setweight.argument.weight=waga jaka ma zostać ustawiona
luckperms.usage.group-set-display-name.description=Ustaw wyświetlaną nazwę grupy
luckperms.usage.group-set-display-name.argument.name=nazwa do ustawienia
luckperms.usage.group-set-display-name.argument.context=kontekst w którym zostanie ustawiona nazwa
luckperms.usage.group-rename.description=Zmień nazwę grupy
luckperms.usage.group-rename.argument.name=nowa nazwa
luckperms.usage.group-clone.description=Klonuj grupę
luckperms.usage.group-clone.argument.name=nazwa klona grupy
luckperms.usage.holder-editor.description=Otwiera zewnętrzny edytor uprawnień
luckperms.usage.holder-showtracks.description=Wyświetla ścieżki, na których jest obiekt
luckperms.usage.holder-clear.description=Usuwa wszystkie uprawnienia, rodziców i metę
luckperms.usage.holder-clear.argument.context=kontekst do filtrowania
luckperms.usage.permission.description=Edytuj uprawnienia
luckperms.usage.parent.description=Edytuj dziedziczenie
luckperms.usage.meta.description=Edytuj metadane
luckperms.usage.permission-info.description=Lista węzłów uprawnień, które posiada obiekt
luckperms.usage.permission-info.argument.page=strona do oglądania
luckperms.usage.permission-info.argument.sort-mode=jak sortować wpisy
luckperms.usage.permission-set.description=Ustawia uprawnienie dla obiektu
luckperms.usage.permission-set.argument.node=uprawnienie do ustawienia
luckperms.usage.permission-set.argument.value=wartość węzła
luckperms.usage.permission-set.argument.context=kontekst w którym zostanie dodane uprawnienie
luckperms.usage.permission-unset.description=Usuwa uprawnienie dla obiektu
luckperms.usage.permission-unset.argument.node=węzeł uprawnień do usunięcia
luckperms.usage.permission-unset.argument.context=kontekst, w jakim uprawnienia mają zostać usunięte
luckperms.usage.permission-settemp.description=Tymczasowo nadaje uprawnienia
luckperms.usage.permission-settemp.argument.node=uprawnienie do ustawienia
luckperms.usage.permission-settemp.argument.value=wartość węzła
luckperms.usage.permission-settemp.argument.duration=czas do wygaśnięcia węzła uprawnień
luckperms.usage.permission-settemp.argument.temporary-modifier=w jaki sposób należy stosować uprawnienia tymczasowe
luckperms.usage.permission-settemp.argument.context=warunki, jakie mają zajść, by uprawnienie zostało dodane
luckperms.usage.permission-unsettemp.description=Anuluje tymczasowe uprawnienie dla obiektu
luckperms.usage.permission-unsettemp.argument.node=uprawnienie do usunięcia
luckperms.usage.permission-unsettemp.argument.duration=czas trwania do odjęcia
luckperms.usage.permission-unsettemp.argument.context=kontekst w którym zostanie usunięte uprawnienie
luckperms.usage.permission-check.description=Sprawdza, czy obiekt ma pewien węzeł uprawnień
luckperms.usage.permission-check.argument.node=uprawnienie do sprawdzenia
luckperms.usage.permission-clear.description=Czyści wszystkie uprawnienia
luckperms.usage.permission-clear.argument.context=kontekst do filtrowania
luckperms.usage.parent-info.description=Wypisuje grupy z których ten obiekt dziedziczy
luckperms.usage.parent-info.argument.page=strona do wyświetlenia
luckperms.usage.parent-info.argument.sort-mode=jak sortować wpisy
luckperms.usage.parent-set.description=Usuwa wszystkie inne grupy, które obiekt już dziedziczy i dodaje je do podanego
luckperms.usage.parent-set.argument.group=grupa do ustawiania
luckperms.usage.parent-set.argument.context=konteksty do ustawiania grupy
luckperms.usage.parent-add.description=Dodaje grupę do dziedziczenia uprawnień dla danego obiektu
luckperms.usage.parent-add.argument.group=grupa do dziedziczenia
luckperms.usage.parent-add.argument.context=kontekst dziedziczenia grupy
luckperms.usage.parent-remove.description=Usuwa poprzednio ustawioną regułę dziedziczenia
luckperms.usage.parent-remove.argument.group=grupa do usunięcia
luckperms.usage.parent-remove.argument.context=konteksty do usunięcia grupy
luckperms.usage.parent-set-track.description=Usuwa pozostałe grupy z których dziedziczy obiekt na tej scieżce i dodaje podane
luckperms.usage.parent-set-track.argument.track=ścieżka ustawiona na
luckperms.usage.parent-set-track.argument.group=grupa do ustawienia, lub numer odnoszący się do pozycji grupy na danej ścieżce
luckperms.usage.parent-set-track.argument.context=konteksty do ustawiania grupy
luckperms.usage.parent-add-temp.description=Dodaje grupę do tymczasowego dziedziczenia uprawnień dla danego obiektu
luckperms.usage.parent-add-temp.argument.group=grupa dziedziczy z
luckperms.usage.parent-add-temp.argument.duration=czas trwania członkostwa w grupie
luckperms.usage.parent-add-temp.argument.temporary-modifier=w jaki sposób należy stosować uprawnienia tymczasowe
luckperms.usage.parent-add-temp.argument.context=kontekst dziedziczenia grupy
luckperms.usage.parent-remove-temp.description=Usuwa wcześniej ustawioną tymczasową regułę dziedziczenia
luckperms.usage.parent-remove-temp.argument.group=grupa do usunięcia
luckperms.usage.parent-remove-temp.argument.duration=czas trwania do odjęcia
luckperms.usage.parent-remove-temp.argument.context=konteksty do usunięcia grupy
luckperms.usage.parent-clear.description=Usuwa wszystkich rodziców
luckperms.usage.parent-clear.argument.context=kontekst do filtrowania
luckperms.usage.parent-clear-track.description=Usuwa wszystkich rodziców na danej ścieżce
luckperms.usage.parent-clear-track.argument.track=ścieżka do usunięcia na
luckperms.usage.parent-clear-track.argument.context=konteksty do filtrowania przez
luckperms.usage.meta-info.description=Wyświetla wszystkie meta czatu
luckperms.usage.meta-set.description=Ustawia wartość meta
luckperms.usage.meta-set.argument.key=klucz do ustawienia
luckperms.usage.meta-set.argument.value=wartość do ustawienia
luckperms.usage.meta-set.argument.context=konteksty do dodania pary meta w
luckperms.usage.meta-unset.description=Usuwa wartość meta
luckperms.usage.meta-unset.argument.key=klucz do usunięcia
luckperms.usage.meta-unset.argument.context=konteksty do usunięcia pary meta w
luckperms.usage.meta-settemp.description=Ustawia wartość meta tymczasowo
luckperms.usage.meta-settemp.argument.key=klucz do ustawienia
luckperms.usage.meta-settemp.argument.value=wartość do ustawienia
luckperms.usage.meta-settemp.argument.duration=czas trwania do wygaśnięcia wartości meta
luckperms.usage.meta-settemp.argument.context=konteksty do dodania pary meta w
luckperms.usage.meta-unsettemp.description=Usuwa tymczasową wartość meta
luckperms.usage.meta-unsettemp.argument.key=klucz do usunięcia
luckperms.usage.meta-unsettemp.argument.context=konteksty do usunięcia pary meta w
luckperms.usage.meta-addprefix.description=Dodaje przedrostek
luckperms.usage.meta-addprefix.argument.priority=priorytet dodania prefiksu
luckperms.usage.meta-addprefix.argument.prefix=prefiks
luckperms.usage.meta-addprefix.argument.context=konteksty do dodania prefiksu w
luckperms.usage.meta-addsuffix.description=Dodaje przyrostek
luckperms.usage.meta-addsuffix.argument.priority=priorytet dodania suffiksu
luckperms.usage.meta-addsuffix.argument.suffix=sufiks
luckperms.usage.meta-addsuffix.argument.context=konteksty do dodania suffiksu w
luckperms.usage.meta-setprefix.description=Ustawia przedrostek
luckperms.usage.meta-setprefix.argument.priority=priorytet dodania prefiksu
luckperms.usage.meta-setprefix.argument.prefix=prefiks
luckperms.usage.meta-setprefix.argument.context=konteksty do ustawienia prefiksu w
luckperms.usage.meta-setsuffix.description=Ustawia przyrostek
luckperms.usage.meta-setsuffix.argument.priority=priorytet dodania suffiksu
luckperms.usage.meta-setsuffix.argument.suffix=sufiks
luckperms.usage.meta-setsuffix.argument.context=konteksty do ustawienia suffiksu w
luckperms.usage.meta-removeprefix.description=Usuwa przedrostek
luckperms.usage.meta-removeprefix.argument.priority=priorytet usunięcia prefiksu
luckperms.usage.meta-removeprefix.argument.prefix=prefiks
luckperms.usage.meta-removeprefix.argument.context=konteksty do usunięcia prefiksu w
luckperms.usage.meta-removesuffix.description=Usuwa przyrostek
luckperms.usage.meta-removesuffix.argument.priority=priorytet usunięcia suffiksu
luckperms.usage.meta-removesuffix.argument.suffix=sufiks
luckperms.usage.meta-removesuffix.argument.context=konteksty do usunięcia suffiksu w
luckperms.usage.meta-addtemp-prefix.description=Dodaje tymczasowo prefiks
luckperms.usage.meta-addtemp-prefix.argument.priority=priorytet dodania prefiksu
luckperms.usage.meta-addtemp-prefix.argument.prefix=prefiks
luckperms.usage.meta-addtemp-prefix.argument.duration=czas trwania do wygaśnięcia prefiksu
luckperms.usage.meta-addtemp-prefix.argument.context=konteksty do dodania prefiksu w
luckperms.usage.meta-addtemp-suffix.description=Dodaje sufiks tymczasowo
luckperms.usage.meta-addtemp-suffix.argument.priority=priorytet dodania suffiksu
luckperms.usage.meta-addtemp-suffix.argument.suffix=sufiks
luckperms.usage.meta-addtemp-suffix.argument.duration=czas do wygaśnięcia suffiksu
luckperms.usage.meta-addtemp-suffix.argument.context=konteksty do dodania suffiksu w
luckperms.usage.meta-settemp-prefix.description=Tymczasowo ustawia prefiks
luckperms.usage.meta-settemp-prefix.argument.priority=priorytet ustawienia prefiksu
luckperms.usage.meta-settemp-prefix.argument.prefix=prefiks
luckperms.usage.meta-settemp-prefix.argument.duration=czas do wygaśnięcia prefiksu
luckperms.usage.meta-settemp-prefix.argument.context=konteksty do ustawienia prefiksu w
luckperms.usage.meta-settemp-suffix.description=Tymczasowo ustawia przyrostek
luckperms.usage.meta-settemp-suffix.argument.priority=priorytet ustawienia suffiksu
luckperms.usage.meta-settemp-suffix.argument.suffix=sufiks
luckperms.usage.meta-settemp-suffix.argument.duration=czas do wygaśnięcia suffiksu
luckperms.usage.meta-settemp-suffix.argument.context=konteksty do ustawienia suffiksu w
luckperms.usage.meta-removetemp-prefix.description=Usuwa tymczasowy prefiks
luckperms.usage.meta-removetemp-prefix.argument.priority=priorytet usunięcia prefiksu
luckperms.usage.meta-removetemp-prefix.argument.prefix=prefiks
luckperms.usage.meta-removetemp-prefix.argument.context=konteksty do usunięcia prefiksu w
luckperms.usage.meta-removetemp-suffix.description=Usuwa tymczasowy sufiks
luckperms.usage.meta-removetemp-suffix.argument.priority=priorytet usunięcia suffiksu
luckperms.usage.meta-removetemp-suffix.argument.suffix=sufiks
luckperms.usage.meta-removetemp-suffix.argument.context=konteksty do usunięcia suffiksu w
luckperms.usage.meta-clear.description=Czyści wszystkie metadane
luckperms.usage.meta-clear.argument.type=typ meta do usunięcia
luckperms.usage.meta-clear.argument.context=kontekst do filtrowania
luckperms.usage.track-info.description=Wyświetla informacje na temat ścieżki
luckperms.usage.track-editor.description=Otwiera zewnętrzny edytor uprawnień
luckperms.usage.track-append.description=Dołącza grupę na końcu ścieżki
luckperms.usage.track-append.argument.group=grupa do dodania
luckperms.usage.track-insert.description=Wstawia grupę na danej pozycji wzdłuż ścieżki
luckperms.usage.track-insert.argument.group=grupa do wstawienia
luckperms.usage.track-insert.argument.position=pozycja do wstawienia grupy w (pierwsza pozycja na ścieżce wynosi 1)
luckperms.usage.track-remove.description=Usuwa grupę ze ścieżki
luckperms.usage.track-remove.argument.group=grupa do usunięcia
luckperms.usage.track-clear.description=Usuwa grupy w ścieżce
luckperms.usage.track-rename.description=Zmień nazwę ścieżki
luckperms.usage.track-rename.argument.name=nowa nazwa
luckperms.usage.track-clone.description=Klonuj ścieżkę
luckperms.usage.track-clone.argument.name=nazwa klona ścieżki
luckperms.usage.log-recent.description=Zobacz ostatnie działania
luckperms.usage.log-recent.argument.user=nazwa/uuid użytkownika do filtrowania
luckperms.usage.log-recent.argument.page=numer strony do wyświetlenia
luckperms.usage.log-search.description=Przeszukaj dziennik w poszukiwaniu wpisu
luckperms.usage.log-search.argument.query=zapytanie do wyszukania
luckperms.usage.log-search.argument.page=numer strony do wyświetlenia
luckperms.usage.log-notify.description=Przełącz powiadomienia dziennika
luckperms.usage.log-notify.argument.toggle=czy włączyć lub wyłączyć
luckperms.usage.log-user-history.description=Pokaż historię użytkownika
luckperms.usage.log-user-history.argument.user=nazwa/uuid użytkownika
luckperms.usage.log-user-history.argument.page=numer strony do wyświetlenia
luckperms.usage.log-group-history.description=Pokaż historię grupy
luckperms.usage.log-group-history.argument.group=nazwa grupy
luckperms.usage.log-group-history.argument.page=numer strony do wyświetlenia
luckperms.usage.log-track-history.description=Zobacz historię ścieżki
luckperms.usage.log-track-history.argument.track=nazwa ścieżki
luckperms.usage.log-track-history.argument.page=numer strony do wyświetlenia
luckperms.usage.sponge.description=Edytuj dodatkowe dane Sponge
luckperms.usage.sponge.argument.collection=kolekcja do zapytania
luckperms.usage.sponge.argument.subject=temat do modyfikacji
luckperms.usage.sponge-permission-info.description=Wyświetla informacje o uprawnieniach uczestnika
luckperms.usage.sponge-permission-info.argument.contexts=kontekst do filtrowania
luckperms.usage.sponge-permission-set.description=Ustawia uprawnienie dla tematu
luckperms.usage.sponge-permission-set.argument.node=uprawnienie do ustawienia
luckperms.usage.sponge-permission-set.argument.tristate=wartość do ustawienia uprawnienia
luckperms.usage.sponge-permission-set.argument.contexts=kontekst w którym zostanie ustawione uprawnienie
luckperms.usage.sponge-permission-clear.description=Czyści uprawnienia tematów
luckperms.usage.sponge-permission-clear.argument.contexts=konteksty do czyszczenia uprawnień w
luckperms.usage.sponge-parent-info.description=Wyświetla informacje o rodzicach uczestnika
luckperms.usage.sponge-parent-info.argument.contexts=kontekst do filtrowania
luckperms.usage.sponge-parent-add.description=Dodaje rodzica do tematu
luckperms.usage.sponge-parent-add.argument.collection=zbiór tematów, w którym jest temat nadrzędny
luckperms.usage.sponge-parent-add.argument.subject=nazwa nadrzędnego tematu
luckperms.usage.sponge-parent-add.argument.contexts=konteksty do dodania rodzica w
luckperms.usage.sponge-parent-remove.description=Usuwa rodzica z tematu
luckperms.usage.sponge-parent-remove.argument.collection=zbiór tematów, w którym jest temat nadrzędny
luckperms.usage.sponge-parent-remove.argument.subject=nazwa nadrzędnego tematu
luckperms.usage.sponge-parent-remove.argument.contexts=konteksty do usunięcia rodzica w
luckperms.usage.sponge-parent-clear.description=Czyści rodziców tematów
luckperms.usage.sponge-parent-clear.argument.contexts=konteksty do czyszczenia rodziców w
luckperms.usage.sponge-option-info.description=Wyświetla informacje o opcjach uczestnika
luckperms.usage.sponge-option-info.argument.contexts=kontekst do filtrowania
luckperms.usage.sponge-option-set.description=Ustawia opcję dla tematu
luckperms.usage.sponge-option-set.argument.key=klucz do ustawienia
luckperms.usage.sponge-option-set.argument.value=wartość do ustawienia klucza
luckperms.usage.sponge-option-set.argument.contexts=konteksty do ustawienia opcji w
luckperms.usage.sponge-option-unset.description=Cofa opcję dla tematu
luckperms.usage.sponge-option-unset.argument.key=klucz do usunięcia
luckperms.usage.sponge-option-unset.argument.contexts=konteksty, w których ma zostać usunięty klucz
luckperms.usage.sponge-option-clear.description=Czyści opcje tematów
luckperms.usage.sponge-option-clear.argument.contexts=konteksty do czyszczenia opcji w
