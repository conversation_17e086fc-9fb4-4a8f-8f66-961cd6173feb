luckperms.logs.actionlog-prefix=LOKI
luckperms.logs.verbose-prefix=YK
luckperms.logs.export-prefix=VIENTI
luckperms.commandsystem.available-commands=<PERSON><PERSON><PERSON><PERSON> {0} nähdäksesi saatavilla olevat komennot
luckperms.commandsystem.command-not-recognised=Komentoa ei tunnistettu
luckperms.commandsystem.no-permission=Sinulla ei ole oikeuksia käyttää tätä komentoa\!
luckperms.commandsystem.no-permission-subcommands=Sinulla ei ole oikeuksia käyttää alikomentoja
luckperms.commandsystem.already-executing-command=Toista komentoa suoritetaan, odotetaan sen valmistumista...
luckperms.commandsystem.usage.sub-commands-header=Alikomennot
luckperms.commandsystem.usage.usage-header=Komentojen käyttö
luckperms.commandsystem.usage.arguments-header=Argumentit
luckperms.first-time.no-permissions-setup=Näyttää siltä, että oikeuksia ei ole vielä määritetty\!
luckperms.first-time.use-console-to-give-access=Ennen kuin voit käyttää LuckPerms-komentoja pelissä, sinun täytyy antaa itsellesi siihen oikeudet konsolissa
luckperms.first-time.console-command-prompt=Avaa konsolisi ja suorita
luckperms.first-time.next-step=Kun olet tehnyt tämän, voit alkaa määrittelemään ryhmiä ja oikeuksia
luckperms.first-time.wiki-prompt=Etkö tiedä mistä aloittaa? Kurkkaa täältä\: {0}
luckperms.login.try-again=Yritä myöhemmin uudelleen
luckperms.login.loading-database-error=Tietokannassa tapahtui virhe käyttöoikeustietoja ladattaessa
luckperms.login.server-admin-check-console-errors=Jos olet palvelimen ylläpitäjä, tarkista konsoli mahdollisten virheiden varalta
luckperms.login.server-admin-check-console-info=Ole hyvä ja tarkista palvelimen konsoli saadaksesi lisätietoja
luckperms.login.data-not-loaded-at-pre=Käyttöoikeustietojasi ei ladattu esikirjautumis-vaiheen aikana
luckperms.login.unable-to-continue=ei voida jatkaa
luckperms.login.craftbukkit-offline-mode-error=tämä johtuu todennäköisesti ristiriidasta CraftBukkitin ja online-tilan asetusten välillä
luckperms.login.unexpected-error=Tapahtui odottamaton virhe määritettäessä käyttöoikeustietojasi
luckperms.opsystem.disabled=Vanilla OP oikeudet eivät ole käytössä tällä palvelimella
luckperms.opsystem.sponge-warning=Huomioithan, että palvelimen OP-oikeuksien tila ei vaikuta Sponge\:n käyttöoikeustarkastuksiin, kun käyttöoikeus-plugin on asennettu. Sinun täytyy muokata käyttäjän tietoja tiedostosta käsin
luckperms.duration.unit.years.plural={0} vuotta
luckperms.duration.unit.years.singular={0} vuosi
luckperms.duration.unit.years.short={0}v
luckperms.duration.unit.months.plural={0} kuukautta
luckperms.duration.unit.months.singular={0} kuukausi
luckperms.duration.unit.months.short={0}kk
luckperms.duration.unit.weeks.plural={0} viikkoa
luckperms.duration.unit.weeks.singular={0} viikko
luckperms.duration.unit.weeks.short={0}vi
luckperms.duration.unit.days.plural={0} päivää
luckperms.duration.unit.days.singular={0} päivä
luckperms.duration.unit.days.short={0}p
luckperms.duration.unit.hours.plural={0} tuntia
luckperms.duration.unit.hours.singular={0} tunti
luckperms.duration.unit.hours.short={0}t
luckperms.duration.unit.minutes.plural={0} minuuttia
luckperms.duration.unit.minutes.singular={0} minuutti
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} sekuntia
luckperms.duration.unit.seconds.singular={0} sekunti
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} sitten
luckperms.command.misc.invalid-code=Virheellinen koodi
luckperms.command.misc.response-code-key=vastauskoodi
luckperms.command.misc.error-message-key=viesti
luckperms.command.misc.bytebin-unable-to-communicate=Ei voitu kommunikoida bytebinin kanssa
luckperms.command.misc.webapp-unable-to-communicate=Kommunikointi web-sovelluksen kanssa epäonnistui
luckperms.command.misc.check-console-for-errors=Tarkista virheet konsolista
luckperms.command.misc.file-must-be-in-data=Tiedoston {0} on oltava data-kansiossa
luckperms.command.misc.wait-to-finish=Odota edellisen prosessin loppumista ja yritä sitten uudestaan
luckperms.command.misc.invalid-priority=Virheellinen prioriteetti {0}
luckperms.command.misc.expected-number=Oletettiin numeroa
luckperms.command.misc.date-parse-error=Päivämäärää {0} ei voitu parsia
luckperms.command.misc.date-in-past-error=Et voi asettaa päivämäärää menneisyyteen\!
luckperms.command.misc.page=sivu {0}/{1}
luckperms.command.misc.page-entries={0} tulosta
luckperms.command.misc.none=-
luckperms.command.misc.loading.error.unexpected=Tapahtui odottamaton virhe
luckperms.command.misc.loading.error.user=Käyttäjää ei ladattu
luckperms.command.misc.loading.error.user-specific=Kohdekäyttäjän {0} lataus ei onnistu
luckperms.command.misc.loading.error.user-not-found=Käyttäjää {0} ei löytynyt
luckperms.command.misc.loading.error.user-save-error=Käyttäjän {0} tietojen tallentamisessa tapahtui virhe
luckperms.command.misc.loading.error.user-not-online=Käyttäjä {0} ei ole paikalla
luckperms.command.misc.loading.error.user-invalid={0} ei ole kelvollinen käyttäjänimi tai uuid
luckperms.command.misc.loading.error.user-not-uuid=Kohdekäyttäjällä {0} ei ole kelvollista uuid-tunnusta
luckperms.command.misc.loading.error.group=Ryhmää ei ladattu
luckperms.command.misc.loading.error.all-groups=Kaikkia ryhmiä ei voitu ladata
luckperms.command.misc.loading.error.group-not-found=Ryhmää nimellä {0} ei löydetty
luckperms.command.misc.loading.error.group-save-error=Virhe tapahtui tallentassa ryhmän {0} dataa
luckperms.command.misc.loading.error.group-invalid={0} ei ole kelvollinen ryhmän nimi
luckperms.command.misc.loading.error.track=Polkua ei ladattu
luckperms.command.misc.loading.error.all-tracks=Kaikkia polkuja ei voitu ladata
luckperms.command.misc.loading.error.track-not-found=Polkua nimellä {0} ei löytynyt
luckperms.command.misc.loading.error.track-save-error=Virhe tallentaessa polun {0} tietoa
luckperms.command.misc.loading.error.track-invalid={0} ei ole kelvollinen polun nimi
luckperms.command.editor.no-match=Editoria ei voi avata\: yksikään objekti ei vastaa pyytämääsi tyyppiä
luckperms.command.editor.start=Valmistellaan uutta editori-istuntoa, odota hetki...
luckperms.command.editor.url=Klikkaa alla olevasta linkistä avataksesi editori
luckperms.command.editor.unable-to-communicate=Kommunikointi editorin kanssa epäonnistui
luckperms.command.editor.apply-edits.success=Editorin data lisätty {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} ja {2} {3}
luckperms.command.editor.apply-edits.success.additions=lisäystä
luckperms.command.editor.apply-edits.success.additions-singular=lisäys
luckperms.command.editor.apply-edits.success.deletions=poistoa
luckperms.command.editor.apply-edits.success.deletions-singular=poisto
luckperms.command.editor.apply-edits.no-changes=Web-editorista ei tehty muutoksia, palautetut tiedot eivät sisältänet muokkauksia
luckperms.command.editor.apply-edits.unknown-type=Muokkausta ei voida soveltaa määritettyyn objektityypiin
luckperms.command.editor.apply-edits.unable-to-read=Tietoja ei voi lukea annetulta koodilta
luckperms.command.search.searching.permission=Etsitään käyttäjiä ja ryhmiä, joilla on oikeus {0}
luckperms.command.search.searching.inherit=Etsitään käyttäjiä ja ryhmiä, jotka perivät ryhmän {0} oikeudet
luckperms.command.search.result=Löytyi {0} tulosta {1} käyttältä ja {2} ryhmältä
luckperms.command.search.result.default-notice=Huomaa\: Kun etsit oletusryhmän jäseniä, offline-pelaajia - joilla ei ole muita käyttöoikeuksia ei näytetä\!
luckperms.command.search.showing-users=Näytetään käyttäjätulokset
luckperms.command.search.showing-groups=Näytetään ryhmätulokset
luckperms.command.tree.start=Luodaan oikeuspuuta, odota...
luckperms.command.tree.empty=Puuta ei voitu luoda, tuloksia ei löytynyt
luckperms.command.tree.url=Oikeuspuun URL
luckperms.command.verbose.invalid-filter={0} ei ole kelvollinen yksityiskohtainen suodatin
luckperms.command.verbose.enabled=Yksityiskohtainen suodin on {0} hauille, jotka vastaavat {1}
luckperms.command.verbose.command-exec=Pakotetaan {0} suorittamaan komento {1} ja raportoidaan kaikista tehdyistä tarkistuksista...
luckperms.command.verbose.off=Yksityiskohtainen lokikirjaus {0}
luckperms.command.verbose.command-exec-complete=Komennon suoritus valmis
luckperms.command.verbose.command.no-checks=Komennon suoritus suoritettu, mutta käyttöoikeustarkastuksia ei tehty
luckperms.command.verbose.command.possibly-async=Tämä voi johtua siitä, että plugin suorittaa komentoja taustalla (async)
luckperms.command.verbose.command.try-again-manually=Voit edelleen käyttää verboosia manuaalisesti tämän kaltaisten tarkistusten havaitsemiseksi
luckperms.command.verbose.enabled-recording=Yksityiskohtainen lokitallennus on {0} tuloksille, jotka vastaavat {1}
luckperms.command.verbose.uploading=Yksityiskohtainen lokikirjaus {0}, lähetetään tuloksia...
luckperms.command.verbose.url=Yksityiskohtaisen suotimen URL-osoite
luckperms.command.verbose.enabled-term=käytössä
luckperms.command.verbose.disabled-term=poistettu käytöstä
luckperms.command.verbose.query-any=JOKIN
luckperms.command.info.running-plugin=Käytetään
luckperms.command.info.platform-key=Alusta
luckperms.command.info.server-brand-key=Palvelinbrändi
luckperms.command.info.server-version-key=Palvelimen versio
luckperms.command.info.storage-key=Tallennustila
luckperms.command.info.storage-type-key=Tyyppi
luckperms.command.info.storage.meta.split-types-key=Tyypit
luckperms.command.info.storage.meta.ping-key=Viive
luckperms.command.info.storage.meta.connected-key=Yhdistetty
luckperms.command.info.storage.meta.file-size-key=Tiedoston koko
luckperms.command.info.extensions-key=Laajennukset
luckperms.command.info.messaging-key=Viestintä
luckperms.command.info.instance-key=Instanssi
luckperms.command.info.static-contexts-key=Staattiset kontekstit
luckperms.command.info.online-players-key=Aktiiviset pelaajat
luckperms.command.info.online-players-unique={0} uniikki
luckperms.command.info.uptime-key=Käyttöaika
luckperms.command.info.local-data-key=Paikallinen data
luckperms.command.info.local-data={0} käyttäjää, {1} ryhmää, {2} polkua
luckperms.command.generic.create.success={0} luotiin onnistuneesti
luckperms.command.generic.create.error=Virhe tapahtui luotaessa kohdetta {0}
luckperms.command.generic.create.error-already-exists={0} on jo olemassa\!
luckperms.command.generic.delete.success={0} poistettiin onnistuneesti
luckperms.command.generic.delete.error=Virhe tapahtui poistaessa kohdetta {0}
luckperms.command.generic.delete.error-doesnt-exist={0} ei ole olemassa\!
luckperms.command.generic.rename.success={0} nimettiin uudelleen onnistuneesti nimellä {1}
luckperms.command.generic.clone.success={0} kloonattiin onnistuneesti {1} päälle
luckperms.command.generic.info.parent.title=Isäntäryhmät
luckperms.command.generic.info.parent.temporary-title=Väliaikaiset Isäntäryhmät
luckperms.command.generic.info.expires-in=vanhenee
luckperms.command.generic.info.inherited-from=periytyy
luckperms.command.generic.info.inherited-from-self=itseensä
luckperms.command.generic.show-tracks.title={0}n polut
luckperms.command.generic.show-tracks.empty={0} ei ole millään polulla
luckperms.command.generic.clear.node-removed={0} solmua poistettiin
luckperms.command.generic.clear.node-removed-singular={0} solmu poistettiin
luckperms.command.generic.clear={0}\:n solmut tyhjennettiin kontekstissa {1}
luckperms.command.generic.permission.info.title={0}n oikeudet
luckperms.command.generic.permission.info.empty={0}\:lla ei ole oikeuksia asetettuna
luckperms.command.generic.permission.info.click-to-remove=Klikkaa poistaaksesi solmun kohdasta\:  {0}
luckperms.command.generic.permission.check.info.title=Tiedot luvalle {0}
luckperms.command.generic.permission.check.info.directly=Pelaajalla {0} on lupa {1} asetettu arvoon {2} kontekstissa {3}
luckperms.command.generic.permission.check.info.inherited={0} perii luvan {1} asetettu arvoon {2} ryhmästä {3} kontekstissa {4}
luckperms.command.generic.permission.check.info.not-directly=Pelaajalla {0} ei ole asetettu lupaa {1}
luckperms.command.generic.permission.check.info.not-inherited={0} ei peri ryhmää {1}
luckperms.command.generic.permission.check.result.title=Lupatarkistus luvalle {0}
luckperms.command.generic.permission.check.result.result-key=Tulos
luckperms.command.generic.permission.check.result.processor-key=Prosessori
luckperms.command.generic.permission.check.result.cause-key=Syy
luckperms.command.generic.permission.check.result.context-key=Konteksti
luckperms.command.generic.permission.set=Aseta {0} arvoon {1} {2} kontekstissa {3}
luckperms.command.generic.permission.already-has={0} on jo {1} asetettu konteksissä {2}
luckperms.command.generic.permission.set-temp=Aseta {0} arvoon {1} {2} ajaksi {3} kontekstissa {4}
luckperms.command.generic.permission.already-has-temp={0} on jo {1} asetettu kontekstissa {2}
luckperms.command.generic.permission.unset=Poista {0} kohteelle {1} kontekstissa {2}
luckperms.command.generic.permission.doesnt-have={0} ei ole {1} kontekstissa {2}
luckperms.command.generic.permission.unset-temp=Poista väliaikainen lupa {0} kohteelle {1} kontekstissa {2}
luckperms.command.generic.permission.subtract=Aseta {0} arvoon {1} {2} ajaksi {3} \nkontekstissa {4}, {5} vähemmän kuin ennen
luckperms.command.generic.permission.doesnt-have-temp={0} ei ole {1} kontekstissa tällä hetkellä {2}
luckperms.command.generic.permission.clear={0}\:n oikeudet tyhjennettiin kontekstissa {1}
luckperms.command.generic.parent.info.title={0}\:n vanhemmat
luckperms.command.generic.parent.info.empty={0}\:lla ei ole vanhempia asetettuna
luckperms.command.generic.parent.info.click-to-remove=Paina poistaaksesi tämän vanhemman kohteesta {0}
luckperms.command.generic.parent.add={0} perii nyt oikeudet kohteelta {1} kontekstissa {2}
luckperms.command.generic.parent.add-temp={0} perii nyt oikeudet kohteelta {1} ajaksi {2} kontekstissa {3}
luckperms.command.generic.parent.set={0} sai olemassa olevat vanhempansa tyhjennettyä, ja nyt vain perii {1} kontekstissa {2}
luckperms.command.generic.parent.set-track={0} sai olemassa olevat vanhempansa tyhjennettyä, ja nyt vain perii {1} kontekstissa {2}
luckperms.command.generic.parent.remove={0} ei enää peri oikeuksia kohteelta {1} kontekstissa {2}
luckperms.command.generic.parent.remove-temp={0} ei enää väliaikaisesti peri oikeuksia kohteelta {1} kontekstissa {2}
luckperms.command.generic.parent.subtract={0} perii oikeudet kohteelta {1} ajaksi {2} kontekstissa {3}, {4} vähemmän kuin aiemmin
luckperms.command.generic.parent.clear={0}\:n vanhemmat tyhjennettiin kontekstista {1}
luckperms.command.generic.parent.clear-track={0}\:n vanhemmat radalla {1} tyhjennettiin kontekstista {2}
luckperms.command.generic.parent.already-inherits={0} perii jo kohteesta {1} kontekstissa {2}
luckperms.command.generic.parent.doesnt-inherit={0} ei peri kohteesta {1} kontekstissa {2}
luckperms.command.generic.parent.already-temp-inherits={0} perii jo kohteesta {1} konteksissa {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} ei väliaikaisesti peri kohteesta {1} kontekstissa {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}\:n etuliitteet
luckperms.command.generic.chat-meta.info.title-suffix={0}\:n jälkiliitteet
luckperms.command.generic.chat-meta.info.none-prefix={0} ei sisällä etuliitteitä
luckperms.command.generic.chat-meta.info.none-suffix={0} ei sisällä jälkiliitteitä
luckperms.command.generic.chat-meta.info.click-to-remove=Klikkaa poistaaksesi tämän {0} kohteesta {1}
luckperms.command.generic.chat-meta.already-has={0} on jo {1} {2} asetettu prioriteetiksi kohteelle {3} kontekstissa {4}
luckperms.command.generic.chat-meta.already-has-temp={0} on jo {1} {2} asetettu prioriteetiksi väliaikaisesti kohteelle {3} kontekstissa {4}
luckperms.command.generic.chat-meta.doesnt-have={0}\:lla ei ole {1} {2} asetettu prioriteetiksi kohteelle {3} kontekstissa {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0}\:lla ei ole {1} {2} asetettu väliaikaisesti priosriteetiksi kohteelle {3} kontekstissa {4}
luckperms.command.generic.chat-meta.add={0} sai {1} {2} asetettu prioriteetiksi kohteelle {3} kontekstissa {4}
luckperms.command.generic.chat-meta.add-temp={0} sai {1} {2} asetettu prioriteetiksi kohteelle {3} {4} ajaksi kontekstissa {5}
luckperms.command.generic.chat-meta.remove={0} sai {1} {2} prioriteetti {3} poistettu kontekstissa {4}
luckperms.command.generic.chat-meta.remove-bulk={0} sai kaikki {1} prioriteetti {2} poistettu kontekstissa {3}
luckperms.command.generic.chat-meta.remove-temp={0} sai väliaikaisesti {1} {2} prioriteetti {3} poistettu kontekstissa {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} sai väliaikaisesti kaikki {1} prioriteetti {2} poistettu kontekstissa {3}
luckperms.command.generic.meta.info.title={0}\:n meta
luckperms.command.generic.meta.info.none={0} ei sisällä meta-tietoja
luckperms.command.generic.meta.info.click-to-remove=Klikkaa poistaaksesi tämän meta-solmun kohteesta {0}
luckperms.command.generic.meta.already-has={0}\:lla on jo meta-avain {1} asetettu {2} kontekstissa {3}
luckperms.command.generic.meta.already-has-temp={0}\:lla on jo meta-avain {1} asetettu väliaikaisesti {2} kontekstissa {3}
luckperms.command.generic.meta.doesnt-have={0}\:lla ei ole meta-avainta {1} asetettu kontekstissa {2}
luckperms.command.generic.meta.doesnt-have-temp={0}\:lla ei ole meta-avainta {1} asetettu väliaikaisesti kontekstissa {2}
luckperms.command.generic.meta.set=Aseta meta-avain {0} arvoon {1} {2} kontekstissa {3}
luckperms.command.generic.meta.set-temp=Aseta meta-avain {0} arvoon {1} {2} ajaksi {3} kontekstissa {4}
luckperms.command.generic.meta.unset=Poista meta-avain {0} {1} kontekstissa {2}
luckperms.command.generic.meta.unset-temp=Poista väliaikainen meta-key {0} {1} kontekstissa {2}
luckperms.command.generic.meta.clear={0}\:n meta-täsmäävä tyyppi {1} tyhjennettiin kontekstissa {2}
luckperms.command.generic.contextual-data.title=Kontekstuaalinen Tiedot
luckperms.command.generic.contextual-data.mode.key=Tila
luckperms.command.generic.contextual-data.mode.server=Palvelin
luckperms.command.generic.contextual-data.mode.active-player=aktiivinen pelaaja
luckperms.command.generic.contextual-data.contexts-key=Konteksti
luckperms.command.generic.contextual-data.prefix-key=Etuliite
luckperms.command.generic.contextual-data.suffix-key=Jälkiliite
luckperms.command.generic.contextual-data.primary-group-key=Ensisijainen Ryhmä
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Ei mitään
luckperms.command.user.info.title=Käyttäjätiedot
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=tyyppi
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=poissa
luckperms.command.user.info.status-key=Tila
luckperms.command.user.info.status.online=Paikalla
luckperms.command.user.info.status.offline=Poissa
luckperms.command.user.removegroup.error-primary=Et voi poistaa käyttäjää heidän ensisijaisesta ryhmästään
luckperms.command.user.primarygroup.not-member={0} ei ollut jo jäsen {1}, lisäten ne nyt
luckperms.command.user.primarygroup.already-has={0}\:lla on jo {1} asetettu ensisijaiseksi ryhmäksi
luckperms.command.user.primarygroup.warn-option=Varoitus\: Tämän palvelimen käyttämä ensisijainen ryhmän laskentamenetelmä ({0}) ei välttämättä heijasta tätä muutosta
luckperms.command.user.primarygroup.set={0}\:n ensisijainen ryhmä asetettiin {1}
luckperms.command.user.track.error-not-contain-group={0} ei ole jo missään ryhmässä {1}
luckperms.command.user.track.unsure-which-track=Etkö ole varma, minkä reittiä käyttää, täsmentäkää se argumentina.
luckperms.command.user.track.missing-group-advice=Joko luo ryhmä tai poista se ja yritä uudelleen
luckperms.command.user.promote.added-to-first={0} ei ole missään ryhmässä {1}, joten se lisättiin ensimmäiseen ryhmään, {2} kontekstissa {3}
luckperms.command.user.promote.not-on-track={0} ei ole missään rymässä {1}, joten ei ylennetty
luckperms.command.user.promote.success=Ylennetään {0} radalla {1} ryhmästä {2} ryhmään {3} kontekstissa {4}
luckperms.command.user.promote.end-of-track=Seuraamisen lopussa {0} saavutettiin, ei voida ylentää {1}
luckperms.command.user.promote.next-group-deleted=Seuraava ryhmä radalla, {0}, ei ole enää olemassa
luckperms.command.user.promote.unable-to-promote=Käyttäjää ei voitu ylentää
luckperms.command.user.demote.success=Alennetaan {0} radalla {1} ryhmästä {2} ryhmään {3} kontekstissa {4}
luckperms.command.user.demote.end-of-track=Radan {0} pääty saavutettiin, joten {1} poistettiin kohteelta {2}
luckperms.command.user.demote.end-of-track-not-removed=Radan {0} loppu saavutettiin, mutta {1} ei poistunut ensimmäisestä ryhmästä
luckperms.command.user.demote.previous-group-deleted=Edellinen ryhmä radalla, {0}, ei ole enää olemassa
luckperms.command.user.demote.unable-to-demote=Mahdotonta alentaa käyttäjää
luckperms.command.group.list.title=Ryhmät
luckperms.command.group.delete.not-default=Oletusryhmää ei voi poistaa
luckperms.command.group.info.title=Ryhmän tiedot
luckperms.command.group.info.display-name-key=Näyttönimi
luckperms.command.group.info.weight-key=Paino
luckperms.command.group.setweight.set=Aseta painoksi {0} ryhmälle {1}
luckperms.command.group.setdisplayname.doesnt-have={0}\:lle ei ole asetettu näyttönimeä
luckperms.command.group.setdisplayname.already-has={0} sisältää jo näytön nimen {1}
luckperms.command.group.setdisplayname.already-in-use=Näyttönimi {0} on jo käytössä {1} toimesta
luckperms.command.group.setdisplayname.set=Aseta näyttönimi\: ”{0}” ryhmälle {1} kontekstissa {2}
luckperms.command.group.setdisplayname.removed=Poista näyttönimi ryhmälle {0} kontekstissa {1}
luckperms.command.track.list.title=Jäljet
luckperms.command.track.path.empty=-
luckperms.command.track.info.showing-track=Näytetään jäljet
luckperms.command.track.info.path-property=Polku
luckperms.command.track.clear={0}\:n ryhmät jäljet tyhjennettiin
luckperms.command.track.append.success=Ryhmä {0} liitettiin seuratakseen {1}
luckperms.command.track.insert.success=Ryhmä {0} lisättiin kappaleeseen {1} sijainnissa {2}
luckperms.command.track.insert.error-number=Odotettiin numeroa mutta sen sijaan vastaanotettu\: {0}
luckperms.command.track.insert.error-invalid-pos=Kohdassa {0} ei voitu lisätä
luckperms.command.track.insert.error-invalid-pos-reason=virheellinen asema
luckperms.command.track.remove.success=Ryhmä {0} poistettiin raidasta {1}
luckperms.command.track.error-empty={0} ei voi käyttää, koska se on tyhjä tai sisältää vain yhden ryhmän
luckperms.command.track.error-multiple-groups={0} on useiden ryhmien jäsen tällä radalla
luckperms.command.track.error-ambiguous=Niiden sijaintia ei pystytty määrittämään
luckperms.command.track.already-contains={0} sisältää jo {1}
luckperms.command.track.doesnt-contain={0} ei sisällä {1}
luckperms.command.log.load-error=Tietoja ei voitu ladata
luckperms.command.log.invalid-page=Virheellinen sivunumero
luckperms.command.log.invalid-page-range=Lisää arvo välitä {0} ja {1}
luckperms.command.log.empty=Ei näytettäviä lokitietoja
luckperms.command.log.notify.error-console=Konsolin ilmoituksia ei voi vaihtaa
luckperms.command.log.notify.enabled-term=Käytössä
luckperms.command.log.notify.disabled-term=Poistettu käytöstä
luckperms.command.log.notify.changed-state={0} loggauksen ulostulo
luckperms.command.log.notify.already-on=Olet jo vastaanottamassa ilmoituksia
luckperms.command.log.notify.already-off=Sinä et ole vastaanottamassa ilmoituksia juuri nyt
luckperms.command.log.notify.invalid-state=Tuntematon tila. Oletetaan {0} tai {1}
luckperms.command.log.show.search=Näytetään viimeaikaiset toiminnot haulla {0}
luckperms.command.log.show.recent=Näytetään viimeaikaiset tapahtumat
luckperms.command.log.show.by=Näytetään viimeaikaiset toiminnot käyttäjältä {0}
luckperms.command.log.show.history=Näytetään historia {0} {1}
luckperms.command.export.error-term=Virhe
luckperms.command.export.already-running=Toinen vientiprosessi on jo käynnissä
luckperms.command.export.file.already-exists=Tiedosto {0} on jo olemassa
luckperms.command.export.file.not-writable=Tiedostoon {0} ei voida kirjoittaa
luckperms.command.export.file.success=Viety onnistuneesti kohteeseen {0}
luckperms.command.export.file-unexpected-error-writing=Odottamaton virhe tapahtui kirjoittaessa tiedostoon
luckperms.command.export.web.export-code=Vie koodi
luckperms.command.export.web.import-command-description=Käytä seuraavaa komentoa tuodaksesi muutokset
luckperms.command.import.term=Tuo
luckperms.command.import.error-term=Virhe
luckperms.command.import.already-running=Toinen tuonti prosessi on jo käynnissä
luckperms.command.import.file.doesnt-exist=Tiedostoa {0} ei ole olemassa
luckperms.command.import.file.not-readable=Tiedostoa {0} ei voida lukea
luckperms.command.import.file.unexpected-error-reading=Odottamaton virhe tapahtui tuontitiedostoa luettaessa
luckperms.command.import.file.correct-format=onko se oikeassa muodossa?
luckperms.command.import.web.unable-to-read=Tietoja ei voida lukea annetulla koodilla
luckperms.command.import.progress.percent={0}% valmiina
luckperms.command.import.progress.operations={0}/{1} toimintoa valmiina
luckperms.command.import.starting=Aloitetaan tuontiprosessi
luckperms.command.import.completed=VALMIS
luckperms.command.import.duration=kesti {0} sekuntia
luckperms.command.bulkupdate.must-use-console=Massapäivityskomentoa voi käyttää vain konsolin kautta
luckperms.command.bulkupdate.invalid-data-type=Virheellinen tyyppi, odotettiin {0}
luckperms.command.bulkupdate.invalid-constraint=Virheellinen rajoite {0}
luckperms.command.bulkupdate.invalid-constraint-format=Rajoitusten tulee olla muodossa {0}
luckperms.command.bulkupdate.invalid-comparison=Virheellinen vertailuoperaattori {0}
luckperms.command.bulkupdate.invalid-comparison-format=Odotettiin joitakin seuraavista\: {0}
luckperms.command.bulkupdate.queued=Massapäivitystoiminto on jonossa
luckperms.command.bulkupdate.confirm=Suorita {0} suorittaaksesi päivitykset
luckperms.command.bulkupdate.unknown-id=Toimintoa ID\:llä {0} ei ole tai se on vanhentunut
luckperms.command.bulkupdate.starting=Massapäivitys käynnissä
luckperms.command.bulkupdate.success=Massapäivitys suoritettu onnistuneesti
luckperms.command.bulkupdate.success.statistics.nodes=Vaikutetut solmut
luckperms.command.bulkupdate.success.statistics.users=Vaikutetut käyttäjät
luckperms.command.bulkupdate.success.statistics.groups=Vaikutetut ryhmät
luckperms.command.bulkupdate.failure=Massapäivitys epäonnistui, tarkista konsoli virheiden varalta
luckperms.command.update-task.request=Päivitystä on pyydetty, ole hyvä ja odota
luckperms.command.update-task.complete=Päivitys valmis
luckperms.command.update-task.push.attempting=Yritetään viedä päivitystä muille palvelimille
luckperms.command.update-task.push.complete=Ilmoitettiin muille palvelimille {0} kautta onnistuneesti
luckperms.command.update-task.push.error=Virhe muiden palvelimien päivityksessä
luckperms.command.update-task.push.error-not-setup=Ei voi lähettää muutoksia muille palvelimille, koska viestipalvelua ei ole määritetty
luckperms.command.reload-config.success=Asetustiedosto ladattiin uudelleen
luckperms.command.reload-config.restart-note=jotkin asetukset tulevat voimaan vasta, kun palvelin on käynnistetty uudelleen
luckperms.command.translations.searching=Etsitään saatavilla olevia käännöksiä, odota hetki...
luckperms.command.translations.searching-error=Ei saatu luetteloa käytettävissä olevista käännöksistä
luckperms.command.translations.installed-translations=Asennetut Käännökset
luckperms.command.translations.available-translations=Käytettävissä olevat käännökset
luckperms.command.translations.percent-translated={0}% käännetty
luckperms.command.translations.translations-by=kääntänyt
luckperms.command.translations.installing=Asennetaan käännöksiä, odota hetki...
luckperms.command.translations.download-error=Käännöstä {0} ei voi ladata
luckperms.command.translations.installing-specific=Asennetaan kieltä {0}...
luckperms.command.translations.install-complete=Asennus valmis
luckperms.command.translations.download-prompt=Käytä {0} ladataksesi ja asentaaksesi uusimpia versioita yhteisön tarjoamista käännöksistä
luckperms.command.translations.download-override-warning=Huomaa, että tämä korvaa kaikki muutokset, jotka olet tehnyt näille kielille
luckperms.usage.user.description=Joukko komentoja käyttäjien hallintaan LuckPerms\:ssä. (''käyttäjä'' LuckPerms\:ssä tarkoittaa pelaajaa, johon voidaan viitata joko UUID\:llä tai käyttäjänimellä)
luckperms.usage.group.description=Joukko komentoja LuckPermsin sisällä olevien ryhmien hallintaan. Ryhmät ovat vain kokoelmia käyttöoikeuksista, jotka voidaan antaa käyttäjille. Uudet ryhmät tehdään käyttäen ''creategroup'' komentoa.
luckperms.usage.track.description=A set of commands for managing tracks within LuckPerms. Tracks are a ordered collection of groups which can be used for defining promotions and demotions.
luckperms.usage.log.description=Joukko komentoja, joilla voit hallita kirjaamiseen liittyviä toimintoja LuckPermsissa.
luckperms.usage.sync.description=Lataa kaikki tiedot liitännäisen tallennustilasta muistiin ja soveltaa havaitut muutokset.
luckperms.usage.info.description=Tulostaa yleistä tietoa aktiivisesta liitännäisestä.
luckperms.usage.editor.description=Luo uusi verkkosivun editorisessio.
luckperms.usage.editor.argument.type=tyypit, jotka ladataan editoriin. (''kaikki'', ''käyttäjät'' tai ''ryhmät'')
luckperms.usage.editor.argument.filter=sallimus, jonka mukaan suodatetaan käyttäjätiedot
luckperms.usage.verbose.description=Ohjaa liitännäisen yksityiskohtaisen sallimusmonitoroinnin järjestelmää.
luckperms.usage.verbose.argument.action=otetaanko lokitus käyttöön/pois käytöstä vai ladataanko lokitiedot
luckperms.usage.verbose.argument.filter=suodatin, jonka mukaan verrataan merkintöjä
luckperms.usage.verbose.argument.commandas=pelaaja/komento, joka suoritetaan
luckperms.usage.tree.description=Luo puunäkymä (järjestetty luettelohierarkia) kaikista LuckPermsin tuntemista sallimuksista.
luckperms.usage.tree.argument.scope=puiden juuripiste. Määritä "." sisällyttääksesi kaikki sallimukset
luckperms.usage.tree.argument.player=online-pelaajan nimi, jota tarkastellaan
luckperms.usage.search.description=Hakee kaikki käyttäjät/ryhmät, joilla on tietty sallimus
luckperms.usage.search.argument.permission=sallimus, jota haetaan
luckperms.usage.search.argument.page=sivu, jota tarkastellaan
luckperms.usage.network-sync.description=Synkronoi muutokset tallennustilan kanssa ja pyytää, että kaikki muut palvelimet verkossa tekevät samoin
luckperms.usage.import.description=Tuodaan tietoja (aiemmin luodusta) vientitiedostosta
luckperms.usage.import.argument.file=tiedosto, josta tuodaan
luckperms.usage.import.argument.replace=korvataan olemassa olevat tiedot sen sijaan, että yhdistettäisiin
luckperms.usage.import.argument.upload=lataa tiedot aiemmasta viennistä
luckperms.usage.export.description=Vie kaikki sallimustiedot ''vientitiedostoon''. Voidaan tuoda takaisin myöhemmin.
luckperms.usage.export.argument.file=tiedosto, johon viedään
luckperms.usage.export.argument.without-users=poista käyttäjät viennistä
luckperms.usage.export.argument.without-groups=poista ryhmät viennistä
luckperms.usage.export.argument.upload=Lataa kaikki sallimustiedot verkkosivueditoriin. Voidaan tuoda takaisin myöhemmin.
luckperms.usage.reload-config.description=Lataa joitain asetuksia uudelleen
luckperms.usage.bulk-update.description=Suorita joukkopäivityspyyntöjä kaikille tiedoille
luckperms.usage.bulk-update.argument.data-type=tiedon tyyppi, joka muuttuu. (''kaikki'', ''käyttäjät'' tai ''ryhmät'')
luckperms.usage.bulk-update.argument.action=suoritettava toiminto tiedoille. (''päivitä'' tai ''poista'')
luckperms.usage.bulk-update.argument.action-field=kenttä, johon toiminta kohdistuu. Vain tarvitaan ''päivitä''-toiminnolle. (''sallimus'', ''palvelin'' tai ''maailma'')
luckperms.usage.bulk-update.argument.action-value=arvo, joka korvataan. Vain tarvitaan ''päivitä''-toiminnolle.
luckperms.usage.bulk-update.argument.constraint=rajoitukset, joita tarvitaan päivitykselle
luckperms.usage.translations.description=Hallitse käännöksiä
luckperms.usage.translations.argument.install=komennoita käännösten asentamiseen
luckperms.usage.apply-edits.description=Soveltaa verkkosivueditorissa tehtyjä sallimusmuutoksia
luckperms.usage.apply-edits.argument.code=tiedon yksilöllinen koodi
luckperms.usage.apply-edits.argument.target=kohde, johon tiedot sovelletaan
luckperms.usage.create-group.description=Luo uusi ryhmä
luckperms.usage.create-group.argument.name=ryhmän nimi
luckperms.usage.create-group.argument.weight=ryhmän paino
luckperms.usage.create-group.argument.display-name=ryhmän näyttönimi
luckperms.usage.delete-group.description=Poistaa ryhmän
luckperms.usage.delete-group.argument.name=ryhmän nimi
luckperms.usage.list-groups.description=Listaa kaikki ryhmät alustalla
luckperms.usage.create-track.description=Luo uusi raita
luckperms.usage.create-track.argument.name=raidan nimi
luckperms.usage.delete-track.description=Poistaa raidan
luckperms.usage.delete-track.argument.name=raidan nimi
luckperms.usage.list-tracks.description=Listaa kaikki raiteet alustalla
luckperms.usage.user-info.description=Näyttää käyttäjätiedot
luckperms.usage.user-switchprimarygroup.description=Vaihda käyttäjän ensisijainen ryhmä
luckperms.usage.user-switchprimarygroup.argument.group=ryhmä, johon siirrytään
luckperms.usage.user-promote.description=Ylentää käyttäjän raiteella
luckperms.usage.user-promote.argument.track=raita, jolle käyttäjä ylennetään
luckperms.usage.user-promote.argument.context=asiayhteydet, joissa käyttäjä ylennetään
luckperms.usage.user-promote.argument.dont-add-to-first=ylennetään vain, jos käyttäjä on jo radalla
luckperms.usage.user-demote.description=Alentaa käyttäjää radalla
luckperms.usage.user-demote.argument.track=raita, jolle käyttäjä alennetaan
luckperms.usage.user-demote.argument.context=asiayhteydet, joissa käyttäjä alennetaan
luckperms.usage.user-demote.argument.dont-remove-from-first=estää käyttäjää poistumasta ensimmäisestä ryhmästä
luckperms.usage.user-clone.description=Monistaa käyttäjän
luckperms.usage.user-clone.argument.user=keskusteltavan käyttäjän nimi/uuid
luckperms.usage.group-info.description=Antaa tietoa ryhmästä
luckperms.usage.group-listmembers.description=Näyttää käyttäjät/ryhmät, jotka perivät tämän ryhmän
luckperms.usage.group-listmembers.argument.page=sivu, jota tarkastellaan
luckperms.usage.group-setweight.description=Aseta ryhmän paino
luckperms.usage.group-setweight.argument.weight=asetettava paino
luckperms.usage.group-set-display-name.description=Aseta ryhmän näyttönimi
luckperms.usage.group-set-display-name.argument.name=nimi, joka asetetaan
luckperms.usage.group-set-display-name.argument.context=asiayhteydet, joissa nimi asetetaan
luckperms.usage.group-rename.description=Nimeä ryhmä uudelleen
luckperms.usage.group-rename.argument.name=uusi nimi
luckperms.usage.group-clone.description=Monistaa ryhmän
luckperms.usage.group-clone.argument.name=ryhmän nimi, johon monistetaan
luckperms.usage.holder-editor.description=Avaa verkkosivun sallimuseditori
luckperms.usage.holder-showtracks.description=Listaa raiteet, joilla objekti on
luckperms.usage.holder-clear.description=Poistaa kaikki sallimukset, vanhemmat ja metatiedot
luckperms.usage.holder-clear.argument.context=asiayhteydet, joilla suodatetaan
luckperms.usage.permission.description=Muokkaa sallimuksia
luckperms.usage.parent.description=Muokkaa perintöjä
luckperms.usage.meta.description=Muokkaa metatietoja
luckperms.usage.permission-info.description=Listaa objektin sallimusnauhat
luckperms.usage.permission-info.argument.page=sivu, jota tarkastellaan
luckperms.usage.permission-info.argument.sort-mode=tapa, jolla merkinnät lajitellaan
luckperms.usage.permission-set.description=Määrittää sallimuksen objektille
luckperms.usage.permission-set.argument.node=sallimusnauha, joka asetetaan
luckperms.usage.permission-set.argument.value=nauhan arvo
luckperms.usage.permission-set.argument.context=asiayhteydet, joissa sallimus lisätään
luckperms.usage.permission-unset.description=Poistaa sallimuksen objektista
luckperms.usage.permission-unset.argument.node=sallimusnauha, joka poistetaan
luckperms.usage.permission-unset.argument.context=asiayhteydet, joissa sallimus poistetaan
luckperms.usage.permission-settemp.description=Asettaa väliaikaisen sallimuksen objektille
luckperms.usage.permission-settemp.argument.node=sallimusnauha, joka asetetaan
luckperms.usage.permission-settemp.argument.value=nauhan arvo
luckperms.usage.permission-settemp.argument.duration=the duration until the permission node expires
luckperms.usage.permission-settemp.argument.temporary-modifier=how the temporary permission should be applied
luckperms.usage.permission-settemp.argument.context=asiayhteydet, joissa sallimus lisätään
luckperms.usage.permission-unsettemp.description=Poistaa väliaikaisen sallimuksen objektista
luckperms.usage.permission-unsettemp.argument.node=sallimusnauha, joka poistetaan
luckperms.usage.permission-unsettemp.argument.duration=the duration to subtract
luckperms.usage.permission-unsettemp.argument.context=asiayhteydet, joissa sallimus poistetaan
luckperms.usage.permission-check.description=Tarkistaa, onko objektilla tietty käyttöoikeus
luckperms.usage.permission-check.argument.node=käyttöoikeus, jota tarkistetaan
luckperms.usage.permission-clear.description=Puhdistaa kaikki käyttöoikeudet
luckperms.usage.permission-clear.argument.context=suodattimet konteksteille
luckperms.usage.parent-info.description=Listaa ryhmät, joita tämä objekti perii
luckperms.usage.parent-info.argument.page=näytettävä sivu
luckperms.usage.parent-info.argument.sort-mode=tapa, jolla merkinnät järjestetään
luckperms.usage.parent-set.description=Poistaa kaikki muut ryhmät, jotka objekti on jo perinyt, ja lisää ne annettuun
luckperms.usage.parent-set.argument.group=ryhmä, johon asetetaan
luckperms.usage.parent-set.argument.context=kontekstit, joihin ryhmä asetetaan
luckperms.usage.parent-add.description=Asettaa toisen ryhmän, jolta objekti perii käyttöoikeudet
luckperms.usage.parent-add.argument.group=ryhmä, jolta peritään
luckperms.usage.parent-add.argument.context=kontekstit, joissa ryhmä peritään
luckperms.usage.parent-remove.description=Poistaa aiemmin asetetun perimissäännön
luckperms.usage.parent-remove.argument.group=poistettava ryhmä
luckperms.usage.parent-remove.argument.context=kontekstit, joista ryhmä poistetaan
luckperms.usage.parent-set-track.description=Poistaa kaikki muut ryhmät, joita objekti on perinyt tietyllä uralla, ja lisää ne annettuun
luckperms.usage.parent-set-track.argument.track=ura, johon asetetaan
luckperms.usage.parent-set-track.argument.group=ryhmä, johon asetetaan, tai numero, joka liittyy ryhmän sijaintiin tietyllä uralla
luckperms.usage.parent-set-track.argument.context=kontekstit, joihin ryhmä asetetaan
luckperms.usage.parent-add-temp.description=Asettaa toisen ryhmän, jolta objekti perii käyttöoikeudet tilapäisesti
luckperms.usage.parent-add-temp.argument.group=ryhmä, jolta peritään
luckperms.usage.parent-add-temp.argument.duration=tilapäisen jäsenyyden kesto
luckperms.usage.parent-add-temp.argument.temporary-modifier=miten tilapäinen käyttöoikeus sovelletaan
luckperms.usage.parent-add-temp.argument.context=kontekstit, joissa ryhmä peritään
luckperms.usage.parent-remove-temp.description=Poistaa aiemmin asetetun tilapäisen perimissäännön
luckperms.usage.parent-remove-temp.argument.group=poistettava ryhmä
luckperms.usage.parent-remove-temp.argument.duration=kesto, jonka ajan ryhmä poistetaan
luckperms.usage.parent-remove-temp.argument.context=kontekstit, joista ryhmä poistetaan
luckperms.usage.parent-clear.description=Puhdistaa kaikki vanhemmat
luckperms.usage.parent-clear.argument.context=kontekstit, joilla suodatetaan
luckperms.usage.parent-clear-track.description=Puhdistaa kaikki vanhemmat tietyllä uralla
luckperms.usage.parent-clear-track.argument.track=ura, jolta poistetaan
luckperms.usage.parent-clear-track.argument.context=kontekstit, joilla suodatetaan
luckperms.usage.meta-info.description=Näyttää kaikki keskustelu-meta-arvot
luckperms.usage.meta-set.description=Asettaa meta-arvon
luckperms.usage.meta-set.argument.key=avain, joka asetetaan
luckperms.usage.meta-set.argument.value=arvo, joka asetetaan
luckperms.usage.meta-set.argument.context=kontekstit, joihin meta-asetukset lisätään
luckperms.usage.meta-unset.description=Poistaa meta-arvon
luckperms.usage.meta-unset.argument.key=avain, joka poistetaan
luckperms.usage.meta-unset.argument.context=kontekstit, joista meta-arvo poistetaan
luckperms.usage.meta-settemp.description=Asettaa meta-arvon tilapäisesti
luckperms.usage.meta-settemp.argument.key=avain, joka asetetaan
luckperms.usage.meta-settemp.argument.value=arvo, joka asetetaan
luckperms.usage.meta-settemp.argument.duration=kesto, jonka ajan meta-arvo on voimassa
luckperms.usage.meta-settemp.argument.context=kontekstit, joihin meta-asetukset lisätään
luckperms.usage.meta-unsettemp.description=Poistaa tilapäisen meta-arvon
luckperms.usage.meta-unsettemp.argument.key=avain, joka poistetaan
luckperms.usage.meta-unsettemp.argument.context=kontekstit, joista meta-arvo poistetaan
luckperms.usage.meta-addprefix.description=Lisää etuliitteen
luckperms.usage.meta-addprefix.argument.priority=etuliitteen lisäysprioriteetti
luckperms.usage.meta-addprefix.argument.prefix=etuliitteen merkkijono
luckperms.usage.meta-addprefix.argument.context=kontekstit, joihin etuliite lisätään
luckperms.usage.meta-addsuffix.description=Lisää jälkiliitteen
luckperms.usage.meta-addsuffix.argument.priority=jälkiliitteen lisäysprioriteetti
luckperms.usage.meta-addsuffix.argument.suffix=jälkiliitteen merkkijono
luckperms.usage.meta-addsuffix.argument.context=kontekstit, joihin jälkiliite lisätään
luckperms.usage.meta-setprefix.description=Asettaa etuliitteen
luckperms.usage.meta-setprefix.argument.priority=etuliitteen asetuksen prioriteetti
luckperms.usage.meta-setprefix.argument.prefix=etuliitteen merkkijono
luckperms.usage.meta-setprefix.argument.context=kontekstit, joihin etuliite asetetaan
luckperms.usage.meta-setsuffix.description=Asettaa jälkiliitteen
luckperms.usage.meta-setsuffix.argument.priority=jälkiliitteen asetuksen prioriteetti
luckperms.usage.meta-setsuffix.argument.suffix=jälkiliitteen merkkijono
luckperms.usage.meta-setsuffix.argument.context=kontekstit, joihin jälkiliite asetetaan
luckperms.usage.meta-removeprefix.description=Poistaa etuliitteen
luckperms.usage.meta-removeprefix.argument.priority=etuliitteen poistamisen prioriteetti
luckperms.usage.meta-removeprefix.argument.prefix=etuliitteen merkkijono
luckperms.usage.meta-removeprefix.argument.context=kontekstit, joista etuliite poistetaan
luckperms.usage.meta-removesuffix.description=Poistaa jälkiliitteen
luckperms.usage.meta-removesuffix.argument.priority=jälkiliitteen poistamisen prioriteetti
luckperms.usage.meta-removesuffix.argument.suffix=jälkiliitteen merkkijono
luckperms.usage.meta-removesuffix.argument.context=kontekstit, joista jälkiliite poistetaan
luckperms.usage.meta-addtemp-prefix.description=Lisää etuliitteen tilapäisesti
luckperms.usage.meta-addtemp-prefix.argument.priority=etuliitteen lisäysprioriteetti
luckperms.usage.meta-addtemp-prefix.argument.prefix=etuliitteen merkkijono
luckperms.usage.meta-addtemp-prefix.argument.duration=kesto, jonka ajan etuliite on voimassa
luckperms.usage.meta-addtemp-prefix.argument.context=kontekstit, joihin etuliite lisätään
luckperms.usage.meta-addtemp-suffix.description=Lisää jälkiliitteen tilapäisesti
luckperms.usage.meta-addtemp-suffix.argument.priority=jälkiliitteen lisäysprioriteetti
luckperms.usage.meta-addtemp-suffix.argument.suffix=jälkiliitteen merkkijono
luckperms.usage.meta-addtemp-suffix.argument.duration=kesto, jonka ajan jälkiliite on voimassa
luckperms.usage.meta-addtemp-suffix.argument.context=kontekstit, joihin jälkiliite lisätään
luckperms.usage.meta-settemp-prefix.description=Asettaa etuliitteen tilapäisesti
luckperms.usage.meta-settemp-prefix.argument.priority=etuliitteen asetuksen prioriteetti
luckperms.usage.meta-settemp-prefix.argument.prefix=etuliitteen merkkijono
luckperms.usage.meta-settemp-prefix.argument.duration=kesto, jonka ajan etuliite on voimassa
luckperms.usage.meta-settemp-prefix.argument.context=kontekstit, joihin etuliite asetetaan
luckperms.usage.meta-settemp-suffix.description=Asettaa jälkiliitteen tilapäisesti
luckperms.usage.meta-settemp-suffix.argument.priority=jälkiliitteen asetuksen prioriteetti
luckperms.usage.meta-settemp-suffix.argument.suffix=jälkiliitteen merkkijono
luckperms.usage.meta-settemp-suffix.argument.duration=kesto, jonka ajan jälkiliite on voimassa
luckperms.usage.meta-settemp-suffix.argument.context=kontekstit, joihin jälkiliite asetetaan
luckperms.usage.meta-removetemp-prefix.description=Poistaa tilapäisen etuliitteen
luckperms.usage.meta-removetemp-prefix.argument.priority=etuliitteen poistamisen prioriteetti
luckperms.usage.meta-removetemp-prefix.argument.prefix=etuliitteen merkkijono
luckperms.usage.meta-removetemp-prefix.argument.context=kontekstit, joista etuliite poistetaan
luckperms.usage.meta-removetemp-suffix.description=Poistaa tilapäisen jälkiliitteen
luckperms.usage.meta-removetemp-suffix.argument.priority=jälkiliitteen poistamisen prioriteetti
luckperms.usage.meta-removetemp-suffix.argument.suffix=jälkiliitteen merkkijono
luckperms.usage.meta-removetemp-suffix.argument.context=kontekstit, joista jälkiliite poistetaan
luckperms.usage.meta-clear.description=Clears all meta
luckperms.usage.meta-clear.argument.type=the type of meta to remove
luckperms.usage.meta-clear.argument.context=the contexts to filter by
luckperms.usage.track-info.description=Näyttää tiedot nykyisestä trackista
luckperms.usage.track-editor.description=Asettaa trackin jäsenen
luckperms.usage.track-append.description=Appends a group onto the end of the track
luckperms.usage.track-append.argument.group=the group to append
luckperms.usage.track-insert.description=Inserts a group at a given position along the track
luckperms.usage.track-insert.argument.group=the group to insert
luckperms.usage.track-insert.argument.position=the position to insert the group at (the first position on the track is 1)
luckperms.usage.track-remove.description=Removes a group from the track
luckperms.usage.track-remove.argument.group=the group to remove
luckperms.usage.track-clear.description=Puhdistaa trackin jäsenet
luckperms.usage.track-rename.description=Rename the track
luckperms.usage.track-rename.argument.name=the new name
luckperms.usage.track-clone.description=Clone the track
luckperms.usage.track-clone.argument.name=the name of the track to clone onto
luckperms.usage.log-recent.description=Näyttää viimeisimmät lokimerkinnät
luckperms.usage.log-recent.argument.user=the name/uuid of the user to filter by
luckperms.usage.log-recent.argument.page=näytettävä sivu
luckperms.usage.log-search.description=Suorittaa haun lokitiedoista
luckperms.usage.log-search.argument.query=hakulauseke
luckperms.usage.log-search.argument.page=sivunumero, joka näytetään
luckperms.usage.log-notify.description=Vaihda lokiviestien ilmoitukset päälle tai pois
luckperms.usage.log-notify.argument.toggle=käytetäänkö päälle vai pois
luckperms.usage.log-user-history.description=Näyttää käyttäjän historian
luckperms.usage.log-user-history.argument.user=kayttajan nimi/uuid
luckperms.usage.log-user-history.argument.page=sivunumero, joka näytetään
luckperms.usage.log-group-history.description=Näyttää ryhmän historian
luckperms.usage.log-group-history.argument.group=ryhmän nimi
luckperms.usage.log-group-history.argument.page=sivunumero, joka näytetään
luckperms.usage.log-track-history.description=Näyttää trackin historian
luckperms.usage.log-track-history.argument.track=trackin nimi
luckperms.usage.log-track-history.argument.page=sivunumero, joka näytetään
luckperms.usage.sponge.description=Muokkaa lisättyjä Sponge-tietoja
luckperms.usage.sponge.argument.collection=kokoelma, josta kysytään
luckperms.usage.sponge.argument.subject=subjekti, jota muokataan
luckperms.usage.sponge-permission-info.description=Näyttää Sponge-palvelimen käyttöoikeudet
luckperms.usage.sponge-permission-info.argument.contexts=kontekstit, joilla suodatetaan
luckperms.usage.sponge-permission-set.description=Asettaa käyttöoikeuden Subjektille
luckperms.usage.sponge-permission-set.argument.node=kayttöoikeuden solmun nimi
luckperms.usage.sponge-permission-set.argument.tristate=arvo, johon käyttöoikeus asetetaan
luckperms.usage.sponge-permission-set.argument.contexts=kontekstit, joissa käyttöoikeus asetetaan
luckperms.usage.sponge-permission-clear.description=Poistaa Subjektin käyttöoikeudet
luckperms.usage.sponge-permission-clear.argument.contexts=kontekstit, joissa käyttöoikeudet poistetaan
luckperms.usage.sponge-parent-info.description=Näyttää Sponge-vanhemmat
luckperms.usage.sponge-parent-info.argument.contexts=kontekstit, joilla suodatetaan
luckperms.usage.sponge-parent-add.description=Lisää vanhemman Subjektille
luckperms.usage.sponge-parent-add.argument.collection=kokoelma, jossa vanhempi Subjekti sijaitsee
luckperms.usage.sponge-parent-add.argument.subject=vanhemman Subjektin nimi
luckperms.usage.sponge-parent-add.argument.contexts=kontekstit, joissa vanhempi lisätään
luckperms.usage.sponge-parent-remove.description=Poistaa vanhemman Subjektilta
luckperms.usage.sponge-parent-remove.argument.collection=kokoelma, jossa vanhempi Subjekti sijaitsee
luckperms.usage.sponge-parent-remove.argument.subject=vanhemman Subjektin nimi
luckperms.usage.sponge-parent-remove.argument.contexts=kontekstit, joissa vanhempi poistetaan
luckperms.usage.sponge-parent-clear.description=Poistaa Subjektin vanhemmat
luckperms.usage.sponge-parent-clear.argument.contexts=kontekstit, joissa vanhemmat poistetaan
luckperms.usage.sponge-option-info.description=Näyttää tietoja Subjektin vaihtoehdoista
luckperms.usage.sponge-option-info.argument.contexts=kontekstit, joilla suodatetaan
luckperms.usage.sponge-option-set.description=Asettaa Sponge-vaihtoehdon
luckperms.usage.sponge-option-set.argument.key=vaihtoehto, joka asetetaan
luckperms.usage.sponge-option-set.argument.value=vaihtoehdon arvo
luckperms.usage.sponge-option-set.argument.contexts=kontekstit, joissa vaihtoehto asetetaan
luckperms.usage.sponge-option-unset.description=Poistaa vaihtoehdon asetuksen Subjektilta
luckperms.usage.sponge-option-unset.argument.key=avain, jonka asetus poistetaan
luckperms.usage.sponge-option-unset.argument.contexts=kontekstit, joissa avain poistetaan
luckperms.usage.sponge-option-clear.description=Poistaa Subjektin vaihtoehdot
luckperms.usage.sponge-option-clear.argument.contexts=kontekstit, joissa vaihtoehdot poistetaan
