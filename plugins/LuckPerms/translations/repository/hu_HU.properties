luckperms.logs.actionlog-prefix=NAPLÓ
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTÀLÀS
luckperms.commandsystem.available-commands=<PERSON><PERSON><PERSON>ld {0} a rendelkezésre álló parancsok megtekintéséhez
luckperms.commandsystem.command-not-recognised=Ismeretlen parancs
luckperms.commandsystem.no-permission=Nincs jogod a parancs has<PERSON>\!
luckperms.commandsystem.no-permission-subcommands=<PERSON>nc<PERSON> jogod egy al-parancs használatához sem
luckperms.commandsystem.already-executing-command=Egy másik parancs végrehajtása folyamatban van. Várakozás a befejezésére...
luckperms.commandsystem.usage.sub-commands-header=Al-Parancsok
luckperms.commandsystem.usage.usage-header=Parancs Hasz<PERSON>lat
luckperms.commandsystem.usage.arguments-header=Argumentumok
luckperms.first-time.no-permissions-setup=<PERSON><PERSON>, még nem lett beállítva egy jogosultság sem\!
luckperms.first-time.use-console-to-give-access=<PERSON>el<PERSON>tt bármelyik LuckPerms parancsot használnád a játékban, szükséged lesz a konzolra, hogy jogosultságokat adj magadnak
luckperms.first-time.console-command-prompt=Nyisd meg a konzolt és futtasd
luckperms.first-time.next-step=Miután elvégezted ezt a műveletet, elkezdheted meghatározni a jogokat és csoportokat
luckperms.first-time.wiki-prompt=Nem tudod, hol kezdd? Nézd meg itt\: {0}
luckperms.login.try-again=Kérlek, próbáld újra később
luckperms.login.loading-database-error=Jogi adatok betöltésekkor hiba történt az adatbázisban
luckperms.login.server-admin-check-console-errors=Amennyiben szerver adminisztrátor vagy, kérlek ellenőrizd a konzolt, hogy nincs-e bármilyen más hiba
luckperms.login.server-admin-check-console-info=Kérlek ellenőrizd a szerver-konzolt további információért
luckperms.login.data-not-loaded-at-pre=A felhasználók jogi adatai nem voltak betöltve a bejelentkezés előtti szakaszban
luckperms.login.unable-to-continue=a folytatás nem lehetséges
luckperms.login.craftbukkit-offline-mode-error=ez valószínűleg a CraftBukkit és online-mode beállítás közötti konfliktusnak köszönhető
luckperms.login.unexpected-error=Váratlan hiba történt a jogi adatok beállítása közben
luckperms.opsystem.disabled=A vanilla OP rendszer le van tiltva a szerveren
luckperms.opsystem.sponge-warning=Kérlek, vedd figyelembe, hogy a Szerver Operátor státusz nem befolyásolja a Sponge jog-ellenőrzéseket, ha jogkezelő plugin van telepítve, közvetlenül módosítsd a felhasználói adatokat
luckperms.duration.unit.years.plural={0} év
luckperms.duration.unit.years.singular={0} év
luckperms.duration.unit.years.short={0}é
luckperms.duration.unit.months.plural={0} hónap
luckperms.duration.unit.months.singular={0} hónap
luckperms.duration.unit.months.short={0}hó
luckperms.duration.unit.weeks.plural={0} hét
luckperms.duration.unit.weeks.singular={0} hét
luckperms.duration.unit.weeks.short={0}hét
luckperms.duration.unit.days.plural={0} nap
luckperms.duration.unit.days.singular={0} nap
luckperms.duration.unit.days.short={0}n
luckperms.duration.unit.hours.plural={0} óra
luckperms.duration.unit.hours.singular={0} óra
luckperms.duration.unit.hours.short={0}ó
luckperms.duration.unit.minutes.plural={0} perc
luckperms.duration.unit.minutes.singular={0} perc
luckperms.duration.unit.minutes.short={0}p
luckperms.duration.unit.seconds.plural={0} másodperc
luckperms.duration.unit.seconds.singular={0} másodperc
luckperms.duration.unit.seconds.short={0}mp
luckperms.duration.since={0} óta
luckperms.command.misc.invalid-code=Helytelen kód
luckperms.command.misc.response-code-key=válaszkód
luckperms.command.misc.error-message-key=üzenet
luckperms.command.misc.bytebin-unable-to-communicate=Nem lehet kommunikálni a bytebin-nel
luckperms.command.misc.webapp-unable-to-communicate=Nem lehet kommunikálni a web alkalmazással
luckperms.command.misc.check-console-for-errors=Ellenőrizd a konzolt a hibákért
luckperms.command.misc.file-must-be-in-data={0} fájlnak közvetlenül benne kell lennie az adatokat tartalmazó mappában
luckperms.command.misc.wait-to-finish=Kérlek várj, míg befejeződik, majd próbáld újra
luckperms.command.misc.invalid-priority=Érvénytelen prioritás {0}
luckperms.command.misc.expected-number=Meg kell adni egy számot
luckperms.command.misc.date-parse-error={0} dátumot nem lehet elemezni
luckperms.command.misc.date-in-past-error=Nem állíthatsz be múltbeli dátumot\!
luckperms.command.misc.page={0}. oldal a {1}-ből/ból
luckperms.command.misc.page-entries={0} bejegyzés
luckperms.command.misc.none=Nincs
luckperms.command.misc.loading.error.unexpected=Váratlan hiba történt
luckperms.command.misc.loading.error.user=A felhasználó nincs betöltve
luckperms.command.misc.loading.error.user-specific=Nem sikerült betölteni a célfelhasználót\: {0}
luckperms.command.misc.loading.error.user-not-found={0} nevű felhasználó nem található
luckperms.command.misc.loading.error.user-save-error=Hiba lépett fel {0} felhasználói adatinak mentése közben
luckperms.command.misc.loading.error.user-not-online={0} nevű felhasználó jelenleg nem elérhető
luckperms.command.misc.loading.error.user-invalid={0} egy érvénytelen név/uuid
luckperms.command.misc.loading.error.user-not-uuid={0} egy érvénytelen uuid
luckperms.command.misc.loading.error.group=A csoport nincs betöltve
luckperms.command.misc.loading.error.all-groups=Nem lehet minden csoportot betölteni
luckperms.command.misc.loading.error.group-not-found={0} nevű csoport nem található
luckperms.command.misc.loading.error.group-save-error=Hiba lépett fel a(z) {0} csoport adatainak mentése közben
luckperms.command.misc.loading.error.group-invalid={0} egy érvénytelen csoportnév
luckperms.command.misc.loading.error.track=A ranglétra nincs betöltve
luckperms.command.misc.loading.error.all-tracks=Nem lehet minden ranglétrát betölteni
luckperms.command.misc.loading.error.track-not-found={0} nevű ranglétra nem található
luckperms.command.misc.loading.error.track-save-error=Hiba lépett fel a(z) {0} ranglétra adatainak mentése közben
luckperms.command.misc.loading.error.track-invalid={0} egy érvénytelen ranglétranév
luckperms.command.editor.no-match=A szerkesztő nem nyitható meg, egyetlen objektum sem felel meg a kívánt típusnak
luckperms.command.editor.start=Új szerkesztő előkészítése, kérlek várj...
luckperms.command.editor.url=Kattints az alábbi linkre a szerkesztő megnyitásához
luckperms.command.editor.unable-to-communicate=Nem lehet kommunikálni a webszerkesztővel
luckperms.command.editor.apply-edits.success=Webszerkesztő adatok alkalmazva {1} {0} számára
luckperms.command.editor.apply-edits.success-summary={0} {1} és {2} {3}
luckperms.command.editor.apply-edits.success.additions=hozzáadás
luckperms.command.editor.apply-edits.success.additions-singular=hozzáadás
luckperms.command.editor.apply-edits.success.deletions=törlés
luckperms.command.editor.apply-edits.success.deletions-singular=törlés
luckperms.command.editor.apply-edits.no-changes=A webszerkesztőn nem történt változtatás, a visszaküldött adatok nem tartalmaztak egy szerkesztést sem
luckperms.command.editor.apply-edits.unknown-type=A megadott objektum nem illeszkedett a kívánt típushoz, így nem lehetett alkalmazni a módosításokat
luckperms.command.editor.apply-edits.unable-to-read=Az adatok nem olvashatóak be a megadott kóddal
luckperms.command.search.searching.permission={0} joggal rendelkező felhasználók és csoportok keresése
luckperms.command.search.searching.inherit={0} jogot öröklő felhasználók és csoportok keresése
luckperms.command.search.result={0} találat, {1} felhasználó és {2} csoport
luckperms.command.search.result.default-notice=Megjegyzés\: a keresés során az alap (default) csoporttal rendelkező felhasználók között az offline felhasználók, akiknek nincs más joguk, nem lesznek megjelenítve\!
luckperms.command.search.showing-users=Felhasználói találatok megjelenítése
luckperms.command.search.showing-groups=Csoport találatok megjelenítése
luckperms.command.tree.start=Jogosultsági fa generálása, kérlek várj...
luckperms.command.tree.empty=Nem lehet generálni jogosultsági fát, nem találhatóak eredmények
luckperms.command.tree.url=Jogosultsági fa URL-je
luckperms.command.verbose.invalid-filter={0} egy érvénytelen verbose szűrő
luckperms.command.verbose.enabled=Részletes naplózás {0} a következő találatokra (szűrő)\: {1}
luckperms.command.verbose.command-exec={0} erőltetése a {1} parancs futtatására, minden elvégzett ellenőrzés jelentése...
luckperms.command.verbose.off=Részletes naplózás {0}
luckperms.command.verbose.command-exec-complete=Parancs futtatása végrehajtva
luckperms.command.verbose.command.no-checks=A parancs végrehajtása befejeződött, de nem történt jogosultság ellenőrzés
luckperms.command.verbose.command.possibly-async=Ez valószínűleg azért van, mert a plugin parancsokat futtat a háttérben (async)
luckperms.command.verbose.command.try-again-manually=Manuálisan is használhatod a részletes naplózást az ilyen ellenőrzésekhez
luckperms.command.verbose.enabled-recording=Részletes naplózás-felvétel {0} a következő találatokra (szűrő)\: {1}
luckperms.command.verbose.uploading=Részletes naplózás {0}, eredmények feltöltése...
luckperms.command.verbose.url=Részletes napló eredményének URL-je
luckperms.command.verbose.enabled-term=engedélyezve
luckperms.command.verbose.disabled-term=kikapcsolva
luckperms.command.verbose.query-any=BÁRMI
luckperms.command.info.running-plugin=Futó
luckperms.command.info.platform-key=Platform
luckperms.command.info.server-brand-key=Szerver márka
luckperms.command.info.server-version-key=Szerver verzió
luckperms.command.info.storage-key=Tárhely
luckperms.command.info.storage-type-key=Típus
luckperms.command.info.storage.meta.split-types-key=Típusok
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Csatlakozva
luckperms.command.info.storage.meta.file-size-key=Fájlméret
luckperms.command.info.extensions-key=Kiegészítők
luckperms.command.info.messaging-key=Üzenetküldés
luckperms.command.info.instance-key=Részletek
luckperms.command.info.static-contexts-key=Statikus kontextusok
luckperms.command.info.online-players-key=Online játékosok
luckperms.command.info.online-players-unique={0} különböző
luckperms.command.info.uptime-key=Üzemidő
luckperms.command.info.local-data-key=Helyi adatok
luckperms.command.info.local-data={0} felhasználó, {1} csoport, {2} ranglétra
luckperms.command.generic.create.success={0} sikeresen létrehozva
luckperms.command.generic.create.error=Hiba lépett fel {0} létrehozása közben
luckperms.command.generic.create.error-already-exists={0} már létezik\!
luckperms.command.generic.delete.success={0} sikeresen törölve
luckperms.command.generic.delete.error=Hiba lépett fel {0} törlése közben
luckperms.command.generic.delete.error-doesnt-exist={0} nem létezik\!
luckperms.command.generic.rename.success={0} sikeresen átnevezve erre\: {1}
luckperms.command.generic.clone.success={0} sikeresen átmásolva {1} -ra/re
luckperms.command.generic.info.parent.title=Szülő csoportok
luckperms.command.generic.info.parent.temporary-title=Ideiglenes Szülő Csoportok
luckperms.command.generic.info.expires-in=lejár
luckperms.command.generic.info.inherited-from=örökli a következőtől
luckperms.command.generic.info.inherited-from-self=önmaga
luckperms.command.generic.show-tracks.title={0} ranglétrái
luckperms.command.generic.show-tracks.empty={0} nincs egy ranglétrán sem
luckperms.command.generic.clear.node-removed={0} elem eltávolítva
luckperms.command.generic.clear.node-removed-singular={0} elem eltávolítva
luckperms.command.generic.clear={0} elem el lett eltávolítva, {1} kontextusban
luckperms.command.generic.permission.info.title={0} jogosultságai
luckperms.command.generic.permission.info.empty={0} nem rendelkezik semmilyen jogosultsággal
luckperms.command.generic.permission.info.click-to-remove={0} ezen jogának eltávolításához kattints ide
luckperms.command.generic.permission.check.info.title={0} jogosultság információk
luckperms.command.generic.permission.check.info.directly={0} {1} joga {2} értékű lett, {3} kontextusban
luckperms.command.generic.permission.check.info.inherited={0} a(z) {1} jogot {2} értékben örökli a(z) {3} csoporttól, {4} kontextusban
luckperms.command.generic.permission.check.info.not-directly={0} nem rendelkezik {1} joggal
luckperms.command.generic.permission.check.info.not-inherited={0} nem örökli a(z) {1} jogot
luckperms.command.generic.permission.check.result.title={0} jog ellenőrzés
luckperms.command.generic.permission.check.result.result-key=Eredmény
luckperms.command.generic.permission.check.result.processor-key=Processzor
luckperms.command.generic.permission.check.result.cause-key=Alap
luckperms.command.generic.permission.check.result.context-key=Kontextusok
luckperms.command.generic.permission.set={0} jog {1} értékre állítva {2}-nak/nek, {3} kontextusban
luckperms.command.generic.permission.already-has={0} már rendelkezik {1} joggal, {2} kontextusban
luckperms.command.generic.permission.set-temp={0} jog {1} értékre állítva {2}-nak/nek, {3} időre, {4} kontextusban
luckperms.command.generic.permission.already-has-temp={0} már rendelkezik {1} ideiglenes joggal, {2} kontextusban
luckperms.command.generic.permission.unset={0} {1} joga eltávolítva, {2} kontextusban
luckperms.command.generic.permission.doesnt-have={0} nem rendelkezik {1} joggal, {2} kontextusban
luckperms.command.generic.permission.unset-temp={0} ideglenes jog eltávolítva {1} felhasználótól, {2} kontextusban
luckperms.command.generic.permission.subtract={0} jog, {1} értékre állítva, {2} nak/nek, {3} időre, {4} kontextusban, {5} értékkel kevesebb, mint ezelőtt
luckperms.command.generic.permission.doesnt-have-temp={0} nem rendelkezik {1} ideglenes joggal, {2} kontextusban
luckperms.command.generic.permission.clear={0} jogai törölve {1} kontextusban
luckperms.command.generic.parent.info.title={0} szülő csoportjai
luckperms.command.generic.parent.info.empty={0} nem rendelkezik semmilyen szülő csoporttal
luckperms.command.generic.parent.info.click-to-remove={0} ezen szülőcsoportjának eltávolításához kattints ide
luckperms.command.generic.parent.add={0} mostantól örökli a(z) {1} csoport jogait, {2} kontextusban
luckperms.command.generic.parent.add-temp={0} mostantól örökli a(z) {1} csoport jogait, {2} időtartamra, {3} kontextusban
luckperms.command.generic.parent.set={0} öröklései törölve lettek, most már csak a(z) {1} csoport jogait örökli {2} kontextusban
luckperms.command.generic.parent.set-track={0} szülő-csoportjai törölve lettek a(z) {1} ranglétrán, most már csak a(z) {2} ranglétra jogait örökli {3} kontextusban
luckperms.command.generic.parent.remove={0} mostantól nem örökli a(z) {1} csoport jogait {2} kontextusban
luckperms.command.generic.parent.remove-temp={0} mostantól nem örökli a(z) {1} ideiglenes csoport jogait {2} kontextusban
luckperms.command.generic.parent.subtract={0} örökölni fogja a(z) {1} csoport jogait, {2} időtartamra, {3} kontextusban, {4}-el/al kevesebb idő mint korábban
luckperms.command.generic.parent.clear={0} szülő-csoport el lett eltávolítva {1} kontextusban
luckperms.command.generic.parent.clear-track={0} szülő-csoportjai a(z) {1} ranglétráról törölve, {2} kontextusban
luckperms.command.generic.parent.already-inherits={0} már örökli a(z) {1} szülő-csoportot, {2} kontextusban
luckperms.command.generic.parent.doesnt-inherit={0} nem örökli a(z) {1} szülő-csoportot, {2} kontextusban
luckperms.command.generic.parent.already-temp-inherits={0} már ideiglenesen örökli a(z) {1} csoportot {2} kontextusban
luckperms.command.generic.parent.doesnt-temp-inherit={0} nem örökli ideiglenesen a(z) {1} csoportot {2} kontextusban
luckperms.command.generic.chat-meta.info.title-prefix={0} Előtagjai
luckperms.command.generic.chat-meta.info.title-suffix={0} Utótagjai
luckperms.command.generic.chat-meta.info.none-prefix={0} nem rendelkezik előtaggal
luckperms.command.generic.chat-meta.info.none-suffix={0} nem rendelkezik utótaggal
luckperms.command.generic.chat-meta.info.click-to-remove={1} ezen {0} meta eltávolításához kattints ide
luckperms.command.generic.chat-meta.already-has={0} már rendelkezik {1} {2} metaadattal {3} prioritással, {4} kontextusban
luckperms.command.generic.chat-meta.already-has-temp={0}-nak/nek már van {1} {2} ideiglenes metaadata {3} prioritással {4} kontextusban
luckperms.command.generic.chat-meta.doesnt-have={0}-nak/nek nincs {1} {2} metaadata {3} prioritással {4} kontextusban
luckperms.command.generic.chat-meta.doesnt-have-temp={0}-nak/nek nincs {1} {2} ideiglenes metaadata {3} prioritással {4} kontextusban
luckperms.command.generic.chat-meta.add={0}-nak/nek {1} {2} metaadat {3} prioritással hozzáadva {4} kontextusban
luckperms.command.generic.chat-meta.add-temp={0}-nak/nek {1} {2} metaadat {3} prioritással hozzáadva, {4} időre, {5} kontextusban
luckperms.command.generic.chat-meta.remove={0} eltávolította a {1} {2} {3} prioritású metaadatot {4} kontextusban
luckperms.command.generic.chat-meta.remove-bulk={0} eltávolította az összes {2} prioritású {1}-t, {3} kontextusban
luckperms.command.generic.chat-meta.remove-temp={0} eltávolította az ideiglenes {1} {2} {3} prioritású metaadatot {4} kontextusban
luckperms.command.generic.chat-meta.remove-temp-bulk={0} eltávolította az összes {2} prioritású ideiglenes {1}-t, {3} kontextusban
luckperms.command.generic.meta.info.title={0} Meta-ja
luckperms.command.generic.meta.info.none={0} nem rendelkezik meta-val
luckperms.command.generic.meta.info.click-to-remove={0} ezen meta eltávolításához kattints ide
luckperms.command.generic.meta.already-has=A (z) {0} metakulcs már {1} {2} a kontextusban {3}
luckperms.command.generic.meta.already-has-temp=A {0} metakulcs már {1} ideiglenesen {2} a kontextusban {3}
luckperms.command.generic.meta.doesnt-have=A (z) {0} metakulcs {1} nincs kontextusban beállítva {2}
luckperms.command.generic.meta.doesnt-have-temp={0} nincs meta kulcs {1} ideiglenesen beállítva a kontextusban {2}
luckperms.command.generic.meta.set=Állítsa a metakulcsot {0} {1} értékre a (z) {2} számára a kontextusban {3}
luckperms.command.generic.meta.set-temp=Állítsa a {0} metakulcsot {1} értékre {2} időtartamra {3} összefüggésben {4}
luckperms.command.generic.meta.unset=A (z) {0} metakulcs beállítása a (z) {1} kontextusban {2}
luckperms.command.generic.meta.unset-temp=Az ideiglenes metakulcs ({0}) beállítása a (z) {1} kontextusban {2}
luckperms.command.generic.meta.clear={0} metaegyezési típusát {1} a (z) {2} kontextusban törölte
luckperms.command.generic.contextual-data.title=Kontextus adatok
luckperms.command.generic.contextual-data.mode.key=mód
luckperms.command.generic.contextual-data.mode.server=szerver
luckperms.command.generic.contextual-data.mode.active-player=aktív játékos
luckperms.command.generic.contextual-data.contexts-key=Kontextusok
luckperms.command.generic.contextual-data.prefix-key=Előtag
luckperms.command.generic.contextual-data.suffix-key=Utótag
luckperms.command.generic.contextual-data.primary-group-key=Elsődleges csoport
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Nincs
luckperms.command.user.info.title=Felhasználói adatok
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=típus
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Állapot
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Nem távolíthatod el az elsődleges csoportjából
luckperms.command.user.primarygroup.not-member={0} nem volt tagja a {1} csoportnak, hozzáadás most
luckperms.command.user.primarygroup.already-has={0} {1} csoportja már be van állítva elsődlegesként
luckperms.command.user.primarygroup.warn-option=Figyelem\: A szerver ({0}) által használt elsődleges csoportszámítási módszer nem feltétlenül tükrözi ezt a változást
luckperms.command.user.primarygroup.set={0} elsődleges csoportja beállítva {1} értékre
luckperms.command.user.track.error-not-contain-group={0} még nincs a (z) {1} csoportban
luckperms.command.user.track.unsure-which-track=Nem biztos abban, hogy melyik pályát használja, adja meg érvként
luckperms.command.user.track.missing-group-advice=Hozzon létre egy csoportot, vagy törölje a ranglétráról majd próbálja újra
luckperms.command.user.promote.added-to-first=A (z) {0} nincs a (z) {1} domainen található csoportokban, ezért felvették őket az első csoportba, {2} összefüggésben {3}
luckperms.command.user.promote.not-on-track={0} nincs benne egyetlen csoportban sem a(z) {1} ranglétrán, így nem lett előléptetve
luckperms.command.user.promote.success=A {0} pálya {1} promóciója a (z) {2} és a (z) {3} kontextusban {4}
luckperms.command.user.promote.end-of-track=Elérte a (z) {0} sáv végét, nem tudta népszerűsíteni a (z) {1} alkalmazást
luckperms.command.user.promote.next-group-deleted=A ranglétra következő csoportja, {0}, már nem létezik
luckperms.command.user.promote.unable-to-promote=Nem sikerült előléptetni a felhasználót
luckperms.command.user.demote.success=A (z) {0} pálya mentén {1} leépítés {2} és {3} kontextusban {4}
luckperms.command.user.demote.end-of-track=A (z) {0} sáv vége elérkezett, ezért a (z) {1} eltávolításra került a (z) {2} listáról
luckperms.command.user.demote.end-of-track-not-removed=A (z) {0} szám vége elérkezett, de {1} nem került eltávolításra az első csoportból
luckperms.command.user.demote.previous-group-deleted=A ranglétrán az előző csoport, {0}, már nem létezik
luckperms.command.user.demote.unable-to-demote=Nem sikerült lefokozni a felhasználót
luckperms.command.group.list.title=Csoportok
luckperms.command.group.delete.not-default=Az alapértelmezett csoportot nem tudod törölni
luckperms.command.group.info.title=Csoport információ
luckperms.command.group.info.display-name-key=Megjelenítési név
luckperms.command.group.info.weight-key=Súly
luckperms.command.group.setweight.set={1} csoport súlya beállítva {0}-ra/re
luckperms.command.group.setdisplayname.doesnt-have={0} csoportnak nincs megjelenítési neve
luckperms.command.group.setdisplayname.already-has={0} csoport már rendelkezik ezzel a megjelenítési névvel\: {1}
luckperms.command.group.setdisplayname.already-in-use=A(z) {0} csoport megjelenítési nevét már használja a(z) {1} csoport
luckperms.command.group.setdisplayname.set=A megjelenített név beállítása {0} a (z) {1} csoporthoz a (z) {2} kontextusban
luckperms.command.group.setdisplayname.removed=A (z) {0} csoport megjelenített neve eltávolítva a (z) {1} kontextusban
luckperms.command.track.list.title=Ranglétrák
luckperms.command.track.path.empty=Nincs
luckperms.command.track.info.showing-track=Ranglétra megjelenítése
luckperms.command.track.info.path-property=Elérési út
luckperms.command.track.clear={0} csoportjainak száma törölve lett
luckperms.command.track.append.success=A {0} csoport hozzáadva a(z) {1} ranglétrához
luckperms.command.track.insert.success=A (z) {0} csoport bekerült a (z) {1} sávba a (z) {2} pozícióban
luckperms.command.track.insert.error-number=Várható szám, de ehelyett megkapta\: {0}
luckperms.command.track.insert.error-invalid-pos=Nem lehet beszúrni a (z) {0} pozícióba
luckperms.command.track.insert.error-invalid-pos-reason=érvénytelen pozíció
luckperms.command.track.remove.success=A (z) {0} csoport eltávolítva a (z) {1} sávból
luckperms.command.track.error-empty=A (z) {0} nem használható, mivel üres, vagy csak egy csoportot tartalmaz
luckperms.command.track.error-multiple-groups={0} ezen a pályán több csoport tagja
luckperms.command.track.error-ambiguous=Nem sikerült meghatározni a helyüket
luckperms.command.track.already-contains=A (z) {0} már tartalmazza a következőt\: {1}
luckperms.command.track.doesnt-contain=A (z) {0} nem tartalmazza a következőt\: {1}
luckperms.command.log.load-error=Az adatok nem tölthetőek be
luckperms.command.log.invalid-page=Érvénytelen oldalszám
luckperms.command.log.invalid-page-range=Adjon meg egy értéket {0} és {1} között
luckperms.command.log.empty=Nincs megjeleníthető naplóbejegyzés
luckperms.command.log.notify.error-console=A konzol értesítései nem kapcsolhatók át
luckperms.command.log.notify.enabled-term=Engedélyezve
luckperms.command.log.notify.disabled-term=Letiltva
luckperms.command.log.notify.changed-state={0} naplózási kimenet
luckperms.command.log.notify.already-on=Már kap értesítéseket
luckperms.command.log.notify.already-off=Jelenleg nem kap értesítéseket
luckperms.command.log.notify.invalid-state=Ismeretlen állapot. Várakozás\: {0} vagy {1}
luckperms.command.log.show.search=Legutóbbi műveletek megjelenítése a (z) {0} lekérdezéshez
luckperms.command.log.show.recent=A legutóbbi műveletek megjelenítése
luckperms.command.log.show.by={0} legutóbbi műveleteinek megjelenítése
luckperms.command.log.show.history=A (z) {0} {1} előzményeinek megjelenítése
luckperms.command.export.error-term=Hiba
luckperms.command.export.already-running=Egy másik exportálási folyamat már fut
luckperms.command.export.file.already-exists=A (z) {0} fájl már létezik
luckperms.command.export.file.not-writable=A (z) {0} fájl nem írható
luckperms.command.export.file.success=Sikeres exportálás ide\: {0}
luckperms.command.export.file-unexpected-error-writing=Váratlan hiba történt a fájlba való írás közben
luckperms.command.export.web.export-code=Export kód
luckperms.command.export.web.import-command-description=Az importáláshoz használja a következő parancsot
luckperms.command.import.term=Importálás
luckperms.command.import.error-term=Hiba
luckperms.command.import.already-running=Egy másik importálási folyamat már fut
luckperms.command.import.file.doesnt-exist=A (z) {0} fájl nem létezik
luckperms.command.import.file.not-readable=A (z) {0} fájl nem olvasható
luckperms.command.import.file.unexpected-error-reading=Váratlan hiba történt az importfájl olvasása közben
luckperms.command.import.file.correct-format=a helyes formátum?
luckperms.command.import.web.unable-to-read=Nem lehet beolvasni az adatokat a megadott kód segítségével
luckperms.command.import.progress.percent={0}% kész
luckperms.command.import.progress.operations=A (z) {0} / {1} műveletek befejeződtek
luckperms.command.import.starting=Az importálási folyamat elindítása
luckperms.command.import.completed=TELJES
luckperms.command.import.duration={0} másodpercig tartott
luckperms.command.bulkupdate.must-use-console=A tömeges frissítés parancs csak a konzolról használható
luckperms.command.bulkupdate.invalid-data-type=Érvénytelen típus, a következőre számított\: {0}
luckperms.command.bulkupdate.invalid-constraint=Érvénytelen korlátozás {0}
luckperms.command.bulkupdate.invalid-constraint-format=A korlátozásoknak a következő formátumban kell lenniük\: {0}
luckperms.command.bulkupdate.invalid-comparison=Érvénytelen összehasonlító operátor {0}
luckperms.command.bulkupdate.invalid-comparison-format=Számított a következők egyikére\: {0}
luckperms.command.bulkupdate.queued=A tömeges frissítési művelet várakozott
luckperms.command.bulkupdate.confirm=Futtassa a (z) {0} parancsot a frissítés végrehajtásához
luckperms.command.bulkupdate.unknown-id=A (z) {0} azonosítójú művelet nem létezik, vagy lejárt
luckperms.command.bulkupdate.starting=Tömeges frissítés fut
luckperms.command.bulkupdate.success=A tömeges frissítés sikeresen befejeződött
luckperms.command.bulkupdate.success.statistics.nodes=Az összes érintett csomópont
luckperms.command.bulkupdate.success.statistics.users=Az érintett felhasználók száma összesen
luckperms.command.bulkupdate.success.statistics.groups=Az összes érintett csoport
luckperms.command.bulkupdate.failure=A tömeges frissítés nem sikerült, ellenőrizze, hogy a konzolon nincsenek-e hibák
luckperms.command.update-task.request=Frissítési feladatot kértek, várjon
luckperms.command.update-task.complete=A feladat frissítése befejeződött
luckperms.command.update-task.push.attempting=Most megpróbál más szerverekre továbbítani
luckperms.command.update-task.push.complete=A többi szervert a (z) {0} keresztül sikeresen értesítettük
luckperms.command.update-task.push.error=Hiba történt a módosítások más szerverekre történő áttöltése közben
luckperms.command.update-task.push.error-not-setup=Nem sikerült más szervereken végrehajtani a változásokat, mert az üzenetkezelés nincs beállítva
luckperms.command.reload-config.success=A konfigurációs fájl sikeresen újratöltve
luckperms.command.reload-config.restart-note=egyes opciók csak a szerver újraindítását követően érvényesülnek
luckperms.command.translations.searching=Elérhető fordítások keresése, kérlek várj...
luckperms.command.translations.searching-error=Nem sikerült lekérni a rendelkezésre álló fordítások listáját
luckperms.command.translations.installed-translations=Telepített fordítások
luckperms.command.translations.available-translations=Elérhető fordítások
luckperms.command.translations.percent-translated={0}% lefordítva
luckperms.command.translations.translations-by=által
luckperms.command.translations.installing=Fordítások telepítése folyamatban, kérlek várj...
luckperms.command.translations.download-error=A(z) {0} fordításának letöltése sikertelen
luckperms.command.translations.installing-specific={0} nyelv telepítése folyamatban...
luckperms.command.translations.install-complete=Telepítés befejezve
luckperms.command.translations.download-prompt=Használd {0}, hogy letöltsd és telepítsd ezeknek a fordításoknak a közösség által biztosított naprakész verziót
luckperms.command.translations.download-override-warning=Kérjük, vegye figyelembe, hogy ez felülírja az ezeken a nyelveken végzett módosításokat
luckperms.usage.user.description=Felhasználók kezelésére alkalmas parancs a LuckPerms-en belül. (A ''user'' a LuckPerms-ben csak egy játékos, ami hivatkozhat UUID-re/ra vagy felhasználónévre)
luckperms.usage.group.description=Csoportok kezelésére alkalmas parancs a LuckPerms-en belül. A csoportok csak jogok gyűjteményei, amelyeket a felhasználóknak adhatunk. Új csoportok létrehozásához használd a ''creategroup'' al-parancsot.
luckperms.usage.track.description=Ranglétrák kezelésére alkalmas parancs a LuckPerms-en belül. A ranglétrák a csoportok gyűjteménye, amely felhasználható az előléptetés és lefokozás meghatározására.
luckperms.usage.log.description=Naplózási funkciók kezelésére alkalmas parancs a LuckPerms-en belül.
luckperms.usage.sync.description=Újratölti az összes adatot a beépülő modulok memóriájából, és végrehajtja az észlelt változásokat.
luckperms.usage.info.description=Általános információkat nyomtat az aktív plugin példányról.
luckperms.usage.editor.description=Új webszerkesztő munkamenetet indít
luckperms.usage.editor.argument.type=a szerkesztőben betöltendő adat típusa. (''all'', ''users'' vagy ''groups'')
luckperms.usage.editor.argument.filter=engedély a felhasználói bejegyzések szűrésére
luckperms.usage.verbose.description=A bővítmények részletes engedélyellenőrző felügyeleti rendszerét vezérli.
luckperms.usage.verbose.argument.action=a naplózás engedélyezése / letiltása, vagy a naplózott kimenet feltöltése
luckperms.usage.verbose.argument.filter=a szűrő, hogy megfeleljen a bejegyzéseknek
luckperms.usage.verbose.argument.commandas=a lejátszó / parancs futtatásához
luckperms.usage.tree.description=Fa nézetet (rendezett listahierarchiát) generál a LuckPerms által ismert összes engedélyről.
luckperms.usage.tree.argument.scope=a fa gyökere. adja meg a "." hogy minden engedélyt belefoglaljon
luckperms.usage.tree.argument.player=egy online játékos neve, akivel szemben ellenőrizni kell
luckperms.usage.search.description=Megadott jogosultsággal rendelkező felhasználók/csoportok keresése
luckperms.usage.search.argument.permission=a keresés engedélye
luckperms.usage.search.argument.page=megtekintendő oldal
luckperms.usage.network-sync.description=A szinkronizálás változásai a tárolóval, és kérik, hogy a hálózat összes többi kiszolgálója tegye ugyanezt
luckperms.usage.import.description=Adatokat importál egy (korábban létrehozott) exportfájlból
luckperms.usage.import.argument.file=az importálandó fájl
luckperms.usage.import.argument.replace=egyesítés helyett cserélje ki a meglévő adatokat
luckperms.usage.import.argument.upload=feltölti egy korábbi export adatait
luckperms.usage.export.description=Minden engedélyadatot exportál egy „export” fájlba. Később újra importálható.
luckperms.usage.export.argument.file=az exportálandó fájl
luckperms.usage.export.argument.without-users=kizárja a felhasználókat az exportból
luckperms.usage.export.argument.without-groups=kizárja a csoportokat az exportból
luckperms.usage.export.argument.upload=Töltse fel az összes engedélyadatot a webszerkesztőhöz. Később újra importálható.
luckperms.usage.reload-config.description=Néhány konfigurációs opció újratöltése
luckperms.usage.bulk-update.description=Kötegelt változtatások lekérdezésének futtatása minden adaton
luckperms.usage.bulk-update.argument.data-type=a módosítandó adat típusa. (''all'', ''users'' vagy ''groups'')
luckperms.usage.bulk-update.argument.action=elvégezendő művelet. (''update'' vagy ''delete'')
luckperms.usage.bulk-update.argument.action-field=módosítandó mező. Csak ''update'' esetén szükséges. (''permission'', ''server'' vagy ''world'')
luckperms.usage.bulk-update.argument.action-value=új érték. Csak ''update'' esetén szükséges.
luckperms.usage.bulk-update.argument.constraint=a frissítéshez szükséges korlátozások
luckperms.usage.translations.description=Fordítások kezelése
luckperms.usage.translations.argument.install=al-parancs a fordítások telepítéséhez
luckperms.usage.apply-edits.description=Jogmódosítások végrehajtása a webszerkesztőből
luckperms.usage.apply-edits.argument.code=az adatok egyedi kódja
luckperms.usage.apply-edits.argument.target=kire alkalmazza az adatokat
luckperms.usage.create-group.description=Új csoport létrehozása
luckperms.usage.create-group.argument.name=csoport neve
luckperms.usage.create-group.argument.weight=csoport súlya
luckperms.usage.create-group.argument.display-name=csoport megjelenítendő neve
luckperms.usage.delete-group.description=Csoport törlése
luckperms.usage.delete-group.argument.name=csoport neve
luckperms.usage.list-groups.description=Listázza az összes csoportot a felületen
luckperms.usage.create-track.description=Új ranglétra létrehozása
luckperms.usage.create-track.argument.name=ranglétra neve
luckperms.usage.delete-track.description=Ranglétra törlése
luckperms.usage.delete-track.argument.name=ranglétra neve
luckperms.usage.list-tracks.description=Listázza az összes ranglétrát a felületen
luckperms.usage.user-info.description=Felhasználó információinak megjelenítése
luckperms.usage.user-switchprimarygroup.description=Felhasználó elsődleges csoportjának cseréje
luckperms.usage.user-switchprimarygroup.argument.group=csoport, amire cserélsz
luckperms.usage.user-promote.description=Felhasználó előléptetése a ranglétrán
luckperms.usage.user-promote.argument.track=ranglétra, amin előléptetnéd a felhasználót
luckperms.usage.user-promote.argument.context=kontextus, amiben előlépteted a felhasználót
luckperms.usage.user-promote.argument.dont-add-to-first=csak akkor léptesd elő a felhasználót, ha már a ranglétrán van
luckperms.usage.user-demote.description=Felhasználó lefokozása a ranglétrán
luckperms.usage.user-demote.argument.track=ranglétra, amin lefokoznád a felhasználót
luckperms.usage.user-demote.argument.context=kontextus, amiben lefokoznád a felhasználót
luckperms.usage.user-demote.argument.dont-remove-from-first=megakadályozza a felhasználó eltávolítását az első csoportból
luckperms.usage.user-clone.description=Felhasználó másolása
luckperms.usage.user-clone.argument.user=felhasználó név/uuid, amibe másolnád a felhasználót
luckperms.usage.group-info.description=Csoport információinak megjelenítése
luckperms.usage.group-listmembers.description=Megjeleníti azokat a felhasználókat/csoportokat, akik öröklik a megadott csoportot
luckperms.usage.group-listmembers.argument.page=megtekintendő oldal
luckperms.usage.group-setweight.description=Csoport súlyának beállítása
luckperms.usage.group-setweight.argument.weight=beállítandó súly
luckperms.usage.group-set-display-name.description=Csoport megjelenítési nevének beállítása
luckperms.usage.group-set-display-name.argument.name=beállítandó név
luckperms.usage.group-set-display-name.argument.context=kontextus, amiben beállítanád a megjelenési nevet
luckperms.usage.group-rename.description=Csoport átnevezése
luckperms.usage.group-rename.argument.name=új név
luckperms.usage.group-clone.description=Csoport másolása
luckperms.usage.group-clone.argument.name=csoport neve, amibe másolnád a csoportot
luckperms.usage.holder-editor.description=Webszerkesztő megnyitása
luckperms.usage.holder-showtracks.description=Listázza azokat a ranglétrákat, amin a célobjektum van
luckperms.usage.holder-clear.description=Az összes jogok, csoportok és meta-k eltávolítása
luckperms.usage.holder-clear.argument.context=kontextus, amit szűrnél
luckperms.usage.permission.description=Jogok szerkesztése
luckperms.usage.parent.description=Öröklések szerkesztése
luckperms.usage.meta.description=Metaadat értékeinek szerkesztése
luckperms.usage.permission-info.description=Listázza a célobjektum jogait
luckperms.usage.permission-info.argument.page=megtekintendő oldal
luckperms.usage.permission-info.argument.sort-mode=hogyan sorolja a bejegyzéseket
luckperms.usage.permission-set.description=Beállít egy jogot a céltárgynak
luckperms.usage.permission-set.argument.node=beállítandó jog
luckperms.usage.permission-set.argument.value=jog értéke
luckperms.usage.permission-set.argument.context=kontextus, amiben beállítanád a jogot
luckperms.usage.permission-unset.description=Jog eltávolítása a célobjektumtól
luckperms.usage.permission-unset.argument.node=eltávolítandó jog
luckperms.usage.permission-unset.argument.context=kontextus, amiből eltávolítanád a jogot
luckperms.usage.permission-settemp.description=Ideiglenes jog hozzáadása a célobjektumnak
luckperms.usage.permission-settemp.argument.node=hozzáadandó jog
luckperms.usage.permission-settemp.argument.value=jog értéke
luckperms.usage.permission-settemp.argument.duration=időintervallum
luckperms.usage.permission-settemp.argument.temporary-modifier=így érdemes az ideiglenes jogot beállítani
luckperms.usage.permission-settemp.argument.context=kontextus, amiben hozzáadnád a jogot
luckperms.usage.permission-unsettemp.description=Ideiglenes jog eltávolítása a célobjektumtól
luckperms.usage.permission-unsettemp.argument.node=eltávolítandó jog
luckperms.usage.permission-unsettemp.argument.duration=időtartam, amennyit levonnál
luckperms.usage.permission-unsettemp.argument.context=kontextusok, amiből eltávolítanád a jogot
luckperms.usage.permission-check.description=Ellenőrzi, hogy a célobjektum rendelkezik-e a megadott joggal
luckperms.usage.permission-check.argument.node=ellenőrizendő jog
luckperms.usage.permission-clear.description=Összes jog törlése
luckperms.usage.permission-clear.argument.context=kontextus, amit szűrnél
luckperms.usage.parent-info.description=Listázza azokat a csoportokat, amiket a megadott objektum örököl
luckperms.usage.parent-info.argument.page=megtekintendő oldal
luckperms.usage.parent-info.argument.sort-mode=hogy sorold a bejegyzéseket
luckperms.usage.parent-set.description=Eltávolít minden más csoportot amit a céltárgy örököl, és hozzáadja őket a megadotthoz
luckperms.usage.parent-set.argument.group=beállítandó öröklési csoport
luckperms.usage.parent-set.argument.context=lontextus(ok), amiben beállítanád a csoportot
luckperms.usage.parent-add.description=Csoport hozzáadása a célobjektum öröklési fájához, amelytől örökölni fogja a jogokat
luckperms.usage.parent-add.argument.group=hozzáadandó csoport
luckperms.usage.parent-add.argument.context=kontextus, amiben öröklődik a csoport
luckperms.usage.parent-remove.description=Eltávolít egy korábban beállított öröklési szabályt
luckperms.usage.parent-remove.argument.group=eltávolítandó csoport
luckperms.usage.parent-remove.argument.context=kontextus, amiben eltávolítanád a csoportot
luckperms.usage.parent-set-track.description=Eltávolít minden csoportot a célobjektum megadott ranglétrájától és hozzáadja őket a megadott ranglétrához
luckperms.usage.parent-set-track.argument.track=ranglétra, amin beállítanád a csoportot
luckperms.usage.parent-set-track.argument.group=csoport, ahova beállítanád, vagy a csoport pozíciójának sorszáma
luckperms.usage.parent-set-track.argument.context=kontextus, amiben beállítanád a csoportot
luckperms.usage.parent-add-temp.description=Csoport ideglenes hozzáadása a célobjektum öröklési fájához, amelytől ideiglenesen örökölni fogja a jogokat
luckperms.usage.parent-add-temp.argument.group=öröklendő csoport
luckperms.usage.parent-add-temp.argument.duration=időtartam
luckperms.usage.parent-add-temp.argument.temporary-modifier=hogyan kell alkalmazni az ideiglenes jogokat
luckperms.usage.parent-add-temp.argument.context=kontextus, amiben örökölnéd a csoportot
luckperms.usage.parent-remove-temp.description=Eltávolítja a korábban beállított ideglenes öröklődési szabályt
luckperms.usage.parent-remove-temp.argument.group=eltávolítandó csoport
luckperms.usage.parent-remove-temp.argument.duration=időtartam, amennyit levonnál
luckperms.usage.parent-remove-temp.argument.context=kontextus, amiben eltávolítanád a csoportot
luckperms.usage.parent-clear.description=Összes szülő-csoport törlése
luckperms.usage.parent-clear.argument.context=kontextus, amit szűrnél
luckperms.usage.parent-clear-track.description=Ranglétra összes szülő-csoportjának törlése
luckperms.usage.parent-clear-track.argument.track=ranglétra, amiből eltávolítanád
luckperms.usage.parent-clear-track.argument.context=kontextus, amit szűrnél
luckperms.usage.meta-info.description=Összes chat meta megjelenítése
luckperms.usage.meta-set.description=Meta érték beállítása
luckperms.usage.meta-set.argument.key=beállítandó kulcs
luckperms.usage.meta-set.argument.value=érték
luckperms.usage.meta-set.argument.context=kontextus, amiben hozzáadnád a meta-t
luckperms.usage.meta-unset.description=Meta érték törlése
luckperms.usage.meta-unset.argument.key=eltávolítandó kulcs
luckperms.usage.meta-unset.argument.context=kontextus, amiben eltávolítanád a meta-t
luckperms.usage.meta-settemp.description=Ideglenes meta érték beállítása
luckperms.usage.meta-settemp.argument.key=beállítandó kulcs
luckperms.usage.meta-settemp.argument.value=érték
luckperms.usage.meta-settemp.argument.duration=időtartam
luckperms.usage.meta-settemp.argument.context=kontextus, amiben hozzáadnád a meta-t
luckperms.usage.meta-unsettemp.description=Ideiglenes meta érték eltávolítása
luckperms.usage.meta-unsettemp.argument.key=eltávolítandó kulcs
luckperms.usage.meta-unsettemp.argument.context=kontextus, amiben eltávolítanád a meta-t
luckperms.usage.meta-addprefix.description=Előtag hozzáadása
luckperms.usage.meta-addprefix.argument.priority=előtag prioritási szintje
luckperms.usage.meta-addprefix.argument.prefix=maga az előtag
luckperms.usage.meta-addprefix.argument.context=kontextus, amiben hozzáadnád az előtagot
luckperms.usage.meta-addsuffix.description=Utótag hozzáadása
luckperms.usage.meta-addsuffix.argument.priority=utótag prioritási szintje
luckperms.usage.meta-addsuffix.argument.suffix=maga az utótag
luckperms.usage.meta-addsuffix.argument.context=kontextus, amiben hozzáadnád az utótagot
luckperms.usage.meta-setprefix.description=Előtag beállítása
luckperms.usage.meta-setprefix.argument.priority=előtag prioritási szintje
luckperms.usage.meta-setprefix.argument.prefix=maga az előtag
luckperms.usage.meta-setprefix.argument.context=kontextus, amiben beállítanád az előtagot
luckperms.usage.meta-setsuffix.description=Utótag beállítása
luckperms.usage.meta-setsuffix.argument.priority=utótag prioritási szintje
luckperms.usage.meta-setsuffix.argument.suffix=maga az utótag
luckperms.usage.meta-setsuffix.argument.context=kontextus, amiben beállítanád az utótagot
luckperms.usage.meta-removeprefix.description=Előtag eltávolítása
luckperms.usage.meta-removeprefix.argument.priority=előtag prioritási szintje
luckperms.usage.meta-removeprefix.argument.prefix=maga az előtag
luckperms.usage.meta-removeprefix.argument.context=kontextus, amiben eltávolítanád az előtagot
luckperms.usage.meta-removesuffix.description=Utótag eltávolítása
luckperms.usage.meta-removesuffix.argument.priority=utótag prioritási szintje
luckperms.usage.meta-removesuffix.argument.suffix=maga az utótag
luckperms.usage.meta-removesuffix.argument.context=kontextus, amiben eltávolítanád az utótagot
luckperms.usage.meta-addtemp-prefix.description=Előtag ideiglenes hozzáadása
luckperms.usage.meta-addtemp-prefix.argument.priority=előtag prioritási szintje
luckperms.usage.meta-addtemp-prefix.argument.prefix=maga az előtag
luckperms.usage.meta-addtemp-prefix.argument.duration=időtartam
luckperms.usage.meta-addtemp-prefix.argument.context=kontextus, amiben hozzáadnád az előtagot
luckperms.usage.meta-addtemp-suffix.description=Utótag ideiglenes hozzáadása
luckperms.usage.meta-addtemp-suffix.argument.priority=utótag prioritási szintje
luckperms.usage.meta-addtemp-suffix.argument.suffix=maga az utótag
luckperms.usage.meta-addtemp-suffix.argument.duration=időtartam
luckperms.usage.meta-addtemp-suffix.argument.context=kontextus, amiben hozzáadnád az utótagot
luckperms.usage.meta-settemp-prefix.description=Előtag ideiglenes beállítása
luckperms.usage.meta-settemp-prefix.argument.priority=előtag prioritási szintje
luckperms.usage.meta-settemp-prefix.argument.prefix=maga az előtag
luckperms.usage.meta-settemp-prefix.argument.duration=időtartam
luckperms.usage.meta-settemp-prefix.argument.context=kontextus, amiben beállítanád az előtagot
luckperms.usage.meta-settemp-suffix.description=Előtag ideiglenes beállítása
luckperms.usage.meta-settemp-suffix.argument.priority=utótag prioritási szintje
luckperms.usage.meta-settemp-suffix.argument.suffix=maga az utótag
luckperms.usage.meta-settemp-suffix.argument.duration=időtartam
luckperms.usage.meta-settemp-suffix.argument.context=kontextus, amiben beállítanád az utótagot
luckperms.usage.meta-removetemp-prefix.description=Ideiglenes utótag eltávolítása
luckperms.usage.meta-removetemp-prefix.argument.priority=előtag prioritási szintje
luckperms.usage.meta-removetemp-prefix.argument.prefix=maga az előtag
luckperms.usage.meta-removetemp-prefix.argument.context=kontextus, amiben eltávolítanád az előtagot
luckperms.usage.meta-removetemp-suffix.description=Ideiglenes utótag eltávolítása
luckperms.usage.meta-removetemp-suffix.argument.priority=utótag prioritási szintje
luckperms.usage.meta-removetemp-suffix.argument.suffix=maga az utótag
luckperms.usage.meta-removetemp-suffix.argument.context=kontextus, amiben eltávolítanád az utótagot
luckperms.usage.meta-clear.description=Összes meta törlése
luckperms.usage.meta-clear.argument.type=típus, amit eltávolítanál mint meta
luckperms.usage.meta-clear.argument.context=kontextus, amit szűrnél
luckperms.usage.track-info.description=Ranglétra információinak megjelenítése
luckperms.usage.track-editor.description=Webszerkesztő megnyitása
luckperms.usage.track-append.description=Csoport hozzáadása a ranglétra legvégére
luckperms.usage.track-append.argument.group=csoport, amit hozzáadnál
luckperms.usage.track-insert.description=Csoport hozzáadása egy adott helyre a ranglétrán
luckperms.usage.track-insert.argument.group=csoport, amit hozzáadnál
luckperms.usage.track-insert.argument.position=csoport hozzáadásának helye (az első pozíció a ranglétrán az 1)
luckperms.usage.track-remove.description=Csoport eltávolítása a ranglétráról
luckperms.usage.track-remove.argument.group=csoport, amit eltávolítanál
luckperms.usage.track-clear.description=Törli a csoportokat a ranglétrán
luckperms.usage.track-rename.description=Ranglétra átnevezése
luckperms.usage.track-rename.argument.name=új név
luckperms.usage.track-clone.description=Ranglétra másolása
luckperms.usage.track-clone.argument.name=ranglétra neve, amibe másolnád a ranglétrát
luckperms.usage.log-recent.description=A legutóbbi műveletek megtekintése
luckperms.usage.log-recent.argument.user=felhasználó név/uuid, amit szűrnél
luckperms.usage.log-recent.argument.page=megtekintendő oldal száma
luckperms.usage.log-search.description=Bejegyzés keresése a naplóban
luckperms.usage.log-search.argument.query=keresendő adat
luckperms.usage.log-search.argument.page=megtekintendő oldal száma
luckperms.usage.log-notify.description=Naplózási információk ki-/bekapcsolása
luckperms.usage.log-notify.argument.toggle=be- vagy kikapcsolás (''on'' vagy ''off'')
luckperms.usage.log-user-history.description=Felhasználó előzményeinek megtekintése
luckperms.usage.log-user-history.argument.user=felhasználó név/uuid
luckperms.usage.log-user-history.argument.page=megtekintendő oldal száma
luckperms.usage.log-group-history.description=Csoport előzményeinek megtekintése
luckperms.usage.log-group-history.argument.group=csoport neve
luckperms.usage.log-group-history.argument.page=megtekintendő oldal száma
luckperms.usage.log-track-history.description=Ranglétra előzményeinek megtekintése
luckperms.usage.log-track-history.argument.track=ranglétra neve
luckperms.usage.log-track-history.argument.page=megtekintendő oldal száma
luckperms.usage.sponge.description=Extra Sponge adatok szerkesztése
luckperms.usage.sponge.argument.collection=gyűjtemény a lekérdezéshez
luckperms.usage.sponge.argument.subject=a módosítandó objektum
luckperms.usage.sponge-permission-info.description=Információ megjelenítése az alany jogairól
luckperms.usage.sponge-permission-info.argument.contexts=kontextus, amit szűrnél
luckperms.usage.sponge-permission-set.description=Jog beállítása a célobjektumnak
luckperms.usage.sponge-permission-set.argument.node=beállítandó jog
luckperms.usage.sponge-permission-set.argument.tristate=beállítandó jog értéke
luckperms.usage.sponge-permission-set.argument.contexts=kontextus, amiben beállítanád a jogot
luckperms.usage.sponge-permission-clear.description=Alany jogainak törlése
luckperms.usage.sponge-permission-clear.argument.contexts=kontextus, amiben törölnéd a jogokat
luckperms.usage.sponge-parent-info.description=Információ megjelenítése az alany szülő-csoportjairól
luckperms.usage.sponge-parent-info.argument.contexts=kontextus, amit szűrnél
luckperms.usage.sponge-parent-add.description=Szülő-csoport hozzáadása az alanyhoz
luckperms.usage.sponge-parent-add.argument.collection=alanygyűjtemény, ahol a szülő-csoport alany van
luckperms.usage.sponge-parent-add.argument.subject=szülő-csoport alany neve
luckperms.usage.sponge-parent-add.argument.contexts=kontextus, amiben hozzáadnád a szülő-csoportot
luckperms.usage.sponge-parent-remove.description=Szülő-csoport eltávolítása az alanytól
luckperms.usage.sponge-parent-remove.argument.collection=alanygyűjtemény, ahol a szülő-csoport alany van
luckperms.usage.sponge-parent-remove.argument.subject=alany szülő-csoportjának neve
luckperms.usage.sponge-parent-remove.argument.contexts=kontextus, amiben eltávolítanád a szülő-csoportot
luckperms.usage.sponge-parent-clear.description=Alany szülő-csoportjainak törlése
luckperms.usage.sponge-parent-clear.argument.contexts=kontextus, amiben törölnéd a szülő-csoportokat
luckperms.usage.sponge-option-info.description=Információ megjelenítése az alany opcióiról
luckperms.usage.sponge-option-info.argument.contexts=kontextus, amit szűrnél
luckperms.usage.sponge-option-set.description=Opció beállítása az alanynak
luckperms.usage.sponge-option-set.argument.key=beállítandó kulcs
luckperms.usage.sponge-option-set.argument.value=beállítandó kulcs értéke
luckperms.usage.sponge-option-set.argument.contexts=kontextus, amiben beállítanád az opciót
luckperms.usage.sponge-option-unset.description=Opció törlése az alanytól
luckperms.usage.sponge-option-unset.argument.key=eltávolítandó kulcs
luckperms.usage.sponge-option-unset.argument.contexts=kontextus, amiben eltávolítanád a kulcsot
luckperms.usage.sponge-option-clear.description=Alany összes opcióinak törlése
luckperms.usage.sponge-option-clear.argument.contexts=kontextus, amiben törölnéd az opciókat
