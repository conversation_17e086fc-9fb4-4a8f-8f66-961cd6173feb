luckperms.logs.actionlog-prefix=LOG
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTIEREN
luckperms.commandsystem.available-commands=<PERSON>utze {0} um verfügbare Befehle anzuzeigen
luckperms.commandsystem.command-not-recognised=Be<PERSON>hl nicht erkannt
luckperms.commandsystem.no-permission=Du hast keine Rechte, diesen Befehl auszuführen\!
luckperms.commandsystem.no-permission-subcommands=Du hast keine <PERSON>, einen Unterbefehl des Befehls auszuführen
luckperms.commandsystem.already-executing-command=Ein anderer Befehl wird ausgeführt, warte bis er beendet ist...
luckperms.commandsystem.usage.sub-commands-header=Unterbefehle
luckperms.commandsystem.usage.usage-header=Befehls-Verwendung
luckperms.commandsystem.usage.arguments-header=Argumente
luckperms.first-time.no-permissions-setup=E<PERSON> scheint, dass noch keine Berechtigungen eingerichtet wurden\!
luckperms.first-time.use-console-to-give-access=Bevor du einen der LuckPerms-Befehle im Spiel verwenden kannst, musst du die Konsole verwenden, um dir selbst Zugriff zu geben
luckperms.first-time.console-command-prompt=Öffne die Konsole und mache
luckperms.first-time.next-step=Danach kannst du beginnen deine Berechtigungen und Gruppen einzurichten
luckperms.first-time.wiki-prompt=Du weißt nicht, wo du anfangen kannst? Schaue hier\: {0}
luckperms.login.try-again=Bitte versuche es später erneut
luckperms.login.loading-database-error=Beim Laden der Berechtigungen aus der Datenbank ist ein Fehler aufgetreten
luckperms.login.server-admin-check-console-errors=Wenn du ein Server-Admin bist, überprüfe bitte die Konsole auf Fehler
luckperms.login.server-admin-check-console-info=Bitte überprüfe die Server-Konsole auf weitere Informationen
luckperms.login.data-not-loaded-at-pre=Deine Berechtigungen wurden während des Prelogins nicht geladen
luckperms.login.unable-to-continue=Fortfahren nicht möglich
luckperms.login.craftbukkit-offline-mode-error=dies liegt wahrscheinlich an einem Konflikt zwischen CraftBukkit und der online-mode Einstellung
luckperms.login.unexpected-error=Ein unerwarteter Fehler ist während des einrichten deiner Berechtigungsdaten aufgetreten
luckperms.opsystem.disabled=Das Vanilla-OP-System ist auf diesem Server deaktiviert
luckperms.opsystem.sponge-warning=Bitte beachte, dass der Server-Operator-Status keine Auswirkungen auf die Sponge-Berechtigungsprüfungen hat, wenn ein Berechtigungs-Plugin installiert ist musst du die Spielerdaten direkt bearbeiten
luckperms.duration.unit.years.plural={0} Jahre
luckperms.duration.unit.years.singular={0} Jahr
luckperms.duration.unit.years.short={0}J
luckperms.duration.unit.months.plural={0} Monate
luckperms.duration.unit.months.singular={0} Monat
luckperms.duration.unit.months.short={0}Mo
luckperms.duration.unit.weeks.plural={0} Wochen
luckperms.duration.unit.weeks.singular={0} Woche
luckperms.duration.unit.weeks.short={0}W
luckperms.duration.unit.days.plural={0} Tage
luckperms.duration.unit.days.singular={0} Tag
luckperms.duration.unit.days.short={0}T
luckperms.duration.unit.hours.plural={0} Stunden
luckperms.duration.unit.hours.singular={0} Stunde
luckperms.duration.unit.hours.short={0}Std
luckperms.duration.unit.minutes.plural={0} Minuten
luckperms.duration.unit.minutes.singular={0} Minute
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} Sekunden
luckperms.duration.unit.seconds.singular={0} Sekunde
luckperms.duration.unit.seconds.short={0}Sek
luckperms.duration.since=vor {0}
luckperms.command.misc.invalid-code=Ungültiger Code
luckperms.command.misc.response-code-key=Antwort-Code
luckperms.command.misc.error-message-key=Nachricht
luckperms.command.misc.bytebin-unable-to-communicate=Kommunikation mit bytebin nicht möglich
luckperms.command.misc.webapp-unable-to-communicate=Kommunikation mit der Webapp nicht möglich
luckperms.command.misc.check-console-for-errors=Überprüfe die Konsole auf Fehler
luckperms.command.misc.file-must-be-in-data=Die Datei {0} muss direkt im Verzeichnis ''data'' liegen
luckperms.command.misc.wait-to-finish=Bitte warte bis der Vorgang abgeschlossen ist und versuche es dann erneut
luckperms.command.misc.invalid-priority=Ungültige Priorität {0}
luckperms.command.misc.expected-number=Nummer erwartet
luckperms.command.misc.date-parse-error={0} konnte nicht als Datum interpretiert werden
luckperms.command.misc.date-in-past-error=Du kannst kein Datum in der Vergangenheit festlegen\!
luckperms.command.misc.page=Seite {0} von {1}
luckperms.command.misc.page-entries={0} Einträge
luckperms.command.misc.none=Keine
luckperms.command.misc.loading.error.unexpected=Ein unerwarteter Fehler ist aufgetreten
luckperms.command.misc.loading.error.user=Benutzer nicht geladen
luckperms.command.misc.loading.error.user-specific=Zielnutzer {0} konnte nicht geladen werden
luckperms.command.misc.loading.error.user-not-found=Benutzer {0} konnte nicht gefunden werden
luckperms.command.misc.loading.error.user-save-error=Beim Speichern der Benutzerdaten von {0} ist ein Fehler aufgetreten
luckperms.command.misc.loading.error.user-not-online={0} ist nicht online
luckperms.command.misc.loading.error.user-invalid={0} ist kein gültiger Benutzername/UUID.
luckperms.command.misc.loading.error.user-not-uuid=Zielnutzer {0} ist keine gültige UUID
luckperms.command.misc.loading.error.group=Gruppe nicht geladen
luckperms.command.misc.loading.error.all-groups=Es konnten nicht alle Gruppen geladen werden
luckperms.command.misc.loading.error.group-not-found=Eine Gruppe namens {0} konnte nicht gefunden werden
luckperms.command.misc.loading.error.group-save-error=Beim Speichern der Daten der Gruppe {0} ist ein Fehler aufgetreten
luckperms.command.misc.loading.error.group-invalid={0} ist kein gültiger Gruppenname
luckperms.command.misc.loading.error.track=Laufbahn nicht geladen
luckperms.command.misc.loading.error.all-tracks=Laufbahnen konnten nicht geladen werden
luckperms.command.misc.loading.error.track-not-found=Eine Laufbahn mit dem Namen {0} konnte nicht gefunden werden
luckperms.command.misc.loading.error.track-save-error=Beim Speichern der Daten der Laufbahn {0} ist ein Fehler aufgetreten
luckperms.command.misc.loading.error.track-invalid={0} ist kein gültiger Spuren-Name
luckperms.command.editor.no-match=Der Editor konnte nicht geöffnet werden, keine Objekte stimmten mit dem gewünschten Typ überein
luckperms.command.editor.start=Eine neue Editor-Sitzung wird vorbereitet, bitte warten...
luckperms.command.editor.url=Klicke auf den unten stehenden Link, um den Editor zu öffnen
luckperms.command.editor.unable-to-communicate=Konnte mit dem Editor nicht kommunizieren
luckperms.command.editor.apply-edits.success=Web-Editor-Änderungen wurden erfolgreich auf {0} {1} angewendet
luckperms.command.editor.apply-edits.success-summary={0} {1} und {2} {3}
luckperms.command.editor.apply-edits.success.additions=Ergänzungen
luckperms.command.editor.apply-edits.success.additions-singular=Ergänzung
luckperms.command.editor.apply-edits.success.deletions=Löschungen
luckperms.command.editor.apply-edits.success.deletions-singular=Löschung
luckperms.command.editor.apply-edits.no-changes=Es wurden keine Änderungen vom Web-Editor-übernommen, da die zurückgegebenen Daten keine Bearbeitungen enthielten
luckperms.command.editor.apply-edits.unknown-type=Die Änderung kann nicht auf den angegebenen Objekt-Typ angewendet werden
luckperms.command.editor.apply-edits.unable-to-read=Mit dem angegebenen Code können keine Daten empfangen werden
luckperms.command.search.searching.permission=Suche nach Nutzern und Gruppen mit {0}
luckperms.command.search.searching.inherit=Suche nach Benutzern und Gruppen, die von {0} erben
luckperms.command.search.result={0} Einträge von {1} Benutzern und {2} Gruppen wurden gefunden
luckperms.command.search.result.default-notice=Hinweis\: Bei der Suche von Mitgliedern der Standardgruppe werden Offline-Spieler ohne andere Berechtigungen nicht angezeigt werden\!
luckperms.command.search.showing-users=Zeige Benutzereinträge
luckperms.command.search.showing-groups=Zeige Gruppeneinträge
luckperms.command.tree.start=Erstelle Rechtebaum, bitte warten...
luckperms.command.tree.empty=Baum konnte nicht generiert werden, keine Ergebnisse gefunden
luckperms.command.tree.url=Rechtebaum-URL
luckperms.command.verbose.invalid-filter={0} ist kein gültiger Verbose-Filter
luckperms.command.verbose.enabled=Verbose-Loggen {0} für Überprüfungen, die {1} erfüllen
luckperms.command.verbose.command-exec=Zwinge {0} den Befehl {1} auszuführen und alle durchgeführten Prüfungen zu melden...
luckperms.command.verbose.off=Verbose-Loggen {0}
luckperms.command.verbose.command-exec-complete=Befehlsausführung abgeschlossen
luckperms.command.verbose.command.no-checks=Die Ausführung des Befehls wurde abgeschlossen, aber es wurden keine Berechtigungsprüfungen durchgeführt
luckperms.command.verbose.command.possibly-async=Dies könnte daran liegen, dass das Plugin Befehle im Hintergrund ausführt (async)
luckperms.command.verbose.command.try-again-manually=Du kannst noch das verbose Kommando manuell verwenden, um Prüfungen wie diese zu erkennen
luckperms.command.verbose.enabled-recording=Verbose-Aufnahme {0} für Überprüfungen, die {1} erfüllen
luckperms.command.verbose.uploading=Verbose-Logging {0}, Ergebnisse werden hochgeladen...
luckperms.command.verbose.url=Ausführliche Ergebnis-URL
luckperms.command.verbose.enabled-term=aktiviert
luckperms.command.verbose.disabled-term=deaktiviert
luckperms.command.verbose.query-any=JEDE
luckperms.command.info.running-plugin=Sie nutzen
luckperms.command.info.platform-key=Plattform
luckperms.command.info.server-brand-key=Server-Marke
luckperms.command.info.server-version-key=Server-Version
luckperms.command.info.storage-key=Speicher
luckperms.command.info.storage-type-key=Typ
luckperms.command.info.storage.meta.split-types-key=Typen
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Verbunden
luckperms.command.info.storage.meta.file-size-key=Dateigröße
luckperms.command.info.extensions-key=Erweiterungen
luckperms.command.info.messaging-key=Mitteilungen
luckperms.command.info.instance-key=Instanz
luckperms.command.info.static-contexts-key=Statische Kontexte
luckperms.command.info.online-players-key=Online-Spieler
luckperms.command.info.online-players-unique={0} einzigartig
luckperms.command.info.uptime-key=Laufzeit
luckperms.command.info.local-data-key=Lokale Daten
luckperms.command.info.local-data={0} Benutzer, {1} Gruppen, {2} Laufbahnen
luckperms.command.generic.create.success={0} wurde erfolgreich erstellt
luckperms.command.generic.create.error=Beim Erstellen von {0} ist ein Fehler aufgetreten
luckperms.command.generic.create.error-already-exists={0} existiert bereits\!
luckperms.command.generic.delete.success={0} wurde erfolgreich gelöscht
luckperms.command.generic.delete.error=Beim Löschen von {0} ist ein Fehler aufgetreten
luckperms.command.generic.delete.error-doesnt-exist={0} existiert nicht\!
luckperms.command.generic.rename.success={0} wurde erfolgreich in {1} unbenannt
luckperms.command.generic.clone.success={0} wurde erfolgreich in {1} geklont
luckperms.command.generic.info.parent.title=Übergeordnete Gruppe
luckperms.command.generic.info.parent.temporary-title=Temporäre übergeordnete Gruppen
luckperms.command.generic.info.expires-in=läuft ab in
luckperms.command.generic.info.inherited-from=geerbt von
luckperms.command.generic.info.inherited-from-self=selbst
luckperms.command.generic.show-tracks.title={0}s Laufbahnen
luckperms.command.generic.show-tracks.empty={0} ist auf keiner Laufbahn
luckperms.command.generic.clear.node-removed={0} Rechte wurden entfernt
luckperms.command.generic.clear.node-removed-singular={0} Recht wurde entfernt
luckperms.command.generic.clear={0}s Rechte wurden im Kontext {1} gelöscht
luckperms.command.generic.permission.info.title={0}s Berechtigungen
luckperms.command.generic.permission.info.empty={0} wurde noch keine Berechtigungen gesetzt
luckperms.command.generic.permission.info.click-to-remove=Klicke, um dieses Recht von {0} zu entfernen
luckperms.command.generic.permission.check.info.title=Berechtigungsinformation für {0}
luckperms.command.generic.permission.check.info.directly={0} hat {1} auf {2} im Kontext {3} gesetzt
luckperms.command.generic.permission.check.info.inherited={0} erbt {1} gesetzt auf {2} von {3} im Kontext {4}
luckperms.command.generic.permission.check.info.not-directly={0} hat nicht {1} gesetzt
luckperms.command.generic.permission.check.info.not-inherited={0} erbt nicht {1}
luckperms.command.generic.permission.check.result.title=Berechtigungsüberprüfung für {0}
luckperms.command.generic.permission.check.result.result-key=Ergebnis
luckperms.command.generic.permission.check.result.processor-key=Prozessor
luckperms.command.generic.permission.check.result.cause-key=Grund
luckperms.command.generic.permission.check.result.context-key=Kontext
luckperms.command.generic.permission.set=Setze {0} auf {1} für {2} im Kontext {3}
luckperms.command.generic.permission.already-has={0} hat bereits {1} im Kontext {2} gesetzt
luckperms.command.generic.permission.set-temp=Setze {0} auf {1} für {2} für eine Dauer von {3} im Kontext {4}
luckperms.command.generic.permission.already-has-temp={0} hat bereits {1} temporär im Kontext {2} gesetzt
luckperms.command.generic.permission.unset={0} für {1} im Kontext {2} auf "nicht gesetzt" gesetzt
luckperms.command.generic.permission.doesnt-have={0} hat {1} im Kontext {2} nicht gesetzt
luckperms.command.generic.permission.unset-temp=Temporäre Berechtigung {0} für {1} im Kontext {2} entfernt
luckperms.command.generic.permission.subtract=Setze {0} auf {1} für {2} für eine Dauer von {3} im Kontext {4}, {5} kürzer als vorher
luckperms.command.generic.permission.doesnt-have-temp={0} hat {1} nicht temporär im Kontext {2} gesetzt
luckperms.command.generic.permission.clear={0}s Rechte wurden im Kontext {1} entfernt
luckperms.command.generic.parent.info.title={0}s übergeordnete Gruppen
luckperms.command.generic.parent.info.empty={0} hat keine übergeordneten Gruppen definiert
luckperms.command.generic.parent.info.click-to-remove=Klicke, um diese übergeordnete Gruppe von {0} zu entfernen
luckperms.command.generic.parent.add={0} erbt nun Rechte von {1} im Kontext {2}
luckperms.command.generic.parent.add-temp={0} erbt nun Berechtigungen von {1} für eine Dauer von {2} im Kontext {3}
luckperms.command.generic.parent.set={0}s existierende übergeordnete Gruppen wurden entfernt, und erbt nun nur von {1} im Kontext {2}
luckperms.command.generic.parent.set-track={0}s übergeordnete Gruppen der Laufbahn {1} wurden gelöscht, und erbt nun nur noch {2} im Kontext {3}
luckperms.command.generic.parent.remove={0} erbt nun keine Berechtigungen mehr von {1} im Kontext {2}
luckperms.command.generic.parent.remove-temp={0} erbt nun keine temporären Berechtigungen mehr von {1} im Kontext {2}
luckperms.command.generic.parent.subtract={0} erbt nun Rechte von {1} für eine Dauer von {2} im Kontext {3}, {4} weniger als zuvor
luckperms.command.generic.parent.clear={0}s übergeordnete Gruppen wurden im Kontext {1} gelöscht
luckperms.command.generic.parent.clear-track={0}s übergeordnete Gruppen auf der Laufbahn {1} wurden im Kontext {2} entfernt
luckperms.command.generic.parent.already-inherits={0} erbt bereits von {1} im Kontext {2}
luckperms.command.generic.parent.doesnt-inherit={0} erbt nicht von {1} im Kontext {2}
luckperms.command.generic.parent.already-temp-inherits={0} erbt bereits vorübergehend von {1} im Kontext {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} erbt nicht temporär von {1} im Kontext {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}s Präfixe
luckperms.command.generic.chat-meta.info.title-suffix={0}s Suffixe
luckperms.command.generic.chat-meta.info.none-prefix={0} hat keine Präfixe
luckperms.command.generic.chat-meta.info.none-suffix={0} hat keine Suffixe
luckperms.command.generic.chat-meta.info.click-to-remove=Klicke, um dieses {0} von {1} zu entfernen
luckperms.command.generic.chat-meta.already-has={0} hat bereits {1} {2} mit einer Priorität von {3} im Kontext {4} gesetzt
luckperms.command.generic.chat-meta.already-has-temp={0} hat bereits {1} {2} temporär mit einer Priorität von {3} im Kontext {4} gesetzt
luckperms.command.generic.chat-meta.doesnt-have={0} hat {1} {2} nicht mit einer Priorität von {3} im Kontext {4} gesetzt
luckperms.command.generic.chat-meta.doesnt-have-temp={0} hat {1} {2} nicht temporär mit einer Priorität von {3} im Kontext {4} gesetzt
luckperms.command.generic.chat-meta.add={0}''s {1} {2} mit einer Priorität von {3} im Kontext {4} gesetzt
luckperms.command.generic.chat-meta.add-temp={0}''s {1} {2} mit einer Priorität von {3} für eine Dauer von {4} im Kontext {5} gesetzt
luckperms.command.generic.chat-meta.remove={0} hat {1} {2} mit der Priorität von {3} und dem Kontext {4} entfernt
luckperms.command.generic.chat-meta.remove-bulk={0} hat alle {1} mit der Priorität {2} und dem Kontext {3} entfernt
luckperms.command.generic.chat-meta.remove-temp={0} hat temporär {1} {2} mit der Priorität von {3} und dem Kontext {4} entfernt
luckperms.command.generic.chat-meta.remove-temp-bulk={0} hat temporär alle {1} mit der Priorität von {2} im Kontext {3} entfernt
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none={0} hat keine Metainformationen
luckperms.command.generic.meta.info.click-to-remove=Klicken, um diese Metadaten von {0} zu entfernen
luckperms.command.generic.meta.already-has={0} hat bereits den Meta-Schlüssel {1} auf {2} im Kontext {3} gesetzt
luckperms.command.generic.meta.already-has-temp={0} hat bereits den Meta-Schlüssel {1} temporär auf {2} im Kontext {3} gesetzt
luckperms.command.generic.meta.doesnt-have={0} hat nicht dem Meta-Schlüssel {1} im Kontext {2} gesetzt
luckperms.command.generic.meta.doesnt-have-temp={0} hat nicht den Meta-Schlüssel {1} temporär im Kontext {2} gesetzt
luckperms.command.generic.meta.set=Setze Meta-Schlüssel {0} auf {1} für {2} im Kontext {3}
luckperms.command.generic.meta.set-temp=Setze den Meta-Schlüssel {0} auf {1} für {2} für eine Dauer von {3} im Kontext {4}
luckperms.command.generic.meta.unset=Lösche Meta-Schlüssel {0} für {1} im Kontext {2}
luckperms.command.generic.meta.unset-temp=Lösche temporär Meta-Schlüssel {0} für {1} im Kontext {2}
luckperms.command.generic.meta.clear={0}s Meta übereinstimmender Typ {1} wurde im Kontext {2} gelöscht
luckperms.command.generic.contextual-data.title=Kontextbezogene Daten
luckperms.command.generic.contextual-data.mode.key=Modus
luckperms.command.generic.contextual-data.mode.server=Server
luckperms.command.generic.contextual-data.mode.active-player=Aktiver Spieler
luckperms.command.generic.contextual-data.contexts-key=Kontexte
luckperms.command.generic.contextual-data.prefix-key=Präfix
luckperms.command.generic.contextual-data.suffix-key=Suffix
luckperms.command.generic.contextual-data.primary-group-key=Primärgruppe
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Nichts
luckperms.command.user.info.title=Benutzerinfo
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=Typ
luckperms.command.user.info.uuid-type.mojang=Mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Du kannst keinen Benutzer aus seiner primären Gruppe entfernen
luckperms.command.user.primarygroup.not-member={0} war noch nicht Mitglied von {1}, wird jetzt hinzugefügt
luckperms.command.user.primarygroup.already-has={0} hat bereits die Gruppe {1} als Hauptgruppe
luckperms.command.user.primarygroup.warn-option=Warnung\: Die primäre Methode der Gruppenberechnung, die von diesem Server verwendet wird ({0}) könnte diese Änderung nicht widerspiegeln
luckperms.command.user.primarygroup.set=Die primäre Gruppe von {0} wurde auf {1} gesetzt
luckperms.command.user.track.error-not-contain-group={0} ist in keiner Gruppe auf {1} vorhanden
luckperms.command.user.track.unsure-which-track=Es ist nicht klar welche Laufbahn verwendet werden soll. Bitte gib eine als Argument an
luckperms.command.user.track.missing-group-advice=Erstelle entweder die Gruppe oder entferne diese von der Laufbahn und versuche es erneut
luckperms.command.user.promote.added-to-first={0} ist nicht in einer Gruppe auf der Laufbahn {1}, deshalb wurden dieser zur ersten Gruppe hinzugefügt, {2} im Kontext {3}
luckperms.command.user.promote.not-on-track={0} ist in keiner Gruppe auf der Laufbahn {1}, daher wurde dieser nicht befördert
luckperms.command.user.promote.success=Befördere {0} auf der Laufbahn {1} von {2} bis {3} im Kontext {4}
luckperms.command.user.promote.end-of-track=Das Ende der Laufbahn {0} wurde erreicht, {1} kann nicht mehr befördert werden
luckperms.command.user.promote.next-group-deleted=Die nächste Gruppe auf der Spur, {0}, existiert nicht mehr
luckperms.command.user.promote.unable-to-promote=Benutzer kann nicht befördert werden.
luckperms.command.user.demote.success={0} wird auf der Laufbahn {1} von {2} auf {3} im Kontext {4} degradiert
luckperms.command.user.demote.end-of-track=Das Ende der Laufbahn {0} wurde erreicht, {1} wurde von {2} entfernt
luckperms.command.user.demote.end-of-track-not-removed=Das Ende der Laufbahn {0} wurde erreicht, aber {1} wurde nicht aus der ersten Gruppe entfernt
luckperms.command.user.demote.previous-group-deleted=Die vorherige Gruppe auf der Laufbahn {0} existiert nicht mehr
luckperms.command.user.demote.unable-to-demote=Spieler kann nicht degradiert werden
luckperms.command.group.list.title=Gruppen
luckperms.command.group.delete.not-default=Die Standardgruppe kann nicht gelöscht werden.
luckperms.command.group.info.title=Gruppeninfo
luckperms.command.group.info.display-name-key=Anzeigename
luckperms.command.group.info.weight-key=Gewicht
luckperms.command.group.setweight.set=Setze Gewicht der Gruppe {1} auf {0}
luckperms.command.group.setdisplayname.doesnt-have={0} hat keinen Anzeigenamen festgelegt
luckperms.command.group.setdisplayname.already-has={0} hat bereits einen Anzeigenamen von {1}
luckperms.command.group.setdisplayname.already-in-use=Der Anzeigename {0} wird bereits von {1} verwendet
luckperms.command.group.setdisplayname.set=Anzeigename auf {0} für Gruppe {1} im Kontext {2} gesetzt
luckperms.command.group.setdisplayname.removed=Anzeigename für Gruppe {0} im Kontext {1} entfernt
luckperms.command.track.list.title=Laufbahnen
luckperms.command.track.path.empty=Nichts
luckperms.command.track.info.showing-track=Zeige Laufbahn
luckperms.command.track.info.path-property=Pfad
luckperms.command.track.clear={0}''s Gruppenlaufbahn wurde geleert
luckperms.command.track.append.success=Die Gruppe {0} wurde an die Laufbahn {1} angehängt
luckperms.command.track.insert.success=Gruppe {0} wurde in die Laufbahn {1} an Position {2} eingefügt
luckperms.command.track.insert.error-number=Nummer erwartet, aber stattdessen {0} erhalten
luckperms.command.track.insert.error-invalid-pos=Einfügen an Position {0} nicht möglich
luckperms.command.track.insert.error-invalid-pos-reason=Ungültige Position
luckperms.command.track.remove.success=Gruppe {0} wurde aus der Laufbahn {1} entfernt
luckperms.command.track.error-empty={0} kann nicht verwendet werden, da die Laufbahn leer ist oder nur eine Gruppe enthält
luckperms.command.track.error-multiple-groups={0} ist Mitglied mehrerer Gruppen auf dieser Laufbahn
luckperms.command.track.error-ambiguous=Position konnte nicht gefunden werden
luckperms.command.track.already-contains={0} enthält bereits {1}
luckperms.command.track.doesnt-contain={0} enthält nicht {1}
luckperms.command.log.load-error=Der Log konnte nicht geladen werden
luckperms.command.log.invalid-page=Ungültige Seitennummer
luckperms.command.log.invalid-page-range=Bitte geben Sie einen Wert zwischen {0} und {1} ein.
luckperms.command.log.empty=Keine Logeinträge vorhanden
luckperms.command.log.notify.error-console=Benachrichtigungen für Konsole können nicht umgestellt werden
luckperms.command.log.notify.enabled-term=Aktiviert
luckperms.command.log.notify.disabled-term=Deaktiviert
luckperms.command.log.notify.changed-state={0} Logging-Ausgabe
luckperms.command.log.notify.already-on=Du erhältst bereits Benachrichtigungen
luckperms.command.log.notify.already-off=Du erhältst derzeit keine Benachrichtigungen
luckperms.command.log.notify.invalid-state=Zustand unbekannt. Erwartet {0} oder {1}
luckperms.command.log.show.search=Zeige die letzten Aktionen für Suchanfrage {0}
luckperms.command.log.show.recent=Zeige die letzten Aktionen
luckperms.command.log.show.by=Zeige die letzten Aktionen sortiert nach {0}
luckperms.command.log.show.history=Zeige Verlauf für {0} {1}
luckperms.command.export.error-term=Fehler
luckperms.command.export.already-running=Ein anderer Exportvorgang läuft bereits
luckperms.command.export.file.already-exists=Die Datei {0} existiert bereits
luckperms.command.export.file.not-writable=Die Datei {0} ist nicht beschreibbar
luckperms.command.export.file.success=Erfolgreich nach {0} exportiert
luckperms.command.export.file-unexpected-error-writing=Beim Schreiben in die Datei ist ein unerwarteter Fehler aufgetreten
luckperms.command.export.web.export-code=Code exportieren
luckperms.command.export.web.import-command-description=Folgenden Befehl zum Importieren verwenden
luckperms.command.import.term=Import
luckperms.command.import.error-term=Fehler
luckperms.command.import.already-running=Ein anderer Importvorgang läuft bereits
luckperms.command.import.file.doesnt-exist=Die Datei {0} existiert nicht
luckperms.command.import.file.not-readable=Die Datei {0} ist nicht lesbar
luckperms.command.import.file.unexpected-error-reading=Beim Lesen der Import Datei ist ein unerwarteter Fehler aufgetreten
luckperms.command.import.file.correct-format=Ist es das korrekte Format?
luckperms.command.import.web.unable-to-read=Mit dem angegebenen Code können keine Daten empfangen werden
luckperms.command.import.progress.percent={0}% abgeschlossen
luckperms.command.import.progress.operations={0}/{1} Vorgänge abgeschlossen
luckperms.command.import.starting=Importvorgang wird gestartet
luckperms.command.import.completed=ABGESCHLOSSEN
luckperms.command.import.duration=Dauerte {0} Sekunden
luckperms.command.bulkupdate.must-use-console=Der Bulk Update Befehl kann nur von der Konsole verwendet werden
luckperms.command.bulkupdate.invalid-data-type=Ungültiger Datentyp, erwartete {0}
luckperms.command.bulkupdate.invalid-constraint={0} ist keine gültige Beschränkung
luckperms.command.bulkupdate.invalid-constraint-format=Beschränkungen sollten im Format {0} sein
luckperms.command.bulkupdate.invalid-comparison={0} ist kein gültiger Vergleichsoperator
luckperms.command.bulkupdate.invalid-comparison-format=Eines der folgenden Formate wurde erwartet\: {0}
luckperms.command.bulkupdate.queued=Massenaktualisierung ist nun in der Warteschlange
luckperms.command.bulkupdate.confirm=Führe {0} aus, um das Update auszuführen
luckperms.command.bulkupdate.unknown-id=Operation mit id {0} existiert nicht oder ist abgelaufen
luckperms.command.bulkupdate.starting=Massenaktualisierung läuft
luckperms.command.bulkupdate.success=Massenaktualisierung erfolgreich abgeschlossen
luckperms.command.bulkupdate.success.statistics.nodes=Insgesamt betroffene Knoten
luckperms.command.bulkupdate.success.statistics.users=Insgesamt betroffene Benutzer
luckperms.command.bulkupdate.success.statistics.groups=Insgesamt betroffene Gruppen
luckperms.command.bulkupdate.failure=Massenaktualisierung fehlgeschlagen, überprüfe die Konsole auf Fehler
luckperms.command.update-task.request=Ein Update wurde angefordert, bitte warten...
luckperms.command.update-task.complete=Update abgeschlossen
luckperms.command.update-task.push.attempting=Versuche nun andere Server zu benachrichtigen
luckperms.command.update-task.push.complete=Andere Server wurden erfolgreich über {0} benachrichtigt
luckperms.command.update-task.push.error=Fehler beim Senden der Änderungen auf andere Server
luckperms.command.update-task.push.error-not-setup=Änderungen können nicht auf andere Server übertragen werden, da kein Messaging-Dienst konfiguriert wurde
luckperms.command.reload-config.success=Die Konfigurationsdatei wurde neu geladen
luckperms.command.reload-config.restart-note=Einige Optionen werden erst nach dem Neustart des Servers angewendet
luckperms.command.translations.searching=Suche nach verfügbaren Übersetzungen, bitte warten...
luckperms.command.translations.searching-error=Konnte keine Liste der verfügbaren Übersetzungen erhalten
luckperms.command.translations.installed-translations=Installierte Übersetzungen
luckperms.command.translations.available-translations=Verfügbare Übersetzungen\:
luckperms.command.translations.percent-translated={0}% übersetzt
luckperms.command.translations.translations-by=von
luckperms.command.translations.installing=Übersetzungen werden installiert, bitte warten...
luckperms.command.translations.download-error=Übersetzung für {0} kann nicht heruntergeladen werden
luckperms.command.translations.installing-specific=Installiere Sprache {0}...
luckperms.command.translations.install-complete=Installation abgeschlossen
luckperms.command.translations.download-prompt=Benutze {0} , um die aktuelle Version von der Community bereitgestellten Übersetzungen herunterzuladen und zu installieren
luckperms.command.translations.download-override-warning=Bitte beachte, dass alle Änderungen, die Sie an diesen Sprachen vorgenommen haben überschrieben werden
luckperms.usage.user.description=Eine Reihe von Befehlen zur Verwaltung von Benutzern innerhalb von LuckPerms. (Ein ''Benutzer'' in LuckPerms ist nur ein Spieler und kann auf eine UUID oder einen Benutzernamen verweisen)
luckperms.usage.group.description=Eine Reihe von Befehlen zur Verwaltung von Gruppen innerhalb von LuckPerms. Gruppen sind nur Sammlungen von Berechtigungszuordnungen, die Benutzern zugewiesen werden können. Neue Gruppen werden mit dem Befehl "creategroup" erstellt.
luckperms.usage.track.description=Eine Reihe von Befehlen zum Verwalten von Laufbahnen in Luckperms. Laufbahnen sind geordnete Sammlungen von Gruppen auf denen Nutzer befördert und degradiert werden können.
luckperms.usage.log.description=Eine Reihe von Befehlen zur Verwaltung der Logging-Funktionalität innerhalb von LuckPerms.
luckperms.usage.sync.description=Lädt alle Daten aus dem Pluginordner in den RAM und wendet alle Änderungen an.
luckperms.usage.info.description=Gibt allgemeine Informationen über die aktive Instanz aus.
luckperms.usage.editor.description=Erstellt eine neue Web-Editor-Sitzung
luckperms.usage.editor.argument.type=die Typen, die in den Editor geladen werden sollen. (''all'', ''users'' oder ''groups'')
luckperms.usage.editor.argument.filter=die Berechtigung um Nutzereinträge zu filtern
luckperms.usage.verbose.description=Steuert das ausführliche Berechtigungsprüfung Überwachungssystem des Plugins.
luckperms.usage.verbose.argument.action=ob die Protokollierung aktiviert oder deaktiviert werden soll oder um die geloggte Ausgabe hochzuladen
luckperms.usage.verbose.argument.filter=der Filter der Einträge entspricht
luckperms.usage.verbose.argument.commandas=der zu ausführende Spieler/Befehl
luckperms.usage.tree.description=Erzeugt eine Baumansicht (sortierte Listenhierarchie) aller Rechte, die LuckPerms bekannt sind.
luckperms.usage.tree.argument.scope=die Wurzel des Baumes. Geben Sie "." an, um alle Berechtigungen einzubinden
luckperms.usage.tree.argument.player=der Name eines Online-Spielers den Sie überprüfen möchten
luckperms.usage.search.description=Sucht nach allen Benutzern/Gruppen mit einer bestimmten Berechtigung
luckperms.usage.search.argument.permission=die Berechtigung zu überprüfen auf
luckperms.usage.search.argument.page=die anzuzeigende Seite
luckperms.usage.network-sync.description=Änderungen mit dem Speicher synchronisieren und anfordern, dass alle anderen Server im Netzwerk dasselbe tun
luckperms.usage.import.description=Importiert Daten aus einer (zuvor erstellten) Exportdatei
luckperms.usage.import.argument.file=die zu importierende Datei
luckperms.usage.import.argument.replace=existierende Daten ersetzen statt Zusammenzuführen
luckperms.usage.import.argument.upload=lade die Daten eines vorherigen Exports hoch
luckperms.usage.export.description=Exportiert alle Berechtigungsdaten in eine ''export''-Datei. Kann zu einem späteren Zeitpunkt erneut importiert werden.
luckperms.usage.export.argument.file=die zu exportierende Datei
luckperms.usage.export.argument.without-users=schließe Benutzer vom Export aus
luckperms.usage.export.argument.without-groups=schließe Gruppen vom Export aus
luckperms.usage.export.argument.upload=Lade all Berechtigungsdaten in den Webeditor hoch. Kann zu einem späteren Zeitpunkt erneut importiert werden.
luckperms.usage.reload-config.description=Einige der Konfigurationsoptionen neu laden
luckperms.usage.bulk-update.description=Massen-Änderungsabfragen auf allen Daten ausführen
luckperms.usage.bulk-update.argument.data-type=der Typ der zu ändernden Daten. (''all'', ''users'' oder ''groups'')
luckperms.usage.bulk-update.argument.action=die Aktion, die auf den Daten ausgeführt werden soll. (''update'' oder ''delete'')
luckperms.usage.bulk-update.argument.action-field=das zu bearbeitende Feld. Nur für ''update'' erforderlich. (''permission'', ''server'' oder ''world'')
luckperms.usage.bulk-update.argument.action-value=der zu ersetzende Wert. Nur für ''update'' erforderlich.
luckperms.usage.bulk-update.argument.constraint=die für das Update erforderlichen Einschränkungen
luckperms.usage.translations.description=Übersetzungen verwalten
luckperms.usage.translations.argument.install=Unterbefehl zum Installieren von Übersetzungen
luckperms.usage.apply-edits.description=Änderungen an den Berechtigungen des Web-Editors anwenden
luckperms.usage.apply-edits.argument.code=der eindeutige Code für die Daten
luckperms.usage.apply-edits.argument.target=auf wen die Daten angewendet werden sollen
luckperms.usage.create-group.description=Erstelle eine neue Gruppe
luckperms.usage.create-group.argument.name=der Name der Gruppe
luckperms.usage.create-group.argument.weight=das Gewicht der Gruppe
luckperms.usage.create-group.argument.display-name=der Anzeigename der Gruppe
luckperms.usage.delete-group.description=Eine Gruppe löschen
luckperms.usage.delete-group.argument.name=der Name der Gruppe
luckperms.usage.list-groups.description=Alle Gruppen der Plattform auflisten
luckperms.usage.create-track.description=Neue Spur hinzufügen
luckperms.usage.create-track.argument.name=der Name der Laufbahn
luckperms.usage.delete-track.description=Eine Laufbahn löschen
luckperms.usage.delete-track.argument.name=der Name der Laufbahn
luckperms.usage.list-tracks.description=Alle Laufbahnen dieser Plattform auflisten
luckperms.usage.user-info.description=Zeigt Informationen zu einem Spieler
luckperms.usage.user-switchprimarygroup.description=Ändert die primäre Gruppe des Spielers
luckperms.usage.user-switchprimarygroup.argument.group=die Gruppe, zu der gewechselt werden soll
luckperms.usage.user-promote.description=Befördert den Nutzer in einer Spur
luckperms.usage.user-promote.argument.track=die Spur, um den Nutzer zu befördern
luckperms.usage.user-promote.argument.context=die Kontexte, in denen der Nutzer gefördert werden soll
luckperms.usage.user-promote.argument.dont-add-to-first=den Nutzer nur befördern, wenn er schon auf der Spur ist
luckperms.usage.user-demote.description=Degradiert den Nutzer in einer Spur
luckperms.usage.user-demote.argument.track=die Spur, wo der Benutzer degradiert werden soll
luckperms.usage.user-demote.argument.context=die Kontexte, in denen der Nutzer degradiert werden soll
luckperms.usage.user-demote.argument.dont-remove-from-first=verhindern, dass der Benutzer von der ersten Gruppe entfernt wird
luckperms.usage.user-clone.description=Klont einen Benutzer
luckperms.usage.user-clone.argument.user=der Name/uuid des zu klonenden Benutzers
luckperms.usage.group-info.description=Zeigt Informationen über die Gruppe an
luckperms.usage.group-listmembers.description=Zeigt Benutzer/Gruppen, die von dieser Gruppe erben
luckperms.usage.group-listmembers.argument.page=die anzuzeigende Seite
luckperms.usage.group-setweight.description=Leg die Gewichtung der Gruppen fest
luckperms.usage.group-setweight.argument.weight=das zu setzende Gewicht
luckperms.usage.group-set-display-name.description=Anzeigename der Gruppe festlegen
luckperms.usage.group-set-display-name.argument.name=der zu setzende Name
luckperms.usage.group-set-display-name.argument.context=die Kontexte, in denen der Name gesetzt werden soll
luckperms.usage.group-rename.description=Gruppe umbenennen
luckperms.usage.group-rename.argument.name=der neue Name
luckperms.usage.group-clone.description=Die Gruppe klonen
luckperms.usage.group-clone.argument.name=der Name der zu klonenden Gruppe
luckperms.usage.holder-editor.description=Öffnet den Web-Berechtigungs-Editor
luckperms.usage.holder-showtracks.description=Listet die Spuren auf, auf denen das Objekt ist
luckperms.usage.holder-clear.description=Entfernt alle Berechtigungen, Eltern und Meta
luckperms.usage.holder-clear.argument.context=die Kontexte nach denen gefiltert werden soll
luckperms.usage.permission.description=Berechtigungen bearbeiten
luckperms.usage.parent.description=Vererbung bearbeiten
luckperms.usage.meta.description=Metadatenwerte bearbeiten
luckperms.usage.permission-info.description=Listet die Berechtigungsknoten auf, die das Objekt hat
luckperms.usage.permission-info.argument.page=die anzuzeigende Seite
luckperms.usage.permission-info.argument.sort-mode=wie die Einträge sortiert werden
luckperms.usage.permission-set.description=Legt eine Berechtigung für das Objekt fest
luckperms.usage.permission-set.argument.node=der Berechtigungsknoten der gesetzt werden soll
luckperms.usage.permission-set.argument.value=der Wert des Knotens
luckperms.usage.permission-set.argument.context=die Kontexte, in denen die Berechtigung hinzugefügt werden soll
luckperms.usage.permission-unset.description=Löscht die Berechtigung für das Objekt
luckperms.usage.permission-unset.argument.node=der Berechtigungsknoten der entfernt werden soll
luckperms.usage.permission-unset.argument.context=die Kontexte, in denen die Berechtigung gelöscht werden soll
luckperms.usage.permission-settemp.description=Legt eine Berechtigung für das Objekt temporär fest
luckperms.usage.permission-settemp.argument.node=der Berechtigungsknoten der gesetzt werden soll
luckperms.usage.permission-settemp.argument.value=der Wert des Knotens
luckperms.usage.permission-settemp.argument.duration=die Dauer bis der Berechtigungsknoten abläuft
luckperms.usage.permission-settemp.argument.temporary-modifier=wie die temporäre Berechtigung angewendet werden soll
luckperms.usage.permission-settemp.argument.context=die Kontexte, in denen die Berechtigung hinzugefügt werden soll
luckperms.usage.permission-unsettemp.description=Löscht die Berechtigung für das Objekt
luckperms.usage.permission-unsettemp.argument.node=der Berechtigungsknoten der entfernt werden soll
luckperms.usage.permission-unsettemp.argument.duration=die Dauer die abgezogen werden soll
luckperms.usage.permission-unsettemp.argument.context=die Kontexte, in denen die Berechtigung gelöscht werden soll
luckperms.usage.permission-check.description=Prüft, ob das Objekt einen bestimmten Berechtigungsknoten hat
luckperms.usage.permission-check.argument.node=der Berechtigungsknoten der überprüft werden soll
luckperms.usage.permission-clear.description=Löscht alle Berechtigungen
luckperms.usage.permission-clear.argument.context=die Kontexte nach denen gefiltert werden soll
luckperms.usage.parent-info.description=Listet die Gruppen auf, von denen dieses Objekt erbt
luckperms.usage.parent-info.argument.page=die anzuzeigende Seite
luckperms.usage.parent-info.argument.sort-mode=wie die Einträge sortiert werden
luckperms.usage.parent-set.description=Entfernt alle anderen Gruppen, von denen das Objekt bereits auf der angegebenen Spur erbt, und fügt sie der angegebenen hinzu
luckperms.usage.parent-set.argument.group=die Gruppe, auf die eingestellt werden soll
luckperms.usage.parent-set.argument.context=die Kontexte, in denen die Gruppe eingerichtet werden soll
luckperms.usage.parent-add.description=Legt eine andere Gruppe für das Objekt fest, von der Berechtigungen geerbt werden sollen
luckperms.usage.parent-add.argument.group=die Gruppe, von der geerbt werden soll
luckperms.usage.parent-add.argument.context=die Kontexte, in denen die Gruppe geerbt werden soll
luckperms.usage.parent-remove.description=Entfernt eine zuvor festgelegte Vererbungsregel
luckperms.usage.parent-remove.argument.group=die zu entfernende Gruppe
luckperms.usage.parent-remove.argument.context=die Kontexte, in denen die Gruppe entfernt werden soll
luckperms.usage.parent-set-track.description=Entfernt alle anderen Gruppen, von denen das Objekt bereits auf der angegebenen Spur erbt, und fügt sie der angegebenen hinzu
luckperms.usage.parent-set-track.argument.track=die Spur, auf die gesetzt werden soll
luckperms.usage.parent-set-track.argument.group=die Gruppe, auf die eingestellt werden soll, oder eine Nummer, die sich auf die Position der Gruppe auf der angegebenen Spur bezieht
luckperms.usage.parent-set-track.argument.context=die Kontexte, in denen die Gruppe eingerichtet werden soll
luckperms.usage.parent-add-temp.description=Legt eine andere Gruppe für das Objekt fest, von der Berechtigungen vorübergehend geerbt werden sollen
luckperms.usage.parent-add-temp.argument.group=die Gruppe, von der geerbt werden soll
luckperms.usage.parent-add-temp.argument.duration=die Dauer der Gruppenmitgliedschaft
luckperms.usage.parent-add-temp.argument.temporary-modifier=wie die temporäre Berechtigung angewendet werden soll
luckperms.usage.parent-add-temp.argument.context=die Kontexte, in denen die Gruppe geerbt werden soll
luckperms.usage.parent-remove-temp.description=Entfernt eine zuvor festgelegte temporäre Vererbungsregel
luckperms.usage.parent-remove-temp.argument.group=die zu entfernende Gruppe
luckperms.usage.parent-remove-temp.argument.duration=die zu subtrahierende Dauer
luckperms.usage.parent-remove-temp.argument.context=die Kontexte, in denen die Gruppe entfernt werden soll
luckperms.usage.parent-clear.description=Löscht alle Eltern
luckperms.usage.parent-clear.argument.context=die Kontexte nach denen gefiltert werden soll
luckperms.usage.parent-clear-track.description=Löscht alle Eltern auf einer bestimmten Spur
luckperms.usage.parent-clear-track.argument.track=die Spur auf der entfernt werden soll
luckperms.usage.parent-clear-track.argument.context=die Kontexte nach denen gefiltert werden soll
luckperms.usage.meta-info.description=Zeigt alle Chat-Meta an
luckperms.usage.meta-set.description=Setzt einen Meta-Wert
luckperms.usage.meta-set.argument.key=der zu setzende Schlüssel
luckperms.usage.meta-set.argument.value=der zu setzende Wert
luckperms.usage.meta-set.argument.context=die Kontexte, in denen die Berechtigung hinzugefügt werden soll
luckperms.usage.meta-unset.description=Entfernt einen Meta-Wert
luckperms.usage.meta-unset.argument.key=der zu entfernende Schlüssel
luckperms.usage.meta-unset.argument.context=die Kontexte, in denen die Berechtigung entfernt werden soll
luckperms.usage.meta-settemp.description=Setzt vorübergehend einen Meta-Wert
luckperms.usage.meta-settemp.argument.key=der zu setzende Schlüssel
luckperms.usage.meta-settemp.argument.value=der zu setzende Name
luckperms.usage.meta-settemp.argument.duration=die Dauer bis der Meta-Wert abläuft
luckperms.usage.meta-settemp.argument.context=die Kontexte, in denen das Meta-Paar hinzugefügt werden soll
luckperms.usage.meta-unsettemp.description=Entfernt einen temporären Meta-Wert
luckperms.usage.meta-unsettemp.argument.key=der zu entfernende Schlüssel
luckperms.usage.meta-unsettemp.argument.context=die Kontexte, in denen die Berechtigung entfernt werden soll
luckperms.usage.meta-addprefix.description=Fügt ein Präfix hinzu
luckperms.usage.meta-addprefix.argument.priority=die Priorität mit, der der Präfix gesetzt werden soll
luckperms.usage.meta-addprefix.argument.prefix=der Präfix String
luckperms.usage.meta-addprefix.argument.context=die Kontexte, in denen der Präfix hinzugefügt werden soll
luckperms.usage.meta-addsuffix.description=Setzt ein Suffix
luckperms.usage.meta-addsuffix.argument.priority=die Priorität mit, der der Suffix hinzugefügt werden soll
luckperms.usage.meta-addsuffix.argument.suffix=der Suffix String
luckperms.usage.meta-addsuffix.argument.context=die Kontexte, in denen der Suffix hinzugefügt werden soll
luckperms.usage.meta-setprefix.description=Setzt ein Präfix
luckperms.usage.meta-setprefix.argument.priority=die Priorität mit, der der Präfix gesetzt werden soll
luckperms.usage.meta-setprefix.argument.prefix=der Präfix String
luckperms.usage.meta-setprefix.argument.context=die Kontexte, in denen der Präfix gesetzt wird
luckperms.usage.meta-setsuffix.description=Setzt ein Suffix
luckperms.usage.meta-setsuffix.argument.priority=die Priorität mit, der der Suffix gesetzt werden soll
luckperms.usage.meta-setsuffix.argument.suffix=der Suffix String
luckperms.usage.meta-setsuffix.argument.context=die Kontexte, in denen der Suffix gesetzt werden soll
luckperms.usage.meta-removeprefix.description=Entfernt einen Präfix
luckperms.usage.meta-removeprefix.argument.priority=die Priorität mit, der der Präfix entfernt werden soll
luckperms.usage.meta-removeprefix.argument.prefix=der Präfix String
luckperms.usage.meta-removeprefix.argument.context=die Kontexte, in denen der Präfix entfernt werden soll
luckperms.usage.meta-removesuffix.description=Entfernt einen Suffix
luckperms.usage.meta-removesuffix.argument.priority=die Priorität mit, der der Suffix entfernt werden soll
luckperms.usage.meta-removesuffix.argument.suffix=der Suffix String
luckperms.usage.meta-removesuffix.argument.context=die Kontexte, in denen der Suffix entfernt werden soll
luckperms.usage.meta-addtemp-prefix.description=Fügt ein Präfix temporär hinzu
luckperms.usage.meta-addtemp-prefix.argument.priority=die Priorität mit, der der Präfix gesetzt werden soll
luckperms.usage.meta-addtemp-prefix.argument.prefix=der Präfix String
luckperms.usage.meta-addtemp-prefix.argument.duration=die Dauer bis der Präfix abläuft
luckperms.usage.meta-addtemp-prefix.argument.context=die Kontexte, in denen der Präfix hinzugefügt werden soll
luckperms.usage.meta-addtemp-suffix.description=Fügt vorübergehend ein Suffix hinzu
luckperms.usage.meta-addtemp-suffix.argument.priority=die Priorität mit, der der Suffix hinzugefügt werden soll
luckperms.usage.meta-addtemp-suffix.argument.suffix=der Suffix String
luckperms.usage.meta-addtemp-suffix.argument.duration=die Dauer bis der Suffix abläuft
luckperms.usage.meta-addtemp-suffix.argument.context=die Kontexte, in denen der Suffix hinzugefügt werden soll
luckperms.usage.meta-settemp-prefix.description=Setzt ein Präfix temporär
luckperms.usage.meta-settemp-prefix.argument.priority=die Priorität mit, der der Präfix gesetzt werden soll
luckperms.usage.meta-settemp-prefix.argument.prefix=der Prefix String
luckperms.usage.meta-settemp-prefix.argument.duration=die Dauer bis der Präfix abläuft
luckperms.usage.meta-settemp-prefix.argument.context=die Kontexte, in denen der Präfix gesetzt werden soll
luckperms.usage.meta-settemp-suffix.description=Fügt ein Suffix temporär hinzu
luckperms.usage.meta-settemp-suffix.argument.priority=die Priorität mit, der der Suffix gesetzt werden soll
luckperms.usage.meta-settemp-suffix.argument.suffix=der Suffix String
luckperms.usage.meta-settemp-suffix.argument.duration=die Dauer bis der Suffix abläuft
luckperms.usage.meta-settemp-suffix.argument.context=die Kontexte, in denen der Suffix gesetzt werden soll
luckperms.usage.meta-removetemp-prefix.description=Entfernt einen temporären Präfix
luckperms.usage.meta-removetemp-prefix.argument.priority=die Priorität mit, der der Präfix entfernt werden soll
luckperms.usage.meta-removetemp-prefix.argument.prefix=der Präfix String
luckperms.usage.meta-removetemp-prefix.argument.context=die Kontexte, in denen der Präfix entfernt werden soll
luckperms.usage.meta-removetemp-suffix.description=Entfernt einen temporären Suffix
luckperms.usage.meta-removetemp-suffix.argument.priority=die Priorität mit, der der Suffix entfernt werden soll
luckperms.usage.meta-removetemp-suffix.argument.suffix=der Suffix String
luckperms.usage.meta-removetemp-suffix.argument.context=die Kontexte, in denen der Suffix entfernt werden soll
luckperms.usage.meta-clear.description=Löscht alle Meta
luckperms.usage.meta-clear.argument.type=der Typ der zu entfernenden Meta
luckperms.usage.meta-clear.argument.context=die Kontexte nach denen gefiltert werden soll
luckperms.usage.track-info.description=Zeigt Informationen über die Spur an
luckperms.usage.track-editor.description=Öffnet den Web-Berechtigungs-Editor
luckperms.usage.track-append.description=Hängt eine Gruppe am Ende der Spur an
luckperms.usage.track-append.argument.group=die anzuhängende Gruppe
luckperms.usage.track-insert.description=Fügt eine Gruppe an einer bestimmten Stelle entlang der Spur ein
luckperms.usage.track-insert.argument.group=die einzusetzende Gruppe
luckperms.usage.track-insert.argument.position=die Position, an der die Gruppe eingefügt wird (die erste Position auf der Spur ist 1)
luckperms.usage.track-remove.description=Entfernt eine Gruppe von der Spur
luckperms.usage.track-remove.argument.group=die zu entfernende Gruppe
luckperms.usage.track-clear.description=Löscht die Gruppen auf der Spur
luckperms.usage.track-rename.description=Die Spur umbenennen
luckperms.usage.track-rename.argument.name=der neue Name
luckperms.usage.track-clone.description=Die Spur klonen
luckperms.usage.track-clone.argument.name=der Name der zu klonenden Spur
luckperms.usage.log-recent.description=Kürzliche Aktionen anzeigen
luckperms.usage.log-recent.argument.user=der Name/uuid des zu filternden Benutzers
luckperms.usage.log-recent.argument.page=die anzuzeigende Seitennummer
luckperms.usage.log-search.description=Im Log nach einem Eintrag suchen
luckperms.usage.log-search.argument.query=die Abfrage, nach der gesucht werden soll
luckperms.usage.log-search.argument.page=die anzuzeigende Seitennummer
luckperms.usage.log-notify.description=Log Benachrichtigungen umschalten
luckperms.usage.log-notify.argument.toggle=ob ein- oder ausschalten
luckperms.usage.log-user-history.description=Verlauf eines Nutzers anzeigen
luckperms.usage.log-user-history.argument.user=der Name/uuid des Benutzers
luckperms.usage.log-user-history.argument.page=die anzuzeigende Seitennummer
luckperms.usage.log-group-history.description=Verlauf einer Gruppe anzeigen
luckperms.usage.log-group-history.argument.group=der Name der Gruppe
luckperms.usage.log-group-history.argument.page=die anzuzeigende Seitennummer
luckperms.usage.log-track-history.description=Verlauf einer Spur anzeigen
luckperms.usage.log-track-history.argument.track=der Name der Spur
luckperms.usage.log-track-history.argument.page=die anzuzeigende Seitennummer
luckperms.usage.sponge.description=Zusätzliche Sponge Daten bearbeiten
luckperms.usage.sponge.argument.collection=die zu durchsuchende Sammlung
luckperms.usage.sponge.argument.subject=die zu ändernde Person
luckperms.usage.sponge-permission-info.description=Zeigt Informationen über die Berechtigungen der Person an
luckperms.usage.sponge-permission-info.argument.contexts=die Kontexte nach denen gefiltert werden soll
luckperms.usage.sponge-permission-set.description=Legt eine Berechtigung für die Person fest
luckperms.usage.sponge-permission-set.argument.node=der Berechtigungsknoten der gesetzt werden soll
luckperms.usage.sponge-permission-set.argument.tristate=der Wert auf den die Berechtigung gesetzt werden soll
luckperms.usage.sponge-permission-set.argument.contexts=die Kontexte, in denen die Berechtigung gesetzt werden soll
luckperms.usage.sponge-permission-clear.description=Löscht die Berechtigungen der Person
luckperms.usage.sponge-permission-clear.argument.contexts=die Kontexte, in denen Berechtigungen gelöscht werden sollen
luckperms.usage.sponge-parent-info.description=Zeigt Informationen über die Eltern der Person an
luckperms.usage.sponge-parent-info.argument.contexts=die Kontexte nach denen gefiltert werden soll
luckperms.usage.sponge-parent-add.description=Fügt einen Elternteil zur Person hinzu
luckperms.usage.sponge-parent-add.argument.collection=die Personen-Sammlung, wo die Gruppe ist
luckperms.usage.sponge-parent-add.argument.subject=der Name der Eltern Person
luckperms.usage.sponge-parent-add.argument.contexts=die Kontexte, in denen der Elternteil hinzugefügt werden soll
luckperms.usage.sponge-parent-remove.description=Entfernt ein Elternteil von der Person
luckperms.usage.sponge-parent-remove.argument.collection=die Personen-Sammlung, wo die Gruppe ist
luckperms.usage.sponge-parent-remove.argument.subject=der Name der Eltern Person
luckperms.usage.sponge-parent-remove.argument.contexts=die Kontexte, in denen der Elternteil entfernt werden soll
luckperms.usage.sponge-parent-clear.description=Löscht die Eltern der Person
luckperms.usage.sponge-parent-clear.argument.contexts=die Kontexte, in denen die Eltern gelöscht werden sollen
luckperms.usage.sponge-option-info.description=Zeigt Informationen über die Optionen der Person an
luckperms.usage.sponge-option-info.argument.contexts=die Kontexte nach denen gefiltert werden soll
luckperms.usage.sponge-option-set.description=Setzt eine Option für die Person
luckperms.usage.sponge-option-set.argument.key=der zu setzende Schlüssel
luckperms.usage.sponge-option-set.argument.value=der Wert auf den der Schlüssel gesetzt werden soll
luckperms.usage.sponge-option-set.argument.contexts=die Kontexte, in denen die Option gesetzt werden soll
luckperms.usage.sponge-option-unset.description=Entfernt eine Option für die Person
luckperms.usage.sponge-option-unset.argument.key=der zu entfernende Schlüssel
luckperms.usage.sponge-option-unset.argument.contexts=die Kontexte, in denen der Schlüssel entfernt werden soll
luckperms.usage.sponge-option-clear.description=Löscht die Optionen der Person
luckperms.usage.sponge-option-clear.argument.contexts=die Kontexte, in denen Optionen gelöscht werden sollen
