luckperms.logs.actionlog-prefix=記錄
luckperms.logs.verbose-prefix=詳細資訊
luckperms.logs.export-prefix=匯出
luckperms.commandsystem.available-commands=使用 {0} 來查看可用的指令
luckperms.commandsystem.command-not-recognised=未知的指令
luckperms.commandsystem.no-permission=你沒有使用該指令的權限！
luckperms.commandsystem.no-permission-subcommands=你沒有使用子指令的權限！
luckperms.commandsystem.already-executing-command=目前有另一個指令正在執行，正在等待該指令執行完成……
luckperms.commandsystem.usage.sub-commands-header=子指令
luckperms.commandsystem.usage.usage-header=指令用法
luckperms.commandsystem.usage.arguments-header=引數
luckperms.first-time.no-permissions-setup=你似乎還沒設定任何權限！
luckperms.first-time.use-console-to-give-access=你在遊戲裡使用任何 LuckPerms 指令前，需要使用控制台來給予你存取 LuckPerms 的權限
luckperms.first-time.console-command-prompt=開啟控制台並執行以下指令
luckperms.first-time.next-step=當你完成操作後，便可以定義你的權限分配和群組
luckperms.first-time.wiki-prompt=不知道從哪裡開始？到這裡看看：{0}
luckperms.login.try-again=請稍後再試
luckperms.login.loading-database-error=載入權限資料時發生資料庫錯誤
luckperms.login.server-admin-check-console-errors=如果你是伺服器管理員，請檢查控制台是否有任何錯誤
luckperms.login.server-admin-check-console-info=更多資訊請查看伺服器主控台
luckperms.login.data-not-loaded-at-pre=在登入前階段時未能載入使用者的權限資料
luckperms.login.unable-to-continue=無法繼續
luckperms.login.craftbukkit-offline-mode-error=這可能是因為 CraftBukkit 和線上模式設定之間有所衝突
luckperms.login.unexpected-error=設定權限資料時發生未預期的錯誤
luckperms.opsystem.disabled=這個伺服器已停用原版 OP 系統。
luckperms.opsystem.sponge-warning=請注意，安裝權限插件後，伺服器管理員身分不會影響 Sponge 的權限檢查，你必須直接編輯使用者資料
luckperms.duration.unit.years.plural={0} 年
luckperms.duration.unit.years.singular={0} 年
luckperms.duration.unit.years.short={0} 年
luckperms.duration.unit.months.plural={0} 個月
luckperms.duration.unit.months.singular={0} 個月
luckperms.duration.unit.months.short={0} 個月
luckperms.duration.unit.weeks.plural={0} 週
luckperms.duration.unit.weeks.singular={0} 週
luckperms.duration.unit.weeks.short={0} 週
luckperms.duration.unit.days.plural={0} 天
luckperms.duration.unit.days.singular={0} 天
luckperms.duration.unit.days.short={0} 天
luckperms.duration.unit.hours.plural={0} 小時
luckperms.duration.unit.hours.singular={0} 小時
luckperms.duration.unit.hours.short={0} 小時
luckperms.duration.unit.minutes.plural={0} 分鐘
luckperms.duration.unit.minutes.singular={0} 分鐘
luckperms.duration.unit.minutes.short={0} 分
luckperms.duration.unit.seconds.plural={0} 秒
luckperms.duration.unit.seconds.singular={0} 秒
luckperms.duration.unit.seconds.short={0} 秒
luckperms.duration.since={0} 前
luckperms.command.misc.invalid-code=驗證碼無效
luckperms.command.misc.response-code-key=驗證碼
luckperms.command.misc.error-message-key=訊息
luckperms.command.misc.bytebin-unable-to-communicate=無法連線至 bytebin
luckperms.command.misc.webapp-unable-to-communicate=無法連線至網路應用程式
luckperms.command.misc.check-console-for-errors=請到控制台檢查錯誤
luckperms.command.misc.file-must-be-in-data=檔案 {0} 必須直接放在 data 目錄中
luckperms.command.misc.wait-to-finish=請等待其完成後再試一次
luckperms.command.misc.invalid-priority=無效的優先權 {0}
luckperms.command.misc.expected-number=應為數字
luckperms.command.misc.date-parse-error=無法分析日期 {0}
luckperms.command.misc.date-in-past-error=你無法設定過去的日期！
luckperms.command.misc.page=第 {0} 頁，共 {1} 頁
luckperms.command.misc.page-entries={0} 個項目
luckperms.command.misc.none=無
luckperms.command.misc.loading.error.unexpected=發生未預期的錯誤
luckperms.command.misc.loading.error.user=尚未載入使用者
luckperms.command.misc.loading.error.user-specific=無法載入目標使用者 {0}
luckperms.command.misc.loading.error.user-not-found=找不到名為 {0} 的使用者
luckperms.command.misc.loading.error.user-save-error=儲存使用者 {0} 的資料時發生錯誤
luckperms.command.misc.loading.error.user-not-online=玩家 {0} 不在線上
luckperms.command.misc.loading.error.user-invalid={0} 無效的使用者名稱或 UUID
luckperms.command.misc.loading.error.user-not-uuid=目標使用者 {0} 不是有效的 UUID
luckperms.command.misc.loading.error.group=尚未載入群組
luckperms.command.misc.loading.error.all-groups=無法載入所有群組
luckperms.command.misc.loading.error.group-not-found=找不到名為 {0} 的群組
luckperms.command.misc.loading.error.group-save-error=儲存群組 {0} 的資料時發生錯誤
luckperms.command.misc.loading.error.group-invalid={0} 不是有效的群組名稱
luckperms.command.misc.loading.error.track=尚未載入權限階級
luckperms.command.misc.loading.error.all-tracks=無法載入所有權限階級
luckperms.command.misc.loading.error.track-not-found=找不到名為 {0} 的權限階級
luckperms.command.misc.loading.error.track-save-error=儲存權限階級 {0} 的資料時發生錯誤
luckperms.command.misc.loading.error.track-invalid={0} 不是有效的權限階級名稱
luckperms.command.editor.no-match=無法開啟編輯器，沒有與所需類型符合的物件
luckperms.command.editor.start=正在準備一個新的編輯器，請稍候……
luckperms.command.editor.url=點擊下方連結開啟編輯器
luckperms.command.editor.unable-to-communicate=無法連線至編輯器
luckperms.command.editor.apply-edits.success=網頁編輯器資料已成功套用到 {0} {1}
luckperms.command.editor.apply-edits.success-summary={1} {0} 項和{3} {2} 項
luckperms.command.editor.apply-edits.success.additions=增加
luckperms.command.editor.apply-edits.success.additions-singular=增加
luckperms.command.editor.apply-edits.success.deletions=刪除
luckperms.command.editor.apply-edits.success.deletions-singular=刪除
luckperms.command.editor.apply-edits.no-changes=網頁編輯器沒有套用任何變更，返回的資料不包含任何編輯
luckperms.command.editor.apply-edits.unknown-type=無法對指定的物件類型套用編輯
luckperms.command.editor.apply-edits.unable-to-read=無法從指定的代碼讀取資料
luckperms.command.search.searching.permission=正在搜尋擁有 {0} 權限的使用者和群組
luckperms.command.search.searching.inherit=正在搜尋繼承自 {0} 的使用者和群組
luckperms.command.search.result=從 {1} 個使用者和 {2} 個群組中找到 {0} 個項目
luckperms.command.search.result.default-notice=注意：搜尋預設群組成員時，將不會顯示沒有其他權限的離線玩家！
luckperms.command.search.showing-users=顯示使用者項目
luckperms.command.search.showing-groups=顯示群組項目
luckperms.command.tree.start=正在產生權限樹，請稍候……
luckperms.command.tree.empty=找不到任何結果，因此無法產生權限樹
luckperms.command.tree.url=權限樹網址
luckperms.command.verbose.invalid-filter={0} 不是有效的詳細記錄篩選器
luckperms.command.verbose.enabled=針對符合 {1} 篩選器的檢查，開啟 {0} 詳細記錄功能
luckperms.command.verbose.command-exec=強制 {0} 執行指令 {1} 並回報所有檢查結果……
luckperms.command.verbose.off=詳細記錄 {0}
luckperms.command.verbose.command-exec-complete=指令執行完成
luckperms.command.verbose.command.no-checks=指令執行完成，但沒有進行權限檢查
luckperms.command.verbose.command.possibly-async=這可能是因為插件在背景執行指令（非同步）
luckperms.command.verbose.command.try-again-manually=你仍然可以手動開啟詳細資訊模式來偵測這類檢查
luckperms.command.verbose.enabled-recording=針對符合 {1} 的檢查結果，開啟 {0} 詳細記錄功能
luckperms.command.verbose.uploading=正在上傳 {0} 的詳細記錄結果……
luckperms.command.verbose.url=詳細結果網址
luckperms.command.verbose.enabled-term=已啟用
luckperms.command.verbose.disabled-term=已停用
luckperms.command.verbose.query-any=任意
luckperms.command.info.running-plugin=執行中
luckperms.command.info.platform-key=平台
luckperms.command.info.server-brand-key=伺服器軟體
luckperms.command.info.server-version-key=伺服器版本
luckperms.command.info.storage-key=儲存
luckperms.command.info.storage-type-key=類型
luckperms.command.info.storage.meta.split-types-key=類型
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=已連線
luckperms.command.info.storage.meta.file-size-key=檔案大小
luckperms.command.info.extensions-key=附加元件
luckperms.command.info.messaging-key=訊息
luckperms.command.info.instance-key=伺服器實例
luckperms.command.info.static-contexts-key=靜態環境
luckperms.command.info.online-players-key=線上玩家
luckperms.command.info.online-players-unique={0} 獨立 IP 玩家
luckperms.command.info.uptime-key=執行時間
luckperms.command.info.local-data-key=本機資料
luckperms.command.info.local-data={0} 個使用者、{1} 個群組、{2} 個權限階級
luckperms.command.generic.create.success=已成功建立 {0}
luckperms.command.generic.create.error=建立 {0} 時發生錯誤
luckperms.command.generic.create.error-already-exists={0} 已存在！
luckperms.command.generic.delete.success=已成功刪除 {0}
luckperms.command.generic.delete.error=刪除 {0} 時發生錯誤
luckperms.command.generic.delete.error-doesnt-exist={0} 不存在！
luckperms.command.generic.rename.success={0} 已成功重新命名為 {1}
luckperms.command.generic.clone.success={0} 已成功複製到 {1}
luckperms.command.generic.info.parent.title=父群組
luckperms.command.generic.info.parent.temporary-title=臨時父群組
luckperms.command.generic.info.expires-in=剩餘有效時間︰
luckperms.command.generic.info.inherited-from=繼承自
luckperms.command.generic.info.inherited-from-self=自己
luckperms.command.generic.show-tracks.title={0} 的權限階級
luckperms.command.generic.show-tracks.empty={0} 不在任何權限階級裡
luckperms.command.generic.clear.node-removed=已移除 {0} 個節點
luckperms.command.generic.clear.node-removed-singular=已移除 {0} 個節點
luckperms.command.generic.clear={0} 在環境 {1} 中的節點已經清除
luckperms.command.generic.permission.info.title={0} 的權限
luckperms.command.generic.permission.info.empty={0} 沒有任何已設定的權限
luckperms.command.generic.permission.info.click-to-remove=點擊來將這個節點從 {0} 中移除
luckperms.command.generic.permission.check.info.title={0} 的權限資訊
luckperms.command.generic.permission.check.info.directly=在環境 {3} 中，{0} 的權限 {1} 被設為 {2}
luckperms.command.generic.permission.check.info.inherited=在環境 {4} 中，{0} 從 {3} 繼承的權限 {1} 被設為 {2}
luckperms.command.generic.permission.check.info.not-directly={0} 沒有設定權限 {1}
luckperms.command.generic.permission.check.info.not-inherited={0} 沒有繼承 {1}
luckperms.command.generic.permission.check.result.title={0} 的權限檢查
luckperms.command.generic.permission.check.result.result-key=結果
luckperms.command.generic.permission.check.result.processor-key=處理者
luckperms.command.generic.permission.check.result.cause-key=原因
luckperms.command.generic.permission.check.result.context-key=環境
luckperms.command.generic.permission.set=已設定在環境 {3} 中 {2} 的權限 {0} 為 {1}
luckperms.command.generic.permission.already-has={0} 已經在環境 {2} 中設定了權限 {1}
luckperms.command.generic.permission.set-temp=已設定在環境 {4} 中 {2} 的權限 {0} 為 {1}，有效時間：{3}
luckperms.command.generic.permission.already-has-temp={0} 已經在環境 {2} 中設定了臨時權限 {1}
luckperms.command.generic.permission.unset=已解除 {1} 在環境 {2} 中的權限 {0}
luckperms.command.generic.permission.doesnt-have={0} 沒有在環境 {2} 中設定權限 {1}
luckperms.command.generic.permission.unset-temp=已解除 {1} 在環境 {2} 中的臨時權限 {0}
luckperms.command.generic.permission.subtract=已設定在環境 {4} 中 {2} 的權限 {0} 為 {1}，有效時間：{3}，比上次設定少 {5}
luckperms.command.generic.permission.doesnt-have-temp={0} 沒有在環境 {2} 中設定臨時權限 {1}
luckperms.command.generic.permission.clear={0} 在環境 {1} 中的的權限已經清除
luckperms.command.generic.parent.info.title={0} 的父系
luckperms.command.generic.parent.info.empty={0} 沒有任何已定義的父系
luckperms.command.generic.parent.info.click-to-remove=點擊來將這個父系從 {0} 中移除
luckperms.command.generic.parent.add={0} 現在從環境 {2} 中繼承 {1} 的權限
luckperms.command.generic.parent.add-temp={0} 現在從環境 {3} 中繼承 {1} 的權限，有效時間：{2}
luckperms.command.generic.parent.set=清除了 {0} 現有的父群組，現在只在環境 {2} 中繼承 {1}
luckperms.command.generic.parent.set-track=清除了 {0} 在權限階級 {1} 中現有的父群組，現在只在環境 {3} 中繼承 {2}
luckperms.command.generic.parent.remove={0} 不再從環境 {2} 中繼承 {1} 的權限
luckperms.command.generic.parent.remove-temp={0} 不再從環境 {2} 中臨時繼承 {1} 的權限
luckperms.command.generic.parent.subtract={0} 現在從環境 {3} 中繼承 {1} 的權限，有效時間：{2}，比上次設定少 {4}
luckperms.command.generic.parent.clear={0} 在環境 {1} 中的父系已經清除
luckperms.command.generic.parent.clear-track={0} 在環境 {2} 中的權限階級 {1} 的父系已經清除
luckperms.command.generic.parent.already-inherits={0} 已經從環境 {2} 中繼承了 {1}
luckperms.command.generic.parent.doesnt-inherit={0} 沒有從環境 {2} 中繼承 {1}
luckperms.command.generic.parent.already-temp-inherits={0} 已經從環境 {2} 中臨時繼承 {1}
luckperms.command.generic.parent.doesnt-temp-inherit={0} 沒有從環境 {2} 中臨時繼承 {1} 的權限
luckperms.command.generic.chat-meta.info.title-prefix={0} 的前綴
luckperms.command.generic.chat-meta.info.title-suffix={0} 的後綴
luckperms.command.generic.chat-meta.info.none-prefix={0} 沒有前綴
luckperms.command.generic.chat-meta.info.none-suffix={0} 沒有後綴
luckperms.command.generic.chat-meta.info.click-to-remove=點擊來將 {0} 從 {1} 中移除
luckperms.command.generic.chat-meta.already-has={0} 已經在環境 {4} 下設定了 {1} {2} 的優先權為 {3}
luckperms.command.generic.chat-meta.already-has-temp={0} 已經在環境 {4} 下臨時設定了 {1} {2} 的優先權為 {3}
luckperms.command.generic.chat-meta.doesnt-have={0} 沒有在環境 {4} 下設定了 {1} {2} 的優先權為 {3}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} 沒有在環境 {4} 下臨時設定了 {1} {2} 的優先權為 {3}
luckperms.command.generic.chat-meta.add={0} 在環境 {4} 下設定了 {1} {2} 的優先權為 {3}
luckperms.command.generic.chat-meta.add-temp={0} 在環境 {5} 下設定了 {1} {2} 的優先權為 {3}，有效時間：{4}
luckperms.command.generic.chat-meta.remove={0} 在環境 {4} 下移除了 {1} {2} 的優先權 {3}
luckperms.command.generic.chat-meta.remove-bulk={0} 在環境 {3} 下移除了全部 {1} 的優先權 {2}
luckperms.command.generic.chat-meta.remove-temp={0} 在環境 {4} 下臨時移除了 {1} {2} 的優先權 {3}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} 在環境 {3} 下臨時移除了全部 {1} 的優先權 {2}
luckperms.command.generic.meta.info.title={0} 的中繼資料
luckperms.command.generic.meta.info.none={0} 沒有中繼資料
luckperms.command.generic.meta.info.click-to-remove=點擊來將這個元節點從 {0} 中移除
luckperms.command.generic.meta.already-has={0} 已經在環境 {3} 中將金鑰 {1} 設為 {2}
luckperms.command.generic.meta.already-has-temp={0} 已經在環境 {3} 中將臨時元金鑰 {1} 設為 {2}
luckperms.command.generic.meta.doesnt-have={0} 沒有在環境 {2} 中設定元金鑰 {1}
luckperms.command.generic.meta.doesnt-have-temp={0} 沒有在環境 {2} 中設定臨時元金鑰 {1}
luckperms.command.generic.meta.set=已設定在環境 {3} 中 {2} 的元金鑰 {0} 為 {1}
luckperms.command.generic.meta.set-temp=已設定在環境 {4} 中 {2} 的元金鑰 {0} 為 {1}，有效時間：{3}
luckperms.command.generic.meta.unset=已解除 {1} 在環境 {2} 中的元金鑰 {0}
luckperms.command.generic.meta.unset-temp=已解除 {1} 在環境 {2} 中的臨時元金鑰 {0}
luckperms.command.generic.meta.clear=在 {2} 環境中，屬於 {0} 的中繼資料比對類型 {1} 已被清除
luckperms.command.generic.contextual-data.title=環境資料
luckperms.command.generic.contextual-data.mode.key=模式
luckperms.command.generic.contextual-data.mode.server=伺服器
luckperms.command.generic.contextual-data.mode.active-player=活躍玩家
luckperms.command.generic.contextual-data.contexts-key=環境
luckperms.command.generic.contextual-data.prefix-key=前綴
luckperms.command.generic.contextual-data.suffix-key=後綴
luckperms.command.generic.contextual-data.primary-group-key=主要群組
luckperms.command.generic.contextual-data.meta-key=中繼資料
luckperms.command.generic.contextual-data.null-result=無
luckperms.command.user.info.title=使用者資訊
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=類型
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=離線
luckperms.command.user.info.status-key=狀態
luckperms.command.user.info.status.online=線上
luckperms.command.user.info.status.offline=離線
luckperms.command.user.removegroup.error-primary=你不能將使用者從其主要群組中移除
luckperms.command.user.primarygroup.not-member={0} 尚未成為 {1} 的成員，現在正在新增
luckperms.command.user.primarygroup.already-has={0} 的主要群組已經是 {1}
luckperms.command.user.primarygroup.warn-option=警告：此伺服器 ({0}) 正在使用的主要群組計算方法可能無法反映本次變更
luckperms.command.user.primarygroup.set={0} 的主要群組已設定為 {1}
luckperms.command.user.track.error-not-contain-group={0} 尚未在 {1} 的任何群組內
luckperms.command.user.track.unsure-which-track=無法確定要使用的權限階級，請將其指定為引數
luckperms.command.user.track.missing-group-advice=請建立群組，或從權限階級中刪除它，然後再試一次
luckperms.command.user.promote.added-to-first={0} 不在 {1} 的任何群組中，所以他們被加入到環境 {3} 的第一個群組 {2}
luckperms.command.user.promote.not-on-track={0} 不在 {1} 的任何一個群組，因而沒有升級
luckperms.command.user.promote.success=在環境 {4} 中沿權限階級 {1} 升級 {0} 從 {2} 到 {3}
luckperms.command.user.promote.end-of-track=權限階級 {0} 已經到達結尾，無法升級 {1}
luckperms.command.user.promote.next-group-deleted=權限階級上的下一個群組 {0}，已經不存在
luckperms.command.user.promote.unable-to-promote=無法提升使用者權限
luckperms.command.user.demote.success=在環境 {4} 中沿權限階級 {1} 降級 {0} 從 {2} 到 {3}
luckperms.command.user.demote.end-of-track=權限階級 {0} 已經到達結尾，因此 {1} 已經從 {2} 移除
luckperms.command.user.demote.end-of-track-not-removed=權限階級 {0} 已經到達結尾，但是 {1} 沒有從第一群組中移除
luckperms.command.user.demote.previous-group-deleted=權限階級上的上一個群組 {0}，已經不存在
luckperms.command.user.demote.unable-to-demote=無法降低使用者權限
luckperms.command.group.list.title=群組
luckperms.command.group.delete.not-default=你不能刪除預設群組
luckperms.command.group.info.title=群組資訊
luckperms.command.group.info.display-name-key=顯示名稱
luckperms.command.group.info.weight-key=權重
luckperms.command.group.setweight.set=已將群組 {1} 的權重設為 {0}
luckperms.command.group.setdisplayname.doesnt-have={0} 尚未設定顯示名稱
luckperms.command.group.setdisplayname.already-has={0} 的顯示名稱已是 {1}
luckperms.command.group.setdisplayname.already-in-use=該顯示名稱 {0} 已經被 {1} 所使用
luckperms.command.group.setdisplayname.set=已設定群組 {1} 在環境 {2} 中的顯示名稱為 {0}
luckperms.command.group.setdisplayname.removed=已移除群組 {0} 在環境 {1} 中的顯示名稱
luckperms.command.track.list.title=權限階級
luckperms.command.track.path.empty=無
luckperms.command.track.info.showing-track=顯示歷史記錄
luckperms.command.track.info.path-property=路徑
luckperms.command.track.clear={0} 的群組權限階級已被清除
luckperms.command.track.append.success=群組 {0} 已加入到權限階級 {1}
luckperms.command.track.insert.success=群組 {0} 被插入到權限階級 {1} 的位置 {2}
luckperms.command.track.insert.error-number=應為數值，但卻收到：{0}
luckperms.command.track.insert.error-invalid-pos=無法插入到位置 {0}
luckperms.command.track.insert.error-invalid-pos-reason=無效的位置
luckperms.command.track.remove.success=群組 {0} 已從權限階級 {1} 中移除
luckperms.command.track.error-empty=無法使用 {0}，因為它是空的或僅包含一個群組
luckperms.command.track.error-multiple-groups={0} 是此權限階級上多個群組的成員
luckperms.command.track.error-ambiguous=無法確定其位置
luckperms.command.track.already-contains={0} 已經包含 {1}
luckperms.command.track.doesnt-contain={0} 並未包含 {1}
luckperms.command.log.load-error=無法載入記錄
luckperms.command.log.invalid-page=無效的頁碼
luckperms.command.log.invalid-page-range=請輸入介於 {0} 至 {1} 之間的值
luckperms.command.log.empty=沒有可顯示的記錄項目
luckperms.command.log.notify.error-console=無法切換控制台的通知
luckperms.command.log.notify.enabled-term=已啟用
luckperms.command.log.notify.disabled-term=已停用
luckperms.command.log.notify.changed-state={0} 記錄輸出
luckperms.command.log.notify.already-on=你已正在接收通知
luckperms.command.log.notify.already-off=你目前不會接收通知
luckperms.command.log.notify.invalid-state=未知的狀態。應為 {0} 或 {1}
luckperms.command.log.show.search=顯示查詢 {0} 的最近動作
luckperms.command.log.show.recent=顯示最近的動作
luckperms.command.log.show.by=顯示 {0} 最近的動作
luckperms.command.log.show.history=顯示 {0} {1} 的歷史記錄
luckperms.command.export.error-term=錯誤
luckperms.command.export.already-running=另一個匯出處理程序正在執行中
luckperms.command.export.file.already-exists=檔案 {0} 已存在
luckperms.command.export.file.not-writable=檔案 {0} 無法寫入
luckperms.command.export.file.success=已成功匯出至 {0}
luckperms.command.export.file-unexpected-error-writing=在寫入檔案時發生非預期的錯誤
luckperms.command.export.web.export-code=匯出代碼
luckperms.command.export.web.import-command-description=使用以下指令來匯入
luckperms.command.import.term=匯入
luckperms.command.import.error-term=錯誤
luckperms.command.import.already-running=已有一項匯入作業正在進行
luckperms.command.import.file.doesnt-exist=檔案 {0} 不存在
luckperms.command.import.file.not-readable=無法讀取檔案 {0}
luckperms.command.import.file.unexpected-error-reading=在讀取匯入檔案時發生非預期的錯誤
luckperms.command.import.file.correct-format=格式是否正確？
luckperms.command.import.web.unable-to-read=無法從指定的代碼讀取資料
luckperms.command.import.progress.percent=已完成 {0}%
luckperms.command.import.progress.operations=已完成 {0}/{1} 項作業
luckperms.command.import.starting=開始匯入作業
luckperms.command.import.completed=已完成
luckperms.command.import.duration=耗時 {0} 秒
luckperms.command.bulkupdate.must-use-console=大量更新指令只能在控制台使用
luckperms.command.bulkupdate.invalid-data-type=無效的類型，應為 {0}
luckperms.command.bulkupdate.invalid-constraint=無效的約束 {0}
luckperms.command.bulkupdate.invalid-constraint-format=約束應遵循格式 {0}
luckperms.command.bulkupdate.invalid-comparison=無效的比較運算子 {0}
luckperms.command.bulkupdate.invalid-comparison-format=應為以下之一：{0}
luckperms.command.bulkupdate.queued=大量更新作業已佇列
luckperms.command.bulkupdate.confirm=執行 {0} 來進行更新
luckperms.command.bulkupdate.unknown-id=ID {0} 的作業不存在或已過期
luckperms.command.bulkupdate.starting=正在執行大量更新
luckperms.command.bulkupdate.success=大量更新已成功完成
luckperms.command.bulkupdate.success.statistics.nodes=受影響節點總數
luckperms.command.bulkupdate.success.statistics.users=受影響使用者總數
luckperms.command.bulkupdate.success.statistics.groups=受影響群組總數
luckperms.command.bulkupdate.failure=大量更新失敗，請檢查主控台是否有錯誤訊息
luckperms.command.update-task.request=已收到更新任務請求，請稍候
luckperms.command.update-task.complete=更新任務完成
luckperms.command.update-task.push.attempting=現在正嘗試推送到其他伺服器
luckperms.command.update-task.push.complete=已透過 {0} 成功通知其他伺服器
luckperms.command.update-task.push.error=將變更推送到其他伺服器時發生錯誤
luckperms.command.update-task.push.error-not-setup=由於尚未設定訊息服務，無法將變更推送到其他伺服器
luckperms.command.reload-config.success=已重新載入設定檔
luckperms.command.reload-config.restart-note=部分選項將在伺服器重新啟動後才會生效
luckperms.command.translations.searching=正在搜尋可用的翻譯，請稍候……
luckperms.command.translations.searching-error=無法取得可用的翻譯清單
luckperms.command.translations.installed-translations=已安裝的翻譯
luckperms.command.translations.available-translations=可用的翻譯
luckperms.command.translations.percent-translated=已翻譯 {0}%
luckperms.command.translations.translations-by=翻譯者︰
luckperms.command.translations.installing=正在安裝翻譯，請稍候……
luckperms.command.translations.download-error=無法下載語言 {0} 的翻譯
luckperms.command.translations.installing-specific=正在安裝語言 {0}……
luckperms.command.translations.install-complete=安裝完成
luckperms.command.translations.download-prompt=使用 {0} 下載並安裝由社群提供的最新翻譯版本
luckperms.command.translations.download-override-warning=請注意，這將你覆蓋你對這些語言所做的任何變更
luckperms.usage.user.description=在 LuckPerms 中管理使用者的一組指令。（LuckPerms 中的「user」代表玩家，可以是其 UUID 或使用者名稱）
luckperms.usage.group.description=在 LuckPerms 裡用來管理群組（Group）的一組指令。群組集合了一些需要給使用者的權限節點，你可以使用「creategroup」來新增一個群組。
luckperms.usage.track.description=在 LuckPerms 裡用來管理權限階級（Track）的一組指令。權限階級是群組的排位順序，可用於定義升級和降級。
luckperms.usage.log.description=用於管理 LuckPerms 中記錄功能的一組指令。
luckperms.usage.sync.description=從插件儲存空間重新載入所有資料到記憶體，並套用檢測到的任何變更。
luckperms.usage.info.description=列出有關執行中的插件實例的一般訊息。
luckperms.usage.editor.description=建立一個新的網頁編輯器工作階段
luckperms.usage.editor.argument.type=要載入至編輯器中的類型。（「all」、「users」或「groups」）
luckperms.usage.editor.argument.filter=過濾使用者條目的權限
luckperms.usage.verbose.description=控制插件詳細權限檢查監控系統。
luckperms.usage.verbose.argument.action=是否要啟用或停用記錄功能，或上傳已記錄的輸出。
luckperms.usage.verbose.argument.filter=用於比對項目的篩選器
luckperms.usage.verbose.argument.commandas=要執行的玩家或指令
luckperms.usage.tree.description=產生 LuckPerms 已知的所有權限的樹狀檢視（已排序的清單階層）。
luckperms.usage.tree.argument.scope=根目錄指定「.」以包含所有權限
luckperms.usage.tree.argument.player=要檢查的線上玩家名稱
luckperms.usage.search.description=搜尋擁有特定權限的所有使用者或群組
luckperms.usage.search.argument.permission=要搜尋的權限
luckperms.usage.search.argument.page=要查看的頁面
luckperms.usage.network-sync.description=將修改同步到資料庫中並且也在連線的其他伺服器中同步
luckperms.usage.import.description=從（先前建立的）匯出檔案中匯入資料
luckperms.usage.import.argument.file=要匯入的檔案
luckperms.usage.import.argument.replace=取代現有資料而不作合併
luckperms.usage.import.argument.upload=上傳先前匯出的資料
luckperms.usage.export.description=將所有權限資料匯出到「匯出」檔案。可在稍後重新匯入。
luckperms.usage.export.argument.file=要匯出的檔案
luckperms.usage.export.argument.without-users=在匯出時排除使用者
luckperms.usage.export.argument.without-groups=在匯出時排除群組
luckperms.usage.export.argument.upload=將所有權限資料上傳到網頁編輯器。可在稍後重新匯入。
luckperms.usage.reload-config.description=重新載入一些設定的選項
luckperms.usage.bulk-update.description=對所有資料執行大量變更查詢。
luckperms.usage.bulk-update.argument.data-type=要變更的資料類型。（「all」、「users」或「groups」）
luckperms.usage.bulk-update.argument.action=要在資料上執行的動作。（「update」或「delete」）
luckperms.usage.bulk-update.argument.action-field=需要更新的區域，僅在「update」時需要輸入。（「permission」、「server」或「world」）
luckperms.usage.bulk-update.argument.action-value=要取代的值。僅在 ''update'' 時需要輸入。
luckperms.usage.bulk-update.argument.constraint=更新時需要的約束
luckperms.usage.translations.description=管理翻譯
luckperms.usage.translations.argument.install=安裝翻譯的子指令
luckperms.usage.apply-edits.description=套用從網頁編輯器進行的權限變更
luckperms.usage.apply-edits.argument.code=資料的唯一代碼
luckperms.usage.apply-edits.argument.target=將資料套用到誰身上
luckperms.usage.create-group.description=建立新群組
luckperms.usage.create-group.argument.name=群組名稱
luckperms.usage.create-group.argument.weight=群組的權重
luckperms.usage.create-group.argument.display-name=群組的顯示名稱
luckperms.usage.delete-group.description=刪除群組
luckperms.usage.delete-group.argument.name=群組名稱
luckperms.usage.list-groups.description=列出平台上的所有群組
luckperms.usage.create-track.description=建立新權限階級
luckperms.usage.create-track.argument.name=權限階級名稱
luckperms.usage.delete-track.description=刪除權限階級
luckperms.usage.delete-track.argument.name=權限階級名稱
luckperms.usage.list-tracks.description=列出平台上所有的權限階級
luckperms.usage.user-info.description=顯示有關使用者的資訊
luckperms.usage.user-switchprimarygroup.description=切換使用者的主要群組
luckperms.usage.user-switchprimarygroup.argument.group=要切換的群組
luckperms.usage.user-promote.description=提升使用者一個權限階級
luckperms.usage.user-promote.argument.track=要升級使用者的目標權限階級
luckperms.usage.user-promote.argument.context=要升級使用者的環境
luckperms.usage.user-promote.argument.dont-add-to-first=只有已經在權限階級內的使用者才會升級
luckperms.usage.user-demote.description=降低使用者一個權限階級
luckperms.usage.user-demote.argument.track=要降級使用者的目標權限階級
luckperms.usage.user-demote.argument.context=要降級使用者的環境
luckperms.usage.user-demote.argument.dont-remove-from-first=防止使用者從第一群組中移除
luckperms.usage.user-clone.description=複製使用者
luckperms.usage.user-clone.argument.user=要複製的使用者名稱或 UUID
luckperms.usage.group-info.description=顯示有關群組的資訊
luckperms.usage.group-listmembers.description=顯示繼承此群組的使用者或群組
luckperms.usage.group-listmembers.argument.page=要查看的頁面
luckperms.usage.group-setweight.description=設定群組的權重
luckperms.usage.group-setweight.argument.weight=要設定的權重
luckperms.usage.group-set-display-name.description=設定群組的顯示名稱
luckperms.usage.group-set-display-name.argument.name=要設定的名稱
luckperms.usage.group-set-display-name.argument.context=要設定名稱的環境
luckperms.usage.group-rename.description=重新命名群組
luckperms.usage.group-rename.argument.name=新名稱
luckperms.usage.group-clone.description=複製群組
luckperms.usage.group-clone.argument.name=要複製的群組名稱
luckperms.usage.holder-editor.description=開啟權限編輯器網頁
luckperms.usage.holder-showtracks.description=列出物件所在的權限階級
luckperms.usage.holder-clear.description=移除所有權限、父系和中繼資料
luckperms.usage.holder-clear.argument.context=要過濾的環境
luckperms.usage.permission.description=編輯權限
luckperms.usage.parent.description=編輯繼承項
luckperms.usage.meta.description=編輯中繼資料值
luckperms.usage.permission-info.description=列出物件擁有的權限
luckperms.usage.permission-info.argument.page=要查看的頁面
luckperms.usage.permission-info.argument.sort-mode=如何排序條目
luckperms.usage.permission-set.description=為物件設定權限
luckperms.usage.permission-set.argument.node=要設定的權限節點
luckperms.usage.permission-set.argument.value=節點的值
luckperms.usage.permission-set.argument.context=要增加權限的環境
luckperms.usage.permission-unset.description=為物件解除設定權限
luckperms.usage.permission-unset.argument.node=要解除設定的權限節點
luckperms.usage.permission-unset.argument.context=要移除權限的環境
luckperms.usage.permission-settemp.description=為物件設定臨時權限
luckperms.usage.permission-settemp.argument.node=要設定的權限節點
luckperms.usage.permission-settemp.argument.value=節點的值
luckperms.usage.permission-settemp.argument.duration=權限節點的有效時間
luckperms.usage.permission-settemp.argument.temporary-modifier=臨時權限的套用方式
luckperms.usage.permission-settemp.argument.context=要增加權限的環境
luckperms.usage.permission-unsettemp.description=為物件解除設定臨時權限
luckperms.usage.permission-unsettemp.argument.node=要解除設定的權限節點
luckperms.usage.permission-unsettemp.argument.duration=要減去的時間
luckperms.usage.permission-unsettemp.argument.context=要移除權限的環境
luckperms.usage.permission-check.description=檢查物件是否擁有特定的權限節點
luckperms.usage.permission-check.argument.node=要檢查的權限節點
luckperms.usage.permission-clear.description=清除所有權限
luckperms.usage.permission-clear.argument.context=要過濾的環境
luckperms.usage.parent-info.description=列出該物件繼承的群組
luckperms.usage.parent-info.argument.page=要查看的頁面
luckperms.usage.parent-info.argument.sort-mode=如何排序條目
luckperms.usage.parent-set.description=刪除物件現時繼承的所有群組並將其增加到指定的群組
luckperms.usage.parent-set.argument.group=要設定的群組
luckperms.usage.parent-set.argument.context=要設定群組的環境
luckperms.usage.parent-add.description=設定另一個繼承物件的權限的群組
luckperms.usage.parent-add.argument.group=要繼承的群組
luckperms.usage.parent-add.argument.context=要繼承群組的環境
luckperms.usage.parent-remove.description=刪除先前設定的繼承規則
luckperms.usage.parent-remove.argument.group=要移除的群組
luckperms.usage.parent-remove.argument.context=要移除群組的環境
luckperms.usage.parent-set-track.description=刪除物件現時在權限階級繼承的所有群組並將其增加到指定的群組
luckperms.usage.parent-set-track.argument.track=要設定的權限階級
luckperms.usage.parent-set-track.argument.group=要設定的群組，或是該群組在指定的權限階級裡的位置
luckperms.usage.parent-set-track.argument.context=要設定群組的環境
luckperms.usage.parent-add-temp.description=設定另一個暫時繼承物件的權限的群組
luckperms.usage.parent-add-temp.argument.group=要繼承的群組
luckperms.usage.parent-add-temp.argument.duration=群組成員的有效時間
luckperms.usage.parent-add-temp.argument.temporary-modifier=如何套用臨時權限
luckperms.usage.parent-add-temp.argument.context=要繼承群組的環境
luckperms.usage.parent-remove-temp.description=刪除先前設定的臨時繼承規則
luckperms.usage.parent-remove-temp.argument.group=要移除的群組
luckperms.usage.parent-remove-temp.argument.duration=要減去的時間
luckperms.usage.parent-remove-temp.argument.context=要移除群組的環境
luckperms.usage.parent-clear.description=清除所有父系
luckperms.usage.parent-clear.argument.context=要過濾的環境
luckperms.usage.parent-clear-track.description=清除指定權限階級上的所有父系群組
luckperms.usage.parent-clear-track.argument.track=要移除的權限階級
luckperms.usage.parent-clear-track.argument.context=要過濾的環境
luckperms.usage.meta-info.description=顯示所有聊天中繼資料
luckperms.usage.meta-set.description=設定一個元值
luckperms.usage.meta-set.argument.key=要設定的金鑰
luckperms.usage.meta-set.argument.value=要設定的值
luckperms.usage.meta-set.argument.context=要增加中繼資料的環境
luckperms.usage.meta-unset.description=解除設定一個元值
luckperms.usage.meta-unset.argument.key=要解除設定的金鑰
luckperms.usage.meta-unset.argument.context=要移除元資料的環境
luckperms.usage.meta-settemp.description=設定一個臨時元值
luckperms.usage.meta-settemp.argument.key=要設定的金鑰
luckperms.usage.meta-settemp.argument.value=要設定的值
luckperms.usage.meta-settemp.argument.duration=元值的有效時間
luckperms.usage.meta-settemp.argument.context=要增加中繼資料的環境
luckperms.usage.meta-unsettemp.description=解除設定一個臨時元值
luckperms.usage.meta-unsettemp.argument.key=要解除設定的金鑰
luckperms.usage.meta-unsettemp.argument.context=要移除中繼資料的環境
luckperms.usage.meta-addprefix.description=增加一個前綴
luckperms.usage.meta-addprefix.argument.priority=要增加前綴的優先權
luckperms.usage.meta-addprefix.argument.prefix=前綴字串
luckperms.usage.meta-addprefix.argument.context=要增加前綴的環境
luckperms.usage.meta-addsuffix.description=增加一個後綴
luckperms.usage.meta-addsuffix.argument.priority=要增加後綴的優先權
luckperms.usage.meta-addsuffix.argument.suffix=後綴字串
luckperms.usage.meta-addsuffix.argument.context=要增加後綴的環境
luckperms.usage.meta-setprefix.description=設定一個前綴
luckperms.usage.meta-setprefix.argument.priority=要設定前綴的優先權
luckperms.usage.meta-setprefix.argument.prefix=前綴字串
luckperms.usage.meta-setprefix.argument.context=要設定前綴的環境
luckperms.usage.meta-setsuffix.description=設定後綴
luckperms.usage.meta-setsuffix.argument.priority=要設定後綴的優先權
luckperms.usage.meta-setsuffix.argument.suffix=後綴文字
luckperms.usage.meta-setsuffix.argument.context=要設定後綴的環境
luckperms.usage.meta-removeprefix.description=移除一個前綴
luckperms.usage.meta-removeprefix.argument.priority=要移除前綴的優先權
luckperms.usage.meta-removeprefix.argument.prefix=前綴字串
luckperms.usage.meta-removeprefix.argument.context=要移除前綴的環境
luckperms.usage.meta-removesuffix.description=移除一個後綴
luckperms.usage.meta-removesuffix.argument.priority=要移除後綴的優先權
luckperms.usage.meta-removesuffix.argument.suffix=後綴字串
luckperms.usage.meta-removesuffix.argument.context=要移除後綴的環境
luckperms.usage.meta-addtemp-prefix.description=增加一個臨時前綴
luckperms.usage.meta-addtemp-prefix.argument.priority=要增加前綴的優先權
luckperms.usage.meta-addtemp-prefix.argument.prefix=前綴字串
luckperms.usage.meta-addtemp-prefix.argument.duration=前綴的有效時間
luckperms.usage.meta-addtemp-prefix.argument.context=要增加前綴的環境
luckperms.usage.meta-addtemp-suffix.description=增加一個臨時後綴
luckperms.usage.meta-addtemp-suffix.argument.priority=要增加後綴的優先權
luckperms.usage.meta-addtemp-suffix.argument.suffix=後綴字串
luckperms.usage.meta-addtemp-suffix.argument.duration=後綴的有效時間
luckperms.usage.meta-addtemp-suffix.argument.context=要增加後綴的環境
luckperms.usage.meta-settemp-prefix.description=設定一個臨時前綴
luckperms.usage.meta-settemp-prefix.argument.priority=要設定前綴的優先權
luckperms.usage.meta-settemp-prefix.argument.prefix=前綴字串
luckperms.usage.meta-settemp-prefix.argument.duration=前綴的有效時間
luckperms.usage.meta-settemp-prefix.argument.context=要設定前綴的環境
luckperms.usage.meta-settemp-suffix.description=設定一個臨時後綴
luckperms.usage.meta-settemp-suffix.argument.priority=要設定後綴的優先權
luckperms.usage.meta-settemp-suffix.argument.suffix=後綴字串
luckperms.usage.meta-settemp-suffix.argument.duration=後綴的有效時間
luckperms.usage.meta-settemp-suffix.argument.context=要設定後綴的環境
luckperms.usage.meta-removetemp-prefix.description=移除一個臨時前綴
luckperms.usage.meta-removetemp-prefix.argument.priority=要移除前綴的優先權
luckperms.usage.meta-removetemp-prefix.argument.prefix=前綴字串
luckperms.usage.meta-removetemp-prefix.argument.context=要移除前綴的環境
luckperms.usage.meta-removetemp-suffix.description=移除一個臨時後綴
luckperms.usage.meta-removetemp-suffix.argument.priority=要移除後綴的優先權
luckperms.usage.meta-removetemp-suffix.argument.suffix=後綴字串
luckperms.usage.meta-removetemp-suffix.argument.context=要移除後綴的環境
luckperms.usage.meta-clear.description=清除所有中繼資料
luckperms.usage.meta-clear.argument.type=要移除的中繼資料類型
luckperms.usage.meta-clear.argument.context=要過濾的環境
luckperms.usage.track-info.description=顯示有關權限階級的資訊
luckperms.usage.track-editor.description=開啟權限編輯器網頁
luckperms.usage.track-append.description=將一個群組追加到權限階級的結尾
luckperms.usage.track-append.argument.group=要附加的組別
luckperms.usage.track-insert.description=在權限階級的指定位置插入群組
luckperms.usage.track-insert.argument.group=要插入的組別
luckperms.usage.track-insert.argument.position=插入群組的位置（權限階級上的第一個位置是 1）
luckperms.usage.track-remove.description=從權限階級中移除群組
luckperms.usage.track-remove.argument.group=要移除的群組
luckperms.usage.track-clear.description=清除權限階級上的群組
luckperms.usage.track-rename.description=重新命名權限階級
luckperms.usage.track-rename.argument.name=新名稱
luckperms.usage.track-clone.description=複製權限階級
luckperms.usage.track-clone.argument.name=要複製的權限階級名稱
luckperms.usage.log-recent.description=查看最近的操作
luckperms.usage.log-recent.argument.user=要過濾的使用者名稱或 UUID
luckperms.usage.log-recent.argument.page=要查看的頁碼
luckperms.usage.log-search.description=在日誌中搜尋條目
luckperms.usage.log-search.argument.query=要搜尋的查詢
luckperms.usage.log-search.argument.page=要查看的頁碼
luckperms.usage.log-notify.description=切換權限通知開啟或關閉
luckperms.usage.log-notify.argument.toggle=指定權限通知開啟或關閉
luckperms.usage.log-user-history.description=查看使用者的歷程記錄
luckperms.usage.log-user-history.argument.user=使用者名稱或 UUID
luckperms.usage.log-user-history.argument.page=要查看的頁碼
luckperms.usage.log-group-history.description=查看群組的歷程記錄
luckperms.usage.log-group-history.argument.group=群組名稱
luckperms.usage.log-group-history.argument.page=要查看的頁碼
luckperms.usage.log-track-history.description=查看歷程記錄
luckperms.usage.log-track-history.argument.track=歷程名稱
luckperms.usage.log-track-history.argument.page=要查看的頁碼
luckperms.usage.sponge.description=編輯額外的 Sponge 資料
luckperms.usage.sponge.argument.collection=要查詢的集合
luckperms.usage.sponge.argument.subject=要修改的主體
luckperms.usage.sponge-permission-info.description=顯示主體的權限資訊
luckperms.usage.sponge-permission-info.argument.contexts=要過濾的環境
luckperms.usage.sponge-permission-set.description=為主體設定權限
luckperms.usage.sponge-permission-set.argument.node=要設定的權限節點
luckperms.usage.sponge-permission-set.argument.tristate=要設定權限的值
luckperms.usage.sponge-permission-set.argument.contexts=要設定權限的環境
luckperms.usage.sponge-permission-clear.description=清除主體的權限
luckperms.usage.sponge-permission-clear.argument.contexts=要清除權限的環境
luckperms.usage.sponge-parent-info.description=顯示主體的父系資訊
luckperms.usage.sponge-parent-info.argument.contexts=要過濾的環境
luckperms.usage.sponge-parent-add.description=為主體增加一個父系
luckperms.usage.sponge-parent-add.argument.collection=目標父系所在的主體集合
luckperms.usage.sponge-parent-add.argument.subject=父系名稱
luckperms.usage.sponge-parent-add.argument.contexts=要增加父系的環境
luckperms.usage.sponge-parent-remove.description=從主體移除父系
luckperms.usage.sponge-parent-remove.argument.collection=目標父系所在的主體集合
luckperms.usage.sponge-parent-remove.argument.subject=父系名稱
luckperms.usage.sponge-parent-remove.argument.contexts=要移除父系的環境
luckperms.usage.sponge-parent-clear.description=清除主體的父系
luckperms.usage.sponge-parent-clear.argument.contexts=要清除父系的環境
luckperms.usage.sponge-option-info.description=顯示有關主體的選項的資訊
luckperms.usage.sponge-option-info.argument.contexts=要篩選的情境
luckperms.usage.sponge-option-set.description=為主體設定一個選項
luckperms.usage.sponge-option-set.argument.key=要設定的金鑰
luckperms.usage.sponge-option-set.argument.value=要設定金鑰的值
luckperms.usage.sponge-option-set.argument.contexts=要設定選項的環境
luckperms.usage.sponge-option-unset.description=為主體解除設定一個選項
luckperms.usage.sponge-option-unset.argument.key=要解除設定的金鑰
luckperms.usage.sponge-option-unset.argument.contexts=要解除設定金鑰的環境
luckperms.usage.sponge-option-clear.description=清除主體的選項
luckperms.usage.sponge-option-clear.argument.contexts=要清除選項的環境
