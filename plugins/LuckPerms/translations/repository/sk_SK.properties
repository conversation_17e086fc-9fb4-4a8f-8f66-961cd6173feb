luckperms.logs.actionlog-prefix=ZÁZNAMY
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTOVAŤ
luckperms.commandsystem.available-commands=<PERSON><PERSON><PERSON><PERSON> {0} pre zobrazenie dostupných príkazov
luckperms.commandsystem.command-not-recognised=Príkaz nebol rozpoznaný
luckperms.commandsystem.no-permission=Nemáš dostatočné oprávnenia na použitie tohoto príkazu\!
luckperms.commandsystem.no-permission-subcommands=Nemáš práva na používanie tohto príkazu
luckperms.commandsystem.already-executing-command=Vykonáva sa <PERSON>alší príkaz, čaká sa na jeho dokončenie...
luckperms.commandsystem.usage.sub-commands-header=Podpríkazy
luckperms.commandsystem.usage.usage-header=Použitie Príkazu
luckperms.commandsystem.usage.arguments-header=Argumenty
luckperms.first-time.no-permissions-setup=Žiadne permisie neboli doposiaľ nastavené\!
luckperms.first-time.use-console-to-give-access=<PERSON><PERSON><PERSON><PERSON>, ako použiješ nejaký príkaz LuckPerms v hre, mus<PERSON><PERSON> mať práva cez konzolu
luckperms.first-time.console-command-prompt=Kľúč\: luckperms.first-time.console-command-prompt\nluckperms.first-time.console-command-prompt
luckperms.first-time.next-step=Po dokončení môžeš začať zadávať oprávnenia a role
luckperms.first-time.wiki-prompt=Nevieš kde začať? Skontroluj tu\: {0}
luckperms.login.try-again=Skús to opäť neskôr, prosím
luckperms.login.loading-database-error=Chyba v databáze, ktorá nastala pri načítaní dát oprávnení
luckperms.login.server-admin-check-console-errors=Ak si server admin, skontroluj konzolu pre akékoľvek chyby
luckperms.login.server-admin-check-console-info=Prosím skontroluj konzolu pre viac informácií
luckperms.login.data-not-loaded-at-pre=Dáta oprávnení neboli načítané pre hráča počas pred prihlasovacej fáze
luckperms.login.unable-to-continue=nemožno pokračovať
luckperms.login.craftbukkit-offline-mode-error=toto je pravdepodobne chyba medzi CraftBukkit-om a online-mode nastavením
luckperms.login.unexpected-error=Kľúč\: luckperms.login.unexpected-error\nluckperms.login.unexpected-error
luckperms.opsystem.disabled=Vanilla OP systém je vypnutý na tomto serveri
luckperms.opsystem.sponge-warning=Upozorňujeme, že stav serverového operátora nemá žiadny vplyv na kontroly oprávnení Sponge, keď je nainštalovaný doplnok oprávnení, údaje používateľa musíte upraviť priamo
luckperms.duration.unit.years.plural={0} roky
luckperms.duration.unit.years.singular={0} rok
luckperms.duration.unit.years.short={0}r
luckperms.duration.unit.months.plural={0} mesiacov
luckperms.duration.unit.months.singular={0} mesiac
luckperms.duration.unit.months.short={0}m
luckperms.duration.unit.weeks.plural={0} týždňov
luckperms.duration.unit.weeks.singular={0} týždeň
luckperms.duration.unit.weeks.short={0}t
luckperms.duration.unit.days.plural={0} dní
luckperms.duration.unit.days.singular={0} deň
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} hodín
luckperms.duration.unit.hours.singular={0} hodina
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minút
luckperms.duration.unit.minutes.singular={0} minúta
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} sekúnd
luckperms.duration.unit.seconds.singular={0} sekunda
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since=Pred {0}
luckperms.command.misc.invalid-code=Neplatný kód
luckperms.command.misc.response-code-key=kód odpovede
luckperms.command.misc.error-message-key=správa
luckperms.command.misc.bytebin-unable-to-communicate=Nebolo možné komunikovať s bytebin
luckperms.command.misc.webapp-unable-to-communicate=Komunikácia s webovou aplikáciou bola neúspešná
luckperms.command.misc.check-console-for-errors=Skontroluj konzolu pre chyby
luckperms.command.misc.file-must-be-in-data=Súbor {0} musí byť priamo podradený adresáru dat
luckperms.command.misc.wait-to-finish=Počkaj prosím na dokončenie a skús to znovu
luckperms.command.misc.invalid-priority=Neplatná priorita {0}
luckperms.command.misc.expected-number=Očakávane číslo
luckperms.command.misc.date-parse-error=Nie je možné spracovať dátum {0}
luckperms.command.misc.date-in-past-error=Nie je možné nastaviť dátum z minulosti\!
luckperms.command.misc.page=strana {0} z {1}
luckperms.command.misc.page-entries={0} Vstupov
luckperms.command.misc.none=Žiadne
luckperms.command.misc.loading.error.unexpected=Vyskytla sa neočakávaná chyba
luckperms.command.misc.loading.error.user=Používateľ nie je načítaný
luckperms.command.misc.loading.error.user-specific=Nebolo možné načítať používateľa {0}
luckperms.command.misc.loading.error.user-not-found=Používateľ {0} nebol nájdený
luckperms.command.misc.loading.error.user-save-error=Pri ukladaní používateľských dát {0} došlo k chybe
luckperms.command.misc.loading.error.user-not-online=Užívateľ {0} není online
luckperms.command.misc.loading.error.user-invalid={0} Nie je platné užívateľské meno/uuid
luckperms.command.misc.loading.error.user-not-uuid=Cieľový používateľ {0} nie je platné UUID
luckperms.command.misc.loading.error.group=Skupina nebola načítaná
luckperms.command.misc.loading.error.all-groups=Nebolo možné načítať všetky skupiny
luckperms.command.misc.loading.error.group-not-found=Skupina s názvom {0} nebola nájdená
luckperms.command.misc.loading.error.group-save-error=Pri ukladaní dat skupiny {0} došlo k chybe
luckperms.command.misc.loading.error.group-invalid={0} Nie je platný názov skupiny
luckperms.command.misc.loading.error.track=Záznam nebol načítaný
luckperms.command.misc.loading.error.all-tracks=Nebolo možné načítať záznamy
luckperms.command.misc.loading.error.track-not-found=Záznam s názvom {0} nebol nájdený
luckperms.command.misc.loading.error.track-save-error=Pri ukladaní dat skupiny {0} došlo k chybe
luckperms.command.misc.loading.error.track-invalid={0} Nie je platný názov záznamu
luckperms.command.editor.no-match=Nebolo možné otvoriť editor, žiadne objekty neodpovedajú požadovanému typu
luckperms.command.editor.start=Príprava novej relácie editoru, počkaj prosím...
luckperms.command.editor.url=Klikni na odkaz nižšie pre otvorenie editoru
luckperms.command.editor.unable-to-communicate=Komunikácia s webovou aplikáciou bola neúspešná
luckperms.command.editor.apply-edits.success=Dáta z webovej aplikácie {0} {1} boli úspešne použité
luckperms.command.editor.apply-edits.success-summary={0} {1} a {2} {3}
luckperms.command.editor.apply-edits.success.additions=doplnky
luckperms.command.editor.apply-edits.success.additions-singular=doplnok
luckperms.command.editor.apply-edits.success.deletions=odstránenia
luckperms.command.editor.apply-edits.success.deletions-singular=odstránenie
luckperms.command.editor.apply-edits.no-changes=Vo webovom editore neboli prevedené žiadne úpravy. Dáta, ktoré sa vrátili neboli žiadnym spôsobom zmenené
luckperms.command.editor.apply-edits.unknown-type=Nie je možné použiť úpravu daného typu objektu
luckperms.command.editor.apply-edits.unable-to-read=Nebolo možné prečítať dáta pomocou daného kódu
luckperms.command.search.searching.permission=Hľadanie skupín a používateľov s {0}
luckperms.command.search.searching.inherit=Hľadanie používateľov a skupín zo skupín, ktoré zdedili z {0}
luckperms.command.search.result=Bolo nájdene {0} vstupov z {1} používateľov a {2} skupín
luckperms.command.search.result.default-notice=Poznámka\: Pri hľadaní členov výchozej skupiny nebudú zobrazení offline hráči bez ďalších oprávnení\!
luckperms.command.search.showing-users=Zobrazené záznamy používateľov
luckperms.command.search.showing-groups=Zobrazené záznamy skupín
luckperms.command.tree.start=Generovanie stromu oprávnení, prosím čakajte...
luckperms.command.tree.empty=Nepodarilo sa vygenerovať strom, neboli nájdené žiadne výsledky
luckperms.command.tree.url=URL stromu oprávnení
luckperms.command.verbose.invalid-filter={0} nie je platný filter pre podrobné sledovanie
luckperms.command.verbose.enabled=Podrobné sledovanie {0} pre kontroly zodpovedajúce {1}
luckperms.command.verbose.command-exec=Vynucujem vykonanie príkazu {1} pre {0} a hlásim všetky vykonané kontroly...
luckperms.command.verbose.off=Podrobné sledovanie {0}
luckperms.command.verbose.command-exec-complete=Vykonanie príkazu bolo dokončené
luckperms.command.verbose.command.no-checks=Vykonanie príkazu bolo dokončené, ale neboli vykonané žiadne kontroly oprávnení
luckperms.command.verbose.command.possibly-async=To môže byť spôsobené tým, že plugin vykonáva príkazy na pozadí (asynchrónne)
luckperms.command.verbose.command.try-again-manually=Môžete ešte použiť podrobné sledovanie manuálne na detekciu vykonaných kontrol
luckperms.command.verbose.enabled-recording=Podrobné sledovanie {0}, nahrávanie výsledkov...
luckperms.command.verbose.uploading=Podrobné sledovanie {0}, nahrávanie výsledkov...
luckperms.command.verbose.url=URL výsledkov podrobného sledovania
luckperms.command.verbose.enabled-term=povolené
luckperms.command.verbose.disabled-term=zakázané
luckperms.command.verbose.query-any=AKÝKOĽVEK
luckperms.command.info.running-plugin=Beží
luckperms.command.info.platform-key=Platforma
luckperms.command.info.server-brand-key=Značka servera
luckperms.command.info.server-version-key=Verzia servera
luckperms.command.info.storage-key=Úložisko
luckperms.command.info.storage-type-key=Typ
luckperms.command.info.storage.meta.split-types-key=Typy
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Pripojené
luckperms.command.info.storage.meta.file-size-key=Veľkosť súboru
luckperms.command.info.extensions-key=Rozšírenia
luckperms.command.info.messaging-key=Odosielanie správ
luckperms.command.info.instance-key=Inštancia
luckperms.command.info.static-contexts-key=Statické kontexty
luckperms.command.info.online-players-key=Online hráči
luckperms.command.info.online-players-unique={0} unikátne
luckperms.command.info.uptime-key=Uptime
luckperms.command.info.local-data-key=Mieste dáta
luckperms.command.info.local-data={0} používateľov, {1} skupín, {2} záznamov
luckperms.command.generic.create.success={0} bol úspešne vytvorený
luckperms.command.generic.create.error=Vyskytla sa chyba pri vytváraní {0}
luckperms.command.generic.create.error-already-exists={0} už existuje\!
luckperms.command.generic.delete.success={0} bol úspešne odstránený
luckperms.command.generic.delete.error=Vyskytla sa chyba pri odstraňovaní {0}
luckperms.command.generic.delete.error-doesnt-exist={0} neexistuje\!
luckperms.command.generic.rename.success={0} bol úspešne premenovaný na {1}
luckperms.command.generic.clone.success={0} bol úspešne skopírovaný na {1}
luckperms.command.generic.info.parent.title=Rodičovské skupiny
luckperms.command.generic.info.parent.temporary-title=Dočasné rodičovské skupiny
luckperms.command.generic.info.expires-in=expiruje za
luckperms.command.generic.info.inherited-from=dedené z
luckperms.command.generic.info.inherited-from-self=sám
luckperms.command.generic.show-tracks.title={0}''s Záznamy
luckperms.command.generic.show-tracks.empty={0} nie je na žiadnych záznamoch
luckperms.command.generic.clear.node-removed={0} uzly boli odstránené
luckperms.command.generic.clear.node-removed-singular={0} uzol bol odstránený
luckperms.command.generic.clear={0}''s uzly boli vyčistené v kontexte {1}
luckperms.command.generic.permission.info.title={0}''s Oprávnenia
luckperms.command.generic.permission.info.empty={0} nemá žiadne oprávnenia nastavené
luckperms.command.generic.permission.info.click-to-remove=Kliknite na odstránenie tohto uzla z {0}
luckperms.command.generic.permission.check.info.title=Informácie o oprávnení pre {0}
luckperms.command.generic.permission.check.info.directly={0} má {1} nastavené na {2} v kontexte {3}
luckperms.command.generic.permission.check.info.inherited={0} dedí {1} nastavené na {2} od {3} v kontexte {4}
luckperms.command.generic.permission.check.info.not-directly={0} nemá {1} nastavené
luckperms.command.generic.permission.check.info.not-inherited={0} nededí {1}
luckperms.command.generic.permission.check.result.title=Kontrola oprávnení pre {0}
luckperms.command.generic.permission.check.result.result-key=Výsledok
luckperms.command.generic.permission.check.result.processor-key=Procesor
luckperms.command.generic.permission.check.result.cause-key=Príčina
luckperms.command.generic.permission.check.result.context-key=Kontext
luckperms.command.generic.permission.set=Nastavené {0} na {1} pre {2} v kontexte {3}
luckperms.command.generic.permission.already-has={0} už má {1} nastavené v kontexte {2}
luckperms.command.generic.permission.set-temp=Nastavené {0} na {1} pre {2} na dobu {3} v kontexte {4}
luckperms.command.generic.permission.already-has-temp={0} už má {1} dočasne nastavené v kontexte {2}
luckperms.command.generic.permission.unset=Zrušené {0} pre {1} v kontexte {2}
luckperms.command.generic.permission.doesnt-have={0} nemá {1} nastavené v kontexte {2}
luckperms.command.generic.permission.unset-temp=Dočasné nastavenie oprávnení {0} bolo zrušené pre {1} v kontexte {2}
luckperms.command.generic.permission.subtract=Nastavené {0} na {1} pre {2} na dobu {3} v kontexte {4}, {5} menej než predtým
luckperms.command.generic.permission.doesnt-have-temp={0} nemá {1} nastavené v kontexte {2}
luckperms.command.generic.permission.clear=Oprávnenia hráča {0} boli vyčistené v kontexte {1}
luckperms.command.generic.parent.info.title=Nadradenie hráča {0}
luckperms.command.generic.parent.info.empty={0} nemá definované žiadne nadradenia
luckperms.command.generic.parent.info.click-to-remove=Klikni pre odstránenie tohoto nadriadeného z {0}
luckperms.command.generic.parent.add={0} práve dedí oprávnenia od {1} v kontexte {2}
luckperms.command.generic.parent.add-temp={0} práve dedí oprávnenia od {1} na dobu {2} v kontexte {3}
luckperms.command.generic.parent.set={0} mal svoje nadriadené skupiny zmazane, teraz dedí len od {1} v kontexte {2}
luckperms.command.generic.parent.set-track={0} mal zmazane jeho nadriadené skupiny na zázname {1}, a teraz dedí len od {2} v kontexte {3}
luckperms.command.generic.parent.remove={0} už nededí oprávnenia od {1} v kontexte {2}
luckperms.command.generic.parent.remove-temp={0} už nededí dočasné oprávnenia od {1} v kontexte {2}
luckperms.command.generic.parent.subtract={0} Zdedí oprávnenia od {1} na dobu {2} v kontexte {3}, {4} menej než predtým
luckperms.command.generic.parent.clear=Nadradenie hráča {0} boli vyčistené v kontexte {1}
luckperms.command.generic.parent.clear-track={0}''s rodičia na trati {1} boli vymazaní v kontexte {2}
luckperms.command.generic.parent.already-inherits={0} už dedí od {1} v kontexte {2}
luckperms.command.generic.parent.doesnt-inherit={0} nezdedil od {1} v kontexte {2}
luckperms.command.generic.parent.already-temp-inherits={0} už dočasne dedí od {1} v kontexte {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} nezdedil dočasne od {1} v kontexte {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}''s Prefixy
luckperms.command.generic.chat-meta.info.title-suffix={0}''s Sufixy
luckperms.command.generic.chat-meta.info.none-prefix={0} nemá žiadne prefixy
luckperms.command.generic.chat-meta.info.none-suffix={0} nemá žiadne sufixy
luckperms.command.generic.chat-meta.info.click-to-remove=Kliknite na odstránenie tohto {0} od {1}
luckperms.command.generic.chat-meta.already-has={0} už má nastavené {1} {2} s prioritou {3} v kontexte {4}
luckperms.command.generic.chat-meta.already-has-temp={0} už má dočasne nastavené {1} {2} s prioritou {3} v kontexte {4}
luckperms.command.generic.chat-meta.doesnt-have={0} nemá nastavené {1} {2} s prioritou {3} v kontexte {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} nemá dočasne nastavené {1} {2} s prioritou {3} v kontexte {4}
luckperms.command.generic.chat-meta.add={0} mal nastavené {1} {2} s prioritou {3} v kontexte {4}
luckperms.command.generic.chat-meta.add-temp={0} mal nastavené {1} {2} s prioritou {3} na dobu {4} v kontexte {5}
luckperms.command.generic.chat-meta.remove={0} mal odstránené {1} {2} s prioritou {3} v kontexte {4}
luckperms.command.generic.chat-meta.remove-bulk={0} mal odstránené všetky {1} s prioritou {2} v kontexte {3}
luckperms.command.generic.chat-meta.remove-temp={0} mal dočasne odstránené {1} {2} s prioritou {3} v kontexte {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} mal dočasne odstránené všetky {1} s prioritou {2} v kontexte {3}
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none={0} nemá žiadne meta
luckperms.command.generic.meta.info.click-to-remove=Kliknite na odstránenie tohto meta uzla od {0}
luckperms.command.generic.meta.already-has={0} už má nastavený meta kľúč {1} na {2} v kontexte {3}
luckperms.command.generic.meta.already-has-temp={0} už má dočasne nastavený meta kľúč {1} na {2} v kontexte {3}
luckperms.command.generic.meta.doesnt-have={0} nemá nastavený meta kľúč {1} v kontexte {2}
luckperms.command.generic.meta.doesnt-have-temp={0} nemá dočasne nastavený meta kľúč {1} v kontexte {2}
luckperms.command.generic.meta.set=Nastavený meta kľúč {0} na {1} pre {2} v kontexte {3}
luckperms.command.generic.meta.set-temp=Nastavený meta kľúč {0} na {1} pre {2} na dobu {3} v kontexte {4}
luckperms.command.generic.meta.unset=Odstránený meta kľúč {0} pre {1} v kontexte {2}
luckperms.command.generic.meta.unset-temp=Odstránený dočasný meta kľúč {0} pre {1} v kontexte {2}
luckperms.command.generic.meta.clear={0}''s meta zodpovedajúce typu {1} bolo vymazané v kontexte {2}
luckperms.command.generic.contextual-data.title=Kontextové dáta
luckperms.command.generic.contextual-data.mode.key=režim
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=aktívny hráč
luckperms.command.generic.contextual-data.contexts-key=Kontexty
luckperms.command.generic.contextual-data.prefix-key=Prefix
luckperms.command.generic.contextual-data.suffix-key=Sufix
luckperms.command.generic.contextual-data.primary-group-key=Hlavná skupina
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Žiadne
luckperms.command.user.info.title=Informácie o používateľovi
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=typ
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Stav
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Nebudete môcť odstrániť používateľa zo svojej hlavnej skupiny
luckperms.command.user.primarygroup.not-member={0} nebol už členom {1}, teraz ho pridávame
luckperms.command.user.primarygroup.already-has={0} už má {1} nastavenú ako svoju hlavnú skupinu
luckperms.command.user.primarygroup.warn-option=Upozornenie\: Metóda výpočtu hlavnej skupiny, ktorú používa tento server ({0}), nemusí zodpovedať tejto zmene
luckperms.command.user.primarygroup.set={0}''s hlavná skupina bola nastavená na {1}
luckperms.command.user.track.error-not-contain-group={0} ešte nie je v žiadnej skupine na {1}
luckperms.command.user.track.unsure-which-track=Nie je isté, ktorú trasu použiť, prosím, špecifikujte ju ako argument
luckperms.command.user.track.missing-group-advice=Buď vytvorte skupinu, alebo ju odstráňte z trasy a skúste to znova
luckperms.command.user.promote.added-to-first={0} nie je v žiadnej skupine na {1}, takže bol pridaný do prvej skupiny, {2} v kontexte {3}
luckperms.command.user.promote.not-on-track={0} nie je v žiadnej skupine na {1}, takže nebol povýšený
luckperms.command.user.promote.success=Povýšenie {0} na trati {1} z {2} na {3} v kontexte {4}
luckperms.command.user.promote.end-of-track=Dosiahli ste koniec trasy {0}, nemožno povýšiť {1}
luckperms.command.user.promote.next-group-deleted=Nasledujúca skupina na trase, {0}, už neexistuje
luckperms.command.user.promote.unable-to-promote=Nie je možné povýšiť používateľa
luckperms.command.user.demote.success=Zníženie {0} na trati {1} z {2} na {3} v kontexte {4}
luckperms.command.user.demote.end-of-track=Dosiahli ste koniec trasy {0}, takže {1} bol odstránený zo skupiny {2}
luckperms.command.user.demote.end-of-track-not-removed=Dosiahli ste koniec trasy {0}, ale {1} nebol odstránený z prvej skupiny
luckperms.command.user.demote.previous-group-deleted=Predchádzajúca skupina na trase, {0}, už neexistuje
luckperms.command.user.demote.unable-to-demote=Nie je možné znížiť používateľa
luckperms.command.group.list.title=Skupiny
luckperms.command.group.delete.not-default=Nemôžete odstrániť predvolenú skupinu
luckperms.command.group.info.title=Group info
luckperms.command.group.info.display-name-key=Zobrazované meno
luckperms.command.group.info.weight-key=Váha
luckperms.command.group.setweight.set=Nastaviť váhu {0} pre groupku {1}
luckperms.command.group.setdisplayname.doesnt-have={0} nemá zobrazované meno
luckperms.command.group.setdisplayname.already-has={0} už má zobrazované meno {1}
luckperms.command.group.setdisplayname.already-in-use=Zobrazované meno {0} už používa {1}
luckperms.command.group.setdisplayname.set=Zobrazované meno bolo nastavené na {0} pre skupinu {1} v kontexte {2}
luckperms.command.group.setdisplayname.removed=Zobrazované meno pre skupinu {0} v kontexte {1} bolo odstránené
luckperms.command.track.list.title=Záznamy
luckperms.command.track.path.empty=Žiadne
luckperms.command.track.info.showing-track=Zobrazujem záznam
luckperms.command.track.info.path-property=Trasa
luckperms.command.track.clear={0}''s skupiny záznamov boli vymazané
luckperms.command.track.append.success=Skupina {0} bola pridaná do záznamu {1}
luckperms.command.track.insert.success=Skupina {0} bola vložená do záznamu {1} na pozíciu {2}
luckperms.command.track.insert.error-number=Očakávalo sa číslo, ale bolo prijaté\: {0}
luckperms.command.track.insert.error-invalid-pos=Nie je možné vložiť na pozíciu {0}
luckperms.command.track.insert.error-invalid-pos-reason=neplatná pozícia
luckperms.command.track.remove.success=Skupina {0} bola odstránená zo záznamu {1}
luckperms.command.track.error-empty={0} nemôže byť použitý, pretože je prázdny alebo obsahuje len jednu skupinu
luckperms.command.track.error-multiple-groups={0} je členom viacerých skupín na tomto zázname
luckperms.command.track.error-ambiguous=Nie je možné určiť ich umiestnenie
luckperms.command.track.already-contains={0} už obsahuje {1}
luckperms.command.track.doesnt-contain={0} neobsahuje {1}
luckperms.command.log.load-error=Záznam nebolo možné načítať
luckperms.command.log.invalid-page=Neplatné číslo stránky
luckperms.command.log.invalid-page-range=Zadajte hodnotu medzi {0} a {1}
luckperms.command.log.empty=Žiadne záznamy na zobrazenie
luckperms.command.log.notify.error-console=Nie je možné prepínať oznámenia pre konzolu
luckperms.command.log.notify.enabled-term=Zapnuté
luckperms.command.log.notify.disabled-term=Vypnuté
luckperms.command.log.notify.changed-state={0} zaznamenáva výstup
luckperms.command.log.notify.already-on=Už prijímate oznámenia
luckperms.command.log.notify.already-off=Momentálne neberiete oznámenia
luckperms.command.log.notify.invalid-state=Neznámy stav. Očakáva sa {0} alebo {1}
luckperms.command.log.show.search=Zobrazujem nedávne akcie pre dopyt {0}
luckperms.command.log.show.recent=Zobrazujem nedávne akcie
luckperms.command.log.show.by=Zobrazujem nedávne akcie podľa {0}
luckperms.command.log.show.history=Zobrazujem históriu pre {0} {1}
luckperms.command.export.error-term=Chyba
luckperms.command.export.already-running=Iný proces exportu už beží
luckperms.command.export.file.already-exists=Súbor {0} už existuje
luckperms.command.export.file.not-writable=Súbor {0} nie je zapisovateľný
luckperms.command.export.file.success=Úspešne exportované do {0}
luckperms.command.export.file-unexpected-error-writing=Pri písaní do súboru došlo k neočakávanej chybe
luckperms.command.export.web.export-code=Exportovať kód
luckperms.command.export.web.import-command-description=Použite nasledujúci príkaz na import
luckperms.command.import.term=Importovať
luckperms.command.import.error-term=Chyba
luckperms.command.import.already-running=Už prebieha iný proces importu
luckperms.command.import.file.doesnt-exist=Súbor {0} neexistuje
luckperms.command.import.file.not-readable=Súbor {0} nie je čitateľný
luckperms.command.import.file.unexpected-error-reading=Pri čítaní zo súboru došlo k neočakávanej chybe
luckperms.command.import.file.correct-format=Je to správny formát?
luckperms.command.import.web.unable-to-read=Nemožno načítať údaje pomocou daného kódu
luckperms.command.import.progress.percent={0}% dokončené
luckperms.command.import.progress.operations={0}/{1} operácií dokončených
luckperms.command.import.starting=Spúšťam proces importu
luckperms.command.import.completed=SKONČENÉ
luckperms.command.import.duration=trvalo {0} sekúnd
luckperms.command.bulkupdate.must-use-console=Príkaz hromadnej aktualizácie môže byť použitý len z konzoly
luckperms.command.bulkupdate.invalid-data-type=Neplatný typ, očakávalo sa {0}
luckperms.command.bulkupdate.invalid-constraint=Neplatné obmedzenie {0}
luckperms.command.bulkupdate.invalid-constraint-format=Obmedzenia by mali byť vo formáte {0}
luckperms.command.bulkupdate.invalid-comparison=Neplatný porovnávací operátor {0}
luckperms.command.bulkupdate.invalid-comparison-format=Očakávalo sa jedno z nasledovných\: {0}
luckperms.command.bulkupdate.queued=Operácia hromadnej aktualizácie bola zaradená
luckperms.command.bulkupdate.confirm=Spustite {0} na vykonanie aktualizácie
luckperms.command.bulkupdate.unknown-id=Operácia s ID {0} neexistuje alebo vypršala
luckperms.command.bulkupdate.starting=Spúšťam hromadnú aktualizáciu
luckperms.command.bulkupdate.success=Hromadná aktualizácia bola úspešne dokončená
luckperms.command.bulkupdate.success.statistics.nodes=Celkový počet ovplyvnených uzlov
luckperms.command.bulkupdate.success.statistics.users=Celkový počet ovplyvnených používateľov
luckperms.command.bulkupdate.success.statistics.groups=Celkový počet ovplyvnených skupín
luckperms.command.bulkupdate.failure=Hromadná aktualizácia zlyhala, skontrolujte konzolu pre chyby
luckperms.command.update-task.request=Bol požiadavka na aktualizačnú úlohu, prosím čakajte
luckperms.command.update-task.complete=Úloha aktualizácie dokončená
luckperms.command.update-task.push.attempting=Teraz sa pokúšam poslať na iné servery
luckperms.command.update-task.push.complete=Iné servery boli úspešne upozornené cez {0}
luckperms.command.update-task.push.error=Chyba pri posielaní zmien na iné servery
luckperms.command.update-task.push.error-not-setup=Nie je možné poslať zmeny na iné servery, pretože nebola nakonfigurovaná služba na posielanie správ
luckperms.command.reload-config.success=Konfiguračný súbor bol načítaný
luckperms.command.reload-config.restart-note=niektoré možnosti sa aplikujú až po reštarte servera
luckperms.command.translations.searching=Hľadám dostupné preklady, prosím čakajte...
luckperms.command.translations.searching-error=Nie je možné získať zoznam dostupných prekladov
luckperms.command.translations.installed-translations=Inštalované preklady
luckperms.command.translations.available-translations=Dostupné preklady
luckperms.command.translations.percent-translated={0}% preložené
luckperms.command.translations.translations-by=od
luckperms.command.translations.installing=Inštalujem preklady, prosím čakajte...
luckperms.command.translations.download-error=Nie je možné stiahnuť preklad pre {0}
luckperms.command.translations.installing-specific=Inštalujem jazyk {0}...
luckperms.command.translations.install-complete=Inštalácia dokončená
luckperms.command.translations.download-prompt=Použite {0} na stiahnutie a inštaláciu aktuálnych verzií týchto prekladov poskytnutých komunitou
luckperms.command.translations.download-override-warning=Upozornenie\: Týmto sa prepíšu akékoľvek zmeny, ktoré ste vykonali v týchto jazykoch
luckperms.usage.user.description=Set príkazov na správu používateľov v LuckPerms. (''používateľ'' v LuckPerms je jednoducho hráč a môže sa týkať UUID alebo používateľského mena)
luckperms.usage.group.description=Set príkazov na správu skupín v LuckPerms. Skupiny sú jednoducho kolekcie priradení oprávnení, ktoré môžu byť pridelené používateľom. Nové skupiny sa vytvárajú pomocou príkazu ''creategroup''.
luckperms.usage.track.description=Set príkazov na správu záznamov v LuckPerms. Záznamy sú usporiadané kolekcie skupín, ktoré sa môžu používať na definovanie povýšení a degradácií.
luckperms.usage.log.description=Set príkazov na správu funkcie logovania v LuckPerms.
luckperms.usage.sync.description=Načíta všetky údaje zo servera do pamäte a aplikuje akékoľvek zistené zmeny.
luckperms.usage.info.description=Tlačí všeobecné informácie o aktívnej inštancii pluginu.
luckperms.usage.editor.description=Vytvára novú reláciu webového editora
luckperms.usage.editor.argument.type=typy, ktoré sa načítajú do editora. (''all'', ''users'' alebo ''groups'')
luckperms.usage.editor.argument.filter=Oprávnenie na filtrovanie používateľských záznamov
luckperms.usage.verbose.description=Ovláda podrobný systém monitorovania oprávnení pluginu.
luckperms.usage.verbose.argument.action=či povoliť/zakázať logovanie alebo nahrať zaznamenaný výstup
luckperms.usage.verbose.argument.filter=filtrovať podľa zadaného zápisu
luckperms.usage.verbose.argument.commandas=hráč/ príkaz na spustenie
luckperms.usage.tree.description=Vytvára stromový pohľad (zoradený zoznam hierarchie) všetkých oprávnení známych LuckPerms.
luckperms.usage.tree.argument.scope=korene stromu. Zadajte "." na zahrnutie všetkých oprávnení
luckperms.usage.tree.argument.player=meno online hráča, na ktorého sa má skontrolovať
luckperms.usage.search.description=Vyhľadáva všetkých používateľov/skupiny s konkrétnym oprávnením
luckperms.usage.search.argument.permission=oprávnenie, ktoré sa má hľadať
luckperms.usage.search.argument.page=stránka na zobrazenie
luckperms.usage.network-sync.description=Synchronizuje zmeny so skladom a požaduje, aby všetky ostatné servery v sieti vykonali rovnaké
luckperms.usage.import.description=Importuje dáta zo súboru (predtým vytvoreného exportu)
luckperms.usage.import.argument.file=súbor na import
luckperms.usage.import.argument.replace=nahrať existujúce dáta namiesto ich zlúčenia
luckperms.usage.import.argument.upload=nahrať dáta z predchádzajúceho exportu
luckperms.usage.export.description=Exportuje všetky dáta oprávnení do súboru "export". Môže byť opäť importovaný neskôr.
luckperms.usage.export.argument.file=súbor na export
luckperms.usage.export.argument.without-users=vynechať používateľov z exportu
luckperms.usage.export.argument.without-groups=vynechať skupiny z exportu
luckperms.usage.export.argument.upload=Nahrať všetky údaje o oprávneniach na webový editor. Môže byť opäť importovaný neskôr.
luckperms.usage.reload-config.description=Načítanie niektorých konfiguračných možností
luckperms.usage.bulk-update.description=Vykonajte hromadné zmeny vo všetkých dátach
luckperms.usage.bulk-update.argument.data-type=typ zmenených dát. ("všetky", "používateľov" alebo "skupiny")
luckperms.usage.bulk-update.argument.action=akcia, ktorá sa má vykonať na dátach. ("aktualizovať" alebo "vymazať")
luckperms.usage.bulk-update.argument.action-field=pole, na ktoré sa má konať. Vyžaduje sa iba pre "aktualizovať". ("oprávnenie", "server" alebo "svet")
luckperms.usage.bulk-update.argument.action-value=hodnota, ktorú je potrebné nahradiť. Vyžaduje sa iba pre "aktualizovať".
luckperms.usage.bulk-update.argument.constraint=požiadavky pre aktualizáciu
luckperms.usage.translations.description=Správa prekladov
luckperms.usage.translations.argument.install=podpríkaz na inštaláciu prekladov
luckperms.usage.apply-edits.description=Aplikuje zmeny oprávnení vykonané cez web editor
luckperms.usage.apply-edits.argument.code=unikátny kód pre dáta
luckperms.usage.apply-edits.argument.target=komu aplikovať dáta
luckperms.usage.create-group.description=Vytvoriť novú skupinu
luckperms.usage.create-group.argument.name=názov skupiny
luckperms.usage.create-group.argument.weight=vaha skupiny
luckperms.usage.create-group.argument.display-name=zobrazovaný názov skupiny
luckperms.usage.delete-group.description=Vymazať skupinu
luckperms.usage.delete-group.argument.name=názov skupiny
luckperms.usage.list-groups.description=Zobraziť všetky skupiny na platforme
luckperms.usage.create-track.description=Vytvoriť novú stopu
luckperms.usage.create-track.argument.name=názov stopy
luckperms.usage.delete-track.description=Vymazať stopu
luckperms.usage.delete-track.argument.name=názov stopy
luckperms.usage.list-tracks.description=Zobraziť všetky stopy na platforme
luckperms.usage.user-info.description=Zobrazuje informácie o používateľovi
luckperms.usage.user-switchprimarygroup.description=Prepnúť primárnu skupinu používateľa
luckperms.usage.user-switchprimarygroup.argument.group=skupina, na ktorú sa má prepnúť
luckperms.usage.user-promote.description=Postupuje používateľa na vyššiu stopu
luckperms.usage.user-promote.argument.track=stopa, na ktorú sa má používateľ povýšiť
luckperms.usage.user-promote.argument.context=konteksty, v ktorých sa má používateľ povýšiť
luckperms.usage.user-promote.argument.dont-add-to-first=povýšiť používateľa iba v prípade, že už je na stope
luckperms.usage.user-demote.description=Znižuje používateľa na nižšiu stopu
luckperms.usage.user-demote.argument.track=stopa, na ktorú sa má používateľ znížiť
luckperms.usage.user-demote.argument.context=konteksty, v ktorých sa má používateľ znížiť
luckperms.usage.user-demote.argument.dont-remove-from-first=zabraňuje používateľovi byť odstránený z prvej skupiny
luckperms.usage.user-clone.description=Klónuje používateľa
luckperms.usage.user-clone.argument.user=meno/uuid používateľa, na ktorého sa má klonovať
luckperms.usage.group-info.description=Zobraziť informácie o skupine
luckperms.usage.group-listmembers.description=Zobraziť používateľov/skupiny, ktoré zdedia túto skupinu
luckperms.usage.group-listmembers.argument.page=stránka na zobrazenie
luckperms.usage.group-setweight.description=Nastavte váhu skupiny
luckperms.usage.group-setweight.argument.weight=vaha, ktorú sa má nastaviť
luckperms.usage.group-set-display-name.description=Nastaviť zobrazený názov skupiny
luckperms.usage.group-set-display-name.argument.name=názov na nastavenie
luckperms.usage.group-set-display-name.argument.context=konteksty, v ktorých sa má nastaviť názov
luckperms.usage.group-rename.description=Premenovať skupinu
luckperms.usage.group-rename.argument.name=nový názov
luckperms.usage.group-clone.description=Klónovať skupinu
luckperms.usage.group-clone.argument.name=názov skupiny, na ktorú sa má klonovať
luckperms.usage.holder-editor.description=Otvára webový editor oprávnení
luckperms.usage.holder-showtracks.description=Zobraziť stopy, na ktorých sa objekt nachádza
luckperms.usage.holder-clear.description=Odstráni všetky oprávnenia, rodičov a meta informácie
luckperms.usage.holder-clear.argument.context=konteksty, na ktoré sa má filtrovať
luckperms.usage.permission.description=Upraviť oprávnenia
luckperms.usage.parent.description=Upraviť dedičstvo
luckperms.usage.meta.description=Upraviť meta hodnoty
luckperms.usage.permission-info.description=Zobraziť oprávnenia objektu
luckperms.usage.permission-info.argument.page=stránka na zobrazenie
luckperms.usage.permission-info.argument.sort-mode=spôsob zoradenia položiek
luckperms.usage.permission-set.description=Nastaviť oprávnenie pre objekt
luckperms.usage.permission-set.argument.node=názov oprávnenia na nastavenie
luckperms.usage.permission-set.argument.value=hodnota oprávnenia
luckperms.usage.permission-set.argument.context=konteksty, do ktorých sa má pridať oprávnenie
luckperms.usage.permission-unset.description=Zrušiť oprávnenie pre objekt
luckperms.usage.permission-unset.argument.node=názov oprávnenia na zrušenie
luckperms.usage.permission-unset.argument.context=konteksty, z ktorých sa má odstrániť oprávnenie
luckperms.usage.permission-settemp.description=Nastaviť dočasné oprávnenie pre objekt
luckperms.usage.permission-settemp.argument.node=názov oprávnenia na nastavenie
luckperms.usage.permission-settemp.argument.value=hodnota oprávnenia
luckperms.usage.permission-settemp.argument.duration=dĺžka trvania do vypršania oprávnenia
luckperms.usage.permission-settemp.argument.temporary-modifier=spôsob aplikácie dočasného oprávnenia
luckperms.usage.permission-settemp.argument.context=konteksty, do ktorých sa má pridať oprávnenie
luckperms.usage.permission-unsettemp.description=Zruší dočasné oprávnenie pre objekt
luckperms.usage.permission-unsettemp.argument.node=Oprávnenie node, ktoré sa má zrušiť
luckperms.usage.permission-unsettemp.argument.duration=Dĺžka, ktorá sa má odpočítať
luckperms.usage.permission-unsettemp.argument.context=Kontexty, v ktorých sa má oprávnenie odstrániť
luckperms.usage.permission-check.description=Skontroluje, či objekt má daný oprávnenie node
luckperms.usage.permission-check.argument.node=Oprávnenie node, ktoré sa má skontrolovať
luckperms.usage.permission-clear.description=Vymaže všetky oprávnenia
luckperms.usage.permission-clear.argument.context=Kontexty, podľa ktorých sa má filtrovať
luckperms.usage.parent-info.description=Zoznamuje skupiny, z ktorých tento objekt dedí
luckperms.usage.parent-info.argument.page=Stránka, ktorú si chcete pozrieť
luckperms.usage.parent-info.argument.sort-mode=Spôsob zoradenia záznamov
luckperms.usage.parent-set.description=Odstráni všetky ostatné skupiny, z ktorých objekt už dedí, a pridá ho do danej skupiny
luckperms.usage.parent-set.argument.group=Skupina, do ktorej sa má nastaviť
luckperms.usage.parent-set.argument.context=Kontexty, v ktorých sa má nastaviť skupina
luckperms.usage.parent-add.description=Pridá ďalšiu skupinu, z ktorej objekt bude dedičom oprávnení
luckperms.usage.parent-add.argument.group=Skupina, z ktorej má objekt dediť
luckperms.usage.parent-add.argument.context=Kontexty, v ktorých sa má dedičstvo nastaviť
luckperms.usage.parent-remove.description=Odstráni predchádzajúce nastavenie dedičstva
luckperms.usage.parent-remove.argument.group=Skupina, ktorá sa má odstrániť
luckperms.usage.parent-remove.argument.context=Kontexty, v ktorých sa má skupina odstrániť
luckperms.usage.parent-set-track.description=Odstráni všetky ostatné skupiny, z ktorých objekt už dedí na danom trakte, a pridá ich do danej skupiny
luckperms.usage.parent-set-track.argument.track=Trak, ktorý sa má nastaviť
luckperms.usage.parent-set-track.argument.group=Skupina, do ktorej sa má nastaviť, alebo číslo súvisiace s pozíciou skupiny na danom trakte
luckperms.usage.parent-set-track.argument.context=Kontexty, v ktorých sa má nastaviť skupina
luckperms.usage.parent-add-temp.description=Pridá ďalšiu skupinu, z ktorej objekt bude dočasne dedičom oprávnení
luckperms.usage.parent-add-temp.argument.group=Skupina, z ktorej má objekt dediť
luckperms.usage.parent-add-temp.argument.duration=Dĺžka členstva v skupine
luckperms.usage.parent-add-temp.argument.temporary-modifier=Spôsob, akým sa má dočasné oprávnenie aplikovať
luckperms.usage.parent-add-temp.argument.context=Kontexty, v ktorých sa má dedičstvo nastaviť
luckperms.usage.parent-remove-temp.description=Odstráni predchádzajúce dočasné nastavenie dedičstva
luckperms.usage.parent-remove-temp.argument.group=Skupina, ktorá sa má odstrániť
luckperms.usage.parent-remove-temp.argument.duration=Dĺžka, ktorá sa má odpočítať
luckperms.usage.parent-remove-temp.argument.context=Kontexty, v ktorých sa má skupina odstrániť
luckperms.usage.parent-clear.description=Vymaže všetkých rodičov
luckperms.usage.parent-clear.argument.context=Kontexty, podľa ktorých sa má filtrovať
luckperms.usage.parent-clear-track.description=Vymaže všetkých rodičov na danom trakte
luckperms.usage.parent-clear-track.argument.track=Trak, na ktorom sa má odstrániť
luckperms.usage.parent-clear-track.argument.context=Kontexty, podľa ktorých sa má filtrovať
luckperms.usage.meta-info.description=Zobrazuje všetky chatové meta
luckperms.usage.meta-set.description=Nastaví meta hodnotu
luckperms.usage.meta-set.argument.key=Kľúč, ktorý sa má nastaviť
luckperms.usage.meta-set.argument.value=Hodnota, ktorá sa má nastaviť
luckperms.usage.meta-set.argument.context=Kontexty, v ktorých sa má pridať meta pár
luckperms.usage.meta-unset.description=Zruší meta hodnotu
luckperms.usage.meta-unset.argument.key=Kľúč, ktorý sa má zrušiť
luckperms.usage.meta-unset.argument.context=Kontexty, v ktorých sa má zrušiť meta pár
luckperms.usage.meta-settemp.description=Nastaví meta hodnotu dočasne
luckperms.usage.meta-settemp.argument.key=Kľúč, ktorý sa má nastaviť
luckperms.usage.meta-settemp.argument.value=Hodnota, ktorá sa má nastaviť
luckperms.usage.meta-settemp.argument.duration=Dĺžka do vypršania meta hodnoty
luckperms.usage.meta-settemp.argument.context=Kontexty, v ktorých sa má pridať meta pár
luckperms.usage.meta-unsettemp.description=Zruší dočasnú meta hodnotu
luckperms.usage.meta-unsettemp.argument.key=Kľúč, ktorý sa má zrušiť
luckperms.usage.meta-unsettemp.argument.context=Kontexty, v ktorých sa má zrušiť meta pár
luckperms.usage.meta-addprefix.description=Pridá prefix
luckperms.usage.meta-addprefix.argument.priority=Priorita pridania prefixu
luckperms.usage.meta-addprefix.argument.prefix=Prefixová reťazec
luckperms.usage.meta-addprefix.argument.context=Kontexty, v ktorých sa má pridať prefix
luckperms.usage.meta-addsuffix.description=Pridá suffix
luckperms.usage.meta-addsuffix.argument.priority=Priorita pridania suffixu
luckperms.usage.meta-addsuffix.argument.suffix=Suffixová reťazec
luckperms.usage.meta-addsuffix.argument.context=Kontexty, v ktorých sa má pridať suffix
luckperms.usage.meta-setprefix.description=Nastaví prefix
luckperms.usage.meta-setprefix.argument.priority=Priorita nastavenia prefixu
luckperms.usage.meta-setprefix.argument.prefix=Prefixová reťazec
luckperms.usage.meta-setprefix.argument.context=Kontexty, v ktorých sa má nastaviť prefix
luckperms.usage.meta-setsuffix.description=Nastaví suffix
luckperms.usage.meta-setsuffix.argument.priority=Priorita nastavenia suffixu
luckperms.usage.meta-setsuffix.argument.suffix=Suffixová reťazec
luckperms.usage.meta-setsuffix.argument.context=Kontexty, v ktorých sa má nastaviť suffix
luckperms.usage.meta-removeprefix.description=Odstráni prefix
luckperms.usage.meta-removeprefix.argument.priority=Priorita odstránenia prefixu
luckperms.usage.meta-removeprefix.argument.prefix=Prefixová reťazec
luckperms.usage.meta-removeprefix.argument.context=Kontexty, v ktorých sa má odstrániť prefix
luckperms.usage.meta-removesuffix.description=Odstráni suffix
luckperms.usage.meta-removesuffix.argument.priority=Priorita odstránenia suffixu
luckperms.usage.meta-removesuffix.argument.suffix=Suffixová reťazec
luckperms.usage.meta-removesuffix.argument.context=Kontexty, v ktorých sa má odstrániť suffix
luckperms.usage.meta-addtemp-prefix.description=Pridá prefix dočasne
luckperms.usage.meta-addtemp-prefix.argument.priority=Priorita pridania prefixu
luckperms.usage.meta-addtemp-prefix.argument.prefix=Prefixová reťazec
luckperms.usage.meta-addtemp-prefix.argument.duration=Dĺžka, do kedy má prefix vypršať
luckperms.usage.meta-addtemp-prefix.argument.context=Kontexty, v ktorých sa má pridať prefix
luckperms.usage.meta-addtemp-suffix.description=Pridá suffix dočasne
luckperms.usage.meta-addtemp-suffix.argument.priority=Priorita pridania suffixu
luckperms.usage.meta-addtemp-suffix.argument.suffix=Suffixová reťazec
luckperms.usage.meta-addtemp-suffix.argument.duration=Dĺžka, do kedy má suffix vypršať
luckperms.usage.meta-addtemp-suffix.argument.context=Kontexty, v ktorých sa má pridať suffix
luckperms.usage.meta-settemp-prefix.description=Nastaví prefix dočasne
luckperms.usage.meta-settemp-prefix.argument.priority=Priorita nastavenia prefixu
luckperms.usage.meta-settemp-prefix.argument.prefix=Prefixová reťazec
luckperms.usage.meta-settemp-prefix.argument.duration=Dĺžka, do kedy má prefix vypršať
luckperms.usage.meta-settemp-prefix.argument.context=Kontexty, v ktorých sa má nastaviť prefix
luckperms.usage.meta-settemp-suffix.description=Nastaví suffix dočasne
luckperms.usage.meta-settemp-suffix.argument.priority=Priorita nastavenia suffixu
luckperms.usage.meta-settemp-suffix.argument.suffix=Suffixová reťazec
luckperms.usage.meta-settemp-suffix.argument.duration=Dĺžka, do kedy má suffix vypršať
luckperms.usage.meta-settemp-suffix.argument.context=Kontexty, v ktorých sa má nastaviť suffix
luckperms.usage.meta-removetemp-prefix.description=Odstráni dočasný prefix
luckperms.usage.meta-removetemp-prefix.argument.priority=Priorita na odstránenie prefixu
luckperms.usage.meta-removetemp-prefix.argument.prefix=Prefixová reťazec
luckperms.usage.meta-removetemp-prefix.argument.context=Kontexty, v ktorých sa má odstrániť prefix
luckperms.usage.meta-removetemp-suffix.description=Odstráni dočasný suffix
luckperms.usage.meta-removetemp-suffix.argument.priority=Priorita na odstránenie suffixu
luckperms.usage.meta-removetemp-suffix.argument.suffix=Suffixová reťazec
luckperms.usage.meta-removetemp-suffix.argument.context=Kontexty, v ktorých sa má odstrániť suffix
luckperms.usage.meta-clear.description=Vymaže všetky meta
luckperms.usage.meta-clear.argument.type=Typ meta, ktorý sa má odstrániť
luckperms.usage.meta-clear.argument.context=Kontexty na filtrovanie
luckperms.usage.track-info.description=Zobrazuje všetky skupiny a ich pozície na trakte
luckperms.usage.track-editor.description=Otvára editor práv na webe
luckperms.usage.track-append.description=Pridá skupinu na koniec traktu
luckperms.usage.track-append.argument.group=Skupina, ktorú chcete pridať
luckperms.usage.track-insert.description=Vloží skupinu na danú pozíciu na trakte
luckperms.usage.track-insert.argument.group=Skupina, ktorú chcete vložiť
luckperms.usage.track-insert.argument.position=Pozícia, na ktorú chcete skupinu vložiť (prvá pozícia na trakte je 1)
luckperms.usage.track-remove.description=Odstráni danú skupinu z traktu
luckperms.usage.track-remove.argument.group=Skupina, ktorú chcete odstrániť
luckperms.usage.track-clear.description=Vymaže skupiny na trakte
luckperms.usage.track-rename.description=Premenuje trak
luckperms.usage.track-rename.argument.name=Nový názov
luckperms.usage.track-clone.description=Skopíruje trak
luckperms.usage.track-clone.argument.name=Názov traktu, ktorý sa má skopírovať
luckperms.usage.log-recent.description=Zobraziť nedávne akcie
luckperms.usage.log-recent.argument.user=Meno/UUID používateľa na filtrovanie
luckperms.usage.log-recent.argument.page=Číslo stránky, ktorú chcete zobraziť
luckperms.usage.log-search.description=Hľadá záznam v logu
luckperms.usage.log-search.argument.query=dotaz podľa ktorého sa má vyhľadávať
luckperms.usage.log-search.argument.page=Číslo stránky na zobrazenie
luckperms.usage.log-notify.description=Prepnúť notifikácie logu
luckperms.usage.log-notify.argument.toggle=Ak chcete povoliť alebo zakázať
luckperms.usage.log-user-history.description=Zobraziť históriu používateľa
luckperms.usage.log-user-history.argument.user=UUID/meno používateľa
luckperms.usage.log-user-history.argument.page=Číslo stránky na zobrazenie
luckperms.usage.log-group-history.description=Zobraziť históriu skupiny
luckperms.usage.log-group-history.argument.group=Názov skupiny
luckperms.usage.log-group-history.argument.page=Číslo stránky na zobrazenie
luckperms.usage.log-track-history.description=Zobraziť históriu traktu
luckperms.usage.log-track-history.argument.track=Názov traktu
luckperms.usage.log-track-history.argument.page=Číslo stránky na zobrazenie
luckperms.usage.sponge.description=Upraviť extra údaje Sponge
luckperms.usage.sponge.argument.collection=Kolekcia na dotazovanie
luckperms.usage.sponge.argument.subject=Subjekt na úpravu
luckperms.usage.sponge-permission-info.description=Zobraziť informácie o oprávneniach subjektu
luckperms.usage.sponge-permission-info.argument.contexts=Kontexty na filtrovanie
luckperms.usage.sponge-permission-set.description=Uloží oprávnenie pre subjekt
luckperms.usage.sponge-permission-set.argument.node=Oprávnenie, ktoré sa má nastaviť
luckperms.usage.sponge-permission-set.argument.tristate=Hodnota, na ktorú sa oprávnenie nastaví
luckperms.usage.sponge-permission-set.argument.contexts=Kontexty na nastavenie oprávnenia
luckperms.usage.sponge-permission-clear.description=Vymaže oprávnenia subjektu
luckperms.usage.sponge-permission-clear.argument.contexts=Kontexty na vymazanie oprávnení
luckperms.usage.sponge-parent-info.description=Zobraziť informácie o rodičoch subjektu
luckperms.usage.sponge-parent-info.argument.contexts=Kontexty na filtrovanie
luckperms.usage.sponge-parent-add.description=Pridá rodiča k subjektu
luckperms.usage.sponge-parent-add.argument.collection=Kolekcia subjektov, kde je rodič
luckperms.usage.sponge-parent-add.argument.subject=Meno rodičovského subjektu
luckperms.usage.sponge-parent-add.argument.contexts=Kontexty, kde sa pridá rodič
luckperms.usage.sponge-parent-remove.description=Odstráni rodiča zo subjektu
luckperms.usage.sponge-parent-remove.argument.collection=Kolekcia subjektov, kde je rodič
luckperms.usage.sponge-parent-remove.argument.subject=Meno rodičovského subjektu
luckperms.usage.sponge-parent-remove.argument.contexts=Kontexty, kde sa odstráni rodič
luckperms.usage.sponge-parent-clear.description=Vymaže rodičov subjektu
luckperms.usage.sponge-parent-clear.argument.contexts=Kontexty, kde sa vymažú rodičia
luckperms.usage.sponge-option-info.description=Zobraziť informácie o možnostiach subjektu
luckperms.usage.sponge-option-info.argument.contexts=Kontexty na filtrovanie
luckperms.usage.sponge-option-set.description=Nastaví možnosť pre subjekt
luckperms.usage.sponge-option-set.argument.key=Kľúč na nastavenie
luckperms.usage.sponge-option-set.argument.value=Hodnota, na ktorú sa kľúč nastaví
luckperms.usage.sponge-option-set.argument.contexts=Kontexty na nastavenie možnosti
luckperms.usage.sponge-option-unset.description=Odstráni možnosť pre subjekt
luckperms.usage.sponge-option-unset.argument.key=Kľúč, ktorý sa má odstrániť
luckperms.usage.sponge-option-unset.argument.contexts=Kontexty na odstránenie kľúča
luckperms.usage.sponge-option-clear.description=Vymaže možnosti subjektu
luckperms.usage.sponge-option-clear.argument.contexts=Kontexty na vymazanie možností
