luckperms.logs.actionlog-prefix=PROTOKOL
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTOVAT
luckperms.commandsystem.available-commands=Použijte {0} pro zobrazení dostupných příkazů
luckperms.commandsystem.command-not-recognised=Příkaz nebyl rozpoznán
luckperms.commandsystem.no-permission=Nemáte oprávnění k použití tohoto příkazu\!
luckperms.commandsystem.no-permission-subcommands=Nemáte oprávnění k použití jakýchkoli podpříkazů
luckperms.commandsystem.already-executing-command=Právě probíhá jiný příkaz, čeká se na jeho dokončení...
luckperms.commandsystem.usage.sub-commands-header=Podpříkazy
luckperms.commandsystem.usage.usage-header=Použití příkazu
luckperms.commandsystem.usage.arguments-header=Argumenty
luckperms.first-time.no-permissions-setup=<PERSON><PERSON><PERSON><PERSON> to, že zatím nebyla nastavena žádná oprávnění\!
luckperms.first-time.use-console-to-give-access=P<PERSON>ed tím, než budete moct použít jakýkoli z příkazů LuckPerms ve hře, si musíte dát pomocí konzole přístup
luckperms.first-time.console-command-prompt=Otevřete konzoli a spusťte
luckperms.first-time.next-step=Po dokončení můžete začít nastavovat svá oprávnění a skupiny
luckperms.first-time.wiki-prompt=Nevíte, kde začít? Mrkněte sem\: {0}
luckperms.login.try-again=Zkuste to znovu později
luckperms.login.loading-database-error=Při načítání dat oprávnění se vyskytla chyba databáze
luckperms.login.server-admin-check-console-errors=Pokud jste správce serveru, podívejte se do konzole kůli případným chybám
luckperms.login.server-admin-check-console-info=Zkontrolujte konzoli serveru pro více informací
luckperms.login.data-not-loaded-at-pre=Data oprávnění vašeho uživatele nebyla načtena během fáze předpřihlášení
luckperms.login.unable-to-continue=nelze pokračovat
luckperms.login.craftbukkit-offline-mode-error=toto je pravděpodobně způsobeno konfliktem mezi CraftBukkitem a nastavením online-mode
luckperms.login.unexpected-error=Při nastavování dat oprávnění se vyskytla neočekávaná chyba
luckperms.opsystem.disabled=Systém vanilla OP je na tomto serveru zakázán
luckperms.opsystem.sponge-warning=Status operátora serveru nemá žádný vliv na kontroly nastavení Sponge při nainstalovaném pluginu na oprávnění, musíte upravit uživatelská data napřímo
luckperms.duration.unit.years.plural={0} let
luckperms.duration.unit.years.singular={0} rok
luckperms.duration.unit.years.short={0}r
luckperms.duration.unit.months.plural={0} měsíců
luckperms.duration.unit.months.singular={0} měsíc
luckperms.duration.unit.months.short={0}m
luckperms.duration.unit.weeks.plural={0} týdnů
luckperms.duration.unit.weeks.singular={0} týden
luckperms.duration.unit.weeks.short={0}t
luckperms.duration.unit.days.plural={0} dnů
luckperms.duration.unit.days.singular={0} den
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} hodin
luckperms.duration.unit.hours.singular={0} hodina
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minut
luckperms.duration.unit.minutes.singular={0} minuta
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} sekund
luckperms.duration.unit.seconds.singular={0} sekunda
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since=Před {0}
luckperms.command.misc.invalid-code=Neplatný kód
luckperms.command.misc.response-code-key=kód odpovědi
luckperms.command.misc.error-message-key=zpráva
luckperms.command.misc.bytebin-unable-to-communicate=Komunikace s bytebinem byla neúspěšná
luckperms.command.misc.webapp-unable-to-communicate=Komunikace s webovou aplikací byla neúspěšná
luckperms.command.misc.check-console-for-errors=Zkontrolujte konzoli kvůli případným chybám
luckperms.command.misc.file-must-be-in-data=Soubor {0} musí být přímo podřazený adresáři dat
luckperms.command.misc.wait-to-finish=Počkejte prosím na dokončení a zkuste to znovu
luckperms.command.misc.invalid-priority=Neplatná priorita {0}
luckperms.command.misc.expected-number=Očekáváno číslo
luckperms.command.misc.date-parse-error=Nelze zpracovat datum {0}
luckperms.command.misc.date-in-past-error=Nelze nastavit datum v minulosti\!
luckperms.command.misc.page=strana {0} z {1}
luckperms.command.misc.page-entries={0} vstupů
luckperms.command.misc.none=Žádné
luckperms.command.misc.loading.error.unexpected=Došlo k nečekané chybě
luckperms.command.misc.loading.error.user=Uživatel se nenačetl
luckperms.command.misc.loading.error.user-specific=Nelze načíst cílového uživatele {0}
luckperms.command.misc.loading.error.user-not-found=Uživatel {0} nebyl nalezen
luckperms.command.misc.loading.error.user-save-error=Při ukládání uživatelských dat {0} došlo k chybě
luckperms.command.misc.loading.error.user-not-online=Uživatel {0} není online
luckperms.command.misc.loading.error.user-invalid={0} není platné uživatelské jméno/UUID
luckperms.command.misc.loading.error.user-not-uuid=Cílový uživatel {0} není platné UUID
luckperms.command.misc.loading.error.group=Skupina se nenačetla
luckperms.command.misc.loading.error.all-groups=Nepodařilo se načíst všechny skupiny
luckperms.command.misc.loading.error.group-not-found=Skupina s názvem {0} nenalezena
luckperms.command.misc.loading.error.group-save-error=Při ukládání dat skupiny {0} došlo k chybě
luckperms.command.misc.loading.error.group-invalid={0} není platný název skupiny
luckperms.command.misc.loading.error.track=Trasa se nenačetla
luckperms.command.misc.loading.error.all-tracks=Nepodařilo se načíst všechny trasy
luckperms.command.misc.loading.error.track-not-found=Trasa s názvem {0} nenalezena
luckperms.command.misc.loading.error.track-save-error=Při ukládání dat trasy {0} došlo k chybě
luckperms.command.misc.loading.error.track-invalid={0} není platný název trasy
luckperms.command.editor.no-match=Nepodařilo se otevřít editor, žádné objekty neodpovídají požadovanému typu
luckperms.command.editor.start=Příprava nové relace editoru, čekejte prosím...
luckperms.command.editor.url=Klikněte na odkaz níže pro otevření editoru
luckperms.command.editor.unable-to-communicate=Komunikace s editorem byla neúspěšná
luckperms.command.editor.apply-edits.success=Data webového editoru byla úspěšně použita na {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} a {2} {3}
luckperms.command.editor.apply-edits.success.additions=doplňky
luckperms.command.editor.apply-edits.success.additions-singular=doplněk
luckperms.command.editor.apply-edits.success.deletions=odstranění
luckperms.command.editor.apply-edits.success.deletions-singular=odstranění
luckperms.command.editor.apply-edits.no-changes=Ve webovém editoru nebyly provedeny žádné úpravy, vrácená data neobsahovala žádné změny
luckperms.command.editor.apply-edits.unknown-type=Nelze použít úpravu daného typu objektu
luckperms.command.editor.apply-edits.unable-to-read=Nelze přečíst data pomocí daného kódu
luckperms.command.search.searching.permission=Hledání uživatelů a skupin s {0}
luckperms.command.search.searching.inherit=Hledání uživatelů a skupin dědících z {0}
luckperms.command.search.result=Nalezeno {0} vstupů od {1} uživatelů a {2} skupin
luckperms.command.search.result.default-notice=Poznámka\: při hledání členů s výchozí skupinou nebudou zobrazeni offline hráči s žádnými jinými oprávněními\!
luckperms.command.search.showing-users=Zobrazování uživatelských vstupů
luckperms.command.search.showing-groups=Zobrazují se položky skupiny
luckperms.command.tree.start=Generuji strom oprávnění, čekejte prosím...
luckperms.command.tree.empty=Nelze vygenerovat strom, žádné výsledky nebyly nalezeny
luckperms.command.tree.url=URL stromu oprávnění
luckperms.command.verbose.invalid-filter={0} není platný podrobný filtr
luckperms.command.verbose.enabled=Podrobné protokolování {0} pro kontroly odpovídající {1}
luckperms.command.verbose.command-exec=Nutím {0} k vykonání příkazu {1} a nahlašuji všechny provedené kontroly...
luckperms.command.verbose.off=Podrobné protokolování {0}
luckperms.command.verbose.command-exec-complete=Příkaz byl úspěšně vykonán
luckperms.command.verbose.command.no-checks=Vykonání příkazu dokončeno, ale nebyly provedeny žádné kontroly oprávnění
luckperms.command.verbose.command.possibly-async=Může to být, protože plugin provádí příkazy na pozadí (asynchronně)
luckperms.command.verbose.command.try-again-manually=Stále můžete ručně použít verbose k detekci kontrol, jako je tato
luckperms.command.verbose.enabled-recording=Podrobné zaznamenávání {0} pro kontroly odpovídající {1}
luckperms.command.verbose.uploading=Podrobné protokolování {0}, nahrávám výsledky...
luckperms.command.verbose.url=URL podrobných výsledků
luckperms.command.verbose.enabled-term=povoleno
luckperms.command.verbose.disabled-term=zakázáno
luckperms.command.verbose.query-any=COKOLI
luckperms.command.info.running-plugin=Používáme
luckperms.command.info.platform-key=Platforma
luckperms.command.info.server-brand-key=Značka serveru
luckperms.command.info.server-version-key=Verze serveru
luckperms.command.info.storage-key=Úložiště
luckperms.command.info.storage-type-key=Typ
luckperms.command.info.storage.meta.split-types-key=Typy
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Připojeno
luckperms.command.info.storage.meta.file-size-key=Velikost souboru
luckperms.command.info.extensions-key=Rozšíření
luckperms.command.info.messaging-key=Zasílání zpráv
luckperms.command.info.instance-key=Instance
luckperms.command.info.static-contexts-key=Statické kontexty
luckperms.command.info.online-players-key=Online hráči
luckperms.command.info.online-players-unique={0} unikátní
luckperms.command.info.uptime-key=Doba provozu
luckperms.command.info.local-data-key=Místní data
luckperms.command.info.local-data={0} uživatelů, {1} skupin, {2} tras
luckperms.command.generic.create.success={0} byl úspěšně vytvořen
luckperms.command.generic.create.error=Při vytváření {0} se vyskytla chyba
luckperms.command.generic.create.error-already-exists={0} již existuje\!
luckperms.command.generic.delete.success={0} byl úspěšně odebrán
luckperms.command.generic.delete.error=Při odstraňování {0} se vyskytla chyba
luckperms.command.generic.delete.error-doesnt-exist={0} neexistuje\!
luckperms.command.generic.rename.success={0} byl úspěšně přejmenován na {1}
luckperms.command.generic.clone.success={0} byl úspěšně zduplikován na {1}
luckperms.command.generic.info.parent.title=Nadřazené skupiny
luckperms.command.generic.info.parent.temporary-title=Dočasné nadřazené skupiny
luckperms.command.generic.info.expires-in=vyprší za
luckperms.command.generic.info.inherited-from=zděděno z
luckperms.command.generic.info.inherited-from-self=sám
luckperms.command.generic.show-tracks.title=Trasy hráče {0}
luckperms.command.generic.show-tracks.empty={0} není na žádných trasách
luckperms.command.generic.clear.node-removed={0} uzlů bylo odebráno
luckperms.command.generic.clear.node-removed-singular={0} uzel byl odebrán
luckperms.command.generic.clear=Uzly hráče {0} byly vyčištěny v kontextu {1}
luckperms.command.generic.permission.info.title=Oprávnění hráče {0}
luckperms.command.generic.permission.info.empty={0} nemá nastavena žádná oprávnění
luckperms.command.generic.permission.info.click-to-remove=Klikněte pro odebrání tohoto uzlu z {0}
luckperms.command.generic.permission.check.info.title=Informace o oprávnění pro {0}
luckperms.command.generic.permission.check.info.directly={0} má {1} nastaveno na {2} v kontextu {3}
luckperms.command.generic.permission.check.info.inherited={0} dědí {1} nastavené na {2} z {3} v kontextu {4}
luckperms.command.generic.permission.check.info.not-directly={0} nemá nastaveno {1}
luckperms.command.generic.permission.check.info.not-inherited={0} nedědí {1}
luckperms.command.generic.permission.check.result.title=Kontrola oprávnění pro {0}
luckperms.command.generic.permission.check.result.result-key=Výsledek
luckperms.command.generic.permission.check.result.processor-key=Zpracovatel
luckperms.command.generic.permission.check.result.cause-key=Příčina
luckperms.command.generic.permission.check.result.context-key=Kontext
luckperms.command.generic.permission.set=Nastavit {0} na {1} pro hráče {2} v kontextu {3}
luckperms.command.generic.permission.already-has={0} již má {1} nastaveno v kontextu {2}
luckperms.command.generic.permission.set-temp=Nastavit {0} na {1} pro hráče {2} s dobou trvání {3} v kontextu {4}
luckperms.command.generic.permission.already-has-temp={0} již má {1} dočasně nastaveno v kontextu {2}
luckperms.command.generic.permission.unset=Zrušeno oprávnění {0} hráči {1} v kontextu {2}
luckperms.command.generic.permission.doesnt-have={0} nemá {1} nastaveno v kontextu {2}
luckperms.command.generic.permission.unset-temp=Zrušeno dočasné oprávnění {0} hráči {1} v kontextu {2}
luckperms.command.generic.permission.subtract=Nastavit {0} na {1} pro hráče {2} s délkou trvání {3} v kontextu {4}, {5} méně než předtím
luckperms.command.generic.permission.doesnt-have-temp={0} nemá {1} dočasně nastaveno v kontextu {2}
luckperms.command.generic.permission.clear=Oprávnění hráče {0} byla vyčištěna v kontextu {1}
luckperms.command.generic.parent.info.title=Nadřízení hráče {0}
luckperms.command.generic.parent.info.empty={0} nemá definována žádná nadřízení
luckperms.command.generic.parent.info.click-to-remove=Klikněte pro odebrání tohoto nadřízeného z {0}
luckperms.command.generic.parent.add={0} nyní dědí oprávnění od {1} v kontextu {2}
luckperms.command.generic.parent.add-temp={0} nyní dědí oprávnění od {1} po dobu {2} v kontextu {3}
luckperms.command.generic.parent.set={0} měl smazány jeho nadřízené skupiny, a nyní pouze dědí od {1} v kontextu {2}
luckperms.command.generic.parent.set-track={0} měl smazány jeho nadřízené skupiny na trase {1}, a nyní pouze dědí od {2} v kontextu {3}
luckperms.command.generic.parent.remove={0} již nedědí oprávnění od {1} v kontextu {2}
luckperms.command.generic.parent.remove-temp={0} již dočasně nedědí oprávnění od {1} v kontextu {2}
luckperms.command.generic.parent.subtract={0} bude dědit oprávnění od {1} po dobu {2} v kontextu {3}, {4} méně než předtím
luckperms.command.generic.parent.clear=Nadřízení hráče {0} byli vyčištěni v kontextu {1}
luckperms.command.generic.parent.clear-track=Nadřízení hráče {0} na trase {1} byli vyčišteni v kontextu {2}
luckperms.command.generic.parent.already-inherits={0} již dědí od {1} v kontextu {2}
luckperms.command.generic.parent.doesnt-inherit={0} nedědí od {1} v kontextu {2}
luckperms.command.generic.parent.already-temp-inherits={0} již dočasně dědí od {1} v kontextu {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} nedědí dočasně od {1} v kontextu {2}
luckperms.command.generic.chat-meta.info.title-prefix=Prefixy hráče/skupiny {0}
luckperms.command.generic.chat-meta.info.title-suffix=Suffixy hráče/skupiny {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} nemá žádné prefixy
luckperms.command.generic.chat-meta.info.none-suffix={0} nemá žádné suffixy
luckperms.command.generic.chat-meta.info.click-to-remove=Klikněte pro odebrání tohoto {0} z {1}
luckperms.command.generic.chat-meta.already-has={0} již má {1} {2} nastaveno jako prioritu {3} v kontextu {4}
luckperms.command.generic.chat-meta.already-has-temp={0} již má {1} {2} nastaveno dočasně jako prioritu {3} v kontextu {4}
luckperms.command.generic.chat-meta.doesnt-have={0} nemá {1} {2} nastaveno jako prioritu {3} v kontextu {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} nemá {1} {2} nastaveno dočasně jako prioritu {3} v kontextu {4}
luckperms.command.generic.chat-meta.add={0} měl {1} {2} nastaveno jako prioritu {3} v kontextu {4}
luckperms.command.generic.chat-meta.add-temp={0} měl {1} {2} nastaveno jako prioritu {3} po dobu {4} v kontextu {5}
luckperms.command.generic.chat-meta.remove={0} měl {1} {2} jako prioritu {3} odebrán v kontextu {4}
luckperms.command.generic.chat-meta.remove-bulk={0} měl všechny {1} jako prioritu {2} odebrán v kontextu {3}
luckperms.command.generic.chat-meta.remove-temp={0} měl dočasně {1} {2} jako prioritu {3} odebrán v kontextu {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} měl vše dočasně {1} jako prioritu {2} odebráno v kontextu {3}
luckperms.command.generic.meta.info.title=Meta hráče/skupiny {0}
luckperms.command.generic.meta.info.none={0} nemá žádná meta
luckperms.command.generic.meta.info.click-to-remove=Klikněte pro odebrání meta hráči/skupině {0}
luckperms.command.generic.meta.already-has={0} již má meta klíč {1} nastavený na {2} v kontextu {3}
luckperms.command.generic.meta.already-has-temp={0} již má meta klíč {1} dočasně nastavený na {2} v kontextu {3}
luckperms.command.generic.meta.doesnt-have={0} nemá meta klíč {1} nastavený v kontextu {2}
luckperms.command.generic.meta.doesnt-have-temp={0} nemá meta klíč {1} dočasně nastavený v kontextu {2}
luckperms.command.generic.meta.set=Nastavit meta klíř {0} na {1} hráči/skupině {2} v kontextu {3}
luckperms.command.generic.meta.set-temp=Nastavit meta klíč {0} na {1} pro hráče {2} s dobou trvání {3} v kontextu {4}
luckperms.command.generic.meta.unset=Zrušen meta klíč {0} hráči {1} v kontextu {2}
luckperms.command.generic.meta.unset-temp=Zrušen dočasný meta klíč{0} hráči {1} v kontextu {2}
luckperms.command.generic.meta.clear=Meta hráče/skupiny {0} shodující se s typem {1} byly vyčištěny v kontextu {2}
luckperms.command.generic.contextual-data.title=Kontextová data
luckperms.command.generic.contextual-data.mode.key=režim
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=aktivní hráč
luckperms.command.generic.contextual-data.contexts-key=Kontexty
luckperms.command.generic.contextual-data.prefix-key=Prefix
luckperms.command.generic.contextual-data.suffix-key=Suffix
luckperms.command.generic.contextual-data.primary-group-key=Primární skupina
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Žádný
luckperms.command.user.info.title=Údaje uživatele
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=typ
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Stav
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Nelze odebrat uživatele z jeho primární skupiny
luckperms.command.user.primarygroup.not-member={0} ještě nebyl členem {1}, přidávání
luckperms.command.user.primarygroup.already-has={0} již má {1} nastaveno jako primární skupinu
luckperms.command.user.primarygroup.warn-option=Varování\: metoda výpočtu primární skupiny používaná tímto serverem ({0}) nemusí odrážet tuto změnu
luckperms.command.user.primarygroup.set=Primární skupina hráče {0} nastavena na {1}
luckperms.command.user.track.error-not-contain-group={0} není v žádných skupinách na {1}
luckperms.command.user.track.unsure-which-track=Nejsme si jisti, kterou trasu použít, zadejte ji prosím jako argument
luckperms.command.user.track.missing-group-advice=Buď vytvořte skupinu, nebo ji odeberte z trasy a zkuste to znovu
luckperms.command.user.promote.added-to-first={0} není v žádných skupinách na {1}, takže byli přidáni do první skupiny, {2} v kontextu {3}
luckperms.command.user.promote.not-on-track={0} není v žádných skupinách na {1}, takže nebyl povýšen
luckperms.command.user.promote.success=Povyšuji {0} na trase {1} z {2} na {3} v kontextu {4}
luckperms.command.user.promote.end-of-track=Byl dosáhnut konec trasy {0} nelze povýšit hráče {1}
luckperms.command.user.promote.next-group-deleted=Další skupina na trase, {0}, již neexistuje
luckperms.command.user.promote.unable-to-promote=Nepodařilo se povýšit uživatele
luckperms.command.user.demote.success=Degraduji uživatele {0} na trase {1} z {2} na {3} v kontextu {4}
luckperms.command.user.demote.end-of-track=Byl dosažen konec trasy {0} takže byl hráč {1} odebrán ze skupiny {2}
luckperms.command.user.demote.end-of-track-not-removed=Byl dosažen konec trasy {0} ale hráč {1} nebyl odebrán z první skupiny
luckperms.command.user.demote.previous-group-deleted=Předchozí skupina na trase, {0}, již neexistuje
luckperms.command.user.demote.unable-to-demote=Nepodařilo se degradovat uživatele
luckperms.command.group.list.title=Skupiny
luckperms.command.group.delete.not-default=Nelze odstranit výchozí skupinu
luckperms.command.group.info.title=Informace o skupině
luckperms.command.group.info.display-name-key=Zobrazované jméno
luckperms.command.group.info.weight-key=Váha
luckperms.command.group.setweight.set=Váha skupiny {1} nastavena na {0}
luckperms.command.group.setdisplayname.doesnt-have={0} nemá nastavené zobrazované jméno
luckperms.command.group.setdisplayname.already-has={0} již má nastavené zobrazované jméno na {1}
luckperms.command.group.setdisplayname.already-in-use=Zobrazované jméno {0} se již používá skupinou {1}
luckperms.command.group.setdisplayname.set=Zobrazované jméno nastaveno na {0} skupině {1} v kontextu {2}
luckperms.command.group.setdisplayname.removed=Odebráno zobrazované jméno skupině {0} v kontextu {1}
luckperms.command.track.list.title=Trasy
luckperms.command.track.path.empty=Žádné
luckperms.command.track.info.showing-track=Zobrazuji trasu
luckperms.command.track.info.path-property=Cesta
luckperms.command.track.clear=Odebrána trasa skupiny hráče {0}
luckperms.command.track.append.success=Skupina {0} byla připojena k trase {1}
luckperms.command.track.insert.success=Skupina {0} byla vložena do trasy {1} na pozici {2}
luckperms.command.track.insert.error-number=Očekávali jsme číslo, ale místo toho jsme dostali\: {0}
luckperms.command.track.insert.error-invalid-pos=Nepodařilo se vložit na pozici {0}
luckperms.command.track.insert.error-invalid-pos-reason=neplatná pozice
luckperms.command.track.remove.success=Skupina {0} byla odebrána z trasy {1}
luckperms.command.track.error-empty={0} nemůže být použitý, protože je prázdný nebo obsahuje pouze jednu skupinu
luckperms.command.track.error-multiple-groups={0} je členem několika skupin na této trase
luckperms.command.track.error-ambiguous=Nepodařilo se zjistit polohu
luckperms.command.track.already-contains={0} již obsahuje {1}
luckperms.command.track.doesnt-contain={0} neobsahuje {1}
luckperms.command.log.load-error=Protokol nelze načíst
luckperms.command.log.invalid-page=Neplatné číslo stránky
luckperms.command.log.invalid-page-range=Zadejte hodnotu v rozmezí od {0} do {1}
luckperms.command.log.empty=Žádné protokoly k zobrazení
luckperms.command.log.notify.error-console=Nelze přepnout oznámení konzole
luckperms.command.log.notify.enabled-term=Povoleno
luckperms.command.log.notify.disabled-term=Zakázáno
luckperms.command.log.notify.changed-state={0} protokolovací výstup
luckperms.command.log.notify.already-on=Již dostáváte oznámení
luckperms.command.log.notify.already-off=Momentálně nedostáváte oznámení
luckperms.command.log.notify.invalid-state=Stav neznámý. Očekáváme {0} nebo {1}
luckperms.command.log.show.search=Zobrazování nedávných akcí pro dotaz {0}
luckperms.command.log.show.recent=Zobrazování nedávných akcí
luckperms.command.log.show.by=Zobrazování nedávných akcí uživatele {0}
luckperms.command.log.show.history=Zobrazování historie {0} {1}
luckperms.command.export.error-term=Chyba
luckperms.command.export.already-running=Již probíhá jiný proces exportu
luckperms.command.export.file.already-exists=Soubor {0} již existuje
luckperms.command.export.file.not-writable=Soubor {0} není zapisovatelný
luckperms.command.export.file.success=Úspěšně exportováno do {0}
luckperms.command.export.file-unexpected-error-writing=Při zapisování do souboru se vyskytla neočekávaná chyba
luckperms.command.export.web.export-code=Kód exportu
luckperms.command.export.web.import-command-description=Použijte následující příkaz pro import
luckperms.command.import.term=Import
luckperms.command.import.error-term=Chyba
luckperms.command.import.already-running=Již probíhá jiný proces importu
luckperms.command.import.file.doesnt-exist=Soubor {0} neexistuje
luckperms.command.import.file.not-readable=Soubor {0} není čitelný
luckperms.command.import.file.unexpected-error-reading=Při čtení z importovaného souboru došlo k neočekávané chybě
luckperms.command.import.file.correct-format=je to správný formát?
luckperms.command.import.web.unable-to-read=Nelze přečíst data pomocí daného kódu
luckperms.command.import.progress.percent={0}% hotovo
luckperms.command.import.progress.operations={0}/{1} operací dokončeno
luckperms.command.import.starting=Zahajování procesu importu
luckperms.command.import.completed=DOKONČENO
luckperms.command.import.duration=trvalo {0} sekund
luckperms.command.bulkupdate.must-use-console=Příkaz na hromadnou aktualizaci může být vykonán pouze z konzole
luckperms.command.bulkupdate.invalid-data-type=Neplatný typ, očekávali jsme {0}
luckperms.command.bulkupdate.invalid-constraint=Neplatné omezení {0}
luckperms.command.bulkupdate.invalid-constraint-format=Omezení by měla být ve formátu {0}
luckperms.command.bulkupdate.invalid-comparison=Neplatný operátor porovnání {0}
luckperms.command.bulkupdate.invalid-comparison-format=Očekáván jeden z následujících\: {0}
luckperms.command.bulkupdate.queued=Hromadná aktualizace byla zařazena do fronty
luckperms.command.bulkupdate.confirm=Spusťte {0} pro provedení aktualizace
luckperms.command.bulkupdate.unknown-id=Operace s ID {0} neexistuje nebo vypršela
luckperms.command.bulkupdate.starting=Probíhá hromadná aktualizace
luckperms.command.bulkupdate.success=Hromadná aktualizace úspěšně dokončena
luckperms.command.bulkupdate.success.statistics.nodes=Celkový počet ovlivněných uzlů
luckperms.command.bulkupdate.success.statistics.users=Celkový počet ovlivněných uživatelů
luckperms.command.bulkupdate.success.statistics.groups=Celkový počet ovlivněných skupin
luckperms.command.bulkupdate.failure=Hromadná aktualizace selhala, zkontrolujte konzoli kvůli chybám
luckperms.command.update-task.request=Bylo zažádáno o aktualizaci, čekejte prosím
luckperms.command.update-task.complete=Aktualizace dokončena
luckperms.command.update-task.push.attempting=Pokoušíme se odeslat změny na ostatní servery
luckperms.command.update-task.push.complete=Ostatní servery byly úspěšně upozorněny pomocí {0}
luckperms.command.update-task.push.error=Při pokusu odeslat změny na ostatní servery se vyskytla chyba
luckperms.command.update-task.push.error-not-setup=Nelze odeslat změny na ostatní servery, protože služba zasílání zpráv nebyla nakonfigurována
luckperms.command.reload-config.success=Konfigurační soubor byl znovu načten
luckperms.command.reload-config.restart-note=některé možnosti budou platné až po restartování serveru
luckperms.command.translations.searching=Hledání dostupných překladů, čekejte prosím...
luckperms.command.translations.searching-error=Nepodařilo se získat seznam dostupných překladů
luckperms.command.translations.installed-translations=Nainstalované překlady
luckperms.command.translations.available-translations=Dostupné překlady
luckperms.command.translations.percent-translated={0}% přeloženo
luckperms.command.translations.translations-by=od hráče
luckperms.command.translations.installing=Instalace překladů, čekejte prosím...
luckperms.command.translations.download-error=Nepodařilo se stáhnout překlad {0}
luckperms.command.translations.installing-specific=Instalace jazyka {0}...
luckperms.command.translations.install-complete=Instalace dokončena
luckperms.command.translations.download-prompt=Použijte {0} pro stažení a nainstalování aktuálních verzí těchto překladů poskytnutých komunitou
luckperms.command.translations.download-override-warning=Toto přepíše všechny změny, které jste udělali u daných jazyků
luckperms.usage.user.description=Sada příkazů pro správu uživatelů v LuckPerms. (''uživatel'' v LuckPerms je pouze hráč a může odkazovat na UUID nebo uživatelské jméno)
luckperms.usage.group.description=Sada příkazů pro správu skupin v LuckPerms. Skupiny jsou pouze sbírky oprávnění, které mohou být přiděleny novým uživatelům. Nové skupiny se dají vytvořit pomocí příkazu ''creategroup''.
luckperms.usage.track.description=Sada příkazů pro správu tras v LuckPerms. Trasy jsou pouze seřazené kolekce skupin, které mohou být použity pro definování povýšení a degradování.
luckperms.usage.log.description=Sada příkazů pro správu protokolování v LuckPerms.
luckperms.usage.sync.description=Znovu načte všechna data z úložiště pluginu do paměti a použije všechny zjištěné změny.
luckperms.usage.info.description=Vypíše obecné informace o instanci aktivního pluginu.
luckperms.usage.editor.description=Vytvoří novou relaci webového editoru
luckperms.usage.editor.argument.type=typy k načtení do editoru (''all'', ''users'' nebo ''groups'')
luckperms.usage.editor.argument.filter=oprávnění, podle kterého filtrovat uživatelské vstupy
luckperms.usage.verbose.description=Ovládá podrobný kontrolní monitorovací systém oprávnění pluginu.
luckperms.usage.verbose.argument.action=zda povolit/zakázat protokolování, nebo nahrát zaznamenaný výstup
luckperms.usage.verbose.argument.filter=filtr, podle kterého zobrazovat výstupy
luckperms.usage.verbose.argument.commandas=hráč/příkaz ke spuštění
luckperms.usage.tree.description=Vygeneruje stromové zobrazení (seřazený seznam) všech oprávnění v LuckPerms.
luckperms.usage.tree.argument.scope=kořen stromu. zadejte "." pro zahrnutí všech oprávnění
luckperms.usage.tree.argument.player=jméno onlinr hráče ke kontrole
luckperms.usage.search.description=Vyhledá všechny uživatele/skupiny s konkrétním oprávněním
luckperms.usage.search.argument.permission=oprávnění k hledání
luckperms.usage.search.argument.page=strana k zobrazení
luckperms.usage.network-sync.description=Synchronizuje změny s úložištěm a zažádá ostatní servery pro vykonání stejného úkolu
luckperms.usage.import.description=Importuje data z (dříve vytvořeného) exportovaného souboru
luckperms.usage.import.argument.file=soubor, ze kterého importovat data
luckperms.usage.import.argument.replace=nahradit existující data místo sloučení
luckperms.usage.import.argument.upload=nahrát data z předchozího exportu
luckperms.usage.export.description=Exportuje všechna data oprávnění do souboru ''export''. Soubor může být později znovu importován.
luckperms.usage.export.argument.file=soubor do kterého exportovat data
luckperms.usage.export.argument.without-users=vyloučit uživatele z exportu
luckperms.usage.export.argument.without-groups=vyloučit skupiny z exportu
luckperms.usage.export.argument.upload=Nahrát všechna data oprávnění do webového editoru. Může být později znovu importováno.
luckperms.usage.reload-config.description=Znovu načte některé konfigurační možnosti
luckperms.usage.bulk-update.description=Provede hromadné dotazy změn na všechna data
luckperms.usage.bulk-update.argument.data-type=typ změněných dat. (''all'', ''users'' nebo ''groups'')
luckperms.usage.bulk-update.argument.action=akce, která má být provedena na datech. (''update'' nebo ''delete'')
luckperms.usage.bulk-update.argument.action-field=pole, ze kterého se jedná. vyžadováno pouze pro ''update''. (''permission'', ''server'' nebo ''world'')
luckperms.usage.bulk-update.argument.action-value=hodnota, kterou se má nahradit. vyžadováno pouze pro ''update''.
luckperms.usage.bulk-update.argument.constraint=omezení vyžadovaná pro aktualizaci
luckperms.usage.translations.description=Spravuje překlady
luckperms.usage.translations.argument.install=podpříkaz pro instalaci překladů
luckperms.usage.apply-edits.description=Použije změny oprávnění provedené ve webovém editoru
luckperms.usage.apply-edits.argument.code=unikátní kód pro data
luckperms.usage.apply-edits.argument.target=na koho použít data
luckperms.usage.create-group.description=Vytvoří novou skupinu
luckperms.usage.create-group.argument.name=název skupiny
luckperms.usage.create-group.argument.weight=váha skupiny
luckperms.usage.create-group.argument.display-name=zobrazovaný název skupiny
luckperms.usage.delete-group.description=Odstraní skupinu
luckperms.usage.delete-group.argument.name=název skupiny
luckperms.usage.list-groups.description=Zobrazí všechny skupiny na platformě
luckperms.usage.create-track.description=Vytvoří novou trasu
luckperms.usage.create-track.argument.name=název trasy
luckperms.usage.delete-track.description=Odstraní trasu
luckperms.usage.delete-track.argument.name=název trasy
luckperms.usage.list-tracks.description=Zobrazí všechny trasy na platformě
luckperms.usage.user-info.description=Zobrazí informace o uživateli
luckperms.usage.user-switchprimarygroup.description=Změní primární skupinu uživatele
luckperms.usage.user-switchprimarygroup.argument.group=skupina změněna na
luckperms.usage.user-promote.description=Povýší uživatele na trase
luckperms.usage.user-promote.argument.track=trasa, na které povýšit uživatele
luckperms.usage.user-promote.argument.context=kontext pro povýšení uživatele
luckperms.usage.user-promote.argument.dont-add-to-first=povýšit uživatele pouze pokud jsou již na trase
luckperms.usage.user-demote.description=Degraduje uživatele na trase
luckperms.usage.user-demote.argument.track=trasa, na které degradovat uživatele
luckperms.usage.user-demote.argument.context=kontext pro degradování uživatele
luckperms.usage.user-demote.argument.dont-remove-from-first=zabránit uživateli, aby byl odebrán z první skupiny
luckperms.usage.user-clone.description=Naklonuje uživatele
luckperms.usage.user-clone.argument.user=jméno/UUID uživatele k naklonování
luckperms.usage.group-info.description=Zobrazí informace o skupině
luckperms.usage.group-listmembers.description=Zobrazí uživatele/skupiny, které dědí z této skupiny
luckperms.usage.group-listmembers.argument.page=strana k zobrazení
luckperms.usage.group-setweight.description=Nastaví váhu skupin
luckperms.usage.group-setweight.argument.weight=váha k nastavení
luckperms.usage.group-set-display-name.description=Nastaví zobrazovaný název skupiny
luckperms.usage.group-set-display-name.argument.name=název k nastavení
luckperms.usage.group-set-display-name.argument.context=kontext pro nastavení názvu
luckperms.usage.group-rename.description=Přejmenuje skupinu
luckperms.usage.group-rename.argument.name=nový název
luckperms.usage.group-clone.description=Naklonuje skupinu
luckperms.usage.group-clone.argument.name=název skupiny, která se má naklonovat
luckperms.usage.holder-editor.description=Otevře webový editor oprávnění
luckperms.usage.holder-showtracks.description=Vypíše trasy, na kterých je objekt
luckperms.usage.holder-clear.description=Odebere všechna oprávnění, nadřízené a meta
luckperms.usage.holder-clear.argument.context=kontexty k filtrování
luckperms.usage.permission.description=Upraví oprávnění
luckperms.usage.parent.description=Upraví dědění
luckperms.usage.meta.description=Upraví hodnoty metadat
luckperms.usage.permission-info.description=Zobrazí uzly oprávnění, které má objekt
luckperms.usage.permission-info.argument.page=strana k zobrazení
luckperms.usage.permission-info.argument.sort-mode=jak řadit položky
luckperms.usage.permission-set.description=Nastaví oprávnění pro objekt
luckperms.usage.permission-set.argument.node=uzel oprávnění k nastavení
luckperms.usage.permission-set.argument.value=hodnota uzlu
luckperms.usage.permission-set.argument.context=kontexty do kterých přidat oprávnění
luckperms.usage.permission-unset.description=Odnastaví oprávnění objektu
luckperms.usage.permission-unset.argument.node=uzel oprávnění odnastaven
luckperms.usage.permission-unset.argument.context=kontext ve kterém odebrat oprávnění
luckperms.usage.permission-settemp.description=Dočasně nastaví oprávnění objektu
luckperms.usage.permission-settemp.argument.node=uzel oprávnění k nastavení
luckperms.usage.permission-settemp.argument.value=hodnota uzlu
luckperms.usage.permission-settemp.argument.duration=trvání do vypršení uzlu oprávnění
luckperms.usage.permission-settemp.argument.temporary-modifier=jak by mělo být použité dočasné oprávnění
luckperms.usage.permission-settemp.argument.context=kontexty, do kterých přidat oprávnění
luckperms.usage.permission-unsettemp.description=Odnastaví dočasné oprávnění objektu
luckperms.usage.permission-unsettemp.argument.node=uzel oprávnění k odnastavení
luckperms.usage.permission-unsettemp.argument.duration=doba k odečtení
luckperms.usage.permission-unsettemp.argument.context=kontext ve kterém odebrat oprávnění
luckperms.usage.permission-check.description=Zkontroluje, zda má objekt určitý uzel oprávnění
luckperms.usage.permission-check.argument.node=uzel oprávnění ke kontrole
luckperms.usage.permission-clear.description=Vymaže všechna oprávnění
luckperms.usage.permission-clear.argument.context=kontexty podle kterých filtrovat
luckperms.usage.parent-info.description=Zobrazí skupiny, ze kterých tento objekt dědí
luckperms.usage.parent-info.argument.page=strana k zobrazení
luckperms.usage.parent-info.argument.sort-mode=jak řadit položky
luckperms.usage.parent-set.description=Odebere všechny ostatní skupiny, ze kterých objekt již dědí a přidá je k zadanému
luckperms.usage.parent-set.argument.group=skupina, ke které nastavit
luckperms.usage.parent-set.argument.context=kontexty, do kterých nastavit skupinu
luckperms.usage.parent-add.description=Nastaví jinou skupinu, ze které má objekt dědit oprávnění
luckperms.usage.parent-add.argument.group=skupina, ze které dědit
luckperms.usage.parent-add.argument.context=kontexty, do kterých dědit skupinu
luckperms.usage.parent-remove.description=Odebere dříve nastavené pravidlo dědičnosti
luckperms.usage.parent-remove.argument.group=skupina k odebrání
luckperms.usage.parent-remove.argument.context=kontexty, ve kterých odebrat skupinu
luckperms.usage.parent-set-track.description=Odebere všechny ostatní skupiny, ze kterých objekt již dědí na dané trase a přidá je k zadanému
luckperms.usage.parent-set-track.argument.track=trasa, na které nastavit
luckperms.usage.parent-set-track.argument.group=skupina, na které nastavit, nebo číslo týkající se umístění skupiny na dané trase
luckperms.usage.parent-set-track.argument.context=kontexty, do kterých nastavit skupinu
luckperms.usage.parent-add-temp.description=Nastaví jinou skupinu, ze které má dočasně objekt dědit oprávnění
luckperms.usage.parent-add-temp.argument.group=skupina, ze které dědit
luckperms.usage.parent-add-temp.argument.duration=doba trvání členství ve skupině
luckperms.usage.parent-add-temp.argument.temporary-modifier=jak by mělo být použité dočasné oprávnění
luckperms.usage.parent-add-temp.argument.context=kontexty, do kterých dědit skupinu
luckperms.usage.parent-remove-temp.description=Odebere dříve nastavené dočasné pravidlo dědičnosti
luckperms.usage.parent-remove-temp.argument.group=skupina k odebrání
luckperms.usage.parent-remove-temp.argument.duration=doba k odečtení
luckperms.usage.parent-remove-temp.argument.context=kontexty, ve kterých odebrat skupinu
luckperms.usage.parent-clear.description=Vymaže všechny nadřízené
luckperms.usage.parent-clear.argument.context=kontexty, podle kterých filtrovat
luckperms.usage.parent-clear-track.description=Vymaže všechny nadřízené na dané trase
luckperms.usage.parent-clear-track.argument.track=trasa, na které odebrat
luckperms.usage.parent-clear-track.argument.context=kontexty podle kterých filtrovat
luckperms.usage.meta-info.description=Zobrazí všechna meta chatu
luckperms.usage.meta-set.description=Nastaví meta hodnotu
luckperms.usage.meta-set.argument.key=klíč k nastavení
luckperms.usage.meta-set.argument.value=hodnota k nastavení
luckperms.usage.meta-set.argument.context=kontexty, do kterých přidat meta pár
luckperms.usage.meta-unset.description=Odnastaví meta hodnotu
luckperms.usage.meta-unset.argument.key=klíč pro odnastavení
luckperms.usage.meta-unset.argument.context=kontexty, ze kterých odebrat meta pár
luckperms.usage.meta-settemp.description=Nastaví dočasnou meta hodnotu
luckperms.usage.meta-settemp.argument.key=klíč k nastavení
luckperms.usage.meta-settemp.argument.value=hodnota k nastavení
luckperms.usage.meta-settemp.argument.duration=trvání, dokud nevyprší meta hodnota
luckperms.usage.meta-settemp.argument.context=kontexty, do kterých přidat meta pár
luckperms.usage.meta-unsettemp.description=Odnastaví dočasnou meta hodnotu
luckperms.usage.meta-unsettemp.argument.key=klíč pro odnastavení
luckperms.usage.meta-unsettemp.argument.context=kontexty, ze kterých odebrat meta pár
luckperms.usage.meta-addprefix.description=Přidá prefix
luckperms.usage.meta-addprefix.argument.priority=priorita přidání prefixu na
luckperms.usage.meta-addprefix.argument.prefix=řetězec prefixu
luckperms.usage.meta-addprefix.argument.context=kontexty, do kterých přidat prefix
luckperms.usage.meta-addsuffix.description=Přidá suffix
luckperms.usage.meta-addsuffix.argument.priority=priorita do které přidat suffix
luckperms.usage.meta-addsuffix.argument.suffix=řetězec suffixu
luckperms.usage.meta-addsuffix.argument.context=kontexty, do kterých přidat suffix
luckperms.usage.meta-setprefix.description=Nastaví prefix
luckperms.usage.meta-setprefix.argument.priority=priorita, na které nastavit prefix
luckperms.usage.meta-setprefix.argument.prefix=řetězec prefixu
luckperms.usage.meta-setprefix.argument.context=kontexty, do kterých nastavit prefix
luckperms.usage.meta-setsuffix.description=Nastaví suffix
luckperms.usage.meta-setsuffix.argument.priority=priorita, na které nastavit suffix
luckperms.usage.meta-setsuffix.argument.suffix=řetězec suffixu
luckperms.usage.meta-setsuffix.argument.context=kontexty, do kterých přidat suffix
luckperms.usage.meta-removeprefix.description=Odebere prefix
luckperms.usage.meta-removeprefix.argument.priority=priorita k odebrání prefixu v
luckperms.usage.meta-removeprefix.argument.prefix=řetězec prefixu
luckperms.usage.meta-removeprefix.argument.context=kontexty pro odebrání prefixu
luckperms.usage.meta-removesuffix.description=Odebere suffix
luckperms.usage.meta-removesuffix.argument.priority=priorita k odebrání suffixu v
luckperms.usage.meta-removesuffix.argument.suffix=řetězec suffixu
luckperms.usage.meta-removesuffix.argument.context=kontexty pro odebrání suffixu
luckperms.usage.meta-addtemp-prefix.description=Dočasně přidá prefix
luckperms.usage.meta-addtemp-prefix.argument.priority=priorita pro přidání prefixu
luckperms.usage.meta-addtemp-prefix.argument.prefix=řetězec prefixu
luckperms.usage.meta-addtemp-prefix.argument.duration=trvání, dokud nevyprší prefix
luckperms.usage.meta-addtemp-prefix.argument.context=kontexty, do kterých přidat prefix
luckperms.usage.meta-addtemp-suffix.description=Přidá dočasný suffix
luckperms.usage.meta-addtemp-suffix.argument.priority=priorita do které přidat suffix
luckperms.usage.meta-addtemp-suffix.argument.suffix=řetězec suffixu
luckperms.usage.meta-addtemp-suffix.argument.duration=trvání, dokud nevyprší suffix
luckperms.usage.meta-addtemp-suffix.argument.context=kontexty, do kterých přidat suffix
luckperms.usage.meta-settemp-prefix.description=Dočasně nastaví prefix
luckperms.usage.meta-settemp-prefix.argument.priority=priorita, na které nastavit prefix
luckperms.usage.meta-settemp-prefix.argument.prefix=řetězec prefixu
luckperms.usage.meta-settemp-prefix.argument.duration=trvání, dokud nevyprší prefix
luckperms.usage.meta-settemp-prefix.argument.context=kontexty, do kterých nastavit prefix
luckperms.usage.meta-settemp-suffix.description=Nastaví dočasný suffix
luckperms.usage.meta-settemp-suffix.argument.priority=priorita, na které nastavit suffix
luckperms.usage.meta-settemp-suffix.argument.suffix=řetězec suffixu
luckperms.usage.meta-settemp-suffix.argument.duration=trvání, dokud nevyprší suffix
luckperms.usage.meta-settemp-suffix.argument.context=kontexty, do kterých přidat suffix
luckperms.usage.meta-removetemp-prefix.description=Odebere dočasný prefix
luckperms.usage.meta-removetemp-prefix.argument.priority=priorita k odebrání prefixu v
luckperms.usage.meta-removetemp-prefix.argument.prefix=řetězec prefixu
luckperms.usage.meta-removetemp-prefix.argument.context=kontexty pro odebrání prefixu
luckperms.usage.meta-removetemp-suffix.description=Odebere dočasný suffix
luckperms.usage.meta-removetemp-suffix.argument.priority=priorita k odebrání suffixu v
luckperms.usage.meta-removetemp-suffix.argument.suffix=řetězec suffixu
luckperms.usage.meta-removetemp-suffix.argument.context=kontexty pro odebrání suffixu
luckperms.usage.meta-clear.description=Vymaže všechna meta
luckperms.usage.meta-clear.argument.type=typ mety k odstranění
luckperms.usage.meta-clear.argument.context=kontexty, podle kterých filtrovat
luckperms.usage.track-info.description=Zobrazí informace o trase
luckperms.usage.track-editor.description=Otevře webový editor oprávnění
luckperms.usage.track-append.description=Přidá skupinu na konec trasy
luckperms.usage.track-append.argument.group=skupina k připojení
luckperms.usage.track-insert.description=Vloží skupinu na danou pozici na trase
luckperms.usage.track-insert.argument.group=skupina pro vložení
luckperms.usage.track-insert.argument.position=pozice k vložení skuipny (první pozice na trase je 1)
luckperms.usage.track-remove.description=Odebere skupinu z trasy
luckperms.usage.track-remove.argument.group=skupina k odebrání
luckperms.usage.track-clear.description=Vymaže skupiny na trase
luckperms.usage.track-rename.description=Přejmenuje trasu
luckperms.usage.track-rename.argument.name=nový název
luckperms.usage.track-clone.description=Naklonuje trasu
luckperms.usage.track-clone.argument.name=název trasy, která se má naklonovat
luckperms.usage.log-recent.description=Zobrazí nedávné akce
luckperms.usage.log-recent.argument.user=jméno/UUID uživatele k filtrování
luckperms.usage.log-recent.argument.page=číslo stránky k zobrazení
luckperms.usage.log-search.description=Hledat záznam v protokolu
luckperms.usage.log-search.argument.query=dotaz k hledání
luckperms.usage.log-search.argument.page=číslo stránky k zobrazení
luckperms.usage.log-notify.description=Přepne oznámení protokolu
luckperms.usage.log-notify.argument.toggle=zapnout nebo vypnout
luckperms.usage.log-user-history.description=Zobrazí historii uživatele
luckperms.usage.log-user-history.argument.user=jméno/UUID uživatele
luckperms.usage.log-user-history.argument.page=číslo stránky k zobrazení
luckperms.usage.log-group-history.description=Zobrazí historii skupiny
luckperms.usage.log-group-history.argument.group=název skupiny
luckperms.usage.log-group-history.argument.page=číslo stránky k zobrazení
luckperms.usage.log-track-history.description=Zobrazí historii trasy
luckperms.usage.log-track-history.argument.track=název trasy
luckperms.usage.log-track-history.argument.page=číslo stránky k zobrazení
luckperms.usage.sponge.description=Upraví dodatečná Sponge data
luckperms.usage.sponge.argument.collection=sbírka k dotazu
luckperms.usage.sponge.argument.subject=subjekt ke změně
luckperms.usage.sponge-permission-info.description=Zobrazí informace o oprávněních subjektu
luckperms.usage.sponge-permission-info.argument.contexts=kontexty podle kterých filtrovat
luckperms.usage.sponge-permission-set.description=Nastaví oprávnění subjektu
luckperms.usage.sponge-permission-set.argument.node=uzel oprávnění k nastavení
luckperms.usage.sponge-permission-set.argument.tristate=hodnota, na kterou nastavit oprávnění
luckperms.usage.sponge-permission-set.argument.contexts=kontexty, do kterých nastavit oprávnění
luckperms.usage.sponge-permission-clear.description=Vymaže oprávnění subjektu
luckperms.usage.sponge-permission-clear.argument.contexts=kontexty, ve kterých odebrat oprávnění
luckperms.usage.sponge-parent-info.description=Zobrazí informace o nadřízených subjektu
luckperms.usage.sponge-parent-info.argument.contexts=kontexty podle kterých filtrovat
luckperms.usage.sponge-parent-add.description=Přidá nadřazeného subjektu
luckperms.usage.sponge-parent-add.argument.collection=soubor subjektů, kde je nadřízený subjekt
luckperms.usage.sponge-parent-add.argument.subject=název nadřízeného subjektu
luckperms.usage.sponge-parent-add.argument.contexts=kontexty, do kterých přidat nadřízeného
luckperms.usage.sponge-parent-remove.description=Odebere nadřízeného ze subjektu
luckperms.usage.sponge-parent-remove.argument.collection=soubor subjektů, kde je nadřízený subjekt
luckperms.usage.sponge-parent-remove.argument.subject=název nadřízeného subjektu
luckperms.usage.sponge-parent-remove.argument.contexts=kontexty, ze kterých odebrat nadřízeného
luckperms.usage.sponge-parent-clear.description=Vymaže nadřízené subjektu
luckperms.usage.sponge-parent-clear.argument.contexts=kontexty, ve kterých vymazat nadřízené
luckperms.usage.sponge-option-info.description=Zobrazí informace o možnostech subjektu
luckperms.usage.sponge-option-info.argument.contexts=kontexty, podle kterých filtrovat
luckperms.usage.sponge-option-set.description=Nastaví možnost subjektu
luckperms.usage.sponge-option-set.argument.key=klíč k nastavení
luckperms.usage.sponge-option-set.argument.value=hodnota, na kterou má být klíč nastaven
luckperms.usage.sponge-option-set.argument.contexts=kontexty, do kterých nastavit možnost
luckperms.usage.sponge-option-unset.description=Odnastaví možnost subjektu
luckperms.usage.sponge-option-unset.argument.key=klíč pro odnastavení
luckperms.usage.sponge-option-unset.argument.contexts=kontexty, ve kterých zrušit klíč
luckperms.usage.sponge-option-clear.description=Vymaže možnosti subjektu
luckperms.usage.sponge-option-clear.argument.contexts=kontexty, ve kterých vymazat možnosti
