luckperms.logs.actionlog-prefix=JOURNAL
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTATION
luckperms.commandsystem.available-commands=Utilisez {0} pour afficher les commandes disponibles
luckperms.commandsystem.command-not-recognised=Commande non reconnue
luckperms.commandsystem.no-permission=Vous n''avez pas la permission d''utiliser cette commande \!
luckperms.commandsystem.no-permission-subcommands=Vous n''avez pas la permission d''utiliser les sous-commandes
luckperms.commandsystem.already-executing-command=Une autre commande est en cours d''exécution, en attente de sa fin d''exécution...
luckperms.commandsystem.usage.sub-commands-header=Sous-commandes
luckperms.commandsystem.usage.usage-header=Utilisation de la commande
luckperms.commandsystem.usage.arguments-header=Arguments
luckperms.first-time.no-permissions-setup=Il semble qu’aucune permission n’ait encore été configurée \!
luckperms.first-time.use-console-to-give-access=Avant de pouvoir utiliser l''une des commandes de LuckPerms en jeu, vous devez utiliser la console pour vous donner les accès
luckperms.first-time.console-command-prompt=Ouvrez votre console et exécutez
luckperms.first-time.next-step=Après avoir fait ceci, vous pouvez commencer à définir vos permissions et vos groupes
luckperms.first-time.wiki-prompt=Vous ne savez pas par où commencer ? Cliquez ici \: {0}
luckperms.login.try-again=Veuillez réessayer ultérieurement
luckperms.login.loading-database-error=Une erreur de base de données est survenue lors du chargement des données de permissions
luckperms.login.server-admin-check-console-errors=Si vous êtes un administrateur du serveur, veuillez vérifier la console pour toute erreur
luckperms.login.server-admin-check-console-info=Veuillez vérifier la console du serveur pour plus d''informations
luckperms.login.data-not-loaded-at-pre=Les données des permissions de votre utilisateur n''ont pas été chargées pendant l''étape de pré-connexion
luckperms.login.unable-to-continue=impossible de continuer
luckperms.login.craftbukkit-offline-mode-error=ceci est probablement dû à un conflit entre CraftBukkit et le paramètre online-mode
luckperms.login.unexpected-error=Une erreur inattendue s''est produite lors de la configuration de vos données de permission
luckperms.opsystem.disabled=Le système vanilla d''OP est désactivé sur ce serveur
luckperms.opsystem.sponge-warning=Veuillez noter que le statut d''opérateur n''a aucun effet sur les vérifications de permission de Sponge lorsqu''un plugin de permissions est installé, vous devez directement éditer les données utilisateur
luckperms.duration.unit.years.plural={0} ans
luckperms.duration.unit.years.singular={0} an
luckperms.duration.unit.years.short={0} a
luckperms.duration.unit.months.plural={0} mois
luckperms.duration.unit.months.singular={0} mois
luckperms.duration.unit.months.short={0} mo
luckperms.duration.unit.weeks.plural={0} semaines
luckperms.duration.unit.weeks.singular={0} semaine
luckperms.duration.unit.weeks.short={0} sem
luckperms.duration.unit.days.plural={0} jours
luckperms.duration.unit.days.singular={0} jour
luckperms.duration.unit.days.short={0} j
luckperms.duration.unit.hours.plural={0} heures
luckperms.duration.unit.hours.singular={0} heure
luckperms.duration.unit.hours.short={0} h
luckperms.duration.unit.minutes.plural={0} minutes
luckperms.duration.unit.minutes.singular={0} minute
luckperms.duration.unit.minutes.short={0} min
luckperms.duration.unit.seconds.plural={0} secondes
luckperms.duration.unit.seconds.singular={0} seconde
luckperms.duration.unit.seconds.short={0} s
luckperms.duration.since=il y a {0}
luckperms.command.misc.invalid-code=Code invalide
luckperms.command.misc.response-code-key=code de réponse
luckperms.command.misc.error-message-key=message
luckperms.command.misc.bytebin-unable-to-communicate=Impossible de communiquer avec bytebin
luckperms.command.misc.webapp-unable-to-communicate=Impossible de communiquer avec l''application web
luckperms.command.misc.check-console-for-errors=Vérifiez les erreurs dans la console
luckperms.command.misc.file-must-be-in-data=Le fichier {0} doit être placé directement dans le répertoire de données
luckperms.command.misc.wait-to-finish=Veuillez attendre la fin et réessayer
luckperms.command.misc.invalid-priority=Priorité invalide {0}
luckperms.command.misc.expected-number=Un nombre était attendu
luckperms.command.misc.date-parse-error=Impossible de déterminer la date {0}
luckperms.command.misc.date-in-past-error=Vous ne pouvez pas choisir une date dans le passé \!
luckperms.command.misc.page=page {0} sur {1}
luckperms.command.misc.page-entries={0} entrées
luckperms.command.misc.none=Aucun
luckperms.command.misc.loading.error.unexpected=Une erreur inattendue est survenue
luckperms.command.misc.loading.error.user=Utilisateur non chargé
luckperms.command.misc.loading.error.user-specific=Impossible de charger l''utilisateur cible {0}
luckperms.command.misc.loading.error.user-not-found=Un utilisateur pour {0} n''a pas pu être trouvé
luckperms.command.misc.loading.error.user-save-error=Une erreur s''est produite lors de la sauvegarde des données utilisateur de {0}
luckperms.command.misc.loading.error.user-not-online=L''utilisateur {0} n’est pas connecté
luckperms.command.misc.loading.error.user-invalid={0} n''est pas un pseudonyme/uuid valide
luckperms.command.misc.loading.error.user-not-uuid=L''utilisateur cible {0} n''est pas un uuid valide
luckperms.command.misc.loading.error.group=Groupe non chargé
luckperms.command.misc.loading.error.all-groups=Impossible de charger tous les groupes
luckperms.command.misc.loading.error.group-not-found=Il n''y a aucun groupe nommé {0}
luckperms.command.misc.loading.error.group-save-error=Une erreur s''est produite lors de la sauvegarde des données de groupe de {0}
luckperms.command.misc.loading.error.group-invalid={0} n''est pas un nom de groupe valide
luckperms.command.misc.loading.error.track=Track non chargée
luckperms.command.misc.loading.error.all-tracks=Impossibles de charger toutes les tracks
luckperms.command.misc.loading.error.track-not-found=Impossible de trouver une track nommée {0}
luckperms.command.misc.loading.error.track-save-error=Une erreur s''est produite lors de la sauvegarde des données de track de {0}
luckperms.command.misc.loading.error.track-invalid={0} n''est pas un nom de track valide
luckperms.command.editor.no-match=Impossible d''ouvrir l''éditeur, aucun objet ne correspond au type souhaité
luckperms.command.editor.start=Préparation d''une nouvelle session d''éditeur, veuillez patienter...
luckperms.command.editor.url=Cliquez sur le lien ci-dessous pour ouvrir l''éditeur
luckperms.command.editor.unable-to-communicate=Impossible de communiquer avec l''éditeur
luckperms.command.editor.apply-edits.success=Les données de l''éditeur web ont été appliquées à {0} {1} avec succès
luckperms.command.editor.apply-edits.success-summary={0} {1} et {2} {3}
luckperms.command.editor.apply-edits.success.additions=ajouts
luckperms.command.editor.apply-edits.success.additions-singular=ajout
luckperms.command.editor.apply-edits.success.deletions=suppressions
luckperms.command.editor.apply-edits.success.deletions-singular=suppression
luckperms.command.editor.apply-edits.no-changes=Aucune modification n''a été appliquée depuis l''éditeur web, les données retournées ne contiennent aucune modification
luckperms.command.editor.apply-edits.unknown-type=Impossible d''appliquer la modification au type d''objet spécifié
luckperms.command.editor.apply-edits.unable-to-read=Impossible de lire les données en utilisant le code donné
luckperms.command.search.searching.permission=Recherche des utilisateurs et des groupes avec {0}
luckperms.command.search.searching.inherit=Recherche des utilisateurs et des groupes qui héritent de {0}
luckperms.command.search.result={0} entrées trouvées de {1} utilisateurs et {2} groupes
luckperms.command.search.result.default-notice=Remarque \: lors de la recherche des membres dans le groupe par défaut, les joueurs hors ligne qui n''ont pas d''autres permissions ne seront pas affichés \!
luckperms.command.search.showing-users=Affichage des entrées utilisateur
luckperms.command.search.showing-groups=Affichage des entrées du groupe
luckperms.command.tree.start=Génération de l''arborescence des permissions, veuillez patienter...
luckperms.command.tree.empty=Impossible de générer l''arborescence, aucun résultat n''a été trouvé
luckperms.command.tree.url=URL de l''arborescence des permissions
luckperms.command.verbose.invalid-filter={0} n''est pas un filtre détaillé valide
luckperms.command.verbose.enabled=Journalisation détaillée de {0} pour les vérifications correspondant à {1}
luckperms.command.verbose.command-exec=Execution de la commande {1} par {0} et rapport de toutes les vérifications effectuées...
luckperms.command.verbose.off=Journalisation détaillée {0}
luckperms.command.verbose.command-exec-complete=Exécution de la commande terminée
luckperms.command.verbose.command.no-checks=L''exécution de la commande est terminée, mais aucune vérification de permission n''a été effectuée
luckperms.command.verbose.command.possibly-async=Cela peut être dû au fait que le plugin exécute les commandes en arrière-plan (async)
luckperms.command.verbose.command.try-again-manually=Vous pouvez toujours utiliser le verbe manuellement  pour détecter les contrôles effectués de cette manière
luckperms.command.verbose.enabled-recording=Enregistrement détaillé de {0} pour les vérifications correspondant à {1}
luckperms.command.verbose.uploading=Journalisation détaillée {0}, envoie des résultats...
luckperms.command.verbose.url=URL des résultats détaillés
luckperms.command.verbose.enabled-term=activé
luckperms.command.verbose.disabled-term=desactivé
luckperms.command.verbose.query-any=TOUS
luckperms.command.info.running-plugin=Utilisation de
luckperms.command.info.platform-key=Plateforme
luckperms.command.info.server-brand-key=Marque du serveur
luckperms.command.info.server-version-key=Version du serveur
luckperms.command.info.storage-key=Stockage
luckperms.command.info.storage-type-key=Type
luckperms.command.info.storage.meta.split-types-key=Types
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Connecté
luckperms.command.info.storage.meta.file-size-key=Taille du fichier
luckperms.command.info.extensions-key=Extensions
luckperms.command.info.messaging-key=Message
luckperms.command.info.instance-key=Instance
luckperms.command.info.static-contexts-key=Contextes statiques
luckperms.command.info.online-players-key=Joueurs connectés
luckperms.command.info.online-players-unique={0} uniques
luckperms.command.info.uptime-key=Temps de fonctionnement
luckperms.command.info.local-data-key=Données Locales
luckperms.command.info.local-data={0} utilisateurs, {1} groupes, {2} tracks
luckperms.command.generic.create.success={0} a été créé avec succès
luckperms.command.generic.create.error=Une erreur est survenue lors de la création de {0}
luckperms.command.generic.create.error-already-exists={0} existe déjà \!
luckperms.command.generic.delete.success={0} a été supprimé avec succès
luckperms.command.generic.delete.error=Une erreur est survenue lors de la suppression de {0}
luckperms.command.generic.delete.error-doesnt-exist={0} n''existe pas \!
luckperms.command.generic.rename.success={0} a été renommé avec succès en {1}
luckperms.command.generic.clone.success={0} a été cloné avec succès vers {1}
luckperms.command.generic.info.parent.title=Groupes Parents
luckperms.command.generic.info.parent.temporary-title=Groupes Parents Temporaires
luckperms.command.generic.info.expires-in=expire dans
luckperms.command.generic.info.inherited-from=hérité de
luckperms.command.generic.info.inherited-from-self=soi
luckperms.command.generic.show-tracks.title=Tracks de {0}
luckperms.command.generic.show-tracks.empty={0} n''est sur aucune track
luckperms.command.generic.clear.node-removed={0} nœuds ont été supprimés
luckperms.command.generic.clear.node-removed-singular={0} nœud a été supprimé
luckperms.command.generic.clear=Les nœuds de {0} ont été effacés dans le contexte {1}
luckperms.command.generic.permission.info.title=Permissions de {0}
luckperms.command.generic.permission.info.empty={0} n’a pas de permission définie
luckperms.command.generic.permission.info.click-to-remove=Cliquez pour retirer ce nœud de {0}
luckperms.command.generic.permission.check.info.title=Informations de permission pour {0}
luckperms.command.generic.permission.check.info.directly={0} a la permission {1} définie à {2} dans le contexte {3}
luckperms.command.generic.permission.check.info.inherited={0} hérite de {1} défini à {2} depuis {3} dans le contexte {4}
luckperms.command.generic.permission.check.info.not-directly={0} n''a pas de {1} défini
luckperms.command.generic.permission.check.info.not-inherited={0} n''hérite pas de {1}
luckperms.command.generic.permission.check.result.title=Vérification des permissions pour {0}
luckperms.command.generic.permission.check.result.result-key=Résultat
luckperms.command.generic.permission.check.result.processor-key=Processeur
luckperms.command.generic.permission.check.result.cause-key=Cause
luckperms.command.generic.permission.check.result.context-key=Contexte
luckperms.command.generic.permission.set={0} à été défini sur {1} pour {2} dans le contexte {3}
luckperms.command.generic.permission.already-has={0} a déjà {1} de défini dans le contexte {2}
luckperms.command.generic.permission.set-temp={0} a été défini à {1} pour {2} pour une durée de {3} dans le contexte {4}
luckperms.command.generic.permission.already-has-temp={0} a déjà {1} de défini temporairement dans le contexte {2}
luckperms.command.generic.permission.unset=Retirer {0} pour {1} dans le contexte {2}
luckperms.command.generic.permission.doesnt-have={0} n''a pas {1} défini dans le contexte {2}
luckperms.command.generic.permission.unset-temp=Retirer la permission temporaire {0} pour {1} dans le contexte {2}
luckperms.command.generic.permission.subtract=Défini {0} à {1} pour {2} pour une durée de {3} dans le contexte {4}, {5} de moins qu''avant
luckperms.command.generic.permission.doesnt-have-temp={0} n''a pas {1} défini temporairement dans le contexte {2}
luckperms.command.generic.permission.clear=Les permissions de {0} ont été effacées dans le contexte {1}
luckperms.command.generic.parent.info.title=Parents de {0}
luckperms.command.generic.parent.info.empty={0} n''a aucun parent défini
luckperms.command.generic.parent.info.click-to-remove=Cliquez pour retirer ce parent de {0}
luckperms.command.generic.parent.add={0} hérite maintenant des permissions de {1} dans le contexte {2}
luckperms.command.generic.parent.add-temp={0} hérite désormais des permissions de {1} pour une durée de {2} dans le contexte {3}
luckperms.command.generic.parent.set=Les groupes parents de {0} ont été effacés et il hérite maintenant seulement de {1} dans le contexte {2}
luckperms.command.generic.parent.set-track=Les groupes parents de {0} ont été effacés de la track {1} et il hérite maintenant seulement de {2} dans le contexte {3}
luckperms.command.generic.parent.remove={0} n''hérite plus des permissions de {1} dans le contexte {2}
luckperms.command.generic.parent.remove-temp={0} n''hérite plus temporairement des permissions de {1} dans le contexte {2}
luckperms.command.generic.parent.subtract={0} héritera des permissions de {1} pour une durée de {2} dans le contexte {3}, {4} de moins qu''avant
luckperms.command.generic.parent.clear=Les parents de {0} ont été effacés dans le contexte {1}
luckperms.command.generic.parent.clear-track=Les parents de {0} de la track {1} ont été effacés dans le contexte {2}
luckperms.command.generic.parent.already-inherits={0} hérite déjà de {1} dans le contexte {2}
luckperms.command.generic.parent.doesnt-inherit={0} n''hérite pas de {1} dans le contexte {2}
luckperms.command.generic.parent.already-temp-inherits={0} hérite déjà temporairement de {1} dans le contexte {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} n''hérite pas temporairement de {1} dans le contexte {2}
luckperms.command.generic.chat-meta.info.title-prefix=Préfixes de {0}
luckperms.command.generic.chat-meta.info.title-suffix=Suffixes de {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} n''a pas de préfixes
luckperms.command.generic.chat-meta.info.none-suffix={0} n''a pas de suffixes
luckperms.command.generic.chat-meta.info.click-to-remove=Cliquez pour retirer ce {0} de {1}
luckperms.command.generic.chat-meta.already-has={0} a déjà {1} {2} défini à une priorité de {3} dans le contexte {4}
luckperms.command.generic.chat-meta.already-has-temp={0} a déjà {1} {2} défini temporairement à une priorité de {3} dans le contexte {4}
luckperms.command.generic.chat-meta.doesnt-have={0} n''a pas {1} {2} défini à une priorité de {3} dans le contexte {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} n''a pas {1} {2} défini temporairement à une priorité de {3} dans le contexte {4}
luckperms.command.generic.chat-meta.add={0} a eu {1} {2} défini à une priorité de {3} dans le contexte {4}
luckperms.command.generic.chat-meta.add-temp={0} a eu {1} {2} défini à une priorité de {3} pour une durée de {4} dans le contexte {5}
luckperms.command.generic.chat-meta.remove={0} a eu {1} {2} à la priorité {3} retiré dans le contexte {4}
luckperms.command.generic.chat-meta.remove-bulk={0} a eu tous les {1} à la priorité {2} retirés dans le contexte {3}
luckperms.command.generic.chat-meta.remove-temp={0} a eu temporairement {1} {2} à la priorité {3} retiré dans le contexte {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} a eu tous les {1} temporaires à la priorité {2} retirés dans le contexte {3}
luckperms.command.generic.meta.info.title=Métadonnées de {0}
luckperms.command.generic.meta.info.none={0} n''a pas de métadonnées
luckperms.command.generic.meta.info.click-to-remove=Cliquez pour retirer cette métadonnée de {0}
luckperms.command.generic.meta.already-has={0} a déjà la métadonnée {1} définie à {2} dans le contexte {3}
luckperms.command.generic.meta.already-has-temp={0} a déjà la métadonnée {1} définie temporaiement à {2} dans le contexte {3}
luckperms.command.generic.meta.doesnt-have={0} n''a pas de métadonnée {1} définie dans le contexte {2}
luckperms.command.generic.meta.doesnt-have-temp={0} n''a pas de métadonnée {1} définie temporairement dans le contexte {2}
luckperms.command.generic.meta.set=La métadonnée {0} a été défini à {1} pour {2} dans le contexte {3}
luckperms.command.generic.meta.set-temp=La métadonnée {0} a été défini à {1} pour {2} pour une durée de {3} dans le contexte {4}
luckperms.command.generic.meta.unset=Retirer la métadonnée {0} pour {1} dans le contexte {2}
luckperms.command.generic.meta.unset-temp=Retirer la métadonnée temporaire {0} pour {1} dans le contexte {2}
luckperms.command.generic.meta.clear=Le type de métadonnée correspondant à {0} {1} a été effacé dans le contexte {2}
luckperms.command.generic.contextual-data.title=Données contextuelles
luckperms.command.generic.contextual-data.mode.key=mode
luckperms.command.generic.contextual-data.mode.server=serveur
luckperms.command.generic.contextual-data.mode.active-player=joueur actif
luckperms.command.generic.contextual-data.contexts-key=Contextes
luckperms.command.generic.contextual-data.prefix-key=Préfixe
luckperms.command.generic.contextual-data.suffix-key=Suffixe
luckperms.command.generic.contextual-data.primary-group-key=Groupe principal
luckperms.command.generic.contextual-data.meta-key=Méta
luckperms.command.generic.contextual-data.null-result=Aucun
luckperms.command.user.info.title=Informations de l''utilisateur
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=type
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=hors-ligne
luckperms.command.user.info.status-key=Statut
luckperms.command.user.info.status.online=En ligne
luckperms.command.user.info.status.offline=Hors-ligne
luckperms.command.user.removegroup.error-primary=Vous ne pouvez pas retirer un utilisateur de son groupe principal
luckperms.command.user.primarygroup.not-member={0} n''était pas déjà membre de {1}, ajout en cours
luckperms.command.user.primarygroup.already-has={0} a déjà {1} comme groupe principal
luckperms.command.user.primarygroup.warn-option=Attention \: La méthode de calcul de groupe principale utilisée par ce serveur ({0}) peut ne pas refléter ce changement
luckperms.command.user.primarygroup.set=Le groupe principal de {0} a été défini à {1}
luckperms.command.user.track.error-not-contain-group={0} n''est pas déjà dans un groupe de {1}
luckperms.command.user.track.unsure-which-track=Veuillez spécifier la track à utiliser comme argument
luckperms.command.user.track.missing-group-advice=Créez le groupe, ou supprimez-le de la track et réessayez
luckperms.command.user.promote.added-to-first={0} n''est dans aucun groupe sur {1}, ils ont donc été ajoutés au premier groupe, {2} dans le contexte {3}
luckperms.command.user.promote.not-on-track={0} n''est dans aucun groupe sur {1}, donc n''a pas été promu
luckperms.command.user.promote.success=Promotion de {0} le long de la track {1} de {2} à {3} dans le contexte {4}
luckperms.command.user.promote.end-of-track=La fin de la track {0} a été atteinte, impossible de promouvoir {1}
luckperms.command.user.promote.next-group-deleted=Le groupe suivant sur la track, {0}, n''existe plus
luckperms.command.user.promote.unable-to-promote=Impossible de promouvoir l''utilisateur
luckperms.command.user.demote.success=Rétrogradation de {0} le long de la track {1} de {2} à {3} dans le contexte {4}
luckperms.command.user.demote.end-of-track=La fin de la track {0} a été atteinte, donc {1} a été retiré de {2}
luckperms.command.user.demote.end-of-track-not-removed=La fin de la track {0} a été atteinte, mais {1} n''a pas été retiré du premier groupe
luckperms.command.user.demote.previous-group-deleted=Le groupe précédent sur la track, {0}, n''existe plus
luckperms.command.user.demote.unable-to-demote=Impossible de rétrograder l''utilisateur
luckperms.command.group.list.title=Groupes
luckperms.command.group.delete.not-default=Vous ne pouvez pas supprimer le groupe par défaut
luckperms.command.group.info.title=Informations du groupe
luckperms.command.group.info.display-name-key=Nom d''Affichage
luckperms.command.group.info.weight-key=Poids
luckperms.command.group.setweight.set=Poids défini à {0} pour le groupe {1}
luckperms.command.group.setdisplayname.doesnt-have={0} n''a pas de nom d''affichage défini
luckperms.command.group.setdisplayname.already-has={0} a déjà un nom d''affichage de {1}
luckperms.command.group.setdisplayname.already-in-use=Le nom d''affichage {0} est déjà utilisé par {1}
luckperms.command.group.setdisplayname.set=Définir le nom d''affichage sur {0} pour le groupe {1} dans le contexte {2}
luckperms.command.group.setdisplayname.removed=Retirer le nom d''affichage du groupe {0} dans le contexte {1}
luckperms.command.track.list.title=Tracks
luckperms.command.track.path.empty=Aucun
luckperms.command.track.info.showing-track=Affichage de la track
luckperms.command.track.info.path-property=Chemin
luckperms.command.track.clear=Les groupes de la track {0} ont été effacés
luckperms.command.track.append.success=Le groupe {0} a été ajouté à la track {1}
luckperms.command.track.insert.success=Le groupe {0} a été inséré dans la track {1} à la position {2}
luckperms.command.track.insert.error-number=Nombre attendu, mais reçu \: {0}
luckperms.command.track.insert.error-invalid-pos=Impossible d''insérer à la position {0}
luckperms.command.track.insert.error-invalid-pos-reason=position invalide
luckperms.command.track.remove.success=Le groupe {0} a été retiré de la track {1}
luckperms.command.track.error-empty=La track {0} ne peut pas être utilisée car elle est vide ou ne contient qu''un seul groupe
luckperms.command.track.error-multiple-groups={0} est membre de plusieurs groupes sur cette track
luckperms.command.track.error-ambiguous=Impossible de déterminer sa position
luckperms.command.track.already-contains={0} contient déjà {1}
luckperms.command.track.doesnt-contain={0} ne contient pas {1}
luckperms.command.log.load-error=Les logs n''ont pas pu être chargés
luckperms.command.log.invalid-page=Numéro de page invalide
luckperms.command.log.invalid-page-range=Veuillez saisir une valeur comprise entre {0} et {1}
luckperms.command.log.empty=Aucune entrée à afficher
luckperms.command.log.notify.error-console=Impossible d''activer/désactiver les notifications pour la console
luckperms.command.log.notify.enabled-term=Activé
luckperms.command.log.notify.disabled-term=Desactivé
luckperms.command.log.notify.changed-state={0} résultat de connexion
luckperms.command.log.notify.already-on=Vous recevez déjà les notifications
luckperms.command.log.notify.already-off=Vous ne recevez pas les notifications actuellement
luckperms.command.log.notify.invalid-state=État inconnu. {0} ou {1} attendu
luckperms.command.log.show.search=Affichage des actions récentes pour la requête {0}
luckperms.command.log.show.recent=Affichage des actions récentes
luckperms.command.log.show.by=Affichage des actions récentes par {0}
luckperms.command.log.show.history=Affichage des actions récentes pour {0} {1}
luckperms.command.export.error-term=Erreur
luckperms.command.export.already-running=Un autre processus d''exportation est déjà en cours
luckperms.command.export.file.already-exists=Le fichier {0} existe déjà
luckperms.command.export.file.not-writable=Le fichier {0} ne peut pas être écrit
luckperms.command.export.file.success=Exportation vers {0} réussie
luckperms.command.export.file-unexpected-error-writing=Une erreur inattendue s''est produite lors de l''écriture dans le fichier
luckperms.command.export.web.export-code=Code d''export
luckperms.command.export.web.import-command-description=Utilisez la commande suivante pour importer
luckperms.command.import.term=Importation
luckperms.command.import.error-term=Erreur
luckperms.command.import.already-running=Un autre processus d''importation est déjà en cours
luckperms.command.import.file.doesnt-exist=Le fichier {0} n''existe pas
luckperms.command.import.file.not-readable=Le fichier {0} n''est pas lisible
luckperms.command.import.file.unexpected-error-reading=Une erreur inattendue s''est produite lors de la lecture du fichier d''importation
luckperms.command.import.file.correct-format=est-ce le bon format ?
luckperms.command.import.web.unable-to-read=Impossible de lire les données en utilisant le code donné
luckperms.command.import.progress.percent={0}% effectué
luckperms.command.import.progress.operations={0}/{1} opérations terminées
luckperms.command.import.starting=Démarrage du processus d''importation
luckperms.command.import.completed=TERMINÉ
luckperms.command.import.duration=a pris {0} secondes
luckperms.command.bulkupdate.must-use-console=La commande de mise à jour en masse ne peut être utilisée que depuis la console
luckperms.command.bulkupdate.invalid-data-type=Type invalide, {0} attendu
luckperms.command.bulkupdate.invalid-constraint=Contrainte invalide {0}
luckperms.command.bulkupdate.invalid-constraint-format=Les contraintes doivent être au format {0}
luckperms.command.bulkupdate.invalid-comparison=Opérateur de comparaison invalide {0}
luckperms.command.bulkupdate.invalid-comparison-format=Un des suivants était attendu \: {0}
luckperms.command.bulkupdate.queued=L''opération de mise à jour a été mise en attente
luckperms.command.bulkupdate.confirm=Faites {0} pour exécuter la mise à jour
luckperms.command.bulkupdate.unknown-id=L''opération avec l''id {0} n''existe pas ou a expiré
luckperms.command.bulkupdate.starting=Exécution d''une mise à jour de masse
luckperms.command.bulkupdate.success=Mise à jour de masse complétée avec succès
luckperms.command.bulkupdate.success.statistics.nodes=Nombre total de nœuds affectés
luckperms.command.bulkupdate.success.statistics.users=Nombre total d''utilisateurs affectés
luckperms.command.bulkupdate.success.statistics.groups=Nombre total de groupes affectés
luckperms.command.bulkupdate.failure=Echec de la mise à jour de masse, vérifiez la console pour les erreurs
luckperms.command.update-task.request=Une tâche de mise à jour a été demandée, veuillez patienter
luckperms.command.update-task.complete=Tâche de mise à jour terminée
luckperms.command.update-task.push.attempting=Tentative d''envoi vers les autres serveurs
luckperms.command.update-task.push.complete=Les autres serveurs ont été notifiés via {0} avec succès
luckperms.command.update-task.push.error=Erreur lors de l''envoi des modifications vers les autres serveurs
luckperms.command.update-task.push.error-not-setup=Impossible d''envoyer les modifications vers mes autres serveurs, car un service de messagerie n''a pas été configuré
luckperms.command.reload-config.success=Le fichier de configuration a été rechargé
luckperms.command.reload-config.restart-note=certaines options ne seront appliquées qu''après le redémarrage du serveur
luckperms.command.translations.searching=Recherche de traductions disponibles, veuillez patienter...
luckperms.command.translations.searching-error=Impossible d''obtenir la liste des traductions disponibles
luckperms.command.translations.installed-translations=Traductions installées
luckperms.command.translations.available-translations=Traductions disponibles
luckperms.command.translations.percent-translated={0}% traduits
luckperms.command.translations.translations-by=par
luckperms.command.translations.installing=Installation des traductions, veuillez patienter...
luckperms.command.translations.download-error=Impossible de télécharger la traduction {0}
luckperms.command.translations.installing-specific=Installation de la langue {0}...
luckperms.command.translations.install-complete=Installation terminée
luckperms.command.translations.download-prompt=Utilisez {0} pour télécharger et installer les versions à jour de ces traductions fournies par la communauté
luckperms.command.translations.download-override-warning=Veuillez noter que cela remplacera toutes les modifications que vous avez apportées à ces langues
luckperms.usage.user.description=Un ensemble de commandes pour gérer les utilisateurs dans LuckPerms. (un ''utilisateur'' dans LuckPerms est juste un joueur, et peut se référer à un UUID ou à pseudo)
luckperms.usage.group.description=Un ensemble de commandes pour gérer les groupes dans LuckPerms. Les groupes ne sont que des ensembles de permissions qui peuvent être attribuées aux utilisateurs. Les nouveaux groupes sont faits en utilisant la commande ''creategroup''.
luckperms.usage.track.description=Un ensemble de commandes pour gérer les tracks dans LuckPerms. Les tacks sont un ensemble ordonné de groupes qui peuvent être utilisés pour définir des promotions et des démotions.
luckperms.usage.log.description=Un ensemble de commandes pour gérer les fonctionnalités de journalisation dans LuckPerms.
luckperms.usage.sync.description=Recharge toutes les donnés depuis le stockage du plugin dans la mémoire, et applique les changements détectés.
luckperms.usage.info.description=Affiche des informations générales sur l''instance de plugin active.
luckperms.usage.editor.description=Crée une nouvelle session d''éditeur web
luckperms.usage.editor.argument.type=les types à charger dans l''éditeur. (''all'', ''users'' ou ''groups'')
luckperms.usage.editor.argument.filter=permissions par lesquelles filtrer les entrées utilisateur
luckperms.usage.verbose.description=Contrôle le système de contrôle détaillé des permissions des plugins.
luckperms.usage.verbose.argument.action=choisir d''activer/désactiver la connexion, ou d''envoyer le résultat de connexion
luckperms.usage.verbose.argument.filter=le filtre à appliquer aux entrées
luckperms.usage.verbose.argument.commandas=le joueur/la commande à exécuter
luckperms.usage.tree.description=Génère une vue arborescente de toutes les permissions connues de LuckPerms.
luckperms.usage.tree.argument.scope=la racine de l''arborescence. spécifier "." pour inclure toutes les permissions
luckperms.usage.tree.argument.player=le nom d''un joueur en ligne à vérifier
luckperms.usage.search.description=Recherche tous les utilisateurs/groupes avec une permission spécifique
luckperms.usage.search.argument.permission=la permission à rechercher
luckperms.usage.search.argument.page=la page à afficher
luckperms.usage.network-sync.description=Synchroniser les modifications dans le stockage et demander à tous les autres serveurs du réseau de faire la même chose
luckperms.usage.import.description=Importe des données depuis un fichier d''exportation (précédemment créé)
luckperms.usage.import.argument.file=le fichier depuis lequel importer
luckperms.usage.import.argument.replace=remplacer les données existantes au lieu de les fusionner
luckperms.usage.import.argument.upload=importer les données à partir d''une précédente exportation
luckperms.usage.export.description=Exporte toutes les données de permissions vers un fichier ''export''. Il peut être réimporté ultérieurement.
luckperms.usage.export.argument.file=le fichier vers lequel exporter
luckperms.usage.export.argument.without-users=exclure les utilisateurs de l''exportation
luckperms.usage.export.argument.without-groups=exclure les groupes lors de l''exportation
luckperms.usage.export.argument.upload=Importer toutes les permissions sur l''éditeur web. Peut être importé plus tard.
luckperms.usage.reload-config.description=Recharger certaines des options de configuration
luckperms.usage.bulk-update.description=Exécute les requêtes de modification de masse sur toutes les données
luckperms.usage.bulk-update.argument.data-type=le type de données à modifier. (''all'', ''users'' ou ''groups'')
luckperms.usage.bulk-update.argument.action=l''action à effectuer sur les données. (''update'' ou ''delete'')
luckperms.usage.bulk-update.argument.action-field=le champ sur lequel agir. uniquement requis pour ''update''. (''permission'', ''server'' ou ''world'')
luckperms.usage.bulk-update.argument.action-value=la valeur à remplacer. uniquement nécessaire pour ''update''.
luckperms.usage.bulk-update.argument.constraint=les contraintes requises pour la mise à jour
luckperms.usage.translations.description=Gérer les traductions
luckperms.usage.translations.argument.install=sous-commande pour installer les traductions
luckperms.usage.apply-edits.description=Applique les modifications des permissions effectuées à partir de l''éditeur web
luckperms.usage.apply-edits.argument.code=le code unique des données
luckperms.usage.apply-edits.argument.target=à qui appliquer les données
luckperms.usage.create-group.description=Créer un nouveau groupe
luckperms.usage.create-group.argument.name=le nom du groupe
luckperms.usage.create-group.argument.weight=le poids du groupe
luckperms.usage.create-group.argument.display-name=Le noms à afficher pour ce groupe
luckperms.usage.delete-group.description=Supprimer un groupe
luckperms.usage.delete-group.argument.name=le nom du groupe
luckperms.usage.list-groups.description=Lister tous les groupes sur la plateforme
luckperms.usage.create-track.description=Créer une nouvelle track
luckperms.usage.create-track.argument.name=le nom de la track
luckperms.usage.delete-track.description=Supprimer une track
luckperms.usage.delete-track.argument.name=le nom de la track
luckperms.usage.list-tracks.description=Lister toutes les tracks sur la plateforme
luckperms.usage.user-info.description=Affiche les informations de l''utilisateur
luckperms.usage.user-switchprimarygroup.description=Change le groupe principal de l''utilisateur
luckperms.usage.user-switchprimarygroup.argument.group=le groupe à utiliser
luckperms.usage.user-promote.description=Promeut l''utilisateur sur la track
luckperms.usage.user-promote.argument.track=la track pour laquelle promouvoir l''utilisateur
luckperms.usage.user-promote.argument.context=les contextes dans lesquels promouvoir l''utilisateur
luckperms.usage.user-promote.argument.dont-add-to-first=ne promouvoir l''utilisateur que s''il est déjà sur la track
luckperms.usage.user-demote.description=Rétrograde l''utilisateur vers le bas d''une track
luckperms.usage.user-demote.argument.track=la track dans laquelle rétrograder l''utilisateur
luckperms.usage.user-demote.argument.context=les contextes dans lesquels rétrograder l''utilisateur
luckperms.usage.user-demote.argument.dont-remove-from-first=empêcher l''utilisateur d''être retiré du premier groupe
luckperms.usage.user-clone.description=Cloner l''utilisateur
luckperms.usage.user-clone.argument.user=le pseudo/uuid de l''utilisateur vers lequel cloner
luckperms.usage.group-info.description=Donne des informations sur le groupe
luckperms.usage.group-listmembers.description=Afficher les utilisateurs/groupes qui héritent de ce groupe
luckperms.usage.group-listmembers.argument.page=la page à afficher
luckperms.usage.group-setweight.description=Définir le poids des groupes
luckperms.usage.group-setweight.argument.weight=le poids à définir
luckperms.usage.group-set-display-name.description=Définir les noms d''affichage des groupes
luckperms.usage.group-set-display-name.argument.name=le nom à définir
luckperms.usage.group-set-display-name.argument.context=les contextes dans lesquels définir le nom
luckperms.usage.group-rename.description=Renommer le groupe
luckperms.usage.group-rename.argument.name=le nouveau nom
luckperms.usage.group-clone.description=Dupliquer le groupe
luckperms.usage.group-clone.argument.name=le nom du groupe vers lequel dupliquer
luckperms.usage.holder-editor.description=Ouvre l''éditeur de permissions web
luckperms.usage.holder-showtracks.description=Liste les tracks sur lesquelles l''objet est
luckperms.usage.holder-clear.description=Supprime toutes les permissions, les parents et les métadonnées
luckperms.usage.holder-clear.argument.context=les contextes par lesquels filtrer
luckperms.usage.permission.description=Modifier les permissions
luckperms.usage.parent.description=Modifier les héritages
luckperms.usage.meta.description=Modifier les valeurs des métadonnées
luckperms.usage.permission-info.description=Liste les nœuds de permission de l''objet
luckperms.usage.permission-info.argument.page=la page à afficher
luckperms.usage.permission-info.argument.sort-mode=comment trier les entrées
luckperms.usage.permission-set.description=Définit une permission pour l''objet
luckperms.usage.permission-set.argument.node=le noeud de permission à définir
luckperms.usage.permission-set.argument.value=la valeur du noeud
luckperms.usage.permission-set.argument.context=les contextes dans lesquels ajouter la permission
luckperms.usage.permission-unset.description=Retire une permission pour l''objet
luckperms.usage.permission-unset.argument.node=le noeud de permission à retirer
luckperms.usage.permission-unset.argument.context=les contextes dans lesquels retirer la permission
luckperms.usage.permission-settemp.description=Définit temporairement une permission pour l''objet
luckperms.usage.permission-settemp.argument.node=le noeud de permission à définir
luckperms.usage.permission-settemp.argument.value=la valeur du noeud
luckperms.usage.permission-settemp.argument.duration=la durée jusqu''à l''expiration du noeud de permission
luckperms.usage.permission-settemp.argument.temporary-modifier=comment la permission temporaire devrait être appliquée
luckperms.usage.permission-settemp.argument.context=les contextes dans lesquels ajouter la permission
luckperms.usage.permission-unsettemp.description=Retire une permission temporaire pour l''objet
luckperms.usage.permission-unsettemp.argument.node=le noeud de permission à retirer
luckperms.usage.permission-unsettemp.argument.duration=la durée à soustraire
luckperms.usage.permission-unsettemp.argument.context=les contextes dans lesquels retirer la permission
luckperms.usage.permission-check.description=Vérifie si l''objet a un certain nœud de permission
luckperms.usage.permission-check.argument.node=le nœud de permission à vérifier
luckperms.usage.permission-clear.description=Efface toutes les permissions
luckperms.usage.permission-clear.argument.context=les contextes par lesquels filtrer
luckperms.usage.parent-info.description=Liste les groupes dont l''objet hérite
luckperms.usage.parent-info.argument.page=la page à afficher
luckperms.usage.parent-info.argument.sort-mode=comment trier les entrées
luckperms.usage.parent-set.description=Retire tous les autres groupes dont l''objet hérite déjà et les ajoute à celui donné
luckperms.usage.parent-set.argument.group=le groupe à définir
luckperms.usage.parent-set.argument.context=les contextes dans lesquels définir le groupe
luckperms.usage.parent-add.description=Définit un autre groupe pour que l''objet hérite de ses permissions
luckperms.usage.parent-add.argument.group=le groupe duquel hériter
luckperms.usage.parent-add.argument.context=les contextes dans lesquels le groupe va hériter
luckperms.usage.parent-remove.description=Supprime une règle d''héritage précédemment définie
luckperms.usage.parent-remove.argument.group=le groupe à retirer
luckperms.usage.parent-remove.argument.context=les contextes dans lesquels retirer le groupe
luckperms.usage.parent-set-track.description=Retire tous les autres groupes dont l''objet hérite déjà sur la track donnée et les ajoute à celui donné
luckperms.usage.parent-set-track.argument.track=la track à définir
luckperms.usage.parent-set-track.argument.group=le groupe à définir, ou un nombre relatif à la position du groupe sur la track donnée
luckperms.usage.parent-set-track.argument.context=les contextes dans lesquels définir le groupe
luckperms.usage.parent-add-temp.description=Définit un autre groupe pour que l''objet hérite temporairement de ses permissions
luckperms.usage.parent-add-temp.argument.group=le groupe duquel hériter
luckperms.usage.parent-add-temp.argument.duration=la durée de l''appartenance au groupe
luckperms.usage.parent-add-temp.argument.temporary-modifier=comment la permission temporaire devrait être appliquée
luckperms.usage.parent-add-temp.argument.context=les contextes dans lesquels le groupe va hériter
luckperms.usage.parent-remove-temp.description=Supprime une règle d''héritage temporaire précédemment définie
luckperms.usage.parent-remove-temp.argument.group=le groupe à retirer
luckperms.usage.parent-remove-temp.argument.duration=la durée à soustraire
luckperms.usage.parent-remove-temp.argument.context=les contextes dans lesquels retirer le groupe
luckperms.usage.parent-clear.description=Efface tous les parents
luckperms.usage.parent-clear.argument.context=les contextes par lesquels filtrer
luckperms.usage.parent-clear-track.description=Efface tous les parents sur une track donnée
luckperms.usage.parent-clear-track.argument.track=la track à retirer
luckperms.usage.parent-clear-track.argument.context=les contextes par lesquels filtrer
luckperms.usage.meta-info.description=Afficher toutes les méta du chat
luckperms.usage.meta-set.description=Définit une valeur méta
luckperms.usage.meta-set.argument.key=la clé à définir
luckperms.usage.meta-set.argument.value=la valeur à définir
luckperms.usage.meta-set.argument.context=les contextes dans lesquels ajouter la paire de métadonnées
luckperms.usage.meta-unset.description=Retire une valeur méta
luckperms.usage.meta-unset.argument.key=la clé à retirer
luckperms.usage.meta-unset.argument.context=Les contextes dans lesquels retirer la paire de métadonnées
luckperms.usage.meta-settemp.description=Définit temporairement une valeur méta
luckperms.usage.meta-settemp.argument.key=Clé à définir
luckperms.usage.meta-settemp.argument.value=Valeur à définir
luckperms.usage.meta-settemp.argument.duration=Durée avant que la valeur de la métadonnée expire
luckperms.usage.meta-settemp.argument.context=les contextes dans lesquels ajouter la paire de métadonnées
luckperms.usage.meta-unsettemp.description=Retire une valeur de métadonnée temporaire
luckperms.usage.meta-unsettemp.argument.key=la clé à retirer
luckperms.usage.meta-unsettemp.argument.context=les contextes dans lesquels retirer la paire de métadonnées
luckperms.usage.meta-addprefix.description=Ajoute un préfixe
luckperms.usage.meta-addprefix.argument.priority=la priorité à laquelle ajouter le préfixe
luckperms.usage.meta-addprefix.argument.prefix=la valeur du préfixe
luckperms.usage.meta-addprefix.argument.context=les contextes dans lesquels ajouter le préfixe
luckperms.usage.meta-addsuffix.description=Ajoute un suffixe
luckperms.usage.meta-addsuffix.argument.priority=la priorité à laquelle ajouter le suffixe
luckperms.usage.meta-addsuffix.argument.suffix=la valeur du suffixe
luckperms.usage.meta-addsuffix.argument.context=les contextes dans lesquels ajouter le suffixe
luckperms.usage.meta-setprefix.description=Définit un préfixe
luckperms.usage.meta-setprefix.argument.priority=les contextes pour lesquels ajouter le préfixe
luckperms.usage.meta-setprefix.argument.prefix=la valeur du préfixe
luckperms.usage.meta-setprefix.argument.context=les contextes dans lesquels définir le préfixe
luckperms.usage.meta-setsuffix.description=Définit un suffixe
luckperms.usage.meta-setsuffix.argument.priority=la priorité à laquelle définir le suffixe
luckperms.usage.meta-setsuffix.argument.suffix=la valeur du suffixe
luckperms.usage.meta-setsuffix.argument.context=les contextes dans lesquels définir le suffixe
luckperms.usage.meta-removeprefix.description=Retire un préfixe
luckperms.usage.meta-removeprefix.argument.priority=la priorité à laquelle retirer le préfixe
luckperms.usage.meta-removeprefix.argument.prefix=la valeur du préfixe
luckperms.usage.meta-removeprefix.argument.context=les contextes dans lesquels retirer le préfixe
luckperms.usage.meta-removesuffix.description=Retire un suffixe
luckperms.usage.meta-removesuffix.argument.priority=la priorité à laquelle retirer le suffixe
luckperms.usage.meta-removesuffix.argument.suffix=la valeur du suffixe
luckperms.usage.meta-removesuffix.argument.context=les contextes dans lesquels retirer le suffixe
luckperms.usage.meta-addtemp-prefix.description=Ajoute un préfixe temporairement
luckperms.usage.meta-addtemp-prefix.argument.priority=la priorité à laquelle ajouter le préfixe
luckperms.usage.meta-addtemp-prefix.argument.prefix=la valeur du préfixe
luckperms.usage.meta-addtemp-prefix.argument.duration=la durée avant que le préfixe expire
luckperms.usage.meta-addtemp-prefix.argument.context=les contextes dans lesquels ajouter le préfixe
luckperms.usage.meta-addtemp-suffix.description=Ajoute un suffixe temporairement
luckperms.usage.meta-addtemp-suffix.argument.priority=la priorité à laquelle ajouter le suffixe
luckperms.usage.meta-addtemp-suffix.argument.suffix=la valeur du suffixe
luckperms.usage.meta-addtemp-suffix.argument.duration=la durée avant que le suffixe expire
luckperms.usage.meta-addtemp-suffix.argument.context=les contextes dans lesquels ajouter le suffixe
luckperms.usage.meta-settemp-prefix.description=Définit un préfixe temporairement
luckperms.usage.meta-settemp-prefix.argument.priority=la priorité à laquelle définir le préfixe
luckperms.usage.meta-settemp-prefix.argument.prefix=la valeur du préfixe
luckperms.usage.meta-settemp-prefix.argument.duration=la durée avant que le préfixe expire
luckperms.usage.meta-settemp-prefix.argument.context=les contextes dans lesquels définir le préfixe
luckperms.usage.meta-settemp-suffix.description=Définit un suffixe temporairement
luckperms.usage.meta-settemp-suffix.argument.priority=la priorité à laquelle définir le suffixe
luckperms.usage.meta-settemp-suffix.argument.suffix=la valeur du suffixe
luckperms.usage.meta-settemp-suffix.argument.duration=la durée avant que le suffixe expire
luckperms.usage.meta-settemp-suffix.argument.context=les contextes dans lesquels définir le suffixe
luckperms.usage.meta-removetemp-prefix.description=Retire un préfixe temporaire
luckperms.usage.meta-removetemp-prefix.argument.priority=la priorité à laquelle retirer le préfixe
luckperms.usage.meta-removetemp-prefix.argument.prefix=la valeur du préfixe
luckperms.usage.meta-removetemp-prefix.argument.context=les contextes dans lesquels retirer le préfixe
luckperms.usage.meta-removetemp-suffix.description=Retire un préfixe temporaire
luckperms.usage.meta-removetemp-suffix.argument.priority=la priorité à laquelle retirer le suffixe
luckperms.usage.meta-removetemp-suffix.argument.suffix=la valeur du suffixe
luckperms.usage.meta-removetemp-suffix.argument.context=les contextes dans lesquels retirer le suffixe
luckperms.usage.meta-clear.description=Efface toutes les métadonnées
luckperms.usage.meta-clear.argument.type=le type de métadonnée à retirer
luckperms.usage.meta-clear.argument.context=les contextes par lesquels filtrer
luckperms.usage.track-info.description=Donne des informations sur la track
luckperms.usage.track-editor.description=Ouvre l''éditeur web des permissions
luckperms.usage.track-append.description=Ajoute un groupe à la fin de la track
luckperms.usage.track-append.argument.group=le groupe à ajouter
luckperms.usage.track-insert.description=Insère un groupe à une position donnée dans la track
luckperms.usage.track-insert.argument.group=le groupe à insérer
luckperms.usage.track-insert.argument.position=la position à laquelle insérer le groupe (la première position de la track est 1)
luckperms.usage.track-remove.description=Retire un groupe de la track
luckperms.usage.track-remove.argument.group=le groupe à retirer
luckperms.usage.track-clear.description=Efface les groupes de la track
luckperms.usage.track-rename.description=Renommer la track
luckperms.usage.track-rename.argument.name=le nouveau nom
luckperms.usage.track-clone.description=Dupliquer la track
luckperms.usage.track-clone.argument.name=le nom de la track vers laquelle dupliquer
luckperms.usage.log-recent.description=Voir les actions récentes
luckperms.usage.log-recent.argument.user=le pseudo/uuid de l''utilisateur par lequel filtrer
luckperms.usage.log-recent.argument.page=le numéro de page à afficher
luckperms.usage.log-search.description=Rechercher une entrée dans les logs
luckperms.usage.log-search.argument.query=la requête par laquelle rechercher
luckperms.usage.log-search.argument.page=le numéro de page à afficher
luckperms.usage.log-notify.description=Activer/désactiver les notifications de log
luckperms.usage.log-notify.argument.toggle=activer ou désactiver
luckperms.usage.log-user-history.description=Voir l''historique d''un utilisateur
luckperms.usage.log-user-history.argument.user=le pseudo/uuid de l''utilisateur
luckperms.usage.log-user-history.argument.page=le numéro de page à afficher
luckperms.usage.log-group-history.description=Voir l''historique d''un groupe
luckperms.usage.log-group-history.argument.group=le nom du groupe
luckperms.usage.log-group-history.argument.page=le numéro de page à afficher
luckperms.usage.log-track-history.description=Voir l''historique d''une track
luckperms.usage.log-track-history.argument.track=le nom de la track
luckperms.usage.log-track-history.argument.page=le numéro de page à afficher
luckperms.usage.sponge.description=Modifier les données supplémentaires de Sponge
luckperms.usage.sponge.argument.collection=la collection à consulter
luckperms.usage.sponge.argument.subject=le sujet à modifier
luckperms.usage.sponge-permission-info.description=Affiche les informations sur les permissions du sujet
luckperms.usage.sponge-permission-info.argument.contexts=les contextes par lesquels filtrer
luckperms.usage.sponge-permission-set.description=Définit une permission pour le sujet
luckperms.usage.sponge-permission-set.argument.node=le noeud de permission à définir
luckperms.usage.sponge-permission-set.argument.tristate=la valeur à laquelle définir la permission
luckperms.usage.sponge-permission-set.argument.contexts=les contextes dans lesquels définir la permission
luckperms.usage.sponge-permission-clear.description=Efface les permissions des sujets
luckperms.usage.sponge-permission-clear.argument.contexts=les contextes dans lesquels effacer les permissions
luckperms.usage.sponge-parent-info.description=Affiche les informations sur les groupes parents du sujet
luckperms.usage.sponge-parent-info.argument.contexts=les contextes par lesquels filtrer
luckperms.usage.sponge-parent-add.description=Ajoute un parent au sujet
luckperms.usage.sponge-parent-add.argument.collection=la collection du sujet dans laquelle est le sujet parent
luckperms.usage.sponge-parent-add.argument.subject=le nom du sujet parent
luckperms.usage.sponge-parent-add.argument.contexts=les contextes dans lesquels ajouter le parent
luckperms.usage.sponge-parent-remove.description=Supprime un parent du sujet
luckperms.usage.sponge-parent-remove.argument.collection=la collection du sujet dans laquelle est le sujet parent
luckperms.usage.sponge-parent-remove.argument.subject=le nom du sujet parent
luckperms.usage.sponge-parent-remove.argument.contexts=les contextes dans lesquels retirer le parent
luckperms.usage.sponge-parent-clear.description=Efface les parents des sujets
luckperms.usage.sponge-parent-clear.argument.contexts=les contextes dans lesquels effacer les parents
luckperms.usage.sponge-option-info.description=Affiche les informations sur les options du sujet
luckperms.usage.sponge-option-info.argument.contexts=les contextes par lesquels filtrer
luckperms.usage.sponge-option-set.description=Définit une option pour le sujet
luckperms.usage.sponge-option-set.argument.key=la clé à définir
luckperms.usage.sponge-option-set.argument.value=la valeur pour laquelle définir la clé
luckperms.usage.sponge-option-set.argument.contexts=les contextes dans lesquels l''option sera définie
luckperms.usage.sponge-option-unset.description=Retirer une option pour le sujet
luckperms.usage.sponge-option-unset.argument.key=la clé à retirer
luckperms.usage.sponge-option-unset.argument.contexts=les contextes dans lesquels la clé sera retirée
luckperms.usage.sponge-option-clear.description=Efface les options des sujets
luckperms.usage.sponge-option-clear.argument.contexts=les contextes dans lesquels effacer les options
