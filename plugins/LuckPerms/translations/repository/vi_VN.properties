luckperms.logs.actionlog-prefix=NHẬT KÍ
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=XUẤT
luckperms.commandsystem.available-commands=Dùng {0} để xem mã lệnh có sẵn
luckperms.commandsystem.command-not-recognised=Lệnh không được ghi nhận
luckperms.commandsystem.no-permission=Bạn không có quyền để thực hiện lệnh này\!
luckperms.commandsystem.no-permission-subcommands=Bạn không có quyền để thực hiện mã lệnh con này
luckperms.commandsystem.already-executing-command=Một lệnh khác đang được thực thi, đang chờ lệnh được hoàn thành...
luckperms.commandsystem.usage.sub-commands-header=Lệnh con
luckperms.commandsystem.usage.usage-header=Sử dụng lệnh
luckperms.commandsystem.usage.arguments-header=Tham số
luckperms.first-time.no-permissions-setup=C<PERSON> vẻ như chưa có quyền nào được thiết lập\!
luckperms.first-time.use-console-to-give-access=<PERSON><PERSON><PERSON><PERSON><PERSON> khi có thể sử dụng bất kỳ lệnh LuckPerms nào trong trò chơi, bạn cần sử dụng bảng điều khiển để cấp quyền truy cập cho mình
luckperms.first-time.console-command-prompt=Mở bảng điều khiển của bạn và chạy
luckperms.first-time.next-step=Sau khi hoàn thành việc này, bạn có thể bắt đầu xác định các nhiệm vụ cấp quyền và nhóm của mình
luckperms.first-time.wiki-prompt=Không biết bắt đầu từ đâu? Xem tại đây\: {0}
luckperms.login.try-again=Vui lòng thử lại sau
luckperms.login.loading-database-error=Đã xảy ra lỗi trong dữ liệu khi tải dữ liệu quyền cấp
luckperms.login.server-admin-check-console-errors=Nếu bạn là quản trị viên của máy chủ, vui lòng kiểm tra bảng điều khiển xem có lỗi nào không
luckperms.login.server-admin-check-console-info=Vui lòng kiểm tra bảng điều khiển của máy chủ để biết thêm thông tin
luckperms.login.data-not-loaded-at-pre=Dữ liệu quyền cho người dùng của bạn không được xử lí trong giai đoạn đăng nhập trước
luckperms.login.unable-to-continue=không thể tiếp tục được
luckperms.login.craftbukkit-offline-mode-error=điều này có thể do xung đột giữa CraftBukkit và chế độ trực tuyến
luckperms.login.unexpected-error=Đã xảy ra lỗi không mong muốn trong khi thiết lập dữ liệu quyền cấp của bạn
luckperms.opsystem.disabled=Hệ thống Vanilla OP bị tắt trên máy chủ này
luckperms.opsystem.sponge-warning=Xin lưu ý rằng trạng thái của Máy chủ không ảnh hưởng đến việc kiểm tra quyền của Sponge khi plugin cấp quyền được cài đặt, nên bạn phải chỉnh sửa dữ liệu người dùng trực tiếp
luckperms.duration.unit.years.plural={0} năm
luckperms.duration.unit.years.singular={0} năm
luckperms.duration.unit.years.short={0}y
luckperms.duration.unit.months.plural={0} tháng
luckperms.duration.unit.months.singular={0} tháng
luckperms.duration.unit.months.short={0}mo
luckperms.duration.unit.weeks.plural={0} tuần
luckperms.duration.unit.weeks.singular={0} tuần
luckperms.duration.unit.weeks.short={0}w
luckperms.duration.unit.days.plural={0} ngày
luckperms.duration.unit.days.singular={0} ngày
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} giờ
luckperms.duration.unit.hours.singular={0} giờ
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} phút
luckperms.duration.unit.minutes.singular={0} phút
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} giây
luckperms.duration.unit.seconds.singular={0} giây
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since=cách đây {0}
luckperms.command.misc.invalid-code=Mã không hợp lệ
luckperms.command.misc.response-code-key=mã phản hồi
luckperms.command.misc.error-message-key=tin-nhắn
luckperms.command.misc.bytebin-unable-to-communicate=Không thể tương tác với bytebin
luckperms.command.misc.webapp-unable-to-communicate=Không thể tương tác với ứng dụng web
luckperms.command.misc.check-console-for-errors=Vui lòng nhìn vào bảng điều khiển để xem lỗi
luckperms.command.misc.file-must-be-in-data=Tệp tin {0} phải là tệp trực tiếp với dữ liệu thư mục
luckperms.command.misc.wait-to-finish=Vui lòng đợi cho đến khi hoàn thành rồi thử lại
luckperms.command.misc.invalid-priority=Mã ưu tiên {0} không hợp lệ
luckperms.command.misc.expected-number=Cần một con số
luckperms.command.misc.date-parse-error=Không thể phân tích cú pháp {0}
luckperms.command.misc.date-in-past-error=Bạn không thể đặt thời gian trong quá khứ\!
luckperms.command.misc.page=trang {0} trong {1}
luckperms.command.misc.page-entries={0} mục
luckperms.command.misc.none=Không có
luckperms.command.misc.loading.error.unexpected=Đã xảy ra lỗi không mong muốn
luckperms.command.misc.loading.error.user=Người dùng không xử lí được
luckperms.command.misc.loading.error.user-specific=Không thể xử lí người dùng xác định {0}
luckperms.command.misc.loading.error.user-not-found=Không thể tìm thấy người dùng có tên {0}
luckperms.command.misc.loading.error.user-save-error=Có một lỗi xảy ra khi lưu dữ liệu người dùng cho {0}
luckperms.command.misc.loading.error.user-not-online=Người dùng {0} không ở trực tuyến
luckperms.command.misc.loading.error.user-invalid={0} không phải là tên người dùng hoặc UUID hợp lệ
luckperms.command.misc.loading.error.user-not-uuid=Người dùng xác định {0} có UUID không hợp lệ
luckperms.command.misc.loading.error.group=Nhóm chưa được xử lí
luckperms.command.misc.loading.error.all-groups=Không thể xử lí tất cả các nhóm
luckperms.command.misc.loading.error.group-not-found=Không thể tìm thấy nhóm có tên {0}
luckperms.command.misc.loading.error.group-save-error=Có một lỗi xảy ra khi lưu dữ liệu nhóm cho {0}
luckperms.command.misc.loading.error.group-invalid={0} không phải là một tên nhóm hợp lệ
luckperms.command.misc.loading.error.track=Thang chưa được xử lí
luckperms.command.misc.loading.error.all-tracks=Không thể xử lí tất cả các thang
luckperms.command.misc.loading.error.track-not-found=Không thể tìm thấy thang có tên {0}
luckperms.command.misc.loading.error.track-save-error=Có một lỗi đã xảy ra khi lưu dữ liệu thang cho {0}
luckperms.command.misc.loading.error.track-invalid={0} không phải là một tên thang hợp lệ
luckperms.command.editor.no-match=Không thể mở trình chỉnh sửa, không có gì trùng với kiểu mong muốn
luckperms.command.editor.start=Đang chuẩn bị bản chỉnh sửa mới, xin đợi...
luckperms.command.editor.url=Nhấp vào đường dẫn phía dưới để mở trình chỉnh sửa
luckperms.command.editor.unable-to-communicate=Không thể kết nối với bản chỉnh sửa
luckperms.command.editor.apply-edits.success=Dữ liệu trong trình chỉnh sửa web được áp dụng cho {0} {1} thành công
luckperms.command.editor.apply-edits.success-summary={0} {1} và {2} {3}
luckperms.command.editor.apply-edits.success.additions=phần bổ sung
luckperms.command.editor.apply-edits.success.additions-singular=phần bổ sung
luckperms.command.editor.apply-edits.success.deletions=mục xóa
luckperms.command.editor.apply-edits.success.deletions-singular=mục xóa
luckperms.command.editor.apply-edits.no-changes=Không có thay đổi nào được áp dụng từ trình chỉnh sửa web, dữ liệu trả về không chứa bất kỳ chỉnh sửa nào
luckperms.command.editor.apply-edits.unknown-type=Không thể áp dụng bản chỉnh sửa cho loại đối tượng được xác định
luckperms.command.editor.apply-edits.unable-to-read=Không thể đọc dữ liệu bằng mã đã cho
luckperms.command.search.searching.permission=Đang tìm người dùng và nhóm với {0}
luckperms.command.search.searching.inherit=Đang tìm kiếm người dùng và nhóm kế thừa từ {0}
luckperms.command.search.result=Đã tìm thấy {0} mục từ {1} người dùng và {2} nhóm
luckperms.command.search.result.default-notice=Lưu ý\: khi tìm kiếm thành viên của nhóm mặc định, những người chơi ngoại tuyến không có quyền khác sẽ không được hiển thị\!
luckperms.command.search.showing-users=Hiển thị các mục người dùng
luckperms.command.search.showing-groups=Hiển thị các mục nhóm
luckperms.command.tree.start=Đang tạo quyền thang bậc, vui lòng đợi...
luckperms.command.tree.empty=Không thể tạo thang bậc, nên không tìm thấy kết quả
luckperms.command.tree.url=Quyền thang bậc URL
luckperms.command.verbose.invalid-filter={0} không phải là một bộ lọc hợp lệ
luckperms.command.verbose.enabled=Đang ghi bộ nhật ký {0} cho các séc khớp với {1}
luckperms.command.verbose.command-exec=Đang buộc {0} phải thực hiện lệnh {1} và báo cáo tất cả các lần kiểm tra đã thực hiện...
luckperms.command.verbose.off=Bộ ghi nhật ký {0}
luckperms.command.verbose.command-exec-complete=Thực hiện lệnh hoàn tất
luckperms.command.verbose.command.no-checks=Lệnh dược thực thi thành công, nhưng không có quyền nào được kiểm tra
luckperms.command.verbose.command.possibly-async=Đây có thể là do plugin chạy lệnh ở nền sau (async)
luckperms.command.verbose.command.try-again-manually=Bạn vẫn có thể dùng bộ nhật ký thủ công để phát hiện các việc kiểm tra như sau
luckperms.command.verbose.enabled-recording=Đang ghi lại chi tiết {0} để kiểm tra trùng khớp với {1}
luckperms.command.verbose.uploading=Ghi nhật ký {0}, tải lên kết quả...
luckperms.command.verbose.url=Kết quả bộ nhật kí URL
luckperms.command.verbose.enabled-term=kích hoạt
luckperms.command.verbose.disabled-term=tắt
luckperms.command.verbose.query-any=BẤT KÌ
luckperms.command.info.running-plugin=Ðang chạy
luckperms.command.info.platform-key=Nền tảng
luckperms.command.info.server-brand-key=Thương hiệu máy chủ
luckperms.command.info.server-version-key=Phiên bản máy chủ
luckperms.command.info.storage-key=Bộ nhớ
luckperms.command.info.storage-type-key=Dạng
luckperms.command.info.storage.meta.split-types-key=Dạng
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Đã kết nối
luckperms.command.info.storage.meta.file-size-key=Kích thước tập tin
luckperms.command.info.extensions-key=Tiện ích mở rộng
luckperms.command.info.messaging-key=Tin nhắn
luckperms.command.info.instance-key=Đối tượng
luckperms.command.info.static-contexts-key=Trường chốt
luckperms.command.info.online-players-key=Người chơi trực tuyến
luckperms.command.info.online-players-unique={0} đa dạng
luckperms.command.info.uptime-key=Thời gian hoạt động
luckperms.command.info.local-data-key=Dữ liệu cục bộ
luckperms.command.info.local-data={0} người dùng, {1} nhóm, {2} thang
luckperms.command.generic.create.success={0} đã được tạo thành công
luckperms.command.generic.create.error=Đã xảy ra lỗi khi tạo {0}
luckperms.command.generic.create.error-already-exists={0} đã tồn tại\!
luckperms.command.generic.delete.success={0} đã được xóa thành công
luckperms.command.generic.delete.error=Đã xảy ra lỗi khi xóa {0}
luckperms.command.generic.delete.error-doesnt-exist={0} không tồn tại\!
luckperms.command.generic.rename.success={0} đã được thay đổi tên đến {1}
luckperms.command.generic.clone.success={0} đã được sao lưu đến {1}
luckperms.command.generic.info.parent.title=Nhóm chính
luckperms.command.generic.info.parent.temporary-title=Nhóm chính tạm thời
luckperms.command.generic.info.expires-in=hết hạn trong
luckperms.command.generic.info.inherited-from=thừa hưởng từ
luckperms.command.generic.info.inherited-from-self=bản thân
luckperms.command.generic.show-tracks.title=Thang của {0}
luckperms.command.generic.show-tracks.empty={0} không ở trong thang nào
luckperms.command.generic.clear.node-removed={0} mẩu được gỡ bỏ
luckperms.command.generic.clear.node-removed-singular={0} mẩu được gỡ bỏ
luckperms.command.generic.clear={0} mẩu được gỡ bỏ trong trường {1}
luckperms.command.generic.permission.info.title=Quyền cấp của {0}
luckperms.command.generic.permission.info.empty={0} không có quyền nào được sắp đặt
luckperms.command.generic.permission.info.click-to-remove=Nhấp để gỡ mẩu khỏi {0}
luckperms.command.generic.permission.check.info.title=Thông tin quyền về {0}
luckperms.command.generic.permission.check.info.directly={0} có {1} được đặt về {2} ở trường {3}
luckperms.command.generic.permission.check.info.inherited={0} kế thừa từ {1} được đặt về {2} từ {3} ở trường {4}
luckperms.command.generic.permission.check.info.not-directly={0} có {1} chưa được đặt
luckperms.command.generic.permission.check.info.not-inherited={0} không kế thừa {1}
luckperms.command.generic.permission.check.result.title=Kiểm tra quyền của {0}
luckperms.command.generic.permission.check.result.result-key=Kết quả
luckperms.command.generic.permission.check.result.processor-key=Bộ xử lý
luckperms.command.generic.permission.check.result.cause-key=Lý do
luckperms.command.generic.permission.check.result.context-key=Trường
luckperms.command.generic.permission.set=Đặt {0} thành {1} cho {2} trong trường {3}
luckperms.command.generic.permission.already-has={0} đã có {1} đặt trong trường {2}
luckperms.command.generic.permission.set-temp=Đã đặt {0} thành {1} cho {2} trong khoảng thời gian {3} trong trường {4}
luckperms.command.generic.permission.already-has-temp={0} đã có {1} đặt tạm thời trong trường {2}
luckperms.command.generic.permission.unset=Đã gỡ {0} cho {1} trong trường {2}
luckperms.command.generic.permission.doesnt-have={0} không có {1} đặt trong trường {2}
luckperms.command.generic.permission.unset-temp=Đã gỡ quyền cấp tạm thời {0} cho {1} trong trường {2}
luckperms.command.generic.permission.subtract=Đã đặt {0} thành {1} cho {2} trong khoảng thời gian {3} trong trường {4}, thấp hơn {5}
luckperms.command.generic.permission.doesnt-have-temp={0} không có {1} đặt tạm thời trong trường {2}
luckperms.command.generic.permission.clear=Các quyền của {0} đã được xóa khỏi trường {1}
luckperms.command.generic.parent.info.title=Chốt chính của {0}
luckperms.command.generic.parent.info.empty={0} không có chốt chính sắp đặt
luckperms.command.generic.parent.info.click-to-remove=Nhấp để gỡ chốt chính này khỏi {0}
luckperms.command.generic.parent.add={0} bây giờ được thừa hưởng quyền cấp từ {1} trong trường {2}
luckperms.command.generic.parent.add-temp={0} bây giờ được thừa hưởng quyền cấp từ {1} trong khoảng thời gian {2} trong trường {3}
luckperms.command.generic.parent.set={0} đã có nhóm chính có sẵn bị xóa, nên được thừa hưởng từ {1} trong trường {2}
luckperms.command.generic.parent.set-track={0} đã có nhóm chính có sẵn trong thang {1} bị xóa, nên giờ được thừa hưởng từ {2} trong trường {3}
luckperms.command.generic.parent.remove={0} không còn thừa hưởng quyền cấp từ {1} trong trường {2}
luckperms.command.generic.parent.remove-temp={0} không còn thừa hưởng quyền cấp tạm thời từ {1} trong trường {2}
luckperms.command.generic.parent.subtract={0} sẽ được thừa hưởng quyền cấp từ {1} trong khoảng thời gian {2} in trong trường {3}, thấp hơn một khoảng {4}
luckperms.command.generic.parent.clear=Chốt chính của {0} đã được xóa khỏi trường {1}
luckperms.command.generic.parent.clear-track=Chốt chính của {0} trong thang {1} đã bị xóa trong trường {2}
luckperms.command.generic.parent.already-inherits={0} đã được kế thừa từ {1} trong trường {2}
luckperms.command.generic.parent.doesnt-inherit={0} không thừa hưởng từ {1} trong trường {2}
luckperms.command.generic.parent.already-temp-inherits={0} không còn thừa hưởng quyền cấp tạm thời từ {1} trong trường {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} tạm thời không kế thừa từ {1} trong trường {2}
luckperms.command.generic.chat-meta.info.title-prefix=Tiền tố của {0}
luckperms.command.generic.chat-meta.info.title-suffix=Hậu tố của {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} không có tiền tố nào
luckperms.command.generic.chat-meta.info.none-suffix={0} không có hậu tố nào
luckperms.command.generic.chat-meta.info.click-to-remove=Nhấp để gỡ {0} này khỏi {1}
luckperms.command.generic.chat-meta.already-has={0} đã có {1} {2} đặt ở mức ưu tiên {3} trong trường {4}
luckperms.command.generic.chat-meta.already-has-temp={0} đã có {1} {2} đặt ở mức ưu tiên tạm thời {3} trong trường {4}
luckperms.command.generic.chat-meta.doesnt-have={0} không có {1} {2} đặt ở mức ưu tiên {3} trong trường {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} không có {1} {2} đặt ở mức ưu tiên tạm thời {3} trong trường {4}
luckperms.command.generic.chat-meta.add={0} đã có {1} {2} đặt ở mức ưu tiên {3} trong trường {4}
luckperms.command.generic.chat-meta.add-temp={0} đã có {1} {2} đặt ở mức ưu tiên {3} trong khoảng thời gian {4} của trường {5}
luckperms.command.generic.chat-meta.remove={0} đã có {1} {2} ở mức ưu tiên {3} bị xóa trong trường {4}
luckperms.command.generic.chat-meta.remove-bulk={0} đã có tất cả {1} ở mức ưu tiên {2} bị xóa trong trường {3}
luckperms.command.generic.chat-meta.remove-temp={0} đã có {1} {2} tạm thời ở mức ưu tiên {3} bị xóa trong trường {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} đã có tất cả {1} tạm thời ở mức ưu tiên {2} bị xóa trong trường {3}
luckperms.command.generic.meta.info.title=Dữ liệu đối tượng của {0}
luckperms.command.generic.meta.info.none={0} không có đối tượng dữ liệu nào
luckperms.command.generic.meta.info.click-to-remove=Nhấp để xóa mẩu dữ liệu này khỏi {0}
luckperms.command.generic.meta.already-has={0} đã có khóa dữ liệu {1} đặt thành {2} trong trường {3}
luckperms.command.generic.meta.already-has-temp={0} đã có khóa dữ liệu {1} tạm thời đặt thành {2} trong trường {3}
luckperms.command.generic.meta.doesnt-have={0} không có khóa dữ liệu {1} trong trường {2}
luckperms.command.generic.meta.doesnt-have-temp={0} không có khóa dữ liệu {1} đặt tạm thời trong trường {2}
luckperms.command.generic.meta.set=Đã đặt khóa đối tượng dữ liệu {0} thành {1} cho {2} trong trường {3}
luckperms.command.generic.meta.set-temp=Đã đặt khóa meta {0} thành {1} cho {2} trong khoảng thời gian {3} của trường {4}
luckperms.command.generic.meta.unset=Đã gỡ khóa đối tượng dữ liệu {0} cho {1} trong trường {2}
luckperms.command.generic.meta.unset-temp=Đã gỡ khóa đối tượng dữ liệu tạm thời {0} cho {1} trong trường {2}
luckperms.command.generic.meta.clear=Đối tượng dữ liệu {0} có loại trùng khớp {1} bị xóa trong trường {2}
luckperms.command.generic.contextual-data.title=Dữ liệu của trường
luckperms.command.generic.contextual-data.mode.key=chế độ
luckperms.command.generic.contextual-data.mode.server=máy chủ
luckperms.command.generic.contextual-data.mode.active-player=các người chơi hoạt động
luckperms.command.generic.contextual-data.contexts-key=Trường
luckperms.command.generic.contextual-data.prefix-key=Tiền tố
luckperms.command.generic.contextual-data.suffix-key=Hậu tố
luckperms.command.generic.contextual-data.primary-group-key=Nhóm chính
luckperms.command.generic.contextual-data.meta-key=Dữ liệu đối tượng
luckperms.command.generic.contextual-data.null-result=Không có
luckperms.command.user.info.title=Thông tin người dùng
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=dạng
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=ngoại tuyến
luckperms.command.user.info.status-key=Trạng thái
luckperms.command.user.info.status.online=Trực tuyến
luckperms.command.user.info.status.offline=Ngoại tuyến
luckperms.command.user.removegroup.error-primary=Bạn không thể xóa người dùng khỏi nhóm chính của họ
luckperms.command.user.primarygroup.not-member={0} chưa phải là thành viên của {1}, đang thêm họ bây giờ
luckperms.command.user.primarygroup.already-has={0} đã đặt {1} làm nhóm chính của họ
luckperms.command.user.primarygroup.warn-option=Cảnh báo\: Phương pháp tính toán nhóm chính đang được máy chủ này ({0}) sử dụng có thể không phản lại thay đổi này
luckperms.command.user.primarygroup.set=Nhóm chính của {0} đã được đặt thành {1}
luckperms.command.user.track.error-not-contain-group={0} chưa có trong bất kỳ nhóm nào trên {1}
luckperms.command.user.track.unsure-which-track=Khi không chắc chắn nên sử dụng thang nào, vui lòng chỉ định nó trong tham biến
luckperms.command.user.track.missing-group-advice=Có thể tạo nhóm hoặc xóa nhóm khỏi thang và thử lại
luckperms.command.user.promote.added-to-first={0} không có trong bất kỳ nhóm nào trên {1}, vì vậy chúng đã được thêm vào nhóm đầu tiên, {2} trong trường {3}
luckperms.command.user.promote.not-on-track={0} không thuộc bất kỳ nhóm nào trên {1}, vì vậy đã không được lên cấp
luckperms.command.user.promote.success=Đã lên cấp {0} theo thang {1} từ {2} đến {3} trong trường {4}
luckperms.command.user.promote.end-of-track=Đã đến điểm kết thúc của thang {0}, không thể lên cấp {1} được nữa
luckperms.command.user.promote.next-group-deleted=Nhóm tiếp theo trên thang {0} không còn tồn tại nữa
luckperms.command.user.promote.unable-to-promote=Không thể thăng cấp người dùng
luckperms.command.user.demote.success=Đã xuống cấp {0} theo thang {1} từ {2} đến {3} trong trường {4}
luckperms.command.user.demote.end-of-track=Đã đến điểm kết thúc của thang {0}, vì vậy {1} đã bị xóa khỏi {2}
luckperms.command.user.demote.end-of-track-not-removed=Đã đến điểm kết thúc của thang {0}, nhưng {1} không bị xóa khỏi nhóm đầu tiên
luckperms.command.user.demote.previous-group-deleted=Nhóm trước trên thang {0} không còn tồn tại nữa
luckperms.command.user.demote.unable-to-demote=Không thể xuống cấp người dùng
luckperms.command.group.list.title=Nhóm
luckperms.command.group.delete.not-default=Bạn không thể xóa nhóm mặc định
luckperms.command.group.info.title=Thông tin Nhóm
luckperms.command.group.info.display-name-key=Tên hiển thị
luckperms.command.group.info.weight-key=Trọng số
luckperms.command.group.setweight.set=Đặt trọng số thành {0} cho nhóm {1}
luckperms.command.group.setdisplayname.doesnt-have={0} không có tên hiển thị nào được đặt
luckperms.command.group.setdisplayname.already-has={0} đã có tên hiển thị được đặt là {1}
luckperms.command.group.setdisplayname.already-in-use=Tên hiển thị {0} đã được {1} sử dụng
luckperms.command.group.setdisplayname.set=Đặt tên hiển thị thành {0} cho nhóm {1} trong trường {2}
luckperms.command.group.setdisplayname.removed=Đã xóa tên hiển thị cho nhóm {0} trong trường {1}
luckperms.command.track.list.title=Thang
luckperms.command.track.path.empty=Không có
luckperms.command.track.info.showing-track=Hiển thị thang
luckperms.command.track.info.path-property=Đường dẫn
luckperms.command.track.clear=Nhóm thang của {0} đã bị xóa
luckperms.command.track.append.success=Nhóm {0} đã được thêm vào thang {1}
luckperms.command.track.insert.success=Nhóm {0} đã được chèn vào trong thang {1} tại vị trí {2}
luckperms.command.track.insert.error-number=Đang cần con số nhưng lại nhận được\: {0}
luckperms.command.track.insert.error-invalid-pos=Không thể chèn ở vị trí {0}
luckperms.command.track.insert.error-invalid-pos-reason=vị trí không hợp lệ
luckperms.command.track.remove.success=Nhóm {0} đã được gỡ bỏ từ thang {1}
luckperms.command.track.error-empty=Không thể sử dụng {0} vì nó trống hoặc chỉ chứa một nhóm
luckperms.command.track.error-multiple-groups={0} là thành viên của nhiều nhóm trên thang này
luckperms.command.track.error-ambiguous=Không thể xác định vị trí được
luckperms.command.track.already-contains={0} đã chứa {1}
luckperms.command.track.doesnt-contain={0} không chứa {1}
luckperms.command.log.load-error=Nhật kí không thể xử lí được
luckperms.command.log.invalid-page=Số trang không hợp lệ
luckperms.command.log.invalid-page-range=Vui lòng nhập giá trị từ {0} đến {1}
luckperms.command.log.empty=Không có mục nhật ký nào để hiển thị
luckperms.command.log.notify.error-console=Không thể chuyển đổi chế độ thông báo cho bảng điều khiển
luckperms.command.log.notify.enabled-term=Đã bật
luckperms.command.log.notify.disabled-term=Đã tắt
luckperms.command.log.notify.changed-state={0} kết quả nhật kí
luckperms.command.log.notify.already-on=Bạn đã được nhận được thông báo
luckperms.command.log.notify.already-off=Bạn hiện không nhận được thông báo nào
luckperms.command.log.notify.invalid-state=Trạng thái không xác định. Đang cần {0} hoặc {1}
luckperms.command.log.show.search=Hiển thị các hành động gần đây cho mẫu {0}
luckperms.command.log.show.recent=Đạng xem hành động gần đây
luckperms.command.log.show.by=Đạng xem hành động gần đây trong {0}
luckperms.command.log.show.history=Hiển thị lịch sử cho {0} {1}
luckperms.command.export.error-term=Lỗi
luckperms.command.export.already-running=Có một quá trình xuất hiện đang chạy
luckperms.command.export.file.already-exists=Tập tin {0} đã tồn tại
luckperms.command.export.file.not-writable=Tập tin {0} không thể ghi được
luckperms.command.export.file.success=Xuất thành công sang {0}
luckperms.command.export.file-unexpected-error-writing=Đã có lỗi không mong muốn khi đang ghi vào tập tin
luckperms.command.export.web.export-code=Xuất mã
luckperms.command.export.web.import-command-description=Sử dụng lệnh sau để nhập vào
luckperms.command.import.term=Nhập
luckperms.command.import.error-term=Lỗi
luckperms.command.import.already-running=Có một quá trình nhập vào hiện đang chạy
luckperms.command.import.file.doesnt-exist=Tập tin {0} không tồn tại
luckperms.command.import.file.not-readable=Tập tin {0} không thể đọc được
luckperms.command.import.file.unexpected-error-reading=Đã có lỗi không xác định khi đọc từ tập tin nhập vào
luckperms.command.import.file.correct-format=nó có phải là định dạng chính xác không?
luckperms.command.import.web.unable-to-read=Không thể đọc dữ liệu bằng mã đã cho
luckperms.command.import.progress.percent={0}% đã hoàn thành
luckperms.command.import.progress.operations={0}/{1} hoạt động hoàn tất
luckperms.command.import.starting=Đang bắt đầu quá trình nhập vào
luckperms.command.import.completed=HOÀN THÀNH
luckperms.command.import.duration=mất {0} giây
luckperms.command.bulkupdate.must-use-console=Lệnh cập nhật hàng loạt chỉ có thể được sử dụng từ bảng điều khiển
luckperms.command.bulkupdate.invalid-data-type=Loại không hợp lệ, đang cần {0}
luckperms.command.bulkupdate.invalid-constraint=Ràng buộc {0} không hợp lệ
luckperms.command.bulkupdate.invalid-constraint-format=Sự cưỡng ép nên dược đặt trong định dạng {0}
luckperms.command.bulkupdate.invalid-comparison=Trình hoạt động so sánh không hợp lệ {0}
luckperms.command.bulkupdate.invalid-comparison-format=Đang cần một trong những yếu tố sau\: {0}
luckperms.command.bulkupdate.queued=Quá trình cập nhật hoạt động đã được xếp vào hàng đợi
luckperms.command.bulkupdate.confirm=Chạy {0} để thực hiện cập nhật
luckperms.command.bulkupdate.unknown-id=Quá trình với id {0} không tồn tại hoặc đã hết hạn
luckperms.command.bulkupdate.starting=Đang chạy khối cập nhật
luckperms.command.bulkupdate.success=Khối cập nhật đã hoàn thành
luckperms.command.bulkupdate.success.statistics.nodes=Tổng số mẩu bị ảnh hưởng
luckperms.command.bulkupdate.success.statistics.users=Tổng số người dùng bị ảnh hưởng
luckperms.command.bulkupdate.success.statistics.groups=Tổng số nhóm bị ảnh hưởng
luckperms.command.bulkupdate.failure=Cập nhật khối không thành công, hãy kiểm tra bảng điều khiển để tìm lỗi
luckperms.command.update-task.request=Một tác vụ cập nhật đã được yêu cầu, vui lòng đợi
luckperms.command.update-task.complete=Cập nhật tác vụ hoàn tất
luckperms.command.update-task.push.attempting=Hiện đang cố gắng đẩy sang các máy chủ khác
luckperms.command.update-task.push.complete=Các máy chủ khác đã được thông báo qua {0} thành công
luckperms.command.update-task.push.error=Đã có lỗi khi đẩy các thay đổi đến các máy chủ khác
luckperms.command.update-task.push.error-not-setup=Không thể đẩy các thay đổi đến các máy chủ khác vì dịch vụ tin nhắn chưa được cấu hình
luckperms.command.reload-config.success=Tệp cấu hình đã được tải lại
luckperms.command.reload-config.restart-note=một số tùy chọn sẽ chỉ áp dụng sau khi máy chủ đã khởi động lại
luckperms.command.translations.searching=Đang tìm kiếm các bản dịch có sẵn, vui lòng đợi...
luckperms.command.translations.searching-error=Không thể lấy danh sách các bản dịch có sẵn
luckperms.command.translations.installed-translations=Những bản dịch đã được cài
luckperms.command.translations.available-translations=Các bản dịch có sẵn
luckperms.command.translations.percent-translated={0}% đã dịch
luckperms.command.translations.translations-by=bởi
luckperms.command.translations.installing=Đang cài đặt bản dịch, vui lòng đợi...
luckperms.command.translations.download-error=Không thể tải xuống bản dịch cho tiếng {0}
luckperms.command.translations.installing-specific=Đang cài ngôn ngữ {0}...
luckperms.command.translations.install-complete=Cài đặt hoàn tất
luckperms.command.translations.download-prompt=Sử dụng {0} để tải xuống và cài đặt các phiên bản cập nhật của các bản dịch này do cộng đồng cung cấp
luckperms.command.translations.download-override-warning=Xin lưu ý rằng điều này sẽ ghi đè bất kỳ thay đổi nào bạn đã thực hiện cho các ngôn ngữ này
luckperms.usage.user.description=Một nhóm các lệnh để quản lý người dùng trong LuckPerms. (''Người dùng'' trong LuckPerms chỉ là một người chơi và có thể tham chiếu đến UUID hoặc tên người dùng)
luckperms.usage.group.description=Một nhóm các lệnh để quản lý các nhóm trong LuckPerms. Nhóm chỉ là tập hợp các tác vụ quyền có thể được cấp cho người dùng. Các nhóm mới được tạo bằng lệnh ''creategroup''.
luckperms.usage.track.description=Một nhóm các lệnh để quản lý các thang trong LuckPerms. Thang bậc là một tập hợp các nhóm có thứ tự có thể được sử dụng để xác định các việc lên cấp hay xuống cấp.
luckperms.usage.log.description=Một nhóm các lệnh để quản lý chức năng ghi nhật ký trong LuckPerms.
luckperms.usage.sync.description=Tải lại tất cả dữ liệu từ bộ nhớ plugin vào bộ nhớ và áp dụng bất kỳ thay đổi nào được phát hiện.
luckperms.usage.info.description=In vào những thông tin chung về đối tượng plugin đang hoạt động.
luckperms.usage.editor.description=Tạo trình chỉnh sửa web mới
luckperms.usage.editor.argument.type=các loại cần tải vào trình chỉnh sửa. (''tất cả'', ''người dùng'' hoặc ''nhóm'')
luckperms.usage.editor.argument.filter=quyền để lọc mục của người dùng theo
luckperms.usage.verbose.description=Kiểm soát hệ thống giám sát kiểm tra quyền của plugin.
luckperms.usage.verbose.argument.action=khi muốn bật/tắt việc in nhật ký hoặc tải lên kết quả đã ghi
luckperms.usage.verbose.argument.filter=bộ lọc để khớp các mục nhập với
luckperms.usage.verbose.argument.commandas=người chơi /lệnh để chạy
luckperms.usage.tree.description=Tạo chế độ xem dạng thang bậc (thứ tự danh sách hệ thống phân cấp) của tất cả các quyền mà LuckPerms đã biết.
luckperms.usage.tree.argument.scope=gốc của thang bậc. chỉ định "." để bao gồm tất cả các quyền
luckperms.usage.tree.argument.player=tên của một người chơi trực tuyến để kiểm tra
luckperms.usage.search.description=Tìm tất cả người dùng/nhóm với một quyền cụ thể
luckperms.usage.search.argument.permission=quyền cấp cho tìm kiếm
luckperms.usage.search.argument.page=trang để xem
luckperms.usage.network-sync.description=Đồng bộ hóa các thay đổi với bộ nhớ và yêu cầu mà tất cả các máy chủ khác trên mạng kết nối đang thực hiện tương tự
luckperms.usage.import.description=Nhập dữ liệu từ tệp xuất (đã tạo trước đó)
luckperms.usage.import.argument.file=chọn tệp để nhập từ
luckperms.usage.import.argument.replace=thay thế dữ liệu hiện có thay vì kết hợp
luckperms.usage.import.argument.upload=tải dữ liệu lên từ lần xuất trước
luckperms.usage.export.description=Xuất tất cả dữ liệu quyền sang tệp ''xuất''. Có thể được nhập lại sau đó.
luckperms.usage.export.argument.file=tệp tin để xuất sang
luckperms.usage.export.argument.without-users=loại trừ người dùng khỏi quá trình xuất
luckperms.usage.export.argument.without-groups=loại trừ nhóm khỏi quá trình xuất
luckperms.usage.export.argument.upload=Tải lên tất cả dữ liệu quyền cấp sang trình chỉnh sửa web. Có thể được nhập lại sau đó.
luckperms.usage.reload-config.description=Tải lại một số tùy chọn cấu hình
luckperms.usage.bulk-update.description=Thực hiện các thay đổi khối trên tất cả dữ liệu
luckperms.usage.bulk-update.argument.data-type=loại dữ liệu đang được thay đổi. (''tất cả'', ''người dùng'' hoặc ''nhóm'')
luckperms.usage.bulk-update.argument.action=các hành động cần thực hiện trên dữ liệu. (''cập nhật'' hoặc ''xóa'')
luckperms.usage.bulk-update.argument.action-field=vùng để hành động. chỉ cần thiết cho ''cập nhật''. (''quyền'', ''máy chủ'' hoặc ''thế giới'')
luckperms.usage.bulk-update.argument.action-value=giá trị để thay thế. chỉ cần thiết cho ''cập nhật''.
luckperms.usage.bulk-update.argument.constraint=các giới hạn cần thiết cho bản cập nhật
luckperms.usage.translations.description=Quản lý chuyển ngữ
luckperms.usage.translations.argument.install=lệnh con để cài đặt bản dịch
luckperms.usage.apply-edits.description=Áp dụng các thay đổi quyền cấp được thực hiện từ trình chỉnh sửa web
luckperms.usage.apply-edits.argument.code=mã duy nhất cho dữ liệu
luckperms.usage.apply-edits.argument.target=người cần được áp dụng dữ liệu
luckperms.usage.create-group.description=Tạo một nhóm mới
luckperms.usage.create-group.argument.name=tên nhóm
luckperms.usage.create-group.argument.weight=trọng số của nhóm
luckperms.usage.create-group.argument.display-name=tên hiển thị của nhóm
luckperms.usage.delete-group.description=Xoá một nhóm
luckperms.usage.delete-group.argument.name=tên nhóm
luckperms.usage.list-groups.description=Danh sách tất cả các nhóm trên nền tảng
luckperms.usage.create-track.description=Tạo một thang mới
luckperms.usage.create-track.argument.name=tên thang
luckperms.usage.delete-track.description=Xóa một thang
luckperms.usage.delete-track.argument.name=tên thang
luckperms.usage.list-tracks.description=Danh sách tất cả các thang trên nền tảng
luckperms.usage.user-info.description=Hiển thị thông tin về người dùng
luckperms.usage.user-switchprimarygroup.description=Chuyển đổi nhóm chính của người dùng
luckperms.usage.user-switchprimarygroup.argument.group=nhóm để chuyển sang
luckperms.usage.user-promote.description=Thăng cấp người dùng lên một thang
luckperms.usage.user-promote.argument.track=thang để thăng cấp người dùng lên
luckperms.usage.user-promote.argument.context=trường để thăng cấp người dùng lên
luckperms.usage.user-promote.argument.dont-add-to-first=chỉ thăng cấp người dùng nếu họ đã ở trong thang
luckperms.usage.user-demote.description=Hạ cấp người dùng xuống một thang
luckperms.usage.user-demote.argument.track=thang để hạ cấp người dùng xuống
luckperms.usage.user-demote.argument.context=trường để hạ cấp người dùng xuống
luckperms.usage.user-demote.argument.dont-remove-from-first=ngăn người dùng khỏi việc bị xóa khỏi nhóm đầu tiên
luckperms.usage.user-clone.description=Sao chép người dùng
luckperms.usage.user-clone.argument.user=tên/uuid của người dùng sao chép vào
luckperms.usage.group-info.description=Cung cấp thông tin về nhóm
luckperms.usage.group-listmembers.description=Hiển thị những người dùng/nhóm kế thừa từ nhóm này
luckperms.usage.group-listmembers.argument.page=trang để xem
luckperms.usage.group-setweight.description=Đặt trọng số của nhóm
luckperms.usage.group-setweight.argument.weight=trọng số cần đặt
luckperms.usage.group-set-display-name.description=Đặt tên hiển thị nhóm
luckperms.usage.group-set-display-name.argument.name=tên để đặt
luckperms.usage.group-set-display-name.argument.context=trường để đặt tên vào
luckperms.usage.group-rename.description=Đổi tên nhóm
luckperms.usage.group-rename.argument.name=tên mới
luckperms.usage.group-clone.description=Sao chép nhóm
luckperms.usage.group-clone.argument.name=tên của nhóm để sao chép vào
luckperms.usage.holder-editor.description=Mở trình chỉnh sửa quyền web
luckperms.usage.holder-showtracks.description=Liệt kê các thang của đối tượng
luckperms.usage.holder-clear.description=Xóa bỏ tất cả các quyền, chính chủ và dữ liệu đối tượng
luckperms.usage.holder-clear.argument.context=trường để lọc theo
luckperms.usage.permission.description=Chỉnh sửa quyền
luckperms.usage.parent.description=Chỉnh sửa kế thừa
luckperms.usage.meta.description=Sửa giá trị dữ liệu đối tượng
luckperms.usage.permission-info.description=Liệt kê các mẩu quyền mà đối tượng có
luckperms.usage.permission-info.argument.page=trang để xem
luckperms.usage.permission-info.argument.sort-mode=cách sắp xếp các mục nhập
luckperms.usage.permission-set.description=Đặt quyền cấp cho đối tượng
luckperms.usage.permission-set.argument.node=mẩu quyền để thiết lập
luckperms.usage.permission-set.argument.value=giá trị của mẩu
luckperms.usage.permission-set.argument.context=trường để thêm quyền vào
luckperms.usage.permission-unset.description=Bỏ quyền cho đối tượng
luckperms.usage.permission-unset.argument.node=mẩu quyền để gỡ bỏ
luckperms.usage.permission-unset.argument.context=trường để xóa quyền vào
luckperms.usage.permission-settemp.description=Đặt quyền cấp cho đối tượng tạm thời
luckperms.usage.permission-settemp.argument.node=mẩu quyền để thiết lập
luckperms.usage.permission-settemp.argument.value=giá trị của mẩu
luckperms.usage.permission-settemp.argument.duration=khoảng thời gian cho đến khi mẩu quyền hết hạn
luckperms.usage.permission-settemp.argument.temporary-modifier=quyền cấp tạm thời sẽ được áp dụng như thế nào
luckperms.usage.permission-settemp.argument.context=trường để thêm quyền vào
luckperms.usage.permission-unsettemp.description=Gỡ quyền cấp tạm thời cho đối tượng
luckperms.usage.permission-unsettemp.argument.node=mẩu quyền để gỡ bỏ
luckperms.usage.permission-unsettemp.argument.duration=khoảng thời gian để trừ đi
luckperms.usage.permission-unsettemp.argument.context=trường để xóa quyền
luckperms.usage.permission-check.description=Kiểm tra xem đối tượng có một mẩu quyền nhất định hay không
luckperms.usage.permission-check.argument.node=mẩu quyền cấp để tìm kiếm
luckperms.usage.permission-clear.description=Xóa tất cả quyền
luckperms.usage.permission-clear.argument.context=trường để lọc theo
luckperms.usage.parent-info.description=Liệt kê các nhóm mà đối tượng này kế thừa
luckperms.usage.parent-info.argument.page=trang để xem
luckperms.usage.parent-info.argument.sort-mode=cách sắp xếp các mục nhập
luckperms.usage.parent-set.description=Loại bỏ tất cả các nhóm khác mà đối tượng đã kế thừa và thêm chúng vào nhóm đã cho
luckperms.usage.parent-set.argument.group=nhóm để đặt vào
luckperms.usage.parent-set.argument.context=trường để đặt nhóm vào
luckperms.usage.parent-add.description=Đặt một nhóm khác cho đối tượng để kế thừa quyền từ
luckperms.usage.parent-add.argument.group=nhóm để thừa kế
luckperms.usage.parent-add.argument.context=trường để thừa hưởng nhóm vào
luckperms.usage.parent-remove.description=Gỡ bỏ quy tắc kế thừa đã đặt trước đó
luckperms.usage.parent-remove.argument.group=nhóm để gỡ bỏ
luckperms.usage.parent-remove.argument.context=trường để xóa nhóm
luckperms.usage.parent-set-track.description=Loại bỏ tất cả các nhóm khác mà đối tượng đã kế thừa từ thang đã cho và thêm chúng vào nhóm đã cho
luckperms.usage.parent-set-track.argument.track=thang để đặt vào
luckperms.usage.parent-set-track.argument.group=nhóm để đặt hoặc một con số liên quan đến vị trí của nhóm trên thang đã cho
luckperms.usage.parent-set-track.argument.context=trường để đặt nhóm vào
luckperms.usage.parent-add-temp.description=Đặt một nhóm khác cho đối tượng để kế thừa quyền cấp tạm thời
luckperms.usage.parent-add-temp.argument.group=nhóm để thừa hưởng
luckperms.usage.parent-add-temp.argument.duration=thời hạn của thành viên nhóm
luckperms.usage.parent-add-temp.argument.temporary-modifier=quyền cấp tạm thời nên được áp dụng như thế nào
luckperms.usage.parent-add-temp.argument.context=trường để thừa hưởng nhóm vào
luckperms.usage.parent-remove-temp.description=Gỡ bỏ một quy tắc kế thừa tạm thời đã đặt trước đó
luckperms.usage.parent-remove-temp.argument.group=nhóm để gỡ bỏ
luckperms.usage.parent-remove-temp.argument.duration=khoảng thời gian để trừ đi
luckperms.usage.parent-remove-temp.argument.context=trường để xóa nhóm
luckperms.usage.parent-clear.description=Xóa tất cả chính chủ
luckperms.usage.parent-clear.argument.context=trường để lọc theo
luckperms.usage.parent-clear-track.description=Xóa tất cả chủ chính trên một thang đã cho
luckperms.usage.parent-clear-track.argument.track=thang để xóa
luckperms.usage.parent-clear-track.argument.context=trường để lọc theo
luckperms.usage.meta-info.description=Hiển thị tất cả dữ liệu đối tượng trong tin nhắn
luckperms.usage.meta-set.description=Đặt giá trị dữ liệu đối tượng
luckperms.usage.meta-set.argument.key=khóa chính để đặt
luckperms.usage.meta-set.argument.value=giá trị để đặt
luckperms.usage.meta-set.argument.context=trường để thêm cặp dữ liệu đối tượng vào
luckperms.usage.meta-unset.description=Gỡ bỏ giá trị dữ liệu đối tượng
luckperms.usage.meta-unset.argument.key=khóa chính để gỡ bỏ
luckperms.usage.meta-unset.argument.context=trường để loại bỏ cặp dữ liệu đối tượng vào
luckperms.usage.meta-settemp.description=Đặt giá trị dữ liệu đối tượng tạm thời
luckperms.usage.meta-settemp.argument.key=khóa chính để đặt
luckperms.usage.meta-settemp.argument.value=giá trị để đặt
luckperms.usage.meta-settemp.argument.duration=khoảng thời gian cho đến khi giá trị dữ liệu đối tượng hết hạn
luckperms.usage.meta-settemp.argument.context=trường để thêm cặp dữ liệu đối tượng vào
luckperms.usage.meta-unsettemp.description=Gỡ bỏ giá trị dữ liệu đối tượng tạm thời
luckperms.usage.meta-unsettemp.argument.key=khóa chính để gỡ bỏ
luckperms.usage.meta-unsettemp.argument.context=trường để loại bỏ cặp dữ liệu đối tượng vào
luckperms.usage.meta-addprefix.description=Thêm tiền tố vào
luckperms.usage.meta-addprefix.argument.priority=giá trị ưu tiên để thêm tiền tố tại
luckperms.usage.meta-addprefix.argument.prefix=chuỗi tiền tố
luckperms.usage.meta-addprefix.argument.context=trường để thêm tiền tố vào
luckperms.usage.meta-addsuffix.description=Thêm hậu tố vào
luckperms.usage.meta-addsuffix.argument.priority=giá trị ưu tiên để thêm hậu tố tại
luckperms.usage.meta-addsuffix.argument.suffix=chuỗi hậu tố
luckperms.usage.meta-addsuffix.argument.context=trường để thêm hậu tố vào
luckperms.usage.meta-setprefix.description=Đặt tiền tố vào
luckperms.usage.meta-setprefix.argument.priority=giá trị ưu tiên để đặt tiền tố tại
luckperms.usage.meta-setprefix.argument.prefix=chuỗi tiền tố
luckperms.usage.meta-setprefix.argument.context=trường để đặt tiền tố vào
luckperms.usage.meta-setsuffix.description=Đặt hậu tố vào
luckperms.usage.meta-setsuffix.argument.priority=giá trị ưu tiên để đặt hậu tố
luckperms.usage.meta-setsuffix.argument.suffix=chuỗi hậu tố
luckperms.usage.meta-setsuffix.argument.context=trường để đặt hậu tố vào
luckperms.usage.meta-removeprefix.description=Gỡ bỏ một tiền tố
luckperms.usage.meta-removeprefix.argument.priority=giá trị ưu tiên để gỡ tiền tố tại
luckperms.usage.meta-removeprefix.argument.prefix=chuỗi tiền tố
luckperms.usage.meta-removeprefix.argument.context=trường để xóa tiền tố
luckperms.usage.meta-removesuffix.description=Gỡ bỏ một hậu tố
luckperms.usage.meta-removesuffix.argument.priority=giá trị ưu tiên để gỡ hậu tố tại
luckperms.usage.meta-removesuffix.argument.suffix=chuỗi hậu tố
luckperms.usage.meta-removesuffix.argument.context=trường để xóa hậu tố
luckperms.usage.meta-addtemp-prefix.description=Thêm một tiền tố tạm thời
luckperms.usage.meta-addtemp-prefix.argument.priority=giá trị ưu tiên để thêm tiền tố tại
luckperms.usage.meta-addtemp-prefix.argument.prefix=chuỗi tiền tố
luckperms.usage.meta-addtemp-prefix.argument.duration=khoảng thời gian cho đến khi tiền tố hết hạn
luckperms.usage.meta-addtemp-prefix.argument.context=trường để thêm tiền tố vào
luckperms.usage.meta-addtemp-suffix.description=Thêm một hậu tố tạm thời
luckperms.usage.meta-addtemp-suffix.argument.priority=giá trị ưu tiên để thêm hậu tố tại
luckperms.usage.meta-addtemp-suffix.argument.suffix=chuỗi hậu tố
luckperms.usage.meta-addtemp-suffix.argument.duration=khoảng thời gian cho đến khi hậu tố hết hạn
luckperms.usage.meta-addtemp-suffix.argument.context=trường để thêm hậu tố vào
luckperms.usage.meta-settemp-prefix.description=Đặt một tiền tố tạm thời
luckperms.usage.meta-settemp-prefix.argument.priority=giá trị ưu tiên để đặt tiền tố tại
luckperms.usage.meta-settemp-prefix.argument.prefix=chuỗi tiền tố
luckperms.usage.meta-settemp-prefix.argument.duration=khoảng thời gian cho đến khi tiền tố hết hạn
luckperms.usage.meta-settemp-prefix.argument.context=trường để đặt tiền tố vào
luckperms.usage.meta-settemp-suffix.description=Đặt một hậu tố tạm thời
luckperms.usage.meta-settemp-suffix.argument.priority=giá trị ưu tiên để đặt hậu tố
luckperms.usage.meta-settemp-suffix.argument.suffix=chuỗi hậu tố
luckperms.usage.meta-settemp-suffix.argument.duration=khoảng thời gian cho đến khi hậu tố hết hạn
luckperms.usage.meta-settemp-suffix.argument.context=trường để đặt hậu tố vào
luckperms.usage.meta-removetemp-prefix.description=Gỡ bỏ một tiền tố tạm thời
luckperms.usage.meta-removetemp-prefix.argument.priority=giá trị ưu tiên để gỡ tiền tố
luckperms.usage.meta-removetemp-prefix.argument.prefix=chuỗi tiền tố
luckperms.usage.meta-removetemp-prefix.argument.context=trường để xóa tiền tố
luckperms.usage.meta-removetemp-suffix.description=Gỡ bỏ một hậu tố tạm thời
luckperms.usage.meta-removetemp-suffix.argument.priority=giá trị ưu tiên để gỡ hậu tố tại
luckperms.usage.meta-removetemp-suffix.argument.suffix=chuỗi hậu tố
luckperms.usage.meta-removetemp-suffix.argument.context=trường để xóa hậu tố
luckperms.usage.meta-clear.description=Xóa tất cả dữ liệu
luckperms.usage.meta-clear.argument.type=loại dữ liệu đối tượng cần loại bỏ
luckperms.usage.meta-clear.argument.context=trường để lọc theo
luckperms.usage.track-info.description=Cung cấp thông tin về thang
luckperms.usage.track-editor.description=Mở trình biên tập quyền trên web
luckperms.usage.track-append.description=Thêm một nhóm vào cuối thang
luckperms.usage.track-append.argument.group=nhóm để thêm vào
luckperms.usage.track-insert.description=Chèn một nhóm vào một vị trí nhất định dọc theo thang
luckperms.usage.track-insert.argument.group=nhóm để chèn vào
luckperms.usage.track-insert.argument.position=vị trí để chèn nhóm (vị trí đầu tiên trên thang là 1)
luckperms.usage.track-remove.description=Xóa một nhóm khỏi thang
luckperms.usage.track-remove.argument.group=nhóm để gỡ bỏ
luckperms.usage.track-clear.description=Gỡ bỏ các nhóm trên thang
luckperms.usage.track-rename.description=Đổi tên thang
luckperms.usage.track-rename.argument.name=tên mới
luckperms.usage.track-clone.description=Sao chép thang
luckperms.usage.track-clone.argument.name=tên của thang để sao chép vào
luckperms.usage.log-recent.description=Xem hành động gần đây
luckperms.usage.log-recent.argument.user=tên/uuid của người dùng để lọc theo
luckperms.usage.log-recent.argument.page=số trang để xem
luckperms.usage.log-search.description=Tìm kiếm nhật ký cho một mục nhập
luckperms.usage.log-search.argument.query=chuỗi để tìm kiếm bởi
luckperms.usage.log-search.argument.page=số trang để xem
luckperms.usage.log-notify.description=Bật/tắt thông báo nhật ký
luckperms.usage.log-notify.argument.toggle=khi cần chuyển đổi bật/tắt
luckperms.usage.log-user-history.description=Xem lịch sử của người dùng
luckperms.usage.log-user-history.argument.user=tên/uuid của người dùng
luckperms.usage.log-user-history.argument.page=số trang để xem
luckperms.usage.log-group-history.description=Xem lịch sử của nhóm
luckperms.usage.log-group-history.argument.group=tên nhóm
luckperms.usage.log-group-history.argument.page=số trang để xem
luckperms.usage.log-track-history.description=Xem lịch sử của thang
luckperms.usage.log-track-history.argument.track=tên thang
luckperms.usage.log-track-history.argument.page=số trang để xem
luckperms.usage.sponge.description=Chỉnh sửa dữ liệu bổ sung của Sponge
luckperms.usage.sponge.argument.collection=bộ sưu tập để tra cứu
luckperms.usage.sponge.argument.subject=chủ thể để sửa đổi
luckperms.usage.sponge-permission-info.description=Hiển thị thông tin về quyền của chủ thể
luckperms.usage.sponge-permission-info.argument.contexts=trường để lọc theo
luckperms.usage.sponge-permission-set.description=Đặt quyền cấp cho chủ thể
luckperms.usage.sponge-permission-set.argument.node=mẩu quyền để thiết lập
luckperms.usage.sponge-permission-set.argument.tristate=giá trị để đặt quyền thành
luckperms.usage.sponge-permission-set.argument.contexts=trường để đặt quyền vào
luckperms.usage.sponge-permission-clear.description=Xóa quyền cho chủ thể
luckperms.usage.sponge-permission-clear.argument.contexts=trường để xóa các quyền
luckperms.usage.sponge-parent-info.description=Hiển thị thông tin về chủ chính của chủ thể
luckperms.usage.sponge-parent-info.argument.contexts=trường để lọc theo
luckperms.usage.sponge-parent-add.description=Thêm chủ chính vào chủ thể
luckperms.usage.sponge-parent-add.argument.collection=bộ sưu tập chủ thể mà trong đó chủ thể chính là
luckperms.usage.sponge-parent-add.argument.subject=tên của chủ thể chính
luckperms.usage.sponge-parent-add.argument.contexts=trường để thêm chủ chính vào
luckperms.usage.sponge-parent-remove.description=Xóa chủ chính khỏi chủ thể
luckperms.usage.sponge-parent-remove.argument.collection=bộ sưu tập chủ thể mà trong đó chủ thể chính là
luckperms.usage.sponge-parent-remove.argument.subject=tên của chủ thể chính
luckperms.usage.sponge-parent-remove.argument.contexts=các trường để xóa chủ chính
luckperms.usage.sponge-parent-clear.description=Xóa chủ thể chính
luckperms.usage.sponge-parent-clear.argument.contexts=trường để xóa các chủ thể chính
luckperms.usage.sponge-option-info.description=Hiển thị thông tin về tùy chọn của chủ thể
luckperms.usage.sponge-option-info.argument.contexts=trường để lọc theo
luckperms.usage.sponge-option-set.description=Đặt tùy chọn cho chủ thể
luckperms.usage.sponge-option-set.argument.key=khóa chính để đặt
luckperms.usage.sponge-option-set.argument.value=giá trị để đặt khóa chính
luckperms.usage.sponge-option-set.argument.contexts=trường để đặt tùy chọn
luckperms.usage.sponge-option-unset.description=Gỡ bỏ tùy chọn cho chủ thể
luckperms.usage.sponge-option-unset.argument.key=khóa chính để gỡ bỏ
luckperms.usage.sponge-option-unset.argument.contexts=trường để gỡ khóa chính
luckperms.usage.sponge-option-clear.description=Xóa tùy chọn của chủ thể
luckperms.usage.sponge-option-clear.argument.contexts=trường để xóa các tùy chọn
