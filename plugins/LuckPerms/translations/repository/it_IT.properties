luckperms.logs.actionlog-prefix=LOG
luckperms.logs.verbose-prefix=Pref
luckperms.logs.export-prefix=ESPORTA
luckperms.commandsystem.available-commands=Usa {0} per visualizzare i comandi disponibili
luckperms.commandsystem.command-not-recognised=Comando non riconosciuto
luckperms.commandsystem.no-permission=Non hai il permesso di usare questo comando\!
luckperms.commandsystem.no-permission-subcommands=Non hai il permesso di usare alcun sotto-comando
luckperms.commandsystem.already-executing-command=Un altro comando è in esecuzione, aspetta che finisca...
luckperms.commandsystem.usage.sub-commands-header=Sotto comandi
luckperms.commandsystem.usage.usage-header=Utilizzo del comando
luckperms.commandsystem.usage.arguments-header=Argomenti
luckperms.first-time.no-permissions-setup=Sembra che nessun permesso sia ancora stato configurato\!
luckperms.first-time.use-console-to-give-access=Prima che tu possa usare un qualsiasi comando di LuckPerms nel gioco, devi usare la console per darti l''accesso
luckperms.first-time.console-command-prompt=Apri la tua console ed esegui
luckperms.first-time.next-step=Dopo averlo fatto, puoi iniziare a definire le tue assegnazioni dei permessi e gruppi
luckperms.first-time.wiki-prompt=Non sai da dove cominciare? Dai un''occhiata qui\: {0}
luckperms.login.try-again=Sei pregato di riprovare
luckperms.login.loading-database-error=Si è verificato un errore del database durante il caricamento dei dati dei permessi
luckperms.login.server-admin-check-console-errors=Se sei un amministratore del server, sei pregato di controllare la console per ogni errore
luckperms.login.server-admin-check-console-info=Sei pregato di controllare la console del server per ulteriori informazioni
luckperms.login.data-not-loaded-at-pre=I dati dei permessi per il tuo utente non sono stati caricati durante la fase di pre-accesso
luckperms.login.unable-to-continue=impossibile continuare
luckperms.login.craftbukkit-offline-mode-error=questo è probabilmente dovuto ad un conflitto tra CraftBukkit e l''impostazione online-mode
luckperms.login.unexpected-error=Si è verificato un errore imprevisto durante la configurazione dei tuoi dati dei permessi
luckperms.opsystem.disabled=Il sistema OP vanilla è disabilitato su questo server
luckperms.opsystem.sponge-warning=Sei pregato di notare che lo stato di Operatore del Server non ha effetto sui controlli dei permessi di Sponge quando un plugin dei permessi è installato, devi modificare direttamente i dati utente
luckperms.duration.unit.years.plural={0} anni
luckperms.duration.unit.years.singular={0} anno
luckperms.duration.unit.years.short={0}a
luckperms.duration.unit.months.plural={0} mesi
luckperms.duration.unit.months.singular={0} mese
luckperms.duration.unit.months.short={0}me
luckperms.duration.unit.weeks.plural={0} settimane
luckperms.duration.unit.weeks.singular={0} settimana
luckperms.duration.unit.weeks.short={0}s
luckperms.duration.unit.days.plural={0} giorni
luckperms.duration.unit.days.singular={0} giorno
luckperms.duration.unit.days.short={0}g
luckperms.duration.unit.hours.plural={0} ore
luckperms.duration.unit.hours.singular={0} ora
luckperms.duration.unit.hours.short={0}o
luckperms.duration.unit.minutes.plural={0} minuti
luckperms.duration.unit.minutes.singular={0} minuto
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} secondi
luckperms.duration.unit.seconds.singular={0} secondo
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} fa
luckperms.command.misc.invalid-code=Codice non valido
luckperms.command.misc.response-code-key=codice di risposta
luckperms.command.misc.error-message-key=messaggio
luckperms.command.misc.bytebin-unable-to-communicate=Impossibile comunicare con bytebin
luckperms.command.misc.webapp-unable-to-communicate=Impossibile comunicare con l''app web
luckperms.command.misc.check-console-for-errors=Controlla la console per gli errori
luckperms.command.misc.file-must-be-in-data=Il file {0} deve essere un figlio diretto della directory dei dati
luckperms.command.misc.wait-to-finish=Attendi che finisca e riprova
luckperms.command.misc.invalid-priority=Priorità {0} non valida
luckperms.command.misc.expected-number=Previsto un numero
luckperms.command.misc.date-parse-error=Impossibile riconoscere la data {0}
luckperms.command.misc.date-in-past-error=Non puoi impostare una data nel passato\!
luckperms.command.misc.page=pagina {0} di {1}
luckperms.command.misc.page-entries={0} voci
luckperms.command.misc.none=Niente
luckperms.command.misc.loading.error.unexpected=Si è verificato un errore imprevisto
luckperms.command.misc.loading.error.user=Utente non caricato
luckperms.command.misc.loading.error.user-specific=Impossibile caricare l''utente specificato {0}
luckperms.command.misc.loading.error.user-not-found=Non è stato possibile trovare un utente per {0}
luckperms.command.misc.loading.error.user-save-error=Si è verificato un errore durante il salvataggio dei dati utente per {0}
luckperms.command.misc.loading.error.user-not-online=L''utente {0} non è online
luckperms.command.misc.loading.error.user-invalid={0} non è un nome utente o uuid valido
luckperms.command.misc.loading.error.user-not-uuid=L''utente di destinazione {0} non è un uuid valido
luckperms.command.misc.loading.error.group=Gruppo non caricato
luckperms.command.misc.loading.error.all-groups=Non è stato possibile caricare tutti i gruppi
luckperms.command.misc.loading.error.group-not-found=Non è stato trovato alcun gruppo chiamato {0}
luckperms.command.misc.loading.error.group-save-error=Si è verificato un errore durante il salvataggio dei dati del gruppo per {0}
luckperms.command.misc.loading.error.group-invalid={0} non è un nome di gruppo valido
luckperms.command.misc.loading.error.track=Traccia non caricata
luckperms.command.misc.loading.error.all-tracks=Non è stato possibile caricare tutte le tracce
luckperms.command.misc.loading.error.track-not-found=Non è stato possibile trovare una traccia chiamata {0}
luckperms.command.misc.loading.error.track-save-error=Si è verificato un errore durante il salvataggio dei dati della traccia per {0}
luckperms.command.misc.loading.error.track-invalid={0} non è un nome di traccia valido
luckperms.command.editor.no-match=Impossibile aprire l''editor, nessun oggetto corrisponde al tipo desiderato
luckperms.command.editor.start=Preparazione di una nuova sessione dell''editor, attendi...
luckperms.command.editor.url=Clicca il link qui sotto per aprire l''editor
luckperms.command.editor.unable-to-communicate=Non è stato possibile comunicare con l''editor
luckperms.command.editor.apply-edits.success=I dati dell''editor Web sono stati applicati con successo a {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} e {2} {3}
luckperms.command.editor.apply-edits.success.additions=aggiunte
luckperms.command.editor.apply-edits.success.additions-singular=aggiunta
luckperms.command.editor.apply-edits.success.deletions=eliminazioni
luckperms.command.editor.apply-edits.success.deletions-singular=eliminazione
luckperms.command.editor.apply-edits.no-changes=Nessuna modifica è stata applicata dall''editor web, i dati restituiti non contengono alcuna modifica
luckperms.command.editor.apply-edits.unknown-type=Non è stato possibile applicare la modifica al tipo di oggetto specificato
luckperms.command.editor.apply-edits.unable-to-read=Non è stato possibile leggere i dati usando il codice fornito
luckperms.command.search.searching.permission=Ricerca di utenti e gruppi con {0}
luckperms.command.search.searching.inherit=Ricerca di utenti e gruppi che ereditano da {0}
luckperms.command.search.result=Trovate {0} voci da {1} utenti e {2} gruppi
luckperms.command.search.result.default-notice=Nota\: durante la ricerca di membri del gruppo predefinito, non verranno mostrati i giocatori offline senza altri permessi\!
luckperms.command.search.showing-users=Visualizzazione delle voci di utenti
luckperms.command.search.showing-groups=Visualizzazione delle voci del gruppo
luckperms.command.tree.start=Generazione dell''albero dei permessi, sei pregato di attendere...
luckperms.command.tree.empty=Impossibile generare l''albero, nessun risultato trovato
luckperms.command.tree.url=URL dell''albero dei permessi
luckperms.command.verbose.invalid-filter={0} non è un filtro verboso valido
luckperms.command.verbose.enabled=Registrazione dettagliata {0} per gli assegni corrispondenti {1}
luckperms.command.verbose.command-exec=Forzare {0} per eseguire il comando {1} e segnalare tutti i controlli effettuati...
luckperms.command.verbose.off=Dettagli Log
luckperms.command.verbose.command-exec-complete=Esecuzione del comando completata
luckperms.command.verbose.command.no-checks=Esecuzione del comando completata, ma non sono stati effettuali controlli sui permessi
luckperms.command.verbose.command.possibly-async=Questo potrebbe essere perchè il plugin esegue comandi in background (non sincronizzato)
luckperms.command.verbose.command.try-again-manually=Puoi ancora utilizzare manualmente Verbose per trovare controlli in questa maniera
luckperms.command.verbose.enabled-recording=Registrazione dettagliata {0} per gli assegni corrispondenti {1}
luckperms.command.verbose.uploading=Dettagli logging {0}, caricamento risultati...
luckperms.command.verbose.url=URL risultati dettagliati
luckperms.command.verbose.enabled-term=abilitato
luckperms.command.verbose.disabled-term=disabilitato
luckperms.command.verbose.query-any=QUALSIASI
luckperms.command.info.running-plugin=In esecuzione
luckperms.command.info.platform-key=Piattaforma
luckperms.command.info.server-brand-key=Marca Del Server
luckperms.command.info.server-version-key=Versione del server
luckperms.command.info.storage-key=Archiviazione
luckperms.command.info.storage-type-key=Tipo
luckperms.command.info.storage.meta.split-types-key=Tipi
luckperms.command.info.storage.meta.ping-key=Latenza
luckperms.command.info.storage.meta.connected-key=Connesso
luckperms.command.info.storage.meta.file-size-key=Dimensione File
luckperms.command.info.extensions-key=Estensioni
luckperms.command.info.messaging-key=Messaggi
luckperms.command.info.instance-key=Istanza
luckperms.command.info.static-contexts-key=Contesti statici
luckperms.command.info.online-players-key=Giocatori online
luckperms.command.info.online-players-unique={0} unico
luckperms.command.info.uptime-key=Tempo di attività
luckperms.command.info.local-data-key=Dati locali
luckperms.command.info.local-data={0} utenti, {1} gruppi, {2} tracce
luckperms.command.generic.create.success={0} è stato creato con successo
luckperms.command.generic.create.error=Si è verificato un errore durante la creazione di {0}
luckperms.command.generic.create.error-already-exists={0} già esistente\!
luckperms.command.generic.delete.success={0} è stato eliminato con successo
luckperms.command.generic.delete.error=Si è verificato un errore durante la creazione di {0}
luckperms.command.generic.delete.error-doesnt-exist={0} non esiste\!
luckperms.command.generic.rename.success={0} è stato rinominato con successo in {1}
luckperms.command.generic.clone.success={0} è stato clonato con successo in {1}
luckperms.command.generic.info.parent.title=Gruppo Genitore
luckperms.command.generic.info.parent.temporary-title=Gruppi Genitori Temporanei
luckperms.command.generic.info.expires-in=scade tra
luckperms.command.generic.info.inherited-from=ereditata da
luckperms.command.generic.info.inherited-from-self=te stesso
luckperms.command.generic.show-tracks.title=Tracce di {0}
luckperms.command.generic.show-tracks.empty={0} non è in nessuna traccia
luckperms.command.generic.clear.node-removed={0} nodi sono stati eliminati
luckperms.command.generic.clear.node-removed-singular=Il nodo {0} è stato rimosso
luckperms.command.generic.clear=I nodi di {0} sono stati cancellati nel contesto {1}
luckperms.command.generic.permission.info.title=Permessi di {0}
luckperms.command.generic.permission.info.empty={0} non dispone di alcun permesso impostato
luckperms.command.generic.permission.info.click-to-remove=Clicca per rimuovere questo nodo da {0}
luckperms.command.generic.permission.check.info.title=Informazioni permesso per {0}
luckperms.command.generic.permission.check.info.directly={0} ha {1} impostato a {2} nel contesto {3}
luckperms.command.generic.permission.check.info.inherited={0} eredita {1} impostato a {2} da {3} nel contesto {4}
luckperms.command.generic.permission.check.info.not-directly={0} non ha {1} impostato
luckperms.command.generic.permission.check.info.not-inherited={0} non eredita {1}
luckperms.command.generic.permission.check.result.title=Controllo permessi per {0}
luckperms.command.generic.permission.check.result.result-key=Risultati
luckperms.command.generic.permission.check.result.processor-key=Processore
luckperms.command.generic.permission.check.result.cause-key=Causa
luckperms.command.generic.permission.check.result.context-key=Contesto
luckperms.command.generic.permission.set={0} è stato impostato a {1} per {2} nel contesto {3}
luckperms.command.generic.permission.already-has={0} ha già {1} impostato nel contesto {2}
luckperms.command.generic.permission.set-temp=Imposta {0} a {1} per {2} per una durata di {3} nel contesto {4}
luckperms.command.generic.permission.already-has-temp={0} ha già {1} impostato temporaneamente nel contesto {2}
luckperms.command.generic.permission.unset=Azzera {0} per {1} nel contesto {2}
luckperms.command.generic.permission.doesnt-have={0} non ha {1} impostato nel contesto {2}
luckperms.command.generic.permission.unset-temp=Autorizzazione temporanea {0} non impostata per {1} nel contesto {2}
luckperms.command.generic.permission.subtract=Imposta {0} a {1} per {2} per una durata di {3} nel contesto {4}, {5} in meno di prima
luckperms.command.generic.permission.doesnt-have-temp={0} non ha {1} impostato nel contesto {2}
luckperms.command.generic.permission.clear=I permessi di {0} sono stati cancellati nel contesto {1}
luckperms.command.generic.parent.info.title={0}''s Principale
luckperms.command.generic.parent.info.empty={0} non dispone di alcun permesso impostato
luckperms.command.generic.parent.info.click-to-remove=Clicca per rimuovere questo nodo da {0}
luckperms.command.generic.parent.add={0} ora eredita i permessi da {1} nel contesto {2}
luckperms.command.generic.parent.add-temp={0} ora eredita i permessi da {1} per una durata di {2} nel contesto {3}
luckperms.command.generic.parent.set={0} ha cancellato i propri gruppi genitore esistenti, e ora eredita solo {1} nel contesto {2}
luckperms.command.generic.parent.set-track={0} ha cancellato i suoi gruppi genitore esistenti sulla traccia {1}, e ora eredita solo {2} nel contesto {3}
luckperms.command.generic.parent.remove={0} non eredita più i permessi da {1} nel contesto {2}
luckperms.command.generic.parent.remove-temp={0} non eredita più temporaneamente i permessi da {1} nel contesto {2}
luckperms.command.generic.parent.subtract={0} erediterà i permessi da {1} per una durata di {2} nel contesto {3}, {4} in meno rispetto a prima
luckperms.command.generic.parent.clear=I permessi di {0} sono stati cancellati nel contesto {1}
luckperms.command.generic.parent.clear-track=I principali di {0} sulla traccia {1} sono stati cancellati nel contesto {2}
luckperms.command.generic.parent.already-inherits={0} eredita già da {1} nel contesto {2}
luckperms.command.generic.parent.doesnt-inherit={0} non eredita da {1} nel contesto {2}
luckperms.command.generic.parent.already-temp-inherits={0} eredita già temporaneamente da {1} nel contesto {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} non eredita temporaneamente da {1} nel contesto {2}
luckperms.command.generic.chat-meta.info.title-prefix=Prefissi di {0}
luckperms.command.generic.chat-meta.info.title-suffix=Suffissi di {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} non ha prefissi
luckperms.command.generic.chat-meta.info.none-suffix={0} non ha suffissi
luckperms.command.generic.chat-meta.info.click-to-remove=Clicca per rimuovere questo {0} da {1}
luckperms.command.generic.chat-meta.already-has={0} ha già {1} {2} impostato su una priorità di {3} nel contesto {4}
luckperms.command.generic.chat-meta.already-has-temp={0} ha già {1} {2} impostato temporaneamente ad una priorità di {3} nel contesto {4}
luckperms.command.generic.chat-meta.doesnt-have={0} non ha {1} {2} impostato su una priorità di {3} nel contesto {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} ha già {1} {2} impostato su una priorità di {3} nel contesto {4}
luckperms.command.generic.chat-meta.add={0} ha già {1} {2} impostato su una priorità di {3} nel contesto {4}
luckperms.command.generic.chat-meta.add-temp={0} aveva {1} {2} impostato su una priorità di {3} per una durata di {4} nel contesto {5}
luckperms.command.generic.chat-meta.remove={0} aveva {1} {2} alla priorità {3} rimosso nel contesto {4}
luckperms.command.generic.chat-meta.remove-bulk={0} aveva tutti {1} alla priorità {2} rimossi nel contesto {3}
luckperms.command.generic.chat-meta.remove-temp={0} aveva temporaneamente {1} {2} alla priorità {3} rimossa nel contesto {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} aveva temporaneamente {1} {2} alla priorità {3} rimossa nel contesto {4}
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none={0} non ha meta
luckperms.command.generic.meta.info.click-to-remove=Clicca per rimuovere questo nodo da {0}
luckperms.command.generic.meta.already-has={0} ha già il meta chiave {1} impostato a {2} nel contesto {3}
luckperms.command.generic.meta.already-has-temp={0} ha già il meta chiave {1} temporaneamente impostato a {2} nel contesto {3}
luckperms.command.generic.meta.doesnt-have={0} non ha la chiave meta {1} impostata nel contesto {2}
luckperms.command.generic.meta.doesnt-have-temp={0} non ha la chiave meta {1} impostata nel contesto {2}
luckperms.command.generic.meta.set=Imposta il meta chiave {0} a {1} per {2} nel contesto {3}
luckperms.command.generic.meta.set-temp=Imposta {0} a {1} per {2} per una durata di {3} nel contesto {4}
luckperms.command.generic.meta.unset=Azzera {0} per {1} nel contesto {2}
luckperms.command.generic.meta.unset-temp=Autorizzazione temporanea {0} non impostata per {1} nel contesto {2}
luckperms.command.generic.meta.clear={0}''s il tipo di meta corrispondente {1} è stato cancellato nel contesto {2}
luckperms.command.generic.contextual-data.title=Menu contestuale
luckperms.command.generic.contextual-data.mode.key=modalità
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=giocatore attivo
luckperms.command.generic.contextual-data.contexts-key=Contesti
luckperms.command.generic.contextual-data.prefix-key=Prefisso
luckperms.command.generic.contextual-data.suffix-key=Suffisso
luckperms.command.generic.contextual-data.primary-group-key=Gruppo Primario
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Niente
luckperms.command.user.info.title=Info Utente
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=tipo
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Stato
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Non puoi rimuovere un utente dal loro gruppo primario
luckperms.command.user.primarygroup.not-member={0} non era già un membro di {1}, aggiungendoli ora
luckperms.command.user.primarygroup.already-has={0} ha già {1} impostato come gruppo primario
luckperms.command.user.primarygroup.warn-option=Attenzione\: il metodo di calcolo del gruppo primario utilizzato da questo server ({0}) potrebbe non riflettere questo cambiamento
luckperms.command.user.primarygroup.set={0}''s gruppo primario è stato impostato su {1}
luckperms.command.user.track.error-not-contain-group={0} non è già in nessun gruppo su {1}
luckperms.command.user.track.unsure-which-track=Non sei sicuro della traccia da usare, specificala come argomento
luckperms.command.user.track.missing-group-advice=Crea il gruppo o rimuovilo dalla traccia e riprova
luckperms.command.user.promote.added-to-first={0} non è in alcun gruppo su {1}, quindi sono stati aggiunti al primo gruppo, {2} nel contesto {3}
luckperms.command.user.promote.not-on-track={0} non è in nessun gruppo su {1}, quindi non è stato promosso
luckperms.command.user.promote.success=Promuovendo {0} lungo la traccia {1} da {2} a {3} nel contesto {4}
luckperms.command.user.promote.end-of-track=La fine della traccia {0} è stata raggiunta, impossibile promuovere {1}
luckperms.command.user.promote.next-group-deleted=Il prossimo gruppo sulla traccia, {0}, non esiste più
luckperms.command.user.promote.unable-to-promote=Impossibile rimuovere l''utente
luckperms.command.user.demote.success=Promuovendo {0} lungo la traccia {1} da {2} a {3} nel contesto {4}
luckperms.command.user.demote.end-of-track=La fine della traccia {0} è stata raggiunta, quindi {1} è stato rimosso da {2}
luckperms.command.user.demote.end-of-track-not-removed=La fine della traccia {0} è stata raggiunta, ma {1} non è stato rimosso dal primo gruppo
luckperms.command.user.demote.previous-group-deleted=Il prossimo gruppo sulla traccia, {0}, non esiste più
luckperms.command.user.demote.unable-to-demote=Impossibile rimuovere l''utente
luckperms.command.group.list.title=Gruppi
luckperms.command.group.delete.not-default=Non puoi eliminare il gruppo predefinito
luckperms.command.group.info.title=Informazioni gruppo
luckperms.command.group.info.display-name-key=Nome visualizzato
luckperms.command.group.info.weight-key=Peso
luckperms.command.group.setweight.set=Imposta il peso a {0} per il gruppo {1}
luckperms.command.group.setdisplayname.doesnt-have={0} non ha un nome visualizzato impostato
luckperms.command.group.setdisplayname.already-has={0} ha già un nome visualizzato di {1}
luckperms.command.group.setdisplayname.already-in-use=Il nome visualizzato {0} è già in uso da {1}
luckperms.command.group.setdisplayname.set=Imposta il nome visualizzato a {0} per il gruppo {1} nel contesto {2}
luckperms.command.group.setdisplayname.removed=Rimosso il nome visualizzato per il gruppo {0} nel contesto {1}
luckperms.command.track.list.title=Tracce
luckperms.command.track.path.empty=Niente
luckperms.command.track.info.showing-track=Mostra Traccia
luckperms.command.track.info.path-property=Percorso
luckperms.command.track.clear={0}''s la traccia dei gruppi è stata cancellata
luckperms.command.track.append.success=Il gruppo {0} è stato aggiunto alla traccia {1}
luckperms.command.track.insert.success=Il gruppo {0} è stato inserito nella traccia {1} alla posizione {2}
luckperms.command.track.insert.error-number=Numero atteso ma ricevuto\: {0}
luckperms.command.track.insert.error-invalid-pos=Impossibile inserire alla posizione {0}
luckperms.command.track.insert.error-invalid-pos-reason=posizione non valida
luckperms.command.track.remove.success=Il gruppo {0} è stato aggiunto alla traccia {1}
luckperms.command.track.error-empty={0} non può essere usato perché è vuoto o contiene un solo gruppo
luckperms.command.track.error-multiple-groups={0} è un membro di più gruppi su questa traccia
luckperms.command.track.error-ambiguous=Non riesco a determinare la tua posizione
luckperms.command.track.already-contains={0} contiene già {1}
luckperms.command.track.doesnt-contain={0} dovrebbe contenere {1}
luckperms.command.log.load-error=Impossibile caricare i dati
luckperms.command.log.invalid-page=Numero di pagina non valido
luckperms.command.log.invalid-page-range=Inserisci un valore tra {0} e {1}
luckperms.command.log.empty=Nessun log da mostrare
luckperms.command.log.notify.error-console=Impossibile attivare le notifiche per la console
luckperms.command.log.notify.enabled-term=Abilitato
luckperms.command.log.notify.disabled-term=Disabilitato
luckperms.command.log.notify.changed-state={0} logging output
luckperms.command.log.notify.already-on=Stai già ricevendo notifiche
luckperms.command.log.notify.already-off=Al momento non ricevi notifiche
luckperms.command.log.notify.invalid-state=Fortezza sconosciuta. Attesa {0} o {1}
luckperms.command.log.show.search=Mostrando le azioni recenti per la query {0}
luckperms.command.log.show.recent=Visualizzazione delle azioni recenti
luckperms.command.log.show.by=Mostrando le azioni recenti per la query {0}
luckperms.command.log.show.history=Mostrando la cronologia per {0} {1}
luckperms.command.export.error-term=Errore
luckperms.command.export.already-running=Un altro processo di esportazione è già in esecuzione
luckperms.command.export.file.already-exists=Il file {0} esiste già
luckperms.command.export.file.not-writable=Il file {0} non è scrivibile
luckperms.command.export.file.success=Esportato correttamente in\: {0}
luckperms.command.export.file-unexpected-error-writing=Si è verificato un errore inatteso durante la cancellazione del file
luckperms.command.export.web.export-code=Esporta come codice
luckperms.command.export.web.import-command-description=Usa i seguenti comandi
luckperms.command.import.term=Importa
luckperms.command.import.error-term=Errore
luckperms.command.import.already-running=Un altro processo di importazione è già in esecuzione
luckperms.command.import.file.doesnt-exist=Il file {0} non esiste
luckperms.command.import.file.not-readable=Il file {0} non è leggibile
luckperms.command.import.file.unexpected-error-reading=Si è verificato un errore inatteso durante la cancellazione del file
luckperms.command.import.file.correct-format=è il formato corretto?
luckperms.command.import.web.unable-to-read=Non è stato possibile leggere i dati usando il codice fornito
luckperms.command.import.progress.percent={0}% completato
luckperms.command.import.progress.operations={0}/{1} operazioni completate
luckperms.command.import.starting=Avvia il processo d''importazione
luckperms.command.import.completed=COMPLETATO
luckperms.command.import.duration=tra {0} secondi
luckperms.command.bulkupdate.must-use-console=Il comando di aggiornamento di massa può essere utilizzato solo dalla console
luckperms.command.bulkupdate.invalid-data-type=Tipo non valido, era in attesa {0}
luckperms.command.bulkupdate.invalid-constraint=Vincolo non valido {0}
luckperms.command.bulkupdate.invalid-constraint-format=I vincoli dovrebbero essere nel formato {0}
luckperms.command.bulkupdate.invalid-comparison=Operatore di confronto non valido {0}
luckperms.command.bulkupdate.invalid-comparison-format=Atteso uno dei seguenti\: {0}
luckperms.command.bulkupdate.queued=L''operazione di aggiornamento di massa è stata in coda
luckperms.command.bulkupdate.confirm=Esegui {0} per eseguire l''aggiornamento
luckperms.command.bulkupdate.unknown-id=L''operazione con id {0} non esiste o è scaduta
luckperms.command.bulkupdate.starting=Aggiornamento in esecuzione in serie
luckperms.command.bulkupdate.success=Aggiornamento completato con successo
luckperms.command.bulkupdate.success.statistics.nodes=Totale nodi interessati
luckperms.command.bulkupdate.success.statistics.users=Totale utenti attivi
luckperms.command.bulkupdate.success.statistics.groups=Totale utenti attivi
luckperms.command.bulkupdate.failure=Aggiornamento di massa non riuscito, controlla la console per errori
luckperms.command.update-task.request=È stata richiesta un''attività di aggiornamento, attendere prego
luckperms.command.update-task.complete=Aggiornamento completato
luckperms.command.update-task.push.attempting=Tentativo di inviare su altri server
luckperms.command.update-task.push.complete=Altri server sono stati notificati tramite {0} con successo
luckperms.command.update-task.push.error=Errore durante l''invio delle modifiche ad altri server
luckperms.command.update-task.push.error-not-setup=Impossibile inviare le modifiche ad altri server in quanto un servizio di messaggistica non è stato configurato
luckperms.command.reload-config.success=Il file di configurazione è stato ricaricato
luckperms.command.reload-config.restart-note=alcune opzioni verranno applicate solo dopo il riavvio del server
luckperms.command.translations.searching=Ricerca delle traduzioni disponibili, attendere prego...
luckperms.command.translations.searching-error=Impossibile ottenere un elenco di traduzioni disponibili
luckperms.command.translations.installed-translations=Traduzioni moduli installati
luckperms.command.translations.available-translations=Traduzioni Disponibili
luckperms.command.translations.percent-translated={0}% tradotto
luckperms.command.translations.translations-by=da
luckperms.command.translations.installing=Installazione pacchetto, attendere prego...
luckperms.command.translations.download-error=Impossibile scaricare la traduzione per {0}
luckperms.command.translations.installing-specific=Installazione lingua {0}...
luckperms.command.translations.install-complete=Installazione completata
luckperms.command.translations.download-prompt=Usa {0} per scaricare e installare le versioni aggiornate di queste traduzioni fornite dalla community
luckperms.command.translations.download-override-warning=Si prega di notare che questo sovrascriverà qualsiasi modifica apportata per queste lingue
luckperms.usage.user.description=Un insieme di comandi per la gestione degli utenti all''interno di LuckPerms. (Un ''utente'' in LuckPerms è solo un giocatore, e può fare riferimento a un UUID o un nome utente)
luckperms.usage.group.description=Un insieme di comandi per gestire i gruppi all''interno di LuckPerms. I gruppi sono solo collezioni di assegnazioni di permessi che possono essere date agli utenti. I nuovi gruppi sono fatti usando il comando ''creategroup''.
luckperms.usage.track.description=Un insieme di comandi per la gestione delle tracce all''interno di LuckPerms. Le tracce sono una raccolta ordinata di gruppi che possono essere utilizzati per definire promozioni e demovimenti.
luckperms.usage.log.description=Un insieme di comandi per gestire la funzionalità di registrazione all''interno di LuckPerms.
luckperms.usage.sync.description=Ricarica tutti i dati dalla memoria dei plugin in memoria e applica tutte le modifiche rilevate.
luckperms.usage.info.description=Stampa informazioni generali sull''istanza del plugin attivo.
luckperms.usage.editor.description=Crea una nuova sessione web editor
luckperms.usage.editor.argument.type=Tipi da caricare nell''editor. (''all'', ''users'' o ''groups'')
luckperms.usage.editor.argument.filter=permesso di filtrare le voci utente per
luckperms.usage.verbose.description=Controlla il sistema di controllo permessi dettagliati dei plugin.
luckperms.usage.verbose.argument.action=se abilitare/disabilitare la registrazione, o per caricare l''output registrato
luckperms.usage.verbose.argument.filter=il filtro con cui abbinare le voci
luckperms.usage.verbose.argument.commandas=il giocatore/comando da eseguire
luckperms.usage.tree.description=Genera una vista ad albero (lista ordinata gerarchia) di tutti i permessi noti a LuckPerms.
luckperms.usage.tree.argument.scope=la radice dell''albero. specificare "." per includere tutti i permessi
luckperms.usage.tree.argument.player=il nome di un giocatore online da controllare
luckperms.usage.search.description=Cerca tra tutti i gruppi / utenti con uno specifico permesso
luckperms.usage.search.argument.permission=il permesso di cercare
luckperms.usage.search.argument.page=la pagina da visualizzare
luckperms.usage.network-sync.description=Sincronizza le modifiche con l''archivio e richiede che tutti gli altri server della rete facciano lo stesso
luckperms.usage.import.description=Importa dati da un file di esportazione (creato precedentemente)
luckperms.usage.import.argument.file=seleziona il file da cui importare
luckperms.usage.import.argument.replace=sostituire i dati esistenti invece di unire
luckperms.usage.import.argument.upload=caricare i dati da una precedente esportazione
luckperms.usage.export.description=Esporta tutti i dati delle autorizzazioni in un file ''export''. Può essere reimportato in un secondo momento.
luckperms.usage.export.argument.file=il file in cui esportare
luckperms.usage.export.argument.without-users=esclude gli utenti dall''esportazione
luckperms.usage.export.argument.without-groups=escludere gruppi dall''esportazione
luckperms.usage.export.argument.upload=Carica tutti i dati di autorizzazione sul webeditor. Può essere reimportato in un secondo momento.
luckperms.usage.reload-config.description=Ricarica alcune delle opzioni di configurazione
luckperms.usage.bulk-update.description=Esegui query di cambio in serie su tutti i dati
luckperms.usage.bulk-update.argument.data-type=il tipo di dati da modificare. (''all'', ''utenti'' o ''gruppi'')
luckperms.usage.bulk-update.argument.action=l''azione da eseguire sui dati. (''update'' o ''delete'')
luckperms.usage.bulk-update.argument.action-field=il campo su cui agire. Richiesto solo per ''update''. (''permission'', ''server'' o ''world'')
luckperms.usage.bulk-update.argument.action-value=il valore con cui sostituire. Richiesto solo per ''update''.
luckperms.usage.bulk-update.argument.constraint=i vincoli richiesti per l''aggiornamento
luckperms.usage.translations.description=Gestisci le traduzioni
luckperms.usage.translations.argument.install=sottocomando per installare le traduzioni
luckperms.usage.apply-edits.description=Applica le modifiche ai permessi fatte dall''editor web
luckperms.usage.apply-edits.argument.code=il codice univoco dei dati
luckperms.usage.apply-edits.argument.target=a chi applicare i dati a
luckperms.usage.create-group.description=Crea un nuovo gruppo
luckperms.usage.create-group.argument.name=nome del gruppo
luckperms.usage.create-group.argument.weight=il peso del gruppo
luckperms.usage.create-group.argument.display-name=il nome visualizzato del gruppo
luckperms.usage.delete-group.description=Elimina gruppo
luckperms.usage.delete-group.argument.name=nome del gruppo
luckperms.usage.list-groups.description=Elenca tutti i gruppi sulla piattaforma
luckperms.usage.create-track.description=Crea nuova traccia
luckperms.usage.create-track.argument.name=il nome della traccia
luckperms.usage.delete-track.description=Elimina traccia
luckperms.usage.delete-track.argument.name=il nome della traccia
luckperms.usage.list-tracks.description=Elenca tutti i gruppi sulla piattaforma
luckperms.usage.user-info.description=Mostra le informazioni del tuo server
luckperms.usage.user-switchprimarygroup.description=Cambia il gruppo principale dell''utente
luckperms.usage.user-switchprimarygroup.argument.group=il gruppo a cui passare
luckperms.usage.user-promote.description=Promuove l''utente su una traccia
luckperms.usage.user-promote.argument.track=la traccia per promuovere l''utente su
luckperms.usage.user-promote.argument.context=i contesti in cui promuovere l''utente
luckperms.usage.user-promote.argument.dont-add-to-first=promuove l''utente solo se sono già in pista
luckperms.usage.user-demote.description=Promuove l''utente su una traccia
luckperms.usage.user-demote.argument.track=la traccia per promuovere l''utente su
luckperms.usage.user-demote.argument.context=i contesti in cui promuovere l''utente
luckperms.usage.user-demote.argument.dont-remove-from-first=impedire che l''utente venga rimosso dal primo gruppo
luckperms.usage.user-clone.description=Clona un utente
luckperms.usage.user-clone.argument.user=il nome/uuuid dell''utente su cui clonare
luckperms.usage.group-info.description=Fornisce informazioni sul gruppo
luckperms.usage.group-listmembers.description=Mostra gli utenti/gruppi che ereditano da questo gruppo
luckperms.usage.group-listmembers.argument.page=la pagina da visualizzare
luckperms.usage.group-setweight.description=Imposta il peso dei gruppi
luckperms.usage.group-setweight.argument.weight=il peso da impostare
luckperms.usage.group-set-display-name.description=Imposta il nome visualizzato dei gruppi
luckperms.usage.group-set-display-name.argument.name=il nome da impostare
luckperms.usage.group-set-display-name.argument.context=i contesti in cui promuovere l''utente
luckperms.usage.group-rename.description=Rinomina il gruppo
luckperms.usage.group-rename.argument.name=il nuovo nome
luckperms.usage.group-clone.description=Clona il gruppo
luckperms.usage.group-clone.argument.name=il nome del gruppo su cui clonare
luckperms.usage.holder-editor.description=Apre l''editor dei permessi web
luckperms.usage.holder-showtracks.description=Elenca le tracce su cui è attivo l''oggetto
luckperms.usage.holder-clear.description=Rimuove tutti i permessi, genitori e meta
luckperms.usage.holder-clear.argument.context=i contesti da filtrare per
luckperms.usage.permission.description=Modifica i permessi
luckperms.usage.parent.description=Modifica eredità
luckperms.usage.meta.description=Modifica Valori Predefiniti
luckperms.usage.permission-info.description=Elenca i nodi di autorizzazione che l''oggetto ha
luckperms.usage.permission-info.argument.page=la pagina da visualizzare
luckperms.usage.permission-info.argument.sort-mode=come ordinare le voci
luckperms.usage.permission-set.description=Imposta un permesso per l''oggetto
luckperms.usage.permission-set.argument.node=il nodo di autorizzazione da impostare
luckperms.usage.permission-set.argument.value=il valore del campo
luckperms.usage.permission-set.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.permission-unset.description=Imposta un permesso per l''oggetto
luckperms.usage.permission-unset.argument.node=il nodo di autorizzazione da impostare
luckperms.usage.permission-unset.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.permission-settemp.description=Imposta temporaneamente un permesso per l''oggetto
luckperms.usage.permission-settemp.argument.node=il nodo di autorizzazione da impostare
luckperms.usage.permission-settemp.argument.value=il valore del campo
luckperms.usage.permission-settemp.argument.duration=durata fino alla scadenza del nodo autorizzazione
luckperms.usage.permission-settemp.argument.temporary-modifier=modalità di applicazione dell''autorizzazione temporanea
luckperms.usage.permission-settemp.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.permission-unsettemp.description=Imposta un permesso per l''oggetto
luckperms.usage.permission-unsettemp.argument.node=il nodo di autorizzazione da impostare
luckperms.usage.permission-unsettemp.argument.duration=la durata di sottrazione
luckperms.usage.permission-unsettemp.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.permission-check.description=Controlla se l''oggetto ha un certo nodo di autorizzazione
luckperms.usage.permission-check.argument.node=il nodo di autorizzazione da impostare
luckperms.usage.permission-clear.description=Elimina autorizzazione
luckperms.usage.permission-clear.argument.context=i contesti da filtrare per
luckperms.usage.parent-info.description=Elenca i gruppi da cui questo oggetto eredita
luckperms.usage.parent-info.argument.page=la pagina da visualizzare
luckperms.usage.parent-info.argument.sort-mode=come ordinare le voci
luckperms.usage.parent-set.description=Rimuove tutti gli altri gruppi l''oggetto eredita già e li aggiunge a quello dato
luckperms.usage.parent-set.argument.group=il gruppo a cui passare
luckperms.usage.parent-set.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.parent-add.description=Imposta un altro gruppo per l''oggetto da cui ereditare i permessi
luckperms.usage.parent-add.argument.group=il gruppo che eredita da
luckperms.usage.parent-add.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.parent-remove.description=Rimuove una regola di eredità precedentemente impostata
luckperms.usage.parent-remove.argument.group=il gruppo a cui passare
luckperms.usage.parent-remove.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.parent-set-track.description=Rimuove tutti gli altri gruppi l''oggetto eredita già e li aggiunge a quello dato
luckperms.usage.parent-set-track.argument.track=la traccia su cui impostare
luckperms.usage.parent-set-track.argument.group=il gruppo da fissare o un numero relativo alla posizione del gruppo sulla pista data
luckperms.usage.parent-set-track.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.parent-add-temp.description=Imposta un altro gruppo per l''oggetto da cui ereditare i permessi
luckperms.usage.parent-add-temp.argument.group=il gruppo che eredita da
luckperms.usage.parent-add-temp.argument.duration=la durata della composizione del gruppo
luckperms.usage.parent-add-temp.argument.temporary-modifier=modalità di applicazione dell''autorizzazione temporanea
luckperms.usage.parent-add-temp.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.parent-remove-temp.description=Rimuove una regola di eredità precedentemente impostata
luckperms.usage.parent-remove-temp.argument.group=il gruppo a cui passare
luckperms.usage.parent-remove-temp.argument.duration=la durata di sottrazione
luckperms.usage.parent-remove-temp.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.parent-clear.description=Cancella tutti i meta
luckperms.usage.parent-clear.argument.context=i contesti da filtrare per
luckperms.usage.parent-clear-track.description=Cancella tutti i genitori su una determinata traccia
luckperms.usage.parent-clear-track.argument.track=la traccia su cui impostare
luckperms.usage.parent-clear-track.argument.context=i contesti da filtrare per
luckperms.usage.meta-info.description=Mostra tutti i meta della chat
luckperms.usage.meta-set.description=Imposta un meta valore
luckperms.usage.meta-set.argument.key=il nome da impostare
luckperms.usage.meta-set.argument.value=il nome da impostare
luckperms.usage.meta-set.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-unset.description=Imposta un meta valore
luckperms.usage.meta-unset.argument.key=il nome da impostare
luckperms.usage.meta-unset.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-settemp.description=Imposta temporaneamente un meta valore
luckperms.usage.meta-settemp.argument.key=la chiave da impostare
luckperms.usage.meta-settemp.argument.value=il valore da settare
luckperms.usage.meta-settemp.argument.duration=la durata fino alla scadenza del meta value
luckperms.usage.meta-settemp.argument.context=i contesti in cui aggiungere il meta pair
luckperms.usage.meta-unsettemp.description=Azzera un meta valore temporaneo
luckperms.usage.meta-unsettemp.argument.key=la chiave da impostare
luckperms.usage.meta-unsettemp.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-addprefix.description=Imposta un prefisso
luckperms.usage.meta-addprefix.argument.priority=la priorità da aggiungere il prefisso a
luckperms.usage.meta-addprefix.argument.prefix=la stringa suffisso
luckperms.usage.meta-addprefix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-addsuffix.description=Aggiunge un suffisso
luckperms.usage.meta-addsuffix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-addsuffix.argument.suffix=la stringa suffisso
luckperms.usage.meta-addsuffix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-setprefix.description=Imposta un prefisso
luckperms.usage.meta-setprefix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-setprefix.argument.prefix=la stringa suffisso
luckperms.usage.meta-setprefix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-setsuffix.description=Aggiunge un suffisso
luckperms.usage.meta-setsuffix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-setsuffix.argument.suffix=la stringa suffisso
luckperms.usage.meta-setsuffix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-removeprefix.description=Rimuove un prefisso
luckperms.usage.meta-removeprefix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-removeprefix.argument.prefix=la stringa suffisso
luckperms.usage.meta-removeprefix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-removesuffix.description=Rimuove un prefisso
luckperms.usage.meta-removesuffix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-removesuffix.argument.suffix=la stringa suffisso
luckperms.usage.meta-removesuffix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-addtemp-prefix.description=Aggiunge temporaneamente un prefisso
luckperms.usage.meta-addtemp-prefix.argument.priority=la priorità da aggiungere il prefisso a
luckperms.usage.meta-addtemp-prefix.argument.prefix=la stringa suffisso
luckperms.usage.meta-addtemp-prefix.argument.duration=la durata fino alla scadenza del prefisso
luckperms.usage.meta-addtemp-prefix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-addtemp-suffix.description=Aggiunge temporaneamente un suffisso
luckperms.usage.meta-addtemp-suffix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-addtemp-suffix.argument.suffix=la stringa suffisso
luckperms.usage.meta-addtemp-suffix.argument.duration=la durata fino alla scadenza del prefisso
luckperms.usage.meta-addtemp-suffix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-settemp-prefix.description=Aggiunge temporaneamente un prefisso
luckperms.usage.meta-settemp-prefix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-settemp-prefix.argument.prefix=la stringa suffisso
luckperms.usage.meta-settemp-prefix.argument.duration=la durata fino alla scadenza del prefisso
luckperms.usage.meta-settemp-prefix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-settemp-suffix.description=Aggiunge temporaneamente un suffisso
luckperms.usage.meta-settemp-suffix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-settemp-suffix.argument.suffix=la stringa suffisso
luckperms.usage.meta-settemp-suffix.argument.duration=la durata fino alla scadenza del prefisso
luckperms.usage.meta-settemp-suffix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-removetemp-prefix.description=Rimuove un prefisso temporaneo
luckperms.usage.meta-removetemp-prefix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-removetemp-prefix.argument.prefix=la stringa suffisso
luckperms.usage.meta-removetemp-prefix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-removetemp-suffix.description=Rimuove un prefisso temporaneo
luckperms.usage.meta-removetemp-suffix.argument.priority=la priorità da aggiungere il suffisso a
luckperms.usage.meta-removetemp-suffix.argument.suffix=la stringa suffisso
luckperms.usage.meta-removetemp-suffix.argument.context=i contesti in cui aggiungere il permesso
luckperms.usage.meta-clear.description=Cancella tutti i meta
luckperms.usage.meta-clear.argument.type=il tipo di meta da rimuovere
luckperms.usage.meta-clear.argument.context=i contesti da filtrare per
luckperms.usage.track-info.description=Fornisce informazioni sulla traccia
luckperms.usage.track-editor.description=Apre l''editor di permessi online
luckperms.usage.track-append.description=Aggiunge un gruppo alla fine del binario
luckperms.usage.track-append.argument.group=il gruppo da aggiungere
luckperms.usage.track-insert.description=Inserisce un gruppo in una determinata posizione lungo la traccia
luckperms.usage.track-insert.argument.group=il gruppo da aggiungere
luckperms.usage.track-insert.argument.position=la posizione per inserire il gruppo in (la prima posizione sulla pista è 1)
luckperms.usage.track-remove.description=Rimuove un gruppo dalla traccia
luckperms.usage.track-remove.argument.group=il gruppo da rimuovere
luckperms.usage.track-clear.description=Cancella i gruppi sulla traccia
luckperms.usage.track-rename.description=Rinomina il gruppo
luckperms.usage.track-rename.argument.name=il nuovo nome
luckperms.usage.track-clone.description=Clona il gruppo
luckperms.usage.track-clone.argument.name=il nome del gruppo su cui clonare
luckperms.usage.log-recent.description=Visualizzazione delle azioni recenti
luckperms.usage.log-recent.argument.user=il nome/uuuid dell''utente da filtrare per
luckperms.usage.log-recent.argument.page=il numero di pagina da visualizzare
luckperms.usage.log-search.description=Cerca il log per una voce
luckperms.usage.log-search.argument.query=la query da cercare per
luckperms.usage.log-search.argument.page=il numero di pagina da visualizzare
luckperms.usage.log-notify.description=Attiva/Disattiva notifiche di registro
luckperms.usage.log-notify.argument.toggle=se attivare o disattivare
luckperms.usage.log-user-history.description=Visualizza la cronologia di un utente
luckperms.usage.log-user-history.argument.user=il nome/uuuid dell’utente
luckperms.usage.log-user-history.argument.page=il numero di pagina da visualizzare
luckperms.usage.log-group-history.description=Visualizza lo storico del gruppo
luckperms.usage.log-group-history.argument.group=nome del gruppo
luckperms.usage.log-group-history.argument.page=il numero di pagina da visualizzare
luckperms.usage.log-track-history.description=Visualizza lo storico del gruppo
luckperms.usage.log-track-history.argument.track=il nome della traccia
luckperms.usage.log-track-history.argument.page=il numero di pagina da visualizzare
luckperms.usage.sponge.description=Modifica dati extra di Sponge
luckperms.usage.sponge.argument.collection=la raccolta da interrogare
luckperms.usage.sponge.argument.subject=l’oggetto della modifica
luckperms.usage.sponge-permission-info.description=Mostra informazioni sulle opzioni dell''oggetto
luckperms.usage.sponge-permission-info.argument.contexts=i contesti da filtrare per
luckperms.usage.sponge-permission-set.description=Imposta un permesso per l''oggetto
luckperms.usage.sponge-permission-set.argument.node=il nodo di autorizzazione da impostare
luckperms.usage.sponge-permission-set.argument.tristate=il valore su cui impostare la chiave
luckperms.usage.sponge-permission-set.argument.contexts=i contesti in cui aggiungere il permesso
luckperms.usage.sponge-permission-clear.description=Ripulisce i genitori dei soggetti
luckperms.usage.sponge-permission-clear.argument.contexts=i contesti in cui cancellare le opzioni
luckperms.usage.sponge-parent-info.description=Mostra informazioni sulle opzioni dell''oggetto
luckperms.usage.sponge-parent-info.argument.contexts=i contesti da filtrare per
luckperms.usage.sponge-parent-add.description=Aggiunge un genitore all''oggetto
luckperms.usage.sponge-parent-add.argument.collection=la raccolta dell''oggetto in cui l''oggetto genitore è
luckperms.usage.sponge-parent-add.argument.subject=il nome dell''oggetto genitore
luckperms.usage.sponge-parent-add.argument.contexts=i contesti in cui aggiungere il genitore
luckperms.usage.sponge-parent-remove.description=Rimuove un genitore dall''oggetto
luckperms.usage.sponge-parent-remove.argument.collection=la raccolta dell''oggetto in cui l''oggetto genitore è
luckperms.usage.sponge-parent-remove.argument.subject=il nome dell''oggetto genitore
luckperms.usage.sponge-parent-remove.argument.contexts=i contesti in cui rimuovere il genitore
luckperms.usage.sponge-parent-clear.description=Ripulisce i genitori dei soggetti
luckperms.usage.sponge-parent-clear.argument.contexts=i contesti in cui rimuovere il genitore
luckperms.usage.sponge-option-info.description=Mostra informazioni sulle opzioni dell''oggetto
luckperms.usage.sponge-option-info.argument.contexts=il contesto da filtrare per
luckperms.usage.sponge-option-set.description=Imposta un''opzione per l''oggetto
luckperms.usage.sponge-option-set.argument.key=la chiave da impostare
luckperms.usage.sponge-option-set.argument.value=il valore su cui impostare la chiave
luckperms.usage.sponge-option-set.argument.contexts=i contesti in cui aggiungere il permesso
luckperms.usage.sponge-option-unset.description=Imposta un''opzione per l''oggetto
luckperms.usage.sponge-option-unset.argument.key=la chiave da impostare
luckperms.usage.sponge-option-unset.argument.contexts=i contesti in cui promuovere l''utente
luckperms.usage.sponge-option-clear.description=Ripulisce i genitori dei soggetti
luckperms.usage.sponge-option-clear.argument.contexts=i contesti in cui cancellare le opzioni
