luckperms.logs.actionlog-prefix=سجل
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=تصدير
luckperms.commandsystem.available-commands=استخدم {0} لعرض الأوامر المتاحة
luckperms.commandsystem.command-not-recognised=لم يتم التعرف على الأمر
luckperms.commandsystem.no-permission=ليس لديك الإذن لاستخدام هذا الأمر\!
luckperms.commandsystem.no-permission-subcommands=ليس لديك الإذن لاستخدام أي أوامر فرعية
luckperms.commandsystem.already-executing-command=يجري تنفيذ أمر آخر، في انتظار إنهائه...
luckperms.commandsystem.usage.sub-commands-header=أوامر فرعية
luckperms.commandsystem.usage.usage-header=إستخدام الأمر
luckperms.commandsystem.usage.arguments-header=الوسائط
luckperms.first-time.no-permissions-setup=يبدو أنه لم يتم إعداد أي أذونات بعد\!
luckperms.first-time.use-console-to-give-access=قبل ان تتمكن من إستعمال أي من أوامر LuckPerms في اللعبة, تحتاج أن تستخدم لوحة التحكم لمنح الصلاحية لنفسك
luckperms.first-time.console-command-prompt=افتح لوحة التحكم خاصتك وشغل
luckperms.first-time.next-step=بعد أن تنتهى من هذا, يمكنك أن تبتدأ في تحديد الصلاحيات والمجموعات الخاصة بك
luckperms.first-time.wiki-prompt=لا تعرف من أين تبدأ؟ تحقق هنا\: {0}
luckperms.login.try-again=الرجاء المحاولة مرة أخرى لاحقاً
luckperms.login.loading-database-error=حدث خطأ في قاعدة البيانات أثناء تحميل بيانات الصلاحيات
luckperms.login.server-admin-check-console-errors=إذا كنت مشرفاً على الخادم, يرجى الذهاب إلى لوحة التحكم لمعرفة أي أخطاء
luckperms.login.server-admin-check-console-info=يرجى التحقق من وحدة تحكم الخادم للمزيد من المعلومات
luckperms.login.data-not-loaded-at-pre=بيانات الصلاحيات لمستخدمك فشل الحصول عليها أثناء مرحلة ما قبل دخول السيرفر
luckperms.login.unable-to-continue=يعذر الإستمرار
luckperms.login.craftbukkit-offline-mode-error=من المحتمل أنه بسبب تعارض بين CraftBukkit و إعداد الonline-mode
luckperms.login.unexpected-error=حدث خطأ غير متوقع خلال عملية تثبيت بيانات الصلاحيات
luckperms.opsystem.disabled=نظام الأوب الإعتيادي غير مفعل في هذا السيرفر
luckperms.opsystem.sponge-warning=يرجى الملاحظة بأن حالة متحكم السيرفر ليس له تأثير على فحوص صلاحيات Sponge حال وجود بلوقن صلاحيات مثبت، يجب عليك تعديل بيانات المستخدم مباشرة
luckperms.duration.unit.years.plural={0} سنوات
luckperms.duration.unit.years.singular={0} سنة
luckperms.duration.unit.years.short={0}س
luckperms.duration.unit.months.plural={0} شهور
luckperms.duration.unit.months.singular={0} شهر
luckperms.duration.unit.months.short={0}شهر
luckperms.duration.unit.weeks.plural={0} أسابيع
luckperms.duration.unit.weeks.singular={0} أسبوع
luckperms.duration.unit.weeks.short={0}أسب
luckperms.duration.unit.days.plural={0} أيام
luckperms.duration.unit.days.singular={0} يوم
luckperms.duration.unit.days.short={0}يوم
luckperms.duration.unit.hours.plural={0} ساعات
luckperms.duration.unit.hours.singular={0} ساعة
luckperms.duration.unit.hours.short={0}سا
luckperms.duration.unit.minutes.plural={0} دقائق
luckperms.duration.unit.minutes.singular={0} دقيقة
luckperms.duration.unit.minutes.short={0}د
luckperms.duration.unit.seconds.plural={0} ثواني
luckperms.duration.unit.seconds.singular={0} ثانية
luckperms.duration.unit.seconds.short={0}د
luckperms.duration.since={0} قبل
luckperms.command.misc.invalid-code=رمز غير صالح
luckperms.command.misc.response-code-key=رمز الأستجابة
luckperms.command.misc.error-message-key=الرسالة
luckperms.command.misc.bytebin-unable-to-communicate=يعذر الإتصال ب bytebin
luckperms.command.misc.webapp-unable-to-communicate=يعذر الإتصال ب ال web app
luckperms.command.misc.check-console-for-errors=انظر الكونسول للأخطاء
luckperms.command.misc.file-must-be-in-data=الملف {0} يجب أن يكون مجلد داخلي مباشرة لمجلد البيانات
luckperms.command.misc.wait-to-finish=الرجاء الإنتظار حتى ينتهي وحاول مرة أخرى
luckperms.command.misc.invalid-priority=أولوية غير صالحة {0}
luckperms.command.misc.expected-number=توقعت رقماً
luckperms.command.misc.date-parse-error=تعذر تحليل تاريخ {0}
luckperms.command.misc.date-in-past-error=لا يمكنك تعيين تاريخ في الماضي\!
luckperms.command.misc.page=الصفحة {0} من {1}
luckperms.command.misc.page-entries={0} مدخلات
luckperms.command.misc.none=لا شيء
luckperms.command.misc.loading.error.unexpected=حدث خطأ غير متوقع
luckperms.command.misc.loading.error.user=لم يتم تحميل المستخدم
luckperms.command.misc.loading.error.user-specific=تعذر تحميل المستخدم المستهدف {0}
luckperms.command.misc.loading.error.user-not-found=لم يتم العثور على مستخدم لـ {0}
luckperms.command.misc.loading.error.user-save-error=حدث خطأ أثناء حفظ بيانات المستخدم ل {0}
luckperms.command.misc.loading.error.user-not-online=المستخدم {0} ليس متصلا
luckperms.command.misc.loading.error.user-invalid="{0}" صيغة اسم المستخدم \\ uuid غير صحيحة
luckperms.command.misc.loading.error.user-not-uuid=المستخدم المستهدف {0} ليس uuid صالح
luckperms.command.misc.loading.error.group=لم يتم تحميل الرتبة
luckperms.command.misc.loading.error.all-groups=غير قادر على تحميل جميع الرتب
luckperms.command.misc.loading.error.group-not-found=تعذر العثور على رتبة تسمى {0}
luckperms.command.misc.loading.error.group-save-error=حدث خطأ أثناء حفظ بيانات الرتبة لـ {0}
luckperms.command.misc.loading.error.group-invalid={0} ليس اسم رتبة صالح
luckperms.command.misc.loading.error.track=لم يتم تحميل المسار
luckperms.command.misc.loading.error.all-tracks=تعذر تحميل جميع المسارات
luckperms.command.misc.loading.error.track-not-found=لم يتم العثور على المسار المسمى {0}
luckperms.command.misc.loading.error.track-save-error=حدث خطأ أثناء حفظ بيانات المسار لـ {0}
luckperms.command.misc.loading.error.track-invalid={0} ليس اسم مسار صالح
luckperms.command.editor.no-match=غير قادر على فتح المحرر، لا يوجد كائنات مطابقة للنوع المطلوب
luckperms.command.editor.start=إعداد جلسة محرر جديدة، الرجاء الانتظار...
luckperms.command.editor.url=اضغط على الرابط أدناه لفتح المحرر
luckperms.command.editor.unable-to-communicate=تعذر الاتصال مع المحرر
luckperms.command.editor.apply-edits.success=تم تطبيق بيانات محرر الويب على {0} {1} بنجاح
luckperms.command.editor.apply-edits.success-summary={0} {1} و {2} {3}
luckperms.command.editor.apply-edits.success.additions=الإضافات
luckperms.command.editor.apply-edits.success.additions-singular=إضافة
luckperms.command.editor.apply-edits.success.deletions=المحذوفات
luckperms.command.editor.apply-edits.success.deletions-singular=المحذوف
luckperms.command.editor.apply-edits.no-changes=لم يتم تطبيق أي تغييرات من محرر الويب، والبيانات المرسلة لم تحتوي على أي تعديلات
luckperms.command.editor.apply-edits.unknown-type=تعذر تطبيق التعديل على نوع الكائن المحدد
luckperms.command.editor.apply-edits.unable-to-read=غير قادر على قراءة البيانات باستخدام الرمز المعطى
luckperms.command.search.searching.permission=البحث عن المستخدمين والرتب بواسطة {0}
luckperms.command.search.searching.inherit=البحث عن المستخدمين والرتب التي ترث من {0}
luckperms.command.search.result=تم العثور على {0} مدخلات من {1} المستخدمين و {2} رتب
luckperms.command.search.result.default-notice=ملاحظة\: عند البحث عن أعضاء الرتبة الأولية، لن يتم عرض اللاعبين غير المتصلين بأي صلاحيات أخرى\!
luckperms.command.search.showing-users=يتم عرض مدخلات المستخدم
luckperms.command.search.showing-groups=يتم عرض مدخلات الرتبة
luckperms.command.tree.start=إنشاء شجرة الصلاحية، الرجاء الانتظار...
luckperms.command.tree.empty=غير قادر على إنشاء شجرة، لم يتم العثور على نتائج
luckperms.command.tree.url=رابط شجرة الصلاحية
luckperms.command.verbose.invalid-filter={0} ليس فلتر شفوي صالح
luckperms.command.verbose.enabled=تسجيل الفيربوز {0} للفحص المطابق {1}
luckperms.command.verbose.command-exec=إجبار {0} على تنفيذ الأمر {1} والإبلاغ عن جميع عمليات التحقق...
luckperms.command.verbose.off=تسجيل فرعي {0}
luckperms.command.verbose.command-exec-complete=اكتمل تنفيذ الأمر
luckperms.command.verbose.command.no-checks=اكتمل تنفيذ الأمر، ولكن لم يتم التحقق من الأذونات
luckperms.command.verbose.command.possibly-async=قد يكون ذلك لأن البلقن يقوم بتفعيل الأوامر في الخلفية (async)
luckperms.command.verbose.command.try-again-manually=لا يزال بإمكانك استخدام الأمر المطول يدوياً للكشف عن الفحوص التي أجريت هكذا
luckperms.command.verbose.enabled-recording=تسجيل الفيربوز {0} للفحص المطابق {1}
luckperms.command.verbose.uploading=تسجيل الفيربوز {0}، تحميل النتائج...
luckperms.command.verbose.url=رابط نتائج العرض
luckperms.command.verbose.enabled-term=مفعل
luckperms.command.verbose.disabled-term=معطل
luckperms.command.verbose.query-any=أي
luckperms.command.info.running-plugin=مشغل
luckperms.command.info.platform-key=المنصة
luckperms.command.info.server-brand-key=علامة السيرفر
luckperms.command.info.server-version-key=إصدار السيرفر
luckperms.command.info.storage-key=المخزن
luckperms.command.info.storage-type-key=النوع
luckperms.command.info.storage.meta.split-types-key=الأنواع
luckperms.command.info.storage.meta.ping-key=بينغ
luckperms.command.info.storage.meta.connected-key=متصل
luckperms.command.info.storage.meta.file-size-key=حجم الملف
luckperms.command.info.extensions-key=الإضافات
luckperms.command.info.messaging-key=الدردشة
luckperms.command.info.instance-key=الحالة
luckperms.command.info.static-contexts-key=السياقات الثابتة
luckperms.command.info.online-players-key=اللاعبين المتواجدين
luckperms.command.info.online-players-unique={0} فريد
luckperms.command.info.uptime-key=مدة التشغيل
luckperms.command.info.local-data-key=البيانات المحلية
luckperms.command.info.local-data={0} مستخدمين، {1} رتب، {2} مسارات
luckperms.command.generic.create.success={0} قد أنشئ بنجاح
luckperms.command.generic.create.error=حدث خطأ أثناء إنشاء {0}
luckperms.command.generic.create.error-already-exists=''{0}'' موجود مُسبقا\!
luckperms.command.generic.delete.success={0} قد حذف بنجاح
luckperms.command.generic.delete.error=حدث خطأ أثناء حذف {0}
luckperms.command.generic.delete.error-doesnt-exist={0} غير موجود\!
luckperms.command.generic.rename.success=تمت إعادة تسمية {0} بنجاح إلى {1}
luckperms.command.generic.clone.success={0} تم نسخه بنجاح إلى {1}
luckperms.command.generic.info.parent.title=الرتب الأب
luckperms.command.generic.info.parent.temporary-title=الرتب الأب المؤقتة
luckperms.command.generic.info.expires-in=تنتهي الصلاحية في
luckperms.command.generic.info.inherited-from=موروث من
luckperms.command.generic.info.inherited-from-self=الذات
luckperms.command.generic.show-tracks.title=مسارات {0}
luckperms.command.generic.show-tracks.empty={0} ليس على أي مسارات
luckperms.command.generic.clear.node-removed={0} عقد قد مسحت
luckperms.command.generic.clear.node-removed-singular=تمت إزالة عقدة {0}
luckperms.command.generic.clear=تم مسح العقدة {0} في السياق {1}
luckperms.command.generic.permission.info.title=صلاحيات {0}
luckperms.command.generic.permission.info.empty=لايمتلك {0} أي صلاحيات
luckperms.command.generic.permission.info.click-to-remove=اضغط لإزالة هذه العقدة من {0}
luckperms.command.generic.permission.check.info.title=معلومات الصلاحيات {0}
luckperms.command.generic.permission.check.info.directly={0} قد تم تعيين {1} إلى {2} في السياق {3}
luckperms.command.generic.permission.check.info.inherited={0} الموروث {1} تم تعيينه إلى {2} من {3} في السياق {4}
luckperms.command.generic.permission.check.info.not-directly=لا يحتوي {0} على أي علامات
luckperms.command.generic.permission.check.info.not-inherited={0} لا يرث {1}
luckperms.command.generic.permission.check.result.title=التحقق من الصلاحية لـ {0}
luckperms.command.generic.permission.check.result.result-key=النتيجة
luckperms.command.generic.permission.check.result.processor-key=المعالج
luckperms.command.generic.permission.check.result.cause-key=السبب
luckperms.command.generic.permission.check.result.context-key=الإطار
luckperms.command.generic.permission.set=تعيين {0} إلى {1} ل {2} في السياق {3}
luckperms.command.generic.permission.already-has={0} لديه {1} بالفعل في السياق {2}
luckperms.command.generic.permission.set-temp=تم تعيين {0} إلى {1} ل {2} لمدة {3} في السياق {4}
luckperms.command.generic.permission.already-has-temp={0} لديه بالفعل {1} تعيين مؤقت في السياق {2}
luckperms.command.generic.permission.unset=إلغاء تعيين {0} لـ {1} في السياق {2}
luckperms.command.generic.permission.doesnt-have={0} ليس لديه {1} تعيين في السياق {2}
luckperms.command.generic.permission.unset-temp=إلغاء تعيين صلاحية مؤقتة {0} ل {1} في السياق {2}
luckperms.command.generic.permission.subtract=تعيين {0} إلى {1} ل {2} لمدة {3} في السياق {4}، {5} أقل من السابق
luckperms.command.generic.permission.doesnt-have-temp={0} ليس لديه {1} تعيين مؤقت في السياق {2}
luckperms.command.generic.permission.clear=تم مسح صلاحيات {0} في السياق {1}
luckperms.command.generic.parent.info.title=آباء {0}
luckperms.command.generic.parent.info.empty={0} ليس لديه أي والدين معرفين
luckperms.command.generic.parent.info.click-to-remove=اضغط لإزالة هذا الوالد من {0}
luckperms.command.generic.parent.add={0} يرث الآن الصلاحيات من {1} في السياق {2}
luckperms.command.generic.parent.add-temp={0} يرث الآن الصلاحيات من {1} لمدة {2} في السياق {3}
luckperms.command.generic.parent.set={0} تم مسح المجموعات الأصلية الموجودة لديهم، والآن يرث {1} فقط في السياق {2}
luckperms.command.generic.parent.set-track={0} تم مسح المجموعات الأصلية الموجودة على المسار {1} ، والآن فقط يرث {2} في السياق {3}
luckperms.command.generic.parent.remove={0} لم يعد يرث الصلاحيات من {1} في السياق {2}
luckperms.command.generic.parent.remove-temp={0} لم يعد يرث الصلاحيات مؤقتا من {1} في السياق {2}
luckperms.command.generic.parent.subtract={0} سوف يرث الصلاحيات من {1} لمدة {2} في السياق {3}، {4} أقل من السابق
luckperms.command.generic.parent.clear=تم مسح والدي {0} في السياق {1}
luckperms.command.generic.parent.clear-track=تم مسح والدي {0} على المسار {1} في السياق {2}
luckperms.command.generic.parent.already-inherits={0} يرث بالفعل من {1} في السياق {2}
luckperms.command.generic.parent.doesnt-inherit={0} لا يرث من {1} في السياق {2}
luckperms.command.generic.parent.already-temp-inherits={0} يرث مؤقتا بالفعل من {1} في السياق {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} لا يرث مؤقتا من {1} في السياق {2}
luckperms.command.generic.chat-meta.info.title-prefix=بادئات {0}
luckperms.command.generic.chat-meta.info.title-suffix=لواحق {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} ليس لديه بادئات
luckperms.command.generic.chat-meta.info.none-suffix={0} ليس لديه أي لاحقة
luckperms.command.generic.chat-meta.info.click-to-remove=اضغط لإزالة هذا {0} من {1}
luckperms.command.generic.chat-meta.already-has={0} لديه بالفعل {1} {2} تم تعيينه عند أولوية {3} في السياق {4}
luckperms.command.generic.chat-meta.already-has-temp={0} لديه بالفعل {1} {2} تم تعيينه مؤقتا عند أولوية {3} في السياق {4}
luckperms.command.generic.chat-meta.doesnt-have={0} ليس لديه {1} {2} تم تعيينه عند أولوية {3} في السياق {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} ليس لديه {1} {2} تم تعيينه مؤقتا عند أولوية {3} في السياق {4}
luckperms.command.generic.chat-meta.add={0} لديه {1} {2} تم تعيينه في أولوية {3} في السياق {4}
luckperms.command.generic.chat-meta.add-temp={0} لديه {1} {2} معين عند أولوية {3} لمدة {4} في السياق {5}
luckperms.command.generic.chat-meta.remove={0} لديه {1} {2} في الأولوية {3} محذوف في السياق {4}
luckperms.command.generic.chat-meta.remove-bulk={0} كان جميع {1} في الأولوية {2} تمت إزالتها في السياق {3}
luckperms.command.generic.chat-meta.remove-temp={0} كان لديه {1} {2} مؤقت في الأولوية {3} تمت إزالته في السياق {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} لديه جميع {1} المؤقت في الأولوية {2} تم إزالته في السياق {3}
luckperms.command.generic.meta.info.title=وصف {0}
luckperms.command.generic.meta.info.none={0} ليس لديه وصف
luckperms.command.generic.meta.info.click-to-remove=انقر لإزالة هذه العقدة الوصفية من {0}
luckperms.command.generic.meta.already-has={0} لديه بالفعل مفتاح وصفي {1} معين إلى {2} في السياق {3}
luckperms.command.generic.meta.already-has-temp={0} لديه بالفعل مفتاح وصفي {1} محدد مؤقتا إلى {2} في السياق {3}
luckperms.command.generic.meta.doesnt-have={0} ليس لديه مفتاح وصفي {1} تم تعيينه في السياق {2}
luckperms.command.generic.meta.doesnt-have-temp={0} ليس لديه مفتاح وصفي {1} محدد مؤقتاً في السياق {2}
luckperms.command.generic.meta.set=تم تعيين المفتاح الوصفي {0} إلى {1} ل {2} في السياق {3}
luckperms.command.generic.meta.set-temp=تعيين المفتاح الوصفي {0} إلى {1} لمدة {2} لمدة {3} في السياق {4}
luckperms.command.generic.meta.unset=إلغاء تعيين مفتاح الوصف {0} لـ {1} في السياق {2}
luckperms.command.generic.meta.unset-temp=إلغاء تعيين مفتاح الوصف {0} لـ {1} في السياق {2}
luckperms.command.generic.meta.clear={0} مطابقة نوع {1} تم محوها في السياق {2}
luckperms.command.generic.contextual-data.title=البيانات السياقية
luckperms.command.generic.contextual-data.mode.key=الوضع
luckperms.command.generic.contextual-data.mode.server=السيرفر
luckperms.command.generic.contextual-data.mode.active-player=لاعب نشط
luckperms.command.generic.contextual-data.contexts-key=السياقات
luckperms.command.generic.contextual-data.prefix-key=البادئة
luckperms.command.generic.contextual-data.suffix-key=اللاحقة
luckperms.command.generic.contextual-data.primary-group-key=الرتبة الرئيسية
luckperms.command.generic.contextual-data.meta-key=بيانات
luckperms.command.generic.contextual-data.null-result=لا شيء
luckperms.command.user.info.title=معلومات المستخدم
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=النوع
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=غير متصل
luckperms.command.user.info.status-key=الحالة
luckperms.command.user.info.status.online=متصل
luckperms.command.user.info.status.offline=غير متصل
luckperms.command.user.removegroup.error-primary=لا يمكنك إزالة مستخدم من رتبته الأساسية
luckperms.command.user.primarygroup.not-member={0} لم يكن بالفعل عضوا في {1}، إضافة لهم الآن
luckperms.command.user.primarygroup.already-has={0} لديه بالفعل {1} كرتبة أساسية
luckperms.command.user.primarygroup.warn-option=تحذير\: طريقة حساب الرتبة الأساسية التي يتم استخدامها من قبل هذا السيرفر ({0}) قد لا تعكس هذا التغيير
luckperms.command.user.primarygroup.set=تم تعيين رتبة {0} الرئيسية إلى {1}
luckperms.command.user.track.error-not-contain-group={0} ليس بالفعل في أي رتبة على {1}
luckperms.command.user.track.unsure-which-track=غير متأكد من أي مسار يمكن استخدامه، الرجاء تحديده كمدخل
luckperms.command.user.track.missing-group-advice=إما إنشاء المجموعة، أو إزالتها من المسار وحاول مرة أخرى
luckperms.command.user.promote.added-to-first={0} ليس في أي مرتبة في {1}، لذلك تم إضافتهم إلى المرتبة الأولى ، {2} في السياق {3}
luckperms.command.user.promote.not-on-track={0} ليس في أي مرتبة على {1}، لذلك لم يتم ترقيته
luckperms.command.user.promote.success=ترقية {0} على طول المسار {1} من {2} إلى {3} في السياق {4}
luckperms.command.user.promote.end-of-track=تم الوصول إلى نهاية المسار {0} ، غير قادر على ترقية {1}
luckperms.command.user.promote.next-group-deleted=المجموعة التالية على المسار {0}، لم تعد موجودة
luckperms.command.user.promote.unable-to-promote=غير قادر على ترقية المستخدم
luckperms.command.user.demote.success=تنزيل ترقية {0} على طول المسار {1} من {2} إلى {3} في السياق {4}
luckperms.command.user.demote.end-of-track=تم الوصول إلى نهاية المسار {0} ، لذلك تم إزالة {1} من {2}
luckperms.command.user.demote.end-of-track-not-removed=تم الوصول إلى نهاية المسار {0}. ولكن لم يتم إزالة {1} من المرتبة الأولى
luckperms.command.user.demote.previous-group-deleted=المرتبة السابقة على المسار، {0}، لم تعد موجودة
luckperms.command.user.demote.unable-to-demote=غير قادر على تخفيض المستخدم
luckperms.command.group.list.title=الرتب
luckperms.command.group.delete.not-default=لا يمكنك حذف المرتبة الافتراضية
luckperms.command.group.info.title=معلومات المرتبة
luckperms.command.group.info.display-name-key=الاسم الظاهر
luckperms.command.group.info.weight-key=الوزن
luckperms.command.group.setweight.set=تعيين الوزن إلى {0} للمرتبة {1}
luckperms.command.group.setdisplayname.doesnt-have={0} ليس لديه اسم عرض محدد
luckperms.command.group.setdisplayname.already-has={0} لديه بالفعل اسم عرض {1}
luckperms.command.group.setdisplayname.already-in-use=اسم العرض {0} قيد الاستخدام بالفعل من قبل {1}
luckperms.command.group.setdisplayname.set=تعيين اسم العرض إلى {0} للرتبة {1} في السياق {2}
luckperms.command.group.setdisplayname.removed=تم إزالة اسم العرض للرتبة {0} في السياق {1}
luckperms.command.track.list.title=المسارات
luckperms.command.track.path.empty=لا شيء
luckperms.command.track.info.showing-track=عرض المسار
luckperms.command.track.info.path-property=مسار
luckperms.command.track.clear=تم مسح مسار رتب {0}
luckperms.command.track.append.success=تم إلحاق المجموعة {0} للمسار {1}
luckperms.command.track.insert.success=تم إدراج الرتبة {0} في المسار {1} في الموضع {2}
luckperms.command.track.insert.error-number=لقد توقعنا رقما ولكن استلمنا بدلًا\: {0}
luckperms.command.track.insert.error-invalid-pos=غير قادر على الإدراج في الموضع {0}
luckperms.command.track.insert.error-invalid-pos-reason=موضع غير صالح
luckperms.command.track.remove.success=تم إزالة الرتبة {0} من المسار {1}
luckperms.command.track.error-empty={0} لا يمكن استخدامه كما أنه فارغ أو يحتوي على مجموعة واحدة فقط
luckperms.command.track.error-multiple-groups={0} هو عضو في مجموعات متعددة على هذا المسار
luckperms.command.track.error-ambiguous=تعذر تحديد موقعهم
luckperms.command.track.already-contains={0} يحتوي مسبقاً على {1}
luckperms.command.track.doesnt-contain={0} لا تحتوي على {1}
luckperms.command.log.load-error=لا يمكن تحميل السجل
luckperms.command.log.invalid-page=رقم الصفحة خاطئ
luckperms.command.log.invalid-page-range=الرجاء إدخال قيمة بين {0} و {1}
luckperms.command.log.empty=لا توجد إدخالات تسجيل لإظهارها
luckperms.command.log.notify.error-console=لا يمكن تفعيل الإشعارات لوحدة التحكم
luckperms.command.log.notify.enabled-term=مفعل
luckperms.command.log.notify.disabled-term=معطل
luckperms.command.log.notify.changed-state={0} إخراج التسجيل
luckperms.command.log.notify.already-on=أنت تتلقى بالفعل إشعارات
luckperms.command.log.notify.already-off=أنت لا تتلقى الإشعارات حاليًا
luckperms.command.log.notify.invalid-state=حالة غير معروفة. توقع {0} أو {1}
luckperms.command.log.show.search=عرض الإجراءات الأخيرة للاستعلام {0}
luckperms.command.log.show.recent=عرض الإجراءات الأخيرة
luckperms.command.log.show.by=عرض الإجراءات الأخيرة من قبل {0}
luckperms.command.log.show.history=عرض التاريخ لـ {0} {1}
luckperms.command.export.error-term=خطأ
luckperms.command.export.already-running=عملية تصدير أخرى قيد التشغيل بالفعل
luckperms.command.export.file.already-exists=الملف {0} موجود بالفعل
luckperms.command.export.file.not-writable=الملف {0} غير قابل للكتابة
luckperms.command.export.file.success=تم التصدير بنجاح إلى {0}
luckperms.command.export.file-unexpected-error-writing=حدث خطأ غير متوقع أثناء الكتابة إلى الملف
luckperms.command.export.web.export-code=رمز التصدير
luckperms.command.export.web.import-command-description=استخدم الأمر التالي للاستيراد
luckperms.command.import.term=استيراد
luckperms.command.import.error-term=خطأ
luckperms.command.import.already-running=عملية استيراد أخرى قيد التشغيل بالفعل
luckperms.command.import.file.doesnt-exist=الملف {0} غير موجود
luckperms.command.import.file.not-readable=الملف {0} غير قابل للقراءة
luckperms.command.import.file.unexpected-error-reading=حدث خطأ غير متوقع أثناء القراءة من ملف الاستيراد
luckperms.command.import.file.correct-format=هل هو التنسيق الصحيح؟
luckperms.command.import.web.unable-to-read=غير قادر على قراءة البيانات باستخدام الرمز المعطى
luckperms.command.import.progress.percent={0} مكتمل
luckperms.command.import.progress.operations={0}/{1} العمليات مكتملة
luckperms.command.import.starting=البدء في عملية الاستيراد
luckperms.command.import.completed=مكتمل
luckperms.command.import.duration=استغرق {0} ثانية
luckperms.command.bulkupdate.must-use-console=أمر التحديث بالجملة يمكن استخدامه فقط من الكونسول
luckperms.command.bulkupdate.invalid-data-type=نوع غير صالح، كان يتوقع {0}
luckperms.command.bulkupdate.invalid-constraint=قيد غير صالح {0}
luckperms.command.bulkupdate.invalid-constraint-format=يجب أن تكون القيود في تنسيق {0}
luckperms.command.bulkupdate.invalid-comparison=مشغل المقارنة غير صحيح {0}
luckperms.command.bulkupdate.invalid-comparison-format=توقّعت واحدة مما يلي\: {0}
luckperms.command.bulkupdate.queued=تم وضع عملية التحديث بالجملة في قائمة الانتظار
luckperms.command.bulkupdate.confirm=شغل {0} لتنفيذ التحديث
luckperms.command.bulkupdate.unknown-id=العملية بالمعرف {0} غير موجودة أو انتهت صلاحيتها
luckperms.command.bulkupdate.starting=تشغيل التحديث بالجملة
luckperms.command.bulkupdate.success=اكتمل التحديث الشامل بنجاح
luckperms.command.bulkupdate.success.statistics.nodes=إجمالي العقد المتأثرة
luckperms.command.bulkupdate.success.statistics.users=إجمالي المستخدمين المتأثرين
luckperms.command.bulkupdate.success.statistics.groups=إجمالي الرتب المتأثرة
luckperms.command.bulkupdate.failure=فشل التحديث بالجملة، تحقق من الكونسول للأخطاء
luckperms.command.update-task.request=تم طلب مهمة التحديث، الرجاء الانتظار
luckperms.command.update-task.complete=اكتملت عملية التحديث
luckperms.command.update-task.push.attempting=يتم الآن محاولة الضغط إلى خوادم أخرى
luckperms.command.update-task.push.complete=خوادم أخرى تم إعلامها عبر {0} بنجاح
luckperms.command.update-task.push.error=حدث خطأ أثناء دفع التغييرات إلى خوادم أخرى
luckperms.command.update-task.push.error-not-setup=لا يمكن دفع التغييرات إلى خوادم أخرى حيث لم يتم تكوين خدمة المراسلة
luckperms.command.reload-config.success=تم إعادة تحميل ملف الإعدادات
luckperms.command.reload-config.restart-note=بعض الخيارات سيتم تطبيقها فقط بعد إعادة تشغيل الخادم
luckperms.command.translations.searching=البحث عن الترجمات المتاحة، الرجاء الانتظار...
luckperms.command.translations.searching-error=غير قادر على الحصول على قائمة الترجمات المتوفرة
luckperms.command.translations.installed-translations=الترجمات المثبتة
luckperms.command.translations.available-translations=الترجمات المتوفرة
luckperms.command.translations.percent-translated=تمت ترجمة {0}%
luckperms.command.translations.translations-by=بواسطة
luckperms.command.translations.installing=جارٍ تثبيت الترجمات، الرجاء الانتظار...
luckperms.command.translations.download-error=تعذر تحميل الترجمة لـ {0}
luckperms.command.translations.installing-specific=جارٍ تثبيت اللغة {0}...
luckperms.command.translations.install-complete=اكتمل التثبيت
luckperms.command.translations.download-prompt=استخدم {0} لتنزيل وتثبيت الإصدارات الحديثة من هذه الترجمات المقدمة من المجتمع
luckperms.command.translations.download-override-warning=يرجى ملاحظة أن هذا سوف يلغي أي تغييرات قمت بها لهذه اللغات
luckperms.usage.user.description=مجموعة من الأوامر لإدارة المستخدمين داخل LuckPerms. (''user'' في LuckPerms هو فقط لاعب, ويمكنه الإشارة إلى UUID أو اسم المستخدم)
luckperms.usage.group.description=مجموعة من الأوامر لإدارة المجموعات داخل LuckPerms. المجموعات هي فقط مجموعات من تعيينات الأذونات التي يمكن إعطاؤها للمستخدمين. مجموعات جديدة يتم إنشاؤها باستخدام الأمر ''creategroup''.
luckperms.usage.track.description=مجموعة من الأوامر لإدارة المسارات داخل LuckPerms. المسارات هي مجموعة مرتبة من المجموعات التي يمكن استخدامها لتحديد الترقيات والتخفيضات.
luckperms.usage.log.description=مجموعة من الأوامر لإدارة وظيفة تسجيل الدخول داخل LuckPerms.
luckperms.usage.sync.description=إعادة تحميل جميع البيانات من تخزين الإضافات في الذاكرة، وتطبيق أي تغييرات يتم اكتشافها.
luckperms.usage.info.description=يطبع معلومات عامة عن مثيل الإضافات النشطة.
luckperms.usage.editor.description=إنشاء جلسة جديدة لمحرر الويب
luckperms.usage.editor.argument.type=الأنواع المراد تحميلها إلى المحرر. (''كل''، ''المستخدمين'' أو ''المجموعات'')
luckperms.usage.editor.argument.filter=صلاحية لتصفية إدخالات المستخدم بواسطة
luckperms.usage.verbose.description=يتحكم في نظام مراقبة صلاحيات الإضافات الصوتية.
luckperms.usage.verbose.argument.action=تمكين / تعطيل التسجيل، أو تحميل الإخراج المسجل
luckperms.usage.verbose.argument.filter=عامل التصفية لمطابقة المدخلات ضد
luckperms.usage.verbose.argument.commandas=اللاعب/الأمر لتشغيله
luckperms.usage.tree.description=ينشئ طريقة عرض شجرة (ترتيب هرمي القائمة) لجميع الصلاحيات المعروفة ل LuckPerms.
luckperms.usage.tree.argument.scope=جذر الشجرة. حدد "." لتضمين جميع الصلاحيات
luckperms.usage.tree.argument.player=اسم لاعب متصل للتحقق ضده
luckperms.usage.search.description=البحث عن جميع المستخدمين/المجموعات بإذن محدد
luckperms.usage.search.argument.permission=إذن البحث عن
luckperms.usage.search.argument.page=الصفحة المراد عرضها
luckperms.usage.network-sync.description=مزامنة التغييرات مع وحدة التخزين وطلب أن تفعل جميع الخوادم الأخرى على الشبكة نفس الشيء
luckperms.usage.import.description=استيراد البيانات من ملف تصدير (تم إنشاؤه سابقاً)
luckperms.usage.import.argument.file=الملف الذي سيتم استيراده من
luckperms.usage.import.argument.replace=استبدال البيانات الموجودة بدلا من الدمج
luckperms.usage.import.argument.upload=رفع البيانات من استخراج سابق
luckperms.usage.export.description=تصدير كافة بيانات الصلاحيات إلى ملف ''تصدير''. يمكن إعادة استيرادها في وقت لاحق.
luckperms.usage.export.argument.file=الملف المراد تصديره إلى
luckperms.usage.export.argument.without-users=إستبعاد المستخدمين من التصدير
luckperms.usage.export.argument.without-groups=أستبعاد المجموعات من الاستخراج السابق
luckperms.usage.export.argument.upload=تصدير كافة بيانات الصلاحيات إلى ملف ''تصدير''. يمكن إعادة استيرادها في وقت لاحق.
luckperms.usage.reload-config.description=إعادة تحميل بعض خيارات التهيئة
luckperms.usage.bulk-update.description=تنفيذ استفسارات التغيير بالجملة في جميع البيانات
luckperms.usage.bulk-update.argument.data-type=نوع البيانات التي يتم تغييرها. (''جميع''، ''المستخدمين'' أو ''المجموعات'')
luckperms.usage.bulk-update.argument.action=الإجراء المراد تنفيذه على البيانات. (''تحديث'' أو ''حذف'')
luckperms.usage.bulk-update.argument.action-field=الحقل المراد العمل معه. مطلوب فقط لـ ''تحديث'' (''أذونات'' أو ''خادم'' أو ''عالمي'')
luckperms.usage.bulk-update.argument.action-value=القيمة المراد استبدالها بها. مطلوبة فقط لـ "تحديث".
luckperms.usage.bulk-update.argument.constraint=القيود اللازمة للتحديث
luckperms.usage.translations.description=إدارة الترجمات
luckperms.usage.translations.argument.install=الأمر الفرعي لتثبيت الترجمات
luckperms.usage.apply-edits.description=تطبيق تغييرات الصلاحيات التي أجريت من محرر الويب
luckperms.usage.apply-edits.argument.code=الرمز الفريد من نوعه للبيانات
luckperms.usage.apply-edits.argument.target=من يقوم بتطبيق البيانات إلى
luckperms.usage.create-group.description=أنشاء مجموعة جديدة
luckperms.usage.create-group.argument.name=أسم المجموعة
luckperms.usage.create-group.argument.weight=قوة المجموعة
luckperms.usage.create-group.argument.display-name=الاسم الظاهر للمجموعة
luckperms.usage.delete-group.description=حذف المجموعة
luckperms.usage.delete-group.argument.name=أسم المجموعة
luckperms.usage.list-groups.description=قائمة جميع المجموعات في المنصة
luckperms.usage.create-track.description=أنشاء مسار جديد
luckperms.usage.create-track.argument.name=أسم المسار
luckperms.usage.delete-track.description=حذف المسار
luckperms.usage.delete-track.argument.name=أسم المسار
luckperms.usage.list-tracks.description=قائمة جميع المسارات على المنصة
luckperms.usage.user-info.description=إظهار معلومات عن اللاعب
luckperms.usage.user-switchprimarygroup.description=تبديل المجموعة الرئيسية للمستخدم
luckperms.usage.user-switchprimarygroup.argument.group=التبديل الى المجموعة
luckperms.usage.user-promote.description=ترقية مسار اللاعب
luckperms.usage.user-promote.argument.track=المسار المراد ترقيته للاعب
luckperms.usage.user-promote.argument.context=السياقات لترقية اللاعب في
luckperms.usage.user-promote.argument.dont-add-to-first=فقط الترقية للمستخدم إذا كان بالفعل على المسار
luckperms.usage.user-demote.description=خفض مسار اللاعب
luckperms.usage.user-demote.argument.track=المسار المراد خفضه للاعب
luckperms.usage.user-demote.argument.context=السياقات لخفض اللاعب في
luckperms.usage.user-demote.argument.dont-remove-from-first=منع إزالة اللاعب من المجموعة الأولى
luckperms.usage.user-clone.description=نسخ اللاعب
luckperms.usage.user-clone.argument.user=الأسم/معرف اللاعب لنسخه
luckperms.usage.group-info.description=إعطاء معلومات حول المجموعة
luckperms.usage.group-listmembers.description=أظهار اللاعبين/المجموعات الذين يرثون من هذه المجموعة
luckperms.usage.group-listmembers.argument.page=الصفحة المراد عرضها
luckperms.usage.group-setweight.description=تعيين قوة المجموعة
luckperms.usage.group-setweight.argument.weight=القوة المراد وضعها
luckperms.usage.group-set-display-name.description=ضع أسم عرض المجموعات
luckperms.usage.group-set-display-name.argument.name=الأسم تم تغيره الى
luckperms.usage.group-set-display-name.argument.context=السياقات التي سيتم تعيين الاسم فيها
luckperms.usage.group-rename.description=تغير اسم المجموعة
luckperms.usage.group-rename.argument.name=الاسم الجديد للمجموعة
luckperms.usage.group-clone.description=نسخ المجموعة
luckperms.usage.group-clone.argument.name=أسم المجموعة المراد نسخها
luckperms.usage.holder-editor.description=فتح موقع محرر الصلاحيات
luckperms.usage.holder-showtracks.description=قائمة المسارات التي يعمل عليها الكائن
luckperms.usage.holder-clear.description=أزالة جميع صلاحيات، الوارثين والميتا
luckperms.usage.holder-clear.argument.context=تصفية المسارات بواسطة
luckperms.usage.permission.description=تعديل الصلاحيات
luckperms.usage.parent.description=تعديل الميراث
luckperms.usage.meta.description=تحرير قيم البيانات الوصفية
luckperms.usage.permission-info.description=قائمة صلاحية العقد التي يملكها الكائن
luckperms.usage.permission-info.argument.page=الصفحة المراد عرضها
luckperms.usage.permission-info.argument.sort-mode=كيف فرز المدخلات
luckperms.usage.permission-set.description=وضع صلاحية للكائن
luckperms.usage.permission-set.argument.node=صلاحية العقدة لتعين
luckperms.usage.permission-set.argument.value=القيمة للعقدة
luckperms.usage.permission-set.argument.context=السياقات لأضافة الصلاحية الى
luckperms.usage.permission-unset.description=ازالة الصلاحية من الكائن
luckperms.usage.permission-unset.argument.node=صلاحية العقده لأزالتها
luckperms.usage.permission-unset.argument.context=السياقات لأزالة الصلاحية
luckperms.usage.permission-settemp.description=وضع صلاحية لكائن مؤقتًا
luckperms.usage.permission-settemp.argument.node=صلاحية العقدة لوضعها
luckperms.usage.permission-settemp.argument.value=قيمة العقدة
luckperms.usage.permission-settemp.argument.duration=المدة حتى تنتهي صلاحية العقده
luckperms.usage.permission-settemp.argument.temporary-modifier=كيف يتطبق الأذن المؤقت
luckperms.usage.permission-settemp.argument.context=السياقات لأضافة الصلاحية في
luckperms.usage.permission-unsettemp.description=ازالة الصلاحية المؤقتة من الكائن
luckperms.usage.permission-unsettemp.argument.node=الصلاحية لأزالتها
luckperms.usage.permission-unsettemp.argument.duration=المدة المراد ازالتها
luckperms.usage.permission-unsettemp.argument.context=السياقات لأزالة الصلاحية في
luckperms.usage.permission-check.description=تحقق اذا كان الكائن يحتوي على صلاحية العقدة
luckperms.usage.permission-check.argument.node=صلاحية العقدة المراد التحقق منها
luckperms.usage.permission-clear.description=مسح جميع الصلاحيات
luckperms.usage.permission-clear.argument.context=ترتيب السياقات بواسطة
luckperms.usage.parent-info.description=قائمة المجموعات التي يرثها من
luckperms.usage.parent-info.argument.page=الصفحة المراد عرضها
luckperms.usage.parent-info.argument.sort-mode=كيف فرز المدخلات
luckperms.usage.parent-set.description=أزالة جميع المجموعات الأخرى التي يرثها الكائن بالفعل ويضيفها إلى المجموعة المحددة
luckperms.usage.parent-set.argument.group=المجموعة المراد تعيينها
luckperms.usage.parent-set.argument.context=السياقات لتعيين المجموعة فيها
luckperms.usage.parent-add.description=يعين مجموعة أخرى للكائن ليرث الصلاحيات منها
luckperms.usage.parent-add.argument.group=المجموعة التي ترث منها
luckperms.usage.parent-add.argument.context=السياقات لتوريث المجموعة فيها
luckperms.usage.parent-remove.description=أزالة قاعدة التوريث المحددة مسبقًا
luckperms.usage.parent-remove.argument.group=الرتبة لأزالتها
luckperms.usage.parent-remove.argument.context=السياقات لإزالة المجموعة فيها
luckperms.usage.parent-set-track.description=أزالة كل المجموعات الأخرى التي يرثها الكائن بالفعل على المسار المحدد ويضيفها إلى المجموعة المحددة
luckperms.usage.parent-set-track.argument.track=المسار المراد ضبطه
luckperms.usage.parent-set-track.argument.group=المجموعة المراد تعيينها ، أو رقم يتعلق بموقف المجموعة على المسار المحدد
luckperms.usage.parent-set-track.argument.context=السياقات لتعيين المجموعة فيها
luckperms.usage.parent-add-temp.description=يعيّن مجموعة أخرى للكائن ليرث الصلاحيات منها مؤقتًا
luckperms.usage.parent-add-temp.argument.group=المجموعة التي ترث منها
luckperms.usage.parent-add-temp.argument.duration=مدة عضوية المجموعة
luckperms.usage.parent-add-temp.argument.temporary-modifier=كيف ينبغي تطبيق الإذن المؤقت
luckperms.usage.parent-add-temp.argument.context=السياقات لتوريث المجموعة فيها
luckperms.usage.parent-remove-temp.description=أزالة قاعدة توريث مؤقتة تم تعيينها مسبقًا
luckperms.usage.parent-remove-temp.argument.group=الرتبة لحذفها
luckperms.usage.parent-remove-temp.argument.duration=مدة الطرح
luckperms.usage.parent-remove-temp.argument.context=السياقات لإزالة المجموعة فيها
luckperms.usage.parent-clear.description=أزالة جميع الوراثات
luckperms.usage.parent-clear.argument.context=السياقات للتصفية حسب
luckperms.usage.parent-clear-track.description=مسح جميع الوراثات على مسار معين
luckperms.usage.parent-clear-track.argument.track=المسار المطلوب إزالته
luckperms.usage.parent-clear-track.argument.context=السياقات للتصفية حسب
luckperms.usage.meta-info.description=أظهار جميع بيانات الدردشة
luckperms.usage.meta-set.description=تعين قيمة التعريف
luckperms.usage.meta-set.argument.key=المفتاح لضبط
luckperms.usage.meta-set.argument.value=القيمة المراد تعيينها
luckperms.usage.meta-set.argument.context=السياقات لإضافة زوج التعريف فيها
luckperms.usage.meta-unset.description=إلغاء تحديد قيمة التعريف
luckperms.usage.meta-unset.argument.key=مفتاح فك
luckperms.usage.meta-unset.argument.context=السياقات لإزالة زوج التعريف في
luckperms.usage.meta-settemp.description=يحدد قيمة التعريف مؤقتًا
luckperms.usage.meta-settemp.argument.key=المفتاح لضبط
luckperms.usage.meta-settemp.argument.value=القيمة المراد تعيينها
luckperms.usage.meta-settemp.argument.duration=المدة حتى انتهاء صلاحية قيمة التعريف
luckperms.usage.meta-settemp.argument.context=السياقات لإضافة زوج التعريف فيها
luckperms.usage.meta-unsettemp.description=إلغاء تعيين قيمة التعريف المؤقتة
luckperms.usage.meta-unsettemp.argument.key=مفتاح فك
luckperms.usage.meta-unsettemp.argument.context=السياقات لإزالة زوج التعريف في
luckperms.usage.meta-addprefix.description=اضافة شكل
luckperms.usage.meta-addprefix.argument.priority=الأولوية لإضافة البادئة في
luckperms.usage.meta-addprefix.argument.prefix=سلسلة البادئة
luckperms.usage.meta-addprefix.argument.context=السياقات لإضافة البادئة فيها
luckperms.usage.meta-addsuffix.description=يضيف لاحقة
luckperms.usage.meta-addsuffix.argument.priority=الأولوية لإضافة اللاحقة في
luckperms.usage.meta-addsuffix.argument.suffix=سلسلة اللاحقة
luckperms.usage.meta-addsuffix.argument.context=السياقات لإضافة اللاحقة فيها
luckperms.usage.meta-setprefix.description=ضبط بادئة
luckperms.usage.meta-setprefix.argument.priority=الأولوية المراد ضبط البادئة فيها
luckperms.usage.meta-setprefix.argument.prefix=نص البادئة
luckperms.usage.meta-setprefix.argument.context=السياقات لضبط البادئة فيها
luckperms.usage.meta-setsuffix.description=ضبط لاحقة
luckperms.usage.meta-setsuffix.argument.priority=الأولوية المراد ضبط اللاحقة فيها
luckperms.usage.meta-setsuffix.argument.suffix=نص اللاحقة
luckperms.usage.meta-setsuffix.argument.context=السياقات لضبط اللاحقة فيها
luckperms.usage.meta-removeprefix.description=إزالة البادئة
luckperms.usage.meta-removeprefix.argument.priority=الأولوية المراد إزالة البادئة فيها
luckperms.usage.meta-removeprefix.argument.prefix=نص البادئة
luckperms.usage.meta-removeprefix.argument.context=السياقات المراد إزالة البادئة فيها
luckperms.usage.meta-removesuffix.description=إزالة لاحقة
luckperms.usage.meta-removesuffix.argument.priority=الأولوية المراد إزالة اللاحقة فيها
luckperms.usage.meta-removesuffix.argument.suffix=نص اللاحقة
luckperms.usage.meta-removesuffix.argument.context=السياقات المراد إزالة اللاحقة فيها
luckperms.usage.meta-addtemp-prefix.description=إضافة بادئة مؤقتة
luckperms.usage.meta-addtemp-prefix.argument.priority=الأولوية المراد إضافة البادئة فيها
luckperms.usage.meta-addtemp-prefix.argument.prefix=نص البادئة
luckperms.usage.meta-addtemp-prefix.argument.duration=المدة حتى تنتهي صلاحية البادئة
luckperms.usage.meta-addtemp-prefix.argument.context=السياقات المراد إضافة البادئة فيها
luckperms.usage.meta-addtemp-suffix.description=إضافة لاحقة مؤقتة
luckperms.usage.meta-addtemp-suffix.argument.priority=الأولوية المراد إضافة اللاحقة فيها
luckperms.usage.meta-addtemp-suffix.argument.suffix=نص اللاحقة
luckperms.usage.meta-addtemp-suffix.argument.duration=المدة حتى تنتهي صلاحية اللاحقة
luckperms.usage.meta-addtemp-suffix.argument.context=السياقات المراد إضافة اللاحقة فيها
luckperms.usage.meta-settemp-prefix.description=إضافة بادئة مؤقتة
luckperms.usage.meta-settemp-prefix.argument.priority=الأولوية المراد ضبط البادئة فيها
luckperms.usage.meta-settemp-prefix.argument.prefix=نص البادئة
luckperms.usage.meta-settemp-prefix.argument.duration=المدة حتى تنتهي صلاحية البادئة
luckperms.usage.meta-settemp-prefix.argument.context=السياقات المراد ضبط البادئة فيها
luckperms.usage.meta-settemp-suffix.description=إضافة لاحقة مؤقتة
luckperms.usage.meta-settemp-suffix.argument.priority=الأولوية المراد ضبط اللاحقة فيها
luckperms.usage.meta-settemp-suffix.argument.suffix=نص اللاحقة
luckperms.usage.meta-settemp-suffix.argument.duration=المدة حتى تنتهي صلاحية اللاحقة
luckperms.usage.meta-settemp-suffix.argument.context=السياقات المراد ضبط اللاحقة فيها
luckperms.usage.meta-removetemp-prefix.description=إزالة بادئة مؤقتة
luckperms.usage.meta-removetemp-prefix.argument.priority=الأولوية المراد إزالة البادئة فيها
luckperms.usage.meta-removetemp-prefix.argument.prefix=نص البادئة
luckperms.usage.meta-removetemp-prefix.argument.context=السياقات المراد إزالة البادئة فيها
luckperms.usage.meta-removetemp-suffix.description=إزالة بادئة مؤقتة
luckperms.usage.meta-removetemp-suffix.argument.priority=الأولوية المراد إزالة اللاحقة فيها
luckperms.usage.meta-removetemp-suffix.argument.suffix=نص اللاحقة
luckperms.usage.meta-removetemp-suffix.argument.context=السياقات المراد إزالة اللاحقة فيها
luckperms.usage.meta-clear.description=مسح كافة البيانات
luckperms.usage.meta-clear.argument.type=نوع التعريف المراد إزالته
luckperms.usage.meta-clear.argument.context=السياقات المراد التصفية بها
luckperms.usage.track-info.description=إعطاء معلومات حول المسار
luckperms.usage.track-editor.description=فتح موقع محرر الصلاحيات
luckperms.usage.track-append.description=إلحاق مجموعة إلى نهاية المسار
luckperms.usage.track-append.argument.group=المجموعة المراد إلحاقها
luckperms.usage.track-insert.description=إدراج مجموعة في موضع معين على طول المسار
luckperms.usage.track-insert.argument.group=المجموعة المراد إلحاقها
luckperms.usage.track-insert.argument.position=الموقع المراد إدراج المجموعة فيه (الموقع الأول على المسار هو 1)
luckperms.usage.track-remove.description=إزالة مجموعة من المسار
luckperms.usage.track-remove.argument.group=المجموعة المراد إزالتها
luckperms.usage.track-clear.description=مسح المجموعات في المسار
luckperms.usage.track-rename.description=إعادة تسمية المسار
luckperms.usage.track-rename.argument.name=الاسم الجديد
luckperms.usage.track-clone.description=نسخ المسار
luckperms.usage.track-clone.argument.name=اسم المسار المراد استنساخه إليه
luckperms.usage.log-recent.description=عرض آخر الإجراءات
luckperms.usage.log-recent.argument.user=اسم المستخدم / معرف المستخدم المراد للتصفية
luckperms.usage.log-recent.argument.page=رقم الصفحة المراد عرضها
luckperms.usage.log-search.description=البحث في السجل عن مدخلة
luckperms.usage.log-search.argument.query=الاستعلام المراد للبحث
luckperms.usage.log-search.argument.page=رقم الصفحة المراد عرضها
luckperms.usage.log-notify.description=إطفاء\\تشغيل إشعارات السجل
luckperms.usage.log-notify.argument.toggle=ما إذا كان سيتم تشغيل أو إيقاف التشغيل
luckperms.usage.log-user-history.description=عرض سجل المستخدم
luckperms.usage.log-user-history.argument.user=اسم المستخدم / معرف المستخدم
luckperms.usage.log-user-history.argument.page=رقم الصفحة المراد عرضها
luckperms.usage.log-group-history.description=عرض سجل مجموعة
luckperms.usage.log-group-history.argument.group=إسم المجموعة
luckperms.usage.log-group-history.argument.page=رقم الصفحة المراد عرضها
luckperms.usage.log-track-history.description=عرض سجل المسار
luckperms.usage.log-track-history.argument.track=إسم المسار
luckperms.usage.log-track-history.argument.page=رقم الصفحة المراد عرضها
luckperms.usage.sponge.description=تعديل بيانات سبونج الإضافية
luckperms.usage.sponge.argument.collection=المجموعة المراد الاستعلام عنها
luckperms.usage.sponge.argument.subject=الموضوع المراد تعديله
luckperms.usage.sponge-permission-info.description=إظهار معلومات حول صلاحيات الموضوع
luckperms.usage.sponge-permission-info.argument.contexts=السياقات المراد التصفية بها
luckperms.usage.sponge-permission-set.description=تعيين صلاحيّة لهذا الموضوع
luckperms.usage.sponge-permission-set.argument.node=العقدة الصلاحيّة المراد تعيينها
luckperms.usage.sponge-permission-set.argument.tristate=القيمة المراد تعيين الإذن لها
luckperms.usage.sponge-permission-set.argument.contexts=السياقات المراد تعيين الإذن فيها
luckperms.usage.sponge-permission-clear.description=مسح صلاحيات المواضيع
luckperms.usage.sponge-permission-clear.argument.contexts=السياقات المراد مسح الصلاحيات فيها
luckperms.usage.sponge-parent-info.description=إظهار معلومات حول آباء الموضوع
luckperms.usage.sponge-parent-info.argument.contexts=السياقات المراد التصفية بها
luckperms.usage.sponge-parent-add.description=إضافة أب إلى الموضوع
luckperms.usage.sponge-parent-add.argument.collection=مجموعة المواضيع التي يكون فيها الموضوع الرئيسي
luckperms.usage.sponge-parent-add.argument.subject=اسم الموضوع الرئيسي
luckperms.usage.sponge-parent-add.argument.contexts=السياقات المراد إضافة الأب فيها
luckperms.usage.sponge-parent-remove.description=إزالة والد من الموضوع
luckperms.usage.sponge-parent-remove.argument.collection=مجموعة المواضيع التي يكون فيها الموضوع الرئيسي
luckperms.usage.sponge-parent-remove.argument.subject=اسم الموضوع الرئيسي
luckperms.usage.sponge-parent-remove.argument.contexts=السياقات المراد إزالة الأب فيها
luckperms.usage.sponge-parent-clear.description=مسح الوالدين الموضوعين
luckperms.usage.sponge-parent-clear.argument.contexts=السياقات المراد إزالة اللآباء فيها
luckperms.usage.sponge-option-info.description=إظهار معلومات حول خيارات الموضوع
luckperms.usage.sponge-option-info.argument.contexts=السياقات المراد التصفية بها
luckperms.usage.sponge-option-set.description=تعيين خيار للموضوع
luckperms.usage.sponge-option-set.argument.key=المفتاح المراد تعيينه
luckperms.usage.sponge-option-set.argument.value=القيمة المراد تعيين المفتاح إليها
luckperms.usage.sponge-option-set.argument.contexts=السياقات المراد ضبط الخيار فيها
luckperms.usage.sponge-option-unset.description=إلغاء تعيين خيار للموضوع
luckperms.usage.sponge-option-unset.argument.key=المفتاح المراد إلغاء تعيينه
luckperms.usage.sponge-option-unset.argument.contexts=السياقات التي سيتم إلغاء تعيين المفتاح فيها
luckperms.usage.sponge-option-clear.description=مسح خيارات المواضيع
luckperms.usage.sponge-option-clear.argument.contexts=السياقات المراد مسح الخيارات فيها
