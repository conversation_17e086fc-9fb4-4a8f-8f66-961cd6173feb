luckperms.logs.actionlog-prefix=REGISTRO
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTAR
luckperms.commandsystem.available-commands=Use {0} para ver os comandos disponíveis
luckperms.commandsystem.command-not-recognised=Comando não reconhecido
luckperms.commandsystem.no-permission=Você não tem permissão para usar este comando\!
luckperms.commandsystem.no-permission-subcommands=Você não tem permissão para usar quaisquer subcomandos
luckperms.commandsystem.already-executing-command=Outro comando está sendo executado, aguarde terminar...
luckperms.commandsystem.usage.sub-commands-header=Sub Comandos
luckperms.commandsystem.usage.usage-header=Utilização do Comando
luckperms.commandsystem.usage.arguments-header=Argumentos
luckperms.first-time.no-permissions-setup=Parece que nenhuma permissão foi configurada\!
luckperms.first-time.use-console-to-give-access=Antes de você poder usar qualquer um dos comandos do LuckPerms no jogo, você precisa usar o console para dar a si mesmo acesso
luckperms.first-time.console-command-prompt=Abra seu console e execute
luckperms.first-time.next-step=Depois de ter feito isso, você pode começar a definir suas atribuições de grupos e permissões
luckperms.first-time.wiki-prompt=Não sabe por onde começar? Confira aqui\: {0}
luckperms.login.try-again=Por favor, tente novamente mais tarde
luckperms.login.loading-database-error=Ocorreu um erro durante o carregamento dos dados de permissões no banco de dados
luckperms.login.server-admin-check-console-errors=Se você é um administrador do servidor, por favor verifique no console se há algum erro
luckperms.login.server-admin-check-console-info=Por favor, verifique o console do servidor para mais informações
luckperms.login.data-not-loaded-at-pre=Dados de permissões do usuário não foram carregados durante a fase de pré-login
luckperms.login.unable-to-continue=Incapaz de continuar
luckperms.login.craftbukkit-offline-mode-error=provavelmente isto aconteceu devido a um conflito entre o CraftBukkit e a configuração do online-mode
luckperms.login.unexpected-error=Ocorreu um erro inesperado ao configurar seus dados de permissões
luckperms.opsystem.disabled=O sistema OP está desativado neste servidor
luckperms.opsystem.sponge-warning=Note que o status de Operador de Servidor não tem efeito nas verificações de permissão do Sponge quando um plugin de permissão está instalado, você deve editar os dados do usuário diretamente
luckperms.duration.unit.years.plural={0} anos
luckperms.duration.unit.years.singular={0} ano
luckperms.duration.unit.years.short={0}ano
luckperms.duration.unit.months.plural={0} meses
luckperms.duration.unit.months.singular={0} mês
luckperms.duration.unit.months.short={0}mês
luckperms.duration.unit.weeks.plural={0} semanas
luckperms.duration.unit.weeks.singular={0} semana
luckperms.duration.unit.weeks.short={0}sem
luckperms.duration.unit.days.plural={0} dias
luckperms.duration.unit.days.singular={0} dia
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} horas
luckperms.duration.unit.hours.singular={0} hora
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minutos
luckperms.duration.unit.minutes.singular={0} minuto
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} segundos
luckperms.duration.unit.seconds.singular={0} segundo
luckperms.duration.unit.seconds.short={0}seg
luckperms.duration.since={0} atrás
luckperms.command.misc.invalid-code=Código inválido
luckperms.command.misc.response-code-key=código de resposta
luckperms.command.misc.error-message-key=mensagem
luckperms.command.misc.bytebin-unable-to-communicate=Não foi possível comunicar-se com bytebin
luckperms.command.misc.webapp-unable-to-communicate=Não foi possível comunicar-se com o aplicativo web
luckperms.command.misc.check-console-for-errors=Verifique no console se há erros
luckperms.command.misc.file-must-be-in-data=Arquivo {0} deve ser um filho direto do diretório de dados
luckperms.command.misc.wait-to-finish=Por favor, aguarde até que a sua solicitação seja concluída e tente novamente
luckperms.command.misc.invalid-priority=Prioridade inválida {0}
luckperms.command.misc.expected-number=Esperado um número
luckperms.command.misc.date-parse-error=Não foi possível analisar data {0}
luckperms.command.misc.date-in-past-error=Você não pode definir uma data no passado\!
luckperms.command.misc.page=página {0} de {1}
luckperms.command.misc.page-entries={0} entradas
luckperms.command.misc.none=Nenhum
luckperms.command.misc.loading.error.unexpected=Um erro inesperado ocorreu
luckperms.command.misc.loading.error.user=Usuário não carregado
luckperms.command.misc.loading.error.user-specific=Não foi possível carregar usuário alvo {0}
luckperms.command.misc.loading.error.user-not-found=Não foi possível encontrar um usuário para {0}
luckperms.command.misc.loading.error.user-save-error=Houve um erro ao salvar os dados de usuário para {0}
luckperms.command.misc.loading.error.user-not-online=Usuário {0} não está online
luckperms.command.misc.loading.error.user-invalid=''{0}'' não é um nome/uuid de usuário válido
luckperms.command.misc.loading.error.user-not-uuid=O usuário do destino {0} não é um uuid válido
luckperms.command.misc.loading.error.group=Grupo não carregado
luckperms.command.misc.loading.error.all-groups=Não foi possível carregar todos os grupos
luckperms.command.misc.loading.error.group-not-found=Não foi possível encontrar o grupo chamado {0}
luckperms.command.misc.loading.error.group-save-error=Ocorreu um erro ao salvar os dados do grupo para {0}
luckperms.command.misc.loading.error.group-invalid={0} não é um nome de grupo válido
luckperms.command.misc.loading.error.track=Track não carregada
luckperms.command.misc.loading.error.all-tracks=Impossível carregar todos as tracks
luckperms.command.misc.loading.error.track-not-found=O caminho de promoção {0} não foi encontrada
luckperms.command.misc.loading.error.track-save-error=Ocorreu um erro ao salvar os dados do caminho de promoção para {0}
luckperms.command.misc.loading.error.track-invalid={0} não é um nome de track válido
luckperms.command.editor.no-match=Incapaz de abrir o editor, nenhum objeto corresponde ao tipo desejado
luckperms.command.editor.start=Preparando uma nova sessão do web editor, por favor aguarde...
luckperms.command.editor.url=Clique no link abaixo para abrir o editor
luckperms.command.editor.unable-to-communicate=Impossível se comunicar com o editor
luckperms.command.editor.apply-edits.success=Dados do web editor aplicados a {0} {1} com sucesso
luckperms.command.editor.apply-edits.success-summary={0} {1} e {2} {3}
luckperms.command.editor.apply-edits.success.additions=adições
luckperms.command.editor.apply-edits.success.additions-singular=adição
luckperms.command.editor.apply-edits.success.deletions=eliminações
luckperms.command.editor.apply-edits.success.deletions-singular=eliminação
luckperms.command.editor.apply-edits.no-changes=Nenhuma alteração foi aplicada pelo web editor, os dados enviados não contêm nenhuma edição
luckperms.command.editor.apply-edits.unknown-type=Impossível aplicar a edição ao tipo de objeto especificado
luckperms.command.editor.apply-edits.unable-to-read=Impossível ler os dados usando o código fornecido
luckperms.command.search.searching.permission=Procurando usuários e grupos com {0}
luckperms.command.search.searching.inherit=Procurando por usuários e grupos que herdam de {0}
luckperms.command.search.result=Encontrados {0} entradas de {1} usuários e {2} grupos
luckperms.command.search.result.default-notice=Nota\: ao procurar membros do grupo padrão, jogadores offline sem outras permissões não serão exibidos\!
luckperms.command.search.showing-users=Exibindo usuário entradas
luckperms.command.search.showing-groups=Exibindo entradas de grupo
luckperms.command.tree.start=Gerando lista de permissões, por favor aguarde...
luckperms.command.tree.empty=Impossível gerar a lista, nenhum resultado foi encontrado
luckperms.command.tree.url=URL da lista de permissões
luckperms.command.verbose.invalid-filter={0} não é um filtro verboso válido
luckperms.command.verbose.enabled=Registro detalhado {0} para verificações correspondentes a {1}
luckperms.command.verbose.command-exec=Forçando {0} a executar o comando {1} e reportando todas as verificações feitas...
luckperms.command.verbose.off=Registro verboso {0}
luckperms.command.verbose.command-exec-complete=Execução do comando concluída
luckperms.command.verbose.command.no-checks=A execução do comando foi concluída, mas nenhuma verificação de permissão foi feita
luckperms.command.verbose.command.possibly-async=Isso pode ocorrer porque o plug-in executa comandos em segundo plano (async)
luckperms.command.verbose.command.try-again-manually=Você ainda pode usar verbose manualmente para detectar verificações feitas como esta
luckperms.command.verbose.enabled-recording=Gravação detalhada de {0} para verificações correspondentes a {1}
luckperms.command.verbose.uploading=Registro verboso {0}, envio de resultados...
luckperms.command.verbose.url=URL de resultados verboso
luckperms.command.verbose.enabled-term=ativado
luckperms.command.verbose.disabled-term=desativado
luckperms.command.verbose.query-any=QUALQUER
luckperms.command.info.running-plugin=Executando
luckperms.command.info.platform-key=Plataforma
luckperms.command.info.server-brand-key=Marca do Servidor
luckperms.command.info.server-version-key=Versão do Servidor
luckperms.command.info.storage-key=Armazenamento
luckperms.command.info.storage-type-key=Tipo
luckperms.command.info.storage.meta.split-types-key=Tipos
luckperms.command.info.storage.meta.ping-key=Latência
luckperms.command.info.storage.meta.connected-key=Conectado
luckperms.command.info.storage.meta.file-size-key=Tamanho do Arquivo
luckperms.command.info.extensions-key=Extensões
luckperms.command.info.messaging-key=Sistema de Mensagens
luckperms.command.info.instance-key=Instância
luckperms.command.info.static-contexts-key=Contextos estáticos
luckperms.command.info.online-players-key=Jogadores Online
luckperms.command.info.online-players-unique={0} único
luckperms.command.info.uptime-key=Tempo de atividade
luckperms.command.info.local-data-key=Dados Locais
luckperms.command.info.local-data={0} usuários, {1} grupos, {2} tracks
luckperms.command.generic.create.success={0} foi criado com sucesso
luckperms.command.generic.create.error=Houve um erro ao criar {0}
luckperms.command.generic.create.error-already-exists={0} já existe\!
luckperms.command.generic.delete.success={0} foi excluído com sucesso
luckperms.command.generic.delete.error=Houve um erro ao excluir {0}
luckperms.command.generic.delete.error-doesnt-exist={0} não existe\!
luckperms.command.generic.rename.success={0} foi renomeado com sucesso para {1}
luckperms.command.generic.clone.success={0} foi clonado com sucesso em {1}
luckperms.command.generic.info.parent.title=Grupos de Parents
luckperms.command.generic.info.parent.temporary-title=Grupos Temporários de Pais
luckperms.command.generic.info.expires-in=expira em
luckperms.command.generic.info.inherited-from=herdado de
luckperms.command.generic.info.inherited-from-self=si mesmo
luckperms.command.generic.show-tracks.title=Tracks de {0}
luckperms.command.generic.show-tracks.empty={0} não está em nenhum track
luckperms.command.generic.clear.node-removed={0} nódulos foram removidos
luckperms.command.generic.clear.node-removed-singular={0} nódulo foi removido
luckperms.command.generic.clear=Nódulos de {0} limpos no contexto {1}
luckperms.command.generic.permission.info.title=Permissões de {0}
luckperms.command.generic.permission.info.empty={0} não possui quaisquer permissões definidas
luckperms.command.generic.permission.info.click-to-remove=Clique para remover este nódulo de {0}
luckperms.command.generic.permission.check.info.title=Informações da permissão para {0}
luckperms.command.generic.permission.check.info.directly={0} foi {1} definido como {2} no contexto {3}
luckperms.command.generic.permission.check.info.inherited={0} herda {1} definido como {2} de {3} no contexto {4}
luckperms.command.generic.permission.check.info.not-directly={0} não tem {1} definido
luckperms.command.generic.permission.check.info.not-inherited={0} não herda {1}
luckperms.command.generic.permission.check.result.title=Verificação de permissão para {0}
luckperms.command.generic.permission.check.result.result-key=Resultado
luckperms.command.generic.permission.check.result.processor-key=Processador
luckperms.command.generic.permission.check.result.cause-key=Causa
luckperms.command.generic.permission.check.result.context-key=Contexto
luckperms.command.generic.permission.set=Definir {0} a {1} para {2} no contexto {3}
luckperms.command.generic.permission.already-has={0} já tem {1} definidos no contexto {2}
luckperms.command.generic.permission.set-temp=Definir {0} a {1} para {2} por uma duração de {3} no contexto {4}
luckperms.command.generic.permission.already-has-temp={0} já possui {1} definido temporariamente no contexto {2}
luckperms.command.generic.permission.unset=Removido {0} para {1} no contexto {2}
luckperms.command.generic.permission.doesnt-have={0} não tem {1} definido no contexto {2}
luckperms.command.generic.permission.unset-temp=Desconfigurar a permissão temporária {0} para {1} no contexto {2}
luckperms.command.generic.permission.subtract=Definir {0} como {1} para {2} por uma duração de {3} no contexto {4}, {5} a menos do que antes
luckperms.command.generic.permission.doesnt-have-temp={0} não possui {1} definido temporariamente no contexto {2}
luckperms.command.generic.permission.clear=As permissões de {0} foram apagadas no contexto {1}
luckperms.command.generic.parent.info.title=Grupos de {0}
luckperms.command.generic.parent.info.empty={0} não tem quaisquer pais definidos
luckperms.command.generic.parent.info.click-to-remove=Clique para remover esse grupo de {0}
luckperms.command.generic.parent.add={0} agora herda as permissões de {1} no contexto {2}
luckperms.command.generic.parent.add-temp={0} agora herda as permissões de {1} por uma duração de {2} no contexto {3}
luckperms.command.generic.parent.set={0} teve seus grupos-pai existentes apagados, e agora herda apenas {1} em contexto {2}
luckperms.command.generic.parent.set-track={0} teve seus grupos-pai existentes na escada {1}, e agora só herda {2} no contexto {3}
luckperms.command.generic.parent.remove={0} não herda mais as permissões de {1} no contexto {2}
luckperms.command.generic.parent.remove-temp={0} não herda mais temporariamente as permissões de {1} no contexto {2}
luckperms.command.generic.parent.subtract={0} herdará permissões de {1} por uma duração de {2} no contexto {3}, {4} menos que antes
luckperms.command.generic.parent.clear=Os pais de {0} foram apagadas no contexto {1}
luckperms.command.generic.parent.clear-track=Os pais de {0} na escada {1} foram apagadas no contexto {2}
luckperms.command.generic.parent.already-inherits={0} já herda de {1} no contexto {2}
luckperms.command.generic.parent.doesnt-inherit={0} não herda de {1} no contexto {2}
luckperms.command.generic.parent.already-temp-inherits={0} já herda temporariamente de {1} no contexto {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} não herda temporariamente de {1} no contexto {2}
luckperms.command.generic.chat-meta.info.title-prefix=Prefixos de {0}
luckperms.command.generic.chat-meta.info.title-suffix=Sufixos de {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} não tem prefixos
luckperms.command.generic.chat-meta.info.none-suffix={0} não tem sufixos
luckperms.command.generic.chat-meta.info.click-to-remove=Clique para remover este {0} de {1}
luckperms.command.generic.chat-meta.already-has={0} já tem {1} {2} definido a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.already-has-temp={0} já tem {1} {2} definido temporariamente a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.doesnt-have={0} não tem {1} {2} definido a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} não tem {1} {2} definido temporariamente a uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.add={0} teve {1} {2} definido como uma prioridade de {3} no contexto {4}
luckperms.command.generic.chat-meta.add-temp={0} teve {1} {2} definido a uma prioridade de {3} por uma duração de {4} no contexto {5}
luckperms.command.generic.chat-meta.remove={0} teve {1} {2} na prioridade {3} removido no contexto {4}
luckperms.command.generic.chat-meta.remove-bulk={0} teve todos {1} na prioridade {2} removidos no contexto {3}
luckperms.command.generic.chat-meta.remove-temp={0} teve temporário {1} {2} na prioridade {3} removido no contexto {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} teve todos temporários os {1} na prioridade {2} removido no contexto {3}
luckperms.command.generic.meta.info.title=Metadados de {0}
luckperms.command.generic.meta.info.none={0} não tem meta
luckperms.command.generic.meta.info.click-to-remove=Clique para remover este meta node de {0}
luckperms.command.generic.meta.already-has={0} já tem meta-chave {1} definida como {2} no contexto {3}
luckperms.command.generic.meta.already-has-temp={0} já tem meta-chave {1} temporariamente definida como {2} no contexto {3}
luckperms.command.generic.meta.doesnt-have={0} não tem meta-chave {1} definida no contexto {2}
luckperms.command.generic.meta.doesnt-have-temp={0} não tem meta-chave {1} definida temporariamente no contexto {2}
luckperms.command.generic.meta.set=Defina a meta-chave {0} como {1} para {2} no contexto {3}
luckperms.command.generic.meta.set-temp=Defina a meta-chave {0} como {1} para {2} por uma duração de {3} no contexto {4}
luckperms.command.generic.meta.unset=Remova a meta-chave {0} para {1} no contexto {2}
luckperms.command.generic.meta.unset-temp=Remova a meta-chave temporária {0} para {1} no contexto {2}
luckperms.command.generic.meta.clear=O metatipo de correspondência {1} de {0} foi apagado no contexto {2}
luckperms.command.generic.contextual-data.title=Dados Contextuais
luckperms.command.generic.contextual-data.mode.key=modo
luckperms.command.generic.contextual-data.mode.server=servidor
luckperms.command.generic.contextual-data.mode.active-player=jogador ativo
luckperms.command.generic.contextual-data.contexts-key=Contextos
luckperms.command.generic.contextual-data.prefix-key=Prefixo
luckperms.command.generic.contextual-data.suffix-key=Sufixo
luckperms.command.generic.contextual-data.primary-group-key=Grupo Primário
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Nenhum
luckperms.command.user.info.title=Informação do Usuário
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=tipo
luckperms.command.user.info.uuid-type.mojang=Mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Estado
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Você não pode remover um usuário do grupo primário
luckperms.command.user.primarygroup.not-member={0} não era um membro de {1}, adicionando-o agora
luckperms.command.user.primarygroup.already-has={0} já tem {1} definido como seu grupo principal
luckperms.command.user.primarygroup.warn-option=Aviso\: O método de cálculo do grupo primário sendo usado por este servidor ({0}) pode não refletir esta alteração
luckperms.command.user.primarygroup.set=O grupo primário {0} foi definido como {1}
luckperms.command.user.track.error-not-contain-group={0} ainda não está em nenhum grupo em {1}
luckperms.command.user.track.unsure-which-track=Não sabe qual caminho usar, por favor especificar um argumento
luckperms.command.user.track.missing-group-advice=Crie o grupo ou o remova da faixa e tente novamente
luckperms.command.user.promote.added-to-first={0} não está em nenhum grupo em {1}, então foram adicionados ao primeiro grupo, {2} no contexto {3}
luckperms.command.user.promote.not-on-track={0} não está em nenhum grupo em {1}, então não foi promovido
luckperms.command.user.promote.success=Promovendo {0} ao longo da faixa {1} de {2} para {3} no contexto {4}
luckperms.command.user.promote.end-of-track=O fim da faixa {0} foi alcançado, incapaz de promover {1}
luckperms.command.user.promote.next-group-deleted=O próximo grupo na faixa, {0}, não existe mais
luckperms.command.user.promote.unable-to-promote=Não foi possível promover o usuário
luckperms.command.user.demote.success=Promovendo {0} ao longo da faixa {1} de {2} para {3} no contexto {4}
luckperms.command.user.demote.end-of-track=O fim da faixa {0} foi alcançado, então {1} foi removido de {2}
luckperms.command.user.demote.end-of-track-not-removed=O fim da faixa {0} foi alcançado, mas {1} não foi removido do primeiro grupo
luckperms.command.user.demote.previous-group-deleted=O grupo anterior na faixa, {0}, não existe mais
luckperms.command.user.demote.unable-to-demote=Não foi possível despromover o usuário
luckperms.command.group.list.title=Grupos
luckperms.command.group.delete.not-default=Você não pode excluir o grupo padrão
luckperms.command.group.info.title=Informações do Grupo
luckperms.command.group.info.display-name-key=Nome de Exibição
luckperms.command.group.info.weight-key=Peso
luckperms.command.group.setweight.set=Definir o peso {0} para o grupo {1}
luckperms.command.group.setdisplayname.doesnt-have={0} não possui um nome de exibição definido
luckperms.command.group.setdisplayname.already-has={0} já tem um nome de exibição de {1}
luckperms.command.group.setdisplayname.already-in-use=O nome de exibição {0} já está sendo usado por {1}
luckperms.command.group.setdisplayname.set=Definir nome de exibição para {0} para o grupo {1} no contexto {2}
luckperms.command.group.setdisplayname.removed=Remover nome de exibição para o grupo {0} no contexto {1}
luckperms.command.track.list.title=Lista de promoção
luckperms.command.track.path.empty=Nenhum
luckperms.command.track.info.showing-track=Mostrar lista de promoção
luckperms.command.track.info.path-property=Caminho
luckperms.command.track.clear={0} grupos de faixas foram limpos
luckperms.command.track.append.success=Grupo {0} foi anexado à faixa {1}
luckperms.command.track.insert.success=O grupo {0} foi inserido na faixa {1} na posição {2}
luckperms.command.track.insert.error-number=Número esperado, mas em vez disso recebido\: {0}
luckperms.command.track.insert.error-invalid-pos=Não é possível inserir na posição {0}
luckperms.command.track.insert.error-invalid-pos-reason=posição Inválida
luckperms.command.track.remove.success=O grupo {0} foi removido da faixa {1}
luckperms.command.track.error-empty={0} não pode ser usado, pois está vazio ou contém apenas um grupo
luckperms.command.track.error-multiple-groups={0} é um membro de múltiplos grupos deste caminho
luckperms.command.track.error-ambiguous=Não foi possível determinar sua localização
luckperms.command.track.already-contains={0} já contém {1}
luckperms.command.track.doesnt-contain={0} não deve conter {1}
luckperms.command.log.load-error=Não foi possível carregar o log
luckperms.command.log.invalid-page=Número de página inválido
luckperms.command.log.invalid-page-range=Por favor, insira um valor entre {0} e {1}
luckperms.command.log.empty=Não há nenhum registro na log
luckperms.command.log.notify.error-console=Não é possível alterar as notificações para o console
luckperms.command.log.notify.enabled-term=Ativado
luckperms.command.log.notify.disabled-term=Desativado
luckperms.command.log.notify.changed-state={0} saídas no registro
luckperms.command.log.notify.already-on=Você já está recebendo notificações
luckperms.command.log.notify.already-off=Você não está recebendo notificações no momento
luckperms.command.log.notify.invalid-state=Estado desconhecido. Esperando {0} ou {1}
luckperms.command.log.show.search=Mostrando ações recentes para consulta {0}
luckperms.command.log.show.recent=Mostrando ações recentes
luckperms.command.log.show.by=Mostrando ações recentes de {0}
luckperms.command.log.show.history=Mostrando histórico de {0} {1}
luckperms.command.export.error-term=Erro
luckperms.command.export.already-running=Outro processo de exportação já está sendo executado
luckperms.command.export.file.already-exists=Arquivo {0} já existe
luckperms.command.export.file.not-writable=O arquivo {0} não pode ser lido
luckperms.command.export.file.success=Exportado com sucesso para {0}
luckperms.command.export.file-unexpected-error-writing=Ocorreu um erro inesperado ao escrever no arquivo
luckperms.command.export.web.export-code=Exportar Código
luckperms.command.export.web.import-command-description=Use o seguinte comando para importar
luckperms.command.import.term=Importar
luckperms.command.import.error-term=Erro
luckperms.command.import.already-running=Outro processo de importação já está em execução
luckperms.command.import.file.doesnt-exist=O Arquivo {0} não existe
luckperms.command.import.file.not-readable=O arquivo {0} não pode ser lido
luckperms.command.import.file.unexpected-error-reading=Ocorreu um erro inesperado ao ler o arquivo de importação
luckperms.command.import.file.correct-format=este é o formato correto?
luckperms.command.import.web.unable-to-read=Não foi possível ler os dados usando o código fornecido
luckperms.command.import.progress.percent={0}% concluído
luckperms.command.import.progress.operations={0}/{1} operações concluídas
luckperms.command.import.starting=Iniciando processo de importação
luckperms.command.import.completed=CONCLUÍDO
luckperms.command.import.duration=demorou {0} segundos
luckperms.command.bulkupdate.must-use-console=O comando de atualização em massa só pode ser usado pelo console
luckperms.command.bulkupdate.invalid-data-type=Tipo inválido, estava esperando {0}
luckperms.command.bulkupdate.invalid-constraint=Restrição inválida {0}
luckperms.command.bulkupdate.invalid-constraint-format=As restrições devem estar no formato {0}
luckperms.command.bulkupdate.invalid-comparison=Operador de comparação inválido {0}
luckperms.command.bulkupdate.invalid-comparison-format=Era esperado um dos seguintes\: {0}
luckperms.command.bulkupdate.queued=A operação de atualização em massa foi colocada na fila
luckperms.command.bulkupdate.confirm=Execute {0} para executar a atualização
luckperms.command.bulkupdate.unknown-id=A operação com id {0} não existe ou expirou
luckperms.command.bulkupdate.starting=A atualização em massa está agora em execução
luckperms.command.bulkupdate.success=A atualização em massa foi concluída com sucesso
luckperms.command.bulkupdate.success.statistics.nodes=Total de nós afetados
luckperms.command.bulkupdate.success.statistics.users=Total de usuários afetados
luckperms.command.bulkupdate.success.statistics.groups=Total de grupos afetados
luckperms.command.bulkupdate.failure=Falha na atualização em massa, verifique os erros no console
luckperms.command.update-task.request=Uma tarefa de atualização foi solicitada, por favor aguarde
luckperms.command.update-task.complete=Atualização completa
luckperms.command.update-task.push.attempting=Agora tentando enviar para outros servidores
luckperms.command.update-task.push.complete=Outros servidores foram notificados via {0} com êxito
luckperms.command.update-task.push.error=Erro ao enviar as mudanças para outros servidores
luckperms.command.update-task.push.error-not-setup=Não é possível enviar as alterações para outros servidores porque o serviço de mensagens não foi configurado
luckperms.command.reload-config.success=O arquivo de configuração foi recarregado
luckperms.command.reload-config.restart-note=algumas opções só serão aplicadas depois que o servidor tiver sido reiniciado
luckperms.command.translations.searching=Procurando por traduções disponíveis, por favor aguarde...
luckperms.command.translations.searching-error=Não foi possível obter a lista de traduções disponíveis
luckperms.command.translations.installed-translations=Traduções instaladas
luckperms.command.translations.available-translations=Traduções disponíveis
luckperms.command.translations.percent-translated={0}% traduzido
luckperms.command.translations.translations-by=por
luckperms.command.translations.installing=Instalando traduções, por favor aguarde...
luckperms.command.translations.download-error=Não foi possível baixar a tradução para {0}
luckperms.command.translations.installing-specific=Instalando idioma {0}...
luckperms.command.translations.install-complete=Instalação completa
luckperms.command.translations.download-prompt=Use {0} para baixar e instalar as versões mais atualizadas dessas traduções fornecidas pela comunidade
luckperms.command.translations.download-override-warning=Isto irá substituir todas as alterações feitas por você nestes idiomas
luckperms.usage.user.description=Comandos para gerenciar os usuários dentro do LuckPerms. (Um ''usuário'' no LuckPerms é apenas um jogador, e pode-se referir ao seu UUID ou nick)
luckperms.usage.group.description=Comandos para o gerenciamento de grupos dentro do LuckPerms. Grupos são apenas coleções de permissão que podem ser dadas para múltiplos usuários. Para criar novos grupos utilize o comando ''creategroup''.
luckperms.usage.track.description=Comandos para gerenciar faixas de caminhos dentro de LuckPerms. Uma faixa de caminho é uma coleção ordenada de grupos que podem ser usados para definir promoções e rebaixamentos.
luckperms.usage.log.description=Um conjunto de comandos para gerenciar as configurações da log no LuckPerms.
luckperms.usage.sync.description=Recarrega todos os dados dentro do armazenamento do plugin na memória e aplica quaisquer alterações detectadas.
luckperms.usage.info.description=Mostra informações gerais sobre a instância ativa do plugin.
luckperms.usage.editor.description=Cria uma nova sessão de editor web
luckperms.usage.editor.argument.type=os tipos para carregar no editor. (''todos'', ''usuários'' ou ''grupos'')
luckperms.usage.editor.argument.filter=permissão para filtrar entradas do usuário por
luckperms.usage.verbose.description=Controla o sistema de verificação de permissão detalhada do plugin.
luckperms.usage.verbose.argument.action=se habilitar ou desabilitar o log ou fazer upload da saída registrada
luckperms.usage.verbose.argument.filter=o filtro para combinar as entradas com
luckperms.usage.verbose.argument.commandas=o jogador/comando para executar
luckperms.usage.tree.description=Gera uma lista de exibição (hierarquia ordenada) de todas as permissões conhecidas pelo LuckPerms.
luckperms.usage.tree.argument.scope=a raiz da árvore. Especifique "." para incluir todas as permissões
luckperms.usage.tree.argument.player=o nome do usuario para checar contra
luckperms.usage.search.description=Procura por todos os usuários/grupos com uma permissão específica
luckperms.usage.search.argument.permission=a permissão para pesquisar
luckperms.usage.search.argument.page=a página para ver
luckperms.usage.network-sync.description=Sincronize as alterações com o armazenamento e solicite que todos os outros servidores da rede façam o mesmo
luckperms.usage.import.description=Importa dados de um arquivo de exportação (previamente criado)
luckperms.usage.import.argument.file=selecione o arquivo à importar
luckperms.usage.import.argument.replace=substituir os dados existentes em vez de se fundir
luckperms.usage.import.argument.upload=carregar os dados de uma exportação anterior
luckperms.usage.export.description=Exporta todos os dados de permissões para um arquivo de "exportação". Poderá ser importado novamente mais tarde.
luckperms.usage.export.argument.file=exportar o arquivo para
luckperms.usage.export.argument.without-users=excluir os usuários da exportação
luckperms.usage.export.argument.without-groups=excluir grupos da exportação
luckperms.usage.export.argument.upload=Faça o upload de todos os dados de permissão para o editor web. Poderá ser importado novamente mais tarde.
luckperms.usage.reload-config.description=Recarregue algumas das opções da config
luckperms.usage.bulk-update.description=Executa alterações em massa em todos os dados
luckperms.usage.bulk-update.argument.data-type=o tipo de dados sendo alterados. (''todos'', ''usuários'' ou ''grupos'')
luckperms.usage.bulk-update.argument.action=a ação a realizar-se sobre os dados. (''atualização'' ou ''apagar'')
luckperms.usage.bulk-update.argument.action-field=o campo para agir. Somente necessário para ''atualizar''. (''permissão'', ''servidor'' ou ''mundo'')
luckperms.usage.bulk-update.argument.action-value=o valor para substituir. Apenas necessário para ''atualizar''.
luckperms.usage.bulk-update.argument.constraint=as restrições necessárias para a atualização
luckperms.usage.translations.description=Gerenciar traduções
luckperms.usage.translations.argument.install=subcomando para instalar traduções
luckperms.usage.apply-edits.description=Aplica alterações de permissão feitas a partir do editor web
luckperms.usage.apply-edits.argument.code=o código único para os dados
luckperms.usage.apply-edits.argument.target=a quem aplicar os dados para
luckperms.usage.create-group.description=Criar um novo grupo
luckperms.usage.create-group.argument.name=o nome do grupo
luckperms.usage.create-group.argument.weight=a posição do grupo
luckperms.usage.create-group.argument.display-name=o nome de exibição do grupo
luckperms.usage.delete-group.description=Excluir um grupo
luckperms.usage.delete-group.argument.name=o nome do grupo
luckperms.usage.list-groups.description=Listar todos os grupos na plataforma
luckperms.usage.create-track.description=Criar uma nova track
luckperms.usage.create-track.argument.name=o nome da track
luckperms.usage.delete-track.description=Deleta uma track
luckperms.usage.delete-track.argument.name=o nome da track
luckperms.usage.list-tracks.description=Listar todas as tracks na plataforma
luckperms.usage.user-info.description=Mostra informações sobre o jogador
luckperms.usage.user-switchprimarygroup.description=Muda o grupo primário do jogador
luckperms.usage.user-switchprimarygroup.argument.group=mudar o grupo para
luckperms.usage.user-promote.description=Promove o jogador a uma track
luckperms.usage.user-promote.argument.track=a track para promover um usuário
luckperms.usage.user-promote.argument.context=os contextos para promover o usuário em
luckperms.usage.user-promote.argument.dont-add-to-first=só promova o jogador se ele já estiver na track
luckperms.usage.user-demote.description=Rebaixa o jogador a uma track
luckperms.usage.user-demote.argument.track=a track para rebaixar um usuário
luckperms.usage.user-demote.argument.context=os contextos para promover o usuário em
luckperms.usage.user-demote.argument.dont-remove-from-first=evitar que o jogador seja removido do grupo primário
luckperms.usage.user-clone.description=Clonar usuário
luckperms.usage.user-clone.argument.user=o nome/UUID do jogador para clonar sobre
luckperms.usage.group-info.description=Dá informações sobre o grupo
luckperms.usage.group-listmembers.description=Mostrar os usuários/grupos que herdam deste grupo
luckperms.usage.group-listmembers.argument.page=a página para ver
luckperms.usage.group-setweight.description=Definir o peso dos grupos
luckperms.usage.group-setweight.argument.weight=a posição a setar
luckperms.usage.group-set-display-name.description=Definir o nome de exibição dos grupos
luckperms.usage.group-set-display-name.argument.name=definir o nome para
luckperms.usage.group-set-display-name.argument.context=os contextos para definir o nome em
luckperms.usage.group-rename.description=Renomear o grupo
luckperms.usage.group-rename.argument.name=o novo nome
luckperms.usage.group-clone.description=Clonar o grupo
luckperms.usage.group-clone.argument.name=o nome do grupo para clonar sobre
luckperms.usage.holder-editor.description=Abrir o editor de permissão web
luckperms.usage.holder-showtracks.description=Lista as tracks em que o objeto está
luckperms.usage.holder-clear.description=Remova todas as permissões, grupos e meta
luckperms.usage.holder-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.permission.description=Editar permissões
luckperms.usage.parent.description=Editar heranças
luckperms.usage.meta.description=Editar valores de metadata
luckperms.usage.permission-info.description=Lista os nodes de permissão que o objeto tem
luckperms.usage.permission-info.argument.page=a página para ver
luckperms.usage.permission-info.argument.sort-mode=como classificar as entradas
luckperms.usage.permission-set.description=Setar uma permissão para o jogador
luckperms.usage.permission-set.argument.node=o node da permissão a definir
luckperms.usage.permission-set.argument.value=o valor do node
luckperms.usage.permission-set.argument.context=os contextos para definir a opção em
luckperms.usage.permission-unset.description=Remove uma permissão do jogador
luckperms.usage.permission-unset.argument.node=o node da permissão a definir
luckperms.usage.permission-unset.argument.context=os contextos para remover a permissão
luckperms.usage.permission-settemp.description=Define uma permissão para o objeto temporariamente
luckperms.usage.permission-settemp.argument.node=o node da permissão a definir
luckperms.usage.permission-settemp.argument.value=o valor do node
luckperms.usage.permission-settemp.argument.duration=a duração da permissão até que ela se expire
luckperms.usage.permission-settemp.argument.temporary-modifier=o tempo que a permissão ficará aplicada
luckperms.usage.permission-settemp.argument.context=os contextos para adicionar a permissão em
luckperms.usage.permission-unsettemp.description=Remove uma permissão temporária para o objeto
luckperms.usage.permission-unsettemp.argument.node=o node da permissão a remover
luckperms.usage.permission-unsettemp.argument.duration=a duração para subtrair
luckperms.usage.permission-unsettemp.argument.context=os contextos para remover a permissão
luckperms.usage.permission-check.description=Verifica se um jogador possui uma certa permissão
luckperms.usage.permission-check.argument.node=a permissão para ser verificada
luckperms.usage.permission-clear.description=Limpar todas as permissões
luckperms.usage.permission-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.parent-info.description=Lista os grupos que o objeto herda
luckperms.usage.parent-info.argument.page=a página para ver
luckperms.usage.parent-info.argument.sort-mode=como classificar as entradas
luckperms.usage.parent-set.description=Remove todos os outros grupos que o jogador herda e adiciona o escolhido
luckperms.usage.parent-set.argument.group=definir o grupo para
luckperms.usage.parent-set.argument.context=os contextos para definir o grupo em
luckperms.usage.parent-add.description=Define outro grupo para o jogador herdar permissões
luckperms.usage.parent-add.argument.group=herdar o grupo de
luckperms.usage.parent-add.argument.context=os contextos para herdar o grupo em
luckperms.usage.parent-remove.description=Remove um grupo previamente definido
luckperms.usage.parent-remove.argument.group=o grupo a ser removido
luckperms.usage.parent-remove.argument.context=os contextos para remover o grupo em
luckperms.usage.parent-set-track.description=Remove todos os outros grupos que o objeto herda já na track dada e adiciona-os ao dado
luckperms.usage.parent-set-track.argument.track=a track para definir
luckperms.usage.parent-set-track.argument.group=o grupo a definir ou um número relativo à posição do grupo na track
luckperms.usage.parent-set-track.argument.context=os contextos para definir o grupo em
luckperms.usage.parent-add-temp.description=Define outro grupo para o objeto herdar permissões temporariamente
luckperms.usage.parent-add-temp.argument.group=o grupo irá herdar de
luckperms.usage.parent-add-temp.argument.duration=a duração da associação ao grupo
luckperms.usage.parent-add-temp.argument.temporary-modifier=como a permissão temporária deve ser aplicada
luckperms.usage.parent-add-temp.argument.context=os contextos para definir o grupo em
luckperms.usage.parent-remove-temp.description=Remove uma regra de herança temporária definida anteriormente
luckperms.usage.parent-remove-temp.argument.group=o grupo a ser removido
luckperms.usage.parent-remove-temp.argument.duration=o tempo para subtrair
luckperms.usage.parent-remove-temp.argument.context=os contextos para remover o grupo em
luckperms.usage.parent-clear.description=Limpa todos os "parents"
luckperms.usage.parent-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.parent-clear-track.description=Limpa todos os grupos em uma determinada track
luckperms.usage.parent-clear-track.argument.track=remover a track em
luckperms.usage.parent-clear-track.argument.context=os contextos a serem filtrados por
luckperms.usage.meta-info.description=Mostrar todos os prefixos/sufixos e meta no chat
luckperms.usage.meta-set.description=Define um valor meta
luckperms.usage.meta-set.argument.key=a chave a ser definida
luckperms.usage.meta-set.argument.value=definir o valor para
luckperms.usage.meta-set.argument.context=os contextos para adicionar o par meta em
luckperms.usage.meta-unset.description=Remove um valor meta
luckperms.usage.meta-unset.argument.key=a chave a ser removida
luckperms.usage.meta-unset.argument.context=os contextos para remover o meta
luckperms.usage.meta-settemp.description=Setar um valor meta temporariamente
luckperms.usage.meta-settemp.argument.key=a chave a ser definida
luckperms.usage.meta-settemp.argument.value=definir o valor para
luckperms.usage.meta-settemp.argument.duration=a duração até o valor meta expirar
luckperms.usage.meta-settemp.argument.context=os contextos para adicionar o par meta em
luckperms.usage.meta-unsettemp.description=Remove um valor meta temporário
luckperms.usage.meta-unsettemp.argument.key=a chave a ser removida
luckperms.usage.meta-unsettemp.argument.context=os contextos para adicionar o par meta em
luckperms.usage.meta-addprefix.description=Adicionar um prefixo
luckperms.usage.meta-addprefix.argument.priority=a prioridade do prefixo
luckperms.usage.meta-addprefix.argument.prefix=a string prefixo
luckperms.usage.meta-addprefix.argument.context=os contextos para adicionar o prefixo em
luckperms.usage.meta-addsuffix.description=Adiciona um sufixo
luckperms.usage.meta-addsuffix.argument.priority=a prioridade para adicionar o sufixo
luckperms.usage.meta-addsuffix.argument.suffix=a string sufixo
luckperms.usage.meta-addsuffix.argument.context=os contextos para adicionar o sufixo em
luckperms.usage.meta-setprefix.description=Definir um prefixo
luckperms.usage.meta-setprefix.argument.priority=a prioridade para definir o prefixo em
luckperms.usage.meta-setprefix.argument.prefix=a string prefixo
luckperms.usage.meta-setprefix.argument.context=os contextos para definir o prefixo em
luckperms.usage.meta-setsuffix.description=Definir um sufixo
luckperms.usage.meta-setsuffix.argument.priority=a prioridade para definir o sufixo no
luckperms.usage.meta-setsuffix.argument.suffix=a string sufixo
luckperms.usage.meta-setsuffix.argument.context=os contextos para definir o sufixo
luckperms.usage.meta-removeprefix.description=Remover um prefixo
luckperms.usage.meta-removeprefix.argument.priority=a prioridade do prefixo a ser removido
luckperms.usage.meta-removeprefix.argument.prefix=a string prefixo
luckperms.usage.meta-removeprefix.argument.context=os contextos para remover o prefixo
luckperms.usage.meta-removesuffix.description=Remover um sufixo
luckperms.usage.meta-removesuffix.argument.priority=a prioridade do sufixo a ser removido
luckperms.usage.meta-removesuffix.argument.suffix=a string sufixo
luckperms.usage.meta-removesuffix.argument.context=os contextos para remover o sufixo
luckperms.usage.meta-addtemp-prefix.description=Adicionar um prefixo temporário
luckperms.usage.meta-addtemp-prefix.argument.priority=a prioridade do prefixo a ser definido
luckperms.usage.meta-addtemp-prefix.argument.prefix=a string sufixo
luckperms.usage.meta-addtemp-prefix.argument.duration=a duração que o prefixo irá expirar
luckperms.usage.meta-addtemp-prefix.argument.context=os contextos para adicionar o prefixo
luckperms.usage.meta-addtemp-suffix.description=Adicionar um suffix temporário
luckperms.usage.meta-addtemp-suffix.argument.priority=a prioridade do sufixo a ser adicionado
luckperms.usage.meta-addtemp-suffix.argument.suffix=a string sufixo
luckperms.usage.meta-addtemp-suffix.argument.duration=a duração que o sufixo irá expirar
luckperms.usage.meta-addtemp-suffix.argument.context=os contextos para adicionar a suffix em
luckperms.usage.meta-settemp-prefix.description=Setar um prefixo temporário
luckperms.usage.meta-settemp-prefix.argument.priority=a prioridade do prefixo a ser adicionado
luckperms.usage.meta-settemp-prefix.argument.prefix=a string prefixo
luckperms.usage.meta-settemp-prefix.argument.duration=a duração que o prefixo irá expirar
luckperms.usage.meta-settemp-prefix.argument.context=os contextos para definir o prefixo
luckperms.usage.meta-settemp-suffix.description=Setar um suffix temporário
luckperms.usage.meta-settemp-suffix.argument.priority=a prioridade para definir o sufixo
luckperms.usage.meta-settemp-suffix.argument.suffix=a string sufixo
luckperms.usage.meta-settemp-suffix.argument.duration=a duração que o sufixo irá expirar
luckperms.usage.meta-settemp-suffix.argument.context=os contextos para definir o sufixo em
luckperms.usage.meta-removetemp-prefix.description=Remover um prefixo temporário
luckperms.usage.meta-removetemp-prefix.argument.priority=a prioridade do prefixo a ser removido
luckperms.usage.meta-removetemp-prefix.argument.prefix=a string prefixo
luckperms.usage.meta-removetemp-prefix.argument.context=os contextos para remover o prefixo em
luckperms.usage.meta-removetemp-suffix.description=Remover um sufixo temporário
luckperms.usage.meta-removetemp-suffix.argument.priority=a prioridade do sufixo a ser removido
luckperms.usage.meta-removetemp-suffix.argument.suffix=a string sufixo
luckperms.usage.meta-removetemp-suffix.argument.context=os contextos para remover o sufixo em
luckperms.usage.meta-clear.description=Limpa todos os metadados
luckperms.usage.meta-clear.argument.type=o tipo de meta que será removido
luckperms.usage.meta-clear.argument.context=os contextos a serem filtrados por
luckperms.usage.track-info.description=Exibe informações sobre uma track
luckperms.usage.track-editor.description=Abre o editor de permissão da web
luckperms.usage.track-append.description=Acrescenta um grupo ao final da track
luckperms.usage.track-append.argument.group=o grupo a ser adicionado
luckperms.usage.track-insert.description=Insere um grupo em uma determinada posição na track
luckperms.usage.track-insert.argument.group=o grupo a ser inserido
luckperms.usage.track-insert.argument.position=a posição de inserir o grupo em (a primeira posição na track é 1)
luckperms.usage.track-remove.description=Remover um grupo de uma track
luckperms.usage.track-remove.argument.group=o grupo a ser removido
luckperms.usage.track-clear.description=Limpa os grupos na track
luckperms.usage.track-rename.description=Renomear a faixa
luckperms.usage.track-rename.argument.name=o novo nome
luckperms.usage.track-clone.description=Clonar a track
luckperms.usage.track-clone.argument.name=o nome da track para clonar
luckperms.usage.log-recent.description=Ver ações recentes
luckperms.usage.log-recent.argument.user=o nome/uuid do usuário para filtrar por
luckperms.usage.log-recent.argument.page=o número da página para visualizar
luckperms.usage.log-search.description=Procurar no registro por uma entrada
luckperms.usage.log-search.argument.query=a consulta para pesquisa por
luckperms.usage.log-search.argument.page=o número da página para visualizar
luckperms.usage.log-notify.description=Ativar/desativar notificações de registro
luckperms.usage.log-notify.argument.toggle=se deve ligar ou desligar
luckperms.usage.log-user-history.description=Visualizar histórico de um usuário
luckperms.usage.log-user-history.argument.user=o nome/UUID do jogador
luckperms.usage.log-user-history.argument.page=o número da página para visualizar
luckperms.usage.log-group-history.description=Ver o histórico de um grupo
luckperms.usage.log-group-history.argument.group=o nome do grupo
luckperms.usage.log-group-history.argument.page=o número da página para visualizar
luckperms.usage.log-track-history.description=Ver o histórico de uma track
luckperms.usage.log-track-history.argument.track=o nome da track
luckperms.usage.log-track-history.argument.page=o número da página para visualizar
luckperms.usage.sponge.description=Editar dados extras do Sponge
luckperms.usage.sponge.argument.collection=a coleção a realizar consultar
luckperms.usage.sponge.argument.subject=o assunto a ser modificado
luckperms.usage.sponge-permission-info.description=Exibe informações sobre as permissões do assunto
luckperms.usage.sponge-permission-info.argument.contexts=os contextos a serem filtrados por
luckperms.usage.sponge-permission-set.description=Define uma permissão para o Assunto
luckperms.usage.sponge-permission-set.argument.node=o node da permissão a definir
luckperms.usage.sponge-permission-set.argument.tristate=o valor para definir a permissão para
luckperms.usage.sponge-permission-set.argument.contexts=os contextos para definir a permissão em
luckperms.usage.sponge-permission-clear.description=Limpa as permissões dos assuntos
luckperms.usage.sponge-permission-clear.argument.contexts=os contextos para limpar as permissões em
luckperms.usage.sponge-parent-info.description=Mostra informações sobre os parentes do sujeito
luckperms.usage.sponge-parent-info.argument.contexts=os contextos a serem filtrados por
luckperms.usage.sponge-parent-add.description=Adiciona um parente ao sujeito
luckperms.usage.sponge-parent-add.argument.collection=a coleção de assunto onde o assunto pai é
luckperms.usage.sponge-parent-add.argument.subject=o nome do parente do assunto
luckperms.usage.sponge-parent-add.argument.contexts=os contextos para adicionar o grupo
luckperms.usage.sponge-parent-remove.description=Remove um parente do assunto
luckperms.usage.sponge-parent-remove.argument.collection=a coleção de assunto onde o assunto pai é
luckperms.usage.sponge-parent-remove.argument.subject=o nome do parente do assunto
luckperms.usage.sponge-parent-remove.argument.contexts=os contextos para remover o parente em
luckperms.usage.sponge-parent-clear.description=Limpa os parentes dos assuntos
luckperms.usage.sponge-parent-clear.argument.contexts=os contextos para limpar o parente em
luckperms.usage.sponge-option-info.description=Exibe informações sobre as opções do assunto
luckperms.usage.sponge-option-info.argument.contexts=os contextos a serem filtradados por
luckperms.usage.sponge-option-set.description=Define uma opção para o assunto
luckperms.usage.sponge-option-set.argument.key=a chave a ser definida
luckperms.usage.sponge-option-set.argument.value=o valor para definir a chave para
luckperms.usage.sponge-option-set.argument.contexts=os contextos para definir a opção em
luckperms.usage.sponge-option-unset.description=Desdefine uma opção para o assunto
luckperms.usage.sponge-option-unset.argument.key=a chave a ser removida
luckperms.usage.sponge-option-unset.argument.contexts=os contextos para desativar a chave em
luckperms.usage.sponge-option-clear.description=Limpa as opções de assuntos
luckperms.usage.sponge-option-clear.argument.contexts=os contextos para limpar opções em
