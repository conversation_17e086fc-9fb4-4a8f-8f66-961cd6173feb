# 插件信息前缀
prefix: '&e枫影轻语&7 >>  '

# 货币名称
currency-singular: 枫叶片
currency-plural: 枫叶片片
currency-separator: ','
currency-decimal: .
number-abbreviation-thousands: 千
number-abbreviation-millions: 百万
number-abbreviation-billions: 亿万

# 杂项
no-permission: '&c您没有权限那么做!'
only-player: '&c该命令只能由玩家执行。'
invalid-amount: '&c数值必须为正数。'
unknown-player: '&c找不到玩家: &b%player%'
unknown-account: '&c无法找到帐户: &b%account%'
unknown-command: '&c未知命令: &b%input%'
invalid-argument: '无效参数: %message%。'
command-usage: '&c使用方法: &b/%cmd% %args%'
argument-handler-string: 文本不能为空
argument-handler-enum: '%enum% 类型 [%input%] 不存在'
argument-handler-enum-list: '%enum% 类型 [%input%] 不存在。有效类型：%types%'
argument-handler-value: 值 [%input%] 无效
votifier-voted: '&e感谢您在 %service% 上为我们投票! &b%amount% &e已经被添加到您的账户。'
leaderboard-empty-entry: 这可能是你!
command-cooldown: '&c请等待一段时间再使用该命令。'

# 基础命令信息
base-command-color: '&e'
base-command-help: '&e使用 &b/%cmd% help &e来获取命令信息。'

# 帮助命令
command-help-description: 显示帮助菜单... 就是您现在看着的这个
command-help-title: '&e可用命令:'
command-help-list-description: '&8 - &d/%cmd% %subcmd% %args% &7- %desc%'
command-help-list-description-no-args: '&8 - &d/%cmd% %subcmd% &7- %desc%'

# 给予命令
command-give-description: 给一名玩家点劵
command-give-success: '&b%player% &a被给予了 &b%amount% &a%currency%。'
command-give-received: '&e您被给予了 &b%amount% &e%currency%。'

# 给予全体命令
command-giveall-description: 给予所有在线玩家点劵
command-giveall-success: '&a已成功给予 &b%amount% &a%currency% 给所有在线玩家。'

# 拿去命令
command-take-description: 从玩家那取走点劵
command-take-success: '&a成功从玩家 &b%player% 那拿走 &b%amount% &a%currency% 。'
command-take-not-enough: '&b%player% &c没有足够的点劵 %currency% 来被拿走。'

# 查看命令
command-look-description: 查看一名玩家的点卷数
command-look-success: '&b%player% &e有 &b%amount% &e%currency%。'

# 转账命令
command-pay-description: 给一名玩家转账
command-pay-self: '&c您不能给自己转账。'
command-pay-sent: '&a您向玩家 &b%player% 转账了 %amount% &a%currency%。'
command-pay-received: '&e您收到了来自玩家 &b%player% &e的 &b%amount% &e%currency% 。'
command-pay-lacking-funds: '&c您没有多于 %currency% 的点劵来那么做。'
command-pay-minimum-amount: '&c您可以支付的最低金额为 &b%amount% &c%currency%。'

# 设置命令
command-set-description: 设置一名玩家的点劵
command-set-success: '&a已设置玩家 &b%player% &a的 %currency% 至 &b%amount%&a。'

# 重置命令
command-reset-description: 重置一名玩家的点劵
command-reset-success: '&a已重置 &b%player% &a的 %currency% 。'

# 自查命令
command-me-description: 查看您的点劵
command-me-success: '&e您有 &b%amount% &e%currency%。'

# 排行
command-lead-description: 查看排行榜
command-lead-title: '&e排行榜 &7(页 #%page%/%pages%)'
command-lead-entry: '&b%position%). &e%player% &7- &6%amount% %currency%'
command-lead-usage: '&c无效的排行榜页面.'

# 广播命令
command-broadcast-description: 广播一名玩家的点劵
command-broadcast-message: '&b%player% &e有 &b%amount% &e%currency%。'

# 重载命令
command-reload-description: 重载插件
command-reload-reloaded: '&a配置和语言文件已被重新加载。'

# 导出命令
command-export-description: 导出数据至 storage.yml
command-export-success: '&a已保存的数据已被导出 storage.yml.'
command-export-warning: '&c注意: 一个 storage.yml 文件已经存在。如果您想覆盖文件，请使用 &b/points export confirm&c。'

# 导入命令
command-import-description: 从 storage.yml 导入数据
command-import-success: '&a已保存的数据已从 storage.yml 导入。'
command-import-no-backup: '&c无法导入, storage.yml 不存在。您可以使用 &b/points export &c生成一个并使用其来切换数据的存储形式。'
command-import-warning: '&c注意: 这个操作会删除现有的所有数据并覆盖成 storage.yml 所包含的数据结构。&c目前活跃的数据类型为 &b&o&l%type%&c。&c如果您知道您要做什么, 请使用 &b/points import confirm&c 来确认操作。'

# 迁移命令
command-convert-description: 从另一个插件迁移数据
command-convert-invalid: '&b%plugin% &c不是一个可转换的插件名。'
command-convert-invalid-currency: '&b%currency% &c不是有效的可兑换货币。'
command-convert-currency-required: '&c您必须指定使用此插件转换的货币。'
command-convert-success: '&a来自 &b%plugin% &a的货币数据已被迁移。'
command-convert-failure: '&c尝试迁移数据时发生了错误。请检查您的控制台并将您所看到的报告给 PlayerPoint 作者。'
command-convert-warning: '&c注意: 这个操作会删除所有现存的数据并使用来自插件 &b%plugin%&c 的数据结构。 &c如果您知道您在做什么，请使用 &b/points convert %plugin% confirm&c 来确认操作。'
command-convert-warning-currency: '&c注意: 这个操作会删除所有现存的数据并使用来自插件 &b%plugin%&c 的数据结构。 &c如果您知道您在做什么，请使用 &b/points convert %plugin% %currency% confirm&c 来确认操作。'

# Account Command
command-account-description: 管理账户的创建和删除
command-account-create-success: '&a成功创建账户: &b%account%'
command-account-create-exists: '&c创建账户失败。名为 &b%account% &c的帐户已经存在。'
command-account-create-name-invalid: '&c创建账户失败。账户名称长度必须在 3-16 个字符之间，且只能包含字母、数字和下划线。'
command-account-delete-success: '&a成功删除账户。'
command-account-delete-does-not-exist: '&c删除账户失败。名为 &b%account% &c的帐户不存在。'
command-account-delete-warning: '&c注意 此操作将删除 &b%account% &c账户的所有账户数据。如果您对此有绝对把握，请使用 &b/points account delete %account% confirm&c.'

# Import Legacy Command
command-importlegacy-description: 导入一个遗留的表格
command-importlegacy-success: '&a成功地从&b%table%&a导入遗留数据。'
command-importlegacy-failure: '&c从&b%table%&c导入遗留数据失败。该表是否存在？'
command-importlegacy-only-mysql: '&c这个命令只有在你启用了MySQL后才可用。'

# Version Command
command-version-description: 显示版本信息 PlayerPoints
