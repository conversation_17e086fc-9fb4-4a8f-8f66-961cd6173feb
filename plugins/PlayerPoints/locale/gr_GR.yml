# Plugin Message Prefix
prefix: '&7[<g:#E8A230:#ECD32D>PlayerPoints&7] '

# Currency
currency-singular: Point
currency-plural: Points
currency-separator: ','
currency-decimal: .
number-abbreviation-thousands: k
number-abbreviation-millions: m
number-abbreviation-billions: b

# Misc
no-permission: '&cΔεν έχεις άδεια για αυτό!'
only-player: '&cΑυτή η εντολή μπορεί να εκτελεστεί μόνο από έναν παίκτη.'
invalid-amount: '&cΤο ποσό πρέπει να είναι θετικός ακέραιος αριθμός.'
unknown-player: '&cΔεν ήταν δυνατή η εύρεση του παίκτη: &b%player%'
unknown-account: '&cΟ λογαριασμός δεν βρέθηκε: &b%account%'
unknown-command: '&cΑγνωστη εντολή: &b%input%'
invalid-argument: '&cΆκυρο όρισμα: %message%.'
command-usage: '&cΧρήση: &b/%cmd% %args%'
argument-handler-string: Το κείμενο δεν μπορεί να είναι κενό
argument-handler-enum: Ο τύπος %enum% [%input%] δεν υπάρχει
argument-handler-enum-list: 'Ο τύπος %enum% [%input%] δεν υπάρχει. Έγκυροι τύποι: %types%'
argument-handler-value: Η τιμή [%input%] δεν είναι έγκυρη
votifier-voted: '&eΕυχαριστουμε που ψηφίσατε στο %service%! &b%amount% &έχουν προστεθεί στο υπόλοιπό σας.'
leaderboard-empty-entry: Αυτό μπορεί να είσαι εσύ!
command-cooldown: '&cΠεριμένετε πριν χρησιμοποιήσετε ξανά αυτήν την εντολή.'

# Base Command Message
base-command-color: '&e'
base-command-help: '&Χρησημοποιήστε το &b/%cmd% help &eγια πληροφορές σχετικά με την εντολή.'

# Help Command
command-help-description: Εμφανίζει το μενού βοήθειας... Είστε στο σωστό μέρος.
command-help-title: '&eΔιαθέσιμες εντολές:'
command-help-list-description: '&8 - &d/%cmd% %subcmd% %args% &7- %desc%'
command-help-list-description-no-args: '&8 - &d/%cmd% %subcmd% &7- %desc%'

# Give Command
command-give-description: Δώσε πόντους σε έναν παίκτη.
command-give-success: '&b%player% &έδωσε &b%amount% &a%currency%.'
command-give-received: '&eΛάβατε &b%amount% &e%currency%.'

# Give All Command
command-giveall-description: Δίνει σε όλους τους παίκτες που είναι online πόντους.
command-giveall-success: '&aΔόθηκαν &b%amount% &a%currency% σε όλους τους παίκτες που είναι online..'

# Take Command
command-take-description: Πάρε πόντους απο έναν παίκτη.
command-take-success: '&aΈλαβε &b%amount% &a%currency% από &b%player%&a.'
command-take-not-enough: '&b%player% &cδεν έχει αρκετά %currency% για αυτό.'

# Look Command
command-look-description: Δες τους πόντου ενός παίκτη.
command-look-success: '&b%player% &eέχει &b%amount% &e%currency%.'

# Pay Command
command-pay-description: Πλήρωσε έναν παίκτη.
command-pay-self: '&cΔεν μπορείς να πληρώσεις τον ευατό σου!'
command-pay-sent: '&aΠλήρωσες &b%player% %amount% &a%currency%.'
command-pay-received: '&eΠληρώθηκες &b%amount% &e%currency% από τον &b%player%&e.'
command-pay-lacking-funds: '&cΔεν έχεις αρκετά %currency% για αυτό.'
command-pay-minimum-amount: '&cΤο ελάχιστο ποσό που μπορείτε να πληρώσετε είναι &b%amount% &c%currency%.'

# Set Command
command-set-description: Δήλωσε τους πόντους ενός παίκτη.
command-set-success: '&aΔήλωσε τα %currency% του &b%player% &aστο &b%amount%&a.'

# Reset Command
command-reset-description: Μηδένισε τους πόντους ενός παίκτη.
command-reset-success: '&aΜηδένισε τα %currency% του &b%player%&a.'

# Me Command
command-me-description: Ρίξε μια ματιά στους πόντους σου!
command-me-success: '&eΈχεις &b%amount% &e%currency%.'

# Lead Command
command-lead-description: Εμφάνησε το leaderboard
command-lead-title: '&eLeaderboard &7(Page #%page%/%pages%)'
command-lead-entry: '&b%position%). &e%player% &7- &6%amount% %currency%'
command-lead-usage: '&cΜη έγκυρη σελίδα leaderboard.'

# Broadcast Command
command-broadcast-description: Μεταδώστε τους πόντους ενός παίκτη
command-broadcast-message: '&b%player% &eέχει &b%amount% &e%currency%.'

# Reload Command
command-reload-description: Φορτώνει ξανά το plugin
command-reload-reloaded: '&aΤα configuration και locale αρχεία φορτώθηκαν ξανά.'

# Export Command
command-export-description: Εξάγει τα δεδομένα στο storage.yml
command-export-success: '&aΤΑ Save data έχουν εξαχθεί στο storage.yml.'
command-export-warning: '&cΠροσοχή: Το αρχείο storage.yml υπάρχει είδη. Εάν θέλετε να το αντικαταστήσετε, χρησιμοποιήστε το &b/points export confirm&c.'

# Import Command
command-import-description: Εισαγάγει δεδομένα από το storage.yml
command-import-success: '&aΤα Save data έχουν εισαχθεί από το  storage.yml.'
command-import-no-backup: '&cΑδυναμία εισαγωγής, το αρχείο storage.yml δεν υπάρχει. Μπορείτε να δημιουργήσετε ένα με την χρήση της &b/points export &cand use it to transfer data between database types.'
command-import-warning: '&cΠροσοχή: Αυτή η ενέργεια θα διαγράψει ολα τα δεδομέμα απο την ενεργή βάση δεδομένων και θα τα αντικαταστήσει με το περιεχόμενο του αρχείου storage.yml. &cΗ τρέχουσα ενεργή βάση δεδομένων είναι &b&o&l%type%&c. &cΕάν είστε απολύτως σίγουροι για αυτό, χρησιμοποιήστε την &b/points import confirm&c.'

# Convert Command
command-convert-description: Φορτώνει τα δεδομένα νομίσματος από άλλο plugin
command-convert-invalid: '&b%plugin% &cδεν είναι μετατρέψιμο νόμισμα στο plugin.'
command-convert-invalid-currency: '&cΤο &b%currency% &cδεν είναι έγκυρο μετατρέψιμο νόμισμα.'
command-convert-currency-required: '&cΠρέπει να καθορίσετε ένα νόμισμα προς μετατροπή για αυτό το πρόσθετο.'
command-convert-success: '&aΤα δεδομένα νομίσματος από το &b%plugin% &aμετατρέπονται.'
command-convert-failure: '&cΠαρουσιάστηκε σφάλμα κατά την προσπάθεια μετατροπής των δεδομένων. Ελέξτε το console σας και αναφέρετε τυχόν σφάλματα στον συντάκτη του PlayerPoints plugin.'
command-convert-warning: '&cΠροσοχλη: Αυτή η ενέργεια θα διαγράψει όλα τα δεδομένα data από την ενεργή βαση δεδομένων και θα τα αντικαταστήσει με τα δεδομενα από το &b%plugin%&c. &cΑν είστε σίγουροι για αυτο χρησιμοποιήστε την εντολή &b/points convert %plugin% confirm&c.'
command-convert-warning-currency: '&cΠροσοχλη: Αυτή η ενέργεια θα διαγράψει όλα τα δεδομένα data από την ενεργή βαση δεδομένων και θα τα αντικαταστήσει με τα δεδομενα από το &b%plugin%&c. &cΑν είστε σίγουροι για αυτο χρησιμοποιήστε την εντολή &b/points convert %plugin% %currency% confirm&c.'

# Account Command
command-account-description: Διαχειρίζεται τη δημιουργία και τη διαγραφή λογαριασμών
command-account-create-success: '&aΔημιουργήθηκε επιτυχώς λογαριασμός: &b%account%'
command-account-create-exists: '&cΑπέτυχε η δημιουργία λογαριασμού. Ένας λογαριασμός με όνομα &b%account% &cυπάρχει ήδη.'
command-account-delete-success: '&aΔιαγράφηκε επιτυχώς ο λογαριασμός.'
command-account-delete-does-not-exist: '&cΑπέτυχε η διαγραφή λογαριασμού. Ένας λογαριασμός με όνομα &b%account% &cδεν υπάρχει.'
command-account-delete-warning: '&cΑνακοίνωση: Αυτή η λειτουργία θα διαγράψει όλα τα δεδομένα λογαριασμού για το λογαριασμό &b%account%&c. Αν είστε απολύτως σίγουροι γι'' αυτό, χρησιμοποιήστε &b/points account delete %account% confirm&c.'

# Import Legacy Command
command-importlegacy-description: Εισαγάγεται εναν legacy πίνακα.
command-importlegacy-success: '&aΗ εισαγωγή των legacy δεδομένων έγινε με επιτυχία από τον &b%table%&a.'
command-importlegacy-failure: '&cΑπέτυχε η εισαγωγή από τον &b%table%&c. Υπάρχει ο πίνακα;?'
command-importlegacy-only-mysql: '&cΑυτή η εντολή είναι διαθέσιμη μόνο όταν η MySQL είναι ενεργή.'

# Version Command
command-version-description: Εμφάνιση των πληροφοριών έκδοσης για PlayerPoints
