# Plugin Message Prefix
prefix: '&7[<g:#E8A230:#ECD32D>PlayerPoints&7] '

# Currency
currency-singular: Point
currency-plural: Points
currency-separator: ','
currency-decimal: .
number-abbreviation-thousands: k
number-abbreviation-millions: m
number-abbreviation-billions: b

# Misc
no-permission: '&cYou don''t have permission for that!'
only-player: '&cThis command can only be executed by a player.'
invalid-amount: '&cAmount must be a positive whole number.'
unknown-player: '&cPlayer could not be found: &b%player%'
unknown-account: '&cAccount could not be found: &b%account%'
unknown-command: '&cUnknown command: &b%input%'
invalid-argument: '&cInvalid argument: %message%.'
command-usage: '&cUsage: &b/%cmd% %args%'
argument-handler-string: String cannot be empty
argument-handler-enum: '%enum% type [%input%] does not exist'
argument-handler-enum-list: '%enum% type [%input%] does not exist. Valid types: %types%'
argument-handler-value: Value [%input%] is not valid
votifier-voted: '&eThanks for voting on %service%! &b%amount% &ehas been added to your balance.'
leaderboard-empty-entry: This could be you!
command-cooldown: '&cPlease wait before using that command again.'

# Base Command Message
base-command-color: '&e'
base-command-help: '&eUse &b/%cmd% help &efor command information.'

# Help Command
command-help-description: Displays the help menu... You have arrived
command-help-title: '&eAvailable Commands:'
command-help-list-description: '&8 - &d/%cmd% %subcmd% %args% &7- %desc%'
command-help-list-description-no-args: '&8 - &d/%cmd% %subcmd% &7- %desc%'

# Give Command
command-give-description: Give a player points
command-give-success: '&b%player% &awas given &b%amount% &a%currency%.'
command-give-received: '&eYou have received &b%amount% &e%currency%.'

# Give All Command
command-giveall-description: Gives all online players points
command-giveall-success: '&aGave &b%amount% &a%currency% to all online players.'

# Take Command
command-take-description: Take points from a player
command-take-success: '&aTook &b%amount% &a%currency% from &b%player%&a.'
command-take-not-enough: '&b%player% &cdoes not have enough %currency% for that.'

# Look Command
command-look-description: View a player's points
command-look-success: '&b%player% &ehas &b%amount% &e%currency%.'

# Pay Command
command-pay-description: Pay a player
command-pay-self: '&cYou cannot pay yourself!'
command-pay-sent: '&aYou paid &b%player% %amount% &a%currency%.'
command-pay-received: '&eYou were paid &b%amount% &e%currency% by &b%player%&e.'
command-pay-lacking-funds: '&cYou do not have enough %currency% for that.'
command-pay-minimum-amount: '&cThe minimum amount you can pay is &b%amount%&c %currency%.'

# Set Command
command-set-description: Set a player's points
command-set-success: '&aSet the %currency% of &b%player% &ato &b%amount%&a.'

# Reset Command
command-reset-description: Reset a player's points
command-reset-success: '&aReset the %currency% for &b%player%&a.'

# Me Command
command-me-description: View your points
command-me-success: '&eYou have &b%amount% &e%currency%.'

# Lead Command
command-lead-description: View the leaderboard
command-lead-title: '&eLeaderboard &7(Page #%page%/%pages%)'
command-lead-entry: '&b%position%). &e%player% &7- &6%amount% %currency%'
command-lead-usage: '&cInvalid leaderboard page.'

# Broadcast Command
command-broadcast-description: Broadcast a player's points
command-broadcast-message: '&b%player% &ehas &b%amount% &e%currency%.'

# Reload Command
command-reload-description: Reloads the plugin
command-reload-reloaded: '&aConfiguration and locale files were reloaded.'

# Export Command
command-export-description: Exports the data to storage.yml
command-export-success: '&aSave data has been exported to storage.yml.'
command-export-warning: '&cNotice: A storage.yml file already exists. If you would like to overwrite it, use &b/points export confirm&c.'

# Import Command
command-import-description: Imports the data from storage.yml
command-import-success: '&aSave data has been imported from storage.yml.'
command-import-no-backup: '&cUnable to import, storage.yml does not exist. You can generate one with &b/points export &cand use it to transfer data between database types.'
command-import-warning: '&cNotice: This operation will delete all data from the active database and replace it with the contents of storage.yml. &cThe currently active database type is &b&o&l%type%&c. &cIf you are absolutely sure about this, use &b/points import confirm&c.'

# Convert Command
command-convert-description: Loads currency data from another plugin
command-convert-invalid: '&b%plugin% &cis not a convertible currency plugin name.'
command-convert-invalid-currency: '&b%currency% &cis not a valid convertible currency.'
command-convert-currency-required: '&cYou must specify a currency to convert for this plugin.'
command-convert-success: '&aCurrency data from &b%plugin% &ais being converted.'
command-convert-failure: '&cAn error occurred while attempting to convert the data. Please check your console and report any errors to the PlayerPoints plugin author.'
command-convert-warning: '&cNotice: This operation will delete all data from the active database and replace it with the data from &b%plugin%&c. &cIf you are absolutely sure about this, use &b/points convert %plugin% confirm&c.'
command-convert-warning-currency: '&cNotice: This operation will delete all data from the active database and replace it with the data from &b%plugin%&c. &cIf you are absolutely sure about this, use &b/points convert %plugin% %currency% confirm&c.'

# Account Command
command-account-description: Create or delete an accounts
command-account-create-success: '&aSuccessfully created account: &b%account%'
command-account-create-exists: '&cFailed to create account. An account named &b%account% &calready exists.'
command-account-create-name-invalid: '&cFailed to create account. Account names must be between 3-16 characters long and can only contain letters, numbers, and underscores.'
command-account-delete-success: '&aSuccessfully deleted account &b%account%&a.'
command-account-delete-does-not-exist: '&cFailed to delete account. An account named &b%account% &cdoes not exist.'
command-account-delete-warning: '&cNotice: This operation will delete all account data for the account &b%account%&c. If you are absolutely sure about this, use &b/points account delete %account% confirm&c.'

# Import Legacy Command
command-importlegacy-description: Import a legacy table
command-importlegacy-success: '&aSuccessfully imported legacy data from &b%table%&a.'
command-importlegacy-failure: '&cFailed to import legacy data from &b%table%&c. Does the table exist?'
command-importlegacy-only-mysql: '&cThis command is only available when you have MySQL enabled.'

# Version Command
command-version-description: Display the version info for PlayerPoints
