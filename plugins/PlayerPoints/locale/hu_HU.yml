# Plugin Message Prefix
prefix: '&7[<g:#E8A230:#ECD32D>PlayerPoints&7] '

# Currency
currency-singular: Pont
currency-plural: Pont
currency-separator: ','
currency-decimal: .
number-abbreviation-thousands: k
number-abbreviation-millions: m
number-abbreviation-billions: b

# Misc
no-permission: '&cNincs jogod ehhez!'
only-player: '&cEzt a parancsot csak egy játékos hajthatja végre.'
invalid-amount: '&cMennyiségnek pozitív egész számnak kell lennie.'
unknown-player: '&cJátékos nem található: &b%player%'
unknown-account: '&cA fiókot nem sikerült megtalálni: &b%account%'
unknown-command: '&cIsmeretlen parancs: &b%input%'
invalid-argument: '&cInvalid argument: %message%.'
command-usage: '&cHasználat: &b/%cmd% %args%'
argument-handler-string: A szöveg nem lehet üres
argument-handler-enum: '%enum% típus [%input%] nem létezik'
argument-handler-enum-list: '%enum% típus [%input%] nem létezik. Érvényes típusok: %types%'
argument-handler-value: A [%input%] érték nem érvényes
votifier-voted: '&eKöszönjük hogy szavaztál ránk itt %service%! &b%amount% &ehozzáadva az egyenlegedhez.'
leaderboard-empty-entry: Ez akár te is lehetsz!
command-cooldown: '&cKérjük, várjon, mielőtt újra használná ezt a parancsot.'

# Base Command Message
base-command-color: '&e'
base-command-help: '&eHasználd a &b/%cmd% help &eparancsot segítségért.'

# Help Command
command-help-description: Előhozza a segítségeket... Ahova megérkeztél
command-help-title: '&eElérhető Parancsok:'
command-help-list-description: '&8 - &d/%cmd% %subcmd% %args% &7- %desc%'
command-help-list-description-no-args: '&8 - &d/%cmd% %subcmd% &7- %desc%'

# Give Command
command-give-description: Ad a játékosnak pontot
command-give-success: '&b%player% &akapott &b%amount% &a%currency%ot.'
command-give-received: '&eKaptál &b%amount% &e%currency%ot.'

# Give All Command
command-giveall-description: Ad az összes elérhető játékosnak pontot
command-giveall-success: '&aAdtál &b%amount% &a%currency%ot az összes elérhető játékosnak.'

# Take Command
command-take-description: Elvesz pontot a játékostól
command-take-success: '&aElvettél &b%amount% &a%currency%ot &b%player% &ajátékostól.'
command-take-not-enough: '&b%player%-nak/nek &cnincs elég %currency%ja ehhez.'

# Look Command
command-look-description: Játékos pontjainak megtekintése
command-look-success: '&b%player%-nak/nek &evan &b%amount% &e%currency%ja.'

# Pay Command
command-pay-description: Fizetés egy játékosnak
command-pay-self: '&cNem tudsz magadnak fizetni.'
command-pay-sent: '&aFizettél &b%player%-nak/nek %amount% &a%currency%ot.'
command-pay-received: '&eFizetett neked &b%amount% &e%currency%ot &b%player%&e.'
command-pay-lacking-funds: '&cNincs elég %currency%od ehhez.'
command-pay-minimum-amount: '&cA minimálisan fizethető összeg &b%összeg% &c%pénz%.'

# Set Command
command-set-description: Beállítja egy játékos pontját
command-set-success: '&aBeállítottad a %currency%ját &b%player%-nak/nek &aennyire &b%amount%&a.'

# Reset Command
command-reset-description: Játékos pontjait alaphelyzetbe állítja
command-reset-success: '&aAlaphelyzetbe állítottad a %currency%jait &b%player%-nak/nek&a.'

# Me Command
command-me-description: Pontjaid megtekintése
command-me-success: '&eNeked van &b%amount% &e%currency%od.'

# Lead Command
command-lead-description: Ranglista megtekintése
command-lead-title: '&eRanglista &7(Oldal #%page%/%pages%)'
command-lead-entry: '&b%position%). &e%player% &7- &6%amount% %currency%'
command-lead-usage: '&cÉrvénytelen ranglista oldal.'

# Broadcast Command
command-broadcast-description: Kihírdeti a játékos pontjait
command-broadcast-message: '&b%player%-nak/nek &evan &b%amount% &e%currency%ja.'

# Reload Command
command-reload-description: Újratölti a plugint
command-reload-reloaded: '&aKonfiguráció és a fordítások újratöltődtek.'

# Export Command
command-export-description: Exportálja az adatokat a storage.yml fájlba
command-export-success: '&aMentett adatok exportálva a storage.yml fájlba.'
command-export-warning: '&cFigyelmeztetés: A storage.yml fájl már létezik. Ha felül szeretnéd írni, használd a &b/points export confirm &cparancsot.'

# Import Command
command-import-description: Importálja az adatokat a storage.yml fájlból
command-import-success: '&aMentett adatok importálva a storage.yml fájlból.'
command-import-no-backup: 'command-import-no-backup: ''&cSikertelen importálás, storage.yml fájl nem létezik. Készíthetsz egyet a &b/points export &cparancsal és használhatod hogy átvidd az adatokat az adatbázis típusok között.'''
command-import-warning: '&cFigyelmeztetés: Ez a művelet törli az összes eddigi adatot és importálja az adatokat a storage.yml fájlból. &cA jelenleg aktív adatbázis típusa &b&o&l%type%&c. &cHa teljesen biztos vagy ebben, akkor használd a &b/points import confirm &cparancsot.'

# Convert Command
command-convert-description: Betölti a valuta adatot egy másik pluginból
command-convert-invalid: '&b%plugin% &cnem konvertálható valuta plugin.'
command-convert-invalid-currency: '&b%currency% &cnem érvényes konvertibilis valuta.'
command-convert-currency-required: '&cEhhez a bővítményhez meg kell adnia a konvertálandó pénznemet.'
command-convert-success: '&aValuta adat a &b%plugin%-ból/ből &aát konvertálva.'
command-convert-failure: '&cHiba történt az adat konvertálás közben. Kérlek ellenőrizd a konzolt és jelezd a hibákat a PlayerPoints plugin készítőjének.'
command-convert-warning: '&cFigyelmeztetés: Ez a művelet törli az összes eddigi adatot és importálja az adatokat a &b%plugin%-ból/ből&c. &cHa teljesen biztos vagy ebben, akkor használd a &b/points convert %plugin% confirm &cparancsot.'
command-convert-warning-currency: '&cFigyelmeztetés: Ez a művelet törli az összes eddigi adatot és importálja az adatokat a &b%plugin%-ból/ből&c. &cHa teljesen biztos vagy ebben, akkor használd a &b/points convert %plugin% %currency% confirm &cparancsot.'

# Account Command
command-account-description: Kezeli a számlák létrehozását és törlését
command-account-create-success: '&aSikeresen létrehozott fiók: &b%account%'
command-account-create-exists: '&cNem sikerült fiókot létrehozni. Egy &b%account% &cnevű fiók már létezik.'
command-account-create-name-invalid: '&cNem sikerült fiókot létrehozni. A fiókneveknek 3-16 karakter hosszúságúnak kell lenniük, és csak betűket, számokat és aláhúzásokat tartalmazhatnak.'
command-account-delete-success: '&aSikeresen törölte a fiókot.'
command-account-delete-does-not-exist: '&cNem sikerült törölni a fiókot. A &b%account% &cnevű fiók nem létezik.'
command-account-delete-warning: '&cÉrtesítés: Ez a művelet törli a &b%account% &cfiók összes fiókadatát. Ha teljesen biztos vagy benne, használd &b/points account delete %account% confirm&c.'

# Import Legacy Command
command-importlegacy-description: Importálja a régebbi táblát
command-importlegacy-success: '&aSikeresen importálva a régi adatok a &b%table%-ból/ből&a.'
command-importlegacy-failure: '&cSikertelen régi adat importálás a &b%table%-ból/ből&c. Létezik az tábla?'
command-importlegacy-only-mysql: '&cEz a parancs csak akkor elérhető ha a MySQL engedélyezve van.'

# Version Command
command-version-description: Kiírja a verzió információt a PlayerPointsról
