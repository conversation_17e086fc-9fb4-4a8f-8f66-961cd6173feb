# Plugin Message Prefix
prefix: '&7[<g:#E8A230:#ECD32D>PlayerPoints&7] '

# Currency
currency-singular: Point
currency-plural: Points
currency-separator: ','
currency-decimal: .
number-abbreviation-thousands: k
number-abbreviation-millions: m
number-abbreviation-billions: b

# Misc
no-permission: '&cBạn không có quyền để sử dụng!'
only-player: '&cLệnh này chỉ có thể được thực hiện bởi người chơi.'
invalid-amount: '&cSố tiền phải là một số nguyên dương.'
unknown-player: '&cKhông thể tìm thấy người chơi: &b%player%'
unknown-account: '&cKhông tìm thấy tài khoản: &b%account%'
unknown-command: '&cLệnh không xác định: &b%input%'
invalid-argument: '&cĐối số không hợp lệ: %message%.'
command-usage: '&cCách sử dụng: &b/%cmd% %args%'
argument-handler-string: V<PERSON><PERSON> bản không được để trống
argument-handler-enum: '%enum% loại [%input%] không tồn tại'
argument-handler-enum-list: 'Kiểu %enum% [%input%] không tồn tại. Các kiểu hợp lệ: %types%'
argument-handler-value: Giá trị [%input%] không hợp lệ
votifier-voted: '&eCảm ơn vì đã bỏ phiếu trên %service%! &b%amount% &eđã được thêm vào số dư của bạn.'
leaderboard-empty-entry: Đây có thể là bạn!
command-cooldown: '&cVui lòng đợi trước khi sử dụng lại lệnh đó.'

# Base Command Message
base-command-color: '&e'
base-command-help: '&eSử dụng &b/points help &echo thông tin các lệnh.'

# Help Command
command-help-description: Hiển thị menu trợ giúp ... Bạn đã đến
command-help-title: '&eCác lệnh có sẵn:'

# Give Command
command-give-description: Trao cho người chơi points
command-give-success: '&b%player% &ađã nhận được &b%amount% &a%currency%.'
command-give-received: '&eBạn đã nhận được &b%amount% &e%currency%.'

# Give All Command
command-giveall-description: Trao cho tất cả người chơi trực tuyến points
command-giveall-success: '&aTrao &b%amount% &a%currency% cho tất cả người chơi trực tuyến.'

# Take Command
command-take-description: Lấy points từ người chơi
command-take-success: '&aLấy đi &b%amount% &a%currency% từ &b%player%&a.'
command-take-not-enough: '&b%player% &ckhông có đủ %currency% cho điều này.'

# Look Command
command-look-description: Xem points của người chơi
command-look-success: '&b%player% &ecó &b%amount% &e%currency%.'

# Pay Command
command-pay-description: Chuyển points cho người chơi
command-pay-self: '&cBạn không thể chuyển cho chính mình.'
command-pay-sent: '&aBạn đã chuyển cho &b%player% %amount% &a%currency%.'
command-pay-received: '&eBạn đã được chuyển &b%amount% &e%currency% bởi &b%player%&e.'
command-pay-lacking-funds: '&cBạn không có đủ %currency% cho điều này.'
command-pay-minimum-amount: '&cSố tiền tối thiểu bạn có thể thanh toán là &b%amount% &c%currency%.'

# Set Command
command-set-description: Đặt số points của người chơi
command-set-success: '&aĐặt số %currency% của &b%player% &athành &b%amount%&a.'

# Reset Command
command-reset-description: Đặt lại số points của người chơi
command-reset-success: '&aĐặt lại số %currency% cho &b%player% &a.'

# Me Command
command-me-description: Xem số points của bạn
command-me-success: '&eBạn có &b%amount% &e%currency%.'

# Lead Command
command-lead-description: Xem bảng xếp hạng
command-lead-title: '&eBảng Xếp Hạng &7(Trang #%page%/%pages%)'
command-lead-entry: '&b%position%). &e%player% &7- &6%amount% %currency%'
command-lead-usage: '&cTrang bảng xếp hạng không hợp lệ.'

# Broadcast Command
command-broadcast-description: Broadcast points của người chơi
command-broadcast-message: '&b%player% &ecó &b%amount% &e%currency%.'

# Reload Command
command-reload-description: Tải lại plugin
command-reload-reloaded: '&aCác tệp cấu hình và ngôn ngữ đã được tải lại.'

# Export Command
command-export-description: Xuất dữ liệu sang storage.yml
command-export-success: '&aLưu dữ liệu đã được xuất sang storage.yml.'
command-export-warning: '&cLưu ý: Tệp storage.yml đã tồn tại. Nếu bạn muốn ghi đè nó, sử dụng &b/points export confirm&c.'

# Import Command
command-import-description: Nhập dữ liệu từ storage.yml
command-import-success: '&aLưu dữ liệu đã được nhập từ storage.yml.'
command-import-no-backup: '&cKhông thể nhập, storage.yml không tồn tại. Bạn có thể tạo một cái bằng &b/points export &cvà sử dụng nó để chuyển dữ liệu giữa các loại cơ sở dữ liệu.'
command-import-warning: '&cLưu ý: Thao tác này sẽ xóa tất cả dữ liệu khỏi cơ sở dữ liệu đang hoạt động và thay thế nó bằng nội dung của storage.yml. &cLoại cơ sở dữ liệu hiện đang hoạt động là &b&o&l%type%&c. &cNếu bạn hoàn toàn chắc chắn về điều này, hãy sử dụng &b/points import confirm&c.'

# Convert Command
command-convert-description: Load dữ liệu tiền tệ từ một plugin khác
command-convert-invalid: '&b%plugin% &ckhông phải là tên plugin tiền tệ có thể chuyển đổi.'
command-convert-invalid-currency: '&b%currency% &ckhông phải là loại tiền tệ có thể chuyển đổi hợp lệ.'
command-convert-currency-required: '&cBạn phải chỉ định loại tiền tệ cần chuyển đổi bằng plugin này.'
command-convert-success: '&aDữ liệu tiền tệ từ &b%plugin% &ađã được chuyển đổi.'
command-convert-failure: '&cĐã xảy ra lỗi khi cố gắng chuyển đổi dữ liệu. Vui lòng kiểm tra bảng điều khiển của bạn và báo cáo bất kỳ lỗi nào cho tác giả plugin PlayerPoints.'
command-convert-warning: '&cLưu ý: Thao tác này sẽ xóa tất cả dữ liệu khỏi cơ sở dữ liệu đang hoạt động và thay thế bằng dữ liệu từ &b%plugin%&c. &cNếu bạn hoàn toàn chắc chắn về điều này, hãy sử dụng &b/points convert %plugin% confirm&c.'
command-convert-warning-currency: '&cLưu ý: Thao tác này sẽ xóa tất cả dữ liệu khỏi cơ sở dữ liệu đang hoạt động và thay thế bằng dữ liệu từ &b%plugin%&c. &cNếu bạn hoàn toàn chắc chắn về điều này, hãy sử dụng &b/points convert %plugin% %currency% confirm&c.'

# Account Command
command-account-description: Quản lý việc tạo và xóa tài khoản
command-account-create-success: '&aTài khoản được tạo thành công: &b%account%'
command-account-create-exists: '&cKhông tạo được tài khoản. Tài khoản có tên %account% đã tồn tại.'
command-account-delete-success: '&aĐã xóa tài khoản thành công.'
command-account-delete-does-not-exist: '&cKhông xóa được tài khoản. Tài khoản có tên &b%account% &ckhông tồn tại.'
command-account-delete-warning: '&cLưu ý: Thao tác này sẽ xóa tất cả dữ liệu tài khoản cho tài khoản &b%account%&c. Nếu bạn hoàn toàn chắc chắn về điều này, hãy sử dụng lệnh &b/points account delete %account% confirm&c.'

# Import Legacy Command
command-importlegacy-description: Nhập một bảng kế thừa
command-importlegacy-success: '&aĐã nhập thành công dữ liệu kế thừa từ &b%table%&a.'
command-importlegacy-failure: '&cKhông thể nhập dữ liệu cũ từ &b%table%&c. Bảng có tồn tại không?'
command-importlegacy-only-mysql: '&cLệnh này chỉ khả dụng khi bạn đã bật MySQL.'

# Version Command
command-version-description: Hiển thị thông tin phiên bản cho PlayerPoints
