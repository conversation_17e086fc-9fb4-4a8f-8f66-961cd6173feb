# プラグイン Prefix
prefix: '&7[<g:#E8A230:#ECD32D>PlayerPoints&7] '

# 通貨
currency-singular: ポイント
currency-plural: ポイント
currency-separator: ','
currency-decimal: .
number-abbreviation-thousands: k
number-abbreviation-millions: m
number-abbreviation-billions: b

# その他
no-permission: '&c権限不足です！'
only-player: '&cこのコマンドはプレイヤーのだけ使うができる。'
invalid-amount: '&c値は正の整数で指定してください。'
unknown-player: '&c&b%player% という名前のプレイヤーは見つかりませんでした。'
unknown-account: '&cアカウントが見つかりません: &b%account%'
unknown-command: '&c無効なコマンド: &b%input%'
invalid-argument: '無効な引数: %message%。'
command-usage: '&c使い方: &b/%cmd% %args%'
argument-handler-string: テキストを空にすることはできない
argument-handler-enum: '%enum% タイプ [%input%] がない'
argument-handler-enum-list: '%enum% タイプ [%input%] がない。有効なタイプ： %types%'
argument-handler-value: '[%input%] は無効です'
votifier-voted: '&e%service% での投票ありがとうございます。 &b%amount% &eポイントがアカウントに追加されました。'
leaderboard-empty-entry: 現在、ランキングは空です。
command-cooldown: '&cそのコマンドを再度使用する前にお待ちください。'

# 基礎コマンドメッセージ
base-command-color: '&e'
base-command-help: '&eプラグインの使い方に関しては &b/%cmd% help &e で解説されています。'

# ヘルプコマンド
command-help-description: ヘルプメニューを表示、、、ヘルプメニューへようこそ！
command-help-title: '&e使用可能なコマンド:'
command-help-list-description: '&8 - &d/%cmd% %subcmd% %args% &7- %desc%'
command-help-list-description-no-args: '&8 - &d/%cmd% %subcmd% &7- %desc%'

# Give コマンド
command-give-description: プレイヤーにポイントを付与する。
command-give-success: '&b%player% &aに &b%amount% &a%currency% &a付与しました。'
command-give-received: '&eあなたは &b%amount% &e%currency% &e受け取りました。'

# Give All コマンド
command-giveall-description: 鯖内の全プレイヤーにポイントを付与する。
command-giveall-success: '&a現在ログインしている全プレイヤーに &b%amount% &a%currency% 付与しました。'

# Take コマンド
command-take-description: プレイヤーからポイントを剥奪する。
command-take-success: '&b%player%&a から &b%amount% &a%currency% 剥奪しました。'
command-take-not-enough: '&b%player% &cの所持%currency%が足りないため。'

# Look Command
command-look-description: プレイヤーの所持ポイントを確認する。
command-look-success: '&b%player% &eは &b%amount% &e%currency% 所持しています。'

# Pay Command
command-pay-description: プレイヤーにポイントを支払う。
command-pay-self: '&c自分で支払うことはできない！'
command-pay-sent: '&aあなたは &b%player% に %amount% &a%currency% 支払いました。'
command-pay-received: '&eあなたは &b%player% から &b%amount% &e%currency% 受け取りました。'
command-pay-lacking-funds: '&c%currency% 不足です。'
command-pay-minimum-amount: お支払い可能な最低金額は &b%amount% &c%currency% です。

# Set Command
command-set-description: プレイヤーのポイントを新たな値に設定する。
command-set-success: '&b%player% &aの &e%currency% &aを &b%amount% &aに設定しました。'

# Reset Command
command-reset-description: プレイヤーのポイントをリセットする。
command-reset-success: '&b%player% &aの %currency% &aをリセットしました。'

# Me Command
command-me-description: 自分の所持ポイントを確認する。
command-me-success: '&eあなたは &b%amount% &e%currency% 所持しています。'

# Lead Command
command-lead-description: ポイントランキングを確認する。
command-lead-title: '&eランキング &7(ページ #%page%/%pages%)'
command-lead-entry: '&b%position%). &e%player% &7- &6%amount% %currency%'
command-lead-usage: '&c無効なリーダーボードページ。'

# Broadcast Command
command-broadcast-description: プレイヤーの所持ポイントを公表する。
command-broadcast-message: '&b%player% &eは &b%amount% &e%currency% 所持しています。'

# Reload Command
command-reload-description: プラグインをリロードする。
command-reload-reloaded: '&a設定ファイルと言語ファイルをリロードしました。'

# Export Command
command-export-description: storage.ymlにデータを書き出す。
command-export-success: '&aデータがstorage.ymlに書き出されました。'
command-export-warning: '&c注意: すでにstorage.ymlファイルが存在しています。上書きする場合は &b/points export confirm &cコマンドを実行してください。'

# Import Command
command-import-description: storage.ymlからデータを読み込む。
command-import-success: '&aデータがstorage.ymlから読み込まれました。'
command-import-no-backup: '&cstorage.ymlが存在しないため、読み込みが失敗しました。&b/points export &cで新たにstorage.ymlを生成し、データ形式の変換に使用できます。'
command-import-warning: '&c注意：この処理は現在使用中のデータベースのデータを削除し、storage.ymlの中身で置き換えます。&c現在使用中のデータベース形式は &b&o&l%type%&cです。 &cデータのバックアップを取ったうえで本当にこの処理を実行したい場合のみ &b/points import confirm &cコマンドを使用してください。'

# Convert Command
command-convert-description: ほかのプラグインから通貨のデータを読み込む。
command-convert-invalid: '&b%plugin% &cからの通貨の読み込みははサポートされていません。'
command-convert-invalid-currency: '&b%currency%&bは有効な兌換通貨ではありません。'
command-convert-currency-required: '&cこのプラグインで変換する通貨を指定する必要があります。'
command-convert-success: '&b%plugin% &aから通貨データが読み込まれました。'
command-convert-failure: '&cデータの読み込み中にエラーが発生しました。コンソールでエラーを確認し、PlayerPointsの作者に報告してください。'
command-convert-warning: '&c注意：この処理は現在使用中のデータベースのデータを削除し、&b%plugin%&cの中身で置き換えます。 &cデータのバックアップを取ったうえで本当にこの処理を実行したい場合のみ &b/points convert %plugin% confirm&cコマンドを使用してください。'
command-convert-warning-currency: '&c注意：この処理は現在使用中のデータベースのデータを削除し、&b%plugin%&cの中身で置き換えます。 &cデータのバックアップを取ったうえで本当にこの処理を実行したい場合のみ &b/points convert %plugin% %currency% confirm&cコマンドを使用してください。'

# Account Command
command-account-description: アカウントの作成と削除を管理
command-account-create-success: '&aアカウントの作成に成功しました: &b%account%'
command-account-create-exists: '&cアカウントの作成に失敗しました。アカウント &b%account% &cという名前のアカウントが既に存在します。'
command-account-create-name-invalid: '&cアカウントの作成に失敗しました。アカウント名の長さは3～16文字で、文字、数字、アンダースコアのみ使用できます。'
command-account-delete-success: '&a指定されたアカウントの削除に成功しました。'
command-account-delete-does-not-exist: '&cアカウントの削除に失敗しました。&b%account% &cという名前のアカウントは存在しません。'
command-account-delete-warning: '&c注意 この操作により、アカウント &b%account% &cのすべてのアカウント・データが削除されます。これが確実であれば、コマンド &b/points account delete %account% confirm&c.'

# Import Legacy Command
command-importlegacy-description: レガシーテーブルをインポートします
command-importlegacy-success: '&b%table%&aからレガシーのデータを首尾よくインポートしました。'
command-importlegacy-failure: '&b%table%&aからレガシーのデータのインポートに失敗しました。そのテーブルはありますか？'
command-importlegacy-only-mysql: '&cMySQLはエネブルたらこのコマンドは使用可能。'

# Version Command
command-version-description: バージョン情報の表示 PlayerPoints
