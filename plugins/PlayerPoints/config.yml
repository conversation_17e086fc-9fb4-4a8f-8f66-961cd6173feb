# __________ __                           __________       __        __
# \______   \  | _____  ___ __  __________\______   \____ |__| _____/  |_  ______
#  |     ___/  | \__  \<   |  |/ __ \_  __ \     ___/  _ \|  |/    \   __\/  ___/
#  |    |   |  |__/ __ \\___  \  ___/|  | \/    |  (  <_> )  |   |  \  |  \___ \
#  |____|   |____(____  / ____|\___  >__|  |____|   \____/|__|___|  /__| /____  >
#                     \/\/         \/                             \/          \/

# The locale to use in the /locale folder
# Default: en_US
locale: zh_CN

# Should we register with Vault as a currency manager?
# Default: false
vault: false

# The priority level to use for the Vault hook
# Higher priorities will allow PlayerPoints to load before other economy plugins
# Valid values: [Lowest, Low, Normal, High, Highest]
# Default: Low
vault-priority: Low

# Should we register with Treasury as a currency manager?
# Default: false
treasury: false

# The priority level to use for the Treasury hook
# Higher priorities will allow PlayerPoints to load before other economy plugins
# Valid values: [LOW, NORMAL, HIGH]
# Default: LOW
treasury-priority: LOW

# The number of players to be displayed per page on the leaderboard?
# Default: 10
leaderboard-per-page: 10

# The number of seconds to hold a player's points in cache before being released
# Default: 30
cache-duration: 30

# Should we send updates to other servers when a player's points value changes?
# This should work for any type of proxy
# Default: true
bungeecord-send-updates: true

# Should the leaderboard be disabled?
# Setting this to true will cause leaderboard placeholders to no longer function
# Default: false
leaderboard-disable: false

# The number of entries to keep updated in the leaderboard placeholder
# Only a certain number of leaderboard entries can be available at a time
# Accessing an entry that does not exist will display a placeholder message instead
# Default: 10
leaderboard-placeholder-entries: 10

# The number of seconds between leaderboard placeholder updates
# Default: 15
leaderboard-placeholder-refresh-interval: 15

# The amount of points new players will start with
# Default: 0
starting-balance: 0

# The minimum number of points you must send when using the pay command
# Set to -1 for no minimum amount
# Default: -1
minimum-pay-amount: -1

# When true, all players that have a PlayerPoints balance will show in tab complete.
# If false, only non-vanished online players will be displayed.
# Default: false
tab-complete-show-all-players: false

# How often (in seconds) should we update the list of accounts for tab completion purposes?
# Default: 300
cached-account-list-refresh-interval: 300

# Should we show non-player accounts on leaderboards?
# Default: false
show-non-player-accounts-on-leaderboards: false

# Votifier hook settings
vote:

  # If the votifier hook should be enabled
  # Default: false
  enabled: false

  # How many points should be awarded per vote
  # Default: 100
  amount: 100

  # Should points only be awarded when the player who voted is online?
  # Default: false
  online: false

# Which command should we redirect to when using '/points' with no subcommand specified?
# You can use any points command here that does not take any arguments
# If left as blank, the default behavior of showing '/points me' with bypassed permissions will be used
# Default: me
base-command-redirect: me

# Are you upgrading from a much older version of PlayerPoints?
# If you have done anything special with the database settings previously, you may need this
# WARNING: This setting may be removed in the future. Try to get your database updated to use the new format!
legacy-database-mode:

  # Should we use legacy database mode?
  # Default: false
  enabled: false

  # The name of the legacy database table
  # Default: playerpoints
  table-name: playerpoints

# Settings for if you want to use MySQL for data management
mysql-settings:

  # Enable MySQL
  # If false, SQLite will be used instead
  # Default: false
  enabled: false

  # MySQL Database Hostname
  # Default: 127.0.0.1
  hostname: 127.0.0.1

  # MySQL Database Port
  # Default: 3306
  port: 3306

  # MySQL Database Name
  # Default: ''
  database-name: ''

  # MySQL Database User Name
  # Default: ''
  user-name: ''

  # MySQL Database User Password
  # Default: ''
  user-password: ''

  # If the database connection should use SSL
  # You should enable this if your database supports SSL
  # Default: false
  use-ssl: false

  # The number of connections to make to the database
  # Default: 3
  connection-pool-size: 3
