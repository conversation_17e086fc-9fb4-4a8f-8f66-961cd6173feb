locales:
  teleporting_offline_player: '[正在将你传送到 %1% 上次离线的位置…](#00fb9a)'
  teleporting_warmup_start: '[你将在](#00fb9a) [%1%](#00fb9a bold) [秒后被传送,请不要移动…](#00fb9a)'
  teleporting_cancelled_movement: '[传送取消: 你移动了!](#ff7e5e)'
  teleporting_cancelled_damage: '[传送取消: 你受到了伤害!](#ff7e5e)'
  teleporting_complete: '[传送完成!](#00fb9a)'
  teleporting_action_bar_warmup: '[将在](#00fb9a) [%1%](#00fb9a bold)[秒后传送…](#00fb9a)'
  teleporting_action_bar_processing: '[传送中…](#00fb9a)'
  teleporting_action_bar_cancelled: '[传送取消!](#ff7e5e)'
  teleporting_other_complete: '[将 %1% 传送到 %2%](#00fb9a)'
  teleporting_other_complete_position: '[将 %1% 传送到 ⚐ x: %2%, y: %3%, z: %4%](#00fb9a)'
  teleporting_all_players: '[将所有玩家传送到你的位置…](#00fb9a)'
  teleporting_random_generation: '[正在寻找一个安全的随机位置传送你…](#00fb9a)'
  tpa_request_sent: '[已发送传送请求,请求传送到](#00fb9a) [%1%](#00fb9a bold)'
  tpahere_request_sent: '[已发送传送请求,请求](#00fb9a) [%1%](#00fb9a bold) [传送到你的位置](#00fb9a)'
  tpaall_request_sent: '[你已向所有玩家发送传送请求,请求他们传送到你的位置](#00fb9a)'
  tpa_request_received: '[%1%](#00fb9a bold) [请求传送到你的位置](#00fb9a)'
  tpahere_request_received: '[%1%](#00fb9a bold) [请求你传送到他的位置](#00fb9a)'
  teleport_request_buttons: '[选项:](gray) [[✔ 接受]](#00fb9a show_text=&#00fb9a&接受传送请求\n&8点击接受 %1% 的请求 run_command=/huskhomes:tpaccept %1%)   [[❌ 拒绝]](#ff3300 show_text=&#ff3300&拒绝传送请求\n&8点击拒绝 %1% 的请求 run_command=/huskhomes:tpdeny %1%)'
  teleport_request_accepted_confirmation: '[已接受 %1% 的传送请求](#00fb9a)'
  teleport_request_declined_confirmation: '[已拒绝 %1% 的传送请求](#ff7e5e)'
  teleport_request_accepted: '[%1% 接受了你的传送请求](#00fb9a)'
  teleport_request_declined: '[%1% 拒绝了你的传送请求](#ff7e5e)'
  tpignore_toggle_on: '[你已](#00fb9a) [忽略](#00fb9a bold) [传送请求](#00fb9a) %1%'
  tpignore_toggle_off: '[你已](#00fb9a) [恢复接收](#00fb9a bold) [传送请求](#00fb9a) %1%'
  tpignore_on_notification: '[你当前正在忽略传送请求!](gray) %1%'
  tpignore_toggle_button: '[[🔔 切换]](#ffc43b show_text=&#ffc43b&点击切换:\n&7忽略/接收传送请求 run_command=/huskhomes:tpignore)'
  edit_home_menu_title: '[查看家](#00fb9a) [%1%](#00fb9a bold) [的详细信息:](#00fb9a)'
  edit_home_menu_title_other: '[查看 %1% 家](#00fb9a) [%2%](#00fb9a bold) [的详细信息:](#00fb9a)'
  edit_home_menu_metadata_private: '[⌚ %1%](#999999-#ababab show_text=&7时间: \n&8设置家的时间)   [⚡ %2%](#ababab-#c7c7c7 show_text=&7家:\n&8%3%)   [∅ 私有的家](#ff9f0f show_text=&#ff9f0f&隐私: \n&8这个家是私有的)'
  edit_home_menu_metadata_public: '[⌚ %1%](#999999-#ababab show_text=&7时间: \n&8设置家的时间)   [⚡ %2%](#ababab-#c7c7c7 show_text=&7家ID:\n&8%3%)   [☀ 公共的家](#6fff00 show_text=&#6fff00&隐私: \n&8任何人都可传送)'
  edit_home_menu_description: '[ℹ %1%](#c160ff-#b43fff show_text=&#c160ff&描述:\n&8%2%)\n'
  edit_home_menu_world: '[⭘ %1%](#3b76ff-#5286ff show_text=&#3b76ff&世界:\n&8家所在的世界)'
  edit_home_menu_world_server: '[⭘ %1%](#3b76ff-#477fff show_text=&#3b76ff&世界:\n&8家所在的世界)   [☁ %2%](#477fff-#5286ff show_text=&#3b76ff&服务器: \n&8家所在的服务器)'
  edit_home_menu_coordinates: '[⚐ x: %1%, y: %2%, z: %3%](#ffc43b-#f5c962 show_text=&#ffc43b&坐标:\n&8家在世界上的位置\n&8偏航: %4%, 俯仰: %5%)\n'
  edit_home_menu_use_buttons: '[使用:](gray) [[⏩ 传送]](#00fb9a show_text=&7点击传送到这个家 run_command=/huskhomes:home %1%)'
  edit_home_menu_manage_buttons: '[管理:](gray) [[❌ 删除]](#ff3300 show_text=&7点击删除这个家\n&#ff3300&⚠ 此操作无法撤消! suggest_command=/huskhomes:delhome %1%)   [[⚐ 迁移]](#ffc43b-#f5c962 show_text=&7点击将这个家修改到你当前的位置 run_command=/huskhomes:edithome %1% relocate)   %2%'
  edit_home_menu_privacy_button_public: '[[☀ 设为公共]](#6fff00 show_text=&7点击将这个家设为公共 run_command=/huskhomes:edithome %1% privacy)'
  edit_home_menu_privacy_button_private: '[[∅ 设为私有]](#ff9f0f show_text=&7点击将这个家设为私有 run_command=/huskhomes:edithome %1% privacy)'
  edit_home_menu_meta_edit_buttons: '[编辑:](gray) [[✎ 命名]](#c160ff-#bb50ff show_text=&7点击使用重命名命令 suggest_command=/huskhomes:edithome %1% rename )   [[ℹ 描述]](#bb50ff-#b43fff show_text=&7点击使用编辑描述命令 suggest_command=/huskhomes:edithome %1% description )'
  edit_home_privacy_public_success: '[成功将家](#00fb9a) [%1%](#00fb9a bold) [设为公共](#00fb9a)'
  edit_home_privacy_public_success_other: '[成功将 %1% 的家](#00fb9a) [%2%](#00fb9a bold) [设为公共](#00fb9a)'
  edit_home_privacy_private_success: '[成功将](#00fb9a) [%1%](#00fb9a bold) [设为私有](#00fb9a)'
  edit_home_privacy_private_success_other: '[成功将 %1% 的家](#00fb9a) [%2%](#00fb9a bold) [设为私有](#00fb9a)'
  edit_home_update_location: '[成功修改](#00fb9a) [%1%](#00fb9a bold) [的位置](#00fb9a)'
  edit_home_update_location_other: '[成功修改 %1% 的家](#00fb9a) [%2%](#00fb9a bold) [的位置](#00fb9a)'
  edit_home_update_description: '[成功修改 %1% 的描述:](#00fb9a) \n[←](white) [旧](#00fb9a) ["%2%"](gray)\n[→](white) [新](#00fb9a) ["%3%"](gray)'
  edit_home_update_description_other: '[成功修改 %1% 的家 %2% 的描述:](#00fb9a) \n[←](white) [旧](#00fb9a) ["%3%"](gray)\n[→](white) [新](#00fb9a) ["%4%"](gray)'
  edit_home_update_name: '[成功重命名 %1% →](#00fb9a) [%2%](#00fb9a bold)'
  edit_home_update_name_other: '[成功重命名 %1% 的家 %2% →](#00fb9a) [%3%](#00fb9a bold)'
  edit_warp_menu_title: '[查看地标](#00fb9a) [%1%](#00fb9a bold) [的详细信息:](#00fb9a)'
  edit_warp_menu_metadata: '[⌚ %1%](#999999-#ababab show_text=&7时间:\n&8设置地标的时间)   [⚡ %2%](#ababab-#c7c7c7 show_text=&7地标ID:\n&8%3%)'
  edit_warp_menu_description: '[ℹ %1%](#c160ff-#b43fff show_text=&#c160ff&描述:\n&8%2%)\n'
  edit_warp_menu_world: '[⭘ %1%](#3b76ff-#5286ff show_text=&#3b76ff&世界:\n&8设置地标的世界)'
  edit_warp_menu_world_server: '[⭘ %1%](#3b76ff-#477fff show_text=&#3b76ff&世界:\n&8设置地标的世界)   [☁ %2%](#477fff-#5286ff show_text=&#3b76ff&服务器:\n&8设置地标的服务器)'
  edit_warp_menu_coordinates: '[⚐ x: %1%, y: %2%, z: %3%](#ffc43b-#f5c962 show_text=&#ffc43b&坐标:\n&8地标在世界上的位置\n&8偏航: %4%, 俯仰: %5%)\n'
  edit_warp_menu_use_buttons: '[使用:](gray) [[⏩ 传送]](#00fb9a show_text=&7点击传送到地标 run_command=/huskhomes:warp %1%)'
  edit_warp_menu_manage_buttons: '[管理:](gray) [[❌ 删除]](#ff3300 show_text=&7点击删除此地标\n&#ff3300&⚠ 此操作无法撤消! suggest_command=/huskhomes:delwarp %1%)   [[⚐ 迁移]](#ffc43b-#f5c962 show_text=&7点击将地标修改到你当前的位置 run_command=/huskhomes:editwarp %1% relocate)'
  edit_warp_menu_meta_edit_buttons: '[编辑:](gray) [[✎ 命名]](#c160ff-#bb50ff show_text=&7点击使用重命名命令 suggest_command=/huskhomes:editwarp %1% rename )   [[ℹ 描述]](#bb50ff-#b43fff show_text=&7点击使用编辑描述命令 suggest_command=/huskhomes:editwarp %1% description )'
  edit_warp_update_location: '[更改地标](#00fb9a) [%1%](#00fb9a bold) [到你所在的位置](#00fb9a)'
  edit_warp_update_description: '[成功更改 %1% 的描述:](#00fb9a) \n[←](white) [旧](#00fb9a) ["%2%"](gray)\n[→](white) [新](#00fb9a) ["%3%"](gray)'
  edit_warp_update_name: '[成功重命名 %1% →](#00fb9a) [%2%](#00fb9a bold)'
  home_list_page_title: '[%1% 的家:](#00fb9a) [(%2%-%3% 共](#00fb9a)[%4%](#00fb9a bold)[个)](#00fb9a)\n'
  public_home_list_page_title: '[☀ 公共家:](#00fb9a) [(%1%-%2% 共](#00fb9a)[%3%](#00fb9a bold)[个)](#00fb9a)\n'
  warp_list_page_title: '[地标:](#00fb9a) [(%1%-%2% 共](#00fb9a)[%3%](#00fb9a bold)[个)](#00fb9a)\n'
  command_list_title: '&6枫影轻语&7>> (#00fb9a bold) [| 命令列表](#00fb9a)\n'
  home_list_item: '[[%1%]](show_text=&#00fb9a&%1%\n%3%\n&7ℹ %4% run_command=/huskhomes:edithome %2%)'
  public_home_list_item: '[[%1%]](show_text=&#00fb9a&%1%\n&7☻ %3%\n&7ℹ %4% run_command=/huskhomes:phome %2%)'
  home_is_public: '&#6fff00&☀ 公共'
  home_is_private: '&#ff9f0f&∅ 私有'
  warp_list_item: '[[%1%]](show_text=&#00fb9a&%1%\n&7ℹ %3% run_command=/huskhomes:warp %2%)'
  command_list_item: '[/%1%](#00fb9a show_text=&7点击使用命令 suggest_command=/%1%)   [%2%](gray show_text=&#00fb9a&/%1%\n&7%3%)'
  list_item_divider: ' [•](gray) '
  list_footer: '\n%1%[页](#00fb9a) [%2%](#00fb9a)/[%3%](#00fb9a)%4%   %5%'
  list_previous_page_button: '[◀](white show_text=&7上一页 run_command=%2% %1%) '
  list_next_page_button: ' [▶](white show_text=&7下一页 run_command=%2% %1%)'
  list_page_jumpers: '(%1%)'
  list_page_jumper_button: '[%1%](show_text=&7跳转到 %1% 页 run_command=%2% %1%)'
  list_page_jumper_current_page: '[%1%](#00fb9a)'
  list_page_jumper_separator: ' '
  list_page_jumper_group_separator: '…'
  home_deleted: '[成功删除家](#00fb9a) [%1%](#00fb9a bold)'
  home_deleted_other: '[成功删除 %1% 的家](#00fb9a) [%2%](#00fb9a bold)'
  warp_deleted: '[成功删除地标](#00fb9a) [%1%](#00fb9a bold)'
  set_home_used_free_slots: '[ℹ 你设置完了 %1% 个免费的家,额外设置一个家将花费 %2%](gray)'
  set_home_success: '[成功设置家](#00fb9a) [%1%](#00fb9a bold)'
  set_warp_success: '[成功设置地标](#00fb9a) [%1%](#00fb9a bold)'
  set_spawn_success: '[将重生点更新到你所在的位置](#00fb9a)'
  spawn_warp_default_description: '这个服务器的 /spawn 位置'
  item_no_description: '(没有描述)'
  delete_all_homes_confirm: '[警告:](#ff3300) [你确定删除这个家吗?](#ff7e5e)\n[此操作无法撤消!](gray)\n[→](gray) [[删除]](#ff3300 show_text=&#ff3300&点击删除, 此操作无法撤消! run_command=/delhome all confirm)'
  delete_all_warps_confirm: '[警告:](#ff3300) [你确定删除这个地标?](#ff7e5e)\n[此操作无法撤消!](gray)\n[→](gray) [[删除]](#ff3300 show_text=&#ff3300&点击删除, 此操作无法撤消! run_command=/delwarp all confirm)'
  delete_all_homes_success: '[成功删除家](#00fb9a) [%1%](#00fb9a bold)'
  delete_all_warps_success: '[成功删除服务器](#00fb9a) [%1%](#00fb9a bold) [所有的地标](#00fb9a)'
  economy_transaction_complete: '[ℹ %1% 已从您的账户中扣除.(%2%)](gray)'
  economy_action_additional_home_slot: '购买一个额外的家'
  economy_action_make_home_public: '将家设置为公共'
  economy_action_back_command: '使用 /back 命令'
  economy_action_random_teleport: '使用 /rtp 命令'
  economy_action_home_teleport: '传送到一个家'
  economy_action_public_home_teleport: '传送到一个公共的家'
  economy_action_warp_teleport: '传送到一个地标'
  economy_action_spawn_teleport: '传送到重生点'
  economy_action_send_teleport_request: '发送传送请求'
  economy_action_accept_teleport_request: '接受传送请求'
  return_by_death_notification: '[回到上一个位置?](gray) [[⏪ 点击传送]](#00fb9a show_text=&#00fb9a&点击传送\n&7返回上一位置 run_command=/huskhomes:back)'
  map_hook_public_homes_marker_set_name: '公共的家'
  map_hook_warps_marker_set_name: '地标'
  up_to_date: '&6枫影轻语&7>> (#00fb9a bold) [| 你正在使用最新版本的HuskHomes (v%1%)](#00fb9a)'
  update_available: '&6枫影轻语&7>> (#ff7e5e bold) [| 有新版本的 HuskHomes 可用: v%1% (当前版本: v%2%).](#ff7e5e)'
  reload_complete: '&6枫影轻语&7>> (#00fb9a bold) [| 成功重载配置与消息文件.](#00fb9a)\n[⚠ 请确保所有服务器上的配置文件最新!](#00fb9a)\n[部分设置可能需要重启服务器才能生效.](#00fb9a italic)'  
  user_home_slots_status: '&6枫影轻语&7>> (#00fb9a bold) [| %1% has %2% home slot(s) and has set %3% home(s).](#00fb9a)'
  user_home_slots_updated: '&6枫影轻语&7>> (#00fb9a bold) [| %1%''s home slot count has been set to %2%.](#00fb9a)'
  system_status_header: '&6枫影轻语&7>> (#00fb9a bold) [| 系统状态报告:](#00fb9a)'
  system_dump_confirm: '&6枫影轻语&7>> (#00fb9a bold) [| 准备生成系统转储?这将包括: ](#00fb9a)\n[• 您最新的服务器日志和 HuskHomes 配置文件](gray)\n[• 当前插件系统状态信息](gray)\n[• Java 和 Minecraft 服务器环境信息](gray)\n[• 当前已安装的其他插件列表](gray)\n[要确认, 请使用: ](#00fb9a) [/huskhomes dump confirm](#00fb9a italic show_text=&7点击生成转储 run_command=/huskhomes dump confirm)'
  system_dump_started: '&6枫影轻语&7>> (#00fb9a bold) [| 正在准备系统状态转储, 请稍候...](#00fb9a)'
  system_dump_ready: '&6枫影轻语&7>> (#00fb9a bold) [| 系统状态转储已生成! 点击查看: ](#00fb9a)'
  importer_list_title: '&6枫影轻语&7>> (#00fb9a bold) [| 可用的数据导入器:](#00fb9a)\n'
  importer_list_item: '[%1%](#00fb9a show_text=&#00fb9a&点击以通过此导入器导入数据. suggest_command=/huskhomes:huskhomes import start %1%)    [⏩ %2%](white show_text=&#00fb9a&此导入器支持导入:\n&f%2% suggest_command=/huskhomes:huskhomes import start %1%)'
  delete_player_confirm: '[警告:](#ff3300) [你确定要删除 %1% 的所有数据吗?](#ff7e5e)\n[所有关联的家也将被删除. 此操作不可撤销.](gray)\n[→](gray) [[确定]](#ff3300 show_text=&#ff3300&点击确认删除. 此操作不可撤销. run_command=/huskhomes:huskhomes delete player %1% confirm)'
  delete_player_success: '[已删除玩家](#00fb9a) [%1%](#00fb9a bold)[ 的所有数据, 包括 %2% 个家.](#00fb9a)'
  bulk_delete_warps_confirm: '[警告:](#ff3300) [你确定要删除世界 \"%1%\" 中的所有地标吗?](#ff7e5e)\n[此操作不可撤销.](gray)\n[→](gray) [[确定]](#ff3300 show_text=&#ff3300&点击确认删除. 此操作不可撤销. run_command=/huskhomes:huskhomes delete warps %1% %2% confirm)'
  bulk_delete_warps_success: '[已删除](#00fb9a) [%1%](#00fb9a bold) [个地标.](#00fb9a)'
  bulk_delete_homes_confirm: '[警告:](#ff3300) [你确定要删除世界 \"%1%\" 的所有家吗?](#ff7e5e)\n[此操作不可撤销.](gray)\n[→](gray) [[Confirm]](#ff3300 show_text=&#ff3300&点击确认删除. 此操作不可撤销. run_command=/huskhomes:huskhomes delete homes %1% %2% confirm)'
  bulk_delete_homes_success: '[已删除](#00fb9a) [%1%](#00fb9a bold) [个家.](#00fb9a)'
  error_no_importers_available: '[错误:](#ff3300) [目前没有可用的数据来源. 请检查](#ff7e5e) [插件文档](#ff7e5e italic show_text=&#ff7e5e&点击打开链接 open_url=https://william278.net/docs/huskhomes/importing-data)'
  error_invalid_importer: '[错误:](#ff3300) [数据源无效,请检查](#ff7e5e) [插件文档](#ff7e5e italic show_text=&#ff7e5e&点击打开链接 open_url=https://william278.net/docs/huskhomes/importing-data)'
  error_home_name_taken: '[错误:](#ff3300) [已有一个相同名称的家](#ff7e5e)'
  error_home_name_length: '[错误:](#ff3300) [家的名称最多只能包含16个字符!](#ff7e5e)'
  error_home_name_characters: '[错误:](#ff3300) [家的名称只能使用这些字符: (A-Z, 0-9, -_)](#ff7e5e)'
  error_warp_name_taken: '[错误:](#ff3300) [已有一个相同名称的地标](#ff7e5e)'
  error_warp_name_length: '[错误:](#ff3300) [地标名称只能只能包含16个字符!](#ff7e5e)'
  error_warp_name_characters: '[错误:](#ff3300) [地标名称只能使用这些字符: (A-Z,0-9, -_)](#ff7e5e)'
  error_home_description_length: '[错误:](#ff3300) [家的描述最多只能输入255个字符!](#ff7e5e)'
  error_home_description_characters: '[错误:](#ff3300) [家的描述只能使用这些字符: (A-Z, 0-9, -_, 文字, 空格](#ff7e5e)'
  error_warp_description_length: '[错误:](#ff3300) [地标描述最多只能输入255个字符!](#ff7e5e)'
  error_warp_description_characters: '[错误:](#ff3300) [地标描述只能使用这些字符: (A-Z, 0-9, -_, 文字, 空格](#ff7e5e)'
  error_no_permission: '[错误:](#ff3300) [没有权限](#ff7e5e)'
  error_invalid_syntax: '[错误:](#ff3300) [语法错误,使用:](#ff7e5e) [%1%](#ff7e5e italic show_text=&#ff7e5e&点击建议 suggest_command=%1%)'
  error_already_teleporting: '[错误:](#ff3300) [请等待当前传送完成!](#ff7e5e)'
  error_no_last_position: '[错误:](#ff3300) [没有上一个位置](#ff7e5e)'
  error_in_game_only: '错误: 该命令只能在游戏中使用'
  error_invalid_server: '[错误:](#ff3300) [无法传送, 目标服务器不存在或离线](#ff7e5e)'
  error_home_invalid: '[错误:](#ff3300) [家 %1% 不存在](#ff7e5e)'
  error_warp_invalid: '[错误:](#ff3300) [地标 %1% 不存在](#ff7e5e)'
  error_insufficient_funds: '[错误:](#ff3300) [你没有足够的钱 (需要: %1%)](#ff7e5e)'
  error_edit_home_privacy_already_public: '[错误:](#ff3300) [这个家已经是公共的了](#ff7e5e)'
  error_edit_home_privacy_already_private: '[错误:](#ff3300) [这个家已经是私有的了](#ff7e5e)'
  error_no_homes_set: '[错误:](#ff3300) [你还没设置任何家!](#ff7e5e)'
  error_no_public_homes_set: '[错误:](#ff3300) [没有任何公共的家!](#ff7e5e)'
  error_no_warps_set: '[错误:](#ff3300) [没有任何地标!](#ff7e5e)'
  error_unknown_public_home: '[Error:](#ff3300) [找不到名为"%1%"的公共家.](#ff7e5e)'
  error_public_home_invalid: '[错误:](#ff3300) [%1% 没有 %2% 公共家.](#ff7e5e)'
  error_set_home_maximum_homes: '[错误:](#ff3300) [最多可以设置 %1% 个家!](#ff7e5e)'
  error_set_home_not_enough_slots: '[错误:](#ff3300) [你已用完 %1% 个免费家的数量!](#ff7e5e)'
  error_spawn_not_set: '[错误:](#ff3300) [重生点尚未设置](#ff7e5e)'
  error_no_teleport_requests: '[错误:](#ff3300) [你没有待处理的传送请求](#ff7e5e)'
  error_teleport_warmup_stand_still: '[错误:](#ff3300) [在传送时你不能移动!](#ff7e5e)'
  error_teleport_request_expired: '[错误:](#ff3300) [传送请求已过期](#ff7e5e)'
  error_teleport_request_sender_not_online: '[错误:](#ff3300) [你待处理的传送请求目标玩家不在线](#ff7e5e)'
  error_ignoring_teleport_requests: '[错误:](#ff3300) [你已忽略传送请求](#ff7e5e) [[恢复接收]](#ff7e5e show_text=&#ff7e5e&点击恢复接收传送请求 run_command=/huskhomes:tpignore)'
  error_invalid_teleport_request: '[错误:](#ff3300) [目前没有来自 %1% 的传送请求](#ff7e5e)'
  error_teleport_request_self: '[错误:](#ff3300) [不能向自己发送传送请求](#ff7e5e)'
  error_target_not_found: '[Error:](#ff3300) [没有找到目标玩家.](#ff7e5e)'
  error_player_not_found: '[错误:](#ff3300) [没有找到玩家 %1%](#ff7e5e)'
  error_player_not_teleportable: '[Error:](#ff3300) [The target player, %1%, cannot be teleported right now.](#ff7e5e)'
  error_rtp_restricted_world: '[错误:](#ff3300) [你不能在这个世界随机传送](#ff7e5e)'
  error_on_cooldown: '[错误:](#ff3300) [你需要等待 %1% 才能再次进行此操作](#ff7e5e)'
  error_console_command_only: '[错误:](#ff3300) [该命令只能从服务器控制台运行](#ff7e5e)'
  error_rtp_randomization_timeout: '[错误:](#ff3300) [找不到安全位置,无法进行随机传送](#ff7e5e)'
  error_invalid_world: '[错误:](#ff3300) [无法完成传送.找不到目标世界](#ff7e5e)'
  error_no_offline_position: '[错误:](#ff3300) [找不到 %1% 上次离线的位置](#ff7e5e)'
  error_no_homes_set_other: '[错误:](#ff3300) [%1% 未设置家!](#ff7e5e)'
  error_home_invalid_other: '[错误:](#ff3300) [%1% 没有名为 %2% 的家](#ff7e5e)'
  error_edit_home_maximum_public_homes: '[错误:](#ff3300) [你最多只能将 %1% 个家设置为公共](#ff7e5e)'
  error_illegal_target_coordinates: '[错误:](#ff3300) [传送取消,因为它超出了游戏世界的限制范围](#ff7e5e)'
  error_no_players_online: '[错误:](#ff3300) [没有在线玩家可以传送过来](#ff7e5e)'
  home_command_description: '传送回家或显示所有家'
  sethome_command_description: '设置一个新的家'
  delhome_command_description: '删除家'
  edithome_command_description: '编辑家'
  warp_command_description: '传送或显示所有地标'
  setwarp_command_description: '设置一个带有名称的地标'
  delwarp_command_description: '删除地标'
  editwarp_command_description: '编辑地标'
  back_command_description: '返回到上次传送或死亡的位置'
  homelist_command_description: '你的家列表'
  warplist_command_description: '地标列表'
  phomelist_command_description: '公共家的列表'
  phome_command_description: '传送至公共的家'
  tp_command_description: '传送到玩家或坐标'
  tphere_command_description: '将该玩家传送至您这里'
  tpa_command_description: '请求传送到该玩家'
  tpahere_command_description: '要求其他玩家传送过来'
  tpaccept_command_description: '接受传送请求'
  tpdecline_command_description: '拒绝传送请求'
  tpignore_command_description: '忽略传送请求'
  tpoffline_command_description: '传送到玩家最后在线的位置'
  rtp_command_description: '随机传送'
  spawn_command_description: '传送回重生点'
  setspawn_command_description: '设置重生点位置'
  tpaall_command_description: '请求所有人都传送过来'
  tpall_command_description: '将所有人传送过来'
  huskhomes_command_description: '查看插件信息并重新加载配置'
