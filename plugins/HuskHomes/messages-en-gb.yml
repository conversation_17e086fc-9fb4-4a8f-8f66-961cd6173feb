# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃      HuskHomes Locales       ┃
# ┃    Developed by William278   ┃
# ┣━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
# ┣╸ See plugin about menu for international locale credits
# ┣╸ Formatted in MineDown: https://github.com/WiIIiam278/MineDown
# ┗╸ Translate HuskHomes: https://william278.net/docs/huskhomes/translations

locales:
  teleporting_offline_player: '[Teleporting you to where %1% last logged out…](#00fb9a)'
  teleporting_warmup_start: '[You will be teleported in](#00fb9a) [%1%](#00fb9a bold)
    [seconds, please stand still…](#00fb9a)'
  teleporting_cancelled_movement: '[Teleportation cancelled—you moved!](#ff7e5e)'
  teleporting_cancelled_damage: '[Teleportation cancelled—you took damage!](#ff7e5e)'
  teleporting_complete: '[Teleportation complete!](#00fb9a)'
  teleporting_action_bar_warmup: '[Teleporting in](#00fb9a) [%1%](#00fb9a bold)[…](#00fb9a)'
  teleporting_action_bar_processing: '[Teleporting…](#00fb9a)'
  teleporting_action_bar_cancelled: '[Teleportation cancelled!](#ff7e5e)'
  teleporting_other_complete: '[Teleported %1% to %2%.](#00fb9a)'
  teleporting_other_complete_position: '[Teleported %1% to ⚐ x: %2%, y: %3%, z: %4%.](#00fb9a)'
  teleporting_all_players: '[Teleporting all players to your position…](#00fb9a)'
  teleporting_random_generation: '[Finding a safe random position to teleport you
    to…](#00fb9a)'
  tpa_request_sent: '[You sent a teleport request asking to teleport to](#00fb9a)
    [%1%](#00fb9a bold)[.](#00fb9a)'
  tpahere_request_sent: '[You sent a teleport request asking](#00fb9a) [%1%](#00fb9a
    bold) [to teleport to you.](#00fb9a)'
  tpaall_request_sent: '[You sent a teleport request to every player asking them to
    teleport to you.](#00fb9a)'
  tpa_request_received: '[%1%](#00fb9a bold) [has requested to teleport to you.](#00fb9a)'
  tpahere_request_received: '[%1%](#00fb9a bold) [has requested that you teleport
    to them.](#00fb9a)'
  teleport_request_buttons: '[Options:](gray) [[✔ Accept…]](#00fb9a show_text=&#00fb9a&Accept
    teleport request\n&8Click to accept %1%''s request run_command=/huskhomes:tpaccept
    %1%)   [[❌ Decline…]](#ff3300 show_text=&#ff3300&Decline teleport request\n&8Click
    to decline %1%''s request run_command=/huskhomes:tpdeny %1%)'
  teleport_request_accepted_confirmation: '[You accepted %1%''s teleport request!](#00fb9a)'
  teleport_request_declined_confirmation: '[You declined %1%''s teleport request.](#ff7e5e)'
  teleport_request_accepted: '[%1% has accepted your teleport request!](#00fb9a)'
  teleport_request_declined: '[%1% has declined your teleport request.](#ff7e5e)'
  tpignore_toggle_on: '[Now](#00fb9a) [ignoring](#00fb9a bold) [incoming teleport
    requests.](#00fb9a) %1%'
  tpignore_toggle_off: '[Now](#00fb9a) [listening](#00fb9a bold) [to incoming teleport
    requests.](#00fb9a) %1%'
  tpignore_on_notification: '[You are currently ignoring incoming teleport requests!](gray)
    %1%'
  tpignore_toggle_button: '[[🔔 Toggle…]](#ffc43b show_text=&#ffc43b&Click to toggle:\n&7Toggle
    ignoring/listening to incoming teleport requests run_command=/huskhomes:tpignore)'
  edit_home_menu_title: '[Viewing details for your home,](#00fb9a) [%1%](#00fb9a bold)[:](#00fb9a)'
  edit_home_menu_title_other: '[Viewing details for %1%''s home,](#00fb9a) [%2%](#00fb9a
    bold)[:](#00fb9a)'
  edit_home_menu_metadata_private: '[⌚ %1%](#999999-#ababab show_text=&7Set Timestamp:\n&8When
    the home was set)   [⚡ %2%](#ababab-#c7c7c7 show_text=&7Home ID:\n&8%3%)   [∅
    Private Home](#ff9f0f show_text=&#ff9f0f&Privacy:\n&8The home is private to the
    owner)'
  edit_home_menu_metadata_public: '[⌚ %1%](#999999-#ababab show_text=&7Set Timestamp:\n&8When
    the home was set)   [⚡ %2%](#ababab-#c7c7c7 show_text=&7Home ID:\n&8%3%)   [☀
    Public Home](#6fff00 show_text=&#6fff00&Privacy:\n&8Anyone can teleport to the
    home)'
  edit_home_menu_description: '[ℹ %1%](#c160ff-#b43fff show_text=&#c160ff&Description:\n&8%2%)\n'
  edit_home_menu_world: '[⭘ %1%](#3b76ff-#5286ff show_text=&#3b76ff&World:\n&8The
    world where the home is set)'
  edit_home_menu_world_server: '[⭘ %1%](#3b76ff-#477fff show_text=&#3b76ff&World:\n&8The
    world where the home is set)   [☁ %2%](#477fff-#5286ff show_text=&#3b76ff&Server:\n&8The
    server where the home is set)'
  edit_home_menu_coordinates: '[⚐ x: %1%, y: %2%, z: %3%](#ffc43b-#f5c962 show_text=&#ffc43b&Coordinates:\n&8Location
    of the home in the world\n&8yaw: %4%, pitch: %5%)\n'
  edit_home_menu_use_buttons: '[Use:](gray) [[⏩ Teleport…]](#00fb9a show_text=&7Click
    to teleport to this home run_command=/huskhomes:home %1%)'
  edit_home_menu_manage_buttons: '[Manage:](gray) [[❌ Delete…]](#ff3300 show_text=&7Click
    to delete this home\n&#ff3300&⚠ This cannot be undone! suggest_command=/huskhomes:delhome
    %1%)   [[⚐ Relocate…]](#ffc43b-#f5c962 show_text=&7Click to relocate this home
    to your position run_command=/huskhomes:edithome %1% relocate)   %2%'
  edit_home_menu_privacy_button_public: '[[☀ Make Public…]](#6fff00 show_text=&7Click
    to open this home to the public run_command=/huskhomes:edithome %1% privacy)'
  edit_home_menu_privacy_button_private: '[[∅ Make Private…]](#ff9f0f show_text=&7Click
    to make this home private run_command=/huskhomes:edithome %1% privacy)'
  edit_home_menu_meta_edit_buttons: '[Edit:](gray) [[✎ Rename…]](#c160ff-#bb50ff show_text=&7Click
    to suggest the rename command suggest_command=/huskhomes:edithome %1% rename )   [[ℹ
    Description…]](#bb50ff-#b43fff show_text=&7Click to suggest the edit description
    command suggest_command=/huskhomes:edithome %1% description )'
  edit_home_privacy_public_success: '[Successfully made your home,](#00fb9a) [%1%](#00fb9a
    bold)[, open to the public.](#00fb9a)'
  edit_home_privacy_public_success_other: '[Successfully made %1%''s home,](#00fb9a)
    [%2%](#00fb9a bold)[, open to the public.](#00fb9a)'
  edit_home_privacy_private_success: '[Successfully made your home,](#00fb9a) [%1%](#00fb9a
    bold)[, private.](#00fb9a)'
  edit_home_privacy_private_success_other: '[Successfully made %1%''s home,](#00fb9a)
    [%2%](#00fb9a bold)[, private.](#00fb9a)'
  edit_home_update_location: '[Re-located your home](#00fb9a) [%1%](#00fb9a bold)
    [to your current position.](#00fb9a)'
  edit_home_update_location_other: '[Re-located %1%''s home,](#00fb9a) [%2%](#00fb9a
    bold)[, to your current position.](#00fb9a)'
  edit_home_update_description: '[Successfully changed the description of your home,
    %1%:](#00fb9a) \n[←](white) [Old:](#00fb9a) ["%2%"](gray)\n[→](white) [New:](#00fb9a)
    ["%3%" ](gray)'
  edit_home_update_description_other: '[Successfully changed the description of %1%''s
    home, %2%:](#00fb9a) \n[←](white) [Old:](#00fb9a) ["%3%"](gray)\n[→](white) [New:](#00fb9a)
    ["%4%" ](gray)'
  edit_home_update_name: '[Successfully renamed your home: %1% →](#00fb9a) [%2%](#00fb9a
    bold)'
  edit_home_update_name_other: '[Successfully renamed %1%''s home: %2% →](#00fb9a)
    [%3%](#00fb9a bold)'
  edit_warp_menu_title: '[Viewing details for the warp,](#00fb9a) [%1%](#00fb9a bold)[:](#00fb9a)'
  edit_warp_menu_metadata: '[⌚ %1%](#999999-#ababab show_text=&7Set Timestamp:\n&8When
    the warp was set)   [⚡ %2%](#ababab-#c7c7c7 show_text=&7Warp ID:\n&8%3%)'
  edit_warp_menu_description: '[ℹ %1%](#c160ff-#b43fff show_text=&#c160ff&Description:\n&8%2%)\n'
  edit_warp_menu_world: '[⭘ %1%](#3b76ff-#5286ff show_text=&#3b76ff&World:\n&8The
    world where the warp is set)'
  edit_warp_menu_world_server: '[⭘ %1%](#3b76ff-#477fff show_text=&#3b76ff&World:\n&8The
    world where the warp is set)   [☁ %2%](#477fff-#5286ff show_text=&#3b76ff&Server:\n&8The
    server where the warp is set)'
  edit_warp_menu_coordinates: '[⚐ x: %1%, y: %2%, z: %3%](#ffc43b-#f5c962 show_text=&#ffc43b&Coordinates:\n&8Location
    of the warp in the world\n&8yaw: %4%, pitch: %5%)\n'
  edit_warp_menu_use_buttons: '[Use:](gray) [[⏩ Teleport…]](#00fb9a show_text=&7Click
    to teleport to this warp run_command=/huskhomes:warp %1%)'
  edit_warp_menu_manage_buttons: '[Manage:](gray) [[❌ Delete…]](#ff3300 show_text=&7Click
    to delete this warp\n&#ff3300&⚠ This cannot be undone! suggest_command=/huskhomes:delwarp
    %1%)   [[⚐ Relocate…]](#ffc43b-#f5c962 show_text=&7Click to relocate this warp
    to your position run_command=/huskhomes:editwarp %1% relocate)'
  edit_warp_menu_meta_edit_buttons: '[Edit:](gray) [[✎ Rename…]](#c160ff-#bb50ff show_text=&7Click
    to suggest the rename command suggest_command=/huskhomes:editwarp %1% rename )   [[ℹ
    Description…]](#bb50ff-#b43fff show_text=&7Click to suggest the edit description
    command suggest_command=/huskhomes:editwarp %1% description )'
  edit_warp_update_location: '[Re-located the warp](#00fb9a) [%1%](#00fb9a bold) [to
    your current position.](#00fb9a)'
  edit_warp_update_description: '[Successfully changed the description of the warp,
    %1%:](#00fb9a) \n[←](white) [Old:](#00fb9a) ["%2%"](gray)\n[→](white) [New:](#00fb9a)
    ["%3%" ](gray)'
  edit_warp_update_name: '[Successfully renamed the warp: %1% →](#00fb9a) [%2%](#00fb9a
    bold)'
  home_list_page_title: '[%1%''s Homes:](#00fb9a) [(%2%-%3% of](#00fb9a) [%4%](#00fb9a
    bold)[)](#00fb9a)\n'
  public_home_list_page_title: '[☀ Public Homes:](#00fb9a) [(%1%-%2% of](#00fb9a)
    [%3%](#00fb9a bold)[)](#00fb9a)\n'
  warp_list_page_title: '[Warps:](#00fb9a) [(%1%-%2% of](#00fb9a) [%3%](#00fb9a bold)[)](#00fb9a)\n'
  command_list_title: '[HuskHomes](#00fb9a bold) [| Command List](#00fb9a)\n'
  home_list_item: '[[%1%]](show_text=&#00fb9a&%1%\n%3%\n&7ℹ %4% run_command=/huskhomes:edithome
    %2%)'
  public_home_list_item: '[[%1%]](show_text=&#00fb9a&%1%\n&7☻ %3%\n&7ℹ %4% run_command=/huskhomes:phome
    %2%)'
  home_is_public: '&#6fff00&☀ Public'
  home_is_private: '&#ff9f0f&∅ Private'
  warp_list_item: '[[%1%]](show_text=&#00fb9a&%1%\n&7ℹ %3% run_command=/huskhomes:warp
    %2%)'
  command_list_item: '[/%1%](#00fb9a show_text=&7Click to suggest command suggest_command=/%1%)   [%2%](gray
    show_text=&#00fb9a&/%1%\n&7%3%)'
  list_item_divider: ' [•](gray) '
  list_footer: \n%1%[Page](#00fb9a) [%2%](#00fb9a)/[%3%](#00fb9a)%4%   %5%
  list_previous_page_button: '[◀](white show_text=&7View previous page run_command=%2%
    %1%) '
  list_next_page_button: ' [▶](white show_text=&7View next page run_command=%2% %1%)'
  list_page_jumpers: (%1%)
  list_page_jumper_button: '[%1%](show_text=&7Jump to page %1% run_command=%2% %1%)'
  list_page_jumper_current_page: '[%1%](#00fb9a)'
  list_page_jumper_separator: ' '
  list_page_jumper_group_separator: …
  home_deleted: '[Successfully deleted your home,](#00fb9a) [%1%](#00fb9a bold)'
  home_deleted_other: '[Successfully deleted %1%''s home,](#00fb9a) [%2%](#00fb9a
    bold)'
  warp_deleted: '[Successfully deleted the warp,](#00fb9a) [%1%](#00fb9a bold)'
  set_home_used_free_slots: '[ℹ You have used all %1% of your free home slots. Setting
    additional homes will cost %2% each.](gray)'
  set_home_success: '[Successfully set a new home called](#00fb9a) [%1%](#00fb9a bold)'
  set_warp_success: '[Successfully set a new warp called](#00fb9a) [%1%](#00fb9a bold)'
  set_spawn_success: '[Moved the spawn position to your current location.](#00fb9a)'
  spawn_warp_default_description: The /spawn position of the server
  item_no_description: (No description)
  delete_all_homes_confirm: '[Warning:](#ff3300) [Are you sure you want to delete
    all of your homes?](#ff7e5e)\n[Once you confirm, it will not be possible to recover
    them.](gray)\n[→](gray) [[Confirm]](#ff3300 show_text=&#ff3300&Click to confirm
    deletion. This cannot be undone. run_command=/delhome all confirm)'
  delete_all_warps_confirm: '[Warning:](#ff3300) [Are you sure you want to delete
    every warp?](#ff7e5e)\n[Once you confirm, it will not be possible to recover them.](gray)\n[→](gray)
    [[Confirm]](#ff3300 show_text=&#ff3300&Click to confirm deletion. This cannot
    be undone. run_command=/delwarp all confirm)'
  delete_all_homes_success: '[Successfully deleted](#00fb9a) [%1%](#00fb9a bold) [home(s).](#00fb9a)'
  delete_all_warps_success: '[Successfully deleted](#00fb9a) [%1%](#00fb9a bold) [server
    warp(s).](#00fb9a)'
  economy_transaction_complete: '[ℹ %1% has been deducted from your account. (%2%)](gray)'
  economy_action_additional_home_slot: Bought an additional home slot
  economy_action_make_home_public: Made a home public
  economy_action_back_command: Used the /back command
  economy_action_random_teleport: Used the /rtp command
  economy_action_home_teleport: Teleported to a home
  economy_action_public_home_teleport: Teleported to a public home
  economy_action_warp_teleport: Teleported to a warp
  economy_action_spawn_teleport: Teleported to spawn
  economy_action_send_teleport_request: Sent a teleport request
  economy_action_accept_teleport_request: Accepted a teleport request
  return_by_death_notification: '[Return to where you died?](gray) [[⏪ Teleport…]](#00fb9a
    show_text=&#00fb9a&Click to teleport\n&7Return to where you died run_command=/huskhomes:back)'
  map_hook_public_homes_marker_set_name: Public Homes
  map_hook_warps_marker_set_name: Warps
  up_to_date: '[HuskHomes](#00fb9a bold) [| You are running the latest version of
    HuskHomes (v%1%).](#00fb9a)'
  update_available: '[HuskHomes](#ff7e5e bold) [| A new version of HuskHomes is available:
    v%1% (running: v%2%).](#ff7e5e)'
  reload_complete: '[HuskHomes](#00fb9a bold) [| Reloaded config and message files.](#00fb9a)\n[⚠
    Ensure config files are up-to-date on all servers!](#00fb9a)\n[A restart is needed
    for config changes to take effect.](#00fb9a italic)'
  user_home_slots_status: '[HuskHomes](#00fb9a bold) [| %1% has %2% home slot(s) and
    has set %3% home(s).](#00fb9a)'
  user_home_slots_updated: '[HuskHomes](#00fb9a bold) [| %1%''s home slot count has
    been set to %2%.](#00fb9a)'
  system_status_header: '[HuskHomes](#00fb9a bold) [| System status report:](#00fb9a)'
  system_dump_confirm: '[HuskHomes](#00fb9a bold) [| Prepare a system dump? This will
    include:](#00fb9a)\n[• Your latest server logs and HuskHomes config files](gray)\n[•
    Current plugin system status information](gray)\n[• Information about your Java
    & Minecraft server environment](gray)\n[• A list of other currently installed
    plugins](gray)\n[To confirm, use:](#00fb9a) [/huskhomes dump confirm](#00fb9a
    italic show_text=&7Click to prepare dump run_command=/huskhomes dump confirm)'
  system_dump_started: '[HuskHomes](#00fb9a bold) [| Preparing system status dump,
    please wait…](#00fb9a)'
  system_dump_ready: '[HuskHomes](#00fb9a bold) [| System status dump prepared! Click
    to view:](#00fb9a)'
  importer_list_title: '[HuskHomes](#00fb9a bold) [| Available Data Importers:](#00fb9a)\n'
  importer_list_item: '[%1%](#00fb9a show_text=&#00fb9a&Click to import data through
    this importer. suggest_command=/huskhomes:huskhomes import start %1%)    [⏩ %2%](white
    show_text=&#00fb9a&This importer supports importing:\n&f%2% suggest_command=/huskhomes:huskhomes
    import start %1%)'
  delete_player_confirm: '[Warning:](#ff3300) [Are you sure you want to delete all
    of %1%''s data?](#ff7e5e)\n[All associated homes will also be deleted. This action
    is irreversible.](gray)\n[→](gray) [[Confirm]](#ff3300 show_text=&#ff3300&Click
    to confirm deletion. This cannot be undone. run_command=/huskhomes:huskhomes delete
    player %1% confirm)'
  delete_player_success: '[Successfully deleted all player data for](#00fb9a) [%1%](#00fb9a
    bold)[, including %2% home(s).](#00fb9a)'
  bulk_delete_warps_confirm: '[Warning:](#ff3300) [Are you sure you want to delete
    all warps in the world \"%1%\"?](#ff7e5e)\n[This action is irreversible.](gray)\n[→](gray)
    [[Confirm]](#ff3300 show_text=&#ff3300&Click to confirm deletion. This cannot
    be undone. run_command=/huskhomes:huskhomes delete warps %1% %2% confirm)'
  bulk_delete_warps_success: '[Successfully deleted](#00fb9a) [%1%](#00fb9a bold)
    [warps.](#00fb9a)'
  bulk_delete_homes_confirm: '[Warning:](#ff3300) [Are you sure you want to delete
    all homes in the world \"%1%\"?](#ff7e5e)\n[This action is irreversible.](gray)\n[→](gray)
    [[Confirm]](#ff3300 show_text=&#ff3300&Click to confirm deletion. This cannot
    be undone. run_command=/huskhomes:huskhomes delete homes %1% %2% confirm)'
  bulk_delete_homes_success: '[Successfully deleted](#00fb9a) [%1%](#00fb9a bold)
    [homes.](#00fb9a)'
  error_invalid_importer: '[Error:](#ff3300) [Invalid importer. Please check the](#ff7e5e)
    [plugin docs](#ff7e5e italic show_text=&#ff7e5e&Click to open link open_url=https://william278.net/docs/huskhomes/importing-data)
    [for more details.](#ff7e5e)'
  error_home_name_taken: '[Error:](#ff3300) [You already have a home set with that
    name.](#ff7e5e)'
  error_home_name_length: '[Error:](#ff3300) [Home names can only contain up to 16
    characters!](#ff7e5e)'
  error_home_name_characters: '[Error:](#ff3300) [Home names must be alphanumeric
    (A-Z, 0-9, -_)](#ff7e5e)'
  error_warp_name_taken: '[Error:](#ff3300) [There is already a warp set with that
    name.](#ff7e5e)'
  error_warp_name_length: '[Error:](#ff3300) [Warp names can only contain up to 16
    characters!](#ff7e5e)'
  error_warp_name_characters: '[Error:](#ff3300) [Warp names must be alphanumeric
    (A-Z,0-9, -_)](#ff7e5e)'
  error_home_description_length: '[Error:](#ff3300) [Home descriptions must be less
    than 255 characters in length.](#ff7e5e)'
  error_home_description_characters: '[Error:](#ff3300) [Home descriptions may only
    contain alphanumeric (A-Z, 0-9) characters, spaces, hyphens (-) and underscores
    (_)](#ff7e5e)'
  error_warp_description_length: '[Error:](#ff3300) [Warp descriptions must be less
    than 255 characters in length.](#ff7e5e)'
  error_warp_description_characters: '[Error:](#ff3300) [Warp descriptions may only
    contain alphanumeric (A-Z, 0-9) characters, spaces, hyphens (-) and underscores
    (_)](#ff7e5e)'
  error_no_permission: '[Error:](#ff3300) [You do not have permission to execute that
    command.](#ff7e5e)'
  error_invalid_syntax: '[Error:](#ff3300) [Invalid syntax. Usage:](#ff7e5e) [%1%](#ff7e5e
    italic show_text=&#ff7e5e&Click to suggest suggest_command=%1%)'
  error_already_teleporting: '[Error:](#ff3300) [Please wait for the current teleport
    to finish!](#ff7e5e)'
  error_no_last_position: '[Error:](#ff3300) [You have no last position to return
    to!](#ff7e5e)'
  error_in_game_only: 'Error: That command can only be run in-game.'
  error_invalid_server: '[Error:](#ff3300) [Failed to finish teleportation as the
    target server could not be found or was offline.](#ff7e5e)'
  error_home_invalid: '[Error:](#ff3300) [You have not set a home with the name "%1%"](#ff7e5e)'
  error_warp_invalid: '[Error:](#ff3300) [There is no warp set with the name "%1%"](#ff7e5e)'
  error_insufficient_funds: '[Error:](#ff3300) [You do not have enough money. (Cost:
    %1%)](#ff7e5e)'
  error_edit_home_privacy_already_public: '[Error:](#ff3300) [This home is already
    set as public.](#ff7e5e)'
  error_edit_home_privacy_already_private: '[Error:](#ff3300) [This home is already
    set as private.](#ff7e5e)'
  error_no_homes_set: '[Error:](#ff3300) [You have not set any homes!](#ff7e5e)'
  error_no_public_homes_set: '[Error:](#ff3300) [There are no public homes set!](#ff7e5e)'
  error_no_warps_set: '[Error:](#ff3300) [There are no warps set!](#ff7e5e)'
  error_unknown_public_home: '[Error:](#ff3300) [Could not find a public home with
    the name "%1%".](#ff7e5e)'
  error_public_home_invalid: '[Error:](#ff3300) [Could not find a public home owned
    by %1% called %2%.](#ff7e5e)'
  error_set_home_maximum_homes: '[Error:](#ff3300) [You can only set a maximum of
    %1% homes!](#ff7e5e)'
  error_set_home_not_enough_slots: '[Error:](#ff3300) [You have used all %1% of your
    free home slots!](#ff7e5e)'
  error_spawn_not_set: '[Error:](#ff3300) [The spawn position has not been set.](#ff7e5e)'
  error_no_teleport_requests: '[Error:](#ff3300) [You do not have a pending teleport
    request.](#ff7e5e)'
  error_teleport_warmup_stand_still: '[Error:](#ff3300) [You must be standing still
    before starting a teleport!](#ff7e5e)'
  error_teleport_request_expired: '[Error:](#ff3300) [Your pending teleport request
    has expired.](#ff7e5e)'
  error_teleport_request_sender_not_online: '[Error:](#ff3300) [The sender of your
    pending teleport request is no longer online.](#ff7e5e)'
  error_ignoring_teleport_requests: '[Error:](#ff3300) [You are currently ignoring
    teleport requests.](#ff7e5e) [[Stop Ignoring…]](#ff7e5e show_text=&#ff7e5e&Click
    to listen for incoming teleport requests run_command=/huskhomes:tpignore)'
  error_invalid_teleport_request: '[Error:](#ff3300) [You do not have a pending teleport
    request from %1%.](#ff7e5e)'
  error_teleport_request_self: '[Error:](#ff3300) [You cannot send a teleport request
    to yourself.](#ff7e5e)'
  error_target_not_found: '[Error:](#ff3300) [Could not find the target player.](#ff7e5e)'
  error_player_not_found: '[Error:](#ff3300) [Could not find the player %1%.](#ff7e5e)'
  error_player_not_teleportable: '[Error:](#ff3300) [The target player, %1%, cannot
    be teleported right now.](#ff7e5e)'
  error_rtp_restricted_world: '[Error:](#ff3300) [You cannot randomly teleport in
    this world.](#ff7e5e)'
  error_on_cooldown: '[Error:](#ff3300) [You must wait %1% before doing that again.](#ff7e5e)'
  error_console_command_only: '[Error:](#ff3300) [That command can only be run from
    the server console.](#ff7e5e)'
  error_rtp_randomization_timeout: '[Error:](#ff3300) [Failed to find a safe random
    location to teleport you.](#ff7e5e)'
  error_invalid_world: '[Error:](#ff3300) [Failed to finish teleportation as the target
    world could not be found.](#ff7e5e)'
  error_no_offline_position: '[Error:](#ff3300) [Could not find where %1% last logged
    out.](#ff7e5e)'
  error_no_homes_set_other: '[Error:](#ff3300) [%1% has not set any homes!](#ff7e5e)'
  error_home_invalid_other: '[Error:](#ff3300) [%1% does not have a home set named
    "%2%"](#ff7e5e)'
  error_edit_home_maximum_public_homes: '[Error:](#ff3300) [You can only make a maximum
    of %1% homes public.](#ff7e5e)'
  error_illegal_target_coordinates: '[Error:](#ff3300) [Cancelled teleport as it exceeds
    the possible game world limits.](#ff7e5e)'
  error_no_players_online: '[Error:](#ff3300) [There are no players online who could
    teleport to you.](#ff7e5e)'
  error_no_importers_available: '[Error:](#ff3300) [No importers are currently available.
    Please check the](#ff7e5e) [plugin docs](#ff7e5e italic show_text=&#ff7e5e&Click
    to open link open_url=https://william278.net/docs/huskhomes/importing-data) [for
    more details.](#ff7e5e)'
  home_command_description: Teleport to one of your homes
  sethome_command_description: Set a new home with given name
  delhome_command_description: Delete a home you previously set
  edithome_command_description: Edit one of your homes
  warp_command_description: Teleport to a warp
  setwarp_command_description: Set a new warp with given name
  delwarp_command_description: Delete a warp
  editwarp_command_description: Edit a warp
  back_command_description: Return to your previous position, or where you died
  homelist_command_description: Get a list of your homes
  warplist_command_description: View the list of warps
  phomelist_command_description: View the list of public homes
  phome_command_description: Teleport to a public home
  tp_command_description: Teleport to another player or location
  tphere_command_description: Teleport another player to you
  tpa_command_description: Request to teleport to another player
  tpahere_command_description: Request another player to teleport to you
  tpaccept_command_description: Accept a teleport request
  tpdecline_command_description: Decline a teleport request
  tpignore_command_description: Ignore incoming teleport requests
  tpoffline_command_description: Teleport to where a player was last online
  rtp_command_description: Teleport randomly into the wild
  spawn_command_description: Teleport to spawn
  setspawn_command_description: Set the spawn position
  tpaall_command_description: Request that everyone teleports to you
  tpall_command_description: Teleport everyone to your position
  huskhomes_command_description: View plugin information & reload configs
